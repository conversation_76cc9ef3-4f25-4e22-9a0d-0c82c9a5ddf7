# -*- coding: utf-8 -*-
import json
import random
from flask import Flask
from flask_cors import CORS
from sqlalchemy import orm
from sqlalchemy.sql.dml import UpdateBase
from settings import SQLConf, MINIO_CONF
from puppet.cache import redis_pool
from flask_sqlalchemy import SQLAlchemy, SignallingSession, get_state
from contextlib import contextmanager
from sqlalchemy import create_engine
from sqlalchemy.dialects.postgresql.base import PGDialect
from sqlalchemy.pool import QueuePool
from sqlalchemy.orm import sessionmaker
from module.mysql.manager import MysqlConf, ModelConf

from utils.minio_utils import MinIOUtil

# 备份原始方法
_original_get_server_version_info = PGDialect._get_server_version_info


def _patched_kingbase_version_parser(self, connection):
    version_str = connection.exec_driver_sql("SELECT version()").scalar()
    if version_str.startswith("KingbaseES"):
        # 使用kingbase伪装的pgsql版本号
        version = connection.exec_driver_sql("SHOW server_version").scalar()
        match = [int(x) for x in version.split(".")]
        print(f"[INFO] KingbaseES 伪装版本: PostgreSQL {version}")
        return tuple(match)
    else:
        return _original_get_server_version_info(self, connection)


# 应用补丁
PGDialect._get_server_version_info = _patched_kingbase_version_parser

# 导入各资源类
app = Flask(__name__)

minio_conf_map = MINIO_CONF
minio_util = MinIOUtil(
    endpoint=minio_conf_map['endpoint'],
    access_key=minio_conf_map['access_key'],
    secret_key=minio_conf_map['secret_key'],
    secure=False
)


# 主库初始化租户分库
class MasterDataInit:
    def __init__(self, _app):
        self.app = _app
        self.session_cache = dict()
        self.organize_mysql_info = None
        self.init()

    def init(self):
        # 初始化数据库链接信息
        self.organize_mysql_info = dict()

        # 查询主库，查询各个组织的数据
        with self.build_session()() as session:
            # 初始化数据库加载
            objs = session.query(MysqlConf).all()

            sql_binds = dict()
            for obj in objs:
                corp_id = obj.corpid
                host = obj.ip
                port = obj.port
                db_name = obj.database
                permission_type = obj.permission_type
                params = {
                    "host": host,
                    'port': port,
                    'user': obj.user,
                    'password': obj.password,
                    'database': db_name,
                    'charset': obj.charset,
                    'database_type': obj.database_type,
                    'schema': obj.schema,
                }
                bind_key = '{}_{}_{}{}_{}'.format(corp_id, permission_type, host, port, db_name)

                # 组织读写分离库数据结构
                if corp_id in self.organize_mysql_info:
                    if permission_type == 'write':
                        self.organize_mysql_info[corp_id]['write'].append(bind_key)
                    else:
                        self.organize_mysql_info[corp_id]['read'].append(bind_key)
                else:
                    if permission_type == 'write':
                        self.organize_mysql_info[corp_id] = {'write': [bind_key], 'read': []}
                    else:
                        self.organize_mysql_info[corp_id] = {'read': [bind_key], 'write': []}

                if bind_key in sql_binds:
                    raise ValueError('数据名称冲突')
                sql_binds[bind_key] = self.build_url(conf_map=params)
            # 没有读库则读写通用
            for corpid, values in self.organize_mysql_info.items():
                if not values.get('write'):
                    raise ValueError('数据库-写库未配置')
                if not values.get('read'):
                    self.organize_mysql_info[corpid]['read'] = values['write']
            sql_binds['master_db'] = self.build_url()
            self.app.config['SQLALCHEMY_BINDS'] = sql_binds
            # 初始化更新 modelconf
            model_objs = session.query(ModelConf).filter(ModelConf.corpid.isnot(None)).all()
            init_data = dict()
            for obj in model_objs:
                init_data.update({
                    "model_conf^" + obj.corpid + "^" + obj.model_type + "^api_key": obj.api_key or '',
                    "model_conf^" + obj.corpid + "^" + obj.model_type + "^secret_key": obj.secret_key or '',
                })
            if init_data:
                redis_client = redis_pool.use()
                redis_client.hmset('tenant_init_data', init_data)

    @classmethod
    def build_session(cls, conf_map=None, db_link=None):
        if not db_link:
            db_link = cls.build_url(conf_map=conf_map)
        engine = create_engine(db_link, pool_size=20, pool_timeout=30, pool_recycle=1800, poolclass=QueuePool)
        session = sessionmaker(bind=engine)
        return session

    @classmethod
    def build_url(cls, db_name="mysql", conf_map=None):
        if not conf_map:
            conf_map = SQLConf[db_name]
        user = conf_map['user']
        sql_pass = conf_map['password']
        host = conf_map['host']
        port = conf_map['port']
        db = conf_map['database']
        charset = conf_map['charset']
        database_type = conf_map.get('database_type') or 'mysql' # 读取database_type 
        schema = conf_map.get('schema') # 读取schema 
        print(f'database type: {database_type}')
        if database_type == 'pgsql' or database_type == 'kdb':
            url = f'postgresql+psycopg2://{user}:{sql_pass}@{host}:{port}/{db}?options=-csearch_path={schema}&client_encoding={charset}'
        else:
            url = 'mysql+pymysql://%s:%s@%s:%d/%s?charset=%s&collation=utf8mb4_general_ci' % \
              (user, sql_pass, host, port, db, charset)
        return url
       
        # url = 'mysql+mysqlconnector://%s:%s@%s:%d/%s?auth_plugin=mysql_native_password&charset=%s&collation=utf8mb4_general_ci' % \
        #       (user, sql_pass, host, port, db, charset)


app.config['SQLALCHEMY_DATABASE_URI'] = MasterDataInit.build_url(db_name='mysql')
# app.config['SQLALCHEMY_POOL_SIZE'] = 300
# app.config['SQLALCHEMY_POOL_TIMEOUT'] = 20
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
# app.config['SQLALCHEMY_COMMIT_ON_TEARDOWN'] = True

# app.config['SQLALCHEMY_POOL_RECYCLE'] = 1800
app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
    'pool_pre_ping': True,  # 在每次连接使用前进行连接验证
    'pool_size': 300,
    'pool_timeout': 20,
    'pool_recycle': 1800
}
app.config['PROPAGATE_EXCEPTIONS'] = True

exchange = MasterDataInit(app)


class RoutingSession(SignallingSession):
    def get_bind(self, mapper=None, clause=None, for_no=0):
        state = get_state(self.app)
        corpid = self.info.get('corpid')

        # 连接管理库
        if not corpid:
            print('使用master管理库')
            return state.db.get_engine(self.app, bind='master_db')
        else:
            if corpid in exchange.organize_mysql_info:
                db_map_list = exchange.organize_mysql_info[corpid]
                if self._flushing or isinstance(clause, UpdateBase):  # 增删改操作, 使用主库
                    # print('使用读写库')
                    return state.db.get_engine(self.app, bind=random.choice(db_map_list['write']))
                else:  # 读操作, 使用从库
                    # print('使用读库')
                    return state.db.get_engine(self.app, bind=random.choice(db_map_list['read']))
            else:
                # 动态刷新重新加载租户数据库信息
                if for_no == 0:
                    exchange.init()
                    return self.get_bind(mapper, clause, for_no + 1)
                else:
                    raise ValueError('corpid={} 未授权组织'.format(corpid))


class RoutingSQLAlchemy(SQLAlchemy):
    def create_session(self, options):
        return orm.sessionmaker(class_=RoutingSession, db=self, **options)


class BaseModel:
    # 数据库懒散加载绑定flask的app应用
    __db = RoutingSQLAlchemy(app)

    def __init__(self, corpid):
        self.corpid = corpid
        # if self.corpid:
        #     # self.__db = RoutingSQLAlchemy(app, session_options={'info': {'corpid': corpid}})
        #     # self.session = self.__db.session
        #     self.__db = exchange.app.config['SQLALCHEMY_BINDS'][random.choice(exchange.organize_mysql_info[corpid]['write'])]
        # else:
        #     self.__db = None
        #
        # self.session = exchange.build_session(db_link=self.__db)()
        self.session = self.__db.create_scoped_session({'info': {'corpid': corpid}})

    @contextmanager
    def session_scope(self):
        _session = self.session
        try:
            yield _session
            _session.commit()
        except Exception as err:
            try:
                _session.rollback()
            except:
                pass
            finally:
                raise ValueError(err)

    def class_to_dict(self, obj):
        is_list = obj.__class__ == [].__class__
        is_set = obj.__class__ == set().__class__
        if is_list or is_set:
            obj_arr = []
            for o in obj:
                dict = {}
                a = o.__dict__
                if "_sa_instance_state" in a:
                    del a['_sa_instance_state']
                dict.update(a)
                obj_arr.append(dict)
            return obj_arr
        else:
            dict = {}
            a = obj.__dict__
            if "_sa_instance_state" in a:
                del a['_sa_instance_state']
            dict.update(a)
            return dict

    def __del__(self):
        try:
            if self.corpid:
                self.session.commit()
        except:
            try:
                if self.corpid:
                    self.session.rollback()
            except:
                pass
        finally:
            try:
                self.session.close()
            except:
                pass


@contextmanager
def session_scope_celery(corpid):
    if corpid not in exchange.session_cache:
        if corpid not in exchange.organize_mysql_info:
            exchange.init()
            if corpid not in exchange.organize_mysql_info:
                raise ValueError('非法组织信息 {}'.format(corpid))
        db_link = exchange.app.config['SQLALCHEMY_BINDS'][random.choice(exchange.organize_mysql_info[corpid]['write'])]
        exchange.session_cache[corpid] = exchange.build_session(db_link=db_link)
    Session = exchange.session_cache[corpid]
    with Session() as _session:
        yield _session
        _session.commit()
        _session.close()
