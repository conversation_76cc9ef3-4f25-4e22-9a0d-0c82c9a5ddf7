# coding: utf-8
from sqlalchemy import Column, Date, DateTime, Enum, ForeignKey, String, Text, text
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, VARCHAR
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class EsConf(Base):
    __tablename__ = 'es_conf'

    corpid = Column(String(255), primary_key=True)
    ip = Column(String(255), nullable=False)
    port = Column(INTEGER(11), nullable=False)
    db = Column(String(255), nullable=False)
    db_prefix = Column(String(255), nullable=False)
    user = Column(String(255), nullable=False)
    password = Column(String(255), nullable=False)
    auth = Column(INTEGER(11), nullable=False)
    uri = Column(String(500), nullable=False, server_default=text("''"))


class ModelConf(Base):
    __tablename__ = 'model_conf'
    __table_args__ = {'comment': '模型平台配置'}

    id = Column(INTEGER(11), primary_key=True)
    corpid = Column(String(255), nullable=False)
    model_type = Column(String(255), comment='模型类型')
    api_key = Column(String(255))
    secret_key = Column(String(255))


class MongoConf(Base):
    __tablename__ = 'mongo_conf'
    __table_args__ = {'comment': '租户mongo配置'}

    corpid = Column(String(255), primary_key=True)
    ip = Column(String(255), nullable=False)
    port = Column(INTEGER(11), nullable=False)
    db = Column(String(255), nullable=False)
    db_prefix = Column(String(255), nullable=False)
    user = Column(String(255), nullable=False)
    password = Column(String(255), nullable=False)
    auth = Column(INTEGER(11), nullable=False)
    uri = Column(String(500), nullable=False)


class MysqlConf(Base):
    __tablename__ = 'mysql_conf'
    __table_args__ = {'comment': '租户mysql配置'}

    id = Column(INTEGER(11), primary_key=True)
    corpid = Column(String(255), nullable=False)
    ip = Column(String(255), nullable=False)
    port = Column(INTEGER(11), nullable=False)
    user = Column(String(255), nullable=False)
    password = Column(String(255), nullable=False)
    database = Column(String(255), nullable=False)
    charset = Column(String(255), nullable=False)
    permission_type = Column(Enum('read', 'write'), nullable=False, server_default=text("'read'"), comment='读写标识')
    database_type = Column(String(255), nullable=False, server_default=text("'mysql'"), comment="mysql,db,kdb")
    schema = Column(String(255), nullable=False, server_default=text("'public'"), comment="schema")

class Organize(Base):
    __tablename__ = 'organize'
    __table_args__ = {'comment': '租户企业注册信息'}

    corpid = Column(String(255), primary_key=True, comment='组织编号')
    name = Column(String(255), nullable=False, server_default=text("''"), comment='组织名称')
    location = Column(String(255), server_default=text("''"), comment='营业地址')
    corp_sub_industry = Column(String(255), server_default=text("''"), comment='经营范围')
    corp_industry = Column(String(255), server_default=text("''"), comment='行业范围')
    corp_scale = Column(String(255), server_default=text("''"), comment='人员规模')
    corp_wxqrcode = Column(String(255), server_default=text("''"), comment='企业二维码')
    subject_type = Column(String(10), server_default=text("''"), comment='企业类型，1. 企业; 2. 政府以及事业单位; 3. 其他组织, 4.团队号')
    corp_square_logo_url = Column(String(255), server_default=text("''"), comment='企业logo')
    corp_type = Column(String(255), server_default=text("''"), comment='授权方企业类型，认证号：verified, 注册号：unverified')
    regist_flag = Column(String(10), nullable=False, server_default=text("'1'"), comment='注册状态')
    tenant_id = Column(INTEGER(11), nullable=False, server_default=text("0"), comment='租户ID')
    work_erp_sn = Column(String(255), comment='Erp 商户号')
    client_status = Column(String(1), comment='0：试用客户；1：正式客户；2：停用客户')
    service_date_bgn = Column(Date, comment='服务开始日期')
    service_date_end = Column(Date, comment='服务结束日期')
    ww_corp_id = Column(String(64), comment='企微组织id')


class OssConf(Base):
    __tablename__ = 'oss_conf'
    __table_args__ = {'comment': '租户存储配置'}

    corpid = Column(String(255), primary_key=True)
    access_key_id = Column(String(255), nullable=False)
    key_secret = Column(String(255), nullable=False)
    domain = Column(String(255), nullable=False)
    bucket = Column(String(255), nullable=False)
    role_arn = Column(String(255), nullable=False)
    endpoint = Column(String(255), nullable=False)
    role = Column(String(255), nullable=False)
    expire_time = Column(INTEGER(11), nullable=False)
    region = Column(String(255), nullable=False)


class SmsConf(Base):
    __tablename__ = 'sms_conf'
    __table_args__ = {'comment': '租户短信模版'}

    id = Column(INTEGER(11), primary_key=True)
    corpid = Column(String(255), nullable=False)
    code_login = Column(String(255), nullable=False)
    sign_name = Column(String(255), nullable=False)
    sign_chinese_name = Column(String(255), nullable=False)
    tp_key = Column(String(255), nullable=False)
    url = Column(String(255), nullable=False)
    access_key_id = Column(String(255), nullable=False)
    key_secret = Column(String(255), nullable=False)
    use_type = Column(Enum('trans', 'verify'), nullable=False, server_default=text("'verify'"), comment='使用方式 验证码｜广告宣传')


class SysUser(Base):
    __tablename__ = 'sys_user'
    __table_args__ = {'comment': '用户索引组织'}

    sys_id = Column(INTEGER(11), primary_key=True, comment='用户系统id')
    corpid = Column(String(255), comment='组织id')
    user_id = Column(String(255), comment='用户id')
    unionid = Column(String(255), comment='微信平台id')
    phone = Column(String(20), comment='手机号')


class WechatMiniConf(Base):
    __tablename__ = 'wechat_mini_conf'
    __table_args__ = {'comment': '租户小程序配置'}

    id = Column(INTEGER(10), primary_key=True)
    corpid = Column(String(255), nullable=False, comment='组织编号ID')
    appid = Column(String(255), nullable=False, comment='小程序编号')
    secret = Column(String(255), nullable=False, comment='小程序密钥')
    path = Column(String(255), nullable=False, server_default=text("''"), comment='小程序路径')
    env_id = Column(String(255), nullable=False, comment='中间页H5-环境ID')
    env_version = Column(Enum('release', 'trial', 'develop'), nullable=False, server_default=text("'release'"), comment='小程序环境')
    query = Column(String(255), nullable=False, server_default=text("''"), comment='参数')
    use_type = Column(String(255), nullable=False, server_default=text("'radar'"), comment='使用方式；雷达裂变｜短信传播')
    mchid = Column(String(255), nullable=False, server_default=text("''"), comment='小程序关联支付商户号')
    cert_serial_no = Column(String(255), nullable=False, server_default=text("''"), comment='商户证书序列号')
    api_v3_key = Column(String(255), nullable=False, server_default=text("''"), comment='API v3密钥')


class WeworkConf(Base):
    __tablename__ = 'wework_conf'
    __table_args__ = {'comment': '租户授权应用模版'}

    id = Column(INTEGER(11), primary_key=True, nullable=False)
    corpid = Column(String(255), nullable=False, comment='企业租户编号')
    suite_id = Column(String(255), nullable=False, comment='应用ID')
    permanent_code = Column(String(255), primary_key=True, nullable=False, comment='租户授权码')
    agentid = Column(INTEGER(11), nullable=False, comment='应用数字编号')
    cancel_flag = Column(String(10), nullable=False, server_default=text("'0'"), comment='是否授权启用')
    download_flag = Column(String(10), nullable=False, server_default=text("'0'"), comment='素材下载H5链接 1 采用agentid拼接地址')
    name = Column(String(255), server_default=text("''''''"), comment='授权应用名称')
    logo = Column(String(255), server_default=text("''''''"), comment='授权应用logo')
    auth_mode = Column(String(10), server_default=text("''''''"), comment='授权方式 0为管理员授权；1为成员授权； 空 未知')
    auth_user_userid = Column(String(255), server_default=text("''''''"), comment='授权人应用id')
    open_userid = Column(String(255), server_default=text("''''''"), comment='授权人公共ID')
    auth_user_name = Column(String(255), server_default=text("''''''"), comment='授权人姓名')
    auth_user_avatar = Column(String(255), server_default=text("''''''"), comment='授权人头像')


class WeworkSuite(Base):
    __tablename__ = 'wework_suite'
    __table_args__ = {'comment': '服务商应用模版信息'}

    id = Column(INTEGER(11), primary_key=True, nullable=False)
    suite_id = Column(String(255), primary_key=True, nullable=False, comment='应用模版ID')
    suite_secret = Column(String(255), nullable=False, comment='应用密钥')
    auth_flag = Column(String(10), nullable=False, server_default=text("'0'"), comment='租户是否授权应用权限 1高权限； 0低权限')
    remark = Column(String(255), nullable=False, comment='备注')
    build_type = Column(String(10), nullable=False, comment='应用构建方式 1代建应用；2第三方服务商；0自建应用')


class SignCustomer(Base):
    __tablename__ = 'sign_customer'
    __table_args__ = {'comment': '签约客户表'}

    customer_id = Column(BIGINT(20), primary_key=True, comment='客户ID')
    name = Column(VARCHAR(50), comment='客户名称')
    contact = Column(VARCHAR(50), comment='对接人')
    contact_phone = Column(VARCHAR(11), comment='对接人手机号')
    operator = Column(VARCHAR(50), nullable=False, comment='签约人')
    operator_phone = Column(VARCHAR(50), comment='签约人手机号')
    corpid = Column(ForeignKey('organize.corpid'), index=True, comment='组织编号')
    status = Column(String(10), comment='启动状态；0 未启用； 1已激活；2到期停用；3欠费停用；4禁用；5已解约')
    order_id = Column(BIGINT(20), index=True, comment='当前合同id')
    trial_id = Column(BIGINT(20), comment='使用体验id')
    work_person_id = Column(INTEGER(11), comment='work 联系人id')
    work_id = Column(INTEGER(11), server_default=text("0"), comment='work数据同步id')
    grade = Column(String(10), comment='购买等级；try 试用；official购买')
    delete_flag = Column(String(1), server_default=text("'0'"), comment='删除标志；0:未删除；1:已删除')
    add_time = Column(DateTime, server_default=text("current_timestamp()"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("current_timestamp() ON UPDATE current_timestamp()"), comment='修改时间')

    organize = relationship('Organize')


class SignOrder(Base):
    __tablename__ = 'sign_order'
    __table_args__ = {'comment': '签约订单表'}

    order_id = Column(BIGINT(20), primary_key=True, comment='订单ID')
    customer_id = Column(ForeignKey('sign_customer.customer_id'), nullable=False, index=True, comment='客户ID')
    amount = Column(INTEGER(11), nullable=False, server_default=text("0"), comment='合同金额')
    contract_no = Column(VARCHAR(50), nullable=False, server_default=text("''"), comment='合同单号')
    status = Column(VARCHAR(10), server_default=text("'0'"), comment='状态；0 未生效；1 生效；2 到期作废；3 主动作废')
    sys_version = Column(VARCHAR(50), nullable=False, comment='系统版本')
    start_time = Column(DateTime, comment='合同开始时间')
    end_time = Column(DateTime, comment='合同结束时间')
    void_time = Column(DateTime, comment='合同作废时间')
    operator = Column(VARCHAR(50), nullable=False, comment='签约人')
    operator_phone = Column(VARCHAR(50), comment='签约人手机号')
    contact = Column(VARCHAR(50), nullable=False, comment='对接人')
    contact_phone = Column(VARCHAR(11), nullable=False, comment='对接人手机号')
    work_id = Column(INTEGER(11), server_default=text("0"), comment='work数据同步id')
    remark = Column(String(255), server_default=text("''"), comment='备注信息')
    add_time = Column(DateTime, server_default=text("current_timestamp()"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("current_timestamp() ON UPDATE current_timestamp()"), comment='修改时间')
    private_no = Column(INTEGER(11), server_default=text("0"), comment='私域账号数量')
    common_no = Column(INTEGER(11), server_default=text("0"), comment='公域账号数量')
    oss_size = Column(INTEGER(11), server_default=text("0"), comment='oss存储')
    oss_flow = Column(INTEGER(11), server_default=text("0"), comment='oss流量')
    systemid = Column(String(50), comment='系统id')

    customer = relationship('SignCustomer')


class SignTrial(Base):
    __tablename__ = 'sign_trial'
    __table_args__ = {'comment': '签约体验表'}

    trial_id = Column(BIGINT(20), primary_key=True, comment='使用体验id')
    customer_id = Column(ForeignKey('sign_customer.customer_id'), nullable=False, index=True, comment='客户ID')
    status = Column(String(10), server_default=text("'0'"), comment='状态；0 未生效；1 生效；2 到期停用；3 主动停用')
    sys_version = Column(String(10), nullable=False, comment='系统版本')
    systemid = Column(String(50), comment='系统ID')
    sys_url = Column(Text, comment='试用链接')
    remark = Column(String(255), server_default=text("''"), comment='备注信息')
    start_time = Column(DateTime, comment='开始时间')
    end_time = Column(DateTime, comment='结束时间')
    add_time = Column(DateTime, server_default=text("current_timestamp()"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("current_timestamp() ON UPDATE current_timestamp()"), comment='修改时间')

    customer = relationship('SignCustomer')
