# -*- coding: utf-8 -*-
import os
import qrcode
import imgkit
import json
import subprocess
import xmltodict
import random
import hashlib
import markdown
import datetime
import math
import time
import re
import string
import ffmpeg
import snowflake.client
from calendar import monthrange
from PIL import ImageDraw, Image
from PIL import ImageFont
from io import BytesIO
from functools import reduce
from settings import BASE_DIR, KNOWLEDGE_OSS_BUCKET_NAME, JUSURE_OCR_API, IS_910B_EMBEDDINGS
from urllib.parse import quote, urlparse, unquote
from func_timeout import func_set_timeout
from typing import List, Dict, Optional, Tuple
import plotly.io as pio
import plotly.graph_objects as go
import urllib
import fitz
import requests, uuid
from itertools import islice
from docx import Document
from rapidocr_onnxruntime import RapidOCR
import pdfplumber
from pptx import Presentation
import pandas as pd
import subprocess
import asyncio
import aiohttp
from img2table.tables.objects.extraction import ExtractedTable
from lib_func.logger import logger
import zipfile
import base64
from bs4 import BeautifulSoup
from functools import wraps
from Crypto.Cipher import DES3
from Crypto.Util.Padding import pad, unpad

def check_args(func):
    @wraps(func)
    def inner(*args, **kwargs):
        if len(args) == 2:
            return func(args[0], *args[1], **kwargs)
        else:
            return func(*args, **kwargs)
    return inner


def parse_date(date_str):
    """
    将传入的日期字符串解析为 'YYYY-MM-DD' 格式，仅保留年月日部分.
    """
    try:
        date_str = str(date_str)
        date_str = date_str.split(" ")[0]  # 只保留日期部分
        return datetime.datetime.strptime(date_str, "%Y-%m-%d")
    except ValueError:
        raise ValueError('Invalid date format')  # 如果日期格式不正确，返回 None

def list_dict_duplicate_removal(data_list):
    """
    去重list 包含重复字典的元素
    """
    run_function = lambda x, y: x if y in x else x + [y]
    return reduce(run_function, [[], ] + data_list)


def draw_table(headers: List[str], cells: List[list], file_name):
    """
    画表

    :param headers: header=dict(values=['A Scores', 'B Scores'])
    :param cells: cells=dict(values=[[100, 90, 80, 90], [95, 85, 75, 95]])
    :param file_name: 文件名
    :return:
    """
    pio.kaleido.scope.default_width = 1500
    pio.kaleido.scope.default_height = 250 + len(cells[0]) * 20
    fig = go.Figure(data=[go.Table(header=dict(values=headers), cells=dict(values=cells))])
    # fig.show()

    path = "upload/reimbursement/"
    # 文件夹不存在创建文件夹
    if not os.path.exists(os.path.join(BASE_DIR, path, )):
        # 创建多层文件夹
        os.makedirs(os.path.join(BASE_DIR, path, ))
    image_file = f'{file_name}.png'
    fig.write_image(os.path.join(BASE_DIR, path + image_file, ))
    return os.path.join(BASE_DIR, path + image_file, )


def text_img(text, line_size=30, margin_size=50):
    font_conf = {
        'type': 'PingFang.ttc',
        'size': 30,
        'rgb': tuple([112, 130, 105])
    }
    bg_conf = {
        'rgb': tuple([240, 255, 255])
    }
    text_line = [x for x in text.split('\n') if x]
    line_count = math.ceil(len(text) / line_size) + len(text_line)
    font = ImageFont.truetype(font_conf['type'], size=font_conf['size'])

    # calculate the picture size
    fwidth, fheight = font.getsize('中' * line_size)
    owidth, oheight = font.getoffset('中' * line_size)
    pic_size = [margin_size * 2 + fwidth + owidth, (fheight + oheight) * line_count]

    # create new picture
    pic = Image.new('RGB', pic_size, bg_conf['rgb'])
    draw = ImageDraw.Draw(pic)
    all_line_i = 0
    for index, one in enumerate(text_line):
        for i in range(math.ceil(len(one) / line_size)):
            all_line_i += 0 if index == 0 and i == 0 else 1
            # draw lines
            write_text = one[i * line_size:(i + 1) * line_size]
            draw.multiline_text((margin_size, (fheight + oheight) * all_line_i), write_text, font_conf['rgb'], font)
    bytesIO = BytesIO()
    pic.save(bytesIO, format='JPEG')
    return bytesIO.getvalue()

def is_continuous_table(table1, table2):
    """
    判断两个表格是否为连续的跨页表格
    :param table1: 前一个表格
    :param table2: 后一个表格
    :return: 是否为连续表格
    """
    # 检查输入是否有效
    if table1 is None or table2 is None or not table1.content or not table2.content:
        return False
    # 这里简单假设表格的列数相同且相邻页的表格可能是连续的
    return len(table1.content[0]) == len(table2.content[0])

def combine_tables(table1, table2):
    """
    拼接两个连续的表格
    :param table1: 前一个表格
    :param table2: 后一个表格
    :return: 拼接后的表格
    """
    # 检查输入是否有效
    if table1 is None or table2 is None or not table1.content or not table2.content:
        return table1 if table1 is not None else table2

    new_content = table1.content.copy()
    last_row_id = max(new_content.keys()) + 1
    for row in table2.content.values():
        new_content[last_row_id] = row
        last_row_id += 1

    # 这里假设新表格的 bbox 和 title 沿用 table1 的，可根据实际情况调整
    return ExtractedTable(bbox=table1.bbox, title=table1.title, content=new_content)


def word_tables_to_csv(doc_path, output_dir):
    from module import minio_util
    current_dir = os.path.dirname(os.path.abspath(__file__))
    temp_dir = os.path.join(current_dir, 'temp_files')
    temp_file_path_docx = os.path.join(temp_dir, f"{os.path.basename(doc_path)}")
    try:
        doc = Document(doc_path)
    except Exception as e:
        print(f"打开文件时出现异常: {e}，尝试进行文件转换...")
        try:
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
            command = f"soffice --headless --convert-to docx --outdir {temp_dir} {doc_path}"
            subprocess.run(command, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            print(f"文件转换成功，新文件路径: {temp_file_path_docx}")
            doc = Document(temp_file_path_docx)
        except subprocess.CalledProcessError as e:
            print(f"soffice文件转换失败: {e}")
            return []
    os.makedirs(output_dir, exist_ok=True)
    csv_paths = []
    bucket_name = KNOWLEDGE_OSS_BUCKET_NAME
    try:
        for i, table in enumerate(doc.tables, 1):
            # 提取表格数据
            table_data = []
            for row in table.rows:
                row_data = [cell.text for cell in row.cells]
                table_data.append(row_data)
            # 转换为 DataFrame
            df = pd.DataFrame(table_data)

            # 生成唯一的 CSV 文件名
            csv_filename = f'table_{get_snowflake_id()}.csv'
            csv_path = os.path.join(output_dir, csv_filename)
            df.to_csv(csv_path, index=False, header=True)
            with open(csv_path, 'rb') as f:
                #跳过第一行
                next(f) 
                csv_data = f.read()
                csv_url = minio_util.upload_to_minio(bucket_name,csv_filename, csv_data)
            csv_paths.append(csv_url)
        return csv_paths
    except Exception as e:
        logger.error(f"word_tables_to_csv error: {e}")
        return []
    finally:
        if os.path.exists(temp_file_path_docx):
            os.remove(temp_file_path_docx)

def draw_img_text(img_data, text, txt_color, txt_size, txt_site=None, x=None, y=None):
    if type(img_data) is str:
        image = Image.open(BytesIO(requests.get(img_data).content))
    else:
        image = Image.open(BytesIO(img_data))
    font = ImageFont.truetype("PingFang.ttc", txt_size)
    draw_img = ImageDraw.Draw(image)

    if not txt_site:
        image_width, image_height = image.size
        text_width, text_height = draw_img.textsize(text, font)

        if text_width >= image_width or len(text) >= 15:
            text = text[:15] + '...'
            text_width, text_height = draw_img.textsize(text, font)

        cx = (image_width - text_width) / 2
        cy = (image_height - text_height) / 2
        txt_site = [cx, cy]
        if x:
            txt_site[0] = x
        if y:
            txt_site[1] = y
        txt_site = tuple(txt_site)

    draw_img.text(txt_site, text, fill=txt_color, font=font)
    bytesIO = BytesIO()
    image.save(bytesIO, format='JPEG')
    return bytesIO.getvalue()


def get_snowflake_id():
    return str(snowflake.client.get_guid())


def get_age(birthday):
    # 本函数根据输入的8位出生年月日数据返回截至当天的年龄
    today = str(datetime.datetime.now().strftime('%Y-%m-%d')).split("-")
    # 取出系统当天的年月日数据为列表[年,月,日]
    n_monthandday = today[1] + today[2]
    # 将月日连接在一起
    n_year = today[0]
    # 单独列出当年年份
    r_monthandday = birthday[4:]
    # 取出输入日期的月与日
    r_year = birthday[:4]
    # 取出输入日期的年份

    if (int(n_monthandday) >= int(r_monthandday)):

        # 如果月日比系统月日数据要小，刚直接用年份相减就是
        r_age = int(n_year) - int(r_year)
    else:
        r_age = int(n_year) - int(r_year) - 1
    return r_age


def generate_random_str(random_length=16):
    """
    生成一个指定长度的随机字符串
    """
    random_str = ''
    base_str = 'ABCDEFGHIGKLMNOPQRSTUVWXYZabcdefghigklmnopqrstuvwxyz0123456789'
    length = len(base_str) - 1
    for i in range(random_length):
        random_str += base_str[random.randint(0, length)]
    return random_str


def datetime_to_dt_str(date_time):
    return date_time.strftime("%Y-%m-%d %H:%M:%S")


def datetime_to_d_str(date_time):
    return date_time.strftime("%Y-%m-%d")


def now_datetime_str():
    return datetime.datetime.now().strftime('%Y%m%d%H%M%S')

def now_datetime_str_format():
    return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

def now_date_str():
    return datetime.datetime.now().strftime('%Y-%m-%d')


def str_to_datetime(date_string):
    return datetime.datetime.strptime(date_string, "%Y-%m-%d %H:%M:%S")


def str_to_date(date_string):
    return datetime.datetime.strptime(date_string, "%Y-%m-%d")


def datetimeFormat(timestamp, ms=False):
    if ms:
        timestamp = int(timestamp / 1000)
    timeArray = time.localtime(timestamp)
    return time.strftime("%Y-%m-%d %H:%M:%S", timeArray)


def timestampToDatetime(timestamp):
    if isinstance(timestamp, str):
        timestamp = int(timestamp)
    return datetime.datetime.fromtimestamp(timestamp)


def timestamp_to_d_str(timestamp):
    if isinstance(timestamp, str):
        timestamp = int(timestamp)
    return datetime.datetime.strftime(datetime.datetime.fromtimestamp(timestamp), "%Y-%m-%d")


def timestamp_to_dt_str(timestamp):
    if isinstance(timestamp, str):
        timestamp = int(timestamp)
    return datetime.datetime.strftime(datetime.datetime.fromtimestamp(timestamp), "%Y-%m-%d %H:%M:%S")


def date_to_stamp(dateString):
    try:
        timeArray = time.strptime(dateString, "%Y-%m-%d")
        ret = int(time.mktime(timeArray))
    except ValueError:
        ret = 0
    return ret


def get_now_time(is_ms=False):
    if is_ms:
        return int(round(time.time() * 1000))
    else:
        return int(time.time())


def md5(msg, is_upper=False):
    if type(msg) is not bytes:
        msg = bytes(msg, 'utf-8')
    sh = hashlib.md5()
    sh.update(msg)
    ret = sh.hexdigest()
    if is_upper:
        return ret.upper()
    else:
        return ret.lower()


# 生成二维码code
# url 必须带着http或者https
def generate_qrcode(url, content_title=None, qrcode_path=None):
    # img = qrcode.make('https://www.baidu.com')
    if url.count('http') == 0:
        return None
    img = qrcode.make(url)
    # font_path = "/usr/share/fonts/wqy-zenhei/wqy-zenhei.ttc"
    # draw = ImageDraw.Draw(img)
    # ft = ImageFont.truetype('/System/Library/Fonts/PingFang.ttc', 20)
    # ft = ImageFont.truetype(font_path, 20)
    # draw.text((150, 300), u'微信扫码下载', fill='black', font=ft)
    # draw.text((150, 5), content_title, fill='black', font=ft)
    # img.save(qrcode_path)
    bytesIO = BytesIO()
    img.save(bytesIO, format='JPEG')
    return bytesIO.getvalue()


# pdf文档链接转成图片
def pdf2img(pdf_url):
    file_byte_list = list()
    try:
        base_file_name = os.path.basename(pdf_url)
        req = requests.get(pdf_url)
        with fitz.Document(stream=req.content, filetype='pdf') as pdf_doc:
            for pg in pdf_doc:
                rotate = int(0)
                zoom_x = 1.33333333
                zoom_y = 1.33333333
                mat = fitz.Matrix(zoom_x, zoom_y).prerotate(rotate)
                pix = pg.get_pixmap(matrix=mat, alpha=False)
                img_bytes = pix.tobytes()
                file_name = '{}{}.jpeg'.format(base_file_name, pg.number)
                file_byte_list.append((img_bytes, file_name))
        return file_byte_list
    except Exception as err:
        return file_byte_list


# 将普通文章-url转img
def url2img(url):
    @func_set_timeout(30)
    def inner():
        option = {"encoding": "UTF-8"}
        path_wkimg = r'/usr/local/bin/wkhtmltoimage'  # 工具路径
        cfg = imgkit.config(wkhtmltoimage=path_wkimg)
        # 将url转为图片
        img_bytes = imgkit.from_url(url, '', config=cfg, options=option)
        return img_bytes

    try:
        return inner()
    except:
        return None


# 将md_content转img_path
def markdown2img(content_id, md_content, img_path):
    html = markdown.markdown(md_content)
    html_file = img_path + '/{}.html'.format(content_id)
    img_file = img_path + '/{}.jpg'.format(content_id)

    with open(html_file, 'w+') as f:
        f.write(html)

    # 宽度和高度按理说需要根据html来确定
    option = {
        "encoding": "UTF-8",
        "crop-w": "800",
        "crop-h": "600",
        # 'encoding': "UTF-8",
    }

    path_wkimg = r'/usr/local/bin/wkhtmltoimage'  # 工具路径
    cfg = imgkit.config(wkhtmltoimage=path_wkimg)
    # 1、将html文件转为图片
    imgkit.from_file(html_file, img_file, config=cfg, options=option)
    return img_file


# 定义xml转json的函数
def xmltojson(xmlstr):
    xmlparse = xmltodict.parse(xmlstr)
    json_str = json.dumps(xmlparse, indent=1)
    json_str = json_str.strip()
    ret = json.loads(json_str)
    return ret


# 语句参数融合
def join_params(*args, **kwargs):
    def __inner(content):
        ret = '*'
        _type = type(content)
        if _type in filter_keys:
            if _type == bytes and len(content) > 10:
                ret = '*bytes*'
            elif _type == list and len(content) > 10:
                ret = '*list*'
            elif _type == dict and len(content) > 5:
                ret = '*dict*'
            elif _type == str and len(content) > 300:
                ret = '*string*'
            else:
                ret = content
        return ret

    try:
        filter_keys = [dict, list, str, bool, int, bytes]
        ret_list = list()
        ret_json = dict()
        for one in args:
            ret_list.append(__inner(one))
        for key, value in kwargs.items():
            ret_json[key] = __inner(value)
        return json.dumps(ret_list) + '_' + json.dumps(ret_json, cls=CJsonEncoder)
    except Exception as err:
        return ''


class CJsonEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        return json.JSONEncoder.default(self, obj)


def get_bytes_from_url(img_url):
    file_bytes = None
    if 'http' in img_url or 'https' in img_url:
        url = quote(img_url, safe=string.printable)
        file_bytes = urllib.request.urlopen(url).read()
    return file_bytes


def analyse_markdown(text):
    data_list = list()
    img_url_list = list()
    if "![image]" in text:
        mid_str = ''
        str_list = text.split('![image]')
        for index, one_str in enumerate(str_list):
            ret = re.findall(r'^\(((https|http)?://.*\.(jpg|jpeg|png|gif|bmp))', one_str, re.DOTALL)

            # 有结果就是图片
            if ret:
                one_data = ret[0][0]
                img_url_list.append(one_data)
                if mid_str:
                    data_list.append({'type': 'txt', 'data': mid_str})
                    mid_str = ''
                mid_str += one_str[len(one_data) + 2:]
                if len(str_list) - 1 == index:
                    data_list.append({'type': 'txt', 'data': mid_str})
                data_list.append({'type': 'img', 'data': one_data})
            # 否则就是文本
            else:
                mid_str += one_str
                # 最后一个数据填充
                if len(str_list) - 1 == index:
                    data_list.append({'type': 'txt', 'data': mid_str})
    else:
        data_list = [{'type': 'txt', 'data': text}]
    return img_url_list, data_list


# 生成一个随机字符串
def gen_random_string(length=16):
    seed = "1231234567890456123456789071234567890890"
    sa = []
    for i in range(length):
        sa.append(random.choice(seed))
    return ''.join(sa)


# 手机号校验
def phone_check(string):
    if type(string) is not str:
        return False
    preg = re.compile('^1(3\d|4[5-9]|5[0-35-9]|6[567]|7[0-8]|8\d|9[0-35-9])\d{8}$')
    ret = preg.match(string)
    if ret is None:
        return False
    else:
        return True


# 获取图片的比例
def base_get_scale(width, height):
    if width / height <= 0.27:
        scale = '3:11'
    elif 0.27 < width / height <= 0.53:
        scale = '1:2'
    elif 0.53 < width / height <= 0.65:
        scale = '9:16'
    elif 0.65 < width / height <= 0.85:
        scale = '3:4'
    elif 0.85 < width / height <= 1.13:
        scale = '1:1'
    elif 1.13 < width / height <= 1.5:
        scale = '4:3'
    elif 1.5 < width / height <= 1.85:
        scale = '16:9'
    else:
        scale = '2:1'
    return scale


# 图片叠加
def poster(decal_data, baseboard_data, decal_location, size_no=None, decal_resize=None, cpu_resize=False):
    """
    :param decal_data: 贴画url地址 或者 字节
    :param baseboard_data: 底图URL地址
    :param decal_location: 贴画的位置 (x,y)
    :param size_no: 原始贴画值
    :param decal_resize: 贴画的缩放大小 (w,h)
    :param cpu_resize: 是否计算缩放值
    :return: 组合图片的byte字节
    """
    if type(decal_data) is str:
        qr_code_img = Image.open(BytesIO(requests.get(decal_data).content))
    else:
        qr_code_img = Image.open(BytesIO(decal_data))

    if type(baseboard_data) is str:
        baseboard_img = Image.open(BytesIO(requests.get(baseboard_data).content))
    else:
        baseboard_img = Image.open(BytesIO(baseboard_data))

    w_h = baseboard_img.size
    if decal_resize:
        qr_code_img = qr_code_img.resize(decal_resize)
    else:
        if size_no is not None:
            # 按底图的百分比计算 最小边的1/5
            if size_no == 0:
                resize_no = min(w_h) // 5
            else:
                if cpu_resize:
                    # 缩放比例
                    x = int(w_h[0]) / 162
                    resize_no = int(size_no * x)

                    # # 参照比例数据
                    # scale_w, scale_h = base_get_scale(*w_h).split(':')
                    # web_cut_h = int((162 / int(scale_w)) * int(scale_h))
                    # actual_cut_h = int(w_h[1]) // x
                    # offset_px = int((actual_cut_h - web_cut_h))
                    #
                    # decal_location = (int(decal_location[0] * x), int(int(decal_location[1] + offset_px) * x))
                    x = int(w_h[0]) / 162
                    decal_location = (int(decal_location[0] * x), int(decal_location[1] * x))
                else:
                    resize_no = size_no
            qr_code_img = qr_code_img.resize((resize_no, resize_no))
    baseboard_img.paste(qr_code_img, decal_location)
    if baseboard_img.mode == 'RGBA':
        baseboard_img = baseboard_img.convert('RGB')
    bytesIO = BytesIO()
    baseboard_img.save(bytesIO, format='JPEG')
    return bytesIO.getvalue()


def args_filter(param):
    """
    过滤空参数
    """
    if param:
        new_args = {}
        res = [param]
        for dict_values in res:
            result = {key: value for key, value in dict_values.items() if value is not None and value != ''}
            new_args.update(result)
        return new_args
    else:
        return False


def get_env_params():
    if os.environ.get('ENVIRONMENT', '').lower() == 'prod':
        api_fix = '%2Fapi%2Fprod'
        env_version = 'release'
        env_key = 'prod'
    else:
        api_fix = '%2Fapi%2Ftt'
        env_version = 'trial'
        env_key = 'tt'
    return api_fix, env_version, env_key


def upload_file(file, path):
    """
    上传文件
    """
    filename = file.filename
    # 获取文件类型
    file_type = filename.rsplit('.', 1)[1].lower()
    filename = filename.split('.')[0] + '.' + file_type
    upload_path = BASE_DIR + path

    # 文件夹不存在创建文件夹
    if not os.path.exists(upload_path):
        # 创建多层文件夹
        os.makedirs(upload_path)
    # 保存文件
    file.save(os.path.join(upload_path, filename))
    # 获取文件保存路径
    save_url = path + f'{filename}'
    return {"url": save_url, "filename": filename, "file_type": file_type}


def get_video_info(video_url):
    probe = ffmpeg.probe(video_url)
    videoinfo = next(stream for stream in probe['streams'] if stream['codec_type'] == 'video')

    return {
        'duration': videoinfo['duration'],
        'width': videoinfo['width'],
        'height': videoinfo['height'],
        'bit_rate': videoinfo['bit_rate'],
    }


def sha256(msg):
    sh = hashlib.sha256()
    if type(msg) is str:
        msg = msg.encode()
    sh.update(msg)
    ret = sh.hexdigest()
    return ret.upper()


def get_uuid():
    return str(uuid.uuid4())


def convert_seconds(seconds):
    if not seconds:
        return "00天00时00分"
    if seconds < 60:
        return "00天00时01分"
    # 一天有86400秒
    days = seconds // 86400
    seconds %= 86400

    # 一小时有3600秒
    hours = seconds // 3600
    seconds %= 3600

    # 一分钟有60秒
    minutes = seconds // 60
    seconds %= 60

    # 返回格式化后的字符串
    return "{:02}天{:02}时{:02}分".format(days, hours, minutes, seconds)


def extract_images_with_bs4(docx_path: str, output_folder: str) -> tuple:
    """
    使用BeautifulSoup方法从docx文件中提取图片
    
    Args:
        docx_path: docx文件路径
        output_folder: 图片输出目录
    
    Returns:
        tuple: (img_list, position_list)
    """
    img_list = []
    position_list = []
    
    try:
        with zipfile.ZipFile(docx_path, 'r') as zip_ref:
            # 获取所有图片文件，过滤掉目录项
            image_files = [f for f in zip_ref.namelist() 
                         if f.startswith('word/media/') 
                         and f != 'word/media/' 
                         and any(f.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp'])]
            logger.info(f"找到的图片文件: {image_files}")
            
            # 提取并保存所有图片
            for image_file in image_files:
                # 从zip文件直接读取图片数据
                with zip_ref.open(image_file) as image_data:
                    # 生成目标路径
                    image_filename = os.path.basename(image_file)
                    output_path = os.path.join(output_folder, image_filename)
                    # 直接写入到输出目录
                    with open(output_path, 'wb') as f:
                        f.write(image_data.read())
                    img_list.append(output_path)
            
            # 直接从zip文件读取document.xml内容
            with zip_ref.open('word/document.xml') as doc_xml:
                soup = BeautifulSoup(doc_xml.read(), 'xml')
                
                # 获取所有段落
                para_index = 0
                for p in soup.find_all('w:p'):
                    # 获取段落文本
                    text_elements = p.find_all('w:t')
                    has_text = False
                    if text_elements:
                        text = ''.join(t.text for t in text_elements)
                        if text.strip():
                            has_text = True
                            para_index += 1
                    
                    # 检查段落中的图片引用
                    drawings = p.find_all('w:drawing')
                    if drawings:
                        for drawing in drawings:
                            blip = drawing.find('a:blip')
                            if blip and 'r:embed' in blip.attrs:
                                position_list.append(para_index)
                    
                    # 如果段落没有文本但有图片，增加段落索引
                    if not has_text and drawings:
                        para_index += 1
        
        # 确保图片列表和位置列表长度一致
        logger.info(f"BeautifulSoup方法提取的图片: {img_list}")
        logger.info(f"图片位置: {position_list}")
        if len(img_list) != len(position_list):
            logger.warning(f"图片数量({len(img_list)})与位置数量({len(position_list)})不匹配，使用默认位置")
            position_list = list(range(len(img_list)))
            
        return img_list, position_list
        
    except Exception as e:
        logger.error(f"BeautifulSoup解析失败: {str(e)}")
        return [], []

def extract_images_from_docx(docx_path, output_folder):
    """
    从docx文件中提取图片，并返回图片列表和位置列表
    先尝试使用python-docx解析，失败后使用BeautifulSoup解析
    """
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        
    img_list = []
    position_list = []
    
    # 首先尝试使用python-docx
    try:
        doc = Document(docx_path)
        from lxml import etree
        
        namespaces = {
            'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main',
            'v': 'urn:schemas-microsoft-com:vml',
            'r': 'http://schemas.openxmlformats.org/officeDocument/2006/relationships'
        }
        
        for para_index, paragraph in enumerate(doc.paragraphs):
            xml_tree = etree.fromstring(etree.tostring(paragraph._element))
            # 检查图片
            for shape in xml_tree.xpath('.//v:shape', namespaces=namespaces):
                for imagedata in shape.xpath('.//v:imagedata', namespaces=namespaces):
                    rId = imagedata.get('{http://schemas.openxmlformats.org/officeDocument/2006/relationships}id')
                    if rId:
                        image_part = doc.part.rels[rId]._target
                        image_filename = os.path.basename(image_part.partname)
                        image_data = image_part.blob
                        new_image_path = os.path.join(output_folder, image_filename)
                        with open(new_image_path, 'wb') as dst_file:
                            dst_file.write(image_data)
                        img_list.append(new_image_path)
                        position_list.append(para_index)
                        
        if img_list:  # 如果成功提取到图片，直接返回
            return img_list, position_list
            
    except Exception as e:
        logger.warning(f"使用python-docx解析失败，尝试使用BeautifulSoup方法: {str(e)}")
    
    # 如果python-docx方法失败，使用BeautifulSoup方法
    return extract_images_with_bs4(docx_path, output_folder)

def process_mime_doc(doc_content_bytes):
        try:
            if isinstance(doc_content_bytes, bytes):
                doc_content = doc_content_bytes.decode('utf-8', errors='ignore')
            else:
                doc_content = doc_content_bytes
            # 提取边界分隔符
            boundary_start = doc_content.find('boundary="') + len('boundary="')
            boundary_end = doc_content.find('"', boundary_start)
            boundary = doc_content[boundary_start:boundary_end]
            full_boundary = f"------{boundary}"

            # 查找 HTML 部分
            html_start = doc_content.find('Content-Type: text/html; charset="unicode"')
            if html_start != -1:
                html_start = doc_content.find('\n', html_start) + 1
                # 使用动态获取的边界分隔符查找 HTML 内容的结束位置
                html_end = doc_content.find(full_boundary, html_start)
                html_content = doc_content[html_start:html_end].strip()

                # 解码 Base64 内容
                html_decoded = base64.b64decode(html_content).decode('utf-16', errors='ignore')

                soup = BeautifulSoup(html_decoded, 'html.parser')
                text_content = soup.get_text()
                byte_stream = text_content.encode('utf-8')
                return byte_stream
            else:
                logger.info("未找到 HTML 部分")
                return doc_content_bytes
        except Exception as e:
            logger.info(f"读取文件时发生错误: {str(e)}")
            return doc_content_bytes

def get_formatted_content():
    # 获取今天的日期和时间
    today = datetime.datetime.now()

    # 提取年份和日期
    year = today.year
    month = today.month
    day = today.day

    # 获取今天是星期几（0代表星期一，6代表星期日）
    today_weekday = today.weekday()

    # 创建一个日期对象用于格式化输出
    formatted_today = today.strftime("%Y年%m月%d日")

    # 计算明天的日期
    tomorrow = today + datetime.timedelta(days=1)
    tomorrow_weekday = (today_weekday + 1) % 7  # 0-6循环
    tomorrow_weekday_name = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"][tomorrow_weekday]

    # 计算后天的日期
    after_tomorrow = today + datetime.timedelta(days=2)
    after_tomorrow_weekday = (today_weekday + 2) % 7  # 0-6循环
    after_tomorrow_weekday_name = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"][after_tomorrow_weekday]

    # 计算大后天的日期
    day_after_after_tomorrow = today + datetime.timedelta(days=3)
    day_after_after_tomorrow_weekday = (today_weekday + 3) % 7  # 0-6循环
    day_after_after_tomorrow_weekday_name = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"][day_after_after_tomorrow_weekday]

    # 构建返回内容
    content = f"""
- 今年是{year}年  
- 今天是{["周一", "周二", "周三", "周四", "周五", "周六", "周日"][today_weekday]}  
- 明天是{tomorrow_weekday_name}  
- 后天是{after_tomorrow_weekday_name}  
- 大后天是{day_after_after_tomorrow_weekday_name}  
- 今天的日期{formatted_today}  
"""

    return content


def get_name_from_url(url):
    try:
        parsed_url = urlparse(url)
        path = unquote(parsed_url.path)
        filename = path.split('/')[-1]
        return filename
    except:
        return None


def download_url_file(save_path, url_info):
    try:
        if not os.path.exists(save_path):
            os.makedirs(save_path)
        for name, url in url_info.items():
            file_path = f'{save_path}/{name}'
            if os.path.exists(file_path):
                continue
            else:
                response = requests.get(url)
                if response.status_code == 200:
                    with open(file_path, 'wb') as f:
                        f.write(response.content)
                else:
                    logger.info(f'{url} 下载失败 status_code: {response.status_code}')
        return True
    except Exception as err:
        logger.error(err)
        return None

# todo 生成器类型的还未抽离
def return_ocr_result(headers=None, payload=None, file_path=None, is_yield=False):
    if IS_910B_EMBEDDINGS == 0:
        if not headers or not payload:
            return {'code': 400, 'message': 'Required: headers and payload'} 
        response = requests.request('GET', JUSURE_OCR_API, headers=headers, data=payload)
        if response.status_code == 200:
            docs = response.json()['text']
        else:
            return {'code': 400, 'message': 'ocr识别失败'} 
    else:
        if not file_path:
            return {'code': 400, 'message': 'Required: file_path'}
        try:
            ocr = RapidOCR()
            result, _ = ocr(file_path)
            docs = '\n'.join([line[1] for line in result])
        except:
            return {'code': 400, 'message': 'ocr识别失败'}
    return {'code': 0, 'data': docs}

def convert_xlsx_to_markdown(content,doc_name):
    try:
        # 将字节流保存到临时文件
        excel_data = BytesIO(content)
        
        # 读取 Excel 文件
        df = pd.read_excel(excel_data, engine='openpyxl')
        
        # 将 DataFrame 转换为 Markdown 格式
        markdown_table = df.to_markdown(index=False)
        
        # 将提取的内容转换为字节流并返回
        return markdown_table.encode('utf-8')  # 返回字节流
    except Exception as e:
        raise e

def convert_xls_to_markdown(content,doc_name):
    """
    将 .xls 文件内容转换为 Markdown 表格格式
    :param content: .xls 文件的字节流
    :return: 转换后的 Markdown 字符串（字节流形式）
    """
    try:
        # 将字节流保存到临时文件
        excel_data = BytesIO(content)
        
        # 使用 xlrd 引擎读取 .xls 文件
        df = pd.read_excel(excel_data, engine='xlrd')
        
        # 将 DataFrame 转换为 Markdown 格式
        markdown_table = df.to_markdown(index=False)
        
        # 将提取的内容转换为字节流并返回
        return markdown_table.encode('utf-8')  # 返回字节流
    except Exception as e:
        raise e

def convert_pdf_to_markdown(content,doc_name):
    """
    将 PDF 文件（字节流形式）转换为 Markdown 格式
    :param content: PDF 文件的字节流（bytes类型）
    :param file_type: 文件类型（通常为 'pdf'）
    :return: 转换后的 Markdown 内容（bytes类型）
    """
    try:
        pdf_file = ""
        # 将字节流保存到临时文件
        with BytesIO(content) as temp_pdf:
            with pdfplumber.open(temp_pdf) as pdf:
                for page in pdf.pages:
                    text = page.extract_text()
                    if text:
                        pdf_file += text + "\n"
        
        # 将提取的内容转换为字节流并返回
        return pdf_file.encode('utf-8')  # 返回字节流
    except Exception as e:
        raise e


def convert_txt_to_markdown(content,doc_name):
    """
    将 TXT 文件转换为 Markdown 格式
    """            
    return content


def convert_pptx_to_markdown(content,doc_name):
    prs = Presentation(BytesIO(content))
    markdown_content = ""

    for slide in prs.slides:
        slide_content = ""
        for shape in slide.shapes:
            if hasattr(shape, "text") and shape.text:
                slide_content += shape.text + "\n"
        if slide_content:
            markdown_content += f"## Slide\n\n{slide_content}\n"
    
    return markdown_content.encode('utf-8')

def convert_doc_to_markdown(content, file_type,doc_name):
    """
    使用pypandoc将ppt、pdf、txt等文件格式转换为markdown
    :param content: 文件内容 (bytes类型)
    :param file_type: 文件类型（如 'docx' 等）
    :return: 转换后的markdown内容
    """
    try:
        # 创建临时目录
        # 创建临时目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        temp_dir = os.path.join(current_dir, 'temp_files')
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)

        # 为文件加上正确的扩展名并指定文件路径
        temp_file = os.path.join(temp_dir, f'temp_file_{doc_name}.{file_type}')
        markdown_file = os.path.join(temp_dir, f'temp_file_{doc_name}.md')

        # 将内容写入临时文件
        with open(temp_file, 'wb') as temp_md_file:
            temp_md_file.write(content)


        try:
            command = ["pandoc", "-s", temp_file, "-t", "markdown", "-o", markdown_file]
            subprocess.run(command, check=True)
        except Exception as e:
            raise e

        # 检查转换后的文件是否存在
        if not os.path.exists(markdown_file):
            raise FileNotFoundError(f"无法找到转换后的文件: {markdown_file}")

        # 读取转换后的 markdown 内容
        with open(markdown_file, 'rb') as f:
            markdown_content = f.read()

        # 删除临时文件
        os.remove(temp_file)
        os.remove(markdown_file)

        return markdown_content

    except Exception as e:
        raise e


def check_for_malicious_code(script_content):
    dangerous_patterns = [
        r"os\.system",       # os.system 调用
        r"os\.remove",       # os.remove 删除文件
        r"subprocess\.run",  # subprocess.run
        r"rm\s+-rf",         # rm -rf 命令
        r"shutil\.rmtree",   # shutil.rmtree
        r"eval",             # eval 函数
        r"exec",             # exec 函数
    ]
    
    for pattern in dangerous_patterns:
        if re.search(pattern, script_content):
            return False  # 检测到恶意代码，返回 False
    return True  # 没有恶意代码，返回 True

def download_and_verify_scripts(file_url, base_path):
    """
    下载并验证单个脚本文件，检查文件的完整性及内容。
    :param file_url: 脚本文件 URL
    :param base_path: 下载目录
    :return: 下载并验证成功返回 True，否则抛出异常
    """
    # 检查并创建文件夹
    from module import minio_util

    if not os.path.exists(base_path):
        os.makedirs(base_path)

    try:
        # 使用 minio_util 下载文件到指定目录
        file_name = file_url.split('/')[-1]  # 从 URL 提取文件名
        script_path = os.path.join(base_path, file_name)

        minio_util.download_file_use_requests(file_url, script_path)  # 假设函数支持接受 URL 列表
    except Exception as download_error:
        raise

    # 检查文件是否存在
    if not os.path.exists(script_path):
        raise FileNotFoundError(f"文件 {script_path} 不存在")

    try:
        # 读取文件内容并检查是否包含恶意代码
        with open(script_path, 'r', encoding='utf-8') as file:
            script_content = file.read()
            if not check_for_malicious_code(script_content):
                raise ValueError(f"脚本 {file_name} 包含恶意代码，已被拒绝执行")
    except Exception as e:
        raise

    return True

def get_previous_period(start_date, end_date):
    """
    根据给定的开始日期和结束日期计算其前一个周期的开始日期和结束日期。
    参数:
        start_date (date(2024, 1, 1)): 当前周期的开始日期。
        end_date (date(2024, 2, 1)): 当前周期的结束日期。
    返回:
        tuple: 包含两个元素的元组，分别为前一周期的开始日期和结束日期。
    """
    delta = end_date - start_date
    previous_end_date = start_date - datetime.timedelta(days=1)
    previous_start_date = previous_end_date - delta
    return previous_start_date, previous_end_date

def create_temp_dir():
    current_dir = os.path.dirname(os.path.abspath(__file__))
    temp_dir = os.path.join(current_dir, 'temp_files')
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)
    return temp_dir

def extract_and_upload_images(doc, temp_dir):
    from module import minio_util
    image_urls = []
    image_index = 0
    bucket_name = KNOWLEDGE_OSS_BUCKET_NAME
    try:
        for rel in doc.part.rels.values():
            if "image" in rel.target_ref:
                image_index += 1
                image_data = rel.target_part.blob
                id = str(uuid.uuid4())
                image_name = f"knowledge/{id}_image_{image_index}.png"
                image_url = minio_util.upload_to_minio(bucket_name,image_name, image_data)
                image_urls.append(image_url)
    except Exception as e:
        raise e
    return image_urls

# 用 markitdown 转换 DOCX 为 Markdown，并替换图片 URL
def convert_docx_by_MarkItDown(content, file_type, doc_name):
    try:
        temp_dir = create_temp_dir()
        
        # 为文件加上正确的扩展名并指定文件路径
        temp_file = os.path.join(temp_dir, f'temp_file_{doc_name}.{file_type}')
        markdown_file = os.path.join(temp_dir, f'temp_file_{doc_name}.md')

        # 将内容写入临时文件
        with open(temp_file, 'wb') as temp_md_file:
            temp_md_file.write(content)

        # 使用 markitdown 执行转换
        try:
            with open(markdown_file, 'w') as markdown_out:
                command = ["markitdown", temp_file]
                subprocess.run(command, check=True, stdout=markdown_out)

        except Exception as e:
            raise Exception(f"markitdown 转换失败: {e}")

        # 读取 DOCX 文件并提取图片
        doc = Document(BytesIO(content))
        image_urls = extract_and_upload_images(doc, temp_dir)
        # 读取转换后的 Markdown 内容
        with open(markdown_file, 'r', encoding='utf-8') as md_file:
            markdown_content = md_file.read()

        # 按顺序替换 Base64 图片为 MinIO URL
        # 正则表达式：同时匹配有文本描述和无文本描述的 Base64 图片
        base64_pattern = r'!\[.*?\]\(data:image/(?:png|jpg|jpeg);base64.*?\)'
        base64_images = re.findall(base64_pattern, markdown_content)

        # 按顺序替换 Base64 图片为 MinIO URL
        for idx, base64_image in enumerate(base64_images):
            if idx < len(image_urls):
                markdown_content = re.sub(re.escape(base64_image), f'![image]({image_urls[idx]})', markdown_content, count=1)
        # 删除临时文件
    except Exception as e:
        raise e
    
    finally:    
        os.remove(temp_file)
        os.remove(markdown_file)
        markdown_content =markdown_content.encode('utf-8')
        return markdown_content

def convert_otherfile_to_markdown(content, file_type,doc_name):
    """
    使用pypandoc将ppt、pdf、txt等文件格式转换为markdown
    :param content: 文件内容 (bytes类型)
    :param file_type: 文件类型（如 'docx' 等）
    :return: 转换后的markdown内容
    """
    try:
        # 创建临时目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        temp_dir = os.path.join(current_dir, 'temp_files')
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)

        # 为文件加上正确的扩展名并指定文件路径
        temp_file = os.path.join(temp_dir, f'temp_file_{doc_name}.{file_type}')
        markdown_file = os.path.join(temp_dir, f'temp_file_{doc_name}.md')

        # 将内容写入临时文件
        with open(temp_file, 'wb') as temp_md_file:
            temp_md_file.write(content)

        # 使用 subprocess 执行 markitdown 命令并将输出写入文件
        try:
            with open(markdown_file, 'w', encoding="utf-8") as markdown_out:
                command = ["markitdown", temp_file]
                result = subprocess.run(command, check=True, stdout=markdown_out, stderr=subprocess.PIPE)
        except subprocess.CalledProcessError as e:
            raise e

        # 检查转换后的文件是否存在
        if not os.path.exists(markdown_file):
            raise FileNotFoundError(f"无法找到转换后的文件: {markdown_file}")

        # 读取转换后的 markdown 内容
        with open(markdown_file, 'r', encoding="utf-8") as f:
            markdown_content = f.read()

        # 删除临时文件
        try:
            os.remove(temp_file)
            os.remove(markdown_file)
        except FileNotFoundError:
            pass
        return markdown_content.encode('utf-8')

    except Exception as e:
        raise e

def convert_xlsx_to_markdown(content,doc_name):
    try:
        # 将字节流保存到临时文件
        excel_data = BytesIO(content)
        
        # 读取 Excel 文件
        df = pd.read_excel(excel_data, engine='openpyxl')
        
        # 将 DataFrame 转换为 Markdown 格式
        markdown_table = df.to_markdown(index=False)
        
        # 将提取的内容转换为字节流并返回
        return markdown_table.encode('utf-8')  # 返回字节流
    except Exception as e:
        raise e

def convert_xls_to_markdown(content,doc_name):
    """
    将 .xls 文件内容转换为 Markdown 表格格式
    :param content: .xls 文件的字节流
    :return: 转换后的 Markdown 字符串（字节流形式）
    """
    try:
        # 将字节流保存到临时文件
        excel_data = BytesIO(content)
        
        # 使用 xlrd 引擎读取 .xls 文件
        df = pd.read_excel(excel_data, engine='xlrd')
        
        # 将 DataFrame 转换为 Markdown 格式
        markdown_table = df.to_markdown(index=False)
        
        # 将提取的内容转换为字节流并返回
        return markdown_table.encode('utf-8')  # 返回字节流
    except Exception as e:
        raise e

def xls_to_xlsx(doc_file_data, oss_name):
    """
    将 .xls 文件转换为 .xlsx 文件并返回转换后的文件数据
    :param doc_file_data: 传入的文件数据
    :param oss_name: OSS 文件名，用于临时文件命名
    :return: 转换后的 .xlsx 文件数据
    """
    # 获取当前目录并设置临时文件存储路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    temp_dir = os.path.join(current_dir, 'temp_files')
    temp_file_path = os.path.join(temp_dir, f"temp_file_{oss_name}.xls")
    temp_file_path_xlsx = os.path.join(temp_dir, f"temp_file_{oss_name}.xlsx")
    # 创建临时目录（如果不存在）
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)
    # 将传入的 .xls 文件数据写入临时文件
    with open(temp_file_path, "wb") as temp_file:
        temp_file.write(doc_file_data)
    try:
        # 使用 pandas 进行流式读取 .xls 文件并转换为 .xlsx
        # 使用 openpyxl 引擎写入 .xlsx 文件
        xls_file = pd.read_excel(temp_file_path, engine='xlrd')
        with pd.ExcelWriter(temp_file_path_xlsx, engine='openpyxl') as writer:
            # 写入 Excel 文件，保持原有的 sheet 名称
            xls_file.to_excel(writer, index=False, sheet_name="Sheet1")
        # 读取转换后的 .xlsx 文件数据
        with open(temp_file_path_xlsx, "rb") as xlsx_file:
            xlsx_file_data = xlsx_file.read()
    except Exception as e:
        logger.error(f"Error during conversion: {e}")
        xlsx_file_data = None
    finally:
        # 删除临时文件
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)
        if os.path.exists(temp_file_path_xlsx):
            os.remove(temp_file_path_xlsx)
    return xlsx_file_data



def get_ring_times(input_time_str, mode, input_time_format="%Y-%m-%d %H:%M:%S"):
    """
    输入一个时间字符串和模式，返回四个值：
    1. 输入时间当天/当周第一天/当月第一天的0点
    2. 输入的时间
    3. 输入时间前一天/前一周/前一月的0点
    4. 输入时间前一天/前一周/前一月的相同时分秒

    :param input_time_str: 输入的时间字符串，例如 "2025-01-13 14:30:00"
    :param mode: 模式，'day' 表示天模式，'week' 表示周模式，'month' 表示月模式
    :param input_time_format: 输入时间的格式，默认为 "%Y-%m-%d %H:%M:%S"
    :return: 元组 (当天/当周第一天/当月第一天0点, 输入时间, 前一天/前一周/前一月0点, 前一天/前一周/前一月相同时分秒)

    example:
    input_time_str = "2025-01-14 14:30:00"
    mode = "day"  # 可以是 'day', 'week', 'month'
    input_time, start_of_period, previous_period_start, previous_period_same_time = get_times(input_time_str, mode)
    """
    # 将输入的时间字符串转换为datetime对象
    input_time = datetime.datetime.strptime(input_time_str, input_time_format)
    
    if mode == 'day':
        # 当天0点
        start_of_day = input_time.replace(hour=0, minute=0, second=0, microsecond=0)
        # 前一天0点
        previous_day_start = start_of_day - datetime.timedelta(days=1)
        # 前一天相同时分秒
        previous_day_same_time = input_time -  datetime.timedelta(days=1)
    
    elif mode == 'week':
        # 当周第一天0点（周一）
        start_of_week = input_time -  datetime.timedelta(days=input_time.weekday())
        start_of_week = start_of_week.replace(hour=0, minute=0, second=0, microsecond=0)
        # 前一周0点
        previous_week_start = start_of_week - datetime.timedelta(weeks=1)
        # 前一周相同时分秒
        previous_week_same_time = input_time - datetime.timedelta(weeks=1)
    
    elif mode == 'month':
        # 当月第一天0点
        start_of_month = input_time.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        # 前一月0点
        if input_time.month == 1:
            previous_month_year = input_time.year - 1
            previous_month = 12
        else:
            previous_month_year = input_time.year
            previous_month = input_time.month - 1
        previous_month_start = datetime.datetime(previous_month_year, previous_month, 1, 0, 0, 0)
        # 前一月相同时分秒
        days_in_previous_month = monthrange(previous_month_year, previous_month)[1]
        day_of_previous_month = min(input_time.day, days_in_previous_month)
        previous_month_same_time = datetime.datetime(previous_month_year, previous_month, day_of_previous_month, input_time.hour, input_time.minute, input_time.second)
    
    else:
        return ("Mode must be 'day', 'week', or 'month'", ) * 4
    
    if mode == 'day':
        return input_time, start_of_day, previous_day_start, previous_day_same_time
    elif mode == 'week':
        return input_time, start_of_week, previous_week_start, previous_week_same_time
    elif mode == 'month':
        return input_time, start_of_month, previous_month_start, previous_month_same_time
    else:
        return ("Mode must be 'day', 'week', or 'month'", ) * 4


def calculate_chain_ratio_time_ranges(input_time, ratio_type):
    """
    输入一个时间字符串和模式，返回四个值：
    1. 上周期开始时间
    2. 上周期结束时间
    3. 本周期开始时间
    4. 本周期结束时间

    :param input_time_str: 输入的时间字符串，例如 "2025-01-13 14:30:00"
    :param ratio_type: 模式，'daily' 表示天模式，'weekly' 表示周模式，'monthly' 表示月模式
    :return: 元组 

    example:
    input_time_str = "2025-01-14 14:30:00"
    mode = "day"  # 可以是 'daily', 'weekly', 'monthly'
    """
    
    if ratio_type == 'daily':
        # 按日环比
        input_datetime = input_time
        # input_datetime = datetime.fromisoformat(input_time)
        # 计算昨天的时间
        yesterday = input_datetime - datetime.timedelta(days=1)
        # 昨天的零点
        this_period_start = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        # 昨天的输入时间点
        this_period_end = yesterday.replace(hour=input_datetime.hour, minute=input_datetime.minute, second=input_datetime.second, microsecond=input_datetime.microsecond)
        # 前天的时间
        before_yesterday = input_datetime - datetime.timedelta(days=2)
        # 前天的零点
        last_period_start = before_yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        # 前天的输入时间点
        last_period_end = before_yesterday.replace(hour=input_datetime.hour, minute=input_datetime.minute, second=input_datetime.second, microsecond=input_datetime.microsecond)
    elif ratio_type == 'weekly':
        # 按周环比
        # 本周期的开始时间是本周一
        this_period_start = input_time - datetime.timedelta(days=input_time.weekday())
        this_period_start = this_period_start.replace(hour=0, minute=0, second=0, microsecond=0)
        this_period_end = input_time
        # 上个周期的结束时间是输入时间减一天再减一周
        last_period_end = input_time - datetime.timedelta(weeks=1)
        # 上个周期的开始时间是上周一
        last_period_start = this_period_start - datetime.timedelta(days=7)
        last_period_start = last_period_start.replace(hour=0, minute=0, second=0, microsecond=0)
    elif ratio_type == 'monthly':
        # 按月环比
        this_period_start = input_time.replace(day=1)
        this_period_end = input_time
        # 上个周期的结束时间的日期部分与输入时间的日期部分相同，但要考虑月份天数不同的情况
        if input_time.month == 1:
            last_period_end = input_time.replace(year=input_time.year - 1, month=12, day=input_time.day)
        else:
            last_month = input_time.month - 1
            last_year = input_time.year
            # 处理上个月的最后一天
            if last_month == 2:  # 上个月是 2 月
                if (input_time.year % 4 == 0 and input_time.year % 100!= 0) or input_time.year % 400 == 0:
                    last_day = 29
                else:
                    last_day = 28
            elif last_month in [4, 6, 9, 11]:  # 上个月是 4, 6, 9, 11 月
                last_day = 30
            else:
                last_day = 31
            last_period_end = input_time.replace(year=last_year, month=last_month, day=min(input_time.day, last_day))
        # 上个周期的开始时间是上个月的 1 号
        if input_time.month == 1:
            last_period_start = input_time.replace(year=input_time.year - 1, month=12, day=1)
        else:
            last_period_start = input_time.replace(month=input_time.month - 1, day=1)
        this_period_start = this_period_start.replace(hour=0, minute=0, second=0, microsecond=0)
        last_period_start = last_period_start.replace(hour=0, minute=0, second=0, microsecond=0)
    else:
        raise ValueError("Invalid ratio type. Please use 'daily', 'weekly', or 'monthly'.")
    
    return (last_period_start, last_period_end, this_period_start, this_period_end)

async def my_post_request(session, url, data, semaphore):
    """
    @summary:request_task的子函数。可拓展成get put delete option等
    """
    async with semaphore:
        try:
            async with session.post(url, json=data) as response:
                # 检查响应状态码
                response.raise_for_status()
                result = await response.json()
                logger.info(f"Successfully posted to {url}, response: {result}")
                return result
        except aiohttp.ClientError as e:
            # 处理客户端请求相关的错误
            logger.error(f"Error occurred while posting to {url}: {e}")
        except asyncio.TimeoutError:
            # 处理请求超时的情况
            logger.error(f"Timeout occurred while posting to {url}")
        except Exception as e:
            # 处理其他未知错误
            logger.error(f"An unexpected error occurred while posting to {url}: {e}")

async def requset_task(url, data_list, max_concurrency):
    """
    @summary:主函数，协调所有的 POST 请求
    用法：asyncio.run(requset_task(url, data_list, 5))
    """
    semaphore = asyncio.Semaphore(max_concurrency)
    async with aiohttp.ClientSession() as session:
        # tasks = [my_post_request(session, url, data, semaphore) for url, data in zip(urls, data_list)]
        tasks = [my_post_request(session, url, data, semaphore) for data in data_list]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return results
    
def request_description(oss_url):
    import settings
    id = str(uuid.uuid4())
    ip = settings.JUSURE_MULTIMODAL_IMAGE_DESCRIPTION_V1
    headers = {
        "Content-Type": "application/json",
    }
    params ={
        'id' : id,
        'url' :oss_url
    }
    response = requests.request('GET', ip, headers=headers, params=params)

    if response.status_code == 200:
        result = response.json()
        content = result['text'].replace('|||||||||||:','\r\n')
        data = {
            'content': content,
            'character_count': len(content)
        }
        
        return {'code': 1, 'data': data, 'images': [[oss_url]]}

    else:
        return {'code': 0, 'message': '图像处理失败'}
    
async def async_request_description(oss_url, semaphore):
    import settings
    async with semaphore:
        id = str(uuid.uuid4())
        ip = settings.JUSURE_MULTIMODAL_IMAGE_DESCRIPTION_V1
        headers = {
            "Content-Type": "application/json",
        }
        params = {
            'id': id,
            'url': oss_url
        }
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(ip, headers=headers, params=params) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result['text'].replace('|||||||||||:', '\r\n')
                        data = {
                            'content': content,
                            'character_count': len(content)
                        }
                        return {'code': 1, 'data': data, 'images': [[oss_url]]}
                    else:
                        return {'code': 0, 'message': '图像处理失败'}
            except Exception as e:
                return {'code': 0, 'message': f'请求出错: {str(e)}'}
            


def emf_to_png(doc_img_list):
    images_path = os.path.dirname(doc_img_list[0])
    error_list = []
    res_list = []
    file_list = os.listdir(images_path)
    
    for file in file_list:
        name, ext = os.path.splitext(file)  # 使用 os.path.splitext 分割文件名和扩展名
        img_path = os.path.join(images_path, file)  # 使用 os.path.join 构建路径
        save_path = os.path.join(images_path, f"{name}.png")
        
        if ext.lower() == '.emf':
            try:
                # inkscape image1.emf --export-type=png --export-filename=output.png
                command = ["inkscape", img_path, "--export-type=png", f"--export-filename={save_path}"]
                subprocess.run(command)         
                os.remove(img_path)
                res_list.append(save_path)
                logger.info(f"成功将 {img_path} 转换为 {save_path}")
            except subprocess.CalledProcessError as e:
                error_list.append((img_path, str(e)))
            except Exception as e:
                error_list.append((img_path, str(e)))
        else:
            res_list.append(img_path)    
    if error_list:
        return 0, error_list
    return 1, res_list

def ms_to_hms(ms_str):
    try:
        # 将字符串转换为整数类型的毫秒数
        ms = int(ms_str)
        # 计算总秒数
        total_seconds = ms // 1000
        # 计算小时数
        hours = total_seconds // 3600
        # 计算剩余秒数用于计算分钟
        remaining_seconds = total_seconds % 3600
        # 计算分钟数
        minutes = remaining_seconds // 60
        # 计算最终的秒数
        seconds = remaining_seconds % 60
        # 格式化输出为 '时分秒' 格式，不足两位的前面补 0
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    except ValueError:
        logger.error("输入的不是有效的毫秒数，请输入一个能转换为整数的字符串。")
        return None

def concat_video_time_strings(a, b):
    formatted_a = str(a).zfill(2)
    parts = b.split(':')
    formatted_parts = []
    # 补齐分割部分到 3 位
    while len(parts) < 3:
        parts = ['0'] + parts
    for part in parts:
        # 确保每个部分为两位字符串，不足两位时前面补 0
        formatted_parts.append(part.zfill(2))
    result = ':'.join(formatted_parts) + ':' + formatted_a
    return result


def compare_time_with_time_minutes(input_time_str: str, time_minute: int) -> int:
    """
    @summary: 比较输入的时间是否在当前时间的 x 分钟内
    """
    from datetime import datetime, timedelta

    try:
        input_time = datetime.strptime(input_time_str, '%Y-%m-%d %H:%M:%S')
    except ValueError:
        print("输入的时间格式不正确，请使用 'YYYY-MM-DD HH:MM:SS' 格式。")
        return 0
    current_time = datetime.now()
    time_difference = abs(current_time - input_time)
    the_minutes = timedelta(minutes=time_minute)
    if time_difference > the_minutes:
        return 2
    else:
        return 1


def get_file_info(file_path):
    try:
        # 获取文件大小
        file_size = os.path.getsize(file_path)
        # 获取文件类型
        file_type = file_path.split('?')[0].split('.')[-1]
        return file_size, file_type
    except FileNotFoundError:
        print("错误: 文件未找到!")
    except Exception as e:
        print(f"错误: 发生了一个未知错误: {e}")
    return None, None


def encrypt_3des(data: str, key: bytes, iv: bytes) -> str:
    """
    使用 3DES 加密数据
    :param data: 待加密的字符串
    :param key: 24 字节的密钥
    :param iv: 8 字节的初始化向量
    :return: 加密后的 Base64 字符串
    """
    cipher = DES3.new(key, DES3.MODE_CBC, iv)
    padded_data = pad(data.encode('utf-8'), DES3.block_size)
    encrypted_data = cipher.encrypt(padded_data)
    return base64.b64encode(encrypted_data).decode('utf-8')


def decrypt_3des(encrypted_data: str, key: bytes, iv: bytes) -> str:
    """
    使用 3DES 解密数据
    :param encrypted_data: Base64 编码的加密字符串
    :param key: 24 字节的密钥（字节形式）
    :param iv: 8 字节的初始化向量（字节形式）
    :return: 解密后的原始字符串
    """
    # 解码 Base64
    encrypted_bytes = base64.b64decode(encrypted_data)
    # 创建解密器
    cipher = DES3.new(key, DES3.MODE_CBC, iv)
    # 解密并去除填充
    decrypted_data = unpad(cipher.decrypt(encrypted_bytes), DES3.block_size)

    # 返回原始字符串
    return decrypted_data.decode('utf-8')



if __name__ == "__main__":
    # oss_url = 'https://test-oss-bz.oss-cn-beijing.aliyuncs.com/f52985ba-6dc3-4b37-9b1f-3df73bda6d8a/mda-phdtamt9c3asm8tj.mp4'
    # print(get_video_info(oss_url))
    # for i in range(30):
    #     snow_id = get_snowflake_id()
    #     print(f"count: {i}, snow_id: {snow_id}")
    #     i += 1
    # pass
    seconds = None
    print(convert_seconds(seconds))
