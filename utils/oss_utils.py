#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
from aliyunsdkcore import client
import json
from aliyunsdkcore.profile import region_provider
from aliyunsdksts.request.v20150401 import AssumeRoleRequest
import oss2
from settings import BASE_DIR
from controller.manager_controller import MasterAction


class OSSUtil(object):

    def __init__(self, corp_id=''):
        conf_map = MasterAction(corp_id).get_oss_conf()
        if not conf_map:
            raise ValueError(f'租户: {corp_id}无OSS配置！')
        self.keyid = conf_map.get('access_key_id')
        self.secret = conf_map.get('key_secret')
        self.endpoint = conf_map.get('domain', '')
        self.__auth = oss2.Auth(self.keyid, self.secret)
        self.bucket = conf_map.get('bucket')

    def exists(self, filename, bucket_name=None):
        bucket_name = bucket_name or self.bucket
        bucket = oss2.Bucket(self.__auth, self.endpoint, bucket_name)
        return bucket.object_exists(filename)

    def upload(self, filename, content, bucket_name=None):
        bucket_name = bucket_name or self.bucket
        bucket = oss2.Bucket(self.__auth, self.endpoint, bucket_name)
        return bucket.put_object(filename, content)

    def append(self, filename, content, bucket_name=None):
        bucket_name = bucket_name or self.bucket
        bucket = oss2.Bucket(self.__auth, self.endpoint, bucket_name)
        head = bucket.head_object(filename)
        position = head.content_length
        return bucket.append_object(filename, position, content)

    def get_file_length(self, filename, bucket_name=None):
        bucket_name = bucket_name or self.bucket
        try:
            bucket = oss2.Bucket(self.__auth, self.endpoint, bucket_name)
            head = bucket.head_object(filename)
            ret = head.content_length
        except oss2.exceptions.NotFound:
            ret = 0
        return ret

    def get_content(self, filename, bucket_name=None):
        bucket_name = bucket_name or self.bucket
        bucket = oss2.Bucket(self.__auth, self.endpoint, bucket_name)
        result = bucket.get_object(filename)
        return result.read().decode()

    def get_bytes(self, filename, bucket_name=None):
        bucket_name = bucket_name or self.bucket
        bucket = oss2.Bucket(self.__auth, self.endpoint, bucket_name)
        result = bucket.get_object(filename)
        return result.read()

    def get_public_url(self, filename, bucket_name=None, withCdn=True):
        bucket_name = bucket_name or self.bucket
        url = self.endpoint.replace('https://', ''.join(['https://', bucket_name, '.']))
        return ''.join([url, '/', filename])

    def get_download_url(self, filename, bucket_name=None, ttl=1800):
        bucket_name = bucket_name or self.bucket
        bucket = oss2.Bucket(self.__auth, self.endpoint, bucket_name)
        return bucket.sign_url('GET', filename, ttl)

    def delete(self, filename, bucket_name=None):
        bucket_name = bucket_name or self.bucket
        bucket = oss2.Bucket(self.__auth, self.endpoint, bucket_name)
        try:
            result = bucket.delete_object(filename)
            return result
        except oss2.exceptions.NoSuchKey:
            return True

    def download_file(self, img_url, save_dir='', degree=0, p=0):
        if img_url == '':
            return img_url
        bucket = oss2.Bucket(self.__auth, self.endpoint, self.bucket)
        _, image_name = img_url.rsplit('.aliyuncs.com/', 1)
        image_name = image_name.split('?')[0]
        if save_dir == '':
            path = "upload/{}/".format(image_name.split('/')[0])
        else:
            path = "upload/{}/".format(save_dir)
        if '.' in image_name.split('/')[-1]:
            full_path = os.path.join(BASE_DIR, path + "{}".format(image_name.split('/')[-1]), )
        else:
            full_path = os.path.join(BASE_DIR, path + "{}.jpg".format(image_name.split('/')[-1]), )
        # 文件夹不存在创建文件夹
        if not os.path.exists(os.path.join(BASE_DIR, path, )):
            # 创建多层文件夹
            os.makedirs(os.path.join(BASE_DIR, path, ))
        # 填写Object完整路径，完整路径中不包含Bucket名称，例如testfolder/exampleobject.txt。
        # 下载Object到本地文件，并保存到指定的本地路径D:\\localpath\\examplefile.txt。如果指定的本地文件存在会覆盖，不存在则新建。
        # bucket.get_object_to_file(image_name, full_path)
        # return full_path
        if degree > 0:  # 图片旋转
            style = 'image/rotate,%s' % degree
            bucket.get_object_to_file(image_name, full_path, process=style)
        elif p > 0:
            style = 'image/resize,p_%s' % p
            bucket.get_object_to_file(image_name, full_path, process=style)
        else:
            bucket.get_object_to_file(image_name, full_path)
        return full_path

    def put_object_from_file(self, file_name, file_path):
        """
        上传
        """
        bucket = oss2.Bucket(self.__auth, self.endpoint, self.bucket)
        # 填写Object完整路径和本地文件的完整路径。Object完整路径中不能包含Bucket名称。
        # 如果未指定本地路径，则默认从示例程序所属项目对应本地路径中上传文件。
        res = bucket.put_object_from_file(file_name, file_path)
        return res


class STS:
    def __init__(self, corpid):
        map_conf = OSS_SETTINGS[corpid]
        self.endpoint = map_conf.get("endpoint")
        self.role_arn = map_conf.get('role_arn')
        self.region = map_conf.get("region")
        self.role = map_conf.get('role', 'oss')
        self.keyid = map_conf.get('access_key_id')
        self.secret = map_conf.get('key_secret')
        self.bucket = map_conf.get('bucket')
        self.expire_time = int(map_conf.get('expire_time4', 3600))

    def sts_token(self):
        region_provider.modify_point('Sts', self.region, self.endpoint)
        clt = client.AcsClient(self.keyid, self.secret, self.region)
        req = AssumeRoleRequest.AssumeRoleRequest()
        req.set_RoleArn(self.role_arn)
        req.set_RoleSessionName(self.role)
        req.set_DurationSeconds(self.expire_time)
        resp = clt.do_action_with_exception(req)
        resp = json.loads(resp)
        return resp['Credentials']

    def get_token_forced(self, bucket_name, put_status=False):
        clt = client.AcsClient(self.keyid, self.secret, self.region)
        req = AssumeRoleRequest.AssumeRoleRequest()
        # 设置返回值格式为JSON。
        req.set_accept_format('json')
        req.set_RoleArn(self.role_arn)
        req.set_RoleSessionName('upload-file')
        # 设置访问权限
        policy = {
            "Version": "1",
            "Statement": [
                {
                    "Effect": "Allow",
                    "Action": [
                        "oss:*"
                    ],
                    "Resource": [
                        f"acs:oss:*:*:{bucket_name}",
                        f"acs:oss:*:*:{bucket_name}/*"
                    ],
                    "Condition": {}
                }
            ]
        }
        if put_status:
            policy['Statement'][0]['Action'] = ["oss:Put*"]

        req.set_Policy(json.dumps(policy))
        body = clt.do_action_with_exception(req)

        # 使用RAM账号的AccessKeyId和AccessKeySecret向STS申请临时token。
        token = json.loads(body).get('Credentials')
        return token
