import os
import time
import uuid
import wget
import requests
import threading
import settings
from io import Bytes<PERSON>
from flask import send_file

from settings import  MINIO_CONF
from datetime import datetime, timedelta
from lib_func.logger import logger

from settings import IS_MINIO_PRESIGNED_URL_REPLACE, LOCAL_MINIO_IP, OSS_DOMAIN_NAME





class MinIOUtil:
    def __init__(self, endpoint, access_key, secret_key, secure=False):
        self.endpoint = endpoint
        self.access_key = access_key
        self.secret_key = secret_key
        self.secure = secure
        self._pool = threading.local()
    
    @property
    def client(self):
        from minio import Minio
        if not hasattr(self._pool, 'client'):
            self._pool.client = Minio(
                endpoint=self.endpoint,
                access_key=self.access_key,
                secret_key=self.secret_key,
                secure=self.secure
            )
        return self._pool.client

    def create_bucket(self, bucket_name):
        if not self.client.bucket_exists(bucket_name):
            self.client.make_bucket(bucket_name)
        else:
            logger.info(f"Bucket '{bucket_name}' already exists")

    # def upload_file(self, bucket_name, file_path):
    #     t1 = time.time()
    #     object_name = file_path.split('/')[-1]
    #     self.client.fput_object(bucket_name, object_name, file_path)
    #     logger.info(f"'{file_path}' has been uploaded to bucket '{bucket_name}'")
    #     logger.info(f"time: {round((time.time() - t1), 2)}s")

    def get_permanent_url(self, bucket_name, object_name):
        # base_url = "http://oss.ai.zhongshuruizhi.com"
        # import os
        # OSS_DOMAIN_NAME = os.getenv('OSS_DOMAIN_NAME', 'oss.ai.zhongshuruizhi.com')
        # base_url = f"http://{OSS_DOMAIN_NAME}"
        
        base_url = settings.OSS_DOMAIN_NAME
        if 'http' not in base_url:
            if settings.ENABLE_HTTPS == '1':
                base_url = f'https://{base_url}'
            else:
                base_url = f'http://{base_url}'
        url = f"{base_url}/{bucket_name}/{object_name}"
        return url

    def upload_file_v2(self, bucket_name, object_name, file_path):

        file_size_bytes = os.path.getsize(file_path)
        
        if file_size_bytes >= 1 << 30:  # 1 GB = 2^30 bytes
            file_size = f"{file_size_bytes / (1 << 30):.2f} GB"
        elif file_size_bytes >= 1 << 20:  # 1 MB = 2^20 bytes
            file_size = f"{file_size_bytes / (1 << 20):.2f} MB"
        elif file_size_bytes >= 1 << 10:  # 1 KB = 2^10 bytes
            file_size = f"{file_size_bytes / (1 << 10):.2f} KB"
        else:
            file_size = f"{file_size_bytes} bytes"
        
        t1 = time.time()
        logger.info("=" * 25)
        logger.info(f"Start upload file: {file_path} (Size: {file_size}) to bucket {bucket_name}/{object_name}, Current time: {datetime.now()}")
        
        self.client.fput_object(bucket_name, object_name, file_path)
        
        # logger.info(f"'{file_path}' has been uploaded to bucket '{bucket_name}' as '{object_name}'")
        # logger.info(f"Upload file succeeded, Time: {round((time.time() - t1), 2)}s, Current time: {datetime.now()}")
        logger.info(f"Upload file succeeded, Time: {round((time.time() - t1) * 1000, 2)}ms, Current time: {datetime.now()}")
        
        url = self.get_permanent_url(bucket_name, object_name)
        logger.info(f"OSS URL: {url}")
        
        return url
    
    def download_file_v1(self, bucket_name, object_name):
        """
        下载文件数据到内存。
        :param bucket_name: MinIO 桶名
        :param object_name: MinIO 对象名
        :return: 文件数据（字节流）
        """
        
        t1 = time.time()
        logger.info(f"Start downloading file: {object_name} from bucket {bucket_name}, current time: {datetime.now()}")
        
        # 使用 MinIO 客户端下载文件到内存
        response = self.client.get_object(bucket_name, object_name)
        # return response
        file_data = response.read()  # 读取文件数据
        
        # logger.info(f"'{object_name}' has been downloaded.")
        # logger.info(f"time: {round((time.time() - t1), 2)}s, current time: {datetime.now()}")
        # file_data = send_file(file_data, as_attachment=True, download_name=object_name)
        return file_data

    def download_file_v2(self, bucket_name, object_name):
        """
        下载文件数据到内存。
        :param bucket_name: MinIO 桶名
        :param object_name: MinIO 对象名
        :return: 文件数据（字节流）
        """
    
        t1 = time.time()
        logger.info(f"Start downloading file: {object_name} from bucket {bucket_name}, current time: {datetime.now()}")
        
        # 使用 MinIO 客户端下载文件到内存
        response = self.client.get_object(bucket_name, object_name)
        return response

        # file_data = response.read()  # 读取文件数据
        
        # logger.info(f"'{object_name}' has been downloaded.")
        # logger.info(f"time: {round((time.time() - t1), 2)}s, current time: {datetime.now()}")
        # # file_data = send_file(file_data, as_attachment=True, download_name=object_name)
        # return file_data


    def upload_to_minio(self, bucket_name, object_name, file_data):
        """
        上传文件到 MinIO。
        :param bucket_name: MinIO 桶名
        :param object_name: MinIO 对象名
        :param file_data: 文件数据（字节流）
        :return: 文件的 URL 地址
        """
        minio_end_point = settings.MINIO_CONF['endpoint']   
        try:
            # 上传文件数据
            self.client.put_object(
                bucket_name,
                object_name,
                BytesIO(file_data),
                len(file_data)
            )
            
            # 返回上传文件的 URL
            file_url = f"http://{minio_end_point}/{bucket_name}/{object_name}"
            return file_url
        except Exception as e:
            logger.info(f"Error uploading file {object_name} to MinIO: {e}")
            raise

    def download_file(self, bucket_name, object_name, download_path):
        t1 = time.time()
        logger.info(f"Start download file: {download_path} from bucket {bucket_name}/{object_name}, current time: {datetime.now()}")
        self.client.fget_object(bucket_name, object_name, download_path)
        logger.info(f"'{object_name}' has been downloaded to '{download_path}'")
        logger.info(f"time: {round((time.time() - t1), 2)}s, current time: {datetime.now()}")

    def download_file_use_wget(self, url):
        file_name = wget.filename_from_url(url)
        logger.info(f"file_name: {file_name}")
        
        t1 = time.time()
        rsp = wget.download(url, out=file_name)
        logger.info(f"rsp: {rsp}")
        logger.info(f"use time: {round((time.time() - t1), 2)}s, current time: {datetime.now()}")

    def download_file_use_requests(self, url, download_path):
        floder_path = os.path.dirname(download_path)
        os.makedirs(floder_path, exist_ok=True)
        logger.info(f"Start download file: {url} to {download_path}, current time: {datetime.now()}")
        t1 = time.time()
        if IS_MINIO_PRESIGNED_URL_REPLACE:
            url = url.replace(OSS_DOMAIN_NAME, LOCAL_MINIO_IP)
            logger.info(f"replace url: {url}")
        rsp = requests.get(url, stream=True)
        rsp.raise_for_status()
        if rsp.status_code == 200:
            with open(download_path, 'wb') as f:
                for chunk in rsp.iter_content(chunk_size=8192):
                    f.write(chunk)
            logger.info(f"'{download_path}' has been downloaded.")
        else:
            logger.info(f"Failed to download the file. Status code: {rsp.status_code}")
        logger.info(f"use time: {round((time.time() - t1), 2)}s, current time: {datetime.now()}")
        return download_path
        

    def bucket_list(self):
        buckets = self.client.list_buckets()
        for bucket in buckets:
            logger.info(bucket.name)
            
    # 生成预签名URL
    def generate_presigned_url(self, bucket_name, object_name, expires_in=timedelta(minutes=30)):

        presigned = self.client.presigned_put_object(bucket_name, object_name, expires=expires_in)
        url = self.get_permanent_url(bucket_name, object_name)
        mini_map = MINIO_CONF
        rp_url = mini_map.get('replace_presigend_url')
        if rp_url:
            router = presigned.split(self.endpoint)[-1]
            presigned = f'{rp_url}{router}'
            if 'http' not in rp_url:
                presigned = f'https://{presigned}'

        if settings.IS_MINIO_PRESIGNED_URL_REPLACE:
            presigned = presigned.replace(settings.MINIO_CONF.get('endpoint'), settings.PRESIGNED_URL_REPLACE_HOST)
            print("*" * 35)
            print(f"replace presigned_url: {presigned}")
            print("*" * 35)
        logger.info("+" * 25)
        logger.info(f"Presigned URL: {presigned} \nOSS_URL: {url}")
        logger.info("+" * 25)
        return presigned, url

    # 使用预签名URL上传文件
    def upload_file_using_presigned_url(self, presigned_url, file_path):
        with open(file_path, 'rb') as file_data:
            response = requests.put(presigned_url, data=file_data)
        
        if response.status_code == 200:
            logger.info("File uploaded successfully.")
        else:
            logger.info(f"Failed to upload file. Status code: {response.status_code}, Response: {response.text}")

# 示例使用
if __name__ == "__main__":
    minio_util = MinIOUtil(
        # endpoint="**************:9000",
        endpoint="**************:9000",
        access_key="vlnO7uWSDGj92UEItkIX",
        secret_key="94458uubN5x4CiuIxr6xn0Do0aym36lHSlqa0Hz9",
        secure=False
    )
    
    minio_util.bucket_list()
    
    bucket_name = "knowledge-docs"
    minio_util.create_bucket(bucket_name)
    
    file_path = "/home/<USER>/shao.li/jusure_AI/json_dir/minio_demo/1.log"
    minio_util.upload_file_v2(bucket_name, file_path)
    
    url = "http://oss.ai.zhongshuruizhi.com/knowledge-docs/python3.11.tar"
    import urllib.parse
    url = urllib.parse.unquote(url)
    # file_name = url.split('/')[-1]
    # file_name = os.path.basename(url)
    relative_file_path = url.split("oss.ai.zhongshuruizhi.com")[-1]
    
    download_path = "./downloads" + relative_file_path
    # download_path = "./downloads"
    # floder_path = os.path.dirname(download_path)
    # os.makedirs(floder_path, exist_ok=True)

    minio_util.download_file_use_requests(url, download_path)