[uwsgi]
;socket = 0.0.0.0:5101
http = 0.0.0.0:5000
# 磁盘app.py文件路径
enable-threads = true
master = true
wsgi-file = run.py
callable = app
processes = 8
threads = 10
pidfile = uwsgi.pid
buffer-size = 32678
max-requests = 1000
logto = ./logs/uwsgi.log
; supervisor不能使用uwsgi日志
;daemonize = logs/uwsgi.log
log-maxsize = 5000000
master = true
# 设置 harakiri 超时时间（秒）
harakiri = 3660
# 设置 socket 超时时间（秒）
socket-timeout = 3660
# 设置 HTTP 请求超时时间（秒）
http-timeout = 3660
# 设置空闲连接超时时间（秒）
idle = 120
