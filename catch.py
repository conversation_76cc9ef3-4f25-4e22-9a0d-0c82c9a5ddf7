# -*- coding: utf-8 -*-
import numpy as np
from apps import app
import json, time, uuid
from bson import ObjectId
from decimal import Decimal
from datetime import datetime, date
from flask import make_response, request
from lib_func.logger import logger

BaseResp = {
    'code': 0,
    'msg': 'ok',
    'data': ''
}

ValueErrorResponse = {
    'code': 300,
    'msg': 'Customer error',
    'data': ""
}

ExceptionErrorResponse = {
    'code': 500,
    'msg': 'Server error',
    'data': ""
}


@app.errorhandler(ValueError)
def handle_error(error):
    response = ValueErrorResponse.copy()
    response['timestamp'] = int(time.time())
    response['req_id'] = str(uuid.uuid4())
    url_rule = request.path
    req_method = request.method
    req_pmsd = request.pmsd
    req_headers = request.headers
    try:
        for one in error.args:
            if type(one) is str:
                response['msg'] = one
                break
            elif type(one) is dict:
                one['msg'] = one.get('msg', one.get('message'))
                response.update(one)
                break
            elif type(one) is list:
                response['msg'] = one
                break
            else:
                if hasattr(one, 'args') and type(one.args) is tuple:
                    for err_msg in one.args:
                        if type(err_msg) is str:
                            response['msg'] = err_msg
                        elif type(err_msg) is list:
                            try:
                                response['msg'] = json.dumps(err_msg, ensure_ascii=False)
                            except:
                                response['msg'] = err_msg
                        elif type(err_msg) is dict:
                            if 'msg' in err_msg or 'code' in err_msg:
                                _code = err_msg.get('code')
                                if type(_code) in [list, str]:
                                    response['msg'] = _code or err_msg.get('msg', 'error')
                                elif type(_code) is int:
                                    response.update(err_msg)
                                else:
                                    response['msg'] = err_msg.get('msg', err_msg.get('message', 'error'))
                            else:
                                try:
                                    response['msg'] = json.dumps(err_msg, ensure_ascii=False)
                                except:
                                    response['msg'] = err_msg
        err_msg = response.get('msg')
        if type(err_msg) in [list, dict]:
            err_msg = json.dumps(err_msg, ensure_ascii=False)
        elif type(err_msg) in [str, int]:
            err_msg = str(err_msg)
        elif type(err_msg) is bytes:
            err_msg = err_msg.decode()
        else:
            err_msg = '系统服务异常'
        response['message'] = err_msg
        logger.exception('url：{}，{}，headers：{} 请求参数{}'.format(url_rule, req_method, req_headers, req_pmsd))

    except Exception as err:
        logger.error(err)
        logger.exception('url：{}，{}，headers：{} 请求参数{}'.format(url_rule, req_method, req_headers, req_pmsd))
    response = make_response(json.dumps(response, ensure_ascii=False, cls=CJsonEncoder), 200)
    response.content_type = 'application/json; charset=utf-8'
    return response


@app.errorhandler(Exception)
def error_500(error):
    response = ExceptionErrorResponse.copy()
    response['timestamp'] = int(time.time())
    response['req_id'] = str(uuid.uuid4())
    url_rule = request.path
    req_method = request.method
    req_pmsd = request.pmsd
    req_headers = request.headers
    try:
        if hasattr(error, 'args') and error.args:
            for one in error.args:
                response['msg'] = one
        elif hasattr(error, 'data'):
            if type(error.data) is dict:
                response['msg'] = error.data.get('message', error.data.get('msg', '系统错误'))
            elif type(error.data) is str:
                response['msg'] = error.data
            else:
                response['msg'] = '系统错误'
        elif hasattr(error, 'description'):
            response['msg'] = error.description
        if hasattr(error, 'code') and type(error.code) is int:
            response['code'] = error.code

        err_msg = response.get('msg')
        if type(err_msg) in [list, dict]:
            err_msg = json.dumps(err_msg, ensure_ascii=False)
        elif type(err_msg) in [str, int]:
            err_msg = str(err_msg)
        elif type(err_msg) is bytes:
            err_msg = err_msg.decode()
        else:
            err_msg = '系统服务异常'
        response['message'] = err_msg
        logger.exception('url：{}，{}，headers：{} 请求参数{}'.format(url_rule, req_method, req_headers, req_pmsd))

    except Exception as err:
        logger.error(err)
        logger.exception('url：{}，{}，headers：{} 请求参数{}'.format(url_rule, req_method, req_headers, req_pmsd))

    response = make_response(json.dumps(response, ensure_ascii=False, cls=CJsonEncoder), 200)
    response.content_type = 'application/json; charset=utf-8'
    return response


class CJsonEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(obj, date):
            return obj.strftime("%Y-%m-%d")
        elif isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, np.int32) or isinstance(obj, np.int64):
            return int(obj)
        elif isinstance(obj, ObjectId):
            return str(obj)
        else:
            return json.JSONEncoder.default(self, obj)
