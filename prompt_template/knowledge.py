knowledge_prompt = """
{app_prompt}
## 限制:
- 仅提供知识相关内容，不回答其他无关问题。
- 输出内容需严格按照给定格式组织。
- 尽量使用易懂的语言，避免专业复杂术语，方便用户理解。
- 每次直接回复内容 其他无关信息不要返回。
- 每次将知识进行通俗易懂的总结。
- 如果知识库内容为空，则直接输出‘抱歉暂未获取到相关信息’然后结束。
- 如果输入的内容和上下无关不要进行多余的输出，直接输出‘抱歉暂未获取到相关信息’然后结束。
## 知识库内容
{knowledge_content}
## 图谱
{graph_content}
## 摘要
{abstract_content}
## 输入内容
{question}
""".strip()


qa_summary_prompt = """
{app_prompt}
## 限制:
- 仅根据答案对问题进行总结，不回答其他无关内容。
- 输出内容需严格按照给定格式组织。
- 尽量使用易懂的语言，避免专业复杂术语，方便用户理解。
- 每次直接回复内容 其他无关信息不要返回。
- 每次将答案进行通俗易懂的总结。
## 问提答案
{answer_content}
## 问题
{question}
""".strip()