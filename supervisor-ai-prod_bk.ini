[program:jusure-ai]
; user=yons
directory = /app
command=uwsgi --ini /app/uwsgi_prod_bk.ini
stderr_logfile = /app/logs/run.log
stopasgroup=true
killasgroup=true
environment=ENVIRONMENT=RELEASE

[program:jusure-ai-worker]
; user=yons
directory=/app
command=celery --app=celery_app worker -l info
stdout_logfile=/app/logs/celery.worker.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile=/app/logs/celery.worker.err.log
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=10
autostart=true
autorestart=true
startsecs=10
stopwatisecs=60
priority=997
stopasgroup=true
killasgroup=true
environment=ENVIRONMENT=RELEASE

[program:jusure-ai-beat]
; user=yons
directory=/app
command=celery --app=celery_app beat -l info
stdout_logfile=/app/logs/celery.beat.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile=/app/logs/celery.beat.err.log
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=10
autostart=true
autorestart=true
startsecs=10
stopwatisecs=60
priority=998
stopasgroup=true
killasgroup=true
environment=ENVIRONMENT=RELEASE

[program:snowflake-server]
directory=/app
command=snowflake_start_server --worker=1 --port=8910
stdout_logfile=/app/logs/snowflake_server.log
stderr_logfile=/app/logs/snowflake_server.err.log
autostart=true
autorestart=true
startsecs=10
stopasgroup=true
killasgroup=true
environment=ENVIRONMENT=RELEASE


[group:prod-ai]
; user=yons
programs=jusure-ai,jusure-ai-worker,jusure-ai-beat
environment=ENVIRONMENT=RELEASE