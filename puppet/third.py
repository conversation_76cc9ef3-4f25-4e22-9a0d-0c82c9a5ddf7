

import requests
import os
import json
from flask import g
from horticulture.request_retry import retry_request
from horticulture.record import third_req_log
from settings import ZS_BASE_URL

class ThirdPartySDK:
    def __init__(self, base_url, corpid):
        """
        初始化 SDK 实例，配置基础 URL。
        :param base_url: 第三方 API 的基础 URL
        """
        self.base_url = base_url
        self.token = '836de7116db3d779fb52fe584c7f975a'
        self.corpid = corpid
    
    @third_req_log
    def send_request(self, method, endpoint, headers=None, payload=None, params=None):
        """
        发送 POST 请求到第三方 API，获取响应结果。
        :param endpoint: 目标 API 的 endpoint (不包括基础 URL)
        :param headers: 请求头 (字典)
        :param payload: 请求体数据 (字典或字符串，默认是 JSON 格式)
        :param params: URL 查询参数 (字典)
        :return: 返回响应内容，通常是 JSON 数据
        """
        url = f"{self.base_url}/{endpoint}"
        # 设置默认请求头
        if headers is None:
            headers = {
                'Content-Type': 'application/json',
                'token': self.token,
                'corp-id': self.corpid
            }
        if payload is not None and isinstance(payload, dict):
            payload = json.dumps(payload)

        response = requests.request(method, url, headers=headers, data=payload, params=params)
        print('****'*10)
        print(response.json())
        if response.status_code == 200:
            try:
                return response.json()
            except json.JSONDecodeError:
                return response.text
        else:
            response.raise_for_status()
    
    @classmethod
    @third_req_log
    def send_zs_oss_url(cls, file_url, corpid):
        zs_base_url = ZS_BASE_URL
        if not zs_base_url:
            return None
        tp = ThirdPartySDK(zs_base_url, corpid)
        params = {'file_url': file_url}
        ret = tp.send_request('GET', '/content/save/oss',params=params)
        file_url = ret.get('data')
        if not file_url:
            raise ValueError('ai请求ZS ，将url转换为内网oss地址失败')
        return file_url

    
