from settings import JUSURE_MULTIMODAL_IMAGE_DESCRIPTION_V1,TEXT_TO_IMG_MODEL_URL,KNOWLEDGE_OSS_BUCKET_NAME
import json
import requests
# 生成一个随机ID
import uuid
import base64
from module import minio_util
from lib_func.logger import logger
class ImgToTextType:
    code: int
    data: str
    images: list[str]
class ImgToText:
    def __init__(self):
        self.ip = JUSURE_MULTIMODAL_IMAGE_DESCRIPTION_V1

    def get_text(self, oss_url: str, id: str = '') -> ImgToTextType:
        params ={
            'url' :oss_url,
            'id': id or str(uuid.uuid4())
        }
        headers = {
            "Content-Type": "application/json",
        }
        response = requests.request('GET', self.ip, headers=headers, params=params)

        content = ''
        if response.status_code == 200:
            result = response.json()
            content = result['text'].replace('|||||||||||:','\r\n')
                       
        return {'code': 0, 'data': content, 'images': [oss_url]}
    
class TextToImg:
    def __init__(self):
        self.ip = TEXT_TO_IMG_MODEL_URL

    def get_img(self, text: str, id: str = '', height:int = 512, width:int=512) -> ImgToTextType:
        
  #        'http://**************:6300/api/v1/sd/create' \
  # -H 'accept: application/json' \
  # -H 'Content-Type: application/json' \
  # -d '{
  # "trace_id": "1024",
  # "num_inference_steps": 28,
  # "guidance_scale": 3.5,
  # "height": 512,
  # "width": 512,
  # "prompt_list": [
  #   "A capybara holding a sign that reads Hello World"
  # ]
        params ={
            'trace_id': id or str(uuid.uuid4()),
            "num_inference_steps": 48,
            "guidance_scale": 3.5,
            "height": height,
            "width": width,
            "prompt_list": [
              text
            ]
        }
        headers = {
            "Content-Type": "application/json",
        }

        # logger.info('text_to_img params: {}'.format(params))

        print('text_to_img params: {}'.format(params))

        response = requests.request('POST', self.ip, headers=headers, data=json.dumps(params))

        content = ''
        res = '抱歉，生成图片失败'

        response.raise_for_status()
        if response.status_code == 200:
            result = response.json()
            
            # base64 to image 字节流
            base64_str = result.get('output')[0].get('image_base64')

            image_data = base64.b64decode(base64_str)

            bucket_name = KNOWLEDGE_OSS_BUCKET_NAME

            oss_name = 'ai/' + str(uuid.uuid4()) + ".png"

            res = minio_util.upload_to_minio(bucket_name,oss_name, image_data)
        logger.info('status_code: {}, res: {}'.format(response.status_code, res))
        
                       
        return {'code': 0, 'data': res, 'images': ['']}
            
if __name__ == '__main__':
    img_path = 'https://zhongshu-test.oss-cn-beijing.aliyuncs.com/edb15b16-2443-4314-bea1-06c5b7b944a5/aigc_text2img/0be530ee9a1e1436998d0910e88dcfe5_1737445303310.jpg'
    img_to_text = ImgToText()
    print(img_to_text.get_text(img_path))

    text = 'yellow dog'
    text_to_img = TextToImg()
    print(text_to_img.get_img(text))

    pass

                