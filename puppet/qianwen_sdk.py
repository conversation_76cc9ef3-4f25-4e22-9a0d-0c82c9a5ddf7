# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: qianwen_sdk.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 4月 23, 2024
# ---

import dashscope
from pathlib import Path
from openai import OpenAI
from horticulture.request_retry import retry_request
from lib_func.exception import ALiApiError
from lib_func.logger import logger


class Qi<PERSON>WenModel(object):

    def __init__(self, api_key=None):
        self.api_key = api_key

    @retry_request(tries=3, jitter=2, throw_exception=True)
    def create_embeddings(self, input_text, model_path='text-embedding-v1'):
        resp = dashscope.TextEmbedding.call(
            model=model_path,
            api_key=self.api_key,
            input=input_text)
        if resp.get('status_code') != 200:
            raise ALiApiError(resp['message'])
        return resp
        # {
        #     'vector': resp['output']['embeddings'][0]['embedding'],
        #     'total_tokens': resp['usage']['total_tokens']
        # }

    async def async_create_embeddings(self, input_text, model_path='text-embedding-v1'):
        resp = dashscope.TextEmbedding.call(
            model=model_path,
            api_key=self.api_key,
            input=input_text)
        if resp.get('status_code') != 200:
            raise ALiApiError(resp['message'])
        return resp

    def create_files(self, file_path):
        client = OpenAI(
            api_key=self.api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
        file_object = client.files.create(file=Path(file_path), purpose="file-extract")
        logger.info(file_object)

    def text_long(self, fileid):
        client = OpenAI(
            api_key=self.api_key,  # 替换成真实DashScope的API_KEY
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",  # 填写DashScope服务endpoint
        )
        messages = [
                {
                    'role': 'system',
                    'content': 'You are a helpful assistant.'
                },
                {
                    'role': 'system',
                    'content': f'fileid://{fileid}'
                },
                {
                    'role': 'user',
                    'content': '分析下这个数据？'
                }
            ]
        logger.info(messages)
        completion = client.chat.completions.create(
            model="qwen-long",
            messages=messages,
            stream=True
        )
        for chunk in completion:
            if chunk.choices[0].delta.content is not None:
                logger.info(chunk.choices[0].dict())
