# -*- coding: utf-8 -*-
import re
import time
import subprocess
import pymysql

from fabric import Connection
from modeler.mysql import gpu_specs_orm
from lib_func.logger import logger

class ServerStats():
    
    def __init__(self, ip='', port=0, user='', passwd='', method='local'):
        self.method = method
        self.stop_flag = False
        
        match self.method:
            case 'local':
                pass
            case 'remote':
                self.ip = ip
                self.port = port
                self.user = user
                self.passwd = passwd
                self.conn = None
            
        
    def close_connection(self):
        self.stop_flag = True
        
        if self.method == 'remote' and self.conn is not None:
            self.conn.close()
            self.conn = None
            
    
    def get_stats(self):
        command = 'nvidia-smi --query-gpu=driver_version,index,name,utilization.gpu,memory.used,memory.total --format=csv,nounits,noheader\
                    && top -bn1 | grep "Cpu(s)"\
                        && free -m\
                            && df -h'
        
        match self.method:
            case 'local':
                if not self.stop_flag:
                    response = subprocess.check_output(command, shell=True, text=True)
                    response =  response.strip().split('\n')
                    result = self.parse_stats(response)
                    return result
            case 'remote':
                if self.conn is None:
                    self.conn = Connection(self.ip, user=self.user, connect_kwargs={"password": self.passwd}, port=self.port)
                
                if not self.stop_flag:   
                    response = self.conn.run(command, hide=True)
                    response =  response.stdout.encode().decode('utf-8')
                    response =  response.strip().split('\n')
                    result = self.parse_stats(response)
                    return result
    
    def parse_stats(self, result):
        stats = {}
        gpu_stats = {}
        
        checkpoint = 0
        for line in result:
            checkpoint += 1
            if 'NVIDIA' in line:
                if 'driver_version' not in gpu_stats.keys():
                    gpu_stats['gpu_driver_version'] = line.split(',')[0].strip()
                index = line.split(',')[1].strip()
                gpu_stats[index] = {
                    "name": line.split(',')[2].strip().split(' ')[-1],
                    "gpu_util": float(line.split(',')[3].strip()),
                    "mem_avail": float(line.split(',')[5].strip()) - float(line.split(',')[4].strip()),
                }

                continue
            if '%Cpu(s)' in line:
                pattern = r"%Cpu\(s\):\s+(\d+\.\d+)\s+us,\s+(\d+\.\d+)\s+sy,\s+(\d+\.\d+)\s+ni,\s+(\d+\.\d+)\s+id,\s+(\d+\.\d+)\s+wa,\s+(\d+\.\d+)\s+hi,\s+(\d+\.\d+)\s+si,\s+(\d+\.\d+)\s+st"
                match = re.search(pattern, line)
                # us: user, sy: system, ni: nice, id: idle, wa: iowait, hi: hardware interrupt, si: software interrupt, st: steal time
                if match:
                    us = float(match.group(1))
                    sy = float(match.group(2))
                    ni = float(match.group(3))
                    id = float(match.group(4))
                    wa = float(match.group(5))
                    hi = float(match.group(6))
                    si = float(match.group(7))
                    st = float(match.group(8))
                    
                    stats['cpu'] = {
                        "us": us,
                        "sy": sy,
                        "ni": ni,
                        "id": id,
                        "wa": wa,
                        "hi": hi,
                        "si": si,
                        "st": st,
                    }
                    
                continue
            if '内存' in line or 'Mem' in line:
                pattern = r"\d+"
                matches = re.findall(pattern, line)
                
                stats['mem'] = {
                    "total": matches[0],
                    "used": matches[1],
                    "free": matches[2],
                    "shared": matches[3],
                    "buff_cache": matches[4],
                    "available": matches[5],
                }
                continue
            
            if '文件系统' in line or 'Filesystem' in line:
                break
            
            
        pattern_disk = r"^\S+\s+(\S+)\s+(\S+)\s+(\S+)"
        total_disk = 0
        avail_disk = 0
        for line in result[checkpoint:]:
            if 'overlay' in line and 'docker' in line:
                continue
            
            matches = re.search(pattern_disk, line)
            if matches:
                total = matches.group(1)
                used = matches.group(2)
                avail = matches.group(3)
                
                while True:
                    if total == '0':
                        break
                    if 'M' in total:
                        total = float(total[:-1])
                        break
                    if 'K' in total:
                        total = float(total[:-1]) / 1024
                        break
                    if 'G' in total:
                        total = float(total[:-1]) * 1024
                        break
                    if 'T' in total:
                        total = float(total[:-1]) * 1024 * 1024
                        break
                
                while True:
                    if used == '0':
                        break
                    if 'M' in used:
                        used = float(used[:-1])
                        break
                    if 'K' in used:
                        used = float(used[:-1]) / 1024
                        break
                    if 'G' in used:
                        used = float(used[:-1]) * 1024
                        break
                    if 'T' in used:
                        used = float(used[:-1]) * 1024 * 1024
                        break
                
                while True:
                    if avail == '0':
                        break
                    if 'M' in avail:
                        avail = float(avail[:-1])
                        break
                    if 'K' in avail:
                        avail = float(avail[:-1]) / 1024
                        break
                    if 'G' in avail:
                        avail = float(avail[:-1]) * 1024
                        break
                    if 'T' in avail:
                        avail = float(avail[:-1]) * 1024 * 1024
                        break

                total_disk += total
                avail_disk += avail
                
        stats['disk'] = {
            "total": int(total_disk),
            "avail": int(avail_disk),
        }
            
        stats['gpu'] = gpu_stats
        return stats

def get_gpu_reference():
    return gpu_specs_orm.GPUOrm().get_gpu_list()

def get_available_tf(server, gpu_ref):
    summary = {}
    
    int4 = 0
    int4_s = 0
    int8 = 0
    int8_s = 0
    fp8 = 0
    fp8_s = 0
    fp16 = 0
    fp16_s = 0
    bf16 = 0
    bf16_s = 0
    tf32 = 0
    tf32_s = 0
    fp32 = 0
    
    for k, v in server['gpu'].items():
        if k == 'gpu_driver_version':
            continue
        for gpu in gpu_ref.keys():
            if v['name'] in gpu:
                int4 += float(gpu_ref[gpu]['INT4'].split(' ')[0])
                int4_s += float(gpu_ref[gpu]['INT4*'].split(' ')[0])
                int8 += float(gpu_ref[gpu]['INT8'].split(' ')[0])
                int8_s += float(gpu_ref[gpu]['INT8*'].split(' ')[0])
                fp8 += float(gpu_ref[gpu]['FP8'].split(' ')[0])
                fp8_s += float(gpu_ref[gpu]['FP8*'].split(' ')[0])
                fp16 += float(gpu_ref[gpu]['FP16'].split(' ')[0])
                fp16_s += float(gpu_ref[gpu]['FP16*'].split(' ')[0])
                bf16 += float(gpu_ref[gpu]['BF16'].split(' ')[0])
                bf16_s += float(gpu_ref[gpu]['BF16*'].split(' ')[0])
                tf32 += float(gpu_ref[gpu]['TF32'].split(' ')[0])
                tf32_s += float(gpu_ref[gpu]['TF32*'].split(' ')[0])
                fp32 += float(gpu_ref[gpu]['FP32'].split(' ')[0])
                break
    
    summary['int4'] = int4
    summary['int4_s'] = int4_s
    summary['int8'] = int8
    summary['int8_s'] = int8_s
    summary['fp8'] = fp8
    summary['fp8_s'] = fp8_s
    summary['fp16'] = fp16
    summary['fp16_s'] = fp16_s
    summary['bf16'] = bf16
    summary['bf16_s'] = bf16_s
    summary['tf32'] = tf32
    summary['tf32_s'] = tf32_s
    summary['fp32'] = fp32
    
    server['summary'] = summary
    
    return server
    

if __name__ == '__main__':
    # Demo
    # Check remote server stats
    instance = ServerStats(method='remote', ip='**************', port=8081, user='yons', passwd='wfn031641')
    stats = instance.get_stats()
    print(stats)
    
    # fetch gpu reference data from the database
    gpu_ref = get_gpu_reference()
    # print(gpu_ref)
    
    # calculate available computing resources
    all_stats = get_available_tf(stats, gpu_ref)
    print(all_stats)

    