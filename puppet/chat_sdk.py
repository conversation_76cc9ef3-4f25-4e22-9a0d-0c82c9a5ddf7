#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/4/2 17:27
# <AUTHOR> zhang<PERSON>
# @File    : chat_sdk.py
# @Comment : 调用各个模型
import asyncio
import json
from operator import is_
import os
import uuid
from concurrent.futures import ThreadPoolExecutor

import requests, re, time, datetime
from lib_func.const_map import *
from lib_func.logger import logger
# from langchain_wenxin import <PERSON><PERSON>
from langchain_openai import ChatOpenAI
from puppet.qianfan_sdk import QianFanModel
from puppet.qianwen_sdk import QianWenModel
from settings import OSS_DIR, JusureT3Api, JusureT3Url, UrlEnv
from settings import SPARK_MODEL_API_KEY, SPARK_MODEL_API_SECRET, IS_SPARK_SAAS_MODEL, SPARK_MODEL_TRACE_ID, \
    SPARK_SAAS_MODEL_APPID, LOCAL_MODEL_PATH, LOCAL_MODEL_NAME
from utils.tools import get_uuid, return_ocr_result
from rapidocr_onnxruntime import RapidOCR
import dashscope
from puppet.es_sdk import EsSdkPool
from puppet.cache import redis_pool
from puppet.mq_sdk import AiGcRabbitMQProducer
from flask import g
import base64
from openai import OpenAI, AsyncOpenAI
from io import BytesIO
from modeler.mysql.ai_model_orm import AiModelOrm
from utils.oss_utils import OSSUtil
import websocket
import json
import hmac
import hashlib
import base64
from urllib.parse import quote, urlparse
from modeler.mysql.app_orm import AppOrm

from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from requests.exceptions import RequestException, Timeout


class MyBaseChat:
    """
    调用各类模型的基础类
    子类需根据不同调用方式实现receive接口
    """

    def receive(self, content: str, is_stream=True, **kwargs):
        """
        具体调用模型获取内容的方法
        content: 提问内容
        is_stram: 是否流式返回
        """
        raise NotImplemented

    def get_vector(self, input_text, tp_user_id):
        """
        获取向量数据
        """
        if self.model_path == 'text-embedding-ada-002':
            payload = json.dumps({
                "input_text": input_text,
                "user": tp_user_id
            })
            headers = {
                'corp-id': self.corp_id,
                'Content-Type': 'application/json',
            }
            url = JusureT3Api + "/chat/embedding"
            response = requests.request("POST", url, headers=headers, data=payload).json()
            return response['data']['data'][0]['embedding']
        app_key = self.kwargs.get('app_key')
        if self.model_code == WENXIN_CODE:
            app_secret = self.kwargs.get('app_secret')
            qianfan = QianFanModel({'Api_Key': app_key, 'Secret_Key': app_secret})
            data = qianfan.create_embeddings([input_text], tp_user_id, model_path=self.model_path)
            return data['data'][0]['embedding']
        elif self.model_code == QIANWEN_CODE:
            qianfan = QianWenModel(api_key=app_key)
            data = qianfan.create_embeddings([input_text], model_path=self.model_path)
            return data['output']['embeddings'][0]['embedding']
    
    def response_format(self, res, usage=None, reasoning_content=''):
        if usage is None:
            usage = {}
        return  json.dumps({'result': res, 'usage': usage, 'reasoning_content':reasoning_content}).encode()


class MyWenXin(MyBaseChat):

    def __init__(self, app_key, app_secret, model='ernie-bot-turbo', prompt='', old_content=[], **kwargs):
        self.messages = []
        self.old_content = old_content
        self.kwargs = kwargs
        self.model_path = model
        if prompt:
            self.messages.append({"role": "system", "content": prompt})
        # self.llm = Wenxin(temperature=0.9, model=model, baidu_api_key=app_key,
        #                   baidu_secret_key=app_secret, verbose=True)
        logger.error("currently not support MyWenXin")
        self.llm = None
        self.messages.extend(old_content)
        logging.error(f'no longer support early version for langchain_wenxin')
        # self.qianfan = QianFanModel({'Api_Key': app_key, 'Secret_Key': app_secret},
        #                             prompt=prompt)

    def receive(self, content: str, is_stream=True, **kwargs):
        if not content:
            return
        content = [{"role": "user", "content": content}]
        msg = self.messages.copy()
        msg.extend(content)
        if not is_stream:
            yield self.response_format(self.llm.invoke(msg))
            return
        for item in self.llm.stream(msg):
            yield self.response_format(item)
        # if is_stream:
        #     resp = self.qianfan.qianfan_chat_stream(msg, api_path=self.model_path)
        #     for item in resp.iter_lines():
        #         yield item


class MyChatGPT(MyBaseChat):

    def __init__(self, gpt_url, data_type='text', prompt='', model='gpt-4', old_content=[], corpid='', dalle_flag=False, **kwargs):
        self.gpt_url = gpt_url
        self.data_type = data_type
        self.prompt = prompt
        self.model = model
        self.corpid = corpid
        self.kwargs = kwargs
        self.dalle_flag = dalle_flag
        self.message = []
        if self.prompt:
            self.message.append({'role': 'system', 'content': self.prompt})
        if old_content:
            self.message.extend(old_content)

    def receive(self, content: str, is_stream=True, session_id='', tp_user_id='', data_type='', **kwargs):
        if self.data_type == 'text':
            msg = self.message.copy()
            msg.append({"role": "user", "content": content})
            body = {
                "messages": msg,
                "model_path": self.model,
                "is_stream": is_stream,
            }
            gpt_url = JusureT3Api + '/jusurechat/ai/chat'
            if not is_stream:
                resp = requests.post(gpt_url, json=body, headers={'Content-Type': 'application/json', 'corpid': self.corpid}, stream=False)
                yield self.response_format(resp.json().get('data'))
                
                return
            resp = requests.post(gpt_url, json=body, headers={'Content-Type': 'application/json', 'corpid': self.corpid}, stream=True)
            for item in resp.iter_lines():
                item = json.loads(item.decode()) if item else {}
                yield self.response_format(item.get('message'))
            return
        elif self.data_type == 'text2img':
            if self.dalle_flag:
                yield self.response_format(self.call_dalle(content))
                return
        body = {
            "messages": f'{self.prompt}{content}',
            "message_id": get_uuid(),
            "session_id": session_id,
            "ww_id": tp_user_id,
            'data_type': data_type or self.data_type  # text, img2img, text2img
        }
        resp = requests.post(self.gpt_url, json=body, headers={'Content-Type': 'application/json', 'corpid': self.corpid}).json()
        if resp['code'] != 0:
            raise f'生成失败'
        message = resp.get('data', {}).get('message', {}).get('message')
        if not is_stream:
            yield self.response_format(message)

        pattern = r'[，。]'
        symbols = re.findall(pattern, message)
        delimiter_pattern = re.compile(pattern)
        sentences = delimiter_pattern.split(message)
        for i in range(len(sentences)):
            yield self.response_format(sentences[i] + symbols[i] if i < len(symbols) else sentences[i])
            time.sleep(0.1)

    def call_dalle(self, content):
        client = OpenAI(api_key=self.kwargs.get('api_key'))
        response = client.images.generate(
            model="dall-e-3",
            prompt=content,
            size="1024x1024",
            quality="standard",
            n=1,
            response_format='b64_json'
        )

        image_b64 = response.data[0].b64_json
        image_data = base64.b64decode(image_b64)
        oss = OSSUtil(self.corpid)
        img_name = f'{self.corpid}/dalle/{get_uuid()}.png'
        oss.upload(img_name, image_data)
        file_link = oss.get_public_url(img_name)
        return [file_link]


class MyImgReader(MyBaseChat):

    def __init__(self, chat, prompt='', **kwargs):
        self.chat = chat
        self.prompt = prompt
        self.kwargs = kwargs

    def receive(self, content, is_stream=True, img_url='', session_id='', tp_user_id='', **kwargs):
        file_path = self.get_file_path(img_url)
        if not file_path:
            yield self.response_format('无法提取关键信息及进展！')
            return
        # todo 抽离到return_ocr_result里面
        ocr = RapidOCR()
        result, _ = ocr(file_path)
        if not result:
            yield self.response_format('无法提取关键信息及进展！')
            return
        docs = '\n'.join([line[1] for line in result])
        content = f'{self.prompt}{docs}'
        for item in self.chat.receive(content, is_stream=is_stream, session_id=session_id, tp_user_id=tp_user_id):
            yield item

    @classmethod
    def get_file_path(cls, img_url):
        try:
            bucket_name = img_url.split('/')[2].split('.')[0]
            file_path = OSS_DIR + bucket_name +urlparse(img_url).path
            if not os.path.exists(file_path):
                file_path = requests.get(img_url).content
            return file_path
        except Exception as e:
            logger.error(f'get_file_path error: {e}')

class MyQianWenAsync(MyBaseChat):
    def __init__(self, app_key, model='', prompt='', old_content=[], **kwargs):
        self.app_key = app_key
        self.model = model or dashscope.Generation.Models.qwen_turbo
        self.prompt = prompt
        self.messages = []
        if self.prompt:
            self.messages.append({'role': 'system', 'content': self.prompt})
        if old_content:
            self.messages.extend(old_content)
        self.kwargs = kwargs
        self._executor = ThreadPoolExecutor(max_workers=1)

    async def receive(self, content, is_stream=False, **kwargs):
        msg = self.messages.copy()
        msg.append({'role': 'user', 'content': content})

        try:
            response = await asyncio.get_event_loop().run_in_executor(
                self._executor,
                lambda: dashscope.Generation.call(
                    self.model,
                    messages=msg,
                    api_key=self.app_key,
                    result_format='message',
                    stream=is_stream
                )
            )

            if not is_stream:
                data = json.loads(json.dumps(response))
                result = data.get('output', {}).get('choices', [{}])[0].get('message', {}).get('content', '')
                usage = data.get('usage') or {}
                usage = {'prompt_tokens': usage.get('input_tokens', 0),
                         'completion_tokens': usage.get('output_tokens', 0)}
                yield self.response_format(result,usage)
                return

            full_result = ''
            for item in response:
                data = json.loads(json.dumps(item))
                result = data.get('output', {}).get('choices', [{}])[0].get('message', {}).get('content', '')

                # 只生成增量内容
                new_result = result[len(full_result):]
                full_result = result

                usage = data.get('usage') or {}
                usage = {'prompt_tokens': usage.get('input_tokens', 0),
                         'completion_tokens': usage.get('output_tokens', 0)}

                # 只在有增量内容时生成
                if new_result:
                    yield self.response_format(new_result,usage)

        except Exception as e:
            logger.error(f'MyQianWen.receive error: {e}')
            yield self.response_format('生成失败！')

class MyQianWen(MyBaseChat):

    def __init__(self, app_key, model='', prompt='', old_content=[], **kwargs):
        self.app_key = app_key
        self.model = model or dashscope.Generation.Models.qwen_turbo
        self.prompt = prompt
        self.messages = []
        if self.prompt:
            self.messages.append({'role': 'system', 'content': self.prompt})
        if old_content:
            self.messages.extend(old_content)
        self.kwargs = kwargs

    def receive(self, content, is_stream=True, **kwargs):
        msg = self.messages.copy()
        msg.append({'role': 'user', 'content': content})
        logger.info(len(json.dumps(msg)))
        try:
            logger.info(f"self.model:======= {self.model}")
            response = dashscope.Generation.call(
                self.model,
                messages=msg,
                api_key=self.app_key,
                result_format='message',
                stream=is_stream
            )
            if not is_stream:
                data = json.loads(json.dumps(response))
                result = data.get('output', {}).get('choices', [{}])[0].get('message', {}).get('content', '')
                usage = data.get('usage') or {}
                usage = {'prompt_tokens': usage.get('input_tokens', 0), 'completion_tokens': usage.get('output_tokens', 0)}
                yield self.response_format(result,usage)
                return
            last_msg = ''
            for item in response:
                data = json.loads(json.dumps(item))
                result = data.get('output', {}).get('choices', [{}])[0].get('message', {}).get('content', '')
                new_result = result.replace(last_msg, '')
                last_msg = result
                usage = data.get('usage') or {}
                usage = {'prompt_tokens': usage.get('input_tokens', 0), 'completion_tokens': usage.get('output_tokens', 0)}
                yield self.response_format(new_result,usage)
        except Exception as e:
            logger.error(f'MyQianWen.receive error: {e}')
            try:
                response = dashscope.Generation.call(
                    dashscope.Generation.Models.qwen_turbo,
                    messages=msg,
                    api_key=self.app_key,
                    result_format='message',
                    stream=is_stream
                )
                if not is_stream:
                    data = json.loads(json.dumps(response))
                    result = data.get('output', {}).get('choices', [{}])[0].get('message', {}).get('content', '')
                    usage = data.get('usage') or {}
                    usage = {'prompt_tokens': usage.get('input_tokens', 0),
                             'completion_tokens': usage.get('output_tokens', 0)}
                    yield self.response_format(result,usage)
                    return
                last_msg = ''
                for item in response:
                    data = json.loads(json.dumps(item))
                    try:
                        result = data.get('output', {}).get('choices', [{}])[0].get('message', {}).get('content', '')
                        new_result = result.replace(last_msg, '')
                        last_msg = result
                        usage = data.get('usage') or {}
                        usage = {'prompt_tokens': usage.get('input_tokens', 0),
                                 'completion_tokens': usage.get('output_tokens', 0)}
                        yield self.response_format(new_result,usage)
                    except Exception as e:
                        logger.error(f"data: {data}")
            except Exception as e:
                logger.error(f'MyQianWen.receive error: {e}')
                yield '生成失败！'

class MyQianWen_72B(MyBaseChat):
    """
    port 8000: qwen2-7B
    port 8001: glm4-9B
    """

    def __init__(self, model_url='', model_path='', prompt='', old_content=[], **kwargs):
        """
        :param model: 大模型配置
        """
        self.model_url = model_url
        self.model_path = model_path
        # self.context_token = model['context_token'] if model['context_token'] < 11408 else 11408
        self.prompt = prompt
        self.messages = []
        if self.prompt:
            self.messages.append({'role': 'system', 'content': self.prompt})
        if old_content:
            self.messages.extend(old_content)
        self.kwargs = kwargs

    def receive(self, content, is_stream=True, **kwargs):
        """
        :param content: 输入内容
        :param is_stream: 是否流式生成
        """
        model_key = "EMPTY"
        client = OpenAI(
            api_key=model_key,
            base_url=self.model_url,
        )
        msg = self.messages.copy()
        msg.append({'role': 'user', 'content': content})

        # TODO：
        completions = client.chat.completions.create(messages=msg,
                                                     model=self.model_path,
                                                     stream=is_stream,
                                                     temperature=0.3,
                                                     top_p=0.8,
                                                     )
        if not is_stream:
            subjects = completions.choices[0].message.content
            yield json.dumps({'result': subjects, 'usage': ''}).encode()
        else:
            for chunk in completions:
                content = chunk.choices[0].to_dict()
                res = content['delta'].get('content') if content['delta'].get('content') else ''
                yield json.dumps({'result': res, 'usage': ''}).encode()

import httpx
class NoHttps(httpx.Client):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs, verify=False)



class AllInOneModel(MyBaseChat):

    def __init__(self, app_key='', app_secret='',model='', model_url='',  prompt='', old_content=[],enable_return_reasoning_content=False, **kwargs):
        
        """
        :param model: 大模型配置
        """
        self.app_key = app_key
        self.app_secret = app_secret
        self.model_url = model_url
        self.model = model
        # self.context_token = model['context_token'] if model['context_token'] < 11408 else 11408
        self.prompt = prompt
        self.messages = []
        if self.model == LOCAL_MODEL_NAME:
            if enable_return_reasoning_content:
                self.messages.append({'role': 'system', 'content': 'Reasoning flag /think'})
            else:
                self.messages.append({'role': 'system', 'content': 'Reasoning flag /no_think'})
        if self.prompt:
            self.messages.append({'role': 'system', 'content': self.prompt})
        if old_content:
            self.messages.extend(old_content)
        self.kwargs = kwargs
        self.enable_return_reasoning_content = enable_return_reasoning_content
        logger.info('api_key: {}, model_url: {}, model: {}'.format(self.app_key, self.model_url, self.model))

    def receive(self, content, is_stream=True, temperature=0.3, top_p=0.8, **kwargs):
        """
        :param content: 输入内容
        :param is_stream: 是否流式生成
        """

        client = OpenAI(
            api_key=self.app_key,
            base_url=self.model_url,
            http_client=NoHttps()
        )
        msg = self.messages.copy()
        msg.append({'role': 'user', 'content': content})

        logger.info('========= ALL IN ONE MODEL =========')
        logger.info('api_key: {}, model_url: {}, model: {}, msg[:100]:{}, is_stream: {}'.format(
                                self.app_key, self.model_url, self.model, str(msg)[:100], is_stream))

        completions = self.call_model_with_retry(client, self.model, is_stream, msg, temperature, top_p)
        if not is_stream:
            subjects = completions.choices[0].message.content
            yield self.response_format(subjects)
        else:
            for chunk in completions:
                content = chunk.choices[0].to_dict()
                res = content['delta'].get('content') if content['delta'].get('content') else ''
                res = res.replace('\n\n', '\n')
                reasoning_content = content['delta'].get('reasoning_content') if content['delta'].get('reasoning_content') and self.enable_return_reasoning_content else ''
                yield self.response_format(res,reasoning_content=reasoning_content)

    async def receive_async(self, content, is_stream=True, temperature=0.3, top_p=0.8, **kwargs):
        client = AsyncOpenAI(
            api_key=self.app_key,
            base_url=self.model_url,
            http_client=httpx.AsyncClient(verify=False)
        )
        msg = [{'role': 'user', 'content': content}]

        # logger.info('api_key: {}, model_url: {}, model: {}, msg[:100]:{}, is_stream: {}'.format(
        #     self.app_key, self.model_url, self.model, str(msg)[:100], is_stream))

        completions = await self.call_model_with_retry_async(client, self.model, is_stream, msg, temperature, top_p)

        if not is_stream:
            subjects = completions.choices[0].message.content
            yield self.response_format(subjects)
        else:
            async for chunk in completions:
                if chunk.choices and len(chunk.choices) > 0:
                    content_data = chunk.choices[0].to_dict()
                    delta = content_data.get('delta', {})

                    res = delta.get('content', '') or ''
                    reasoning_content = ''

                    if self.enable_return_reasoning_content:
                        reasoning_content = delta.get('reasoning_content', '') or ''

                    if res or reasoning_content:
                        yield self.response_format(res, reasoning_content=reasoning_content)

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=2, min=4, max=30),
        retry=retry_if_exception_type((RequestException, Timeout, Exception)),
        reraise=True
    )
    def call_model_with_retry(self, client, model, is_stream, messages, temperature, top_p=0.8):
        """
        带重试机制的模型调用
        """
        try:
            response = client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                stream=is_stream,
                top_p=top_p,
            )
            return response
        except Exception as e:
            logger.error(f"模型调用失败: {str(e)}")
            if isinstance(e, Timeout):
                time.sleep(10)
            raise

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=2, min=4, max=30),
        retry=retry_if_exception_type((RequestException, Timeout, Exception)),
        reraise=True
    )
    async def call_model_with_retry_async(self, client, model, is_stream, messages, temperature, top_p=0.8):
        try:
            response = await client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                stream=is_stream,
                top_p=top_p,
            )
            return response
        except Exception as e:
            logger.error(f"模型调用失败: {str(e)}")
            if isinstance(e, Timeout):
                await asyncio.sleep(10)
            raise


            
class Ascend_910B(MyBaseChat):
    """
    port 8000: qwen2-7B
    port 8001: glm4-9B
    """

    def __init__(self, model_url='', model_path='', prompt='', old_content=[], **kwargs):
        """
        :param model: 大模型配置
        """
        self.model_url = model_url
        self.model_path = model_path
        # self.context_token = model['context_token'] if model['context_token'] < 11408 else 11408
        self.prompt = prompt
        self.messages = []
        if self.prompt:
            self.messages.append({'role': 'system', 'content': self.prompt})
        if old_content:
            self.messages.extend(old_content)
        self.kwargs = kwargs

    def receive(self, content, is_stream=True):
        """
        :param content: 输入内容
        :param is_stream: 是否流式生成
        """
        msg = self.messages.copy()
        msg.append({"role": "user", "content": content})
        body = {
            "inputs": msg,
            "parameters": {
                "details": "true",
                "do_sample": "true",
                "repetition_penalty": 1.1,
                "return_full_text": "false",
                "seed": "null",
                "temperature": 0.3,
                "top_p": 0.99
            },
            "stream": is_stream
        }

        gpt_url = JusureT3Api   # TODO
        logger.info(f"### 910b gpt_url: {gpt_url},  body: {body}")
        if not is_stream:
            resp = requests.post(gpt_url, json=body,
                                 headers={'Content-Type': 'application/json'}, stream=False)
            yield self.response_format(resp.json().get('data'))
            return
        resp = requests.post(gpt_url, json=body, headers={'Content-Type': 'application/json'},
                             stream=True)
        for item in resp.iter_lines():
            item = json.loads(item.decode()) if item else {}
            yield self.response_format(item.get('message'))
        return

class AiChatGpt:

    def __init__(self, corpid):
        self.corpid = corpid

    def receive(self, messages, model_path, is_stream=True):
        body = {
            "messages": messages,
            "model_path": model_path,
            "is_stream": is_stream
        }
        gpt_url = JusureT3Api + '/jusurechat/ai/chat'
        logger.info(f"Post ChatGPT API: {gpt_url} 模型: {model_path}")
        try:
            t1 = time.time()
            resp = requests.post(gpt_url, json=body, headers={'Content-Type': 'application/json', 'corpid': self.corpid}, stream=is_stream)
            # logger.info(f"Post ChatGPT API succeeded!, codeStatus: {resp.status_code}, requestTime: {int(time.time()-t1)*1000} ms")
            logger.info(f"Post ChatGPT API succeeded!, codeStatus: {resp.status_code}, requestTime: {round((time.time()-t1)*1000, 2)} ms")
        except Exception as e:
            logger.error(f"Post ChatGPT API failured!, codeStatus: {resp.status_code}")
        return resp


class MyRadarRecommend(MyBaseChat):

    def __init__(self, model_path='embedding-v1', dims=384, corp_id='', batch=0, model_code='', **kwargs):
        self.model_path = model_path
        self.model_dims = dims
        self.kwargs = kwargs
        self.es_index = f'radar_{model_path}'
        self.corp_id = corp_id
        self.batch = batch   # 换一批
        self.model_code = model_code
        self.es = EsSdkPool(self.corp_id)

    def receive(self, content, is_stream=True, tp_user_id='', **kwargs):
        vector = self.get_vector(content, tp_user_id)
        start = self.batch * 5
        body_dict = {
            "_source": {
                # 指定查询字段字段
                "includes": ["radar_id", "radar_name", "radar_title", "pic_url", "qr_code", "send_num",
                             "collect_num", "read_time"]},
            "query": {
                "script_score": {
                    # 查询固定条件 delete_flag = False
                    "query": {"term": {"delete_flag": {"value": False}}},
                    # 无固定查询条件
                    # "query": "match_all": {},
                    # 分数要求, 1.4-精确匹配, 1.1-宽松匹配
                    "min_score": 1.1,
                    "script": {
                        # 向量取余弦计算分数
                        "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",  # +1 规避计算分数结果为负数
                        "params": {"query_vector": vector}}}},
            # 指定查询的条数
            "size": self.batch * 5 + 5}
        search_res = self.es.search_data(self.es_index, body_dict)
        if not is_stream:
            yield [item['_source'] for item in search_res['hits']['hits'][start: start + 5]]
            return
        for item in search_res['hits']['hits'][start: start + 5]:
            yield item['_source']


class MyMaterialRecommend(MyBaseChat):

    def __init__(self, corp_id='', batch=0, app_model=None, model_path='embedding-v1',
                 dims=384, model_code=QIANWEN_CODE, **kwargs):
        self.corp_id = corp_id
        self.batch = batch  # 换一批
        self.app_model = app_model
        self.model_path = model_path
        self.model_dims = dims
        self.kwargs = kwargs
        self.model_code = model_code
        self.es_index = f'content_{model_path}'
        self.es = EsSdkPool(self.corp_id)

    def receive(self, content, is_stream=True, tp_user_id='', **kwargs):
        """
        素材推荐 与模型无关
        先请求/ems/select/material获取content_ids
        再请求/content/recommend获取素材数据进行返回
        """
        vector = self.get_vector(content, tp_user_id)
        start = self.batch * 5
        body_dict = {
            "_source": {
                "includes": ["content_id"]},
            "query": {
                "script_score": {
                    # 查询固定条件 delete_flag = False
                    "query": {"term": {"delete_flag": {"value": 0}}},
                    # 无固定查询条件
                    # "query": "match_all": {},
                    # 分数要求, 1.4-精确匹配, 1.1-宽松匹配
                    "min_score": 1.1,
                    "script": {
                        # 向量取余弦计算分数
                        "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",  # +1 规避计算分数结果为负数
                        "params": {"query_vector": vector}}}},
            # 指定查询的条数
            "size": self.batch * 5 + 5}
        search_res = self.es.search_data(self.es_index, body_dict)
        content_ids = [item['_source']['content_id'] for item in search_res['hits']['hits'][start: start + 5]]
        if content_ids:
            data = self.app_model.get_content_data(content_ids)
            if not is_stream:
                yield data
                return
            for item in data:
                yield item


class MyLocalModel(MyBaseChat):
    """
    代表着所有内部服务器在跑的模型通用调用方式
    先发mq通知模型, 再查询redis取生成结果
    """

    def __init__(self, prompt='', old_content=[], cfg='aigc_chatglm', **kwargs):
        self.mq = AiGcRabbitMQProducer(cfg=cfg)
        self.redis_client = redis_pool.use(redis_pool.aigc_pool)
        self.message = []
        self.prompt = prompt
        self.kwargs = kwargs
        self.cfg = cfg
        if self.prompt:
            self.message.append({'role': 'system', 'content': self.prompt})
        if old_content:
            self.message.extend(old_content)

    def receive(self, content, is_stream=True, message_id=str(uuid.uuid4().hex), **kwargs):
        """
        发消息到mq
        然后监听redis取生成数据
        """
        msg = self.message.copy()
        if msg:
            msg.append({'role': 'user', 'content': content})
        else:
            msg = [{'role': 'system', 'content': content}]
        send_data = {'message_id': message_id, 'content': msg}
        send_data = json.dumps(send_data, ensure_ascii=False).encode()
        self.mq.start(send_data)
        time.sleep(0.5)
        last_msg = ''
        all_msg = ''
        i = 0
        while i < 50:
            # 10秒内redis未取到任何数据或者一直取到的数据不变, 则说明生成失败
            res = self.redis_client.hgetall(f'{self.cfg}_{message_id}')
            if res:
                res = dict(res)
                content = res.get('content')
                status = res.get('status')
                if status == 'error':
                    return f'生成失败: {content}'
                if is_stream:
                    new_content = content.replace(last_msg, '')
                    if new_content:
                        last_msg = content
                        yield self.response_format(new_content)
                        i -= 1  # 一直在正常生成时需要保证完全生成, 通过status正常结束而不是i值
                    if status == 'finished':
                        return ''
                    else:
                        i += 1
                else:
                    all_msg += content
                    if status == 'finished':
                        return all_msg
            else:
                i += 1
            time.sleep(0.2)
        return '生成失败'


class MyAINumber(MyBaseChat):  # TODO
    """
    智能问数
    1. gpt解析输入生成要查询的统计
    2. 查询ai_question_number问数表确定要查询的统计
    3. 请求查询接口获取数据返回
    """

    def __init__(self, app_model=None, corp_id='', **kwargs):
        self.app_model = app_model
        self.corp_id = corp_id
        self.kwargs = kwargs

    def receive(self, content, is_stream=True, **kwargs):
        history_list = kwargs.get('history_list', [])[-3:]
        history = '，'.join([i['question'] for i in history_list])

        # yt = self.classifier(content, history)
        # logger.info(yt)
        # if '独立问题' in yt:
        #     history = ''
        # ai_prompt = get_ai_number_prompt(self.corp_id, content, history)
        #
        # matches = re.findall(r'\[(.*?)\]', ai_prompt)
        ai_prompt = get_ai_number_prompt_v2(self.corp_id, content, history)

        # if len(matches) == 4:
        if isinstance(ai_prompt, dict):
            # dict_content = {'start_date': matches[0], 'end_date': matches[1], 'data_name': matches[2], 'type': matches[3],
            #                 'time_bgn': matches[0], 'time_end': matches[1], 'bgn_date': matches[0]}
            dict_content = {
                'start_date': ai_prompt.get('开始时间'),
                'end_date': ai_prompt.get('结束时间'),
                'data_name': ai_prompt.get('何种数据'),
                'time_bgn': ai_prompt.get('开始时间'),
                'time_end': ai_prompt.get('结束时间'),
                'bgn_date': ai_prompt.get('开始时间'),
                'yoy': True if ai_prompt.get('同比') == '是' else False,
                'qoq': True if ai_prompt.get('环比') == '是' else False,
                'province': ai_prompt.get('省份', '').replace('省', '').replace('市', '').replace('县', '').replace('区', '').replace(
                    '全国', '')
            }
            charts_type = ai_prompt.get('何种图形', '')
            if charts_type:
                if charts_type == '柱状图' or charts_type == '条形图':
                    dict_content['charts_type'] = 'bar'
                elif charts_type == '折线图' or charts_type == '趋势图':
                    dict_content['charts_type'] = 'line'

            product_list = ['云视讯', '千里眼', '和对讲', '政企']
            intersection = list(set(product_list) & set(ai_prompt.get('产品', '').split(',')))
            if intersection:
                filtered_list = [item for item in intersection if item]

                dict_content['product'] = ','.join(filtered_list)
            else:
                dict_content['product'] = ''
            if dict_content.get('province'):
                province_list = dict_content.get('province').split(',')
                filtered_list = [item for item in province_list if item]

                dict_content['province'] = ','.join(filtered_list)

            ai_data = self.app_model.get_ai_question_number(dict_content['data_name'])
            if not ai_data:
                yield {'data': '抱歉，输入的内容无法进行解析和匹配。请提详细内容。如：查看1月-3月大视频整体收入情况等。',
                       'ai_data': {'component_code': 'error'}, 'params': {}}
                return
            params = {}
            for k in ai_data['w'].split(','):
                params.update({k: dict_content.get(k)})

            url = ai_data['uri']
            method = ai_data['r']
            data = self.request_api(url, method, params)
            yield {'data': data.get('data'), 'ai_data': ai_data, 'params': params}
        else:
            yield {'data': '抱歉，输入的内容无法进行解析和匹配。请提详细内容。如：查看1月-3月大视频整体收入情况等。。',
                   'ai_data': {'component_code': 'error'}, 'params': {}}

    def request_api(self, url, method, params):
        headers = {'Content-Type': 'application/json', 'corp-id': self.corp_id, 'token': g.token}
        url = f'{JusureT3Url}{UrlEnv}{url}'
        if method.upper() == 'GET':
            param = ''.join([f'{k}={v}&' for k, v in params.items()])
            suf = '&' if '?' in url else '?'
            url += f'{suf}{param[:-1]}'
            return requests.get(url, headers=headers).json()
        elif method.upper() == 'POST':
            return requests.post(url, headers=headers, json=params).json()
        return requests.request(method, url, params).json()

    def classifier(self, input_text, history_question):
        if not history_question:
            content = "独立问题"
        else:
            content = "请根据输入的历史问题：%s和当前输入内容：%s。判断当前意图是独立问题还是和上下文相关联内容。" % (
                history_question, input_text)
        messages = [
            {'role': 'system', 'content': content}
        ]
        app_model = AppOrm(self.corp_id)
        model_conf = app_model.get_model(CHATGPT_MODEL_ID)
        response = MyChatGPT(model_conf.model_url, model='gpt-4o',).receive(messages, is_stream=False)
        res_str = json.loads(response.decode()).get('result')
        return res_str


class MyBing(MyBaseChat):
    """
    Bing搜索大模型
    先bing搜索内容
    再调用模型进行总结
    """

    def __init__(self, url, api_key, corp_id='', prompt='', chat_model=None, **kwargs):
        self.url = url
        self.api_key = api_key
        self.kwargs = kwargs
        self.corp_id = corp_id
        self.prompt = prompt
        self.chat_model = chat_model

    def receive(self, content, is_stream=True, **kwargs):
        params = {'q': content, 'mkt': 'en-ZH'}
        headers = {'Ocp-Apim-Subscription-Key': self.api_key}
        response = requests.get(self.url, headers=headers, params=params)
        response.raise_for_status()
        bing_res = ''
        for item in response.json()['webPages']['value']:
            bing_res += f"标题：{item.get('name')}\n简介：{item.get('snippet')}"

        if is_stream:
            # 此处item是其他model的recevie的返回值，格式为：{'result','useage','reasoning_content'}
            for item in self.chat_model.receive(bing_res, **kwargs):
                yield item
        else:
            yield self.chat_model.receive(bing_res, is_stream=False, **kwargs)

    def receive_v2(self, content, is_chat=True, **kwargs):
        params = {'q': content, 'mkt': 'en-ZH'}
        headers = {'Ocp-Apim-Subscription-Key': self.api_key}
        response = requests.get(self.url, headers=headers, params=params)
        response.raise_for_status()
        res_datas = response.json()['webPages']['value']
        ret = list()
        for item in res_datas:
            ret.append(f"{item.get('name')} \n {item.get('snippet')}")
        return ret


class MyLlama(MyBaseChat):

    def test_ai_number(self, content):
        msg = get_ai_number_prompt_v2(content)
        logger.info(msg)
        logger.info('输入：%s' % content)
        llm = ChatOpenAI(model="llama3-8b", temperature=0, openai_api_base="http://*************:51000/v1", openai_api_key="EMPTY")

        res = llm.predict(msg)
        logger.info('输出：%s' % res)


def get_ai_number_prompt_v2(corpid, message, history=''):
    message = message.replace(',', '和').replace('，', '和').replace('、', '')
    now_date = datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d')
    now_year = datetime.datetime.strftime(datetime.datetime.now(), '%Y')
    today_list = ['今天', '今日', '当日']
    yesterday_list = ['昨天', '昨日']
    qian_day_list = ['前天']
    da_qian_day_list = ['大前天']
    if any(word in message for word in today_list):
        message = replace_with_date(message, today_list, now_date)
    if any(word in message for word in yesterday_list):
        yesterday = datetime.datetime.strftime(datetime.datetime.now() - datetime.timedelta(days=1), '%Y-%m-%d')
        message = replace_with_date(message, yesterday_list, yesterday)
    if any(word in message for word in qian_day_list):
        qian_day = datetime.datetime.strftime(datetime.datetime.now() - datetime.timedelta(days=2), '%Y-%m-%d')
        message = replace_with_date(message, qian_day_list, qian_day)
    if any(word in message for word in da_qian_day_list):
        da_qian_day = datetime.datetime.strftime(datetime.datetime.now() - datetime.timedelta(days=3), '%Y-%m-%d')
        message = replace_with_date(message, da_qian_day_list, da_qian_day)
    try:

        data_str = AiModelOrm(corpid).get_ai_question_number_data()

        content = ("# 角色\n"
                   "你是一个知识渊博的智能助手，能够根据用户的需求迅速提取有用的信息。\n\n"
                   "## 技能\n"
                   "### 技能1 内容提取\n"
                   "1. 根据输入内容提取开始时间、结束时间、何种数据、产品、省份、同比、环比、何种图形。\n"
                   "### 技能2 精准的时间计算\n"
                   "1. 当出现，今天、昨天、本周、上周、本月、上个月、某月、某日、某年等时间相关的关键词的时候请根据{now_date}这个日期计算开始和结束时间。\n"
                   "2. 当出现只有月份没有年份那么年份为{now_year}年。\n"
                   "3. 如果没有开始时间：开始时间为2024-01-01，如果没有结束时间：结束时间为2024-06-30。\n"
                   "### 技能3 精准知识库检索匹配\n"
                   "1. 何种数据与知识库内容进行模糊匹配，在知识库中匹配一个你认为最接近的数据。\n\n"
                   "## 限制:\n"
                   "- 开始时间和结束时间格式为：xxxx-xx-xx.\n"
                   "- 何种数据：只能在知识库中选取，不能为空，只能匹配一个。\n"
                   "- 同比：是或否\n"
                   "- 环比：是或否\n"
                   "- 何种图形：选择范围”条形图、柱状图、折线图、趋势图“，匹配到不返回空\n"
                   "- 产品：多个产品用,连接，提取不到返回空\n"
                   "- 省份：多个省份用,连接，全省，全部省，全国等包含全部的时候这个字段为空，提取不到返回空\n"
                   "- 返回：严格的json格式\n\n"
                   "## 知识库内容\n"
                   "{data_str}\n\n"
                   "## 输入内容\n"
                   f"请根据历史输入：{history}和当前输入内容：{message}。").format(history=history, message=message, now_date=now_date,
                                                                                now_year=now_year, data_str=data_str)
        # logger.info(content)
        messages = [
            {'role': 'system', 'content': content}
        ]
        app_model = AppOrm(corpid)
        model_conf = app_model.get_model(CHATGPT_MODEL_ID)
        response = MyChatGPT(model_conf.model_url, model='gpt-4o',).receive(messages, is_stream=False)
        res_str = json.loads(response.decode()).get('result')
        pattern = r'\{.*?\}'
        matches = re.findall(pattern, res_str, re.DOTALL)

        for match in matches:
            logger.info(match)
        return json.loads(match)
    except Exception as e:
        logger.error(e)
        return ''


def get_ai_number_prompt(corpid, message, history=''):
    now_date = datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d')
    now_year = datetime.datetime.strftime(datetime.datetime.now(), '%Y')
    today_list = ['今天', '今日', '当日']
    yesterday_list = ['昨天', '昨日', '前日']
    qian_day_list = ['前天']
    da_qian_day_list = ['大前天']
    # if any(word in message for word in today):
    #     start_date = now_date
    #     end_date = now_date
    # elif any(word in message for word in yesterday):
    #     start_date = datetime.datetime.strftime(datetime.datetime.now() - datetime.timedelta(days=1), '%Y-%m-%d')
    #     end_date = start_date
    # elif any(word in message for word in qian_day):
    #     start_date = datetime.datetime.strftime(datetime.datetime.now() - datetime.timedelta(days=2), '%Y-%m-%d')
    #     end_date = start_date
    # elif any(word in message for word in da_qian_day):
    #     start_date = datetime.datetime.strftime(datetime.datetime.now() - datetime.timedelta(days=3), '%Y-%m-%d')
    #     end_date = start_date
    # else:
    #     start_date = ''
    #     end_date = ''
    #
    # if start_date and end_date:
    #     message = message + start_date + '至' + end_date
    if any(word in message for word in today_list):
        message = replace_with_date(message, today_list, now_date)
    if any(word in message for word in yesterday_list):
        yesterday = datetime.datetime.strftime(datetime.datetime.now() - datetime.timedelta(days=1), '%Y-%m-%d')
        message = replace_with_date(message, yesterday_list, yesterday)
    if any(word in message for word in qian_day_list):
        qian_day = datetime.datetime.strftime(datetime.datetime.now() - datetime.timedelta(days=2), '%Y-%m-%d')
        message = replace_with_date(message, qian_day_list, qian_day)
    if any(word in message for word in da_qian_day_list):
        da_qian_day = datetime.datetime.strftime(datetime.datetime.now() - datetime.timedelta(days=3), '%Y-%m-%d')
        message = replace_with_date(message, da_qian_day_list, da_qian_day)
    try:

        data_str = AiModelOrm(corpid).get_ai_question_number_data()

        content = ("请根据历史输入：%s和当前输入内容：%s；按照模板“查看[开始时间]到[结束时间]关于[何种数据]的[何种图形]”，提取[]内内容，重新输出,模板中的‘[’和‘]’要保留，完全按照模板格式输出，"
                   "加中括号并对于时间类型解析出具体时间，如果出现，今天、昨天、本周、上周、本月、上个月等时间相关的关键词的时候请根据%s这个日期"
                   "计算开始和结束时间，如果只有月份没有年份那么年份为%s年，如果没有时间就默认从%s结束往前推7天，时间格式xxxx-xx-xx，"
                   "其中何种数据请从“%s”里面匹配，"
                   "可以模糊匹配，如果匹配不到请找一个相近的匹配，只返回我的模板内容即可") % (
                      history, message, now_date, now_year, now_date, data_str)

        # return content
        messages = [
            # {'role': 'system', 'content': '您好，今天是%s，后面出现今天，昨天，上周，本周等和时间相关的时候，请根据这个日期计算' % now_date},
            {'role': 'system', 'content': content}
        ]
        app_model = AppOrm(corpid)
        model_conf = app_model.get_model(CHATGPT_MODEL_ID)
        response = MyChatGPT(model_conf.model_url, model='gpt-4o',).receive(messages, is_stream=False)
        data = json.loads(response.decode()).get('result')
        return data
    except Exception as e:
        logger.error(e)
        return ''


def replace_with_date(text, keywords, replace_date):
    for keyword in keywords:
        text = text.replace(keyword, replace_date)
    return text


def get_ai_number_prompt_free(message, history=''):
    now_date = datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d')
    now_year = datetime.datetime.strftime(datetime.datetime.now(), '%Y')
    # content = ("# 角色\n"
    #            "你是一个知识渊博的智能助手，能够根据用户的需求迅速提取有用的信息。\n\n"
    #            "## 技能\n"
    #            "### 技能1 内容提取\n"
    #            "1. 根据输入内容提取开始时间、结束时间、何种数据、产品、省份、同比、环比、排名、占比、何种图形。\n"
    #            "### 技能2 精准的时间计算\n"
    #            "1. 当出现，今天、昨天、本周、上周、本月、上个月、某月、某日、某年、截止等时间相关的关键词的时候请根据{now_date}这个日期计算开始和结束时间。\n"
    #            "2. 当出现只有月份没有年份那么年份为{now_year}年。\n"
    #            "### 技能3 精准知识库检索匹配\n"
    #            "1. 何种数据与知识库内容进行模糊匹配，匹配一个你认为最接近的数据。\n\n"
    #            "## 限制:\n"
    #            "- 开始时间和结束时间格式为：xxxx-xx-xx.没有时间 返回空\n"
    #            "- 何种数据：选择范围”收入、订单数量、DICT服务包订单数、DICT服务包收入、标品收入、到达用户数、新增用户数、集团客户数、退订用户数、客诉到达量、新增客诉量、高清会场数、到达用户数、新增用户数、集团客户数、退订用户数、客诉到达量、软终端用户数、收编视频到达路数、新增收编视频路数、累计接入数“，匹配不到返回空。\n"
    #            "- 同比：是或否\n"
    #            "- 环比：是或否\n"
    #            "- 排名：是或否\n"
    #            "- 占比：是或否\n"
    #            "- 何种图形：选择范围”条形图、柱状图、折线图、趋势图、饼状图“，匹配到不返回空\n"
    #            "- 产品：多个产品用,连接，提取不到返回空\n"
    #            "- 省份：多个省份用,连接，提取不到返回空\n"
    #            "- 返回：严格的json格式\n\n"
    #            "## 输入内容\n"
    #            f"请根据历史输入：{history}和当前输入内容：{message}。").format(now_date=now_date, now_year=now_year, history=history,
    #                                                                         message=message)
    # # logger.info(content)
    # messages = [
    #     {'role': 'system', 'content': content}
    # ]
    # response = AiChatGpt(g.corpid).receive(messages, 'gpt-4', is_stream=False)
    # res_str = response.json().get('data')
    # pattern = r'\{.*?\}'
    # matches = re.findall(pattern, res_str, re.DOTALL)
    #
    # for match in matches:
    #     logger.info(match)
    # ai_prompt = json.loads(match)
    ai_prompt = {
        "开始时间": "2024-01-01",
        "结束时间": "2024-12-31",
        "何种数据": "收入",
        "产品": "大视频",
        "省份": "",
        "同比": "否",
        "环比": "否",
        "排名": "否",
        "占比": "否",
        "何种图形": "条形图"
    }

    dict_content = {
        'start_date': ai_prompt.get('开始时间'),
        'end_date': ai_prompt.get('结束时间'),
        'data_name': ai_prompt.get('何种数据'),
        'time_bgn': ai_prompt.get('开始时间'),
        'time_end': ai_prompt.get('结束时间'),
        'bgn_date': ai_prompt.get('开始时间'),
        'yoy': True if ai_prompt.get('同比') == '是' else False,
        'qoq': True if ai_prompt.get('环比') == '是' else False,
        'rank': True if ai_prompt.get('排名') == '是' else False,
        'percentage': True if ai_prompt.get('占比') == '是' else False,
        'province': ai_prompt.get('省份', '').replace('省', '').replace('市', '').replace('县', '').replace('区', '').replace(
            '全国', '')
    }
    charts_type = ai_prompt.get('何种图形', '')
    if charts_type:
        if charts_type == '柱状图' or charts_type == '条形图':
            dict_content['charts_type'] = 'bar'
        elif charts_type == '折线图' or charts_type == '趋势图':
            dict_content['charts_type'] = 'line'
        elif charts_type == '饼状图' or ai_prompt.get('占比') == '是':
            dict_content['charts_type'] = 'pie'
    else:
        dict_content.update({
            'charts_type': 'line'
        })
    product_list = ['云视讯', '千里眼', '和对讲', '政企']
    intersection = list(set(product_list) & set(ai_prompt.get('产品', '').split(',')))
    if intersection:
        filtered_list = [item for item in intersection if item]

        dict_content['product'] = ','.join(filtered_list)
    else:
        dict_content['product'] = ''
    if dict_content.get('province'):
        province_list = dict_content.get('province').split(',')
        filtered_list = [item for item in province_list if item]

        dict_content['province'] = ','.join(filtered_list)
    return dict_content


class XFAiModel:
    """
    兼容 MyLocalModel 的 WebSocket 大模型调用类
    """

    def __init__(self, prompt='', old_content=[]):
        """
        初始化类实例
        """
        self.model_url = LOCAL_MODEL_PATH
        self.model_path = ''
        self.prompt = prompt
        self.messages = old_content.copy()
        self.api_key = SPARK_MODEL_API_KEY
        self.api_secret = SPARK_MODEL_API_SECRET
        if self.prompt:
            self.messages.append({'role': 'system', 'content': self.prompt})

    @staticmethod
    def check_and_update_message(messages):
        """
        检查消息列表中是否存在角色为 'user' 的消息，
        如果没有，则将最后一个 'system' 消息的角色改为 'user'。

        :param messages: 消息列表
        :return: 修改后的消息列表
        """
        # 检查是否有角色为 'user' 的消息
        has_user_message = any(message.get("role") == "user" for message in messages)

        if not has_user_message:
            # 没有 'user' 消息时，将最后一个 'system' 改为 'user'
            for message in reversed(messages):
                if message.get("role") == "system":
                    message["role"] = "user"
                    break

        return messages

    def assemble_request_url(self, request_url):
        """
        根据传入的 URL 生成带鉴权的 WebSocket 请求 URL
        """
        try:
            # 转换为 HTTP URL
            http_request_url = request_url.replace("ws://", "http://").replace("wss://", "https://")

            parsed_url = urlparse(http_request_url)
            host = parsed_url.hostname
            path = parsed_url.path

            logger.info("=" * 35)
            logger.info(f"request_url: {request_url}, http_request_url: {http_request_url}, host: {host}, path: {path}")
            # 'Fri, 06 Dec 2024 06:04:50 GMT'
            date = datetime.datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
            signature_raw = f"host: {host}\ndate: {date}\nGET {path} HTTP/1.1"

            mac = hmac.new(self.api_secret.encode(), signature_raw.encode(), hashlib.sha256)
            signature_base64 = base64.b64encode(mac.digest()).decode()

            authorization_origin = (
                f"hmac api_key=\"{self.api_key}\", algorithm=\"hmac-sha256\", "
                f"headers=\"host date request-line\", signature=\"{signature_base64}\""
            )

            authorization_base64 = base64.b64encode(authorization_origin.encode()).decode()

            final_url = (
                f"{request_url}?authorization={quote(authorization_base64)}"
                f"&host={quote(host)}&date={quote(date)}"
            )
            return final_url
        except Exception as e:
            raise RuntimeError(f"assemble requestUrl error: {e}")

    def receive(self, messages, model_path='', is_stream=True):
        """
        调用大模型接口，支持流式和非流式模式
        """
        msg = self.messages.copy()
        msg.extend(messages)
        msg = self.check_and_update_message(msg)
        ws_url = self.assemble_request_url(self.model_url)

        # 非流式模式的结果存储
        non_stream_results = []
        stream_results = []

        def on_open(ws):
            logger.info("WebSocket connection opened.")
            # 构造 API 请求数据
            if not IS_SPARK_SAAS_MODEL:
                api_request = {
                    "header": {"traceId": f"{SPARK_MODEL_TRACE_ID}"},
                    "payload": {"message": {"text": msg}},
                }
            else:
                api_request = {
                    "header": {"app_id": f"{SPARK_SAAS_MODEL_APPID}"},
                    "parameter": {
                        "chat": {
                            # "domain": "generalv3.5",
                            "domain": "4.0Ultra",
                            # "temperature": 0.5,
                            # "max_tokens": 1024,
                        }
                    },
                    "payload": {"message": {"text": msg}},
                }
            ws.send(json.dumps(api_request))

        def on_message(ws, message):
            response = json.loads(message)
            # delta_content = response.get("payload", {}).get("choices", [{}])[0].get("content", "")
            delta_content = response.get("payload", {}).get("choices", {}).get("text", [{}])[0].get("content", "")

            if is_stream:
                # 流式模式直接 yield 返回消息
                logger.info(f"Message received (stream): {delta_content}")
                stream_results.append(delta_content)
                # yield delta_content
            else:
                # 非流式模式，收集消息
                logger.info(f"Message received (non-stream): {delta_content}")
                non_stream_results.append(delta_content)

            # 检查是否结束
            if response.get("header", {}).get("status", 2) == 2:
                ws.close()

        def on_close(ws, close_status_code, close_msg):
            logger.info(f"WebSocket connection closed: {close_status_code}, {close_msg}")

        def on_error(ws, error):
            logger.info(f"WebSocket error: {error}")
            raise RuntimeError(f"WebSocket error: {error}")

        # 创建 WebSocket 连接
        ws = websocket.WebSocketApp(
            ws_url,
            on_open=on_open,
            on_message=on_message,
            on_close=on_close,
            on_error=on_error,
        )

        # 运行 WebSocket 客户端
        ws.run_forever()

        if is_stream:
            # 流式模式
            def stream_generator():
                for result in stream_results:
                    yield json.dumps({"message": result, "usage": ""}).encode()

            return stream_generator()

        # 非流式模式，等待所有消息接收完成并合并结果
        full_result = "".join(non_stream_results).strip()
        return json.dumps({"data": full_result, "usage": ""}, ensure_ascii=False)


# 配置建造者类
class ModelConfigBuilder:
    def __init__(self):
        self.config_dict = {}

    def set_param(self, param):
        self.config_dict.update(param)
        return self

    def build(self):
        return self.config_dict
    


class ModelFactory:
    model_classes = {
        WENXIN_CODE: MyWenXin,
        CHATGPT_CODE: MyChatGPT,
        ASCEND_910B_CODE: Ascend_910B,
        CHATGLM_CODE: MyLocalModel,
        LLAMA_CODE: MyLocalModel,
    }
    
    view_classes = {
        BING_VIEW_ID: MyBing,
        AI_NUMBER_VIEW_ID: MyAINumber,
        MATERIAL_VIEW_ID: MyMaterialRecommend,
        CASE_VIEW_ID: MyRadarRecommend,
        IMG_TO_RECORD_ID: MyImgReader,
    }
    

    @staticmethod
    def get_chat_sdk_by_model_code(model_code, config_builder):
        model_class = ModelFactory.model_classes.get(model_code) or AllInOneModel
        config = config_builder.build()
        return model_class(**config)
    
    
    @staticmethod
    def get_chat_sdk_by_view_id(view_id, config_builder):
        view_class = ModelFactory.view_classes.get(view_id)
        if not view_class:
            raise ValueError(f"Unsupported view id: {view_id}")
        config = config_builder.build()
        return view_class(**config)

if __name__ == '__main__':
    # _chat = AiChatGpt('wwf556ee1bcfa5d9d6')
    # for item in _chat.receive([{"role": "user", "content": "flask是什么"}], 'gpt-3.5-turbo', is_stream=True).iter_lines():
    #     item = json.loads(item.decode()) if item else ''
    # res = _chat.receive([{"role": "user", "content": "flask是什么"}], 'gpt-3.5-turbo', is_stream=False)
    # content = """
    #     你是一个生成提示词专家，请按照下面的格式根据：我先制作一个股票方面的智能应用，内容生成如下格式的模版提示词,
    #     请注意一下关键点：\n
    #
    #     1.角色的制定一定要描述成一个某方面的专家或者是资深研究员\n
    #     2.技能可以是一个或者是多个,技能可以分类型，每个类型可以分成多个方面，技能描述的要和提供的内容密切相关\n
    #     3.限制条件是一个或者多个,仅回答内容相关的问题，不要胡言乱语\n
    #     以下是模版\n
    #     # 角色
    #
    #     ## 技能
    #     ### 技能:
    #     1.
    #     2.
    #
    #     ## 限制
    #     -
    #
    # """
    # rep = _chat.receive([{"role": "system", "content": content}], 'gpt-4', is_stream=False)
    # c = MyLocalModel(cfg='aigc_chatglm')
    # import warnings
    #
    # warnings.filterwarnings("ignore")

    # c = MyLlama()
    # _m = '查询裂变过程'
    #
    # c.test_ai_number(_m)
    chat_model = AllInOneModel(app_key="EMPTY",app_secret="",model="jusure-llm", model_url="http://114.242.210.44:6300/v1/",prompt="" )
    iter = chat_model.receive("你好", is_stream=True)

    for item in iter:
        print(item)
    pass
