from abc import ABC, abstractmethod

from dataclasses import dataclass, asdict

from typing import List

from settings import RANK_MODEL_URL

import requests

@dataclass
class RerankResult:
    index: int
    score: float
class BaseReranker:
    def __init__(self):
        pass

    @abstractmethod
    def rerank(self, query, messages):
        """
        Rerank the messages based on the query.
        :param query: The query string.
        :param texts: The list of messages to be ranked.
        :return: A list of ranked messages.
        """
        pass
    def rerank(self, query, messages) -> List[RerankResult]:
        
        pass
        mock_res = [
            { "index" : 0, "score" : 0.9},
            { "index" : 1, "score" : 0.9},
            { "index" : 2, "score" : 0.8},
            { "index" : 3, "score" : 0.7},
            { "index" : 4, "score" : 0.6},
            { "index" : 5, "score" : 0.5},
            { "index" : 6, "score" : 0.4},
            { "index" : 7, "score" : 0.3},
            { "index" : 8, "score" : 0.2},
            { "index" : 9, "score" : 0.1},
            { "index" : 10, "score" : 0.0},
            { "index" : 11, "score" : -0.1},
            { "index" : 12, "score" : -0.2},
            { "index" : 13, "score" : -0.3},
            { "index" : 14, "score" : -0.4},
            { "index" : 15, "score" : -0.5},
            { "index" : 16, "score" : -0.6},
            { "index" : 17, "score" : -0.7},
            { "index" : 18, "score" : -0.8},
            { "index" : 19, "score" : -0.9},
        ]
        return mock_res
    
        
class ZSReranker(BaseReranker):
    def __init__(self):
        super().__init__()

    def __transform_messages(self, messages):
        new_messages = []
        for idx, item in enumerate(messages):
            new_messages.append(item + f"_{idx}")
        return new_messages
    def rerank(self, query, messages):
        body_data = {
            "question": query,
            "answers_list": self.__transform_messages(messages),
        }
        try:
          response = requests.post(RANK_MODEL_URL, json=body_data, headers={"Content-Type": "application/json"})
          response.raise_for_status()
          if response.status_code == 200:
              response_data = response.json()
              ranked_answers = response_data.get("ranked_answers", [])
              result = []
              for idx, item in enumerate(ranked_answers):
                  result.append(asdict(RerankResult(index=int(item[0].split('_')[-1]), score=item[1])))
              return result
          else:
              print(f"Request failed with status code {response.status_code}")
              return list()
        except requests.RequestException as e:
          print(f"Request failed: {e}")
          raise
        
reranker = ZSReranker()

if __name__ == '__main__':
    zs_reranker = ZSReranker()
    query = "你是谁"
    messages = ["我是谁", "你是谁", "他是谁"]
    result = zs_reranker.rerank(query, messages)
    print(result)