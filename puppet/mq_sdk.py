import pika
from settings import RABBITMQ, AIGC_RABBITMQ
from lib_func.logger import logger

# 消费者
class RabbitMQConsumer:
    def __init__(self, **kwargs):
        self.host = kwargs.get("hostname")  # 主机
        self.port = kwargs.get("port")  # 端口
        self.username = kwargs.get("username")  # 用户名
        self.password = kwargs.get("password")  # 密码
        self.vhost = kwargs.get("vhost")  # 虚拟主机，VirtualHost之间相互隔离
        self.exchange = kwargs.get("exchange")  # 交换机
        self.queue = kwargs.get("queue")  # 队列
        self.routing_key = kwargs.get("routing_key")  # 交换机和队列的绑定
    
    def start(self, _callback):
        # 构造登录参数
        credentials = pika.PlainCredentials(self.username, self.password)
        parameters = pika.ConnectionParameters(self.host, self.port, self.vhost, credentials)
        
        connection = pika.BlockingConnection(parameters)
        channel = connection.channel()
        # 声明交换机
        channel.exchange_declare(exchange=self.exchange, durable=True)
        
        # 消费者实例
        channel.queue_declare(queue=self.queue, durable=True)
        # 绑定队列
        channel.queue_bind(exchange=self.exchange, queue=self.queue, routing_key=self.routing_key)
        # 表明最大阻塞未ack的消息数量
        channel.basic_qos(prefetch_count=1)
        channel.basic_consume(on_message_callback=_callback, queue=self.queue, auto_ack=False)
        
        channel.start_consuming()


"""
MONGO队列消息的数据结构
[table_name, write_type, documents]
插入数据  write_type = 'insert'   documents = [要插入的文档列表]
更新数据  write_type = 'update'   documents = [查询条件, 要修改的文档内容]

AIGC队列消息的数据结构
[tp_user_id, task_id, message, batch_count, batch_size]
用户id， 任务id， 描述内容， 批次数量， 每批数量
"""


# 生产者
class RabbitMQProducer:
    
    def __init__(self, cfg='mongo'):
        self.host = RABBITMQ.get("host")  # 主机
        self.port = RABBITMQ.get("port")  # 端口
        self.username = RABBITMQ.get("user")  # 用户名
        self.password = RABBITMQ.get("password")  # 密码
        self.vhost = RABBITMQ.get("v-host")  # 虚拟主机，VirtualHost之间相互隔离
        self.exchange = RABBITMQ.get("exchange").get(cfg)  # 交换机
        self.queue = RABBITMQ.get("queue").get(cfg)  # 队列
        self.routing_key = RABBITMQ.get("routing_key").get(cfg)  # 交换机和队列的绑定
        
        self.connection = None
        self.channel = None
    
    def __init(self):
        try:
            # 关闭旧的连接
            if self.connection and not self.connection.is_closed:
                self.connection.close()
        except Exception as err:
            logger.error(err)
            pass
        # 构造登录参数
        credentials = pika.PlainCredentials(self.username, self.password)
        parameters = pika.ConnectionParameters(self.host, self.port, self.vhost, credentials)
        
        self.connection = pika.BlockingConnection(parameters)
        self.channel = self.connection.channel()
    
    def start(self, body):
        self.__init()
        # 声明交换机
        self.channel.exchange_declare(exchange=self.exchange, durable=True)
        # 队列实例
        self.channel.queue_declare(queue=self.queue, durable=True)
        # 绑定队列
        self.channel.queue_bind(exchange=self.exchange, queue=self.queue, routing_key=self.routing_key)
        # 消息持久化
        self.channel.basic_publish(exchange=self.exchange, routing_key=self.routing_key, body=body,
                                   properties=pika.BasicProperties(delivery_mode=2))
    
    def __del__(self):
        if self.connection and not self.connection.is_closed:
            self.connection.close()


# 生产者
class AiGcRabbitMQProducer:

    def __init__(self, cfg='aigc'):
        self.host = AIGC_RABBITMQ.get("host")  # 主机
        self.port = AIGC_RABBITMQ.get("port")  # 端口
        self.username = AIGC_RABBITMQ.get("user")  # 用户名
        self.password = AIGC_RABBITMQ.get("password")  # 密码
        self.vhost = AIGC_RABBITMQ.get("v-host")  # 虚拟主机，VirtualHost之间相互隔离
        self.exchange = AIGC_RABBITMQ.get("exchange").get(cfg)  # 交换机
        self.queue = AIGC_RABBITMQ.get("queue").get(cfg)  # 队列
        self.routing_key = AIGC_RABBITMQ.get("routing_key").get(cfg)  # 交换机和队列的绑定

        self.connection = None
        self.channel = None

    def __init(self):
        try:
            # 关闭旧的连接
            if self.connection and not self.connection.is_closed:
                self.connection.close()
        except Exception as err:
            logger.error(err)
            pass
        # 构造登录参数
        credentials = pika.PlainCredentials(self.username, self.password)
        parameters = pika.ConnectionParameters(self.host, self.port, self.vhost, credentials)

        self.connection = pika.BlockingConnection(parameters)
        self.channel = self.connection.channel()

    def start(self, body):
        self.__init()
        # 声明交换机
        self.channel.exchange_declare(exchange=self.exchange, durable=True)
        # 队列实例
        self.channel.queue_declare(queue=self.queue, durable=True)
        # 绑定队列
        self.channel.queue_bind(exchange=self.exchange, queue=self.queue, routing_key=self.routing_key)
        # 消息持久化
        self.channel.basic_publish(exchange=self.exchange, routing_key=self.routing_key, body=body,
                                   properties=pika.BasicProperties(delivery_mode=2))

    def __del__(self):
        if self.connection and not self.connection.is_closed:
            self.connection.close()

