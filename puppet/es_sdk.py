# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: es_sdk.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 4月 07, 2024
# ---
import json

from elasticsearch import Elasticsearch
from controller.manager_controller import MasterAction
from elasticsearch.helpers import bulk
from lib_func.logger import logger

class EsSdkPool(MasterAction):

    # def __init__(self, *args):
    #     super(EsSdkPool, self).__init__(*args)
    #     conf_map = self.get_es_conf()
    #     self.host = conf_map['host']
    #     self.port = conf_map['port']
    #     self.user = conf_map['user']
    #     self.password = conf_map['password']
    #     self.uri = conf_map.get('uri')
    #     self.db_name = conf_map['db']
    #     # self.es = Elasticsearch(self.uri)
    #     self.es = Elasticsearch([self.uri], basic_auth=(self.user, self.password))
    #     print('=========es ===========')

    _es_instance = None

    conf_map = {}

    # 引入IK分词器插件
    settings = {
        "analysis": {
            "analyzer": {
                "ik_max": {
                    "type": "custom",
                    "tokenizer": "ik_max_word"
                }
            }
        }
    }

    @classmethod
    def get_es_instance(cls):
        if cls._es_instance is None:
            logger.info('----------=========es instance===========----------')
            cls._es_instance = cls._initialize_es()
        return cls._es_instance

    @classmethod
    def _initialize_es(cls):

        conf_map = cls.conf_map

        # 获取配置项并设置默认值
        host = conf_map.get('host', 'localhost')
        port = conf_map.get('port', 9200)
        user = conf_map.get('user', '')
        password = conf_map.get('password', '')
        uri = conf_map.get('uri', f"http://{host}:{port}")
        db_name = conf_map.get('db', '')

        try:
            # 确保关键配置项存在
            if not all([host, port, user, password, db_name]):
                raise ValueError("Missing required configuration items")

            # 使用 HTTPS 进行连接，并启用连接池
            es = Elasticsearch(
                [uri],
                http_auth=(user, password),
                maxsize=10  # 设置连接池大小
            )
            return es
        except KeyError as e:
            logger.exception(f"Configuration error: {e}")
            raise
        except Exception as e:
            logger.exception(f"Failed to initialize Elasticsearch client: {e}")
            raise

    def __init__(self, *args):
        super(EsSdkPool, self).__init__(*args)
        EsSdkPool.conf_map = self.get_es_conf()
        self.es = EsSdkPool.get_es_instance()
        self.db_name = EsSdkPool.conf_map.get('db', '')

    def crate_index(self, index_name, mapping):
        index_name = self.db_name + '_' + index_name
        if not self.es.indices.exists(index=index_name):
            self.es.indices.create(index=index_name, body={'mappings': mapping})
        return index_name

    def index_exists(self, index_name):
        index_name = self.db_name + '_' + index_name
        index_exists = self.es.indices.exists(index=index_name)
        # bool True or False
        logger.error(f"Index: {index_name} check exists: {index_exists}")
        return index_exists

    def insert_data(self, index_name, data, mapping={}, ik_set=False):
        index_name = self.db_name + '_' + index_name
        if not self.es.indices.exists(index=index_name):
            body = {'mappings': mapping}
            if ik_set:
                body['settings'] = self.settings
            self.es.indices.create(index=index_name, body=body)
        return self.es.index(index=index_name, body=data)
    
    def insert_data_batch(self, index_name, data_list, mapping={}):
        # 批量插入数据
        index_name = self.db_name + '_' + index_name
        # 检查索引是否存在，如果不存在则创建
        if not self.es.indices.exists(index=index_name):
            self.es.indices.create(index=index_name, body={'mappings': mapping})
        # 准备批量操作的数据
        actions = []
        for data in data_list:
            action = {
                "_index": index_name,
                "_source": data
            }
            actions.append(action)
        # 执行批量插入操作
        try:
            response = bulk(self.es, actions)
            return response
        except Exception as e:
            raise Exception(f"批量插入出错{str(e)}")

    def insert_bulk_data(self, index_name, data_list, mapping={}):
        index_name = self.db_name + '_' + index_name

        if not self.es.indices.exists(index=index_name):
            self.es.indices.create(index=index_name, body={'mappings': mapping})

        actions = []
        for doc in data_list:
            action = {
                "_op_type": "index",  # 执行插入操作
                "_index": index_name,
                "_source": doc
            }
            actions.append(action)

        success, failed = bulk(self.es, actions)
        return {"success": success, "failed": failed}

    # 查询文档
    def search_data(self, index_name, query_dict):
        if type(index_name) == list:
            index_name = [self.db_name + '_' + i for i in index_name]
        else:
            index_name = self.db_name + '_' + index_name
        res = self.es.search(index=index_name, body=query_dict)
        return res
    # 批量查询
    def batch_search_data(self, index_name, query_dict):
        index_name = self.db_name + '_' + index_name
        query_body = []
        for query in query_dict:
            query_body.append({"index": index_name})
            query_body.append(query)
        res = self.es.msearch(body=query_body)
        return res.body.get('responses', [])
    # 删除文档
    def delete_data(self, index_name, query_dict):
        index_name = self.db_name + '_' + index_name
        try:
            res = self.es.delete_by_query(index=index_name, body=query_dict)
            if not res['failures']:
                return True
            else:
                return False
        except Exception as e:
            logger.error(e)
            return False

    # 更新文档
    def update_data(self, index_name, update_query):
        index_name = self.db_name + '_' + index_name
        return self.es.update_by_query(index=index_name, body=update_query)

    # 删除索引
    def delete_index(self, index_name):
        index_name = self.db_name + '_' + index_name
        return self.es.indices.delete(index=index_name)

    def refresh_index(self, index_name):
        index_name = self.db_name + '_' + index_name
        return self.es.indices.refresh(index=index_name)

    def scroll(self, scroll_id, scroll):
        return self.es.scroll(scroll_id=scroll_id, scroll=scroll)

    def clear_scroll(self, scroll_id):
        return self.es.clear_scroll(scroll_id=scroll_id)

    def bulk_index(self, index_name, actions):
        try:
            index_name = self.db_name + '_' + index_name
            for action in actions:
                action["_index"] = index_name
            success, errors = bulk(self.es, actions, stats_only=True)
            return success, len(errors) if errors else 0
        except Exception as e:
            raise Exception(f"rise error: {str(e)}")

    def search_data_scroll(self, index_name, query_dict, scroll):
        if type(index_name) == list:
            index_name = [self.db_name + '_' + i for i in index_name]
        else:
            index_name = self.db_name + '_' + index_name
        res = self.es.search(index=index_name, body=query_dict, scroll=scroll)
        return res

    def count_data(self, index_name, query_dict):
        if isinstance(index_name, list):
            index_name = [self.db_name + '_' + i for i in index_name]
        else:
            index_name = self.db_name + '_' + index_name
        res = self.es.count(index=index_name, body=query_dict)
        return res

    def bulk(self, index_name, actions, raise_on_error=False, stats_only=False):
        try:
            index_name = self.db_name + '_' + index_name
            for action in actions:
                action["_index"] = index_name
            success, errors = bulk(self.es, actions, raise_on_error=raise_on_error, stats_only=stats_only)
            return success, len(errors) if errors else 0
        except Exception as e:
            raise Exception(f"bulk_insert error: {str(e)}")


    @classmethod
    def close_connection(cls):
        if cls._es_instance is not None:
            cls._es_instance.close()
            cls._es_instance = None


if __name__ == '__main__':
    es = EsSdkPool('wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ')
    # print(es.delete_index('content_graph_bge-large-zh-v1.5'))
    # index_name1 = 'content_embedding-v1'

    # print(json.dumps(body_dict))
    # delete_query = {
    #     "query": {
    #         "term": {
    #             "content_id": "1bdf5481-0a65-4e84-b415-27a2e35a058c"
    #         }
    #     }
    # }
    # res = es.delete_data('content_text-embedding-v1', delete_query)
    # print(res)

