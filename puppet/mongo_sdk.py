import threading
import pymongo
# from settings import MONGODB

from controller.manager_controller import MasterAction


class MongodbPool(MasterAction):
    """
    mongodb连接
    """

    _instance_lock = threading.Lock()
    __instance = None
    client = {}

    # 连接池实例化一次单例模式
    def __new__(cls, *args):
        if MongodbPool.__instance is None:
            with cls._instance_lock:
                __instance = object.__new__(cls)
                MongodbPool.__instance = __instance
        return MongodbPool.__instance

    def __init__(self, *args, conf_map=None, corpid=None):
        super(MongodbPool, self).__init__(*args)
        conf_map = self.get_mongo_conf()
        # if not conf_map:
        #     conf_map = Conf[corpid]
        self.host = conf_map['host']
        self.port = conf_map['port']
        self.user = conf_map['user']
        self.password = conf_map['password']
        self.uri = conf_map.get('uri')
        self.db_name = conf_map['db']
        self.db = self.get_mongodb_pool()

    def get_mongodb_pool(self, max_pool_size=10):
        """
        获取连接池
        :return:
        """
        cache_key = self.uri or '{}_{}_{}_{}'.format(self.host, self.port, self.user, self.db_name)
        if self.client.get(cache_key) is None:
            if self.uri:
                self.client[cache_key] = pymongo.MongoClient(self.uri, maxPoolSize=max_pool_size, maxIdleTimeMS=60000)
            else:
                self.client[cache_key] = pymongo.MongoClient(host=self.host, port=self.port, username=self.user,
                                                             password=self.password, maxPoolSize=max_pool_size,
                                                             maxIdleTimeMS=60000)
        return self.client[cache_key][self.db_name]

    def insert_data_one(self, table_name, insert_data=None):
        """
        :
        :param table_name:
        :param insert_data:
        :return:
        """
        result = self.db[table_name].insert_one(insert_data)
        return result.inserted_id

    def insert_data_many(self, table_name: str, documents: list) -> str:
        try:
    # 批量插入文档
            result = self.db[table_name].insert_many(documents)
            # 打印插入的文档的 _id
            return 1, "插入的文档 _id 列表:" + str(result.inserted_ids)
        except Exception as e:
            return 0, f"插入文档时发生错误: {e}"


    def update_data_one(self, table_name, where, update):

        self.db[table_name].update_one(where, {"$set": update}, True)

    def query_one(self, table_name, where):
        return self.db[table_name].find_one(where)
    
    def query_by_field_distinct_count(self, table_name, field_name, query_dict):
        # example： self.db[table_name].distinct("app_id", {"create_time": {"$gte": today_start, "$lte": now}})
        return len(self.db[table_name].distinct(field_name, query_dict))
    
    def query_count(self, table_name, query_dict={}):
        return self.db[table_name].count_documents(query_dict)
    
    def query_aggregate_by_pipeline(self, table_name, pipeline=None):
        return self.db[table_name].aggregate(pipeline)
    

    # def query_one_custom(self, table_name, query, sort_criteria):
    #     """
    #     query = {}  # 如果没有其他查询条件，这里可以是一个空字典
    #     sort_criteria = [('createdAt', -1)]  # 按'createdAt'字段降序排列
    #     """
    #     # latest_document = self.db[table_name].find_one(query).sort(sort_criteria)
    #     # last_document = self.db[table_name].find(query).sort('timestamp', -1).limit(1)[0]
    #     last_document = self.db[table_name].find(query).sort(sort_criteria).limit(1)[0]
    #     return last_document
    
    def query_one_custom(self, table_name, query, sort_criteria=None):
        """
        query = {}  # 如果没有其他查询条件，这里可以是一个空字典
        sort_criteria = [('createdAt', -1)]  # 按'createdAt'字段降序排列
        """
        cursor = self.db[table_name].find(query)
        if sort_criteria:
            cursor = cursor.sort(sort_criteria)
        cursor = cursor.limit(1)

        # 将 cursor 转换为列表并检查是否为空
        result_list = list(cursor)
        if result_list:
            return result_list[0]
        else:
            return None


    def create_index(self, table_name, index_fields):
        """
        @summary: 创建索引
        table_name: 表名
        index_fields: 格式 [('field1', 1), ('field2', -1)]
        “”“"""
        self.db[table_name].create_index(index_fields)
        return self.db[table_name].index_information()
    def close(self):
        for client in self.client.values():
            client.close()

    def __del__(self):
        for client in self.client.values():
            client.close()
