# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: qianfan.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 3月 11, 2024
# ---
import json
import redis
import requests
from puppet.cache import redis_pool
from horticulture.request_retry import retry_request
from lib_func.exception import BaiduApiError
import aiohttp


class QianFanModel(object):

    def __init__(self, qianfan={}, prompt=''):
        self.qianfan = qianfan
        self.prompt = prompt

    def get_access_token(self):
        token_key = self.qianfan['Api_Key'] + "^access_token"
        redis_client = redis.StrictRedis(connection_pool=redis_pool.dict_pool)
        access_token = redis_client.get(token_key)
        if access_token:
            return access_token
        url = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={}&client_secret={}".format(
            self.qianfan['Api_Key'], self.qianfan['Secret_Key'])

        payload = ""
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)

        res_json = response.json()
        if res_json.get('error'):
            raise ValueError('获取token失败')
        else:
            redis_client.set(token_key, res_json['access_token'], ex=86400 * 29)
            # print(res_json['access_token'])
            return res_json['access_token']

    def qianfan_chat_stream(self, messages, api_path='completions_pro'):

        url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/{}?access_token={}".format(api_path,
                                                                                                            self.get_access_token())

        payload = json.dumps({
            "messages": messages,
            "stream": True,
            "system": self.prompt
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload, stream=True, verify=False)

        return response

    @retry_request(tries=3, jitter=2, throw_exception=True)
    def create_embeddings(self, input_text, user_id, model_path='embedding-v1'):
        url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/embeddings/{}?access_token={}".format(model_path,
                                                                                                                  self.get_access_token())
        payload = json.dumps({
            "input": input_text,
            "user_id": user_id
        })
        headers = {
            'Content-Type': 'application/json',
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        rep_data = response.json()
        if rep_data.get('error_code'):
            raise BaiduApiError(rep_data['error_msg'])
        return rep_data

    async def async_create_embeddings(self, input_text, user_id, model_path='embedding-v1'):
        url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/embeddings/{}?access_token={}".format(model_path,
                                                                                                                  self.get_access_token())
        payload = json.dumps({
            "input": input_text,
            "user_id": user_id
        })
        headers = {
            'Content-Type': 'application/json',
        }
        async with aiohttp.ClientSession() as session:
                async with session.post(url=url, headers=headers, data=payload) as resp:
                    rep_data = await resp.json()
        # response = requests.request("POST", url, headers=headers, data=payload)
        # rep_data = response.json()
        if rep_data.get('error_code'):
            raise BaiduApiError(rep_data['error_msg'])
        return rep_data

if __name__ == "__main__":
    # redis_pool.init_app()
    qf = QianFanModel('60200588')
    for i in range(20):
        print(qf.create_embeddings(['你好'], '123'))
    # print(qf.get_access_token())
    # completions = qf.qianfan_chat_stream([
    #     {
    #         "role": "user",
    #         "content": "给我推荐一些自驾游路线"
    #     }
    # ])
    #
    # for line in completions.iter_lines():
    #     # print(line.decode("UTF-8"))
    #     # print('===')
    #     if line.decode("UTF-8"):
    #         # print(line.decode("UTF-8").replace("data: ", ''))
    #         # print(json.loads(line.decode("UTF-8").replace("data: ", '')))
    #         line_dict = json.loads(line.decode("UTF-8").replace("data: ", ''))
    #         print(line_dict['result'])
