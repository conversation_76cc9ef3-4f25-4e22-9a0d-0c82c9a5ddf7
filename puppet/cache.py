import json
import redis
from horticulture.record import redis_error_log
from settings import INS_TOKEN_REDIS, INS_ROLE_REDIS, INS_DICT_REDIS, INS_CLIENT_REDIS, INS_AIGC_REDIS, INS_LLM_REDIS
from lib_func.logger import logger

class CacheAction:
    
    def __init__(self):
        self.token_pool = redis.ConnectionPool(
            host=INS_TOKEN_REDIS['host'],
            port=INS_TOKEN_REDIS['port'],
            db=INS_TOKEN_REDIS['db'],
            password=INS_TOKEN_REDIS['password'],
            decode_responses=True
        )
        self.role_pool = redis.ConnectionPool(
            host=INS_ROLE_REDIS['host'],
            port=INS_ROLE_REDIS['port'],
            db=INS_ROLE_REDIS['db'],
            password=INS_ROLE_REDIS['password'],
            decode_responses=True
        )
        self.dict_pool = redis.ConnectionPool(
            host=INS_DICT_REDIS['host'],
            port=INS_DICT_REDIS['port'],
            db=INS_DICT_REDIS['db'],
            password=INS_DICT_REDIS['password'],
            decode_responses=True
        )
        self.client_pool = redis.ConnectionPool(
            host=INS_CLIENT_REDIS['host'],
            port=INS_CLIENT_REDIS['port'],
            db=INS_CLIENT_REDIS['db'],
            password=INS_CLIENT_REDIS['password'],
            decode_responses=True
        )
        self.aigc_pool = redis.ConnectionPool(
            host=INS_AIGC_REDIS['host'],
            port=INS_AIGC_REDIS['port'],
            db=INS_AIGC_REDIS['db'],
            password=INS_AIGC_REDIS['password'],
            decode_responses=True
        )

        self.llm_pool = redis.ConnectionPool(
            host=INS_LLM_REDIS['host'],
            port=INS_LLM_REDIS['port'],
            db=INS_LLM_REDIS['db'],
            password=INS_LLM_REDIS['password'],
            decode_responses=True
        )
    
    def use(self, use_key=None):
        redis_client = redis.StrictRedis(connection_pool=use_key or self.dict_pool)
        return redis_client
    
    @redis_error_log()
    def set_comment_data(self, _key, _value, expire_time=3600, _redis=None):
        self.use(_redis).set(_key, _value, ex=expire_time)
        return True
    
    @redis_error_log(True)
    def set_secret_data(self, _key, _value, expire_time=3600, _redis=None):
        self.use(_redis).set(_key, _value, ex=expire_time)
        return True
    
    @redis_error_log()
    def del_set_data(self, _key, _redis=None):
        self.use(_redis).delete(_key)
        return True
    
    @redis_error_log()
    def get_comment_data(self, _key, _redis=None):
        return self.use(_redis).get(_key)
    
    @redis_error_log(True)
    def get_secret_data(self, _key, _redis=None):
        return self.use(_redis).get(_key)
    
    @redis_error_log()
    def hset_comment_data(self, name, _key, _value, _redis=None):
        try:
            _value = json.dumps(_value)
        except:
            pass
        self.use(_redis).hset(name, _key, _value)
        return True
    
    @redis_error_log(True)
    def hset_secret_data(self, name, _key, _value, _redis=None):
        try:
            _value = json.dumps(_value)
        except:
            pass
        self.use(_redis).hset(name, _key, _value)
        return True
    
    @redis_error_log()
    def set_expire(self, name, time, _redis=None):
        self.use(_redis).expire(name, time)
        return True
    
    @redis_error_log()
    def hget_comment_data(self, name, _key, _redis=None):
        data = self.use(_redis).hget(name, _key)
        try:
            if data:
                data = json.loads(data)
        except Exception as err:
            logger.error(err)
        return data
    
    @redis_error_log(True)
    def hget_secret_data(self, name, _key, _redis=None):
        data = self.use(_redis).hget(name, _key)
        try:
            if data:
                data = json.loads(data)
        except Exception as err:
            logger.error(err)
        return data
    
    @redis_error_log(True)
    def hdel_secret_data(self, name, _key, _redis=None):
        self.use(_redis).hdel(name, _key)
        return True
    
    @redis_error_log(True)
    def __lock__(self, _key, _value, ex=10, _redis=None):
        return self.use(_redis).set(_key, _value, ex=ex, nx=True)
    
    @redis_error_log(True)
    def __unLock__(self, _key, _value, _redis=None):
        if self.use(_redis).get(_key) == _value:
            redis.del_set_data(_key)


redis_pool = CacheAction()
