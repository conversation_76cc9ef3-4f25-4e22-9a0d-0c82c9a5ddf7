# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: graph_server_sdk.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 10月 18, 2024
# ---
import uuid

import requests
from settings import GRAPH_SERVER_URL


class GraphServerSdk(object):

    def __init__(self):

        self.graph_server_url = GRAPH_SERVER_URL
        self.uuid = uuid.uuid4().hex

    def graph_to_description(self, graph_url):

        params = {
            "url": graph_url,
            "id": self.uuid,
            "ref_text": "",
            "method": 2,
        }

        # 发送 GET 请求
        response = requests.get(self.graph_server_url, params=params)  # 替换为后端服务器地址

        # 处理响应
        if response.status_code == 200:
            res_json = response.json()
            return res_json.get('text', '')
        else:
            return ''


if __name__ == '__main__':
    # graph_url = "https://zhongshu-test.oss-cn-beijing.aliyuncs.com/11231312123312311211/35.jpg"
    graph_server_sdk = GraphServerSdk()
    oss_urls = [
        'https://zhongshu-test.oss-cn-beijing.aliyuncs.com/68a6a43d-1671-11ef-a407-0242ac110002/WechatIMG394.jpeg',
        'https://zhongshu-test.oss-cn-beijing.aliyuncs.com/68a6a43d-1671-11ef-a407-0242ac110002/%E6%84%9F%E8%B0%A2%E4%BF%A1.jpeg',
        'https://zhongshu-test.oss-cn-beijing.aliyuncs.com/68a6a43d-1671-11ef-a407-0242ac110002/%E5%85%AC%E4%BC%97%E5%8F%B7%E6%96%87%E7%AB%A0.png'
        'https://zhongshu-test.oss-cn-beijing.aliyuncs.com/b9f7abf6-9fa8-11ee-86f6-00163e12ca7c/2-1%20(1).png',
        'https://zhongshu-test.oss-cn-beijing.aliyuncs.com/68a6a43d-1671-11ef-a407-0242ac110002/%E5%AE%9E%E6%96%BD%E7%85%A7%E7%89%87-%E6%96%AF%E5%9D%A6%E6%B2%83%E5%85%8B(%E9%BB%91%E9%BE%99%E6%B1%9F)%E5%B7%A5%E7%A8%8B%E7%AE%A1%E7%90%86%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8-%E5%AE%9E%E6%96%BD%E9%83%A8-%E9%82%93%E5%86%9B%EF%BC%881%EF%BC%89.jpg',
        'https://zhongshu-test.oss-cn-beijing.aliyuncs.com/68a6a43d-1671-11ef-a407-0242ac110002/%E5%AE%9E%E6%96%BD%E7%85%A7%E7%89%87-%E6%96%AF%E5%9D%A6%E6%B2%83%E5%85%8B(%E9%BB%91%E9%BE%99%E6%B1%9F)%E5%B7%A5%E7%A8%8B%E7%AE%A1%E7%90%86%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8-%E5%AE%9E%E6%96%BD%E9%83%A8-%E9%82%93%E5%86%9B%EF%BC%882%EF%BC%89.jpg',
        'https://zhongshu-test.oss-cn-beijing.aliyuncs.com/68a6a43d-1671-11ef-a407-0242ac110002/%E5%AE%9E%E6%96%BD%E7%85%A7%E7%89%87-%E6%96%AF%E5%9D%A6%E6%B2%83%E5%85%8B(%E9%BB%91%E9%BE%99%E6%B1%9F)%E5%B7%A5%E7%A8%8B%E7%AE%A1%E7%90%86%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8-%E5%AE%9E%E6%96%BD%E9%83%A8-%E9%82%93%E5%86%9B%EF%BC%883%EF%BC%89.jpg'
        'https://zhongshu-test.oss-cn-beijing.aliyuncs.com/b9f7abf6-9fa8-11ee-86f6-00163e12ca7c/2-1%20(1).png',
        'https://zhongshu-test.oss-cn-beijing.aliyuncs.com/adc900e4-5d19-49d3-bfcf-25fd10e863c6/%E6%84%9F%E8%B0%A2%E4%BF%A1-%E6%B2%B3%E5%8C%97%E8%9E%8D%E6%B6%A6%E8%BE%BE%E5%BB%BA%E6%9D%90%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8-%E5%A4%A9%E7%AD%96%E7%B3%BB%E5%88%97%EF%BC%88%E5%A4%A9%E6%97%B6%E7%B3%BB%E5%88%97%EF%BC%89.jpg',
        'https://zhongshu-test.oss-cn-beijing.aliyuncs.com/68a6a43d-1671-11ef-a407-0242ac110002/qiko-default-avatar.png',
        'https://zhongshu-test.oss-cn-beijing.aliyuncs.com/68a6a43d-1671-11ef-a407-0242ac110002/43031727691532_.pic_1727692390143.jpg'
    ]
    index = 1
    for url in oss_urls:
        print(index, graph_server_sdk.graph_to_description(url))
        index += 1
