import logging
import time
import json
import os
import platform
import inspect
from typing import Any, Dict, Optional, List, Union
from logging.handlers import TimedRotatingFileHandler
from flask import g, has_request_context
from functools import lru_cache

if platform.system() != 'Windows':
    import fcntl

# 配置常量
BASE_DIR = os.environ.get('LogPath', os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
LOG_PATH = os.path.join(BASE_DIR, 'logs/')
LOG_FORMAT = '[%(ip)s][%(asctime)s] %(levelname)s %(method)s:%(path)s - %(filename)s:%(lineno)d  %(message)s'
LOG_BACKUP_COUNT = 7
LOG_ENCODING = 'utf-8'


def ensure_log_path():
    """确保日志目录存在"""
    os.makedirs(LOG_PATH, exist_ok=True)

class MultiCompatibleTimedRotatingFileHandler(TimedRotatingFileHandler):
    """支持多进程的日志轮转处理器"""
    
    def doRollover(self):
        """
        do a rollover; in this case, a date/time stamp is appended to the filename
        when the rollover happens.  However, you want the file to be named for the
        start of the interval, not the current time.  If there is a backup count,
        then we have to get a list of matching filenames, sort them and remove
        the one with the oldest suffix.
        """
        if self.stream:
            self.stream.close()
            self.stream = None
            
        current_time = int(time.time())
        dst_now = time.localtime(current_time)[-1]
        
        # 计算轮转时间
        rotation_ts = self.rolloverAt - self.interval
        time_tuple = time.gmtime(rotation_ts) if self.utc else time.localtime(rotation_ts)
        
        # 生成新文件名
        dfn = self.rotation_filename(
            f"{self.baseFilename}.{time.strftime(self.suffix, time_tuple)}"
        )
        
        # 处理文件轮转
        if platform.system() != 'Windows':
            self._handle_rollover_linux(dfn)
        else:
            self._handle_rollover_windows(dfn)
            
        # 清理旧文件
        if self.backupCount > 0:
            for s in self.getFilesToDelete():
                os.remove(s)
                
        if not self.delay:
            self.stream = self._open()
            
        # 计算下一次轮转时间
        new_rollover_at = self.computeRollover(current_time)
        while new_rollover_at <= current_time:
            new_rollover_at = new_rollover_at + self.interval

        # 如果DST（夏令时）发生变化，则进行调整
        if dst_now != time.localtime(new_rollover_at)[-1]:
            if not self.utc:
                addend = -3600 if dst_now else 3600
                new_rollover_at += addend
            
        self.rolloverAt = new_rollover_at
    
    def _handle_rollover_linux(self, dfn: str) -> None:
        """Linux系统下的文件轮转处理"""
        if not os.path.exists(dfn):
            with open(self.baseFilename, 'a') as f:
                fcntl.lockf(f.fileno(), fcntl.LOCK_EX)
                if not os.path.exists(dfn):
                    os.rename(self.baseFilename, dfn)

    def _handle_rollover_windows(self, dfn: str) -> None:
        """Windows系统下的文件轮转处理"""
        if not os.path.exists(dfn) and os.path.exists(self.baseFilename):
            self.rotate(self.baseFilename, dfn)

class FunctionNameFormatter(logging.Formatter):
    """增强的日志格式化器，支持函数名、IP地址、请求URL和请求方法"""
    
    def format(self, record: logging.LogRecord) -> str:
        # 获取调用函数名
        for frame_info in inspect.stack():
            if 'logging' not in frame_info.frame.f_code.co_filename:
                record.funcName = frame_info.frame.f_code.co_name
                break
                
        # 获取IP地址、URL和请求方法
        record.ip = self._get_client_ip()
        record.path = self._get_request_path()
        record.method = self._get_request_method()
        
        # 如果是错误日志，添加堆栈跟踪
        if record.levelno >= logging.ERROR and hasattr(record, 'exc_info') and record.exc_info:
            if not record.exc_text:
                record.exc_text = self.formatException(record.exc_info)
                
        return super().format(record)
    
    @staticmethod
    def _get_client_ip() -> str:
        """获取客户端IP"""
        if has_request_context() and hasattr(g, 'client_ip'):
            return g.client_ip
        return '未解析到ip'
    
    @staticmethod
    def _get_request_path() -> str:
        """获取请求path"""
        if has_request_context():
            from flask import request
            return request.path
        return '非请求上下文'
    
    @staticmethod
    def _get_request_method() -> str:
        """获取请求方法"""
        if has_request_context():
            from flask import request
            return request.method
        return 'NO_METHOD'

class LoggerManager:
    """日志管理器"""
    
    _instances: Dict[str, logging.Logger] = {}
    
    @classmethod
    @lru_cache(maxsize=None)
    def get_logger(cls, level: int, log_name: str) -> logging.Logger:
        """获取或创建logger实例"""
        if log_name not in cls._instances:
            cls._instances[log_name] = cls._create_logger(level, log_name)
        return cls._instances[log_name]
    
    @classmethod
    def _create_logger(cls, level: int, log_name: str) -> logging.Logger:
        """创建新的logger实例"""
        ensure_log_path()
        
        # 获取logger实例
        logger = logging.getLogger(log_name)
        logger.setLevel(level)
        
        # 清除现有的处理器
        logger.handlers.clear()
        
        # 禁用日志传播到根logger
        logger.propagate = False
        
        # 添加处理器
        handlers = [
            cls._create_file_handler(f"{log_name}_info.log", logging.DEBUG),
            cls._create_file_handler(f"{log_name}_error.log", logging.ERROR),
            cls._create_console_handler(level)
        ]
        
        for handler in handlers:
            logger.addHandler(handler)
            
        return logger
    
    @staticmethod
    def _create_file_handler(filename: str, level: int) -> MultiCompatibleTimedRotatingFileHandler:
        """创建文件处理器"""
        handler = MultiCompatibleTimedRotatingFileHandler(
            os.path.join(LOG_PATH, filename),
            when='MIDNIGHT',
            interval=1,
            backupCount=LOG_BACKUP_COUNT,
            encoding=LOG_ENCODING
        )
        handler.setFormatter(FunctionNameFormatter(LOG_FORMAT))
        handler.setLevel(level)
        return handler
    
    @staticmethod
    def _create_console_handler(level: int) -> logging.StreamHandler:
        """创建控制台处理器"""
        handler = logging.StreamHandler()
        handler.setFormatter(FunctionNameFormatter(LOG_FORMAT))
        handler.setLevel(level)
        return handler

def log_message(level: int, msg: Any, log_name: str, exc_info: bool = None) -> None:
    """统一的日志记录函数"""
    logger = LoggerManager.get_logger(level, log_name)
    
    # 如果消息是字典且包含大字段，进行截断处理
    if isinstance(msg, dict) and 'resp' in msg:
        resp = msg['resp']
        if isinstance(resp, (dict, list)) and len(str(resp)) > 1000:
            msg['resp'] = f"<Truncated data, size={len(str(resp))}>"
    
    message = {
        "content": msg,
        "time": time.strftime("%Y-%m-%d %H:%M:%S"),
        "level": logging.getLevelName(level).lower()
    }
    
    # 对于错误日志，添加堆栈跟踪
    if level >= logging.ERROR:
        exc_info = True if exc_info is None else exc_info
    else:
        exc_info = False if exc_info is None else exc_info
        
    try:
        getattr(logger, logging.getLevelName(level).lower())(
            json.dumps(message, ensure_ascii=False),
            exc_info=exc_info
        )
    except OSError as e:
        # 如果仍然遇到消息过长错误，进一步截断内容
        if e.errno == 40:  # Message too long
            message['content'] = f"<Truncated due to size: {len(str(message['content']))}>"
            getattr(logger, logging.getLevelName(level).lower())(
                json.dumps(message, ensure_ascii=False),
                exc_info=exc_info
            )



# 导出便捷的日志函数
def debug(msg: Any, log_name: str) -> None:
    log_message(logging.DEBUG, msg, log_name)

def info(msg: Any, log_name: str) -> None:
    log_message(logging.INFO, msg, log_name)

def warning(msg: Any, log_name: str) -> None:
    log_message(logging.WARNING, msg, log_name)

def error(msg: Any, log_name: str, exc_info: bool = True) -> None:
    """
    记录错误日志，默认包含堆栈跟踪
    :param msg: 错误信息
    :param log_name: 日志名称
    :param exc_info: 是否包含堆栈跟踪，默认为True
    """
    log_message(logging.ERROR, msg, log_name, exc_info=exc_info)

# 初始化常用logger实例
def format_log_content(req_data, ret, path, method, time_long, escape_param_keys, escape_result_keys, header='', uid=0):
    # 记录日之前，处理需要隐藏的参数字段
    for k in escape_param_keys:
        try:
            if k in req_data:
                req_data[k] = "*"
        except:
            pass

    # 如果有返回result，记录日之前，处理需要隐藏的result字段
    result = ret.get("data")
    if isinstance(result, dict):
        for k in escape_result_keys:
            v = result.get(k)
            if v:
                result[k] = "*"
    elif isinstance(result, list):
        result = ['*']

    # 记录日志时，添加请求路径、请求方式、参数、耗时等信息，与返回值共同记录
    return {
        "header": header,
        "path": path,
        "method": method,
        "params": req_data,
        "cost": time_long,
        'uid': uid,
        'req_id': ret.get("req_id"),
        "ret": {
            "code": ret.get("code"),
            "msg": ret.get("msg") or ret.get('message'),
            "result": result
        }
    }
# sql 格式化日志记录的内容
def format_sql_content(func_name, params_str, time_long, err_msg):
    # 记录日志时，采集参数、函数名、耗时、异常信息等信息
    return {
        'params': params_str,
        'msg': err_msg,
        "func_name": func_name,
        "cost": time_long
    }
logger = LoggerManager.get_logger(logging.INFO, 'logger')
task_logger = LoggerManager.get_logger(logging.INFO, 'task')
# epr_t3_logger = LoggerManager.get_logger(logging.INFO, 'erp_t3_log')
