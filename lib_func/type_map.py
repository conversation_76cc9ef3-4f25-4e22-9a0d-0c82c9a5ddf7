# 处理规则

from dataclasses import dataclass, field, asdict
from typing import List, Dict, Union, Any, Optional, Annotated

ImagTypeList = ['jpg', 'png', 'jpeg', 'gif']

VideoTypeList = ['mp4', 'mov']

DocumentTypeList = ['pdf']

OSSVideoCoverImgSuffix = '?x-oss-process=video/snapshot,t_7000,f_jpg,w_800,h_600,m_fast'

OSSImgSuffix = '?x-oss-process=image/resize,p_50/auto-orient,1/quality,q_75'

OperateTypeDict = {
    "submit": "提交",
    "open": "打开",
    "close": "关闭",
    "collect": "收藏",
    "comment": "评论",
    "trans": "转发",
    'click': '点击',
    'send': '发送'
}

MiniSourceMap = {'1': '小程序历史列表', '2': '搜索', '3': '会话', '4': '扫一扫二维码', '5': '公众号主页', '6': '聊天顶部', '7': '系统桌面',
                 '8': '小程序主页', '9': '附近的小程序', '11': '模板消息', '12': '客服消息', '13': '公众号菜单', '14': 'APP分享', '15': '支付完成页',
                 '16': '长按识别二维码', '17': '相册选取二维码', '18': '公众号文章', '19': '钱包', '20': '卡包', '21': '小程序内卡券', '22': '其他小程序',
                 '23': '其他小程序返回', '24': '卡券适用门店列表', '25': '搜索框快捷入口', '26': '小程序客服消息', '27': '公众号下发', '28': '系统会话菜单',
                 '29': '任务栏-最近使用', '30': '长按小程序菜单圆点', '31': '连wifi成功页', '32': '城市服务', '33': '微信广告', '34': '其他移动应用',
                 '35': '发现入口-我的小程序', '36': '任务栏-我的小程序', '37': '微信圈子', '38': '手机充值', '39': 'H5', '40': '插件',
                 '41': '大家在用', '42': '发现页', '43': '浮窗', '44': '附近的人', '45': '看一看', '46': '朋友圈', '47': '企业微信',
                 '48': '视频', '49': '收藏', '50': '微信红包', '51': '微信游戏中心', '52': '摇一摇', '53': '公众号导购消息', '54': '识物',
                 '55': '小程序订单', '56': '小程序直播', '57': '群工具', '10': '其他'}


page_type_dict = {
    "/pages/tabBar/login/index": '登录', "/pages/tabBar/home/<USER>": '首页', "/pages/solution/detail/index": '解决方案',
    "/pages/tabBar/product/index": '产品', "/pages/tabBar/example/index": '案例', "/pages/tabBar/company/index": '公司',
    "/pages/tabBar/user/index": '我的', "/pages/solution/newsdy/index": '新闻动态', "pages/tabBar/home/<USER>": '首页',
    "/pages/solution/newsdy/textdetail/index": '文章详情',
    '/pages/user/service/index': '我的客服', '/pages/user/userinfo/index': '我的信息', '/pages/user/ordered/index': '预约列表',
    '/pages/user/feedback/index': '投诉反馈', '/pages/user/message/index': '消息列表', '/pages/user/integral/index': '我的积分',
    '/pages/user/userxy/index': '用户协议', '/pages/user/userzc/index': '隐私政策', '/pages/user/product/index': '我的产品',
    '/pages/user/collect/index': '我的收藏', '/pages/user/write/index': '产品介绍', '/pages/user/myopinion/index': '新增吐槽',
    '/pages/user/opinionlist/index': '吐槽有礼', '/pages/user/giftlist/index': '礼品清单', '/pages/product/center/index': '产品中心',
    '/pages/product/livemore/index': '产品价值', '/pages/tabBar/dynamic/index': '动态', '/pages/user/evaluate/index': '我的评价',
    '/pages/user/write/index?write_id=10': '服务体系', '/pages/user/write/index?write_id=9': '公司介绍',
    '/pages/user/write/index?write_id=6': '我的报价', '/pages/user/write/index?write_id=7': '资质证书',
    '/pages/user/write/index?write_id=8': '智邦国际公司资料', '/pages/user/ordereval/index': '演示评价'
}

# 用于将英文字段映射为中文显示
SessionMsgMap = {'question': '提问', 'qa_check': '命中QA', 'app_info': '场景分类', 'tag': '标签检索',
                 'doc_map_abstract': '摘要检索', 'graph_list': '知识图谱', 'chunk_score_map': '召回片段',
                 'answer': '生成回答', 's_question': '优化提问'}



@dataclass
class MapEntry:
    # 用于前端表格列的columns字段
    keys: List[str]
    # 用于前端表格列的中文名
    values: List[str]

@dataclass
class TableConfig:
    msg: str
    map: Optional[MapEntry] = None  # map 可能是可選的，或者有些鍵下沒有 'map'
    dim: Optional[str] = None # dim 可能是可選的，或者有些鍵下沒有 'dim'

@dataclass
class SessionMsgTableMap:
    tag: TableConfig
    doc_map_abstract: TableConfig
    chunk_score_map: TableConfig
    graph_list: TableConfig
    search_ret: List[str]


# 按表格展示的数据结构
session_msg_table_map = asdict(SessionMsgTableMap(
    tag=TableConfig(
        msg='召回文档个数',
        map=MapEntry(
            keys=['doc_name', 'word', 'knowledge_name'],
            values=['文档', '标签', '知识库']
        )
    ),
    doc_map_abstract=TableConfig(
        msg='召回文档个数',
        map=MapEntry(
            keys=['doc_name', 'word', 'score', 'knowledge_name'],
            values=['文档', '摘要', '阈值', '知识库']
        ),
        dim='doc'
    ),
    chunk_score_map=TableConfig(
        msg='命中片段数量',
        map=MapEntry(
            keys=['word', 'doc_name', 'score', 'knowledge_name'],
            values=['片段', '文档', '阈值', '知识库']
        ),
        dim='chunk'
    ),
    graph_list=TableConfig(
        msg='',
        map=MapEntry(
            keys=['doc_name', 'word', 'knowledge_name'],
            values=['文档', '图谱', '知识库']
        ),
        dim='doc'
    ),
    search_ret=['doc_map_abstract', 'chunk_score_map', 'graph_list']
))


# 顺序展示的 例如：开启摘要检索: 否， 摘要召回数量: 5
SessionJsonMap = {'app_info': {'app_name': '命中智能体', 'model_path': '对话模型', 'size': '召回数量', 'mini_score': '阈值',
                               'is_mixture': '开启摘要检索', 'digest_size': '摘要召回数量', 'digest_score': '摘要阈值',
                               'is_graph': '开启知识图谱', 'ques_enabled': '开启标签检索', '_type': 'key_map'},
                  'qa_check': {'1': '是', '0': '否', '_type': 'bool_map'}}
ReplenishMap = {('is_mixture', 'is_graph', 'ques_enabled'): {'1': '是', '0': '否', '_type': 'bool_map'}}

for _keys, _value in ReplenishMap.items():
    for key in _keys:
        SessionJsonMap[key] = _value

ForageQASourceMap = {'add': '新增', 'load': '导入', 'file': '文件'}
ForageFileStageMap = {'add': '文件新增', 'split': '文件切片', 'extract': '算料提取'}
ForageFileStatusMap = {'1': '进行中', '2': '已完成', '3': '失败', '4': '部分完成'}
SeparatorMap={
            31:"",
            32:"\n",
            33:"\n\n",
            34:"。",
            35:".",
            36:"！",
            37:"!"
        }

ForageCleanTypeMap = {
    1:"1.移除ASCII 中的一些不可见字符,如0-32和127-160这两个范围",
    2:"2.去除空格",
    3:"3.去除乱码和无意义的unicode",
    4:"4.将繁体字转换为简体字",
    5:"5.清除QA对中没有实际意义的符号,例如:Q:****你是谁 A:、、、我是Anna",
    6:"6.去除文档中的html标签,如<html>,<dev>,<p>等",
    7:"7.去除文档中的markdown标记,如#,##,###"
}

ForageFilterTypeMap = {
    1:"1.是否包含色情的内容",
    2:"2.是否包含暴力血腥的内容",
    3:"3.是否包含逻辑混乱的内容",
    4:"4.是否包含特殊字符率过高的内容",
    5:"5.是否包含词语重复率过高的内容"
}

ForageDeduplicationTypeMap = {
    1:"question",
    2:"answer",
    3:"question_answer"
}