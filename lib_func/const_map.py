# 响应状态码
"""
Code 大范围标准及对应日志等级

正常或正常错误提示   0 <= Code < 300 （info 日志等级）

异常提醒    300 <= Code < 500      （waring 日志等级）

服务错误    500 < Code     （error 日志等级）
"""

StatusMap = {
    # --------------------------------------------------------------- 
    
    'do_action_failed': {
        'message': '操作失败，请换个姿势重试一次',
        'code': 1
    },
    'missing_token': {
        'message': '身份识别信息缺失，请重新登录',
        'code': 2
    },
    'missing_sign': {
        'message': '缺少签名验证，请重新登录',
        'code': 2,
    },
    'missing_timestamp_sign': {
        'message': '缺少必要参数，请重新登录',
        'code': 2,
    },
    'unauthorized_access': {
        'message': '越权访问，请重新登录',
        'code': 2,
    },
    'verify_failed': {
        'message': '身份识别失败，请重新登录!',
        'code': 2
    },
    'sign_code_filed': {
        'message': '验证code登录失败，请重新打开应用',
        'code': 2,
    },
    'token_expired': {
        'message': '身份认证已过期，请重新登录',
        'code': 2,
    },
    'invalid_token': {
        'message': '签名错误，请重新打开应用',
        'code': 2
    },
    'login_failed': {
        'message': '登录失败，请重新打开应用',
        'code': 2,
    },
    'bind_failed': {
        'message': '该手机号已经绑定！',
        'code': 3
    },
    'name_duplication': {
        'message': '该名称已经被占用！',
        'code': 7
    },
    'not_found': {
        'message': '相关数据已下架',
        'code': 6
    },
    'search_failed': {
        'message': '未查询到相关信息',
        'code': 4
    },
    'create_failed': {
        'message': '创建数据失败',
        'code': 5
    },
    'corp_miss': {
        'message': '非法请求，缺少必要组织信息corp_id',
        'code': 9
    },
    'sign_out': {
        'message': '账号已注销，请联系管理员',
        'code': 10,
    },
    'seat_flag': {
        'message': '账号无系统席位，请购买席位或联系管理员开通。购买完成1分钟后重试',
        'code': 11,
    },
    'code_expired': {
        'message': '验证码已过期，请重新发送',
        'code': 41,
    },
    'verify_code_failed': {
        'message': '验证码不正确',
        'code': 42
    },
    'invalid_phone_no': {
        'message': '请输入正确的手机号',
        'code': 50
    },
    'parameters_abnormal': {
        'message': '参数缺失或异常',
        'code': 52
    },
    'incorrect_password': {
        'message': '密码不符合规则',
        'code': 54
    },
    'necessary_missing': {
        'message': '参数异常',
        'code': 55
    },
    'sensitive_detection': {
        'message': '请剔除敏感词汇',
        'code': 56
    },
    'very_long': {
        'message': '已超过长度限制，请减少输入',
        'code': 57
    },
    'customer_err': {
        'message': '未知错误',
        'code': 100
    },
    'third_login_failed': {
        'message': '授权登录失败',
        'code': 121
    },
    'not_update': {
        'message': '试卷已启封，不可修改',
        'code': 130
    },
    'wait_up_ret': {
        'message': '考卷已提交，请等待阅卷结果',
        'code': 131
    },
    'game_over': {
        'message': '考卷已结束',
        'code': 132
    },
    'es_index_name_err': {
        'message': '知识库不存在',
        'code': 134
    },
    'delete_err': {
        'message': '删除失败！',
        'code': 100
    },
    'record_empty': {
        'message': '暂无数据！',
        'code': 101
    },
    'rel_knowledge_exist': {
        'message': '当前素材已经关联这个知识库了无需重复关联！',
        'code': 102
    },
    'train_request_running': {
        'message': '该训练请求正在训练中，不能删除！',
        'code': 1001
    },
    'stop_queue_param_error': {
        'message': '请检查参数是否正常！',
        'code': 1002
    },
    'stop_queue_error': {
        'message': '中断失败，请稍后重试！',
        'code': 1003
    },
    'model_export_err': {
        'message': '模型发布失败，请重试！',
        'code': 1004
    },
    'model_export_tar_err': {
        'message': '压缩模型失败，请重试！',
        'code': 1005
    },
    'train_model_not_success': {
        'message': '模型还未训练完成！',
        'code': 1006
    },
    'train_model_publish_err': {
        'message': '模型已发布，请勿重复发布！',
        'code': 1007
    },
    'excel_sheet_err': {
        'message': '文件包含多个sheet，请确保只使用一个sheet',
        'code': 1
    },
    'excel_header_err': {
        'message': '文件没有表头，无法导入',
        'code': 1
    },
    'excel_header_not_match': {
        'message': '文件表头不一致，无法导入',
        'code': 1
    },
    'excel_data_err': {
        'message': '文件没有数据，无法导入',
        'code': 1
    },
    'excel_merged_cell_err': {
        'message': '文件可能包含合并单元格，不符合格式要求',
        'code': 1
    },
    'excel_pivot_table_err': {
        'message': '文件可能包含数据透视表，不符合格式要求',
        'code': 1
    },
    'json_format_err': {
        'message': 'JSON 格式不完整',
        'code': 1
    },
    'json_parse_err': {
        'message': 'JSON 解析失败',
        'code': 1
    },
    'prompt_too_long': {
        'message': 'prompt 长度超出限制',
        'code': 1
    },
    'no_export_data': {
        'message': '没有可导出的数据',
        'code': 1
    },
    'export_excel_failed': {
        'message': '导出Excel失败',
        'code': 1
    }
}
# 图片类型列表
ImagTypeList = ['jpg', 'png', 'jpeg', 'gif', 'svg']
VideoTypeList = ['mp4', 'mov']
DocumentTypeList = ['pdf']
OSSVideoCoverImgSuffix = '?x-oss-process=video/snapshot,t_7000,f_jpg,w_800,h_600,m_fast'


# 文心一言模型code
WENXIN_CODE = 'wenxinworkshop'
# ChatGPT code
CHATGPT_CODE = 'chatgpt'
# 通义千问 code
QIANWEN_CODE = 'tongyiqianwen'
# 通义千问async code
QIANWEN_ASYNC_CODE = 'tongyiqianwen_async'
# ChatGLM code
CHATGLM_CODE = 'chatglm'
# llama code
LLAMA_CODE = 'llama'
# 默认雷达推荐使用的模型ID
AIGC_MODEL_ID = '0d7f67dc-0134-11ef-9469-0242ac110003'
# 默认素材推荐使用的模型ID
AIGC_CONTENT_MODEL_ID = '0d7f67dc-0134-11ef-9469-0242ac110003'
# ChatGPT model id
CHATGPT_MODEL_ID = '4bc9b92c-ed70-11ed-9aa2-0242ac110003'
# Bing model id
BING_MODEL_ID = '40e043ec-3aac-47a5-b924-d374627e69f3'
# Qwen-72B code
QWEN_72B_CODE = 'tongyiqianwen_72b'
# Ascend 910B code
ASCEND_910B_CODE = 'ascend_910b'
# DeepSeek
DEEP_SEEK_CODE = 'DeepSeek'
# Kimi
KIMI_CODE = 'kimi'
# 讯飞模型
XunFei_Code = 'xunfei'
# 文生图提示词生成
TEXT_TO_IMG_GENERATE_PROMPT = '基于以下内容生成图片描述：'

# 自由对话菜单
FREE_VIEW_ID = '55378367-efc6-11ee-9469-0242ac110003'
# 文案续写菜单
DOC_WRITE_ID = '93e346d6-efc6-11ee-9469-0242ac110003'
# 数据合成
DATA_SYNTHESIS_ID = '87f7a016-6980-484b-96f8-949c2ddd7aa3'
# 代码撰写
CODE_WRITE_ID = '47a0b75c-a1f3-4395-b84b-016c5515bd4c'
# 文生图
TEXT_TO_IMG_ID = 'f38cf65f-efc6-11ee-9469-0242ac110003'
# 图生文
IMG_TO_TEXT = 'ddbe6e0e-fbec-442f-8444-4e8d57cc780b'
# 多模态-图生图
IMG_TO_IMG_ID_1 = '160bc564-efc7-11ee-9469-0242ac110003'
# 截图生成跟进记录
IMG_TO_RECORD_ID = 'f709fdc2-efc7-11ee-9469-0242ac110003'
# 基础功能
BASE_VIEW_ID = '2272a991-efc6-11ee-9469-0242ac110003'
# 雷达推荐
CASE_VIEW_ID = 'a2b8f6e2-efc7-11ee-9469-0242ac110003'
# 素材推荐
MATERIAL_VIEW_ID = '7c3cd1fa-efc7-11ee-9469-0242ac110003'
# 智能问数
AI_NUMBER_VIEW_ID = '26d05e2c-efcd-4f9c-89a6-9f3fea1dc54e'
# Bing搜索大模型
BING_VIEW_ID = 'a72d4778-3177-46b2-9474-096b536b7185'

# 知识库-应用管理员权限
KNOWLEDGE_ROLE_CODE = '56261c01-0bbf-4e85-94af-63d18c70fe8e'
# 超级管理员权限
God = 'b5e2b5d8-03aa-11ee-9aa2-0242ac110003'
# 知识库-应用管理员权限
KNOWLEDGE_ROLE_CODES = [KNOWLEDGE_ROLE_CODE, God]
# 生图失败默认展示图片
TO_IMG_ERROR_URL = 'https://oss-bz.oss-cn-beijing.aliyuncs.com/404-error-300x246.jpg'


# 任务task_id format
AI_TASK_GENERATE_ASYNC_PREFIX = 'AI_TASK_GENERATE_ASYNC_'

# 任务状态
FINISHED = 'finished'
PROCESSED = 'processed'
FAILED = 'failed'