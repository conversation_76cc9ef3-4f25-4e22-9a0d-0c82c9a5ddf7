# -*- coding: utf-8 -*-
# @Time    : 2022/03/17 16:05
# <AUTHOR> sunLF
# @File    : exception.py
# @Software: 自定义异常


class OnlyWarnError(Exception):
    """
    不记录的异常
    """

    def __init__(self, message):
        self.message = message

    def __str__(self):
        return self.message


class OceanApiError(Exception):
    """
    头条api异常
    """

    def __init__(self, message):
        self.message = '巨量广告接口异常：' + str(message)

    def __str__(self):
        return self.message


class BaiduApiError(Exception):
    """
    头条api异常
    """

    def __init__(self, message):
        self.message = '百度接口异常：' + str(message)

    def __str__(self):
        return self.message


class ALiApiError(Exception):
    """
    阿里
    """

    def __init__(self, message):
        self.message = '阿里接口异常：' + str(message)

    def __str__(self):
        return self.message


class ChatGptError(Exception):
    """

    """

    def __init__(self, message):
        self.message = 'ChatGPT口异常: ' + str(message)

    def __str__(self):
        return self.message


class RpaPromptError(Exception):
    """
    系统提示异常
    """

    def __init__(self, message):
        self.message = '系统提示：' + str(message)

    def __str__(self):
        return self.message


class ManagePromptError(Exception):
    """
    管理系统提示异常
    """

    def __init__(self, message):
        self.message = '管理系统提示：' + str(message)

    def __str__(self):
        return self.message

    def __init__(self, code, status_code, message):
        Exception.__init__(self)
        self.status_code = status_code
        self.code = code
        self.message = message
