# -*- coding: utf-8 -*-
from puppet.mongo_sdk import MongodbPool


class NoticeMg(object):

    def __init__(self, corpid):
        self.table_name = 'notify_info'
        self.mongo = MongodbPool(corpid)

    # 获取通知列表

    def get_notice_list(self, user_id, key_word, page_size, page_no):
        fd = {"tp_user_id": user_id, "status": {"$ne": "delete"}}
        if key_word:
            fd["$or"] = [{"notify_title": {"$regex": key_word}}, {"notify_content": {"$regex": key_word}}]
        query = self.mongo.db[self.table_name].find(fd)
        # total = query.count()
        total = self.mongo.db[self.table_name].count_documents(fd)
        notices = query.sort([("notify_time", -1)]).limit(page_size).skip((page_no - 1) * page_size)
        unread_count = self.mongo.db[self.table_name].count_documents({"tp_user_id": user_id, "status": "unread"})
        data = list()
        for notice in notices:
            data.append({"notify_id": notice['notify_id'], "tp_user_id": notice['tp_user_id'],
                         "tp_user_name": notice['tp_user_name'], "notify_title": notice['notify_title'],
                         "notify_content": notice['notify_content'], "notify_time": notice['notify_time'],
                         "status": notice['status'], "modify_time": notice['modify_time']})

        res = {"total": total, "page_no": page_no, "unread_count": unread_count, "notice": data}

        return res

    def test_insert_mongo(self, args):
        """
        测试mongo入库
        """

        res = self.mongo.insert_data_one('test', args)
        return res
