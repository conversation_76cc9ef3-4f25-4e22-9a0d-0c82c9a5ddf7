# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: ai_app_mg.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 4月 17, 2024
# ---
from datetime import datetime

from seaborn import residplot
from torch import dsmm
from puppet.mongo_sdk import MongodbPool


class AiAppMg(object):

    def __init__(self, corpid):
        self.session_record = 'ai_app_session_record'
        self.session_flow_record = 'ai_app_session_flow_record'
        self.search_record = 'ai_app_search_record'
        self.search_content_record = 'ai_app_search_content_record'
        self.app_judge_log = 'ai_app_judge'
        self.statistics_session_table = 'ai_app_statistics_session'
        self.aggregate_statistics_session_table = 'ai_app_aggregate_statistics_session'
        self.mongo = MongodbPool(corpid)

    def create_statistics_index(self, index_fields):
        return self.mongo.create_index(self.statistics_session_table, index_fields)

    def create_aggregate_statistics_index(self, index_fields):
        return self.mongo.create_index(self.aggregate_statistics_session_table, index_fields)
    
    def insert_app_session(self, data):
        return self.mongo.insert_data_one(self.session_record, data)

    def insert_app_session_flow(self, data):
        return self.mongo.insert_data_one(self.session_flow_record, data)
    
    def insert_statistics_session(self, data):
        return self.mongo.insert_data_one(self.statistics_session_table, data)
    
    def insert_aggregate_statistics_session(self, data):
        return self.mongo.insert_data_one(self.aggregate_statistics_session_table, data)

    def update_app_session_record(self, query, update):
        return self.mongo.update_data_one(self.session_record, query, update)

    def insert_search_record(self, data):
        return self.mongo.insert_data_one(self.search_record, data)

    def insert_search_content_record(self, data):
        return self.mongo.insert_data_one(self.search_content_record, data)
    
    def get_latest_statistics_session(self, query, sort_criteria):
        return self.mongo.query_one_custom(self.statistics_session_table, query, sort_criteria)

    # 获取会话记录
    def get_app_session_record(self, session_id, is_final=False):
        query = {"session_id": session_id}
        record_list = self.mongo.db[self.session_record].find(query)

        data_list = list()
        content_key = 'final_content' if is_final else 'content'
        if record_list:
            for record in record_list:
                data_list.append({'role': 'user', 'content': record[content_key]})
                data_list.append({'role': 'assistant', 'content': record.get('answer', '')})
        return data_list

    # 获取会话流程记录
    def get_app_session_flow_record(self, flow_id):
        return self.mongo.db[self.session_flow_record].find({'flow_id': flow_id})
    
    def get_app_session_record_v1(self, session_id, is_final=False):
        query = {"session_id": session_id}
        record_list = self.mongo.db[self.session_record].find(query)

        data_list = list()
        content_key = 'answer' if is_final else 'content'
        if record_list:
            for record in record_list:
                flow_id = record.get('flow_id', '')
                user_content = {'role': 'user', 'content': record.get(content_key, ''), 'flow_id': flow_id}
                assistant_content = {
                    'role': 'assistant',
                    'content': record.get('answer', ''),
                    'reasoning_content': record.get('reasoning_content', ''),
                    'hit_images_list': record.get('hit_images_list', []),
                    'hit_doc_list': record.get('hit_doc_list', []),
                    'doc_image_list': record.get('doc_images_list', []),
                    'flow_id': flow_id
                }
                data_list.append(user_content)
                data_list.append(assistant_content)
        return data_list

    def get_app_session_record_count(self, session_id):
        query = {"session_id": session_id}
        return self.mongo.db[self.session_record].count_documents(query)

    def get_recommend_search_key(self):

        data_list = [
            {
                '$group': {
                    '_id': "$search_key",
                    'count': {'$sum': 1}
                }
            },
            {
                '$sort': {'count': -1}
            },
            {
                '$limit': 1
            }
        ]

        res = list(self.mongo.db[self.search_record].aggregate(data_list))

        if len(res):
            return res[0].get('_id')
        else:
            return "请输入关键字"

    def get_recommend_content(self, limit=8):

        data_list = [
            {
                '$group': {
                    '_id': "$content_id",
                    'count': {'$sum': 1}
                }
            },
            {
                '$sort': {'count': -1}
            },
            {
                '$limit': limit
            }
        ]

        res = list(self.mongo.db[self.search_content_record].aggregate(data_list))

        if len(res):
            return [i['_id'] for i in res]
        else:
            return []

    def get_recommend_search_key_list(self):
        try:
            data_list = [
                {
                    '$group': {
                        '_id': "$search_key",
                        'count': {'$sum': 1}
                    }
                },
                {
                    '$sort': {'count': -1}
                },
                {
                    '$limit': 6
                }
            ]

            res = list(self.mongo.db[self.search_record].aggregate(data_list))

            if len(res):
                return [i['_id'] for i in res]
            else:
                return []
        except Exception as e:
            print(e)
            return []

    def log_app_judge(self, data):
        return self.mongo.insert_data_one(self.app_judge_log, data)

    def update_log_app_judge(self, query, update):
        return self.mongo.update_data_one(self.app_judge_log, query, update)
    

    def get_statistics_app_by_field_count(self, the_time, field_name):
        # time_obj = datetime.strptime(the_time, "%Y-%m-%d %H:%M:%S")
        query_dict = {"session_start_time": {"$lte": the_time}}
        return self.mongo.query_by_field_distinct_count(self.statistics_session_table, field_name, query_dict)
    
    def get_statistics_app_by_field_time_quantum_count(self, start_time, end_time, field_name):
        query_dict = {"session_start_time": {"$gte": start_time, "$lte": end_time}}
        return self.mongo.query_by_field_distinct_count(self.statistics_session_table, field_name, query_dict)
    
    def get_statistics_app_count_by_query_dict(self, query_dict):
        return self.mongo.query_count(self.statistics_session_table, query_dict=query_dict)
    
    def get_statistics_session_duration(self, the_time):
        pipeline = [
                    {
                        "$match": {
                            "session_start_time": {"$lt": the_time},
                            "session_end_time": {"$exists": True}
                        }
                    },
                    {
                        "$addFields": {
                            "session_end_time_numeric": {"$toDouble": "$session_end_time"}
                        }
                    },
                    {
                        "$group": {
                            "_id": None,
                            "total_duration": {"$sum": "$session_end_time_numeric"},
                            "unique_users": {"$addToSet": "$tp_user_id"}
                        }
                    },
                    {
                        "$project": {
                            "average_duration": {"$divide": ["$total_duration", {"$size": "$unique_users"}]}
                        }
                    }
                ]
        
        today_result_list = list(self.mongo.query_aggregate_by_pipeline(self.statistics_session_table, pipeline=pipeline))
        today_average_duration = today_result_list[0]['average_duration'] if today_result_list else 0
        return today_average_duration
    
    def get_statistics_session_deep(self, the_time):
        pipeline = [
                    {
                        "$match": {
                            "session_start_time": {"$lt": the_time}
                        }
                    },
                    {
                        "$group": {
                            "_id": None,
                            "total_turns": {"$sum": 1},
                            "new_sessions": {"$sum": {"$cond": [{"$eq": ["$is_new_session", True]}, 1, 0]}},
                            "unique_users": {"$addToSet": "$tp_user_id"}
                        }
                    },
                    {
                        "$project": {
                            "average_depth": {"$divide": ["$total_turns", {"$multiply": ["$new_sessions", {"$size": "$unique_users"}]}]}
                        }
                    }
                ]
        
        today_result_list = list(self.mongo.query_aggregate_by_pipeline(self.statistics_session_table, pipeline=pipeline))
        today_average_duration = today_result_list[0]['average_depth'] if today_result_list else 0
        return today_average_duration
    
    def get_statistics_session_by_date(self, start_time, end_time):

        pipeline = [
                    {
                        "$match": {
                            "session_start_time": {"$gte": start_time, "$lte": end_time}
                        }
                    },
                                               {
                        "$addFields": {
                            "session_end_time_numeric": {"$toDouble": "$session_end_time"}
                        }
                    },
                    {
                        "$group": {
                            "_id": {
                                "date": {"$dateTrunc": {"date": "$session_start_time", "unit": "day"}}
                            },
                            "unique_users": {"$addToSet": "$tp_user_id"},
                            "total_turns": {"$sum": 1},
                            "total_duration": {"$sum": "$session_end_time_numeric"},
                            "new_sessions": {"$sum": {"$cond": [{"$eq": ["$is_new_session", True]}, 1, 0]}}
                        }
                    },
                    {
                        "$project": {
                            "_id": 0,
                            "date": {"$dateToString": {"format": "%Y-%m-%d", "date": "$_id.date"}},
                            "user_count": {"$size": "$unique_users"},
                            "session_rounds_count": "$total_turns",
                            "average_duration": {"$divide": ["$total_duration", {"$size": "$unique_users"}]},
                            "average_deep": {"$divide": ["$total_turns", "$new_sessions"]}
                        }
                    },
                    {
                        "$sort": {"date": 1}
                    }

                ]
        result_list = list(self.mongo.query_aggregate_by_pipeline(self.statistics_session_table, pipeline=pipeline))
        return result_list
    
    def get_statistics_order_session(self):
        pipeline = [
                    {
                        "$group": {
                            "_id": "$app_id",
                            "app_name": {"$first": "$app_name"},
                            "session_count": {"$sum": 1}
                        }
                    },
                    {
                        "$sort": {
                            "session_count": -1  # 按会话数量降序排序
                        }
                    },
                    {
                        "$limit": 10  # 限制结果只返回前十条
                    }
                ]
        result_list = list(self.mongo.query_aggregate_by_pipeline(self.statistics_session_table, pipeline=pipeline))
        return result_list
    
    def get_statistics_order_user(self):
        pipeline = [
                {
                    "$group": {
                        "_id": "$app_id",
                        "app_name": {"$first": "$app_name"},
                        "user_count": {"$addToSet": "$tp_user_id"}
                    }
                },
                {
                    "$project": {
                        "app_id": "$_id",
                        "app_name": 1,
                        "user_count": {"$size": "$user_count"}
                    }
                },
                {
                    "$sort": {
                        "user_count": -1  # 按用户数量降序排序
                    }
                },
                {
                    "$limit": 10  # 限制结果只返回前十条
                }
            ]
        result_list = list(self.mongo.query_aggregate_by_pipeline(self.statistics_session_table, pipeline=pipeline))
        return result_list
    
    def get_statistics_order_question(self, app_id=None, limit=10):

        pipeline = [
                        {
                            "$group": {
                                "_id": "$message",
                                "count": {"$sum": 1}
                            }
                        },
                        {
                        "$project": {
                            "question": "$_id",
                            "count": 1
                        }
                        },
                        {
                            "$sort": {
                                "count": -1  # 按出现次数降序排序
                            }
                        },
                        {
                            "$limit": limit  # 限制结果只返回前十条
                        }
                    ]
        if app_id is not None:
            pipeline.insert(0, {
                            "$match": {
                                "app_id": app_id
                            }
                            },
                        )
        result_list = list(self.mongo.query_aggregate_by_pipeline(self.statistics_session_table, pipeline=pipeline))
        return result_list
    
    def get_statistics_detail_by_date(self, start_time, end_time, app_id):
        pipeline = [
                        {
                            "$match": {
                                "session_start_time": {"$gte": start_time, "$lt": end_time},
                                "app_id": app_id
                            }
                        },
                        {
                        "$addFields": {
                            "session_end_time_numeric": {"$toDouble": "$session_end_time"},
                        }
                        },
                        {
                            "$group": {
                                "_id": {
                                    "date": {"$dateTrunc": {"date": "$session_start_time", "unit": "day"}}
                                },
                                "user_count": {"$addToSet": "$tp_user_id"},
                                "success_turns": {"$sum": {"$cond": [{"$eq": ["$is_success_session", True]}, 1, 0]}},
                                "failure_turns": {"$sum": {"$cond": [{"$eq": ["$is_success_session", False]}, 1, 0]}},
                                "total_turns": {"$sum": 1},
                                "total_duration": {"$sum": {"$toDouble": "$session_end_time_numeric"}},
                                "average_response_time": {"$avg": {"$toDouble": "$anwer_time"}},
                                "new_sessions": {"$sum": {"$cond": [{"$eq": ["$is_new_session", True]}, 1, 0]}},
                                "total_token_consumption": {"$sum": "$token"},
                            }
                        },
                        {
                            "$project": {
                                "_id": 0,
                                # "date": "$_id.date",
                                "date": {"$dateToString": {"format": "%Y-%m-%d", "date": "$_id.date"}},
                                "user_count": {"$size": "$user_count"},
                                "success_turns": "$success_turns",
                                "failure_turns": "$failure_turns",
                                "average_turns": { "$round": [{"$divide": ["$total_turns", {"$size": "$user_count"}]}]},
                                "total_duration": { "$round": ["$total_duration", 2] },
                                "average_duration": { "$round": [{ "$divide": ["$total_duration", {"$size": "$user_count"}] }]},
                                "new_sessions": "$new_sessions",
                                "average_depth":{ "$round": [ {"$divide": ["$total_turns", "$new_sessions"]}]},
                                "average_response_time": 1,
                                "average_session_per_user": { "$round": [{"$divide": ["$new_sessions", {"$size": "$user_count"}]}]},
                                "average_session_duration": { "$round": [ {"$divide": ["$total_duration", "$new_sessions"]}]},
                                "total_token_consumption": 1,
                                "average_token_consumption_per_user": { "$round": [{"$divide": ["$total_token_consumption", {"$size": "$user_count"}]}]},
                                "average_token_consumption_per_session": { "$round": [{"$divide": ["$total_token_consumption", "$new_sessions"]}]}
                            }
                        },
                        {
                            "$sort": {"date": 1}
                        }
                    ]
        result_list = list(self.mongo.query_aggregate_by_pipeline(self.statistics_session_table, pipeline=pipeline))
        return result_list
    
    def get_count_by_user(self, app_id, start_time, end_time):
        pipeline = [
            {
                "$match": {
                    "session_start_time": {"$gte": start_time, "$lt": end_time},
                    "app_id": app_id
                }
            },
            {
                "$group": {
                    "_id": None,
                    "unique_users": {"$addToSet": "$tp_user_id"}
                }
            },
            {
                "$project": {
                    "unique_user_count": {"$size": "$unique_users"}
                }
            }
        ]
        result = list(self.mongo.query_aggregate_by_pipeline(self.statistics_session_table, pipeline=pipeline))
        if not result:
            return 0
        return result[0].get("unique_user_count")


    
    def get_count_by_session_turns(self, app_id, start_time, end_time):
        query_dict ={
                    "session_start_time": {"$gte": start_time, "$lt": end_time},
                    "app_id": app_id,
                }
        res = self.mongo.query_count(self.statistics_session_table, query_dict)
        return res
    
    def get_count_by_session(self, app_id, start_time, end_time):
        query_dict =  {
                    "session_start_time": {"$gte": start_time, "$lt": end_time},
                    "app_id": app_id,
                    "is_new_session": True
                }
        res = self.mongo.query_count(self.statistics_session_table, query_dict)
        return res
    
    def get_average_session_per_user(self, app_id, start_time, end_time):
        pipeline = [
            {
                "$match": {
                    "session_start_time": {"$gte": start_time, "$lt": end_time},
                    "app_id": app_id
                }
            },
            {
                "$group": {
                    "_id": None,
                    "total_duration": {"$sum": {"$toDouble": "$session_end_time"}},
                    "unique_users": {"$addToSet": "$tp_user_id"}
                }
            },
            {
                "$project": {
                    "average_duration_per_user": {"$round": [{"$divide": ["$total_duration", {"$size": "$unique_users"}]}, 2]}
                }
            }
        ]
        
        result_list = list(self.mongo.query_aggregate_by_pipeline(self.statistics_session_table, pipeline=pipeline))
        if not result_list:
            return 0
        return result_list[0].get("average_duration_per_user")
        
    def get_average_depth(self, app_id, start_time, end_time):
        pipeline = [
                {
                    "$match": {
                        "session_start_time": {"$gte": start_time, "$lt": end_time},
                        "app_id": app_id
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "total_turns": {"$sum": 1},
                        "new_sessions": {"$sum": {"$cond": [{"$eq": ["$is_new_session", True]}, 1, 0]}}
                    }
                },
                {
                    "$project": {
                        "average_depth": {"$round": [{"$divide": ["$total_turns", "$new_sessions"]}, 2]}
                    }
                }
            ]
        result_list = list(self.mongo.query_aggregate_by_pipeline(self.statistics_session_table, pipeline=pipeline))
        if not result_list:
            return 0
        return result_list[0].get("average_depth")
    
    def get_compound_app_statistics(self, args):
        app_id = args.get('app_id')
        start_time = args.get('start_time')
        end_time = args.get('end_time')

        scene_type_map = {
                0: "手动",
                1: "向量",
                2: "大模型",
                4: "关键字"
        }
        all_scene_types = list(scene_type_map.keys())

        if start_time and end_time:
            start_time = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
            end_time = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
            # MongoDB 聚合查询：按日期和场景分组统计
            pipeline = [
                # 筛选条件：匹配 app_id 和时间范围
                {
                    "$match": {
                        "app_id": app_id,
                        "create_time": {"$gte": start_time, "$lte": end_time}
                    }
                },
                # 添加日期字段
                {
                    "$addFields": {
                        "date": {"$dateToString": {"format": "%Y-%m-%d", "date": "$create_time"}}
                    }
                },
                # 按日期和场景分组统计数量
                {
                    "$group": {
                        "_id": {"date": "$date", "scene_type": "$scene_type", "hit_app_id": "$hit_app_id"},
                        "count": {"$sum": 1},
                        "hit_app_name": {"$first": "$hit_app_name"}  # 获取对应场景名称
                    }
                },
                # 按日期排序
                {
                    "$sort": {"_id.date": 1}
                }
            ]

            # 执行查询
            result_list = list(self.mongo.query_aggregate_by_pipeline(self.aggregate_statistics_session_table, pipeline=pipeline))

            # 构造结果
            daily_statistics = []
            for item in result_list:
                daily_statistics.append({
                    "date": item["_id"]["date"],
                    "scene_type": item["_id"]["scene_type"],
                    "hit_app_id": item["_id"]["hit_app_id"],
                    "hit_app_name": item["hit_app_name"],
                    "count": item["count"]
                })

            return daily_statistics

        else:
            # MongoDB 聚合查询：按场景分组统计
            pipeline = [
                # 筛选条件：匹配 app_id
                {
                    "$match": {
                        "app_id": app_id
                    }
                },
                # 按场景类型分组统计数量
                {
                    "$group": {
                        "_id": "$scene_type",
                        "count": {"$sum": 1}
                    }
                }
            ]

            # 执行查询
            result_list = list(self.mongo.query_aggregate_by_pipeline(self.aggregate_statistics_session_table, pipeline=pipeline))

            # 定义场景类型的映射关系


            # 构造结果
            mapped_result = []
            for scene_type in all_scene_types:
                count = 0
                for item in result_list:
                    if item["_id"] == scene_type:
                        count = item["count"]
                        break
                mapped_result.append({
                    "scene_type": scene_type_map.get(scene_type, "未知"),
                    "count": count
                })

            return mapped_result

