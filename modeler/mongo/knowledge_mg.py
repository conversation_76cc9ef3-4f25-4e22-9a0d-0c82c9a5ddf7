# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: knowledge_mg.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 4月 12, 2024
# ---
from puppet.mongo_sdk import MongodbPool


class KnowledgeMg(object):

    def __init__(self, corpid):
        self.record_table = 'knowledge_run_record'
        self.mongo = MongodbPool(corpid)

    def update_knowledge_run_record(self, query, update):
        return self.mongo.db[self.record_table].update_one(query, {"$set": update}, upsert=True)

    def get_knowledge_run_record(self, query):
        return self.mongo.db[self.record_table].find_one(query, sort=[('_id', -1)])


class DocumentClearMg(object):

    def __init__(self, corpid):
        self.record_table = 'doc_clear_record'
        self.mongo = MongodbPool(corpid)

    def update_doc_run_record(self, query, update):
        return self.mongo.db[self.record_table].update_one(query, {"$set": update}, upsert=True)

    def get_doc_run_record(self, query, ps=None, po=None):
        datas = self.mongo.db[self.record_table].find(query)
        if ps:
            datas = datas.skip(ps * (po - 1)).limit(ps)
        return datas

    def del_doc_run_record(self, query):
        self.mongo.db[self.record_table].delete_one(query)
        return True

class LogMg(object):
    def __init__(self, corpid):
        self.embed_log_table = 'document_embed_log'
        self.mongo = MongodbPool(corpid)

    def insert_many_embed_log(self, doc_list):
        for doc_dict in doc_list:
            doc_dict.update({'start_time': doc_dict.get("doc_start_time"), 
                             'end_time' : doc_dict.get("doc_end_time")
                            })
            chunk_list = doc_dict.get("chunk_log_list")
            for chunk in chunk_list:
                chunk.update({
                    'start_time':  chunk.get("chunk_start_time"),
                    'end_time':  chunk.get("chunk_end_time"),
                })
        return self.mongo.insert_data_many(self.embed_log_table, doc_list)

    def get_embed_mongo_info(self, page_num, page_size, doc_id=None, mongo_time=None):
        query = {}
        if doc_id:
            query = {"doc_id": doc_id}
        if mongo_time is not None:
            query["doc_total_time"] = {"$gt": mongo_time}
        cursor = self.mongo.db[self.embed_log_table].find(query, sort=[('_id', -1)], skip=(page_num - 1) * page_size, limit=page_size)
        return list(cursor)



class DocumentSplitMg(object):

    def __init__(self, corpid):
        self.record_table = 'document_split_log'
        self.mongo = MongodbPool(corpid)

    def add_document_split_log(self, log):
        return self.mongo.db[self.record_table].insert_one(log)
    
    def update_document_split_log(self, query, update):
        return self.mongo.db[self.record_table].update_one(query, {"$set": update}, upsert=True)

    def get_document_split_log(self, query):
        return self.mongo.db[self.record_table].find_one(query, sort=[('_id', -1)])
    
    def get_split_mongo_info(self, page_num, page_size, doc_id=None, mongo_time=None):
        query = {}
        if doc_id:
            query = {"doc_id": doc_id}
        if mongo_time is not None:
            query["doc_total_time"] = {"$gt": mongo_time}
        cursor = self.mongo.db[self.record_table].find(query, sort=[('_id', -1)], skip=(page_num - 1) * page_size, limit=page_size)
        return list(cursor)
        
    def insert_many_document_split_log(self, log_list):
        return self.mongo.insert_data_many(self.record_table, log_list)


if __name__ == '__main__':

    res = KnowledgeMg('wwf556ee1bcfa5d9d6').get_knowledge_run_record({'knowledge_id': '4876370960553349121'})
    print(res)