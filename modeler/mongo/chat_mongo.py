#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/4/2 10:04
# <AUTHOR> zhang<PERSON>
# @File    : chat_mongo.py
# @Comment :
from datetime import timedelta, datetime

from puppet.mongo_sdk import MongodbPool


class ChatMongo:

    def __init__(self, corpid):
        self.mongo = MongodbPool(corpid)
        self.session_table = 'chat_session_record'  # 会话记录表
        self.record_table = 'chat_record'  # 聊天记录表
        self.menu_model_table = 'chat_menu_model'  # 菜单绑定模型表

    def insert_session(self, data):
        return self.mongo.insert_data_one(self.session_table, data)

    def get_session(self, tp_user_id, view_id, model_id):
        now = datetime.now()
        condition = {'tp_user_id': tp_user_id, 'view_id': view_id, 'model_id': model_id, 'expire_time': {'$gt': now}}
        session_record = self.mongo.db[self.session_table].find_one(condition, sort=[('create_time', -1)]) or {}
        return session_record.get('session_id')

    def get_chat_record(self, session_id, tp_user_id='', view_id='', model_id=''):
        condition = {'session_id': session_id}
        if tp_user_id:
            condition['tp_user_id'] = tp_user_id
        if view_id:
            condition['view_id'] = view_id
        if model_id:
            condition['model_id'] = model_id
        return self.mongo.db[self.record_table].find(condition).sort('create_time', 1)

    def check_session(self, session_id, expire=True):
        condition = {'session_id': session_id}
        if expire:
            now = datetime.now()
            condition['expire_time'] = {'$gt': now}
        return self.mongo.db[self.session_table].find_one(condition)

    def update_session_expire(self, session_id):
        expire_time = datetime.now() + timedelta(hours=24)
        self.mongo.db[self.session_table].update_one({'session_id': session_id}, {"$set": {"expire_time": expire_time}})

    def insert_record(self, data):
        if not self.mongo.query_one(self.record_table, {'message_id': data.get('message_id')}):
            return self.mongo.insert_data_one(self.record_table, data)
        else:
            # 雷达推荐换一批时message_id不变，不新建记录变更新
            self.mongo.update_data_one(self.record_table, {'message_id': data.get('message_id')}, data)

    def update_record(self, message_id, dic):
        self.mongo.db[self.record_table].update_one({'message_id': message_id}, {"$set": dic})

    def get_radar_stats_info(self, radar_id):
        table = 'client_radar_single'
        data = self.mongo.db[table].find({'radar_id': radar_id, 'operate_type': {'$in': ['send', 'collect', 'close']}})
        send_num, collect_num, read_time = 0, 0, 0
        for item in data:
            if item.get('operate_type') == 'send':
                send_num += 1
            elif item.get('operate_type') == 'collect':
                collect_num += 1
            elif item.get('operate_type') == 'close':
                read_time += int(item.get('operate_content') or 0)
        return {'send_num': send_num, 'collect_num': collect_num, 'read_time': read_time // 1000}

    def get_chat_record_history(self, session_id):
        condition = {
            'session_id': session_id,
            'name': {"$not": {'$regex': '基于'}}
        }
        projection = {'question': 1, 'session_id': 1, '_id': 0}  # '_id' 字段默认是返回的，但我们可以选择不返回它

        return list(self.mongo.db[self.record_table].find(condition, projection).sort('create_time', 1))

    def update_session_report_record(self, message_id, data):
        self.mongo.update_data_one('ai_app_session_report_record', {'message_id': message_id}, data)

    def get_session_report_record(self, session_id):
        condition = {
            'session_id': session_id,
        }
        projection = {'record_info': 1, 'session_id': 1, '_id': 0}  # '_id' 字段默认是返回的，但我们可以选择不返回它

        return self.mongo.db['ai_app_session_report_record'].find_one(condition, projection)

    def update_chatbi_session(self, message_id, data):
        self.mongo.update_data_one('chatbi_session', {'message_id': message_id}, data)

    def get_chatbi_session_record(self, session_id):
        condition = {
            'session_id': session_id,
        }
        projection = {'record_info': 1, 'session_id': 1, '_id': 0}  # '_id' 字段默认是返回的，但我们可以选择不返回它

        return self.mongo.db['chatbi_session'].find_one(condition, projection)