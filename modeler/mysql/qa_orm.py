import uuid
import settings
from module import BaseModel
from utils.tools import get_snowflake_id, CJsonEncoder, generate_qrcode, get_uuid
from module.mysql.models import *
from lib_func.type_map import ForageQASourceMap, ForageFileStageMap, ForageFileStatusMap
import json
from sqlalchemy import func

class QaOrm(BaseModel):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
    def add_lib(self, data):
        with self.session_scope() as session:
            qa_lib_id = get_snowflake_id()
            obj = AigcQaLib(qa_lib_id=qa_lib_id, **data)
            session.add(obj)
            return qa_lib_id
        
    def get_lib_list(self, data, query_qa_count):
        with self.session_scope() as session:
            page_no = int(data.get('page_no', 1))
            page_size = int(data.get('page_size', 10))
            query = session.query(AigcQaLib).filter(AigcQaLib.delete_flag == settings.DELETE_FLAG_FALSE)
            offset = (page_no - 1) * page_size
            sql = """
                select new_qa.*, COUNT(rel_app_qa_lib.qa_lib_id) as app_count
                from (
                    select qa_lib.*, COUNT(*) OVER() as total_count, ai_model.model_name as aigc_model_name, ai_model.dims as aigc_model_dims, ai_model.model_path as aigc_model_path
                    from aigc_qa_lib qa_lib
                    left join aigc_model ai_model on qa_lib.aigc_model_id = ai_model.aigc_model_id 
                    where qa_lib.delete_flag = 0{filter_sql} 
                    ORDER BY qa_lib.add_time DESC
                    LIMIT {page_size} OFFSET {offset} 
                ) new_qa 
                left join (
                    select * from rel_app_qa_lib
                    where delete_flag = 0
                ) rel_app_qa_lib on new_qa.qa_lib_id = rel_app_qa_lib.qa_lib_id GROUP BY new_qa.qa_lib_id;
            """.strip()
            qa_name = data.get('qa_name', '')
            filter_sql = '' if not qa_name else f" and qa_lib.qa_name like '%{qa_name}%'"    
            result = session.execute(sql.format(page_size=page_size, offset=offset, filter_sql=filter_sql))

            keys = result.keys()
            data_list = [dict(zip(keys, data_item)) for data_item in result.fetchall()]
            if callable(query_qa_count) and data_list:
                query = {
                    "query": {
                        "terms": {
                            "qa_lib_id": [str(item.get('qa_lib_id')) for item in data_list]
                        }
                    },
                    "size": 0,
                    "aggs": {
                        "id_counts": {
                            "terms": {
                                "field": "qa_lib_id",
                                "size": len(data_list)
                            }
                        }
                    }
                }
                model_path_list = set([item.get('aigc_model_path') for item in data_list])

                qa_id_count_map = {}
                for model_path in model_path_list:
                    qa_count_list = query_qa_count(query, model_path).get('aggregations', {}).get('id_counts', {}).get('buckets', [])
                    for item in qa_count_list:
                        qa_id_count_map[item.get('key')] = item.get('doc_count')

            for item in data_list:
                item['add_time'] = item.get('add_time').strftime('%Y-%m-%d %H:%M:%S')
                item['update_time'] = item.get('update_time').strftime('%Y-%m-%d %H:%M:%S')
                item['qa_lib_id'] = str(item.get('qa_lib_id'))
                item["qa_count"] = str(qa_id_count_map.get(item.get('qa_lib_id'), 0))
                item['app_count'] = str(item.get('app_count', 0))
            
            return {'data_list': data_list, 'total': 0 if not data_list else data_list[0].get('total_count',), 'po': page_no}
        
    def modify_lib(self, data):
        with self.session_scope() as session:
            session.query(AigcQaLib).filter(AigcQaLib.qa_lib_id == data['qa_lib_id']).update(data)
            return data['qa_lib_id']
        
    def update_status(self, data):
        with self.session_scope() as session:
            session.query(AigcQaLib).filter(AigcQaLib.qa_lib_id == data['qa_lib_id']).update({
                "status": data.get('status', 1)
            })
            return data['qa_lib_id']
    def delete_lib(self, qa_lib_id):
        with self.session_scope() as session:
            session.query(AigcQaLib).filter(AigcQaLib.qa_lib_id == qa_lib_id).update({
                "delete_flag": settings.DELETE_FLAG_TRUE
            })
            return qa_lib_id
        
    def get_lib_detail(self, data):
        with self.session_scope() as session:
            qa_lib_id = data.get('qa_lib_id')
            query = session.query(AigcQaLib).filter(AigcQaLib.qa_lib_id == qa_lib_id)
            obj = query.first()
            if obj:
                return {
                    'qa_lib_id': str(obj.qa_lib_id),
                    'qa_name': obj.qa_name,
                    'qa_desc': obj.qa_desc,
                    'aigc_model_id': obj.aigc_model_id,
                    'icon_url': obj.icon_url,
                    'add_time': obj.add_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "status": obj.status,
                    'update_time': obj.update_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'aigc_model': {
                        "dims": obj.aigc_model.dims,
                        "model_path": obj.aigc_model.model_path
                    }
                }
            return {}
        
class QaExtractOrm(BaseModel):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
    def get_extracted_list(self, query):
        with self.session_scope() as session:
            knowledge_ids = query.get('knowledge_ids', [])
            key_words = query.get('key_words', '')
            query = session.query(AigcKnowledgeExtractedQa).filter(AigcKnowledgeExtractedQa.delete_flag == settings.DELETE_FLAG_FALSE, AigcKnowledgeExtractedQa.knowledge_id.in_(knowledge_ids), AigcKnowledgeExtractedQa.key_words == key_words)
            data_list = query.all()
            return data_list
         
    def batch_add_extract(self, qa_list):
        with self.session_scope() as session:
            session.bulk_insert_mappings(AigcKnowledgeExtractedQa, qa_list)
            return True
        
    def update_qa_list(self, extract_id, new_qa_content):
        with self.session_scope() as session:
            obj = session.query(AigcKnowledgeExtractedQa).filter(AigcKnowledgeExtractedQa.extract_id == extract_id).first()
            if obj:
                obj.qa_content = new_qa_content
                session.commit()


class ForageOrm(BaseModel):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def get_forage_evaluation(self, forage_id):
        with self.session_scope() as session:
            obj = session.query(ModelForage).filter(ModelForage.forage_id == forage_id).first()
            return obj.evaluation

    def get_forage_evaluation_record(self, args, re_evaluate=1):
        forage_id = args.get('forage_id')
        page_no = args.get('page_no', 1)
        page_size = args.get('page_size', 10)
        with self.session_scope() as session:
            q = session.query(AigcForageEvaluationRecord)
            if forage_id:
                q = q.filter(AigcForageEvaluationRecord.forage_id == forage_id)
            if re_evaluate == 0:
                obj = q.order_by(AigcForageEvaluationRecord.add_time.desc()).first()
                if obj:
                    return {
                        'record_id': str(obj.record_id),
                        'state': obj.state,
                        'tp_user_id': obj.tp_user_id,
                        'tp_user_name': obj.tp_user.tp_user_name,
                        'forage_id': obj.forage_id,
                        'evaluation_results': json.loads(obj.evaluation_results) if obj.evaluation_results else {},
                        'task_id': obj.task_id,
                        'aigc_model_id': obj.aigc_model_id,
                        'aigc_model_name': obj.aigc_model.model_name,
                        "add_time": obj.add_time.strftime('%Y-%m-%d %H:%M:%S'),
                        "update_time": obj.update_time.strftime('%Y-%m-%d %H:%M:%S')
                    }
                return {}
            else:
                total = q.count()
                offset = (page_no - 1) * page_size
                obj = q.order_by(AigcForageEvaluationRecord.add_time.desc()).slice(offset, offset + page_size).all()
            data_list = []
            for item in obj:
                data = {
                    'record_id': str(item.record_id),
                    'state': item.state,
                    'tp_user_id': item.tp_user_id,
                    'tp_user_name': item.tp_user.tp_user_name,
                    'forage_id': item.forage_id,
                    'evaluation_results': json.loads(item.evaluation_results) if item.evaluation_results else {},
                    'task_id': item.task_id,
                    'aigc_model_id': item.aigc_model_id,
                    'aigc_model_name': item.aigc_model.model_name,
                    "add_time": item.add_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "update_time": item.update_time.strftime('%Y-%m-%d %H:%M:%S')
                }
                data_list.append(data)
            return {'data_list': data_list, 'total': total, 'po': page_no}

    def update_forage_evaluation_record(self, query, update):
        with self.session_scope() as session:
            session.query(AigcForageEvaluationRecord).filter_by(**query).update(update)
            return True

    def add_forage_evaluation_record(self, data):
        with self.session_scope() as session:
            record_id = get_snowflake_id()
            obj = AigcForageEvaluationRecord(record_id=record_id, **data)
            session.add(obj)
            return record_id

    def add_forage_combine_record(self, data):
        with self.session_scope() as session:
            record_id = get_snowflake_id()
            obj = AigcForageCombineRecord(record_id=record_id, **data)
            session.add(obj)
            return record_id

    def up_forage_combine_record(self, query, update):
        with self.session_scope() as session:
            session.query(AigcForageCombineRecord).filter_by(**query).update(update)
            return True

    def get_forage_clean_result(self, args):
        with self.session_scope() as session:
            forage_id = args.get('forage_id')
            page_no = args.get('page_no', 1)
            page_size = args.get('page_size', 10)
            obj = session.query(AigcForageCleaningRecord)
            if forage_id:
                obj = obj.filter(AigcForageCleaningRecord.forage_id == forage_id)
            total = obj.count()
            offset = (page_no - 1) * page_size
            obj = obj.order_by(AigcForageCleaningRecord.add_time.desc()).slice(offset, offset + page_size).all()
            data_list = []
            for item in obj:
                data = {
                    'record_id': str(item.record_id),
                    'state': item.state,
                    'tp_user_id': item.tp_user_id,
                    'tp_user_name': item.tp_user.tp_user_name,
                    'forage_id': item.forage_id,
                    'clear_log': json.loads(item.clear_log) if item.clear_log else {},
                    'task_id': item.task_id,
                    'add_time': item.add_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'update_time': item.update_time.strftime('%Y-%m-%d %H:%M:%S')
                }
                data_list.append(data)
            return {'data_list': data_list, 'total': total, 'po': page_no}

    def get_model_forage_info(self, forage_id):
        q = self.session.query(ModelForage).filter_by(delete_flag=settings.DELETE_FLAG_FALSE)
        if forage_id:
            q = q.filter_by(forage_id=forage_id)
        obj = q.first()
        if not obj:
            raise Exception('forage_id not exist')
        return {'forage_id': obj.forage_id, 'aigc_model_id': obj.aigc_model_id, 'structured_gen_state': obj.structured_gen_state}
    
    def update_model_forage_info(self, forage_id, update):
        with self.session_scope() as session:
            session.query(ModelForage).filter_by(forage_id=forage_id).update(update)
            return True
        
    def get_forage_clean_result_detail(self, args):
        with self.session_scope() as session:
            record_id = args.get('record_id')
            result_type = args.get('result_type')
            page_no = args.get('page_no', 1)
            page_size = args.get('page_size', 100)
            

            # 查询清洗记录并检查是否存在
            cleaning_record = session.query(AigcForageCleaningRecord).filter(
                AigcForageCleaningRecord.record_id == record_id
            ).first()
            
            if not cleaning_record:
                return {'data_list': [], 'total': 0, 'po': page_no}
            
            # 保存原始 datetime 对象用于比较
            original_add_time = json.loads(cleaning_record.clear_log).get('add_time')
            original_update_time = json.loads(cleaning_record.clear_log).get('end_time')
            
            forage_id = cleaning_record.forage_id
            model_forage = session.query(ModelForage).filter(ModelForage.forage_id == forage_id).first()

            # 基础查询条件
            base_query = session.query(ForageQa).filter(ForageQa.forage_id == forage_id)
            
            if result_type == 'clean' and model_forage.clear_type !='[]':
                q = base_query.filter(
                    ForageQa.clear_question != '',
                    ForageQa.clear_answer != '',
                    ForageQa.update_time >= original_add_time,
                    ForageQa.update_time <= original_update_time
                )            
            elif result_type in ('evaluation', 'filter'):
                # 修正日期范围查询条件
                q = base_query.filter(
                    ForageQa.update_time >= original_add_time,
                    ForageQa.update_time <= original_update_time
                )
                
                # 根据类型设置不同的过滤条件
                if result_type == 'evaluation' and model_forage.deduplication_type !='[]':
                    q = q.filter(ForageQa.delete_flag == 1, ForageQa.delete_filter_type == 2)
                elif result_type == 'filter' and model_forage.filter_type !='[]':  # filter
                    q = q.filter(ForageQa.delete_flag == 1, ForageQa.delete_filter_type == 1)
                else:
                    q = q.filter(ForageQa.delete_flag == 0)
            else:
                return {'data_list': [], 'total': 0, 'po': page_no}
            
            # 分页查询
            total = q.count()
            offset = (page_no - 1) * page_size
            items = q.order_by(ForageQa.add_time.desc()).slice(offset, offset + page_size).all()
            
            # 构建返回数据
            data_list = []
            for item in items:
                data = {
                    'qa_id': item.qa_id,
                    'question': item.question,
                    'answer': item.answer,
                    'add_time': item.add_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'update_time': item.update_time.strftime('%Y-%m-%d %H:%M:%S')
                }
                
                # 如果是 clean 类型，添加额外字段
                if result_type == 'clean':
                    data.update({
                        'clear_question': item.clear_question,
                        'clear_answer': item.clear_answer
                    })
                
                data_list.append(data)
            
            return {'data_list': data_list, 'total': total, 'po': page_no}
        
    def add_forage(self, params):
        with self.session_scope() as session:
            obj = ModelForage(**params)
            session.add(obj)
            session.commit()
            return obj.forage_id

    def up_forage(self, qp, up):
        with self.session_scope() as session:
            session.query(ModelForage).filter_by(**qp).update(up)
            return True

    def page_forage(self, forage_id=None, name=None, status=None, is_structured=None, ps=10, po=1):
        evaluation_state = ''
        cleaning_state = ''
        evaluation_task_id = ''
        cleaning_task_id = ''
        combine_state = ''
        combine_task_id = ''
        q = self.session.query(ModelForage).filter_by(delete_flag=settings.DELETE_FLAG_FALSE)
        if forage_id:
            q = q.filter_by(forage_id=forage_id)
            evaluation_record = self.session.query(AigcForageEvaluationRecord).filter_by(forage_id=forage_id).order_by(AigcForageEvaluationRecord.add_time.desc()).first()
            cleaning_record = self.session.query(AigcForageCleaningRecord).filter_by(forage_id=forage_id).order_by(AigcForageCleaningRecord.add_time.desc()).first()
            combine_record = self.session.query(AigcForageCombineRecord).filter_by(forage_id=forage_id).order_by(AigcForageCombineRecord.add_time.desc()).first()
            evaluation_state = evaluation_record.state if evaluation_record else "UNEVALUATED"
            cleaning_state = cleaning_record.state if cleaning_record else "UNCLEANED"
            evaluation_task_id = evaluation_record.task_id if evaluation_record else ""
            cleaning_task_id = cleaning_record.task_id if cleaning_record else ""
            combine_state = combine_record.state if combine_record else "UNCOMBINED"
            combine_task_id = combine_record.task_id if combine_record else ""
        if status is not None:
            q = q.filter_by(status=status)
        if name:
            q = q.filter(ModelForage.name.like(f'%{name}%'))
        if is_structured:
            q = q.filter(ModelForage.is_structured==is_structured)
        total = q.count()
        objs = q.order_by(ModelForage.add_time.desc()).slice((po - 1) * ps, ps * po).all()
        ret = list()
        for obj in objs:

            ret.append({
                'forage_id': obj.forage_id,
                'user_name': obj.tp_user.tp_user_name if obj.tp_user_id else '无',
                'tp_user_id': obj.tp_user_id,
                'auth_scheme_id': obj.auth_scheme_id,
                'scheme_name': obj.auth_scheme.scheme_name if obj.auth_scheme_id else '无',
                'name': obj.name,
                'status': obj.status,
                'desc': obj.desc,
                'icon': obj.icon,
                'dty_id': obj.dty_id,
                'add_time': obj.add_time,
                'file_count': self.count_forage_rel_file(obj.forage_id),
                'qa_count': self.all_forage_qa(obj.forage_id, is_count=True),
                'evaluation_state': evaluation_state,
                'evaluation_task_id': evaluation_task_id,
                'forage_clear_state': cleaning_state,
                'forage_clear_task_id': cleaning_task_id,
                'aigc_model_id': obj.aigc_model_id,
                'aigc_model_name': obj.aigc_model.model_name if obj.aigc_model_id else '无',
                'combine_state': combine_state or "",
                'combine_task_id': combine_task_id or "",
                'sft_qa_count': self.all_forage_qa_by_type(obj.forage_id, qa_type=0) or 0,
                'rl_qa_count': self.all_forage_qa_by_type(obj.forage_id, qa_type=1) or 0,
                'pre_train_file_count': self.count_forage_rel_file_by_type(obj.forage_id,upload_type=2) or 0,
                'image_file_count': self.count_forage_rel_file_by_type(obj.forage_id,upload_type=3) or 0,
                'video_file_count': self.count_forage_rel_file_by_type(obj.forage_id,upload_type=4) or 0,
                'audio_file_count': self.count_forage_rel_file_by_type(obj.forage_id,upload_type=5) or 0,
                'sensitive_file_count': self.count_forage_rel_file_by_type(obj.forage_id,upload_type=6) or 0,
                'other_file_count': self.count_forage_rel_file_by_type(obj.forage_id,upload_type=7) or 0,
                'is_structured': obj.is_structured,
                'structured_gen_state': obj.structured_gen_state,
                'structured_gen_task_id': obj.structured_gen_task_id
            })
        return {'data_list': ret, 'page': po, 'total': total}

    def add_file(self, params):
        with self.session_scope() as session:
            file_id = str(uuid.uuid1())
            obj = ForageFile(file_id=file_id, **params)
            session.add(obj)
            return file_id

    def up_file_data(self, qp, up):
        with self.session_scope() as session:
            session.query(ForageFile).filter_by(**qp).update(up)
            return True

    def page_forage_file(self, ids=None, name=None, status=None, stage=None,  ps=10, po=1,upload_type=0):
        q = self.session.query(ForageFile).filter_by(delete_flag=settings.DELETE_FLAG_FALSE)
        if ids:
            q = q.filter(ForageFile.file_id.in_(ids))
        if status is not None:
            q = q.filter_by(status=status)
        if stage:
            q = q.filter_by(stage=stage)
        if name:
            q = q.filter(ForageFile.name.like(f'%{name}%'))
        if upload_type is not None:
            q = q.filter_by(upload_type=upload_type)
        total = q.count()
        objs = q.order_by(ForageFile.add_time.desc()).slice((po - 1) * ps, ps * po).all()
        ret = list()
        for obj in objs:
            ret.append({
                'file_id': obj.file_id,
                'user_name': obj.tp_user.tp_user_name if obj.tp_user_id else '无',
                'tp_user_id': obj.tp_user_id,
                'name': obj.name,
                'status': obj.status,
                'status_msg': ForageFileStatusMap.get(str(obj.status)),
                'stage': obj.stage,
                'stage_msg': ForageFileStageMap.get(str(obj.stage)),
                'url': obj.url,
                'qa_count': self.all_forage_qa(file_id=obj.file_id, is_count=True),
                'add_time': obj.add_time,
                'upload_type': obj.upload_type
            })
        return {'data_list': ret, 'page': po, 'total': total}

    def count_forage_rel_file(self, forage_id):
        return self.session.query(RelForageFile).filter_by(forage_id=forage_id).count()

    def count_forage_rel_file_by_type(self,forage_id, upload_type=None):
        file_ids = self.get_forage_rel_file_ids(forage_id)
        q = self.session.query(ForageFile).filter(ForageFile.file_id.in_(file_ids),ForageFile.delete_flag==settings.DELETE_FLAG_FALSE, ForageFile.upload_type==upload_type).count()
        return q

    def get_forage_rel_file_ids(self, forage_id, file_ids=None):
        q = self.session.query(RelForageFile).filter_by(forage_id=forage_id)
        if file_ids:
            q = q.filter(RelForageFile.file_id.in_(file_ids))
        return [x.file_id for x in q.all()]

    def bilk_add_forage_rel_file(self, forage_id, file_ids):
        with self.session_scope() as session:
            datas = list()
            for i in file_ids:
                obj = RelForageFile(forage_id=forage_id, file_id=i)
                datas.append(obj)
            if datas:
                session.bulk_save_objects(datas)
        return True

    def del_forage_rel_file(self, forage_id=None, file_id=None):
        if not (forage_id or file_id):
            return True
        with self.session_scope() as session:
            q = session.query(RelForageFile)
            if forage_id:
                q = q.filter_by(forage_id=forage_id)
            if file_id:
                q = q.filter_by(file_id=file_id)
            q.delete(synchronize_session=False)
        return True

    def bulk_add_forage_qa(self, params):
        with self.session_scope() as session:
            datas = list()
            for i in params:
                obj = ForageQa(**i)
                datas.append(obj)
            if datas:
                session.bulk_save_objects(datas)
        return True

    def up_forage_qa(self, qp, up):
        with self.session_scope() as session:
            session.query(ForageQa).filter_by(**qp).update(up)
            return True

    def add_forage_cleaning_record(self, params):
        with self.session_scope() as session:
            record_id = get_snowflake_id()
            obj = AigcForageCleaningRecord(record_id=record_id, **params)
            session.add(obj)
            return record_id


    def bulk_up_forage_qa(self, ids, up):
        with self.session_scope() as session:
            session.query(ForageQa).filter(ForageQa.qa_id.in_(ids)).update(up)
            return True

    def del_forage_qa(self, forage_id=None, file_id=None):
        if not (forage_id or file_id):
            return True
        with self.session_scope() as session:
            q = session.query(ForageQa)
            if forage_id:
                q = q.filter_by(forage_id=forage_id)
            if file_id:
                q = q.filter_by(file_id=file_id)
            q.delete(synchronize_session=False)
        return True

    def get_random_qa(self, forage_id, random_percent=1, qa_ids=[]):
        with self.session_scope() as session:
            # 查询需要的所有字段
            q = session.query(ForageQa.qa_id, ForageQa.question, ForageQa.answer).filter_by(
                forage_id=forage_id, 
                delete_flag=0
            )
            
            # 如果提供了qa_ids列表，则进一步过滤
            if qa_ids:
                q = q.filter(ForageQa.qa_id.in_(qa_ids))
            
            # 获取总数量
            total = q.count()
            
            # 计算随机抽取的数量，确保在有效范围内
            random_percent = max(0, min(1, random_percent))  # 限制在0-1之间
            random_count = max(1, int(total * random_percent))  # 至少取1条
            
            # 随机排序并获取指定数量的记录
            q = q.order_by(func.random()).limit(random_count)
            
            # 构建问答字符串
            qa_list = []
            for item in q:
                question = item.question
                answer = item.answer
                qa_list.append(f"问题：{question}\n答案：{answer}")
            
            # 连接所有问答对，用空行分隔
            return "\n\n".join(qa_list)

    def page_forage_qa(self, forage_id=None, file_ids=None, qa_ids=None, question=None, source=None, qa_type=0, ps=10, po=1):
        q = self.session.query(ForageQa).filter_by(delete_flag=settings.DELETE_FLAG_FALSE)
        if source:
            q = q.filter_by(source=source)
        if forage_id:
            q = q.filter_by(forage_id=forage_id)
        if file_ids:
            q = q.filter(ForageQa.file_id.in_(file_ids))
        if qa_ids:
            q = q.filter(ForageQa.qa_id.in_(qa_ids))
        if question:
            q = q.filter(ForageQa.question.like(f'%{question}%'))
        if qa_type is not None:
            q = q.filter_by(qa_type=qa_type)
        total = q.count()
        objs = q.order_by(ForageQa.add_time.desc()).slice((po - 1) * ps, ps * po).all()
        ret = list()
        for obj in objs:
            m_dic = {
                'qa_id': obj.qa_id,
                'question': obj.question,
                'answer': obj.answer,
                'source': obj.source,
                'source_desc': obj.source,
                'forage_id': obj.forage_id,
                'forage_name': obj.forage.name if obj.forage_id else '无',
                'file_id': obj.file_id,
                'file_name': obj.file.name if obj.file_id else '无',
                'add_time': obj.add_time,
                'qa_type': obj.qa_type
            }
            # 数据来源解析
            if m_dic['file_id']:
                m_dic['source_desc'] = m_dic['file_name']
            else:
                m_dic['source_desc'] = ForageQASourceMap.get(m_dic['source'], '未知')
            ret.append(m_dic)
        return {'data_list': ret, 'page': po, 'total': total}

    def all_forage_qa_by_type(self, forage_id=None,  qa_type=None):
        q = self.session.query(ForageQa).filter_by(delete_flag=settings.DELETE_FLAG_FALSE)
        if forage_id:
            q = q.filter_by(forage_id=forage_id)
        if qa_type is not None:
            q = q.filter_by(qa_type=qa_type)
        return q.count()


    def all_forage_qa(self, forage_id=None, file_id=None, is_count=False):
        q = self.session.query(ForageQa).filter_by(delete_flag=settings.DELETE_FLAG_FALSE)
        if forage_id:
            q = q.filter_by(forage_id=forage_id)
        if file_id:
            q = q.filter_by(file_id=file_id)
        if is_count:
            return q.count()
        else:
            objs = q.all()
            ret = list()
            for obj in objs:
                ret.append({
                    'qa_id': obj.qa_id,
                    'question': obj.question,
                    'answer': obj.answer,
                    'forage_id': obj.forage_id,
                    'forage_name': obj.forage.name if obj.forage_id else '无',
                    'file_id': obj.file_id,
                    'file_name': obj.file.name if obj.file_id else '无'
                })
            return ret
