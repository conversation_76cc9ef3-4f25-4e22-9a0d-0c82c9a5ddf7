# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: ai_model_orm.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 4月 09, 2024
# ---
from module import BaseModel
from module.mysql.models import *
from sqlalchemy import func, or_, and_
from utils.tools import get_snowflake_id
from sqlalchemy.dialects import postgresql
import random
import settings


class AiModelOrm(BaseModel):

    def get_aigc_model_list(self, args):
        """
        获取模型列表
        :return:
        """
        q = self.session.query(AigcModel).filter(AigcModel.delete_flag == settings.DELETE_FLAG_FALSE)
        if args.get('model_name'):
            q = q.filter(AigcModel.model_name.like('%' + args.get('model_name') + '%'))
        if args.get('use_ranges'):
            q = q.filter(AigcModel.use_range.in_(args.get('use_ranges')))

        objs = q.all()

        data_list = list()
        for obj in objs:
            data_list.append({
                'aigc_model_id': obj.aigc_model_id,
                'model_name': obj.model_name,
                'model_type_id': obj.aigc_type_id,
                'model_type_name': obj.aigc_type.aigc_type_name,
                'model_icon': obj.model_icon,
                'model_url': obj.model_url,
                'model_path': obj.model_path,
                'model_code': obj.model_code,
                'dims': obj.dims
            })
        return data_list

    def get_aigc_model_list_detail(self, args):
        """
        获取模型列表
        :return:
        """
        q = self.session.query(AigcModel).filter(AigcModel.delete_flag == settings.DELETE_FLAG_FALSE)
        if args.get('model_name'):
            q = q.filter(AigcModel.model_name.like('%' + args.get('model_name') + '%'))
        if args.get('use_ranges'):
            q = q.filter(AigcModel.use_range.in_(args.get('use_ranges')))

        objs = q.all()

        data_list = list()
        for obj in objs:
            data_list.append({
                'aigc_model_id': obj.aigc_model_id,
                'model_name': obj.model_name,
                'model_type_id': obj.aigc_type_id,
                'model_type_name': obj.aigc_type.aigc_type_name,
                'model_icon': obj.model_icon,
                'model_url': obj.model_url,
                'model_path': obj.model_path,
                'model_code': obj.model_code,
                'dims': obj.dims,
                'param_size_b': obj.param_size_b,
                'param_config': obj.param_config,
                'param_preference': obj.param_preference,
                'param_human_rating': obj.param_human_rating,
            })
        return data_list

    def get_ai_question_number_data(self):

        objs = self.session.query(AiQuesionNumber).filter(AiQuesionNumber.delete_flag == settings.DELETE_FLAG_FALSE).all()
        data_list = list()
        for obj in objs:
            data_list.append(obj.data_name)
        return "、".join(data_list)
    
class AiModelBalanceOrm(BaseModel):
    def get_random_model_url(self):
        with self.session_scope() as session:
            result = session.query(AigcModelBalance).filter(AigcModelBalance.status == 0).all()
            # 从result里面随机取一项
            random_item = random.choice(result) if result else None
            
            try:
                normal_list = [item.model_url for item in result]
                sel_model_url = random_item.model_url
                print(f"随机选中为：{sel_model_url}:, 可用列表为{normal_list}")
                return {'model_url':random_item.model_url, 'url_id': random_item.url_id}
            except Exception as e:
                print(f"get_random_model_url error: {e}")
                return {'model_url':settings.JUSURE_910B_EMBEDDINGS_API, 'url_id': 0}    
            
    def set_model_url_status(self, url_id, status):
        with self.session_scope() as session:
            result = session.query(AigcModelBalance).filter(AigcModelBalance.url_id == url_id).update({'status': status})
            return result
        
    def get_all_model_url(self):
        with self.session_scope() as session:
            result = session.query(AigcModelBalance).all()
            return [{'model_url': item.model_url, 'url_id': item.url_id} for item in result]