import settings
from module import BaseModel
from module.mysql.models import *


class MiniOrm(BaseModel):

    def get_client_info_detail(self, client_id, unionid=None, phone_number=None):

        if not client_id and not unionid and not phone_number:
            return None
        with self.session_scope() as _session:
            q = _session.query(ClientInfo).filter(
                ClientInfo.delete_flag == str(settings.DELETE_FLAG_FALSE))
            if client_id:
                q = q.filter(ClientInfo.client_id == client_id)
            if unionid:
                q = q.filter(ClientInfo.unionid == unionid)
            if phone_number:
                q = q.filter(ClientInfo.phone_number == phone_number)

            obj = q.first()
            if obj:
                if obj.gender == '1':
                    gender_name = '男'
                elif obj.gender == '2':
                    gender_name = '女'
                else:
                    gender_name = '未知'

                res = {'client_id': obj.client_id, 'mini_openid': obj.mini_openid, 'nick_name': obj.client_name, 'unionid': obj.unionid,
                       'phone_number': obj.phone_number, 'gender': obj.gender, 'pic_url': obj.pic_url, 'role_id': obj.now_role,
                       'company_name': obj.company_name, 'integral': obj.integral, 'client_code': obj.client_code, 'address': obj.address,
                       'post_code': obj.post_code, 'client_level': obj.client_level, 'work_client_id': obj.work_client_id,
                       'success_client': obj.success_client, 'activity_flag': obj.activity_flag, 'growth_value': obj.growth_value,
                       'growth_level': obj.growth_level, 'integral1': obj.integral1, 'integral2': obj.integral2,
                       'money': 0, 'now_role': obj.now_role, 'id_front_pic': obj.id_front_pic, 'id_back_pic': obj.id_back_pic,
                       'personal_pic': obj.personal_pic, 'graduation_pic': obj.graduation_pic, 'social_id_pic': obj.social_id_pic,
                       'id_address': obj.id_address, 'current_address': obj.current_address, 'nation': obj.nation,
                       'political_id': obj.political_id, 'college_name': obj.college_name, 'max_learn': obj.max_learn,
                       'educations_history': obj.educations_history, 'got_ca': obj.got_ca, 'company_master': obj.company_master,
                       'company_phone': obj.company_phone, 'id_card': obj.id_card, 'real_name': obj.real_name, 'age': obj.age,
                       'gender_name': gender_name, 'political_name': obj.political.political_name if obj.political else '',
                       'max_learn_name': obj.dict_client_max_learn.max_learn_name if obj.dict_client_max_learn else ''}
            else:
                res = None

        return res

