#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/4/1 14:43
# <AUTHOR> zhang<PERSON>
# @File    : app_orm.py
# @Comment :
import json
import settings
from datetime import datetime
from sqlalchemy import or_, func
from sqlalchemy.orm import aliased
from sqlalchemy.dialects import postgresql

from module import BaseModel, exchange
from module.mysql.manager import ModelConf
from module.mysql.models import *
from utils.tools import get_snowflake_id, CJsonEncoder, generate_qrcode, get_uuid, str_to_date
from utils.oss_utils import OSSUtil
from lib_func.const_map import ImagTypeList, VideoTypeList, OSSVideoCoverImgSuffix
from lib_func.logger import logger
from flask import g
from sqlalchemy.sql import literal


class AppOrm(BaseModel):
    # 聊天框菜单id
    CHAT_VIEW_TYPE = '9e67215e-efc5-11ee-9469-0242ac110003'

    def add_ai_model(self, params):
        with self.session_scope() as session:
            obj = AigcModel(**params)
            session.add(obj)
        return True

    def up_ai_model(self, qp, up):
        with self.session_scope() as session:
            session.query(AigcModel).filter_by(**qp).update(up)
        return True
    
    def check_per_use_range_default(self, is_default, aigc_model_id):
        # 将aigc_model_id设置为default,该所属的use_range，is_default=0
        if int(is_default) == 1:
            aigc_model = self.session.query(AigcModel).filter_by(aigc_model_id=aigc_model_id).first()
            use_range = aigc_model.use_range
            with self.session_scope() as session:
                aigc_model.is_default = is_default
                session.query(AigcModel).filter(AigcModel.use_range==use_range, AigcModel.aigc_model_id !=aigc_model_id).update({'is_default':0})
            
    def get_model_types(self):
        with self.session_scope() as session:
            return session.query(DictAigcType).all()

    def get_model_by_name(self, name):
        return self.session.query(DictAigcType).filter_by(aigc_type_name=name).first()

    def add_model_type(self, params):
        with self.session_scope() as session:
            obj = DictAigcType(**params)
            session.add(obj)
        return True

    def up_model_type(self, qp, up):
        with self.session_scope() as session:
            session.query(DictAigcType).filter_by(**qp).update(up)
        return True

    def get_model_list(self, use_range=[]):
        with self.session_scope() as session:
            q = session.query(AigcModel).filter(AigcModel.delete_flag == settings.DELETE_FLAG_FALSE, AigcModel.status == 1)
            if use_range:
                q = q.filter(AigcModel.use_range.in_(use_range))
            q = q.order_by(func.field_(AigcModel.model_name, '语思大模型', '通义千问', '文心一言', 'ChatGPT4.0', 'LLama3-8B'))
            return q.order_by(AigcModel.model_name).all()

    def get_model(self, model_id=''):
        with self.session_scope() as session:
            return session.query(AigcModel).filter(
                AigcModel.aigc_model_id == model_id, AigcModel.delete_flag == settings.DELETE_FLAG_FALSE).first()
    
    def get_model_detail_by_id(self, aigc_model_id):
        """
        获取AI模型详细信息
        Args:
            aigc_model_id: 模型唯一标识
        Returns:
            dict: 包含两张表所有字段的字典
        """
        with self.session_scope() as session:
            result = session.query(
                AigcModel,
                DictAigcType
            ).join(
                DictAigcType, AigcModel.aigc_type_id == DictAigcType.aigc_type_id
            ).filter(
                AigcModel.aigc_model_id == aigc_model_id,
                AigcModel.delete_flag == settings.DELETE_FLAG_FALSE
            ).first()

            if not result:
                logger.warning(f"Model not found: {aigc_model_id}")
                return {}

            model, model_type = result
            return {
                **{c.name: getattr(model, c.name) for c in model.__table__.columns},
                **{f"type_{c.name}": getattr(model_type, c.name) for c in model_type.__table__.columns}
            }

    def get_model_by_typ_id_path(self, aigc_type_id, model_path):
        res = self.session.query(AigcModel).filter(
                AigcModel.aigc_type_id == aigc_type_id, AigcModel.model_path == model_path, AigcModel.delete_flag == settings.DELETE_FLAG_FALSE).first()
        if not res:
            res = self.session.query(AigcModel).filter(AigcModel.aigc_type_id == aigc_type_id, AigcModel.delete_flag == settings.DELETE_FLAG_FALSE).first()
        return res

    def get_chat_menus(self, role_ids=[], level=0, father_id='', view_ids=[], join_rel_flag=False):
        with self.session_scope() as session:
            q = session.query(FunctionView).distinct()
            if join_rel_flag:
                q = q.join(RelRoleView, FunctionView.function_view_id == RelRoleView.view_id)
            q = q.filter(FunctionView.view_type_id == AppOrm.CHAT_VIEW_TYPE, FunctionView.delete_flag == settings.DELETE_FLAG_FALSE)
            if join_rel_flag and role_ids:
                q = q.filter(RelRoleView.role_id.in_(role_ids))
            if view_ids:
                q = q.filter(or_(FunctionView.function_view_id.in_(view_ids),
                                 FunctionView.father_view_id.in_(view_ids)))
            if level:
                q = q.filter(FunctionView.view_level_id == level)
            if father_id:
                q = q.filter(FunctionView.father_view_id == father_id)
            return q.all()

    def get_apps(self, app_name, app_desc, app_id=None):
        """
        查询应用列表
        """
        with self.session_scope() as session:
            q = session.query(AigcApp).filter(AigcApp.delete_flag == settings.DELETE_FLAG_FALSE)
            if app_name:
                q = q.filter(AigcApp.app_name.like(f'%{app_name}%'))
            if app_desc:
                q = q.filter(AigcApp.app_desc.like(f'%{app_desc}%'))
            if app_id:
                q = q.filter(AigcApp.app_id == int(app_id))
            return q.order_by(AigcApp.add_time.desc()).all()

    def get_app_sample_detail(self, app_id):
        obj = self.session.query(AigcApp).filter_by(delete_flag=0, app_id=app_id).first()
        scheme_info = {}
        if obj.auth_scheme_id:
            scheme_info = self.get_scheme_info(obj.auth_scheme.auth_scheme_id)
        ret = {
            'app_name': obj.app_name or '',
            'app_id': str(obj.app_id),
            'run_mode': obj.run_mode,
            'session_log': obj.session_log,
            'session_no': obj.session_no,
            'flow_app': self.get_app_rel_flow(obj.app_id),
            'icon_url': obj.icon_url or '', 'prompt': obj.prompt or '', 'status': obj.status or 0,
            'app_desc': obj.app_desc, 'auth_scheme_id': obj.auth_scheme_id, 'auth_scheme_info': scheme_info,
            'add_time': str(obj.add_time),
            'welcome_content': obj.welcome_content or '您好，欢迎来到AI助手！',
            'classify_prompt': obj.classify_prompt,
            'parent_directory_id': str(obj.parent_directory_id)
        }
        return ret

    def get_app_list_by_directory_id(self, args):

        parent_directory_id = args.get('parent_directory_id')
        page_size = args.get('page_size')
        page_no = args.get('page_no')
        app_name = args.get('app_name')
        app_desc = args.get('app_desc')
        scope = args.get('scope')
        scope_id = args.get('scope_id')
        with self.session_scope() as session:
            q = session.query(AigcApp).filter(AigcApp.delete_flag == settings.DELETE_FLAG_FALSE)
            if scope or scope == 0:
                q = q.filter(AigcApp.scope == scope)
            if scope_id:
                q = q.filter(AigcApp.scope_id == scope_id)
            if parent_directory_id:
                q = q.filter(AigcApp.parent_directory_id == parent_directory_id)
            if app_name:
                q = q.filter(AigcApp.app_name.like(f'%{app_name}%'))
            if app_desc:
                q = q.filter(AigcApp.app_desc.like(f'%{app_desc}%'))
            if page_size and page_size.isdigit():
                page_size = int(page_size)
                page_no = int(page_no)
                objs = q.order_by(AigcApp.add_time.desc()).limit(page_size).offset((page_no - 1) * page_size).all()
            else:
                objs = q.order_by(AigcApp.add_time.desc()).all()
            count = q.count()

            return {
                'total': count,
                'po': page_no,
                'data_list': [{
                    'app_id': str(obj.app_id),
                    'app_name': obj.app_name,
                    'app_desc': obj.app_desc,
                    'icon_url': obj.icon_url,
                } for obj in objs]
            }




    def get_apps_list(self, app_name, app_desc, knowledge_id, page_size, page_no, scope, scope_id):
        """
        查询应用列表
        """
        with self.session_scope() as session:
            q = session.query(AigcApp).filter(AigcApp.delete_flag == settings.DELETE_FLAG_FALSE)
            logger.info(q.count())
            if scope or scope == 0:
                q = q.filter(AigcApp.scope == scope)
            if app_name:
                q = q.filter(AigcApp.app_name.like(f'%{app_name}%'))
            if app_desc:
                q = q.filter(AigcApp.app_desc.like(f'%{app_desc}%'))
            if knowledge_id:
                q = q.join(RelAppKnowledge, RelAppKnowledge.app_id == AigcApp.app_id)
                q = q.filter(RelAppKnowledge.knowledge_id == knowledge_id,RelAppKnowledge.delete_flag == settings.DELETE_FLAG_FALSE)
            if scope_id:
                q = q.filter(AigcApp.scope_id == scope_id)
            if page_size and page_size.isdigit():
                page_size = int(page_size)
                page_no = int(page_no)
                objs = q.order_by(AigcApp.add_time.desc()).limit(page_size).offset((page_no - 1) * page_size).all()
            else:
                objs = q.order_by(AigcApp.add_time.desc()).all()
            count = q.count()
            
            return {
                'total': count,
                'po': page_no,
                'data_list': [{
                    'app_id': str(obj.app_id),
                    'app_name': obj.app_name,
                    'app_desc': obj.app_desc,
                    'icon_url': obj.icon_url,
                    'is_aggregate': obj.app_type-1,
                    'app_type': obj.app_type,
                    'scope_id': obj.scope_id,
                    'run_mode': obj.run_mode
                } for obj in objs]
            }
        
    def get_apps_gt(self, knowledge_ids):
        """
        查询应用列表
        """
        with self.session_scope() as session:
            q = session.query(RelAppKnowledge.app_id).filter(RelAppKnowledge.knowledge_id.in_(knowledge_ids))
            return q.all()

    def get_prompt_by_app_ids(self, app_ids):
        """
        根据app_ids查询提示词变量
        """
        with self.session_scope() as session:
            return session.query(AigcAppPrompt).filter(
                AigcAppPrompt.app_id.in_(app_ids), AigcAppPrompt.delete_flag == settings.DELETE_FLAG_FALSE).all()

    def get_knowledge_ids_by_app_ids(self, app_ids, auth_scheme_ids=None):
        """
        根据app_ids查询绑定知识库ids
        """
        with self.session_scope() as session:
            q = session.query(RelAppKnowledge).join(AigcKnowledge, AigcKnowledge.knowledge_id == RelAppKnowledge.knowledge_id)\
                .filter(RelAppKnowledge.app_id.in_(app_ids), RelAppKnowledge.delete_flag == settings.DELETE_FLAG_FALSE)
            if auth_scheme_ids:
                q = q.filter(AigcKnowledge.auth_scheme_id.in_(auth_scheme_ids))
            return q.all()
        
    def get_knowledge_ids_by_app_ids_v2(self, app_ids, args):
        with self.session_scope() as session:
            q = session.query(RelAppKnowledge).join(AigcKnowledge, AigcKnowledge.knowledge_id == RelAppKnowledge.knowledge_id)\
                .filter(RelAppKnowledge.app_id.in_(app_ids), RelAppKnowledge.delete_flag == settings.DELETE_FLAG_FALSE)
            all_scheme_ids = args.get('all_scheme_ids')
            if all_scheme_ids:
                # logger.info("有权限id")
                q1 = q.filter(AigcKnowledge.scope == 0,AigcKnowledge.auth_scheme_id.in_(all_scheme_ids))
                q2= q.filter(AigcKnowledge.scope == 1,AigcKnowledge.tp_user_id == args["tp_user_id"])
                q3 = q.filter(AigcKnowledge.scope == 1,AigcKnowledge.auth_scheme_id.in_(all_scheme_ids))
                q = q1.union(q2,q3)
            else:
                # logger.info("没权限id")
                q1 = q.filter(AigcKnowledge.scope == 0)
                q2 = q.filter(AigcKnowledge.scope == 1, AigcKnowledge.tp_user_id == args["tp_user_id"])
                q = q1.union(q2)
            return q.all()

    def get_app_by_name(self, app_name):
        with self.session_scope() as session:
            obj = session.query(AigcApp).filter(AigcApp.delete_flag == settings.DELETE_FLAG_FALSE, AigcApp.app_name == app_name).first()
            if obj:
                return obj.app_id
        return False

    def create_app(self, **kwargs):
        """
        创建应用
        """
        with self.session_scope() as session:
            # now = datetime.now()
            app_id = get_snowflake_id()
            kwargs['app_id'] = app_id
            obj = AigcApp(**kwargs)
            session.add(obj)
            return app_id


    def get_app_tree(self, args):

        parent_directory_id = args.get('parent_directory_id')
        directory_name = args.get('directory_name')
        directory_id = args.get('directory_id')
        app_name = args.get('app_name')
        #如果传入了目录id，则获取目录信息
        if app_name:
            # 获取包含该应用名称的所有 parent_directory_id
            directory_ids = self.session.query(AigcApp.parent_directory_id).filter(
                AigcApp.app_name.ilike(f"%{app_name}%"),
                AigcApp.delete_flag == settings.DELETE_FLAG_FALSE
            ).distinct().all()  # 使用 distinct 去重
            # 如果找到符合条件的目录 ID
            if directory_ids:
                directory_ids = [directory_id[0] for directory_id in directory_ids]  # 提取出 directory_id
                tree = []

                # 遍历每个 directory_id 获取完整的目录树
                for directory_id in directory_ids:
                    directory = self.session.query(AppDirectory).filter(
                        AppDirectory.directory_id == directory_id,
                        AppDirectory.delete_flag == settings.DELETE_FLAG_FALSE
                    ).first()
                    if directory:
                        # 构建目录树节点并加入树
                        node = self._build_node(directory,app_name)
                        tree.append(node)
                return {'message': 'Directory tree with knowledge names retrieved successfully', 'data': tree}
            else:
                return {'message': 'No directories found with the given knowledge name'}        
        if directory_id:
            directory = self.session.query(AppDirectory).filter(
                AppDirectory.directory_id == directory_id,
                AppDirectory.delete_flag == settings.DELETE_FLAG_FALSE
            ).first()
            if directory.parent_directory_id:
                parent_directory_id = directory.parent_directory_id
                q = self.session.query(AppDirectory).filter(
                    AppDirectory.directory_id == parent_directory_id,
                    AppDirectory.delete_flag == settings.DELETE_FLAG_FALSE
                ).first()
                parent_directory_name = q.directory_name
            else:
                parent_directory_name = None
            if directory:
                return {       
                    'directory_id': str(directory.directory_id),
                    'directory_name': directory.directory_name,
                    'directory_description': directory.directory_description,
                    'parent_directory_id': str(directory.parent_directory_id) if directory.parent_directory_id else None,
                    'parent_directory_name': parent_directory_name if parent_directory_name else None
                }
            return {'message': 'Directory not found'}
  
        #如果传入了目录名称，则根据名字模糊获取目录信息
        if directory_name:
            directories = self.session.query(AppDirectory).filter(
                AppDirectory.directory_name.ilike(f"%{directory_name}%"),  # 使用 ilike 进行模糊匹配
                AppDirectory.delete_flag == settings.DELETE_FLAG_FALSE
            ).all()
            tree = [self._build_node(directory) for directory in directories]
            return {'message': 'Directory tree retrieved successfully', 'data': tree}

        if parent_directory_id is None:
            page_no = args.get('page_no')
            page_size = args.get('page_size')
            directories = self.session.query(AppDirectory).filter(
                AppDirectory.parent_directory_id == None,
                AppDirectory.delete_flag == settings.DELETE_FLAG_FALSE
            ).slice((page_no - 1) * page_size, page_no * page_size).all()

            if not directories:
                return {'message': 'No directories found with no parent directory ID'}

            # Build the directory tree
            tree = [self._build_node(directory) for directory in directories]
            return {'message': 'Directory tree retrieved successfully', 'data': tree}
        try:
            directories = self.session.query(AppDirectory).filter(
                AppDirectory.parent_directory_id == parent_directory_id,
                AppDirectory.delete_flag == settings.DELETE_FLAG_FALSE
            ).all()

            if not directories:
                return {'message': 'No directories found for the given parent directory ID'}

            # 构建目录树
            tree = []
            for directory in directories:
                node = self._build_node(directory)
                tree.append(node)

            return {'message': 'Directory tree retrieved successfully', 'data': tree}

        except Exception as e:
            return {'message': str(e)}

    def _build_node(self, directory,app_name = None):
        """
        构建树形结构的节点
        Args:
            directory: 当前目录对象
        Returns:
            dict: 树形结构的目录节点
        """
        app_ids = self._get_app_ids(directory.directory_id,app_name)
        node = {
            'directory_id': str(directory.directory_id),
            'directory_name': directory.directory_name,
            'app_ids': app_ids,
            'app_num': len(app_ids),
            'directory_description': directory.directory_description,
            'children': [],  # 初始化子目录
            'parent_directory_id': str(directory.parent_directory_id) if directory.parent_directory_id else None
        }
        # 查找子目录
        subdirectories = self.session.query(AppDirectory).filter(
            AppDirectory.parent_directory_id == directory.directory_id,
            AppDirectory.delete_flag == settings.DELETE_FLAG_FALSE
        ).all()

        for subdirectory in subdirectories:
            node['children'].append(self._build_node(subdirectory,app_name))

        return node


    def _get_app_ids(self, directory_id,app_name = None):
        """
        获取目录下的所有应用ID
        Args:
            directory_id: 当前目录ID
        Returns:
            list: 当前目录下的所有应用ID
        """
        if app_name:
            app_ids = self.session.query(AigcApp.app_id).filter(
                AigcApp.parent_directory_id == directory_id,
                AigcApp.delete_flag == settings.DELETE_FLAG_FALSE,
                AigcApp.app_name.ilike(f"%{app_name}%")
            ).all()
        else:
            app_ids = self.session.query(AigcApp.app_id).filter(
                AigcApp.parent_directory_id == directory_id,
                AigcApp.delete_flag == settings.DELETE_FLAG_FALSE
            ).all()
        return [k.app_id for k in app_ids]

    def get_app_ids_by_directory(self, args):
        directory_id = args.get('directory_id')

        if not directory_id:
            return {'message': '目录 ID 是必需的'}

        try:
            with self.session_scope() as session:
                # 查找当前目录以及所有子目录
                app_ids = self._get_all_app_ids(directory_id, session)
                return app_ids

        except Exception as e:
            return {'message': str(e)}

    def _get_all_app_ids(self, parent_directory_id, session):
        """
        递归获取指定目录及其所有子目录下的应用 ID
        Args:
            parent_directory_id: 父目录 ID
            session: 数据库会话对象
        Returns:
            list: 知识库 ID 列表
        """
        # 查找当前目录下的所有应用 ID
        app_ids = session.query(AigcApp.app_id).filter(
            AigcApp.parent_directory_id == parent_directory_id,
            AigcApp.delete_flag == settings.DELETE_FLAG_FALSE  # 确保没有被删除的目录
        ).all()

        # 提取 knowledge_id 列表
        app_ids = [k.app_id for k in app_ids]
        # 查找所有子目录
        child_directories = session.query(AppDirectory).filter(
            AppDirectory.parent_directory_id == parent_directory_id,
            AppDirectory.delete_flag == settings.DELETE_FLAG_FALSE  # 确保没有被删除的子目录
        ).all()
        for child in child_directories:
            # 递归调用获取子目录的知识库 ID
            app_ids.extend(self._get_all_app_ids(child.directory_id, session))

        return app_ids

    def delete_app_tree(self, args):
        """
        删除指定目录及其所有子目录
        Args:
            directory_id: 要删除的目录 ID
        Returns:
            dict: 包含操作状态和消息的字典
        """
        directory_id = args.get('directory_id')
        try:
            with self.session_scope() as session:
                # 查找当前目录
                directory_to_delete = session.query(AppDirectory).filter(AppDirectory.directory_id == directory_id).first()

                if not directory_to_delete:
                    return {'message': '目录不存在'}

                # 递归删除子目录
                self._delete_subdirectories(directory_id, session)

                # 删除当前目录
                directory_to_delete.delete_flag = settings.DELETE_FLAG_TRUE
                directory_to_delete.parent_directory_id = None
                session.query(AigcApp).filter(AigcApp.parent_directory_id == directory_id).update({'parent_directory_id': None})
                session.commit()

                return {'message': '目录及其子目录删除成功'}

        except Exception as e:
            return {'message': str(e)}

    def _delete_subdirectories(self, parent_directory_id, session):
        """
        删除指定目录下的所有子目录
        Args:
            parent_directory_id: 父目录 ID，用于查询其所有子目录
            session: 数据库会话对象
        """
        # 查找所有子目录
        child_directories = session.query(AppDirectory).filter(AppDirectory.parent_directory_id == parent_directory_id).all()

        for child in child_directories:
            # 递归删除子目录的子目录
            self._delete_subdirectories(child.directory_id, session)
            # 删除子目录
            child.delete_flag = settings.DELETE_FLAG_TRUE
            child.parent_directory_id = None
            session.query(AigcApp).filter(AigcApp.parent_directory_id == child.directory_id).update({'parent_directory_id': None})


    def add_app_tree(self, args):
        logger.info(args)
        directory_id = args.get('directory_id')  # 如果传入了 directory_id，则更新该目录
        directory_name = args.get('directory_name')
        parent_directory_id = args.get('parent_directory_id',None)
        tp_user_id = args.get('tp_user_id')
        directory_description = args.get('directory_description')

        try:
            # 如果传入了 directory_id，先检查目录是否存在
            if directory_id:
                # 更新现有目录信息
                directory = self.session.query(AppDirectory).filter(AppDirectory.directory_id == directory_id,AppDirectory.delete_flag == settings.DELETE_FLAG_FALSE).first()
                if directory:
                    # 更新目录名称和父目录 ID
                    directory.directory_name = directory_name
                    directory.directory_description = directory_description

                    # for knowledge_id in knowledge_ids:
                    #     knowledge = self.session.query(AigcKnowledge).filter(AigcKnowledge.knowledge_id == knowledge_id,AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE).first()
                    #     if knowledge:
                    #         # 更新知识库的 parent_directory_id
                    #         knowledge.parent_directory_id = parent_directory_id
                    #     else:
                    #         return {'message': f'Knowledge ID {knowledge_id} does not exist'}

                    # 提交知识库更新到数据库
                    self.session.commit()

                    return {'message': 'Directory tree updated successfully'}
                else:
                    return {'message': f'Directory with ID {directory_id} does not exist'}

            # 如果没有传入 directory_id，则创建新的目录
            directory = AppDirectory(
                directory_name=directory_name,
                parent_directory_id=parent_directory_id,
                delete_flag=settings.DELETE_FLAG_FALSE,
                tp_user_id =tp_user_id,
                directory_description=directory_description
            )
            self.session.add(directory)

            # 提交目录数据到数据库
            self.session.commit()

            # # 更新 AigcKnowledge 表中的知识库目录关系
            #     for knowledge_id in knowledge_ids:
            #         knowledge = self.session.query(AigcKnowledge).filter(AigcKnowledge.knowledge_id == knowledge_id,AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE).first()
            #         if knowledge:
            #             # 更新知识库的 parent_directory_id
            #             knowledge.parent_directory_id = directory.directory_id
            #         else:
            #             return {'message': f'Knowledge ID {knowledge_id} does not exist'}

            #     # 提交知识库更新到数据库
            #     self.session.commit()

            return {'message': 'Directory tree updated successfully'}

        except Exception as e:
            self.session.rollback()  # 如果发生异常，回滚事务
            return {'message': str(e)}











    def create_app_rels(self, app_id, children, tp_user_id):
        """
        创建应用关联
        """
        with self.session_scope() as session:
            session_list = []
            for child in children:
                session_list.append(
                    RelAigcApp(
                        rel_id=get_snowflake_id(),
                        app_id_entry=app_id,
                        app_id=child,
                        tp_user_id=tp_user_id
                    )
                )
            if session_list:
                session.add_all(session_list)

    def create_app_qa_lib_rels(self, app_id, qa_lib_ids, tp_user_id):
        """
        创建应用关联
        """
        with self.session_scope() as session:
            session_list = []
            for qa_lib_id in qa_lib_ids:
                session_list.append(
                    RelAppQaLib(
                        rel_id=get_snowflake_id(),
                        app_id=app_id,
                        qa_lib_id=qa_lib_id,
                        tp_user_id=tp_user_id
                    )
                )
            if session_list:
                session.add_all(session_list)

    def update_app_qa_lib_rels(self, app_id, qa_lib_ids, tp_user_id):
        """
        更新应用关联
        """
        with self.session_scope() as session:
            session.query(RelAppQaLib).filter_by(app_id=app_id).delete()
            session_list = []
            for qa_lib_id in qa_lib_ids:
                session_list.append(
                    RelAppQaLib(
                        rel_id=get_snowflake_id(),
                        app_id=app_id,
                        qa_lib_id=qa_lib_id,
                        tp_user_id=tp_user_id
                    )
                )
            if session_list:
                session.add_all(session_list)
    def delete_app_qa_lib_rels(self, app_id):
        """
        删除应用关联
        """
        with self.session_scope() as session:
            session.query(RelAppQaLib).filter_by(app_id=app_id).delete()

    def get_app_qa_lib_rels(self, app_id):
        """
        获取应用关联
        """
        with self.session_scope() as session:
            result = session.query(RelAppQaLib).filter_by(app_id=app_id).all()
            return [str(item.qa_lib_id) for item in result]

    def create_app_prompt(self, prompt_info, app_id, tp_user_id):
        """
        创建提示词变量
        """
        with self.session_scope() as session:
            q = session.query(AigcAppPrompt).filter(AigcAppPrompt.app_id == app_id, AigcAppPrompt.delete_flag == settings.DELETE_FLAG_FALSE)
            if q.count():
                q.update({'delete_flag': settings.DELETE_FLAG_TRUE}, synchronize_session=False)
            for prompt in prompt_info:
                prompt_json = prompt.get('prompt_json')
                if isinstance(prompt_json, list):
                    prompt_json = json.dumps(prompt_json, ensure_ascii=False, cls=CJsonEncoder)
                prompt_id = get_snowflake_id()
                obj = AigcAppPrompt(prompt_id=prompt_id, app_id=app_id, tp_user_id=tp_user_id,
                                    prompt_type=prompt.get('prompt_type'), prompt_key=prompt.get('prompt_key'),
                                    field_name=prompt.get('field_name'), is_required=prompt.get('is_required') or 0,
                                    prompt_json=prompt_json)
                session.add(obj)

    def app_bind_knowledge(self, knowledge_ids, app_id, tp_user_id):
        """
        应用绑定知识id
        """
        with self.session_scope() as session:
            q = session.query(RelAppKnowledge).filter(
                RelAppKnowledge.app_id == app_id, RelAppKnowledge.delete_flag == settings.DELETE_FLAG_FALSE)
            if q.count():
                q.update({'delete_flag': settings.DELETE_FLAG_TRUE}, synchronize_session=False)
            for knowledge_id in knowledge_ids:
                rel_id = get_snowflake_id()
                obj = RelAppKnowledge(rel_id=rel_id, app_id=app_id, knowledge_id=knowledge_id, tp_user_id=tp_user_id,
                                      delete_flag=settings.DELETE_FLAG_FALSE)
                session.add(obj)

    def get_app_by_id(self, app_id):
        """
        根据id查询应用
        """
        with self.session_scope() as session:
            return session.query(AigcApp).filter(AigcApp.app_id == app_id, AigcApp.delete_flag == settings.DELETE_FLAG_FALSE).first()

    def get_app_rels(self, app_id=None):
        """
        根据id查询应用
        """
        with self.session_scope() as session:
            # 使用别名创建关联
            app_alias = aliased(AigcApp)
            app_entry_alias = aliased(AigcApp)

            q = session.query(
                RelAigcApp,
                app_alias.app_name.label('app_name'),
                app_alias.app_id.label('app_id'),
                app_alias.app_type.label('app_type'),
                app_alias.classify_name.label('classify_name'),
                app_alias.classify_prompt.label('classify_prompt'),
                app_alias.classify_priority.label('classify_priority'),
                app_alias.classify_target.label('classify_target'),
                app_alias.app_desc.label('app_desc'),
                app_alias.app_type.label('app_type'),
                app_alias.prompt.label('prompt'),
                app_alias.auth_scheme_id.label('auth_scheme_id'),  
                app_alias.tp_user_id.label('tp_user_id'),
                app_alias.add_time.label('add_time'),
                # app_alias.aigc_model.label('aigc_model'),
                app_alias.size.label('size'),
                app_alias.mini_score.label('mini_score'),
                app_alias.is_mixture.label('is_mixture'),
                app_alias.is_graph.label('is_graph'),
                app_alias.ques_enabled.label('ques_enabled'),
                app_alias.ques_prompt.label('ques_prompt'),
                app_alias.ques_keywords.label('ques_keywords'),
                app_alias.ques_enhance.label('ques_enhance'),
                app_alias.ques_replace.label('ques_replace'),
                app_alias.extract_prompt.label('extract_prompt'),
                app_alias.memory_size.label('memory_size'),
                app_alias.scope.label('scope'), 
                app_entry_alias.app_name.label('entry_app_name')
            ).join(app_alias, RelAigcApp.app_id == app_alias.app_id
            ).join(app_entry_alias, RelAigcApp.app_id_entry == app_entry_alias.app_id)

            if app_id:
                q = q.filter(RelAigcApp.app_id_entry == int(app_id))

            results = q.order_by(RelAigcApp.add_time.desc()).all()

            app_list = []
            for rel_aigc_app, app_name, app_id, app_type, classify_name, classify_prompt, classify_priority, classify_target,app_desc,app_type, prompt, auth_scheme_id,tp_user_id, add_time, size, mini_score, is_mixture, is_graph, ques_enabled,ques_prompt, ques_keywords,ques_enhance,ques_replace,extract_prompt,memory_size,scope,entry_app_name in results:
                app_list.append({
                    'app_id': app_id,
                    'app_name': app_name,
                    'app_type': app_type,
                    'classify_name': classify_name,
                    'classify_prompt': classify_prompt,
                    'classify_priority': classify_priority,
                    'classify_target': classify_target,
                    'app_desc': app_desc,
                    'app_type': app_type,
                    'prompt': prompt,
                    'auth_scheme_id': auth_scheme_id,
                    'tp_user_id': tp_user_id,
                    'add_time': add_time,
                    # 'model': aigc_model,
                    'size': size,
                    'mini_score': mini_score,
                    'is_mixture': is_mixture,
                    'is_graph': is_graph,
                    'ques_enabled': ques_enabled,
                    'ques_prompt': ques_prompt,
                    'ques_keywords': ques_keywords,
                    'ques_enhance': ques_enhance,
                    'ques_replace': ques_replace,
                    'extract_prompt': extract_prompt,
                    'memory_size': memory_size,
                    'scope': scope,
                })
            return app_list


    def update_app(self, **kwargs):
        """
        更新应用
        """
        with self.session_scope() as session:
            now = datetime.now()
            app_id = kwargs.pop('app_id')
            kwargs['update_time'] = now
            session.query(AigcApp).filter(AigcApp.app_id == app_id, AigcApp.delete_flag == settings.DELETE_FLAG_FALSE).update(kwargs, synchronize_session=False)

    def update_app_rels(self, tp_user_id, app_id, children):
        """
        更新应用关系
        """
        with self.session_scope() as session:
            session.query(RelAigcApp).filter(RelAigcApp.app_id_entry == app_id).delete()

            session_list = []
            for child in children:
                session_list.append(
                    RelAigcApp(
                        rel_id=get_snowflake_id(),
                        app_id_entry=app_id,
                        app_id=child,
                        tp_user_id=tp_user_id
                    )
                )
            session.add_all(session_list)


    def delete_app(self, app_id):
        """
        删除应用
        """
        with self.session_scope() as session:
            session.query(AigcApp).filter(AigcApp.app_id == app_id, AigcApp.delete_flag == settings.DELETE_FLAG_FALSE).update(
                {'delete_flag': settings.DELETE_FLAG_TRUE, 'update_time': datetime.now()}, synchronize_session=False)

    def delete_app_rels(self, app_id):
        """
        删除指定应用的关系
        """
        with self.session_scope() as session:
            session.query(RelAigcApp).filter(RelAigcApp.app_id_entry == app_id).delete()


    def delete_app_prompt(self, app_id):
        """
        删除应用下所有提示词变量
        """
        with self.session_scope() as session:
            session.query(AigcAppPrompt).filter(AigcAppPrompt.app_id == app_id, AigcAppPrompt.delete_flag == settings.DELETE_FLAG_FALSE).update(
                {'delete_flag': settings.DELETE_FLAG_TRUE, 'update_time': datetime.now()}, synchronize_session=False)

    def delete_app_knowledge_rel(self, app_id):
        """
        删除应用绑定的知识
        """
        with self.session_scope() as session:
            session.query(RelAppKnowledge).filter(
                RelAppKnowledge.app_id == app_id, RelAppKnowledge.delete_flag == settings.DELETE_FLAG_FALSE).update(
                {'delete_flag': settings.DELETE_FLAG_TRUE, 'update_time': datetime.now()}, synchronize_session=False)

    # @cache_ret('get_key_secret', 0, 600)
    def get_key_secret(self, aigc_type_id):
        """
        查询模型的key和secret
        1. 本库dict_aigc_type中读
        2. 未读到则向上查master
        """
        with self.session_scope() as session:
            obj = session.query(DictAigcType).filter(DictAigcType.aigc_type_id == aigc_type_id).first()
            if obj:
                if obj.api_key or obj.secret_key:
                    return obj.api_key, obj.secret_key
        with exchange.build_session()() as master_session:
            obj = master_session.query(ModelConf).filter(
                ModelConf.corpid == self.corpid, ModelConf.model_type == aigc_type_id).first()
            if obj:
                if obj.api_key or obj.secret_key:
                    return obj.api_key, obj.secret_key
        return '', ''

    def get_scheme_info(self, scheme_id):
        with self.session_scope() as session:
            obj = session.query(ContentAuthScheme).filter_by(auth_scheme_id=scheme_id).first()
            ret = {}
            if obj:
                ret = {
                    'scheme_name': obj.scheme_name,
                    'scheme_id': obj.auth_scheme_id,
                    'user_list': self.scheme_user_data(scheme_id, session),
                    'role_list': self.scheme_role_data(scheme_id, session),
                    'dpt_list': self.scheme_dpt_data(scheme_id, session),
                    'show_flag': obj.show_flag
                }
            return ret

    def get_model_by_model_path(self, model_path, aigc_type_id):
        with self.session_scope() as session:
            obj = session.query(AigcModel).filter_by(model_path=model_path, aigc_type_id=aigc_type_id, delete_flag = settings.DELETE_FLAG_FALSE).first()
            ret = {}
            if obj:
                ret = {
                    'aigc_model_id': obj.aigc_model_id,
                    'aigc_type_id': obj.aigc_type_id,
                    'model_name': obj.model_name,
                    'model_url': obj.model_url,
                    'model_path': obj.model_path,
                    'model_key': obj.aigc_type.api_key,
                }
            return ret

    @classmethod
    def scheme_user_data(cls, scheme_id, session):
        objs = session.query(RelAuthSchemeUser).filter_by(scheme_id=scheme_id).all()
        ret = list()
        for one in objs:
            ret.append({
                'scheme_name': one.scheme.scheme_name,
                'scheme_id': one.scheme_id,
                'user_id': one.tp_user_id,
                'user_name': one.tp_user.tp_user_name
            })
        return ret

    # 获取权限方案-角色信息
    @classmethod
    def scheme_role_data(cls, scheme_id, session):
        objs = session.query(RelAuthSchemeRole).filter_by(scheme_id=scheme_id).all()
        ret = list()
        for one in objs:
            ret.append({
                'scheme_name': one.scheme.scheme_name,
                'scheme_id': one.scheme_id,
                'role_id': one.role_id,
                'role_name': one.role.tp_user_role_name
            })
        return ret

    @classmethod
    def scheme_dpt_data(cls, scheme_id, session):
        objs = session.query(RelAuthSchemeDepart).filter_by(scheme_id=scheme_id).all()
        ret = list()
        for one in objs:
            ret.append({
                'scheme_name': one.scheme.scheme_name,
                'scheme_id': one.scheme_id,
                'dpt_id': one.depart_id,
                'dpt_name': one.depart.department_name
            })
        return ret

    def get_user_name(self, tp_user_id):
        with self.session_scope() as session:
            obj = session.query(TouchpointUser).filter_by(tp_user_id=tp_user_id).first()
            return obj.tp_user_name if obj else ''

    def get_ai_app_detail(self, app_id):
        obj = self.get_app_by_id(app_id)

        try:
            ques_replace = [] if not (ques_replace := getattr(obj, 'ques_replace', None)) else json.loads(ques_replace)
        except (json.JSONDecodeError, AttributeError) as e:
            ques_replace = []
        data = {
            'app_id': obj.app_id,
            'app_name': obj.app_name,
            'app_desc': obj.app_desc,
            'app_type': obj.app_type,
            'icon_url': obj.icon_url,
            'prompt': obj.prompt,
            'aigc_model_id': obj.aigc_model_id,
            'auth_scheme_id': obj.auth_scheme_id,
            'tp_user_id': obj.tp_user_id,
            'add_time': obj.add_time,
            'model_path': obj.aigc_model.model_path,
            'model_name': obj.aigc_model.model_name,
            'aigc_type_id': obj.aigc_model.aigc_type_id,
            'size': obj.size,
            'mini_score': obj.mini_score,
            'is_mixture': obj.is_mixture,
            'is_graph': obj.is_graph,
            'classify_name': obj.classify_name,
            'classify_prompt': obj.classify_prompt,
            'classify_target': obj.classify_prompt,
            'classify_priority': obj.classify_priority,
            'ques_enabled': obj.ques_enabled,
            'ques_prompt': obj.ques_prompt,
            'ques_keywords': obj.ques_keywords,
            'ques_enhance': [] if not obj.ques_enhance else obj.ques_enhance.split(","),
            'ques_replace': ques_replace,
            "is_rerank": obj.is_rerank,
            'rerank_size': obj.rerank_size,
            'rarank_ignore_score': obj.rarank_ignore_score,
            'global_percent': obj.global_percent,
            'pic_rarank_ignore_score': obj.pic_rarank_ignore_score,
            'is_pic_rerank': obj.is_pic_rerank,
            'graph_mode': obj.graph_mode,
            'digest_size': obj.digest_size,
            'digest_score': obj.digest_score,
            'scope':obj.scope,
            'dataset_enhance':obj.dataset_enhance,
            'is_global': obj.is_global,
            'global_rerank_num': obj.global_rerank_num,
            'need_suggestion': obj.need_suggestion,
            'model_auto_enable': obj.model_auto_enable,
            'is_multi_qa':obj.is_multi_qa,
            'multi_qa_size':obj.multi_qa_size,
        }

        rel = self.get_app_rels(app_id)
        children = []
        for child in rel:
            try:
                ques_replace_child = [] if not (ques_replace := getattr(child, 'ques_replace', None)) else json.loads(ques_replace)
            except (json.JSONDecodeError, AttributeError) as e:
                ques_replace_child = []
            children.append({
                # use str for comparison
                "app_id": child['app_id'],
                "app_name": child['app_name'],    # notice
                "app_type": child['app_type'],
                "classify_name": child['classify_name'],
                "classify_prompt": child['classify_prompt'],
                "classify_priority": child['classify_priority'],
                "classify_target": child['classify_target'].split(",") if child['classify_target'] else [],
                'app_desc': child.get('app_desc'),
                'app_type': child.get('app_type'),
                'prompt': child.get('prompt'),
                'aigc_model_id': child.get('aigc_model_id'),
                'auth_scheme_id': child.get('auth_scheme_id'),
                'tp_user_id': child.get('tp_user_id'),
                'add_time': child.get('add_time'),
                'model_path': child.get('aigc_model', {}).get('model_path'),
                'aigc_type_id': child.get('aigc_model', {}).get('aigc_type_id'),
                'size': child.get('size'),
                'mini_score': child.get('mini_score'),
                'is_mixture': child.get('is_mixture'),
                'is_graph': child.get('is_graph'),
                'ques_enabled': child.get('ques_enabled'),
                'ques_prompt': child.get('ques_prompt'),
                'ques_keywords': child.get('ques_keywords'),
                'ques_enhance': [] if not child.get('ques_enhance') else child.get('ques_enhance').split(","),
                'ques_replace': ques_replace_child,
                'extract_prompt': child.get('extract_prompt'),
                'memory_size': child.get('memory_size'),
                'scope': child.get('scope')
            })
        data["children"] = children
        return data

    def creat_app_session(self, app_id, tp_user_id, session_name, prompt_list, session_type='search', report_date=None):
        """
        创建会话
        """
        session_id = get_snowflake_id()
        json_prompt = json.dumps(prompt_list)
        obj = AigcAppSession(session_id=session_id, app_id=app_id, tp_user_id=tp_user_id, session_name=session_name,
                             prompt_list=json_prompt, session_type=session_type, report_date=report_date)

        self.session.add(obj)
        self.session.commit()
        return str(session_id)

    def get_menu_supplement(self, view_id=''):
        with self.session_scope() as session:
            q = session.query(AigcMenuSupplement)
            if view_id:
                q = q.filter_by(view_id=view_id)
                return q.first()
            return q.all()

    def get_app_session_list(self, kwargs):
        with self.session_scope() as session:
            q = session.query(AigcAppSession).filter_by(app_id=kwargs['app_id'], delete_flag = settings.DELETE_FLAG_FALSE)
            time_range = kwargs.get('add_time')
            if time_range:
                start, end = time_range.split(',')
                q = q.filter(AigcAppSession.add_time >= str_to_date(start), AigcAppSession.add_time <= str_to_date(end))
            if kwargs.get('session_name'):
                q = q.filter(AigcAppSession.session_name.like('%' + kwargs['session_name'] + '%'))
            if kwargs.get('session_type'):
                q = q.filter_by(session_type=kwargs['session_type'])
            q = q.order_by(AigcAppSession.is_top.desc(), AigcAppSession.add_time.desc())
            # logger.info(q.statement.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True}))

            count = q.count()
            objs = q.slice((int(kwargs['page_no']) - 1) * int(kwargs['page_size']), int(kwargs['page_size']) * int(kwargs['page_no'])).all()
            data_list = list()
            for obj in objs:
                data_list.append({
                    'session_id': str(obj.session_id),
                    'app_id': str(obj.app_id),
                    'session_name': obj.session_name,
                    'is_top': obj.is_top,
                    'add_time': obj.add_time,
                    'qa_no': 0
                })
            return {'total': count, 'po': kwargs['page_no'], 'data_list': data_list}

    def update_app_session(self, session_id, args):
        with self.session_scope() as session:
            session.query(AigcAppSession).filter_by(session_id=session_id).update(args)
            return session_id

    def get_session_detail(self, session_id):
        with self.session_scope() as session:
            obj = session.query(AigcAppSession).filter_by(session_id=session_id).first()
            data = {
                'session_id': str(obj.session_id),
                'app_id': str(obj.app_id),
                'session_name': obj.session_name,
                'is_top': obj.is_top,
                'add_time': obj.add_time,
                'prompt_list': json.loads(obj.prompt_list),
                'session_type': obj.session_type,
                'report_date': obj.report_date
            }
            return data

    def get_content_data(self, content_ids):
        with self.session_scope() as session:
            res = []
            content_ids = content_ids.split(',') if isinstance(content_ids, str) else content_ids
            for obj in session.query(ContentInfo).filter(
                    ContentInfo.content_id.in_(content_ids), ContentInfo.delete_flag == settings.DELETE_FLAG_FALSE).all():
                down_loads_url = obj.pic_url or obj.video_url or obj.docomnet_url or obj.copywriting_pic_url or obj.link_url
                qrcode_url = ''
                if down_loads_url:
                    down_loads_url = [x for x in down_loads_url.split(',') if x]
                    if down_loads_url and down_loads_url[0]:
                        qrcode_url = self.get_qrcode(down_loads_url[0])
                res.append({
                    'content_id': obj.content_id,
                    'content_name': obj.content_name,
                    'content_type_name': obj.content_type.content_type_name,
                    'add_time': obj.add_time.strftime('%Y-%m-%d %H:%M:%S') if obj.add_time else '',
                    'down_loads_url': down_loads_url,
                    'qrcode_url': qrcode_url,
                    'thumbnail_url': obj.thumbnail_url or self.get_order_img_url(obj),
                })
            return res

    @classmethod
    def get_order_img_url(cls, obj, type_code=1, add_suffix=False):
        img_url = obj.cover_pic_url or obj.copywriting_pic_url
        if img_url and img_url.split('.')[-1].lower() in ImagTypeList and type_code == 1:
            return img_url
        else:
            data_list = [x for x in (obj.pic_url or obj.video_url or obj.radar_oss or obj.docomnet_url or '').split(',') if x]
            video_list = list()
            for i in data_list:
                file_type_suffix = data_list[0].split('.')[-1].lower()
                if file_type_suffix in ImagTypeList:
                    return data_list[0]
                elif file_type_suffix in VideoTypeList:
                    video_list.append(i)
            if video_list:
                if add_suffix:
                    return video_list[0] + OSSVideoCoverImgSuffix
                else:
                    if obj.video_url:
                        return video_list[0]
            return obj.content_type.default_pic_url

    def get_qrcode(self, url):
        qrcode_data = generate_qrcode(url)
        qrcode_url = ''
        if qrcode_data:
            file_name = f'{get_uuid()}_{self.corpid}/qr_code/{str(get_uuid())}.png'
            oss = OSSUtil(self.corpid)
            oss.upload(file_name, qrcode_data)
            file_link = oss.get_public_url(file_name)
            qrcode_url = file_link
        return qrcode_url

    def get_ai_question_number(self, data_name):
        with self.session_scope() as session:
            obj = session.query(AiQuesionNumber).filter(AiQuesionNumber.data_name == data_name).filter(
                AiQuesionNumber.delete_flag == settings.DELETE_FLAG_FALSE).first()

            if obj:
                if obj.after_question == "" or obj.after_question == None:
                    after_question = []
                else:
                    after_question = json.loads(obj.after_question)

                data = {
                    'ai_question_number_id': obj.ai_question_number_id,
                    'data_name': obj.data_name,
                    'component_code': obj.component_code,
                    'c': obj.c,
                    'm': obj.m,
                    'w': obj.w,
                    'f': obj.f,
                    'r': obj.r,
                    'uri': obj.uri,
                    'description': obj.description,
                    'comment': obj.comment,
                    'analysis_prompts': obj.analysis_prompts,
                    'after_question': after_question,
                    'flag_after_question': obj.flag_after_question,
                    'drill_item': obj.drill_item
                }
                return data
            else:
                return {}

    def get_radar_data(self):
        with self.session_scope() as session:
            return session.query(ClientRadar).filter(
                ClientRadar.delete_flag == str(settings.DELETE_FLAG_FALSE), ClientRadar.publish_status == "0").all()
        
    def get_app_list_by_auth(self, args):
           with self.session_scope() as session:
                # 1. 查询用户个人知识库的 ID
                if args['all_scheme_ids']:  
                    personal_knowledge_query1 = session.query(AigcKnowledge.knowledge_id).filter(
                        AigcKnowledge.scope == 1,
                        AigcKnowledge.auth_scheme_id.in_(args['all_scheme_ids']),
                        AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE
                    )
                    personal_knowledge_query2 = session.query(AigcKnowledge.knowledge_id).filter(
                        AigcKnowledge.scope == 1,
                        AigcKnowledge.tp_user_id == args["tp_user_id"],
                        AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE
                    )
                    personal_knowledge_query = personal_knowledge_query1.union(personal_knowledge_query2)
                else:
                    personal_knowledge_query = session.query(AigcKnowledge.knowledge_id).filter(
                        AigcKnowledge.scope == 1,
                        AigcKnowledge.tp_user_id == args["tp_user_id"],
                        AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE
                    )
                # 2. 查询用户有权限访问的公共知识库的 ID
                #如果all_scheme_ids存在，则查询有权限的公共知识库，否则查询所有公共知识库
                if args['all_scheme_ids']:
                    public_knowledge_query = session.query(AigcKnowledge.knowledge_id).filter(
                        AigcKnowledge.scope == 0,
                        AigcKnowledge.auth_scheme_id.in_(args['all_scheme_ids']),
                        AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE
                    )
                else:
                    public_knowledge_query = session.query(AigcKnowledge.knowledge_id).filter(
                        AigcKnowledge.scope == 0,
                        AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE
                    )
                # 3. 合并个人知识库和公共知识库的查询
                accessible_knowledge_ids = personal_knowledge_query.union(public_knowledge_query).subquery()

                # 4. 查询用户有权限访问的普通应用的 ID
                accessible_apps_query = session.query(
                    RelAppKnowledge.app_id.label("app_id"),
                    literal("").label('entry_id'), 
                    RelAppKnowledge.knowledge_id == AigcKnowledge.knowledge_id,
                ).filter(
                    RelAppKnowledge.knowledge_id.in_(accessible_knowledge_ids),
                ).distinct()

                # 5. 根据普通应用的ID查询聚合应用的ID
                accessible_apps_query_sub = accessible_apps_query.subquery()
                initial_query = session.query(
                    RelAigcApp.app_id.label("app_id"),
                    RelAigcApp.app_id_entry.label("entry_id"),
                    literal("").label('knowledge_id')
                ).filter(
                    RelAigcApp.app_id.in_(session.query(accessible_apps_query_sub.c.app_id))
                )

                # 去重处理，转为子查询
                final_app_ids = self.get_all_accessible_app_ids(session, initial_query)

                # 将最终的 app_id 列表用于后续查询
                aggregated_app_query = session.query(
                    AigcApp.app_id,
                    literal("").label('entry_id'),
                    literal("").label('knowledge_id')
                ).filter(
                    AigcApp.app_id.in_(final_app_ids),
                    AigcApp.delete_flag == settings.DELETE_FLAG_FALSE
                )
                
                accessible_apps = accessible_apps_query.union_all(aggregated_app_query,initial_query)
                accessible_apps = accessible_apps.subquery()

                # 合并所有 app_id 和 entry_id 为一列
                app_id_query = session.query(
                    accessible_apps.c.app_id.label("app_id")
                ).union_all(
                    session.query(accessible_apps.c.entry_id.label("app_id"))
                ).subquery()

                # 查询结果
                q = session.query(app_id_query.c.app_id)

                # 统计结果数量
                count = q.count()

                # 打印调试信息（可选）
                logger.info(f"Count: {count}")


                objs = q.limit(int(args['page_size'])).offset(
                    (int(args['page_no']) - 1) * int(args['page_size'])
                ).all()

                app_ids = [obj.app_id for obj in objs]

                objs = session.query(AigcApp).filter(
                    AigcApp.app_id.in_(app_ids),
                    AigcApp.delete_flag == settings.DELETE_FLAG_FALSE
                ).all()
                # 构造返回数据
                data_list = []
                for obj in objs:                # for detail api   
                    data_list.append({
                        'app_name': obj.app_name or '', 
                        'app_id': str(obj.app_id), 
                        'app_desc': obj.app_desc,
                        'icon_url': obj.icon_url or '',               
                    })

                return {
                    'total': count,
                    'page_no': args['page_no'],
                    'data_list': data_list
                }
            
    def get_all_accessible_app_ids(self, session, initial_entry_ids):
        """
        非递归实现查询所有可能的 app_id 和 entry_id。
        :param session: SQLAlchemy session
        :param initial_entry_ids: 初始 entry_id 列表
        :return: 最终的 app_id 集合
        """
        # 1. 查询所有 app_id 和 entry_id 的关系
        all_relations = session.query(
            RelAigcApp.app_id,
            RelAigcApp.app_id_entry
        ).distinct().all()
        logger.info(f"all_relations::: {all_relations}")
        
        # 2. 构建关系字典
        reverse_relation_dict = {}
        for app_id, entry_id in all_relations:
            if app_id not in reverse_relation_dict:
                reverse_relation_dict[app_id] = []
            reverse_relation_dict[app_id].append(entry_id)
        logger.info(f"relation_dict::: {reverse_relation_dict}")
        
        # 3. 非递归实现获取所有相关的 app_id
        final_app_ids = set()  # 存储最终的 app_id 集合
        logger.info(f"initial_entry_ids::: {initial_entry_ids.all()}")
        
        # 创建一个栈（list）用于保存需要处理的 entry_id
        stack = [entry_id for _, entry_id, _ in initial_entry_ids]  # 提取 entry_id
        logger.info(f"stack::: {stack}")
        
        # 创建一个集合，用于记录已处理的 entry_id，避免重复处理
        processed = set() 
        
        while stack:
            current_entry_id = stack.pop()
            logger.info(f"current_entry_id::: {current_entry_id}")
            
            # 如果这个 entry_id 没有处理过
            if current_entry_id not in processed:
                processed.add(current_entry_id)  # 标记为已处理
                
                # 检查当前 entry_id 是否在关系字典中有对应的 app_id
                if current_entry_id in reverse_relation_dict:
                    for app_id in reverse_relation_dict[current_entry_id]:
                        logger.info(f"当前 entry_id {current_entry_id} 关联的 app_id: {app_id}")
                        final_app_ids.add(app_id)  # 将 app_id 添加到最终结果集
                    
                    # 将当前 entry_id 的关联 entry_id 加入栈中继续处理
                    for next_entry_id in reverse_relation_dict[current_entry_id]:
                        if next_entry_id not in processed:
                            stack.append(next_entry_id)  # 将未处理的 entry_id 加入栈中
                else:
                    logger.info(f"当前 entry_id {current_entry_id} 在 relation_dict 中没有找到关联的 app_id")
        
        logger.info(f"final_app_ids::: {final_app_ids}")
        return final_app_ids

    def app_bind_library(self, library_ids, app_id, tp_user_id):
        """
        应用绑定图表库id
        """
        with self.session_scope() as session:
            q = session.query(RelAppGraphLibrary).filter(
                RelAppGraphLibrary.app_id == app_id, RelAppGraphLibrary.delete_flag == settings.DELETE_FLAG_FALSE)
            if q.count():
                q.update({'delete_flag': settings.DELETE_FLAG_TRUE}, synchronize_session=False)
            for library_id in library_ids:
                rel_id = get_snowflake_id()
                obj = RelAppGraphLibrary(rel_id=rel_id, app_id=app_id, library_id=library_id, tp_user_id=tp_user_id,
                                       delete_flag = settings.DELETE_FLAG_FALSE)
                session.add(obj)

    def delete_app_library_rel(self, app_id):
        """
        删除应用绑定的图表库
        """
        with self.session_scope() as session:
            session.query(RelAppGraphLibrary).filter(
                RelAppGraphLibrary.app_id == app_id, RelAppGraphLibrary.delete_flag == settings.DELETE_FLAG_FALSE).update(
                {'delete_flag': settings.DELETE_FLAG_TRUE, 'update_time': datetime.now()}, synchronize_session=False)

    def get_library_ids_by_app_ids(self, app_ids):
        """
        根据app_ids查询绑定图表库ids
        """
        with self.session_scope() as session:
            # 基础查询
            q = session.query(RelAppGraphLibrary) \
                .join(
                ChatbiDataGraphLibrary,
                ChatbiDataGraphLibrary.library_id == RelAppGraphLibrary.library_id
            ) \
                .filter(
                RelAppGraphLibrary.app_id.in_(app_ids),
                RelAppGraphLibrary.delete_flag == settings.DELETE_FLAG_FALSE
            )
            return q.all()

    def get_aigc_model_id_by_has_reason(self, has_reason):
        with self.session_scope() as session:
            q = session.query(AigcModel).filter(AigcModel.delete_flag == settings.DELETE_FLAG_FALSE, AigcModel.status == 1)
            if has_reason == 1:
                q = q.filter(AigcModel.has_reason == 1)
            model_info = q.order_by(AigcModel.model_name).first()
            return model_info.aigc_model_id, model_info.aigc_type_id, model_info.model_path, model_info.model_name

    def get_app_session_detail(self, session_id, app_id=None):
        with self.session_scope() as session:
            query = session.query(AigcAppSession).filter_by(session_id=session_id)
            if app_id is not None:
                query = query.filter_by(app_id=app_id)
            obj = query.first()

            if not obj:
                return None

            data = {
                'session_id': str(obj.session_id),
                'app_id': str(obj.app_id),
                'session_name': obj.session_name,
                'is_top': obj.is_top,
                'add_time': obj.add_time,
                'prompt_list': json.loads(obj.prompt_list),
                'session_type': obj.session_type,
                'report_date': obj.report_date
            }
            return data

    def create_app_session_with_id(self, session_id, app_id, tp_user_id, session_name, prompt_list,
                                   session_type='search', report_date=None):
        """
        使用已有的session_id创建会话
        """
        json_prompt = json.dumps(prompt_list)
        obj = AigcAppSession(session_id=session_id, app_id=app_id, tp_user_id=tp_user_id, session_name=session_name,
                             prompt_list=json_prompt, session_type=session_type, report_date=report_date)

        self.session.add(obj)
        self.session.commit()
        return str(session_id)

    def add_app_rel_flow(self, app_id, flow_ids):
        add_objs = list()
        with self.session_scope() as sesssion:
            for i in flow_ids:
                add_objs.append(RelAppFlowApp(version_id=i, app_id=app_id))
            if add_objs:
                sesssion.bulk_save_objects(add_objs)
        return app_id

    def del_app_rel_flow(self, app_id):
        with self.session_scope() as sesssion:
            sesssion.query(RelAppFlowApp).filter_by(app_id=app_id).delete(synchronize_session=False)
        return app_id

    def get_app_rel_flow(self, app_id):
        objs = self.session.query(RelAppFlowApp).filter_by(app_id=app_id).all()
        ret = list()
        for one in objs:
            ret.append({
                'version_id': one.version_id,
                'name':  one.version.name,
                'desc': one.version.desc,
                'version_name': one.version.name,
                'app_id': one.version.flow_app_id,
                'app_name': one.version.flow_app.name
            })
        return ret


class ServiceOrm(BaseModel):

    def __flow_app_analyse(self, obj):
        if not obj:
            return {}
        ret = {'id': obj.id, 'name': obj.name, 'add_time': obj.add_time, 'status': obj.status, 'desc': obj.desc,
               'icon': obj.icon, 'publish': obj.publish, 'title': obj.name}
        return ret

    def flow_app_list(self, key_word):
        q = self.session.query(FlowApp).filter(FlowApp.delete_flag == 0, FlowApp.status == 1)
        q = q.join(FlowAppVersion, FlowAppVersion.flow_app_id == FlowApp.id) \
            .filter(FlowAppVersion.delete_flag == 0, FlowAppVersion.status == 1)
        if key_word:
            q = q.filter(or_(FlowApp.name.like(f'%{key_word}%'), FlowApp.desc.like(f'%{key_word}%')))
        q = q.order_by(FlowApp.add_time.desc())
        objs = q.all()
        ret = list()
        for obj in objs:
            ret.append(self.__flow_app_analyse(obj))
        return ret
