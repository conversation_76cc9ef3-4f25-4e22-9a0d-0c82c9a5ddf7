import settings
from datetime import datetime
from typing import Optional, List, Dict
from sqlalchemy import func
from sqlalchemy.orm import aliased

from module import BaseModel
from module.mysql.models import *
from utils.tools import get_snowflake_id


class ChatFreeOrm(BaseModel):
    """
    ChatFreefils CRUD
    """

    def get_chat_free_files_list(self, page_no: Optional[int] = None,
                                 page_size: Optional[int] = None,
                                 date_view_name: Optional[str] = None):
        # 确保页码和每页条目数为正整数
        page_no = max(page_no, 1) if page_no else 1
        page_size = max(page_size, 1) if page_size else 10

        # 计算偏移量
        offset = (page_no - 1) * page_size

        # 构建查询条件
        conditions = [
            ChatbiDataView.delete_flag == settings.DELETE_FLAG_FALSE,
            ChatbiDataSource.delete_flag == settings.DELETE_FLAG_FALSE,
            ChatbiDataView.status == 1,
            ChatbiDataSource.data_source_type == 1,
            ChatbiDataView.data_source_id == ChatbiDataSource.data_source_id  # 添加联表查询条件
        ]

        if date_view_name:
            conditions.append(ChatbiDataView.date_view_name.like(f"%{date_view_name}%"))

        # 获取总条数
        total_count = self.session.query(func.count(ChatbiDataView.data_view_id)).filter(
            *conditions
        ).scalar()

        # 获取数据列表
        query = self.session.query(
            ChatbiDataView.data_view_id,
            ChatbiDataView.data_source_id,
            ChatbiDataView.date_view_name,
            ChatbiDataSource.file_path
        ).filter(*conditions).offset(offset).limit(page_size)

        objs = query.all()

        model_branch_list = []
        for obj in objs:
            model_branch_list.append({
                'data_view_id': str(obj.data_view_id),
                'data_source_id': str(obj.data_source_id),
                'date_view_name': obj.date_view_name,
                'file_path': obj.file_path
            })

        results = {
            'page_no': page_no,
            'page_size': page_size,
            'total': total_count,
            'data_list': model_branch_list
        }

        return results

    """
    SELECT 
    chatbi_data_source.file_path 
    FROM 
        chatbi_data_view
    JOIN 
        chatbi_data_source 
    ON 
        chatbi_data_view.data_source_id = chatbi_data_source.data_source_id
    WHERE 
        chatbi_data_view.data_view_id = :data_view_id 
        AND chatbi_data_view.delete_flag = 0 
        AND chatbi_data_view.status = 1 
        AND chatbi_data_source.delete_flag = 0 
        AND chatbi_data_source.file_upload_status = 1
    LIMIT 1;

    SELECT 
    chatbi_data_source.file_path
    FROM 
        chatbi_data_view
    JOIN 
        chatbi_data_source 
    ON 
        chatbi_data_view.data_source_id = chatbi_data_source.data_source_id
    WHERE 
        chatbi_data_view.data_view_id = :data_view_id
        AND chatbi_data_view.delete_flag = 0
        AND chatbi_data_view.status = 1
        AND chatbi_data_source.delete_flag = 0
        AND chatbi_data_source.file_upload_status = 1
    LIMIT 1;
    """

    # def get_excel_file_path_by_view_id(self, data_view_id):

    #     print(f"=== start get_excel_file_path_by_view_id")

    #     conditions = [
    #         ChatbiDataView.data_view_id == data_view_id,
    #         ChatbiDataView.delete_flag == 0,
    #         ChatbiDataView.status == 1,
    #         ChatbiDataSource.delete_flag == 0,
    #         ChatbiDataSource.file_upload_status == 1
    #     ]

    #     # 构建查询
    #     query = self.session.query(ChatbiDataSource.file_path).join(
    #         ChatbiDataSource,
    #         ChatbiDataView.data_source_id == ChatbiDataSource.data_source_id
    #     ).filter(*conditions)

    #     # 执行查询
    #     result = query.first()

    #     # 返回文件路径
    #     print(f"=== end get_excel_file_path_by_view_id, result: {result}")
    #     if result:
    #         return result.file_path
    #     else:
    #         return None

    def get_excel_file_path_by_view_id(self, data_view_id):

        conditions = [
            ChatbiDataView.data_view_id == data_view_id,
            ChatbiDataView.delete_flag == settings.DELETE_FLAG_FALSE,
            ChatbiDataView.status == 1,
            ChatbiDataSource.delete_flag == settings.DELETE_FLAG_FALSE,
            ChatbiDataSource.file_upload_status == 1
        ]

        # 构建查询
        query = self.session.query(ChatbiDataSource.file_path).select_from(ChatbiDataView).join(
            ChatbiDataSource,
            ChatbiDataView.data_source_id == ChatbiDataSource.data_source_id
        ).filter(*conditions)

        # 执行查询
        result = query.first()

        # 返回文件路径
        if result:
            return result.file_path
        else:
            return None


if __name__ == '__main__':
    corpid = "wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ"
    chat_free = ChatFreeOrm(corpid)
    # chat_free.get_excel_file_path_by_view_id(5)
    chat_free.get_excel_file_path_by_view_id(5)