# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: question_orm.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 4月 25, 2024
# ---
import settings
from module import BaseModel
from module.mysql.models import *
from sqlalchemy import func, or_, and_
from utils.tools import get_snowflake_id
from sqlalchemy.dialects import postgresql


class QuestionOrm(BaseModel):

    def get_rel_question_tpu_train_data(self, next_id):
        """
        获取个人回答训练数a据集
        """
        q = self.session.query(Question.question_name, DictQuestionType.question_type_name, RelQuestionTpu.content,
                               RelQuestionTpu.rel_question_tup_id, DictQuestionType.form, RelQuestionTpu.answer_type,
                               RelQuestionTpu.add_time, RelQuestionTpu.delete_flag). \
            join(Question, Question.question_id == RelQuestionTpu.question_id).join(
            DictQuestionType,
            DictQuestionType.question_type_id == RelQuestionTpu.question_type_id).filter(
            RelQuestionTpu.delete_flag == settings.DELETE_FLAG_FALSE).order_by(RelQuestionTpu.rel_question_tup_id)

        if next_id and next_id != 'all':
            q = q.filter(RelQuestionTpu.rel_question_tup_id > next_id)

        objs = q.all()
        res = []
        for obj in objs:
            res.append({
                'question_name': obj.question_name,
                'question_type_name': obj.question_type_name,
                'content': obj.content,
                'rel_question_tup_id': obj.rel_question_tup_id,
                'answer_type': obj.answer_type,
                'add_time': str(obj.add_time),
                'delete_flag': obj.delete_flag
            })
        return res

    def get_question_point_plan_train_data(self, next_id):
        """
        获取问题及方案 训练数据集
        """
        q = self.session.query(QuestionPointPlan).filter(QuestionPointPlan.delete_flag == settings.DELETE_FLAG_FALSE)
        if next_id and next_id != 'all':
            q = q.filter(QuestionPointPlan.qpp_id > next_id)

        objs = q.all()
        data_list = []
        for obj in objs:
            data_list.append({
                'describe': obj.describe,
                'sore_point': obj.sore_point,
                'question_type_id': obj.question_type_id,
                'function_point': obj.function_point,
                'question_type_name': obj.question_type.question_type_name,
                'qpp_id': obj.qpp_id,
                'answer_type': obj.answer_type,
                'add_time': obj.add_time
            })
        return data_list

    def get_chatbi_datas(self, data_names):

        objs = self.session.query(AiQuesionNumber).filter(AiQuesionNumber.data_name.in_(data_names)).all()

        data_list = []

        for obj in objs:
            data_list.append({
                'data_name': obj.data_name,
                'component_code': obj.component_code,
                'c': obj.c,
                'm': obj.m,
                'w': obj.w,
                'f': obj.f,
                'uri': obj.uri,
                'r': obj.r,
                'description': obj.description,
                'comment': obj.comment,
                'analysis_prompts': obj.analysis_prompts
            })

        return data_list
