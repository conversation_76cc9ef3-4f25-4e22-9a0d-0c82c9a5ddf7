import settings
from module import BaseModel
from module.mysql.manager import *


# 租户信息管理
class CustomerM(BaseModel):

    # 产品购买者数据
    def get_all_customer(self, status):
        all_datas = self.session.query(SignCustomer).filter_by(delete_flag=str(settings.DELETE_FLAG_FALSE), status=status).all()
        return all_datas

    # 租户组织数据
    def get_all_organize(self):
        all_datas = self.session.query(Organize).all()
        return all_datas

    # 租户mysql数据库配置信息
    def get_all_mysql_conf(self):
        all_datas = self.session.query(MysqlConf).all()
        return all_datas

    # 租户mongo数据库配置信息
    def get_all_mongo_conf(self):
        all_datas = self.session.query(MongoConf).all()

        return all_datas

    # 租户es数据库配置信息
    def get_all_es_conf(self):
        all_datas = self.session.query(EsConf).all()
        return all_datas

    # 租户阿里云OSS配置信息
    def get_all_oss_conf(self):
        all_datas = self.session.query(OssConf).all()
        return all_datas

    # 租户短信配置信息
    def get_all_sms_conf(self):
        all_datas = self.session.query(SmsConf).all()
        return all_datas

    # 租户小程序配置信息
    def get_all_mini_conf(self):
        all_datas = self.session.query(WechatMiniConf).all()
        return all_datas

    # 租户企微授权信息
    def get_all_wework_conf(self):
        all_datas = self.session.query(WeworkConf).all()
        return all_datas

    # 租户应用模版信息
    def get_all_wework_suite(self):
        all_datas = self.session.query(WeworkSuite).all()
        return all_datas

    # 模型配置
    def get_all_model_conf(self):
        all_datas = self.session.query(ModelConf).all()
        return all_datas

    def register_customer_info(self, corpid, suite_id, permanent_code, name, agentid, logo, auth_user_userid,
                               open_userid, auth_corp_info):
        org_obj = self.session.query(Organize).filter_by(corpid=corpid).first()
        suit_obj = WeworkConf(corpid=corpid, suite_id=suite_id, permanent_code=permanent_code, name=name,
                              agentid=agentid, logo=logo, auth_user_userid=auth_user_userid, open_userid=open_userid)
        if not org_obj:
            with self.session_scope() as session:
                org_obj = Organize(corpid=corpid, name=auth_corp_info['corp_name'], location=auth_corp_info['location'],
                                   corp_sub_industry=auth_corp_info['corp_sub_industry'],
                                   corp_industry=auth_corp_info['corp_industry'],
                                   corp_scale=auth_corp_info['corp_scale'],
                                   corp_wxqrcode=auth_corp_info['corp_wxqrcode'],
                                   subject_type=auth_corp_info['subject_type'],
                                   corp_square_logo_url=auth_corp_info['corp_square_logo_url'],
                                   corp_type=auth_corp_info['corp_type'], regist_flag='1')
                session.add(org_obj)
                session.commit()
                session.add(suit_obj)
                session.commit()
        else:
            with self.session_scope() as session:
                org_obj.regist_flag = '1'
                _suit_obj = session.query(WeworkConf).filter_by(corpid=corpid, suite_id=suite_id).first()
                if _suit_obj:
                    _suit_obj.permanent_code = permanent_code
                    _suit_obj.cancel_flag = '0'
                    _suit_obj.name = name
                    _suit_obj.logo = logo
                    _suit_obj.agentid = agentid
                else:
                    session.add(suit_obj)
        return True

    def del_register_customer(self, corpid, suite_id):
        with self.session_scope() as session:
            suit_obj = session.query(WeworkConf).filter_by(corpid=corpid, suite_id=suite_id).first()
            if suit_obj:
                suit_obj.cancel_flag = '1'
        return True
