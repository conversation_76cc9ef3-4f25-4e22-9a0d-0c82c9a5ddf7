# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: content_info_orm.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 4月 15, 2024
# ---
import uuid
import settings
from module import BaseModel
from module.mysql.models import *
from modeler.mysql.user_orm import *
from sqlalchemy import func, or_, and_
from utils.tools import get_snowflake_id
from sqlalchemy.dialects import postgresql
from flask import request
from horticulture.permission import permit_map_code, SuperManager, ContentManager
from lib_func.logger import logger

class ContentInfoOrm(BaseModel):

    def get_content_train_data(self, content_id):
        """
        获取素材
        """
        subq = self.session.query(func.group_concat(Tag.tag_name).label('content_tags'), RelContentTag.content_id). \
            join(Tag, RelContentTag.tag_id == Tag.tags_id).group_by(
            RelContentTag.content_id).subquery()
        q = self.session.query(ContentInfo.content_id, ContentInfo.content_name, ContentInfo.comment, ContentInfo.key_words,
                               ContentInfo.add_time, ContentInfo.content_type_id, subq.c.content_tags, ContentInfo.info,
                               ContentInfo.publish_writing, ContentInfo.publish_status, ContentInfo.copywriting). \
            outerjoin(subq, ContentInfo.content_id == subq.c.content_id).filter(ContentInfo.content_id == content_id,
                                                                                ContentInfo.delete_flag == settings.DELETE_FLAG_FALSE)

        obj = q.first()
        data = dict()
        if obj:
            data.update({
                'content_id': obj.content_id,
                'content_name': obj.content_name,
                'comment': obj.comment,
                'key_words': obj.key_words,
                'add_time': obj.add_time,
                'content_tags': obj.content_tags,
                'info': obj.info,
                'publish_writing': obj.publish_writing or '',
                'copywriting': obj.copywriting or '',
                'publish_status': obj.publish_status
            })
        return data

    def update_rel_content_knowledge(self, content_id, knowledge_id, tp_user_id):
        """
        更新素材
        """
        rel_id = get_snowflake_id()
        obj = RelContentKnowledge(rel_id=rel_id, content_id=content_id, knowledge_id=knowledge_id, tp_user_id=tp_user_id)
        self.session.add(obj)
        self.session.commit()
        return str(rel_id)

    def update_content_info_by_comment(self, content_id, comment):
        """
        更新素材
        """
        with self.session_scope() as session:
            obj = session.query(ContentInfo).filter(ContentInfo.content_id == content_id).first()
            obj.comment = comment
            return content_id

    def del_rel_content_knowledge(self, content_id, knowledge_id):
        """
        删除素材
        """
        q = self.session.query(RelContentKnowledge).filter(RelContentKnowledge.content_id == content_id,
                                                           RelContentKnowledge.knowledge_id == knowledge_id,
                                                           RelContentKnowledge.delete_flag == settings.DELETE_FLAG_FALSE)
        obj = q.first()
        if obj:
            obj.delete_flag = 1
            self.session.commit()

    def check_rel_content_knowledge(self, content_id, knowledge_id):

        q = self.session.query(RelContentKnowledge).filter(RelContentKnowledge.content_id == content_id,
                                                           RelContentKnowledge.knowledge_id == knowledge_id,
                                                           RelContentKnowledge.delete_flag == settings.DELETE_FLAG_FALSE)
        count = q.count()
        return count

    def get_content_train_data_list(self, next_time, content_type_id=None):
        """
        获取素材训练数据集
        """
        subq = self.session.query(func.group_concat(Tag.tag_name).label('content_tags'), RelContentTag.content_id).join(Tag,
                                                                                                                        RelContentTag.tag_id == Tag.tags_id).group_by(
            RelContentTag.content_id).subquery()
        q = (self.session.query(ContentInfo.content_id, ContentInfo.content_name, ContentInfo.comment, ContentInfo.key_words,
                                ContentInfo.add_time, ContentInfo.content_type_id, subq.c.content_tags, ContentInfo.info,ContentInfo.pic_url,
                                ContentInfo.publish_writing, ContentInfo.publish_status, ContentInfo.add_time, ContentInfo.copywriting,
                                ContentInfo.content_type_id).outerjoin(subq, ContentInfo.content_id == subq.c.content_id).filter(

            ContentInfo.delete_flag == settings.DELETE_FLAG_FALSE))

        q = q.order_by(ContentInfo.add_time)
        if next_time and next_time != 'all':
            q = q.filter(ContentInfo.add_time > next_time)
        if content_type_id:
            q = q.filter(ContentInfo.content_type_id == content_type_id)
        objs = q.all()
        res = []
        for obj in objs:
            res.append({
                'content_id': obj.content_id,
                'content_name': obj.content_name,
                'comment': obj.comment,
                'key_words': obj.key_words,
                'content_tags': obj.content_tags,
                'content_type_id': obj.content_type_id,
                'info': obj.info,
                'publish_writing': obj.publish_writing or '',
                'publish_status': obj.publish_status,
                'copywriting': obj.copywriting or '',
                'pic_url': obj.pic_url or '',
                'add_time': str(obj.add_time)
            })
        return res

    def get_content_ids_by_ids(self, content_ids):
        with self.session_scope() as session:
            objs = session.query(ContentInfo.content_id).filter(ContentInfo.content_id.in_(content_ids), ContentInfo.delete_flag == settings.DELETE_FLAG_FALSE,
                                                                     ContentInfo.publish_status == '0').all()
            return [obj.content_id for obj in objs]

    def get_content_info_by_ids(self, content_ids):
        with self.session_scope() as session:
            objs = session.query(ContentInfo).filter(ContentInfo.content_id.in_(content_ids), ContentInfo.delete_flag == settings.DELETE_FLAG_FALSE,
                                                        ContentInfo.publish_status == '0').order_by(ContentInfo.add_time).order_by(
                func.field_(ContentInfo.content_id, *tuple(content_ids))).all()
            data_list = []
            for obj in objs:
                data_list.append({
                    'content_id': obj.content_id,
                    'content_name': obj.content_name,
                    'comment': obj.comment,
                    'add_time': obj.add_time,
                    'tags': self.get_tags_by_content_id(obj.content_id)
                })
            return data_list

    def get_tags_by_content_id(self, content_id):
        with self.session_scope() as session:
            objs = session.query(RelContentTag).filter_by(content_id=content_id).all()
            ret = list()
            for one in objs:
                ret.append({
                    'tag_id': one.tag_id,
                    'tag_name': one.tag.tag_name
                })
            return ret

    def get_content_info_by_id(self, content_id):
        with self.session_scope() as session:
            obj = session.query(ContentInfo).filter_by(content_id=content_id, delete_flag='0').first()

            return obj
        
    def get_latest_add_time(self, content_source_id):

        with self.session_scope() as session:
            obj = session.query(ContentInfo.add_time).filter(ContentInfo.content_source_id==content_source_id).order_by(ContentInfo.add_time.desc()).first()
            if obj:
                return obj.add_time
        return None

    def get_info_by_joint_table(self, content_source_id):
        latest_add_time = self.get_latest_add_time(content_source_id)
        
        # logger.info(latest_chunk_image_id)
        
        with self.session_scope() as session:
            obj = session.query(AigcKnowledgeDocuChunkImage.chunk_image_id,
                                    AigcKnowledgeDocuChunkImage.chunk_id,
                                        AigcKnowledgeDocuChunkImage.image_url,
                                            AigcKnowledgeDocumentChunk.content,
                                                AigcKnowledgeDocument.document_id,
                                                    AigcKnowledgeDocument.doc_name).join(
                                                        AigcKnowledgeDocumentChunk, AigcKnowledgeDocuChunkImage.chunk_id == AigcKnowledgeDocumentChunk.chunk_id).join(
                                                            AigcKnowledgeDocument, AigcKnowledgeDocumentChunk.doc_id == AigcKnowledgeDocument.document_id).filter(
                                                                AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE, 
                                                                    AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE)
            # logger.info(obj.statement.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True}))
            if latest_add_time:
                obj = obj.filter(AigcKnowledgeDocuChunkImage.add_time > latest_add_time)
            else:
                obj = obj.order_by(AigcKnowledgeDocuChunkImage.add_time.asc()).limit(1)
            return obj.all()
        
    def add_content_info_by_chunk_image_id(self):
        
        content_type_id = 2001  # 图片
        content_source_id = 5
        add_user_id = '68a6a43d-1671-11ef-a407-0242ac110002'
        create_user_id = 'edb15b16-2443-4314-bea1-06c5b7b944a5'
        auth_scheme_id = 'b240c5a2-1ec0-4d86-9f8f-445d659c15ef'
        content_level = 'C'
        scope_id = 8001        

        data = self.get_info_by_joint_table(content_source_id)

        for item in data:
            content_id = str(uuid.uuid4())
            pic_url = item.image_url
            thumbnail_url = pic_url
            content_name = "".join(item.doc_name.split('.')[:-1])
            publish_status = 0
            out_flag = 1
            content_source_info = item.chunk_image_id
            info = '[]'
            
            if len(item.content) > 2000:
                comment = item.content[:2000]
            else:
                comment = item.content
            
            # logger.info(f'content_id: {content_id} \n\n \
            #         content_name: {content_name} \n\n \
            #             comment: {comment} \n\n \
            #                 content_type_id: {content_type_id} \n\n \
            #                     add_user_id: {add_user_id} \n\n \
            #                         create_user_id: {create_user_id} \n\n \
            #                             auth_scheme_id: {auth_scheme_id} \n\n \
            #                                 content_level: {content_level} \n\n \
            #                                     scope_id: {scope_id} \n\n \
            #                                         publish_status: {publish_status} \n\n \
            #                                             out_flag: {out_flag} \n\n \
            #                                                 content_source_id: {content_source_id} \n\n \
            #                                                     content_source_info: {content_source_info} \n\n \
            #                                                             pic_url: {pic_url} \n\n \
            #                                                                 thumbnail_url: {thumbnail_url} \n\n')
            
            dt = self.session.query(DirectoryLevel.id).filter_by(is_default_dp='1', delete_flag=str(settings.DELETE_FLAG_FALSE)).first()
            if not dt:
                raise ValueError('定时任务：add_content_info_by_chunk_image_id 默认目录不存在，无法更新')
            
            obj = ContentInfo(
                content_id=content_id,
                content_name=content_name,
                comment=comment,
                content_type_id=content_type_id,
                add_user_id=add_user_id,
                create_user_id=create_user_id,
                auth_scheme_id=auth_scheme_id,
                content_level=content_level,
                scope_id=scope_id,
                publish_status=publish_status,
                out_flag=out_flag,
                content_source_id=content_source_id,
                content_source_info=content_source_info,
                info=info,
                pic_url=pic_url,
                thumbnail_url=thumbnail_url,
                dt_id=dt.id
            )
            logger.info(f'add_content_info_by_chunk_image_id: {obj.content_name}')
            self.session.add(obj)
            self.session.flush()
            self.session.commit()

                
    def check_content_ids_auth(self, content_ids):
        """
        代码摘自于 jusure_zs_master: search_content函数
        """
        user_id = request.user['tp_user_id']
        role_ids = request.user['auth_ids']
        
        q = self.session.query(ContentInfo.content_id).filter_by(delete_flag=str(settings.DELETE_FLAG_FALSE))
        if not content_ids:
            return  []
        q = q.filter(ContentInfo.content_id.in_(content_ids))
        # 超级管理员可见所有
        if SuperManager not in role_ids:
            # 权限内可见范围
            scheme = SchemeM(self.corpid)
            scheme_datas = scheme.dpt_scheme_by_uid(user_id) + scheme.user_scheme_by_uid(user_id) + \
                           scheme.role_scheme_by_uid(user_id)
            tuple_ids = list(set([x['scheme_id'] for x in scheme_datas]))

            if ContentManager in role_ids:
                manager_scheme_ids = list(
                    set([x['scheme_id'] for x in scheme.get_scheme_rel_role_list(ContentManager)]))
            else:
                manager_scheme_ids = list()
            # 作为上传者或者作者可见范围 / 素材管理员可见下架素材
            q = q.filter(((ContentInfo.auth_scheme_id.in_(tuple_ids)) & (ContentInfo.publish_status == '0')) |
                         ((ContentInfo.auth_scheme_id.in_(manager_scheme_ids)) & (ContentInfo.publish_status == '6')) |
                         (ContentInfo.add_user_id == user_id) | (ContentInfo.create_user_id == user_id) |
                         (ContentInfo.publish_tpu_id == user_id))
        logger.info(f'check_content_ids_auth_length: {q.count()}')
        return [i.content_id for i in q.all()]
    
    def get_content_ids_by_delete_flag(self, delete_flag=str(settings.DELETE_FLAG_WAIT_DEL_VECTOR)):
        ret = {}
        with self.session_scope() as session:
            objs = session.query(ContentInfo.content_id, 
                                func.max(ContentInfo.add_time).label('latest_add_time')).group_by(ContentInfo.content_id).filter_by(delete_flag=delete_flag).all()
            ret.update({i.content_id:str(i.latest_add_time)  for i in objs}
)
            objs_version = session.query(ContentInfoVersion.content_id,
                                         func.max(ContentInfoVersion.add_time).label('latest_add_time')).group_by(ContentInfoVersion.content_id).filter_by(delete_flag=delete_flag).all()
            ret.update({i.content_id:str(i.latest_add_time)  for i in objs_version})
            return ret
    
    def update_content_info_delete_flag(self, content_ids):
        with self.session_scope() as session:
            session.query(ContentInfo).filter(ContentInfo.content_id.in_(content_ids),
                                              ContentInfo.delete_flag==settings.DELETE_FLAG_WAIT_DEL_VECTOR).update({
                'delete_flag': settings.DELETE_FLAG_TRUE
            })
            session.query(ContentInfoVersion).filter(ContentInfoVersion.content_id.in_(content_ids),
                                              ContentInfoVersion.delete_flag==settings.DELETE_FLAG_WAIT_DEL_VECTOR).update({
                'delete_flag': settings.DELETE_FLAG_TRUE
            })
        return True
    

    def get_content_detail_list(self, content_id):
        objs = self.session.query(ContentInfoVersion).filter_by(delete_flag=settings.DELETE_FLAG_FALSE, content_id=content_id) \
            .filter(ContentInfoVersion.pid.is_(None)).all()
        ret = list()
        for obj in objs:
            ret.append({'name': obj.url_file_name, 'url': obj.url})
        return ret