# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: knowledge_orm.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 3月 28, 2024
# ---
import json

from flask import request
from httpx import delete
from module import BaseModel
from module.mysql.models import *
from sqlalchemy import func, or_, and_, select
from utils.tools import get_snowflake_id
from datetime import datetime, timedelta
from sqlalchemy.dialects import postgresql
import tempfile
from settings import IS_910B_EMBEDDINGS
from puppet.es_sdk import EsSdkPool

from horticulture.permission import permit_map_code, SuperManager
from module import minio_util
import settings
import os
from utils.tools import get_ring_times, ms_to_hms, concat_video_time_strings
from lib_func.logger import task_logger, logger
import time
class KnowledgeOrm(BaseModel):

    def get_file_state(self, args):
        """
        获取文件状态
        """
        document_id = args.get("document_id")
        current_node = args.get("current_node")

        # 查询主文档对象
        obj = self.session.query(AigcKnowledgeDocument).filter(
            AigcKnowledgeDocument.document_id == document_id,
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE,
        ).first()

        # 初始化返回数据
        data = dict()

        # 各节点处理逻辑
        if current_node == "文档清洗":
            rule_relations = self.session.query(DocClearRelation).filter(
                DocClearRelation.document_id == document_id
            ).all()

            # 初始化 rule_list
            rule_list = {"doc": [], "excel": []}
            for relation in rule_relations:
                rule_obj = self.session.query(DocClearRule).filter(
                    DocClearRule.rule_id == relation.rule_id,
                ).first()
                if rule_obj:
                    if rule_obj.doc_type == 0:
                        rule_list["doc"].append(str(rule_obj.rule_id))
                    elif rule_obj.doc_type == 1:
                        rule_list["excel"].append(str(rule_obj.rule_id))

            if obj:
                data.update({
                    'knowledge_id': str(obj.knowledge_id),
                    'chunk_size':obj.split_chunk_size,
                    'chunk_overlap':obj.chunk_overlap,
                    'slice_model':obj.slice_model,
                    'pdf_model':obj.pdf_model,
                    'doc_list': [{
                        "doc_type": obj.doc_type,
                        "doc_size": obj.doc_size,
                        "doc_name": obj.doc_name,
                        "doc_url": obj.doc_url,
                        "doc_id": str(obj.document_id)
                    }],
                    'rule_list': rule_list
                })

        elif current_node == "文档上传":
            if obj:
                data.update({
                    'knowledge_id': str(obj.knowledge_id),
                    'doc_list': [{
                        "doc_type": obj.doc_type,
                        "doc_size": obj.doc_size,
                        "doc_name": obj.doc_name,
                        "doc_url": obj.doc_url,
                        "doc_id": str(obj.document_id),
                        "knlg_extract": obj.knlg_extract,
                        "chunk_size": obj.split_chunk_size,
                        "chunk_overlap": obj.chunk_overlap,
                        "slice_model": obj.slice_model,
                        "pdf_model": obj.pdf_model,
                        "chunk_clear_call_words": obj.chunk_clear_call_words,
                        "upload_type": obj.upload_type,
                        "have_table": obj.have_table,
                        "parent_cat_id": obj.parent_cat_id,
                        "doc_clear_url": obj.doc_clear_url
                    }]
                })

        elif current_node == "文档切片":
            if obj:
                data.update({
                    'knowledge_id': str(obj.knowledge_id),
                    'chunk_size':obj.split_chunk_size,
                    'chunk_overlap':obj.chunk_overlap,
                    'slice_model':obj.slice_model,
                    'pdf_model':obj.pdf_model,
                    'doc_list': [{
                        "doc_type": obj.doc_type,
                        "doc_size": obj.doc_size,
                        "doc_name": obj.doc_name,
                        "doc_url": obj.doc_url,
                        "doc_id": str(obj.document_id)
                    }]
                })

        elif current_node == "切片清洗":
            # 查询所有分块数据
            chunks = self.session.query(AigcKnowledgeDocumentChunk).filter(
                AigcKnowledgeDocumentChunk.doc_id == document_id,
                AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE
            ).all()
            # 查询清洗规则
            rule_ids = [
                rule.chunk_clear_rule_id
                for rule in self.session.query(RelDocChunkClearRule).filter(
                    RelDocChunkClearRule.doc_id == document_id
                ).all()
            ]
            # 构建分块列表
            chunk_list = []
            for chunk in chunks:
                chunk_list.append({
                    "chunk_id": str(chunk.chunk_id),
                    "content": chunk.content,
                    "doc_id": str(chunk.doc_id),
                    "character_count": chunk.character_count
                })

            # 构建返回数据
            data = {}
            if obj:
                data.update({
                    "doc_name": obj.doc_name,
                    "knowledge_id": str(obj.knowledge_id),
                    "doc_ids": [str(obj.document_id)],
                    "chunk_list": chunk_list,
                    'call_words':obj.chunk_clear_call_words,
                    'rule_ids':rule_ids
                })

        elif current_node == "向量化":
            chunks = self.session.query(AigcKnowledgeDocumentChunk).filter(
                AigcKnowledgeDocumentChunk.doc_id == document_id,
                AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE
            ).all()

            chunk_list = []
            for chunk in chunks:
                chunk_list.append({
                    "chunk_id": str(chunk.chunk_id),
                    "content": chunk.content,
                    "doc_id": str(chunk.doc_id),
                    "character_count": chunk.character_count
                })
            if obj and chunks:
                data.update({
                    'knowledge_id': str(obj.knowledge_id),
                    'chunk_ids': [str(chunk.chunk_id) for chunk in chunks],
                    'doc_ids': [str(obj.document_id)],
                    'is_update': "0",
                    "chunk_list": chunk_list

                })

        return data if data else None
    
    def get_model_url(self, model_path):
        with self.session_scope() as session:
            model_url = session.query(AigcModel).filter(AigcModel.model_path == model_path).first().model_url
            return model_url

    def save_chunk_clear_rule_id(self,doc_id ,chunk_clear_rule_id):
        # 检查是否已存在
        existing_rule = self.session.query(RelDocChunkClearRule).filter(
            RelDocChunkClearRule.doc_id == doc_id,
            RelDocChunkClearRule.chunk_clear_rule_id == chunk_clear_rule_id
        ).first()   
        if existing_rule:
            return {"message":"已存在"}
        else:
            obj = RelDocChunkClearRule( doc_id=doc_id, chunk_clear_rule_id=chunk_clear_rule_id)
            self.session.add(obj)
            self.session.commit()
            return {"message":"添加成功"}


    def get_knowledge_documents_by_date(self, knowledge_id, args):
        q = self.base_query(knowledge_id, args)
        if args.get('doc_name'):
            q = q.filter(AigcKnowledgeDocument.doc_name.ilike(f"%{args.get('doc_name')}%"))
        state = args.get("state")
        if state:
            if state == "0":
                q = q.filter(AigcKnowledgeDocument.status == 'FAILED')
            elif state == "1":
                q = q.filter(AigcKnowledgeDocument.status == 'FINISHED')
            elif state == "2":
                q = q.filter(AigcKnowledgeDocument.result == 'FAILED')
            elif state == "3":
                q = q.filter(AigcKnowledgeDocument.result == 'FINISHED')
            else:
                pass
        count = q.count()
        objs = q.slice((int(args['page_no']) - 1) * int(args['page_size']), int(args['page_size']) * int(args['page_no'])).all()
        data_list = list()
        for obj in objs:
            chunk_content = self.get_chunk_by_doc_id(obj.document_id)
            preview_content = chunk_content.content[:50].replace('\n', '') if chunk_content else ''
            data_list.append({
                'document_id': str(obj.document_id),
                'doc_name': obj.doc_name,
                'status': obj.status,
                'result': obj.result,
                'add_time': obj.add_time,
                'doc_url': obj.doc_url,
                'knowledge_name': obj.knowledge.knowledge_name,
                'preview_content': preview_content
            })
        return {'total': count, 'po': args['page_no'], 'data_list': data_list}

    def del_split_chunk(self,doc_id):
        self.session.query(AigcKnowledgeDocumentChunk).filter(
            AigcKnowledgeDocumentChunk.doc_id == doc_id,
            AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE
        ).update({'delete_flag': settings.DELETE_FLAG_TRUE})
        self.session.query(AigcKnowledgeDocumentTableChunk).filter(
            AigcKnowledgeDocumentTableChunk.doc_id == doc_id,
            AigcKnowledgeDocumentTableChunk.delete_flag == settings.DELETE_FLAG_FALSE
        ).update({'delete_flag': settings.DELETE_FLAG_TRUE})
        self.session.query(AigcKnowledgeDocumentImages).filter(
            AigcKnowledgeDocumentImages.doc_id == doc_id,
            AigcKnowledgeDocumentImages.delete_flag == settings.DELETE_FLAG_FALSE
        ).update({'delete_flag': settings.DELETE_FLAG_TRUE})
 
        self.session.commit()

    def get_knowledge_tree(self, args):
        """
        获取指定目录及其子目录的知识库树结构
        Args:
            args: 包含目录信息的字典，必须包含 'parent_directory_id'
        Returns:
            dict: 包含操作状态和消息的字典，返回树形结构的知识库目录
        """
        parent_directory_id = args.get('parent_directory_id')
        directory_name = args.get('directory_name')
        directory_id = args.get('directory_id')
        knowledge_name = args.get('knowledge_name')
        auth_scheme_ids = args['auth_scheme_ids']
        if knowledge_name:
            # 获取包含该知识库名称的所有 parent_directory_id
            directory_ids = self.session.query(AigcKnowledge.parent_directory_id).filter(
                AigcKnowledge.knowledge_name.ilike(f"%{knowledge_name}%"),
                AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE
            ).distinct().all()  # 使用 distinct 去重
            # 如果找到符合条件的目录 ID
            if directory_ids:
                directory_ids = [directory_id[0] for directory_id in directory_ids]  # 提取出 directory_id
                tree = []

                # 遍历每个 directory_id 获取完整的目录树
                for directory_id in directory_ids:
                    directory = self.session.query(KnowDirectory).filter(
                        KnowDirectory.directory_id == directory_id,
                        KnowDirectory.delete_flag == settings.DELETE_FLAG_FALSE
                    ).first()
                    if directory:
                        # 构建目录树节点并加入树
                        node = self._build_node(directory, auth_scheme_ids,knowledge_name)
                        tree.append(node)
                return {'message': 'Directory tree with knowledge names retrieved successfully', 'data': tree}
            else:
                return {'message': 'No directories found with the given knowledge name'}

        if directory_id:
            directory = self.session.query(KnowDirectory).filter(
                KnowDirectory.directory_id == directory_id,
                KnowDirectory.delete_flag == settings.DELETE_FLAG_FALSE
            ).first()
            if directory.parent_directory_id:
                parent_directory_id = directory.parent_directory_id
                q = self.session.query(KnowDirectory).filter(
                    KnowDirectory.directory_id == parent_directory_id,
                    KnowDirectory.delete_flag == settings.DELETE_FLAG_FALSE
                ).first()
                parent_directory_name = q.directory_name
            else:
                parent_directory_name = None
            if directory:
                return {
                    'directory_id': str(directory.directory_id),
                    'directory_name': directory.directory_name,
                    'directory_description': directory.directory_description,
                    'parent_directory_id': str(directory.parent_directory_id) if directory.parent_directory_id else None,
                    'parent_directory_name': parent_directory_name if parent_directory_name else None
                }
            return {'message': 'Directory not found'}


        if directory_name:
            directories = self.session.query(KnowDirectory).filter(
                KnowDirectory.directory_name.ilike(f"%{directory_name}%"),  # 使用 ilike 进行模糊匹配
                KnowDirectory.delete_flag == settings.DELETE_FLAG_FALSE
            ).all()
            tree = [self._build_node(directory, auth_scheme_ids) for directory in directories]
            return {'message': 'Directory tree retrieved successfully', 'data': tree}

        if parent_directory_id is None:
            page_no = args.get('page_no')
            page_size = args.get('page_size')
            directories = self.session.query(KnowDirectory).filter(
                KnowDirectory.parent_directory_id == None,
                KnowDirectory.delete_flag == settings.DELETE_FLAG_FALSE
            ).slice((page_no - 1) * page_size, page_no * page_size).all()

            if not directories:
                return {'message': 'No directories found with no parent directory ID'}

            # Build the directory tree
            tree = [self._build_node(directory, auth_scheme_ids) for directory in directories]
            return {'message': 'Directory tree retrieved successfully', 'data': tree}
        try:
            # 获取指定父目录的子目录及其知识库记录
            directories = self.session.query(KnowDirectory).filter(
                KnowDirectory.parent_directory_id == parent_directory_id,
                KnowDirectory.delete_flag == settings.DELETE_FLAG_FALSE
            ).all()

            if not directories:
                return {'message': 'No directories found for the given parent directory ID'}

            # 构建目录树
            tree = []
            for directory in directories:
                node = self._build_node(directory, auth_scheme_ids)
                tree.append(node)

            return {'message': 'Directory tree retrieved successfully', 'data': tree}

        except Exception as e:
            return {'message': str(e)}

    def _build_node(self, directory, auth_scheme_ids, knowledge_name = None):
        """
        构建树形结构的节点
        Args:
            directory: 当前目录对象
        Returns:
            dict: 树形结构的目录节点
        """
        knowledge_ids = self._get_knowledge_ids(directory.directory_id, auth_scheme_ids, knowledge_name)

        node = {
            'directory_id': str(directory.directory_id),
            'directory_name': directory.directory_name,
            'knowledge_ids': knowledge_ids,
            'knowledge_count': len(knowledge_ids),
            'directory_description': directory.directory_description,
            'children': [],  # 初始化子目录
            'parent_directory_id': str(directory.parent_directory_id) if directory.parent_directory_id else None
        }

        # 查找子目录
        subdirectories = self.session.query(KnowDirectory).filter(
            KnowDirectory.parent_directory_id == directory.directory_id,
            KnowDirectory.delete_flag == settings.DELETE_FLAG_FALSE
        ).all()

        for subdirectory in subdirectories:
            node['children'].append(self._build_node(subdirectory, auth_scheme_ids, knowledge_name))  # 递归调用

        return node

    def transfer_doc(self, args):
        """
        文档转移, 不进行复制,只修改文档知识库id
        """
        doc_ids = args.get('doc_ids')
        knowledge_id = args.get('knowledge_id')
        target_knowledge_id = args.get('target_knowledge_id')
        es = EsSdkPool(self.corpid)

        with self.session_scope() as session:
            model_path = session.query(AigcKnowledge).filter(AigcKnowledge.knowledge_id == knowledge_id).first().aigc_model.model_path
            index_name = f"knowledge_{model_path}"
            # 查询指定知识库下的文档
            documents = session.query(AigcKnowledgeDocument).filter(
                AigcKnowledgeDocument.document_id.in_(doc_ids),
                AigcKnowledgeDocument.knowledge_id == knowledge_id
            ).all()
            # 更新文档知识库id
            for doc in documents:
                doc.knowledge_id = target_knowledge_id
                doc.source_type = 2
            session.commit()
        # 更新es中知识库id
            for doc in documents:
                query_dict = {
                    "query": {
                        "match": {
                            "doc_id": str(doc.document_id)
                        }
                    },
                    "script": {
                        "source": "ctx._source.knowledge_id = params.target_knowledge_id",
                        "params": {
                            "target_knowledge_id": target_knowledge_id
                        }
                    }
                }
                es.update_data(index_name, query_dict)
        return {'message': 'Document transferred successfully'}

    def transfer_doc_copy(self, args):
        doc_ids = args.get('doc_ids')
        knowledge_id = args.get('knowledge_id')
        target_knowledge_id = args.get('target_knowledge_id')
        es = EsSdkPool(self.corpid)
        with self.session_scope() as session:
            # 查询指定知识库下的文档
            documents = session.query(AigcKnowledgeDocument).filter(
                AigcKnowledgeDocument.document_id.in_(doc_ids),
                AigcKnowledgeDocument.knowledge_id == knowledge_id
            ).all()

            doc_id_mapping = {}  # 用于存储旧文档ID到新文档ID的映射
            chunk_id_mapping = {}  # 用于存储旧切片ID到新切片ID的映射
            new_doc_ids = []  # 存储新生成的文档ID

            # 复制文档到目标知识库
            for doc in documents:
                # 创建新的文档对象
                # 更新文档是否分享
                doc.is_sharing = 1
                new_doc = AigcKnowledgeDocument()
                for column in AigcKnowledgeDocument.__table__.columns:
                    if column.name != 'document_id':  # 跳过主键
                        setattr(new_doc, column.name, getattr(doc, column.name))
                # 为新文档生成唯一的 document_id
                new_doc.document_id = get_snowflake_id()
                new_doc.knowledge_id = target_knowledge_id
                new_doc.source_type = 1
                new_doc.doc_source_id = doc.document_id
                session.add(new_doc)
                doc_id_mapping[doc.document_id] = new_doc.document_id
                new_doc_ids.append(new_doc.document_id)

            session.flush()  # 刷新会话以获取新文档的ID

            # 复制文档切片
            for old_doc_id, new_doc_id in doc_id_mapping.items():
                chunks = session.query(AigcKnowledgeDocumentChunk).filter(
                    AigcKnowledgeDocumentChunk.doc_id == old_doc_id
                ).all()
                for chunk in chunks:
                    new_chunk = AigcKnowledgeDocumentChunk()
                    for column in AigcKnowledgeDocumentChunk.__table__.columns:
                        if column.name != 'chunk_id':  # 跳过主键
                            if column.name == 'doc_id':
                                setattr(new_chunk, column.name, new_doc_id)
                            else:
                                setattr(new_chunk, column.name, getattr(chunk, column.name))
                    # 为新切片生成唯一的 chunk_id
                    new_chunk.chunk_id = get_snowflake_id()
                    new_chunk.source_chunk_id = chunk.chunk_id
                    session.add(new_chunk)
                    chunk_id_mapping[chunk.chunk_id] = new_chunk.chunk_id

            session.flush()  # 刷新会话以获取新切片的ID


            # 新增：复制表格切片表 aigc_knowledge_document_table_chunk
            for old_doc_id, new_doc_id in doc_id_mapping.items():
                # 查询旧表格切片
                old_table_chunks = session.query(AigcKnowledgeDocumentTableChunk).filter(
                    AigcKnowledgeDocumentTableChunk.doc_id == old_doc_id
                ).all()
                for old_table_chunk in old_table_chunks:
                    new_table_chunk = AigcKnowledgeDocumentTableChunk()
                    for column in AigcKnowledgeDocumentTableChunk.__table__.columns:
                        if column.name in ['table_chunk_id', 'doc_id']:  # 跳过主键和外键（doc_id需替换）
                            if column.name == 'doc_id':
                                setattr(new_table_chunk, column.name, new_doc_id)  # 替换为新文档ID
                            # table_chunk_id 生成新的雪花ID
                        else:
                            setattr(new_table_chunk, column.name, getattr(old_table_chunk, column.name))
                    # 生成新的 table_chunk_id
                    new_table_chunk.table_chunk_id = get_snowflake_id()
                    session.add(new_table_chunk)    

            session.flush()  # 刷新会话以获取新表格切片ID

            # 复制切片图片
            for old_chunk_id, new_chunk_id in chunk_id_mapping.items():
                images = session.query(AigcKnowledgeDocuChunkImage).filter(
                    AigcKnowledgeDocuChunkImage.chunk_id == old_chunk_id
                ).all()
                for image in images:
                    new_image = AigcKnowledgeDocuChunkImage()
                    for column in AigcKnowledgeDocuChunkImage.__table__.columns:
                        if column.name != 'chunk_image_id':  # 跳过主键
                            if column.name == 'chunk_id':
                                setattr(new_image, column.name, new_chunk_id)
                            else:
                                setattr(new_image, column.name, getattr(image, column.name))
                    # 为新图片记录生成唯一的 chunk_image_id
                    new_image.chunk_image_id = get_snowflake_id()
                    new_image.source_chunk_image_id = image.chunk_image_id
                    session.add(new_image)

            session.commit()
            # 复制es索引
            knowledge_info = session.query(AigcKnowledge).filter(AigcKnowledge.knowledge_id == knowledge_id).first()
            model_path = knowledge_info.aigc_model.model_path if knowledge_info else 'embedding-v1'
            dims = knowledge_info.aigc_model.dims if knowledge_info else 1024
            index_name = f"jusure_pro_2_knowledge_{model_path}"  # 生成索引名
            target_index_name = f"jusure_pro_2_knowledge_{model_path}"  # 假设目标索引名与源索引名相同

            # 创建 ES 索引（若不存在）
            if not es.get_es_instance().indices.exists(index=target_index_name):
                es.get_es_instance().indices.create(
                    index=target_index_name,
                    body={
                        "mappings": {
                            "properties": {
                                "chunk_id": {"type": "keyword"},
                                "doc_id": {"type": "keyword"},
                                "knowledge_id": {"type": "keyword"},
                                "content_id": {"type": "keyword"},
                                "description": {"type": "text"},
                                "character_count": {"type": "integer"},
                                "hit_count": {"type": "integer"},
                                "tokens": {"type": "integer"},
                                "status": {"type": "boolean"},
                                "vector": {"type": "dense_vector", "dims": dims, "index": True},
                                "delete_flag": {"type": "boolean"}
                            }
                        }
                    }
                )

            # 构建 ES 数据（基于新生成的切片数据）
            es_data_list = []
            for old_doc_id, new_doc_id in doc_id_mapping.items():
                # 查询旧切片（基于旧 doc_id）
                old_chunks = session.query(AigcKnowledgeDocumentChunk).filter(
                    AigcKnowledgeDocumentChunk.doc_id == old_doc_id
                ).all()
                for old_chunk in old_chunks:
                    # 从 ES 获取旧切片的向量
                    try:
                        query_dict = {
                            "query": {
                                "match": {
                                    "chunk_id": str(old_chunk.chunk_id)
                                }
                            }
                        }
                        es_response = es.get_es_instance().search(index=index_name, body=query_dict)
                        vector = es_response['hits']['hits'][0]['_source']['vector']
                    except Exception as e:
                        print(f"Error getting vector for chunk {old_chunk.chunk_id}: {e}")
                        vector = [0] * dims  # 若获取失败，使用默认向量

                    new_chunk_id = chunk_id_mapping[old_chunk.chunk_id]
                    es_doc = {
                        "chunk_id": str(new_chunk_id),
                        "doc_id": str(new_doc_id),
                        "knowledge_id": str(target_knowledge_id),
                        "content_id": None,  # 按需填充
                        "description": old_chunk.content,
                        "character_count": old_chunk.character_count,
                        "hit_count": old_chunk.hit_count,
                        "tokens": old_chunk.tokens,
                        "status": bool(old_chunk.status),  # TINYINT 转布尔
                        "vector": vector,
                        "delete_flag": bool(old_chunk.delete_flag)
                    }
                    es_data_list.append(es_doc)

            # 批量插入 ES（提高效率）
            if es_data_list:
                actions = []
                for doc in es_data_list:
                    action = {
                        "index": {
                            "_index": target_index_name,
                            "_id": doc["chunk_id"]  # 使用 chunk_id 作为文档 ID
                        }
                    }
                    actions.append(action)
                    actions.append(doc)
                try:
                    es.get_es_instance().bulk(body=actions)
                except Exception as e:
                    print(f"Error inserting data into Elasticsearch: {e}")
            
            return {'message': 'Document and related data copied successfully', 'new_doc_ids': new_doc_ids}

    def update_doc_sharing(self, args):
        # 获取 doc_ids 参数，如果不存在则默认为空列表
        doc_ids = args.get('doc_ids', [])
        knowledge_id = args.get('knowledge_id')
        if not doc_ids:
            return {'message': 'No doc_ids provided'}

        # 批量更新文档的 is_sharing 状态为 0
        self.session.query(AigcKnowledgeDocument).filter(
            AigcKnowledgeDocument.document_id.in_(doc_ids),
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).update({'is_sharing': 0})
        
        del_docs = self.session.query(AigcKnowledgeDocument).filter(
            AigcKnowledgeDocument.source_type == 1,
            AigcKnowledgeDocument.doc_source_id.in_(doc_ids),
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).all()
        # 提交事务
        self.session.commit()
        return [str(del_doc.document_id) for del_doc in del_docs]

    def _get_knowledge_ids(self, directory_id, auth_scheme_ids, knowledge_name = None):
        """
        获取目录下的所有知识库ID
        Args:
            directory_id: 当前目录ID
        Returns:
            list: 当前目录下的所有知识库ID
        """
        q = self.session.query(AigcKnowledge.knowledge_id).filter(
                AigcKnowledge.parent_directory_id == directory_id,
                AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE)
        if knowledge_name:
            q = q.filter(AigcKnowledge.knowledge_name.ilike(f"%{knowledge_name}%"))
            
        # 权限控制
        user_id = request.user['tp_user_id']
        if auth_scheme_ids is not None: # 代表非超管
            if len(auth_scheme_ids) == 0:
                q = q.filter(AigcKnowledge.tp_user_id==user_id)
            else:
                q = q.filter(or_(AigcKnowledge.auth_scheme_id.in_(auth_scheme_ids),AigcKnowledge.tp_user_id==user_id))
        knowledge_ids = q.all()

        return [k.knowledge_id for k in knowledge_ids]

    def get_knowledge_ids_by_directory(self, args):
        """
        根据目录 ID 获取当前目录下以及所有子目录下的知识库 ID
        Args:
            directory_id: 需要获取知识库 ID 的目录 ID
        Returns:
            dict: 包含操作状态和知识库 ID 的字典
        """
        directory_id = args.get('directory_id')

        if not directory_id:
            return {'message': '目录 ID 是必需的'}

        try:
            with self.session_scope() as session:
                # 查找当前目录以及所有子目录
                knowledge_ids = self._get_all_knowledge_ids(directory_id, session)
                return knowledge_ids

        except Exception as e:
            return {'message': str(e)}

    def _get_all_knowledge_ids(self, parent_directory_id, session):
        """
        递归获取指定目录及其所有子目录下的知识库 ID
        Args:
            parent_directory_id: 父目录 ID
            session: 数据库会话对象
        Returns:
            list: 知识库 ID 列表
        """
        # 查找当前目录下的所有知识库 ID
        knowledge_ids = session.query(AigcKnowledge.knowledge_id).filter(
            AigcKnowledge.parent_directory_id == parent_directory_id,
            AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE  # 确保没有被删除的目录
        ).all()

        # 提取 knowledge_id 列表
        knowledge_ids = [k.knowledge_id for k in knowledge_ids]
        # 查找所有子目录
        child_directories = session.query(KnowDirectory).filter(
            KnowDirectory.parent_directory_id == parent_directory_id,
            KnowDirectory.delete_flag == settings.DELETE_FLAG_FALSE  # 确保没有被删除的子目录
        ).all()
        for child in child_directories:
            # 递归调用获取子目录的知识库 ID
            knowledge_ids.extend(self._get_all_knowledge_ids(child.directory_id, session))

        return knowledge_ids


    def get_system_list(self):
        q = self.session.query(AigcSystem).filter(AigcSystem.delete_flag == settings.DELETE_FLAG_FALSE)
        obj = q.order_by(AigcSystem.add_time.desc())        
        if obj:
            data = []
            for item in obj:
                data.append({
                    'system_id': str(item.system_id),
                    'system_name': item.system_name,
                })
            return data
        else:
            return []

    def create_system(self, args):
        system_name = args.get('system_name')
        system_id = get_snowflake_id()
        tp_user_id = args.get('tp_user_id')
        system = AigcSystem(
            system_id = system_id,
            system_name = system_name,
            tp_user_id = tp_user_id,
            add_time = datetime.now(),
            update_time = datetime.now()
        )
        self.session.add(system)
        self.session.commit()
        return {'message': '创建成功', 'system_id': system_id}

    def get_system_config(self, args):
        display_name = args.get('display_name')
        page_no = args.get('page_no')
        page_size = args.get('page_size')
        q = self.session.query(AigcSystemSettings).filter(AigcSystemSettings.delete_flag == settings.DELETE_FLAG_FALSE)
        if display_name:
            q = q.filter(AigcSystemSettings.display_name.ilike(f"%{display_name}%"))
        q = q.order_by(AigcSystemSettings.add_time.desc()).offset((page_no - 1) * page_size).limit(page_size).all()
        if q:
            data = []
            for item in q:
                data.append({
                    'setting_id': str(item.setting_id),
                    'display_name': item.display_name,
                    'icon_url': item.icon_url,
                    'is_applying': item.is_applying,
                    'add_time': item.add_time,
                    'update_time': item.update_time,
                    'tp_user_name': item.tp_user.tp_user_name
                })
            return data
        else:
            return []

    def create_system_config(self, args):
        system_id = args.get('system_id')
        display_name = args.get('display_name')
        icon_url = args.get('icon_url')
        tp_user_id = args.get('tp_user_id')
        setting_id = get_snowflake_id()
        system_config = AigcSystemSettings(
            setting_id = setting_id,
            system_id = system_id,
            display_name = display_name,
            icon_url = icon_url,
            tp_user_id = tp_user_id,
            add_time = datetime.now(),
            update_time = datetime.now()
        )
        self.session.add(system_config)
        self.session.commit()
        return {'message': '创建成功', 'setting_id': setting_id}

    def update_system_config(self, args):
        setting_id = args.get('setting_id')
        display_name = args.get('display_name')
        icon_url = args.get('icon_url')
        is_applying = args.get('is_applying', 0)
        tp_user_id = args.get('tp_user_id')
        system_config = self.session.query(AigcSystemSettings).filter(AigcSystemSettings.setting_id == setting_id, AigcSystemSettings.delete_flag == settings.DELETE_FLAG_FALSE).first()
        if system_config:
            system_config.display_name = display_name
            system_config.icon_url = icon_url
            system_config.is_applying = is_applying
            system_config.tp_user_id = tp_user_id
            system_config.update_time = datetime.now()
            self.session.commit()
            return {'message': '更新成功'}
        else:
            return {'message': '配置不存在'}

    def delete_system_config(self, args):
        setting_id = args.get('setting_id')
        system_config = self.session.query(AigcSystemSettings).filter(AigcSystemSettings.setting_id == setting_id, AigcSystemSettings.delete_flag == settings.DELETE_FLAG_FALSE).first() 
        if system_config:
            system_config.delete_flag = 1
            self.session.commit()
            return {'message': '删除成功', 'setting_id': setting_id}
        else:
            return {'message': '配置不存在'}

    def add_knowledge_tree(self, args):
        """
        添加或更新知识树结构
        Args:
            args: 包含目录信息的字典，必须包含 'knowledge_ids' 和 'parent_directory_id'
                如果传入 'directory_id'，则更新对应目录的信息
        Returns:
            dict: 包含操作状态和消息的字典
        """
        # 获取传入参数
        directory_id = args.get('directory_id')  # 如果传入了 directory_id，则更新该目录
        directory_name = args.get('directory_name')
        parent_directory_id = args.get('parent_directory_id',None)
        tp_user_id = args.get('tp_user_id')
        directory_description = args.get('directory_description')

        try:
            # 如果传入了 directory_id，先检查目录是否存在
            if directory_id:
                # 更新现有目录信息
                directory = self.session.query(KnowDirectory).filter(KnowDirectory.directory_id == directory_id,KnowDirectory.delete_flag == settings.DELETE_FLAG_FALSE).first()
                if directory:
                    # 更新目录名称和父目录 ID
                    directory.directory_name = directory_name
                    directory.directory_description = directory_description

                    # for knowledge_id in knowledge_ids:
                    #     knowledge = self.session.query(AigcKnowledge).filter(AigcKnowledge.knowledge_id == knowledge_id,AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE).first()
                    #     if knowledge:
                    #         # 更新知识库的 parent_directory_id
                    #         knowledge.parent_directory_id = parent_directory_id
                    #     else:
                    #         return {'message': f'Knowledge ID {knowledge_id} does not exist'}

                    # 提交知识库更新到数据库
                    self.session.commit()

                    return {'message': 'Directory tree updated successfully'}
                else:
                    return {'message': f'Directory with ID {directory_id} does not exist'}
            else:
                # 如果没有传入 directory_id，则创建新的目录
                directory = KnowDirectory(
                    directory_name=directory_name,
                    parent_directory_id=parent_directory_id,
                    delete_flag=0,
                    tp_user_id =tp_user_id,
                    directory_description=directory_description
                )
                self.session.add(directory)

            # 提交目录数据到数据库
                self.session.commit()

            # # 更新 AigcKnowledge 表中的知识库目录关系
            #     for knowledge_id in knowledge_ids:
            #         knowledge = self.session.query(AigcKnowledge).filter(AigcKnowledge.knowledge_id == knowledge_id,AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE).first()
            #         if knowledge:
            #             # 更新知识库的 parent_directory_id
            #             knowledge.parent_directory_id = directory.directory_id
            #         else:
            #             return {'message': f'Knowledge ID {knowledge_id} does not exist'}

            #     # 提交知识库更新到数据库
            #     self.session.commit()

                return {'message': 'Directory tree updated successfully'}

        except Exception as e:
            self.session.rollback()  # 如果发生异常，回滚事务
            return {'message': str(e)}

    def delete_knowledge_tree(self, args):
        """
        删除指定目录及其所有子目录
        Args:
            directory_id: 要删除的目录 ID
        Returns:
            dict: 包含操作状态和消息的字典
        """
        directory_id = args.get('directory_id')
        try:
            with self.session_scope() as session:
                # 查找当前目录
                directory_to_delete = session.query(KnowDirectory).filter(KnowDirectory.directory_id == directory_id, KnowDirectory.delete_flag == settings.DELETE_FLAG_FALSE).first()

                if not directory_to_delete:
                    return {'message': '目录不存在'}

                # 递归删除子目录
                knowledge_ids = self._delete_subdirectories(directory_id, session)

                # 删除当前目录
                directory_to_delete.delete_flag = 1
                directory_to_delete.parent_directory_id = None
                session.commit()

                # 更新 AigcKnowledge 中相关记录的 parent_directory_id 和 delete_flag
                session.query(AigcKnowledge).filter(AigcKnowledge.parent_directory_id == directory_id, AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE).update({'delete_flag': settings.DELETE_FLAG_TRUE, 'parent_directory_id': None})
                session.commit()

                # 返回所有已删除的知识库 ID
                return {'message': '目录及其子目录删除成功', 'deleted_knowledge_ids': knowledge_ids}

        except Exception as e:
            return {'message': str(e)}

    def _delete_subdirectories(self, parent_directory_id, session):
        """
        删除指定目录下的所有子目录
        Args:
            parent_directory_id: 父目录 ID，用于查询其所有子目录
            session: 数据库会话对象
        """
        # 查找所有子目录
        child_directories = session.query(KnowDirectory).filter(KnowDirectory.parent_directory_id == parent_directory_id).all()
        knowledge_ids = []

        for child in child_directories:
            # 递归删除子目录的子目录
            knowledge_ids.extend(self._delete_subdirectories(child.directory_id, session))

            # 删除子目录
            child.delete_flag = 1
            child.parent_directory_id = None
            session.commit()

            # 更新 AigcKnowledge 中相关记录的 parent_directory_id 和 delete_flag
            result = session.query(AigcKnowledge).filter(AigcKnowledge.parent_directory_id == child.directory_id, AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE).update({'delete_flag': settings.DELETE_FLAG_TRUE, 'parent_directory_id': None})
            session.commit()

            # 将删除的知识库 ID 添加到列表
            knowledge_ids.append(child.directory_id)

        return knowledge_ids


    def get_chunk_by_doc_id(self, doc_id):
        with self.session_scope() as session:
            return session.query(AigcKnowledgeDocumentChunk).filter(
                AigcKnowledgeDocumentChunk.doc_id == doc_id,
                AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE
            ).first()

    def base_query(self, knowledge_id, args):
        with self.session_scope() as session:
            # 获取符合条件的文档
            q = session.query(AigcKnowledgeDocument).filter(
                AigcKnowledgeDocument.knowledge_id == knowledge_id,  # 过滤指定知识库
                AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
            )
            if args.get('start_time'):
                q = q.filter(AigcKnowledgeDocument.add_time >= args.get('start_time'))
            if args.get('end_time'):
                q = q.filter(AigcKnowledgeDocument.add_time <= args.get('end_time'))
        return q

    def delete_knowledge_documents_by_date(self, knowledge_id, args):
        return self.base_query(knowledge_id, args).all()

    def delete_knowledge_documents_by_doc_ids(self, doc_ids, batch_size=100):
        with self.session_scope() as session:
            session.query(AigcKnowledgeDocument).filter(
                AigcKnowledgeDocument.document_id.in_(doc_ids)
            ).update({'delete_flag': settings.DELETE_FLAG_TRUE})
            # 提交更改
            session.commit()
            return doc_ids

    def get_life_status_knowledge_datas(self):
        return self.session.query(AigcKnowledge).filter_by(open_life=1, delete_flag=0).all()

    def get_knowledge_detail(self, knowledge_id):
        """
        获取知识详情
        :param knowledge_id:
        :return:
        """
        obj = self.session.query(AigcKnowledge).filter(AigcKnowledge.knowledge_id == knowledge_id, AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE).first()
        data = dict()
        if obj:
            if obj.tp_user:
                tp_user_name = obj.tp_user.tp_user_alias or obj.tp_user.tp_user_name
            else:
                tp_user_name = ''
            data = {
                'knowledge_id': str(obj.knowledge_id),
                'knowledge_name': obj.knowledge_name,
                'knowledge_desc': obj.knowledge_desc,
                'status': obj.status,
                'add_time': obj.add_time,
                'update_time': obj.update_time,
                'tp_user_name': tp_user_name,
                'icon_url': obj.icon_url,
                'model_name': obj.aigc_model.model_name,
                'model_path': obj.aigc_model.model_path,
                'aigc_model_id': obj.aigc_model_id,
                'dims': obj.aigc_model.dims,
                'aigc_type_name': obj.aigc_model.aigc_type.aigc_type_name,
                'open_life': obj.open_life
            }
        return data

    def get_knowledge_detail_v1(self, knowledge_id):
        """
        获取知识详情
        :param knowledge_id:
        :return:
        """
        # 获取 AigcKnowledge 对象
        # logger.info(f"==== get_knowledge_detail_v1 Start...")
        obj = self.session.query(AigcKnowledge).filter(AigcKnowledge.knowledge_id == knowledge_id, AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE).first()
        data = dict()

        if obj:
            if obj.tp_user:
                tp_user_name = obj.tp_user.tp_user_alias or obj.tp_user.tp_user_name
            else:
                tp_user_name = ''

            # 初始化 scheme 字段
            scheme = {
                "role_list": [],
                "dpt_list": [],
                "user_list": [],
                "auth_scheme_id": obj.auth_scheme_id
            }

            if obj.auth_scheme_id:
                # 获取角色列表
                roles = self.session.query(RelAuthSchemeRole).filter(RelAuthSchemeRole.scheme_id == obj.auth_scheme_id).all()
                scheme["role_list"] = [str(role.role_id) for role in roles]

                # 获取用户列表
                users = self.session.query(RelAuthSchemeUser).filter(RelAuthSchemeUser.scheme_id == obj.auth_scheme_id).all()
                for user in users:
                    tp_user = user.tp_user
                    scheme["user_list"].append({
                        "user_id": tp_user.tp_user_id,
                        "user_name": tp_user.tp_user_name,
                        "scheme_id": user.scheme_id,
                        "scheme_name": user.scheme.scheme_name
                    })
                    # 添加部门ID到 dpt_list
                    scheme["dpt_list"].append(str(tp_user.dp_id))

                # 去重处理
                scheme["dpt_list"] = list(set(scheme["dpt_list"]))

            # 构造返回数据
            data = {
                'knowledge_id': str(obj.knowledge_id),
                'knowledge_name': obj.knowledge_name,
                'knowledge_desc': obj.knowledge_desc,
                'status': obj.status,
                'add_time': obj.add_time,
                'update_time': obj.update_time,
                'tp_user_name': tp_user_name,
                'icon_url': obj.icon_url,
                'model_name': obj.aigc_model.model_name,
                'model_path': obj.aigc_model.model_path,
                'aigc_model_id': obj.aigc_model_id,
                'dims': obj.aigc_model.dims,
                'aigc_type_name': obj.aigc_model.aigc_type.aigc_type_name,
                # 'scheme': scheme,
                'role_list': scheme['role_list'],
                'dpt_list': scheme['dpt_list'],
                'user_list': scheme['user_list'],
                'auth_scheme_id': scheme['auth_scheme_id'],
                'open_life': obj.open_life
            }

        return data

    def get_knowledge_tag(self, args):
        """
        获取知识记录中的标签
        :param args: 包含 knowledge_id, page_no, page_size 的字典
        :return: 每个标签单独返回的结果
        """
        # 获取相关参数
        knowledge_id = args.get('knowledge_id')
        page_no = args.get('page_no')
        page_size = args.get('page_size')

        # 初始化数据库会话
        with self.session_scope() as session:
            # 构建查询条件
            q = session.query(AigcKnowledge).filter(
                AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE
            )
            if knowledge_id is not None:
                q = q.filter(AigcKnowledge.knowledge_id == knowledge_id)

            # 执行查询，不分页
            objs = q.all()

            # 获取总记录数
            total_count = len(objs)  # 如果没有分页，可以直接计算长度

            # 提取所有的 tag 字段并去重
            tag_list = []
            for obj in objs:
                if obj.tag:
                    # 将标签分割并去除空格
                    tags = [tag.strip() for tag in obj.tag.split(',') if tag.strip()]
                    tag_list.extend(tags)  # 合并所有标签

            # 去重
            tag_list = list(set(tag_list))

        return {
            'tags': tag_list,
            'total_count': total_count
        }


    def add_knowledge_tag(self, args):
        """
        向指定 knowledge_id 的知识记录中添加指定的 tag
        :param args: 包含 knowledge_id 和 tag_to_add 的字典
        :return: 添加结果
        """
        knowledge_id = args.get('knowledge_id')
        tags = args.get('tags')

        with self.session_scope() as session:
            # 查询包含指定 knowledge_id 的记录
            obj = session.query(AigcKnowledge).filter(
                AigcKnowledge.knowledge_id == knowledge_id,
                AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE
            ).first()

            if obj:
                # 去除标签中的空格
                tags = [tag.strip() for tag in tags]
                obj.tag = ','.join(tags)
                session.commit()

                data = {'message': '标签已更新', 'tags': tags}
            else:
                data = {'message': f'未找到指定 knowledge_id ({knowledge_id}) 的知识记录'}

        return data

    def get_knowledge_detail_v2(self, knowledge_id):
        """
        获取知识详情
        :param knowledge_id:
        :return:
        """
        obj = self.session.query(AigcKnowledge).filter(AigcKnowledge.knowledge_id == knowledge_id, AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE).first()
        data = dict()

        if obj:
            tp_user_name = obj.tp_user.tp_user_alias or obj.tp_user.tp_user_name if obj.tp_user else ''

            # Initialize the scheme field
            scheme = {
                "role_list": [],
                "dpt_list": [],
                "user_list": [],
                "auth_scheme_id": obj.auth_scheme_id
            }

            if obj.auth_scheme_id:
                # 获取角色列表
                roles = self.session.query(RelAuthSchemeRole).filter(RelAuthSchemeRole.scheme_id == obj.auth_scheme_id).all()
                scheme["role_list"] = [str(role.role_id) for role in roles]

                # 获取用户列表
                users = self.session.query(RelAuthSchemeUser).filter(RelAuthSchemeUser.scheme_id == obj.auth_scheme_id).all()
                for user in users:
                    tp_user = user.tp_user
                    scheme["user_list"].append({
                        "user_id": tp_user.tp_user_id,
                        "user_name": tp_user.tp_user_name,
                        "scheme_id": user.scheme_id,
                        "scheme_name": user.scheme.scheme_name
                    })

                # 获取部门列表，通过 RelAuthSchemeDepart 表关联
                departments = self.session.query(RelAuthSchemeDepart).filter(RelAuthSchemeDepart.scheme_id == obj.auth_scheme_id).all()
                scheme["dpt_list"] = [str(depart.depart_id) for depart in departments]

            tag_list = set()
            if obj.tag:
                # 去除空白并去重
                tag_list = set(tag.strip() for tag in obj.tag.split(',') if tag.strip())

            # 构造返回数据
            data = {
                'knowledge_id': str(obj.knowledge_id),
                'knowledge_name': obj.knowledge_name,
                'knowledge_desc': obj.knowledge_desc,
                'status': obj.status,
                'add_time': obj.add_time,
                'update_time': obj.update_time,
                'tp_user_name': tp_user_name,
                'icon_url': obj.icon_url,
                'model_name': obj.aigc_model.model_name,
                'model_path': obj.aigc_model.model_path,
                'aigc_model_id': obj.aigc_model_id,
                'dims': obj.aigc_model.dims,
                'aigc_type_name': obj.aigc_model.aigc_type.aigc_type_name,
                'aigc_type_id': obj.aigc_model.aigc_type_id,
                'role_list': scheme['role_list'],
                'dpt_list': scheme['dpt_list'],
                'user_list': scheme['user_list'],
                'auth_scheme_id': scheme['auth_scheme_id'],
                'open_life': obj.open_life,
                "scope": obj.scope,
                "parent_directory_id": str(obj.parent_directory_id),
                "graph_status": obj.graph_status,
                "graph_enable": obj.graph_enable,
                "tags":list(set(tag_list)),
                "scope_id": obj.scope_id
            }

        return data

    def get_knowledge_list(self, args):
        """
        获取知识列表
        """

        doc_subquery = self.session.query(AigcKnowledgeDocument.knowledge_id,
                                          func.count(AigcKnowledgeDocument.document_id).label('doc_count')).filter(
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE).group_by(
            AigcKnowledgeDocument.knowledge_id).subquery()
        chunk_subquery = self.session.query(AigcKnowledgeDocument.knowledge_id,
                                            func.sum(AigcKnowledgeDocumentChunk.character_count).label('character_count')).outerjoin(
            AigcKnowledgeDocument, AigcKnowledgeDocument.document_id == AigcKnowledgeDocumentChunk.doc_id).filter(
            AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE).group_by(
            AigcKnowledgeDocument.knowledge_id).subquery()
        app_subquery = self.session.query(RelAppKnowledge.knowledge_id, func.count(RelAppKnowledge.app_id).label('app_count')). \
            join(AigcApp, and_(RelAppKnowledge.app_id == AigcApp.app_id, RelAppKnowledge.delete_flag == settings.DELETE_FLAG_FALSE, AigcApp.delete_flag == settings.DELETE_FLAG_FALSE)). \
            group_by(RelAppKnowledge.knowledge_id).subquery()

        q = self.session.query(AigcKnowledge.knowledge_id, AigcKnowledge.knowledge_name, AigcKnowledge.knowledge_desc, AigcKnowledge.status,
                               AigcKnowledge.add_time, AigcKnowledge.icon_url, doc_subquery.c.doc_count, chunk_subquery.c.character_count,
                               app_subquery.c.app_count, AigcModel.model_name). \
            outerjoin(doc_subquery, AigcKnowledge.knowledge_id == doc_subquery.c.knowledge_id). \
            outerjoin(chunk_subquery, AigcKnowledge.knowledge_id == chunk_subquery.c.knowledge_id). \
            outerjoin(app_subquery, AigcKnowledge.knowledge_id == app_subquery.c.knowledge_id). \
            outerjoin(AigcModel, AigcKnowledge.aigc_model_id == AigcModel.aigc_model_id). \
            filter(AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE)

        if args.get('status'):
            q = q.filter(AigcKnowledge.status == args.get('status'))
        if args.get('model_path'):
            q = q.filter(AigcModel.model_path == args.get('model_path'))
        if args.get('knowledge_name'):
            q = q.filter(or_(AigcKnowledge.knowledge_name.like('%' + args.get('knowledge_name') + '%'),
                             AigcKnowledge.knowledge_desc.like('%' + args.get('knowledge_name') + '%')))
        q = q.order_by(AigcKnowledge.add_time.desc())
        # logger.info(q.statement.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True}))

        count = q.count()

        objs = q.slice((int(args['page_no']) - 1) * int(args['page_size']), int(args['page_size']) * int(args['page_no'])).all()
        res = []
        for obj in objs:
            res.append(
                {'knowledge_id': str(obj.knowledge_id), 'knowledge_name': obj.knowledge_name, 'knowledge_desc': obj.knowledge_desc,
                 'status': obj.status, 'add_time': obj.add_time, 'doc_count': obj.doc_count or 0, 'model_name': obj.model_name,
                 'character_count': obj.character_count or 0, 'app_count': obj.app_count or 0, 'icon_url': obj.icon_url})
        return {'total': count, 'po': args['page_no'], 'data_list': res}

    def get_knowledge_list_v1(self, args):
        """
        获取知识列表
        """
        # 定义子查询
        doc_subquery = self.session.query(AigcKnowledgeDocument.knowledge_id,
                                          func.count(AigcKnowledgeDocument.document_id).label('doc_count')).filter(
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE).group_by(
            AigcKnowledgeDocument.knowledge_id).subquery()
        chunk_subquery = self.session.query(AigcKnowledgeDocument.knowledge_id,
                                            func.sum(AigcKnowledgeDocumentChunk.character_count).label('character_count')).outerjoin(
            AigcKnowledgeDocument, AigcKnowledgeDocument.document_id == AigcKnowledgeDocumentChunk.doc_id).filter(
            AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE).group_by(
            AigcKnowledgeDocument.knowledge_id).subquery()
        app_subquery = self.session.query(RelAppKnowledge.knowledge_id, func.count(RelAppKnowledge.app_id).label('app_count')). \
            join(AigcApp, and_(RelAppKnowledge.app_id == AigcApp.app_id, RelAppKnowledge.delete_flag == settings.DELETE_FLAG_FALSE, AigcApp.delete_flag == settings.DELETE_FLAG_FALSE)). \
            group_by(RelAppKnowledge.knowledge_id).subquery()

        # 查寻所有 AigcKnowledge
        q = self.session.query(AigcKnowledge.knowledge_id, AigcKnowledge.knowledge_name, AigcKnowledge.knowledge_desc,
                               AigcKnowledge.status, AigcKnowledge.add_time, AigcKnowledge.icon_url, AigcKnowledge.update_time,
                               doc_subquery.c.doc_count, chunk_subquery.c.character_count,
                               app_subquery.c.app_count, AigcModel.model_name)

        # 继续外连接子查询和过滤条件
        q = q.outerjoin(doc_subquery, AigcKnowledge.knowledge_id == doc_subquery.c.knowledge_id). \
            outerjoin(chunk_subquery, AigcKnowledge.knowledge_id == chunk_subquery.c.knowledge_id). \
            outerjoin(app_subquery, AigcKnowledge.knowledge_id == app_subquery.c.knowledge_id). \
            outerjoin(AigcModel, AigcKnowledge.aigc_model_id == AigcModel.aigc_model_id). \
            filter(AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE)

        # 根据传入参数进行过滤
        if args.get('status'):
            q = q.filter(AigcKnowledge.status == args.get('status'))
        if args.get('model_path'):
            q = q.filter(AigcModel.model_path == args.get('model_path'))
        if args.get('name'):
            q = q.filter(or_(AigcKnowledge.knowledge_name.like('%' + args.get('name') + '%'),
                             AigcKnowledge.knowledge_desc.like('%' + args.get('name') + '%')))
        q = q.order_by(AigcKnowledge.add_time.desc())
        # logger.info(q.statement.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True}))

        count = q.count()

        # 分页和结果构建
        objs = q.slice((int(args['page']) - 1) * int(args['limit']), int(args['limit']) * int(args['page'])).all()
        res = []
        for obj in objs:
            res.append(
                {'id': str(obj.knowledge_id), 'name': obj.knowledge_name, 'description': obj.knowledge_desc,
                 'created_at': obj.add_time.timestamp(),
                 'document': obj.doc_count or 0, 'model_name': obj.model_name, 'updated_at': obj.update_time.timestamp(),
                 'word_count': obj.character_count or 0, 'app_count': obj.app_count or 0})
        has_more = False
        if count > int(args['limit']) * int(args['page']):
            has_more = True
        return {'total': count, 'limit': args['limit'], 'page': args['page'], 'has_more': has_more, 'data': res}

    def get_knowledge_list_by_directory_id(self, args):
        """
        获取知识列表
        """
        user_id = request.user['tp_user_id']
        scene_id = args.get('scene_id')
        parent_directory_id = args.get('parent_directory_id')
        # logger.info(f"user_id: {user_id}, \nrole_ids: {role_ids},\nscene_id:{scene_id}")
        # 定义子查询
        doc_subquery = self.session.query(AigcKnowledgeDocument.knowledge_id,
                                          func.count(AigcKnowledgeDocument.document_id).label('doc_count'),
                                          func.sum(AigcKnowledgeDocument.chunk_size).label('total_chunk_size')
                                          ).filter(
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE).group_by(
            AigcKnowledgeDocument.knowledge_id).subquery()
        # chunk_subquery = self.session.query(AigcKnowledgeDocument.knowledge_id,
        #                                     func.sum(AigcKnowledgeDocumentChunk.character_count).label('character_count')).outerjoin(
        #     AigcKnowledgeDocument, AigcKnowledgeDocument.document_id == AigcKnowledgeDocumentChunk.doc_id).filter(
        #     AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE).group_by(
        #     AigcKnowledgeDocument.knowledge_id).subquery()
        app_subquery = self.session.query(RelAppKnowledge.knowledge_id, func.count(RelAppKnowledge.app_id).label('app_count')). \
            join(AigcApp, and_(RelAppKnowledge.app_id == AigcApp.app_id, RelAppKnowledge.delete_flag == settings.DELETE_FLAG_FALSE, AigcApp.delete_flag == settings.DELETE_FLAG_FALSE)). \
            group_by(RelAppKnowledge.knowledge_id).subquery()

        # 初始化查询对象
        q = self.session.query(AigcKnowledge.knowledge_id, AigcKnowledge.knowledge_name, AigcKnowledge.knowledge_desc, AigcKnowledge.status,
                               AigcKnowledge.add_time, AigcKnowledge.icon_url, AigcKnowledge.scene_id, doc_subquery.c.doc_count,
                               app_subquery.c.app_count, AigcModel.model_name,AigcKnowledge.tag, AigcKnowledge.open_life, AigcKnowledge.scope,
                               AigcKnowledge.parent_directory_id, AigcKnowledge.graph_status, AigcKnowledge.graph_enable,
                               doc_subquery.c.total_chunk_size). \
            outerjoin(doc_subquery, AigcKnowledge.knowledge_id == doc_subquery.c.knowledge_id). \
            outerjoin(app_subquery, AigcKnowledge.knowledge_id == app_subquery.c.knowledge_id). \
            outerjoin(AigcModel, AigcKnowledge.aigc_model_id == AigcModel.aigc_model_id). \
            filter(AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE)

        # outerjoin(chunk_subquery, AigcKnowledge.knowledge_id == chunk_subquery.c.knowledge_id). \

        # 权限控制
        auth_scheme_ids = args['auth_scheme_ids']
        if auth_scheme_ids is not None: # 代表非超管
            if len(auth_scheme_ids) == 0:
                q = q.filter(AigcKnowledge.tp_user_id==user_id)
            else:
                q = q.filter(or_(AigcKnowledge.auth_scheme_id.in_(auth_scheme_ids),AigcKnowledge.tp_user_id==user_id))
        # logger.info(q.statement.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True}))
        # 继续处理筛选条件
        if args.get('status'):
            q = q.filter(AigcKnowledge.status == args.get('status'))
        if args.get('model_path'):
            q = q.filter(AigcModel.model_path == args.get('model_path'))
        if args.get('knowledge_name'):
            q = q.filter(or_(AigcKnowledge.knowledge_name.like('%' + args.get('knowledge_name') + '%'),
                             AigcKnowledge.knowledge_desc.like('%' + args.get('knowledge_name') + '%')))
        if args.get("scene_id"):
            q = q.filter(AigcKnowledge.scene_id == int(scene_id))
        if args.get("scope") or args.get("scope") == 0:
            q = q.filter(AigcKnowledge.scope == args.get("scope"))
        if args.get("scope_id"):
            q = q.filter(AigcKnowledge.scope_id == args.get("scope_id"))
        if parent_directory_id:
            q = q.filter(AigcKnowledge.parent_directory_id == parent_directory_id)
        if args.get("tag"):
            tags = args.get("tag")
            if isinstance(tags, str):  # 如果是字符串
                tags = tags.split(',')  # 拆分成标签列表
            # 构建 OR 条件，将每个标签匹配到的知识库合并
            or_conditions = []
            for tag in tags:
                tag = tag.strip()  # 去除标签前后的空格
                if tag:  # 如果标签非空
                    or_conditions.append(AigcKnowledge.tag.ilike(f"%{tag}%"))
            # 如果有 OR 条件，将其加入到查询
            if or_conditions:
                q = q.filter(or_(*or_conditions))
        q = q.order_by(AigcKnowledge.add_time.desc())
        # 统计记录总数
        count = q.count()

        # 分页并获取数据
        objs = q.slice((int(args['page_no']) - 1) * int(args['page_size']), int(args['page_size']) * int(args['page_no'])).all()

        # 构建返回结果
        res = []

        for obj in objs:
            tag_list = []

            if obj.tag:
                tags = [tag.strip() for tag in obj.tag.split(',') if tag.strip()]
                tag_list.extend(tags)
                if tags:  # 仅当标签列表非空时才添加
                    tag_list.extend(tags)
            res.append(
                {'knowledge_id': str(obj.knowledge_id), 'knowledge_name': obj.knowledge_name, 'knowledge_desc': obj.knowledge_desc,
                 'status': obj.status, 'add_time': obj.add_time, 'doc_count': obj.doc_count or 0, 'model_name': obj.model_name,
                 'app_count': obj.app_count or 0, 'icon_url': obj.icon_url, 'open_life': obj.open_life, 'scope': obj.scope,
                 'parent_directory_id': str(obj.parent_directory_id), 'graph_status': obj.graph_status, 'graph_enable': obj.graph_enable,
                 'total_chunk_size': obj.total_chunk_size or 0,'tags': list(set(tag_list))})

        return {'total': count, 'po': args['page_no'], 'data_list': res}

    def get_all_knowledge_list(self):
        q =self.session.query(AigcKnowledge, AigcKnowledge.knowledge_id,AigcKnowledge.knowledge_name).filter(AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE,AigcKnowledge.status==1)
        q = q.order_by(AigcKnowledge.add_time.desc())
        # 统计记录总数
        count = q.count()
        res = []
        objs =q.all()
        for obj in objs:
            res.append({'knowledge_id': str(obj.knowledge_id), 'knowledge_name': obj.knowledge_name})
        return {'total': count, 'data_list': res}


    def get_knowledge_list_v2(self, args):
        """
        获取知识列表
        """
        user_id = request.user['tp_user_id']
        scene_id = args.get('scene_id')
        # logger.info(f"user_id: {user_id}, \nrole_ids: {role_ids},\nscene_id:{scene_id}")
        # 定义子查询
        doc_subquery = self.session.query(AigcKnowledgeDocument.knowledge_id,
                                          func.count(AigcKnowledgeDocument.document_id).label('doc_count'),
                                          func.sum(AigcKnowledgeDocument.chunk_size).label('total_chunk_size')
                                          ).filter(
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE).group_by(
            AigcKnowledgeDocument.knowledge_id).subquery()
        # chunk_subquery = self.session.query(AigcKnowledgeDocument.knowledge_id,
        #                                     func.sum(AigcKnowledgeDocumentChunk.character_count).label('character_count')).outerjoin(
        #     AigcKnowledgeDocument, AigcKnowledgeDocument.document_id == AigcKnowledgeDocumentChunk.doc_id).filter(
        #     AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE).group_by(
        #     AigcKnowledgeDocument.knowledge_id).subquery()
        app_subquery = self.session.query(RelAppKnowledge.knowledge_id, func.count(RelAppKnowledge.app_id).label('app_count')). \
            join(AigcApp, and_(RelAppKnowledge.app_id == AigcApp.app_id, RelAppKnowledge.delete_flag == settings.DELETE_FLAG_FALSE, AigcApp.delete_flag == settings.DELETE_FLAG_FALSE)). \
            group_by(RelAppKnowledge.knowledge_id).subquery()
        
        rule_subquery = self.session.query(RelFileRule.knowledge_id, func.count(RelFileRule.rule_id).label('split_rule_num')). \
            join(FileClearRule, and_(RelFileRule.rule_id == FileClearRule.id)). \
            group_by(RelFileRule.knowledge_id).subquery()   
        
        doc_rule_subquery = self.session.query(KnowledgeClearRelation.knowledge_id, func.count(KnowledgeClearRelation.rule_id).label('doc_rule_num')). \
            join(DocClearRule, and_(KnowledgeClearRelation.rule_id == DocClearRule.rule_id)). \
            group_by(KnowledgeClearRelation.knowledge_id).subquery()   

        # 初始化查询对象
        q = self.session.query(AigcKnowledge.knowledge_id, AigcKnowledge.knowledge_name, AigcKnowledge.knowledge_desc, AigcKnowledge.status,
                               AigcKnowledge.add_time, AigcKnowledge.icon_url, AigcKnowledge.scene_id,AigcKnowledge.tag, doc_subquery.c.doc_count,
                               app_subquery.c.app_count, AigcModel.model_name, AigcKnowledge.open_life, AigcKnowledge.scope,
                               AigcKnowledge.parent_directory_id, AigcKnowledge.graph_status, AigcKnowledge.graph_enable,
                               doc_subquery.c.total_chunk_size, rule_subquery.c.split_rule_num, doc_rule_subquery.c.doc_rule_num,AigcKnowledge.scope_id). \
            outerjoin(doc_subquery, AigcKnowledge.knowledge_id == doc_subquery.c.knowledge_id). \
            outerjoin(app_subquery, AigcKnowledge.knowledge_id == app_subquery.c.knowledge_id). \
            outerjoin(AigcModel, AigcKnowledge.aigc_model_id == AigcModel.aigc_model_id). \
            outerjoin(rule_subquery, AigcKnowledge.knowledge_id == rule_subquery.c.knowledge_id). \
            outerjoin(doc_rule_subquery, AigcKnowledge.knowledge_id == doc_rule_subquery.c.knowledge_id). \
            filter(AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE)

        # outerjoin(chunk_subquery, AigcKnowledge.knowledge_id == chunk_subquery.c.knowledge_id). \

        # 权限控制
        auth_scheme_ids = args['auth_scheme_ids']
        print(f"auth_scheme_ids: {auth_scheme_ids}")
        if auth_scheme_ids is not None: # 代表非超管
            if len(auth_scheme_ids) == 0:
                q = q.filter(AigcKnowledge.tp_user_id==user_id)
            else:
                q = q.filter(or_(AigcKnowledge.auth_scheme_id.in_(auth_scheme_ids),AigcKnowledge.tp_user_id==user_id))
        # logger.info(q.statement.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True}))
        # logger.info('=============')
        if args.get('status') is not None:
            status = int(args.get('status'))
            q = q.filter(AigcKnowledge.status == status)
        if args.get('model_path'):
            q = q.filter(AigcModel.model_path == args.get('model_path'))
        if args.get('knowledge_name'):
            q = q.filter(or_(AigcKnowledge.knowledge_name.like('%' + args.get('knowledge_name') + '%'),
                             AigcKnowledge.knowledge_desc.like('%' + args.get('knowledge_name') + '%')))
        if args.get("scene_id"):
            q = q.filter(AigcKnowledge.scene_id == int(scene_id))
        if args.get("scope") or args.get("scope") == 0:
            q = q.filter(AigcKnowledge.scope == args.get("scope"))
        if args.get("scope_id"):
            q = q.filter(AigcKnowledge.scope_id == args.get("scope_id"))

        if args.get("tag"):
            tags = args.get("tag")
            if isinstance(tags, str):  # 如果是字符串
                tags = tags.split(',')  # 拆分成标签列表
            # 构建 OR 条件，将每个标签匹配到的知识库合并
            or_conditions = []
            for tag in tags:
                tag = tag.strip()  # 去除标签前后的空格
                if tag:  # 如果标签非空
                    or_conditions.append(AigcKnowledge.tag.ilike(f"%{tag}%"))
            # 如果有 OR 条件，将其加入到查询
            if or_conditions:
                q = q.filter(or_(*or_conditions))

        q = q.order_by(AigcKnowledge.add_time.desc())
        # 统计记录总数
        count = q.count()

        # 分页并获取数据
        objs = q.slice((int(args['page_no']) - 1) * int(args['page_size']), int(args['page_size']) * int(args['page_no'])).all()

        # 构建返回结果
        res = []

        for obj in objs:
            tag_list = []

            if obj.tag:
                tags = [tag.strip() for tag in obj.tag.split(',') if tag.strip()]
                tag_list.extend(tags)
            res.append(
                {'knowledge_id': str(obj.knowledge_id), 'knowledge_name': obj.knowledge_name, 'knowledge_desc': obj.knowledge_desc,
                 'status': obj.status, 'add_time': obj.add_time, 'doc_count': obj.doc_count or 0, 'model_name': obj.model_name,
                 'app_count': obj.app_count or 0, 'icon_url': obj.icon_url, 'open_life': obj.open_life, 'scope': obj.scope,
                 'parent_directory_id': str(obj.parent_directory_id), 'graph_status': obj.graph_status, 'graph_enable': obj.graph_enable,
                 'total_chunk_size': obj.total_chunk_size or 0,'tags': list(set(tag_list)),'split_rule_num': obj.split_rule_num or 0,'doc_rule_num': obj.doc_rule_num or 0,'scope_id': obj.scope_id or 0})

        return {'total': count, 'po': args['page_no'], 'data_list': res}

    def get_all_scope(self):
        q = self.session.query(DictScope).filter(DictScope.delete_flag == settings.DELETE_FLAG_FALSE)
        objs = q.all()
        res = []
        for obj in objs:
            res.append({'scope_id': obj.scope_id, 'scope_name': obj.scope_name})
        return res

    def get_personal_q(self, args,q):
        user_id = args.get('tp_user_id')
        all_scheme_ids = args.get('all_scheme_ids')
        if all_scheme_ids:
            logger.info("有权限id")
            q1 = q.filter(AigcKnowledge.scope == 1,AigcKnowledge.auth_scheme_id.in_(all_scheme_ids))
            q2 = q.filter(AigcKnowledge.scope == 1,AigcKnowledge.tp_user_id == user_id)
            q = q1.union(q2)
        else:
            logger.info("没权限id")
            q = q.filter(AigcKnowledge.scope == 1,AigcKnowledge.tp_user_id == user_id)
        return q
    
    def get_knowledge_document_images_and_tables(self, args):
        """
        批量获取知识文档分片CSV
        :param doc_id: 文档ID
        """
        page_no = args.get('page_no')
        page_size = int(args.get('page_size', 10))
        doc_id = args.get('doc_id')
        # 构建查询
        if doc_id:
            q = self.session.query(AigcKnowledgeDocumentTableChunk).filter(AigcKnowledgeDocumentTableChunk.doc_id == doc_id,AigcKnowledgeDocumentTableChunk.delete_flag == settings.DELETE_FLAG_FALSE)
        else:
            q = self.session.query(AigcKnowledgeDocumentTableChunk).filter(AigcKnowledgeDocumentTableChunk.delete_flag == settings.DELETE_FLAG_FALSE)

        # 先计算总数
        total = q.count()

        # 排序和分页
        q = q.order_by(AigcKnowledgeDocumentTableChunk.add_time.desc())
        q = q.slice((page_no - 1) * page_size, page_no * page_size).all()

        res = []
        for obj in q:
            res.append({
                'table_chunk_id': str(obj.table_chunk_id),
                'doc_id': str(obj.doc_id),
                'table_name': obj.table_name,
                'table_url': obj.table_url,
                'image_url': obj.image_url,
                'table_len': obj.table_len,
                'table_desc': obj.table_desc
            })
        return {'total': total, 'po': page_no, 'data_list': res}


    def add_knowledge_doc_chunk_csv_in_batch(self, doc_id, csv_list, batch_size=100):
        """
        批量添加知识文档分片CSV
        :param doc_id: 文档ID
        :param csv_list: 一个包含多个CSV路径的列表
        :param batch_size: 每次批量插入的最大数量，默认为 100
        :return: 插入的所有文档分片的 chunk_id 列表
        """
        inserted_table_chunk_ids = []

        def prepare_data(csv_dict):
            table_name = csv_dict.get('csv_url').split('/')[-1].split('.')[0]
            table_url = csv_dict.get('csv_url')
            table_chunk_id = get_snowflake_id()
            image_url = csv_dict.get('png_url')
            table_len = csv_dict.get('table_len')
            table_desc = csv_dict.get('csv_content')
            return {
                'table_chunk_id': table_chunk_id,
                'doc_id': doc_id,
                'table_name': table_name,
                'table_url': table_url,
                'image_url': image_url,
                'table_len': table_len,
                'table_desc': table_desc
            }

        try:
            for i in range(0, len(csv_list), batch_size):
                csv_batch = csv_list[i:i + batch_size]
                data_to_insert = [prepare_data(csv_dict) for csv_dict in csv_batch]
                self.session.bulk_insert_mappings(AigcKnowledgeDocumentTableChunk, data_to_insert)
                self.session.commit()
                inserted_table_chunk_ids.extend([str(item['table_chunk_id']) for item in data_to_insert])
        except Exception as e:
            self.session.rollback()
            logger.error(f"插入数据时发生错误: {e}")
            return []

        return inserted_table_chunk_ids

    def get_model_path(self, knowledge_id):
        result = (
            self.session.query(AigcModel.model_path)
            .join(AigcKnowledge, AigcModel.aigc_model_id == AigcKnowledge.aigc_model_id)
            .filter(AigcKnowledge.knowledge_id == knowledge_id)
            .first()
        )
        return result.model_path if result else None

    
    def add_knowledge_doc_chunk_in_batch(self, doc_chunks, batch_size=100):
        """
        批量添加知识文档分片
        :param doc_chunks: 一个包含多个文档分片信息的列表，每个元素是一个字典，包含 doc_id、content、character_count 和 result
        :param batch_size: 每次批量插入的最大数量，默认为 100
        :return: 插入的所有文档分片的 chunk_id 列表
        """
        inserted_chunk_ids = []
        for i in range(0, len(doc_chunks), batch_size):
            batch = doc_chunks[i:i + batch_size]
            data_to_insert = []
            for chunk in batch:
                doc_id = chunk.get('doc_id')
                content = chunk.get('content')
                character_count = chunk.get('character_count')
                result = chunk.get('result')
                chunk_id = get_snowflake_id()
                data = {
                    'chunk_id': chunk_id,
                    'doc_id': doc_id,
                    'content': content,
                    'character_count': character_count,
                    'result': result
                }
                data_to_insert.append(data)
                inserted_chunk_ids.append(str(chunk_id))

            self.session.bulk_insert_mappings(AigcKnowledgeDocumentChunk, data_to_insert)
            self.session.commit()

        return inserted_chunk_ids
    
    def add_chunk_images_in_batch(self, chunk_image_list, batch_size=100):
        """
        批量插入知识文档分片对应的图片信息
        :param chunk_image_list: 一个列表，列表中的每个元素是一个字典，字典包含 'chunk_id' 和 'image_urls' 键
        :param batch_size: 每次批量插入的最大数量
        :return: 插入的所有分片 ID 列表
        """
        inserted_chunk_ids = []
        for i in range(0, len(chunk_image_list), batch_size):
            batch = chunk_image_list[i:i + batch_size]
            data_to_insert = []
            for item in batch:
                chunk_id = item.get('chunk_id')
                image_urls = item.get('image_urls', [])
                for url in image_urls:
                    record = {
                        'chunk_image_id': int(get_snowflake_id()),
                        'chunk_id': int(chunk_id),
                        'image_url': url
                    }
                    data_to_insert.append(record)
                inserted_chunk_ids.append(str(chunk_id))
            task_logger.info(f"data_to_insert: {data_to_insert}")
            self.session.bulk_insert_mappings(AigcKnowledgeDocuChunkImage, data_to_insert)
            self.session.commit()
        return inserted_chunk_ids
    
    
    def add_chunk_image_in_batch(self, chunk_image_list, batch_size=100):
        """
        批量插入知识文档分片对应的图片信息
        :param chunk_image_list: 一个列表，列表里每个元素是字典，字典需包含 'chunk_id' 和 'image_url' 键
        :param batch_size: 每次批量插入的最大数量，默认为 100
        :return: 插入的所有图片的 chunk_image_id 列表
        """
        inserted_chunk_image_ids = []
        for i in range(0, len(chunk_image_list), batch_size):
            batch = chunk_image_list[i: i + batch_size]
            data_to_insert = []
            for item in batch:
                chunk_id = item.get('chunk_id')
                image_url = item.get('image_url')
                if chunk_id is not None and image_url is not None:
                    chunk_image_id = int(get_snowflake_id())
                    data_to_insert.append({
                        'chunk_image_id': chunk_image_id,
                        'chunk_id': chunk_id,
                        'image_url': image_url
                    })
                    inserted_chunk_image_ids.append(chunk_image_id)

            try:
                # 批量插入数据
                self.session.bulk_insert_mappings(AigcKnowledgeDocuChunkImage, data_to_insert)
                # 提交事务
                self.session.commit()
            except Exception as e:
                task_logger.error(f"插入数据时出现错误: {e}")
                # 可以选择跳过这一批数据或者进行其他处理
                # 这里简单地跳过这一批数据
                continue

        return inserted_chunk_image_ids
    
    def get_abstract_list(self, doc_ids):
        if not doc_ids:
            return []

        with self.session_scope() as session:
            objs = session.query(AigcKnowledgeDocument.abstract, AigcKnowledgeDocument.doc_name, AigcKnowledgeDocument.document_id).filter(AigcKnowledgeDocument.document_id.in_(doc_ids)).all()
            return [{'abstract': obj.abstract, 'doc_name': obj.doc_name, 'doc_id': obj.document_id} for obj in objs]

    
    def get_pubilc_q(self, args,q):
        all_scheme_ids = args.get('all_scheme_ids')
        if all_scheme_ids:
            logger.info("有权限id")
            q = q.filter(AigcKnowledge.scope == 0,AigcKnowledge.auth_scheme_id.in_(all_scheme_ids))
        else:
            logger.info("没权限id")
            q = q.filter(AigcKnowledge.scope == 0)
        return q    
  
    def get_knowledge_document_count(self, knowledge_id):
        # 获取知识库文档数量
        doc_count = self.session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.knowledge_id == knowledge_id,AigcKnowledgeDocument.upload_type == 0,
                                                                AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE).count()
        # 获取知识库绑定的应用数量
        app_count = self.session.query(RelAppKnowledge).filter(RelAppKnowledge.knowledge_id == knowledge_id,
                                                                RelAppKnowledge.delete_flag == settings.DELETE_FLAG_FALSE).count()
        # 获取知识库绑定的视频数量
        video_count = self.session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.knowledge_id == knowledge_id,AigcKnowledgeDocument.upload_type == 1,
                                                                AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE).count()
        # 获取知识库绑定的音频数量  
        audio_count = self.session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.knowledge_id == knowledge_id,AigcKnowledgeDocument.upload_type == 2,
                                                                AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE).count()
        return {
            'doc_count': doc_count,
            'app_count': app_count,
            'video_count': video_count,
            'audio_count': audio_count
        }

    def get_knowledge_list_gt(self, args, user_id = ''):
        """
        获取知识列表
        """
        doc_query = [AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE, AigcKnowledgeDocument.result == 'FINISHED'] if IS_910B_EMBEDDINGS else [AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE]
        # 定义子查询
        doc_subquery = self.session.query(AigcKnowledgeDocument.knowledge_id,
                                          func.count(AigcKnowledgeDocument.document_id).label('doc_count')).filter(
            *doc_query).group_by(
            AigcKnowledgeDocument.knowledge_id).subquery()
        app_subquery = self.session.query(RelAppKnowledge.knowledge_id, func.count(RelAppKnowledge.app_id).label('app_count')). \
            join(AigcApp, and_(RelAppKnowledge.app_id == AigcApp.app_id, RelAppKnowledge.delete_flag == settings.DELETE_FLAG_FALSE, AigcApp.delete_flag == settings.DELETE_FLAG_FALSE)). \
            group_by(RelAppKnowledge.knowledge_id).subquery()

        # 初始化查询对象
        q = self.session.query(AigcKnowledge.knowledge_id, AigcKnowledge.knowledge_name, AigcKnowledge.knowledge_desc, AigcKnowledge.status,
                               AigcKnowledge.add_time, AigcKnowledge.icon_url, AigcKnowledge.scene_id, doc_subquery.c.doc_count,
                               app_subquery.c.app_count, AigcModel.model_name, AigcKnowledge.open_life). \
            outerjoin(doc_subquery, AigcKnowledge.knowledge_id == doc_subquery.c.knowledge_id). \
            outerjoin(app_subquery, AigcKnowledge.knowledge_id == app_subquery.c.knowledge_id). \
            outerjoin(AigcModel, AigcKnowledge.aigc_model_id == AigcModel.aigc_model_id). \
            filter(AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE)
        # 权限过滤
        # TODO: 常量
        if args.get('type') == 1:
            q = self.get_personal_q(args,q)
        else:
            q1 = self.get_pubilc_q(args,q)
            q2 = self.get_personal_q(args,q)
            q = q1.union(q2)
        if args.get('knowledge_name'):
            q = q.filter(or_(AigcKnowledge.knowledge_name.like('%' + args.get('knowledge_name') + '%'),
                             AigcKnowledge.knowledge_desc.like('%' + args.get('knowledge_name') + '%')))
        q = q.order_by(AigcKnowledge.add_time.desc())
        count = q.count()
        # 分页并获取数据
        objs = q.slice((int(args['page_no']) - 1) * int(args['page_size']), int(args['page_size']) * int(args['page_no'])).all()
        # 构建返回结果
        res = []
        for obj in objs:
            res.append({'knowledge_id': str(obj.knowledge_id), 'knowledge_name': obj.knowledge_name,
                        'knowledge_desc': obj.knowledge_desc, 'status': obj.status, 'add_time': obj.add_time,
                        'doc_count': obj.doc_count or 0, 'model_name': obj.model_name, 'app_count': obj.app_count or 0,
                        'icon_url': obj.icon_url, 'open_life': obj.open_life})
        return {'total': count, 'po': args['page_no'], 'data_list': res}

    def get_descriptions_list_by_knowledge_ids(self, knowledge_ids):
        if not knowledge_ids:
            return []
        with self.session_scope() as session:
            objs = session.query(AigcKnowledgeDocument.description, AigcKnowledgeDocument.doc_name, AigcKnowledgeDocument.document_id).filter(AigcKnowledgeDocument.knowledge_id.in_(knowledge_ids)).all()
            descriptions_list =  [obj.description for obj in objs]
            descriptions_list = filter(lambda x: x, descriptions_list)
            return list(descriptions_list)    



    def add_knowledge(self, knowledge_name, knowledge_desc, icon_url, aigc_model_id, tp_user_id, auth_scheme_id, scene_id,
                      scope, parent_directory_id, graph_enable,tag,scope_id):
        """
        添加知识
        :param knowledge_name:
        :param knowledge_desc:
        :param icon_url:
        :param aigc_model_id:
        :param tp_user_id:
        :return:
        """
        knowledge_id = get_snowflake_id()
        obj = AigcKnowledge(knowledge_id=knowledge_id, knowledge_name=knowledge_name, knowledge_desc=knowledge_desc, icon_url=icon_url,
                            aigc_model_id=aigc_model_id, tp_user_id=tp_user_id, auth_scheme_id=auth_scheme_id,
                            scene_id=scene_id, scope=scope,parent_directory_id=parent_directory_id,
                            graph_enable=graph_enable,tag=tag,scope_id=scope_id)
        self.session.add(obj)
        self.session.commit()
        return str(knowledge_id)

    def update_knowledge(self, knowledge_id, args):
        """
        更新知识
        :param knowledge_id:
        :param args:
        :return: knowledge_id
        """
        self.session.query(AigcKnowledge).filter(AigcKnowledge.knowledge_id == knowledge_id).update(args)
        self.session.commit()
        return str(knowledge_id)

    def check_knowledge_doc(self, knowledge_id):
        return self.session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.knowledge_id == knowledge_id,
                                                                AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE).count()

    def get_knowledge_doc_list(self, knowledge_id, args):
        """
        获取知识文档列表
        :param knowledge_id:
        :param args
        :return:
        """
        # subquery = self.session.query(AigcKnowledgeDocumentChunk.doc_id, AigcKnowledgeDocumentChunk.content,
        #                               func.sum(AigcKnowledgeDocumentChunk.tokens).label('tokens'),
        #                               func.sum(AigcKnowledgeDocumentChunk.hit_count).label('hit_count'),
        #                               func.sum(AigcKnowledgeDocumentChunk.character_count).label('character_count')). \
        #     filter(AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE). \
        #     group_by(AigcKnowledgeDocumentChunk.doc_id).subquery()
        q = self.session.query(AigcKnowledgeDocument, AigcKnowledge.knowledge_name).join(
            AigcKnowledge, AigcKnowledge.knowledge_id == AigcKnowledgeDocument.knowledge_id).filter(AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE)
        if knowledge_id:
            q = q.filter(AigcKnowledgeDocument.knowledge_id == knowledge_id)
        if args.get('doc_ids'):
            q = q.filter(AigcKnowledgeDocument.document_id.in_(args.get('doc_ids')))
        if args.get('doc_name'):
            q = q.filter(AigcKnowledgeDocument.doc_name.like('%' + args.get('doc_name') + '%'))
        if args.get('results'):
            q = q.filter(AigcKnowledgeDocument.result.in_(args.get('results')))
        if args.get('status'):
            q = q.filter(AigcKnowledgeDocument.status == args.get('status'))
        if args.get('doc_ids'):
            q = q.filter(AigcKnowledgeDocument.document_id.in_(args.get('doc_ids')))
        if args.get('bgn_id') and args.get('end_id'):
            q = q.filter(AigcKnowledgeDocument.document_id.between(args.get('bgn_id'), args.get('end_id')))
        if args.get('graph_content') == 1:
            q = q.filter(AigcKnowledgeDocument.graph_content.is_(None))
        if args.get('is_abstract') == 1:
            q = q.filter(AigcKnowledgeDocument.abstract.is_(None))
        if args.get('current_node'):
            q = q.filter(AigcKnowledgeDocument.current_node == args.get('current_node'))
        if args.get('state'):
            q = q.filter(AigcKnowledgeDocument.state == args.get('state'))
        if args.get('upload_type'):
            upload_type = int(args.get('upload_type'))
            q = q.filter(AigcKnowledgeDocument.upload_type == upload_type)

        q = q.order_by(AigcKnowledgeDocument.add_time.desc())

        # logger.info(q.statement.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True}))
        if args.get('is_all') == 'all':
            objs = q.all()
            logger.info(f"objs: {objs}")
        else:
            count = q.count()
            objs = q.slice((int(args['page_no']) - 1) * int(args['page_size']), int(args['page_size']) * int(args['page_no'])).all()

        key_frame = ''
        video_duration = ''
        frame_rate = ''
        width = ''
        height = ''

        data_list = list()
        for obj in objs:
            if args.get('upload_type', "0") == "1":
                video_doc = self.session.query(AigcKnowledgeVideo).filter(AigcKnowledgeVideo.doc_id == str(obj.AigcKnowledgeDocument.document_id)).first()
                key_frame = video_doc.key_frame if video_doc else ''
                video_duration = video_doc.video_duration if video_doc else ''
                frame_rate = video_doc.frame_rate if video_doc else ''
                width = video_doc.width if video_doc else ''
                height = video_doc.height if video_doc else ''
                video_duration = ms_to_hms(video_duration)
            data_list.append({'document_id': str(obj.AigcKnowledgeDocument.document_id), 'doc_name': obj.AigcKnowledgeDocument.doc_name,
                              'chunk_size': obj.AigcKnowledgeDocument.chunk_size, 'doc_size': obj.AigcKnowledgeDocument.doc_size,
                              'doc_type': obj.AigcKnowledgeDocument.doc_type, 'status': obj.AigcKnowledgeDocument.status,
                              'add_time': obj.AigcKnowledgeDocument.add_time, 'doc_url': obj.AigcKnowledgeDocument.doc_url,
                              'tokens': obj.AigcKnowledgeDocument.tokens, 'hit_count': obj.AigcKnowledgeDocument.hit_count,
                              'character_count': obj.AigcKnowledgeDocument.character_count,
                              'content_id': obj.AigcKnowledgeDocument.content_id or '',
                              'knowledge_name': obj.knowledge_name, 'doc_id': str(obj.AigcKnowledgeDocument.document_id),
                              'abstract': obj.AigcKnowledgeDocument.abstract or '',
                              'description': obj.AigcKnowledgeDocument.description or '',
                              'graph_content': obj.AigcKnowledgeDocument.graph_content or '',
                              'open_life': obj.AigcKnowledgeDocument.open_life,
                              'content_time': obj.AigcKnowledgeDocument.content_time,
                              'state': obj.AigcKnowledgeDocument.state,
                              'current_node': obj.AigcKnowledgeDocument.current_node,
                              'key_frame': key_frame,
                              'video_duration': video_duration,
                              'frame_rate': frame_rate,
                              'width': width,
                              'height': height,
                              'upload_type': args.get('upload_type', "0"),
                              'upload_type_content': 'int 0: 文档类型, 1: 视频类型',
                              'have_table': obj.AigcKnowledgeDocument.have_table,
                              'source_type': obj.AigcKnowledgeDocument.source_type,
                              'doc_source_id': obj.AigcKnowledgeDocument.doc_source_id or '',
                              'is_sharing': obj.AigcKnowledgeDocument.is_sharing,
                              'mind_map': obj.AigcKnowledgeDocument.mind_map,
                              'main_points': obj.AigcKnowledgeDocument.main_points,
                              'key_words': obj.AigcKnowledgeDocument.key_words,
                              })

        if args.get('is_all') == 'all':
            return {'data_list': data_list}
        else:
            return {'total': count, 'po': args['page_no'], 'data_list': data_list}

    def get_knowledge_document_upload_history(self, args):
        """
        获取知识文档上传历史
        :param args:
        :return:
        """
        page_no = args.get('page_no', 1)
        page_size = args.get('page_size', 10)
        knowledge_id = args.get('knowledge_id')

        # 主查询（分组统计）
        main_query = self.session.query(
            AigcKnowledgeDocument.tp_user_id,
            AigcKnowledgeDocument.add_time,
            func.count(AigcKnowledgeDocument.document_id).label('doc_count'),
            func.sum(func.cast(AigcKnowledgeDocument.result == "FINISHED", Integer)).cast(Integer).label('finished_count')
        ).filter(
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        )

        if knowledge_id:
            main_query = main_query.filter(AigcKnowledgeDocument.knowledge_id == knowledge_id)

        # 分组并排序
        grouped_query = main_query.group_by(
            AigcKnowledgeDocument.tp_user_id,
            AigcKnowledgeDocument.add_time
        ).order_by(AigcKnowledgeDocument.add_time.desc())

        # 计算总数量
        total_count = grouped_query.count()

        # 分页处理
        if page_no:
            grouped_query = grouped_query.slice((page_no - 1) * page_size, page_no * page_size)

        # 获取分组统计结果
        groups = grouped_query.all()

        # 子查询获取文档详情
        doc_subquery = self.session.query(
            AigcKnowledgeDocument.document_id,
            AigcKnowledgeDocument.tp_user_id,
            AigcKnowledgeDocument.add_time,
            AigcKnowledgeDocument.doc_name,
            AigcKnowledgeDocument.current_node,
            AigcKnowledgeDocument.state
        ).filter(
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        )

        if knowledge_id:
            doc_subquery = doc_subquery.filter(AigcKnowledgeDocument.knowledge_id == knowledge_id)

        # 构建结果集
        result = {
            'total': total_count,
            'po': page_no,
            'data_list': []
        }

        # 合并分组统计和文档详情
        for group in groups:
            group_key = (group.tp_user_id, group.add_time)
            
            # 获取该分组下的所有文档
            docs = doc_subquery.filter(
                AigcKnowledgeDocument.tp_user_id == group.tp_user_id,
                AigcKnowledgeDocument.add_time == group.add_time
            ).all()
            user = self.session.query(TouchpointUser).filter(TouchpointUser.tp_user_id == group.tp_user_id).first()
            tp_user_name = user.tp_user_name if user else None
            # 计算状态
            status = '已完成' if group.finished_count == group.doc_count else '进行中'

            result['data_list'].append({
                'add_time': group.add_time,
                'tp_user_id': group.tp_user_id,
                'tp_user_name': tp_user_name,
                'doc_count': group.doc_count,
                'finished_count': group.finished_count or 0,
                'status': status,
                'docs': [{
                    'doc_id': str(doc.document_id),
                    'doc_name': doc.doc_name,
                    'current_node': doc.current_node,
                    'state': doc.state
                } for doc in docs]
            })

        return result

    def openapi_knowledge_doc_list(self, knowledge_id, args):
        """
        获取知识文档列表
        :param knowledge_id:
        :param args
        :return:
        """
        # subquery = self.session.query(AigcKnowledgeDocumentChunk.doc_id, AigcKnowledgeDocumentChunk.content,
        #                               func.sum(AigcKnowledgeDocumentChunk.tokens).label('tokens'),
        #                               func.sum(AigcKnowledgeDocumentChunk.hit_count).label('hit_count'),
        #                               func.sum(AigcKnowledgeDocumentChunk.character_count).label('character_count')). \
        #     filter(AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE). \
        #     group_by(AigcKnowledgeDocumentChunk.doc_id).subquery()
        q = self.session.query(AigcKnowledgeDocument, AigcKnowledge.knowledge_name).join(
            AigcKnowledge, AigcKnowledge.knowledge_id == AigcKnowledgeDocument.knowledge_id).filter(AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE)
        if knowledge_id:
            q = q.filter(AigcKnowledgeDocument.knowledge_id == knowledge_id)
        if args.get('doc_ids'):
            q = q.filter(AigcKnowledgeDocument.document_id.in_(args.get('doc_ids')))
        if args.get('keyword'):
            q = q.filter(AigcKnowledgeDocument.doc_name.like('%' + args.get('keyword') + '%'))
        if args.get('results'):
            q = q.filter(AigcKnowledgeDocument.result.in_(args.get('results')))
        if args.get('status'):
            q = q.filter(AigcKnowledgeDocument.status == args.get('status'))
        if args.get('doc_ids'):
            q = q.filter(AigcKnowledgeDocument.document_id.in_(args.get('doc_ids')))

        q = q.order_by(AigcKnowledgeDocument.add_time.desc())
        # logger.info(q.statement.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True}))

        count = q.count()
        objs = q.slice((int(args['page']) - 1) * int(args['limit']), int(args['limit']) * int(args['page'])).all()
        data_list = list()
        for obj in objs:
            data_list.append({'id': str(obj.AigcKnowledgeDocument.document_id), 'name': obj.AigcKnowledgeDocument.doc_name,
                              'chunk_size': obj.AigcKnowledgeDocument.chunk_size, 'indexing_status': obj.AigcKnowledgeDocument.status,
                              'created_at': obj.AigcKnowledgeDocument.add_time.timestamp(),
                              'update_at': obj.AigcKnowledgeDocument.update_time.timestamp(),
                              'tokens': obj.AigcKnowledgeDocument.tokens, 'hit_count': obj.AigcKnowledgeDocument.hit_count,
                              'word_count': obj.AigcKnowledgeDocument.character_count})
        has_more = False
        if count > int(args['limit']) * int(args['page']):
            has_more = True
        return {'total': count, 'page': args['page'], 'limit': args['limit'], 'has_more': has_more, 'data': data_list}

    def del_knowledge_doc(self, document_id):
        """
        删除知识文档
        :param document_id:
        :return:del_knowledge_doc
        """
        self.session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.document_id == document_id).update({'delete_flag': settings.DELETE_FLAG_TRUE})
        self.session.commit()
        return str(document_id)

    def del_knowledge_doc_by_qp(self, qp):
        """
        删除知识文档
        :param document_id:
        :return:del_knowledge_doc
        """
        with self.session_scope() as session:
            session.query(AigcKnowledgeDocument).filter_by(**qp).update({'delete_flag': settings.DELETE_FLAG_TRUE})
            session.commit()
        return True

    def add_knowledge_document_images_batch(self, doc_id, image_url_list,batch_size=100):
        """
        添加知识文档图片
        :param doc_id:
        :param image_url_list:
        :return:
        """
        inserted_image_ids = []
        for i in range(0, len(image_url_list), batch_size):
            batch = image_url_list[i:i + batch_size]
            data_to_insert = []
            for image_url in batch:
                image_id = get_snowflake_id()
                data = {
                    'image_id': image_id,
                    'doc_id': doc_id,
                    'image_url': image_url
                }
                data_to_insert.append(data)
                inserted_image_ids.append(str(image_id))
            self.session.bulk_insert_mappings(AigcKnowledgeDocumentImages, data_to_insert)
            self.session.commit()

    def add_knowledge_doc_chunk(self, doc_id, content, character_count, result):
        """
        添加知识文档分片
        :param doc_id:
        :param content:
        :param character_count:
        :param result:
        :return:
        """
        # chunk_id = get_snowflake_id()
        # chunk_id = chunk_id,
        obj = AigcKnowledgeDocumentChunk(doc_id=doc_id, content=content, character_count=character_count, result=result)
        self.session.add(obj)
        self.session.flush()
        self.session.commit()
        return str(obj.chunk_id)

    def update_knowledge_doc_chunk(self, chunk_id, args):
        """
        更新知识文档分片
        :param chunk_id:
        :param args:
        :return:
        """
        self.session.query(AigcKnowledgeDocumentChunk).filter(AigcKnowledgeDocumentChunk.chunk_id == chunk_id).update(args)
        self.session.commit()
        return str(chunk_id)

    def update_knowledge_doc_chunk_batch(self, chunk_ids, args):
        """
        批量更新知识文档分片
        :param chunk_ids: 知识文档分片 ID 列表
        :param args: 更新的参数
        :return: 批量更新的知识文档分片 ID 列表的字符串表示
        """
        update_mappings = [{'chunk_id': chunk_id, **args} for chunk_id in chunk_ids]
        self.session.bulk_update_mappings(AigcKnowledgeDocumentChunk, update_mappings)
        self.session.commit()
        return [str(chunk_id) for chunk_id in chunk_ids]

    def update_knowledge_doc_chunk_batch_by_tokenlist(self, chunk_ids, tokens, results):
        """
        批量更新知识文档分片
        :param chunk_ids: 知识文档分片 ID 列表
        :param tokens: 更新的参数
        :param results: 更新的参数
        :return: 批量更新的知识文档分片 ID 列表的字符串表示
        """
        # 检查三个列表长度是否一致
        if len(chunk_ids) != len(tokens) or len(chunk_ids) != len(results):
            raise ValueError("chunk_ids、tokens 和 results 的长度必须一致")
        update_mappings = [
            {
                'chunk_id': chunk_id,
                'tokens': token,
                'result': result
            }
            for chunk_id, token, result in zip(chunk_ids, tokens, results)
        ]
        self.session.bulk_update_mappings(AigcKnowledgeDocumentChunk, update_mappings)
        self.session.commit()
        return [str(chunk_id) for chunk_id in chunk_ids]

    def add_knowledge_doc(self, knowledge_id, doc_name, doc_type, doc_url, doc_size, status, current_node=None, state=None, content_id=None, file_time=None,tp_user_id=None,split_chunk_size=None,chunk_overlap=None,slice_model=None,pdf_model=None,chunk_clear_call_words=None,upload_type=0):
        """
        添加知识文档
        :param knowledge_id:
        :param doc_name:
        :param doc_type:
        :param doc_url:
        :param doc_size:
        :param status:
        :param content_id:
        :param upload_type:
        :return:
        """
        doc_id = get_snowflake_id()
        obj = AigcKnowledgeDocument(document_id=doc_id, knowledge_id=knowledge_id, doc_name=doc_name, doc_type=doc_type,
                                    doc_url=doc_url, doc_size=doc_size, status=status,current_node=current_node, state=state, content_id=content_id,tp_user_id=tp_user_id,split_chunk_size=split_chunk_size,chunk_overlap=chunk_overlap,slice_model=slice_model,pdf_model=pdf_model,chunk_clear_call_words= chunk_clear_call_words,upload_type=upload_type)
        if file_time:
            obj.content_time = file_time
        self.session.add(obj)
        self.session.commit()
        return str(doc_id)

    def video_upload(self, knowledge_id, doc_name, doc_type, doc_url, doc_size,tp_user_id, key_frame, video_duration, frame_rate,upload_type):
        doc_id = get_snowflake_id()
        obj = AigcKnowledgeDocument(document_id=doc_id, knowledge_id=knowledge_id, doc_name=doc_name, doc_type=doc_type,
                                    doc_url=doc_url, doc_size=doc_size, status='TODO',current_node='视频上传', state='已完成', tp_user_id=tp_user_id,upload_type=upload_type)
        video_obj = AigcKnowledgeVideo(doc_id=doc_id, key_frame=key_frame, video_duration=video_duration, frame_rate=frame_rate)
        self.session.add(obj)
        self.session.add(video_obj)
        self.session.commit()
        return str(doc_id)

    def check_knowledge_doc(self, knowledge_id, doc_name, **kwargs):
        with self.session_scope() as session:
            q = session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.knowledge_id == knowledge_id,
                                                            AigcKnowledgeDocument.doc_name == doc_name,
                                                            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE)
            if kwargs:
                q = q.filter_by(**kwargs)
            if q.count():
                return True
            else:
                return False

    def get_doc_clear_url(self, doc_id):
        """
        @summary: 获取文档清理url
        """
        obj = self.session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.document_id == doc_id,
                                                               AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE).first()
        return obj.doc_clear_url
    
    def get_doc_image_urls(self, doc_id):
        url_tuple = self.session.query(AigcKnowledgeDocumentImages.image_url).filter(AigcKnowledgeDocumentImages.doc_id == doc_id,AigcKnowledgeDocumentImages.delete_flag == settings.DELETE_FLAG_FALSE).all()
        url_list = [url[0] for url in url_tuple]
        return url_list
    
    def update_knowledge_doc(self, doc_id, args):
        """
        更新知识文档
        :param doc_id:
        :param args:
        :return:
        """
        self.session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.document_id == doc_id).update(args)
        self.session.commit()
        return str(doc_id)


    def get_knowledge_document_detail_by_doc_source_id(self, doc_source_id):
        doc = self.session.query(AigcKnowledgeDocument.document_id).filter(AigcKnowledgeDocument.doc_source_id == doc_source_id,
                                                               AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE).first()
        return doc.document_id if doc else None

    def get_document_chunk_id_by_chunk_source_id(self, source_chunk_id):
        doc = self.session.query(AigcKnowledgeDocumentChunk.chunk_id).filter(AigcKnowledgeDocumentChunk.source_chunk_id == source_chunk_id,
                                                               AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE).first()
        return doc.chunk_id if doc else None

    def get_document_chunk_id_by_chunk_source_ids(self, source_chunk_ids):
        docs = self.session.query(AigcKnowledgeDocumentChunk.chunk_id).filter(AigcKnowledgeDocumentChunk.source_chunk_id.in_(source_chunk_ids),
                                                               AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE).distinct().all()

        return [i[0] for i in docs]
    
    def get_knowledge_document_detail(self, doc_id, upload_type='0'):
        # subquery = self.session.query(AigcKnowledgeDocumentChunk.doc_id, AigcKnowledgeDocumentChunk.content,
        #                               func.sum(AigcKnowledgeDocumentChunk.tokens).label('tokens'),
        #                               func.sum(AigcKnowledgeDocumentChunk.hit_count).label('hit_count'),
        #                               func.sum(AigcKnowledgeDocumentChunk.character_count).label('character_count')). \
        #     filter(AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE). \
        #     group_by(AigcKnowledgeDocumentChunk.doc_id).subquery()
        obj = self.session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.document_id == doc_id,
                                                               AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE,
                                                               ).first()

        key_frame = ''
        video_duration = ''
        frame_rate = ''
        width = ''
        height = ''
        data = dict()
        if obj:
            if obj.upload_type == '1':
                video_doc = self.session.query(AigcKnowledgeVideo).filter(AigcKnowledgeVideo.doc_id == str(obj.AigcKnowledgeDocument.document_id)).first()
                key_frame = video_doc.key_frame if video_doc else ''
                video_duration = video_doc.video_duration if video_doc else ''
                frame_rate = video_doc.frame_rate if video_doc else ''
                width = video_doc.width if video_doc else ''
                height = video_doc.height if video_doc else ''
                video_duration = ms_to_hms(video_duration)
            data.update({
                'document_id': str(obj.document_id),
                'doc_name': obj.doc_name,
                'doc_type': obj.doc_type,
                'chunk_size': obj.chunk_size,
                'knowledge_id': obj.knowledge_id,
                'status': obj.status,
                'doc_url': obj.doc_url,
                'result': obj.result,
                'add_time': obj.add_time,
                'doc_size': obj.doc_size,
                'aigc_model_id': obj.knowledge.aigc_model_id,
                'model_path': obj.knowledge.aigc_model.model_path,
                'abstract': obj.abstract,
                'description': obj.description,
                'graph_content': obj.graph_content,
                'tokens': obj.tokens,
                'hit_count': obj.hit_count,
                'character_count': obj.character_count,
                'open_life': obj.open_life,
                'life_type': obj.life_type,
                'expire_date': obj.expire_date,
                'content_time': str(obj.content_time or ''),
                'content_id': str(obj.content_id) if obj.content_id else '',
                'key_frame': key_frame,
                'video_duration': video_duration,
                'frame_rate': frame_rate,
                'width': width,
                'height': height,
                'upload_type': obj.upload_type,
                'upload_type_content': 'int 0: 文档类型, 1: 视频类型',
                'have_table': obj.have_table,
                'source_type': obj.source_type,
                'doc_source_id': obj.doc_source_id or '',
                'is_sharing': obj.is_sharing,
                'main_points': obj.main_points,
                'key_words': obj.key_words,
                'mind_map': obj.mind_map,
                'knowledge_name': obj.knowledge.knowledge_name
            })
        return data

    def get_knowledge_doc_chunk_detail(self, chunk_id):
        obj = self.session.query(AigcKnowledgeDocumentChunk).filter(AigcKnowledgeDocumentChunk.chunk_id == chunk_id,
                                                                    AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE).first()
        data = dict()
        if obj:
            data.update({
                'chunk_id': str(obj.chunk_id),
                'model_path': obj.doc.knowledge.aigc_model.model_path,
                'content': obj.content,
                'character_count': obj.character_count,
                'result': obj.result,
                'add_time': obj.add_time,
                'doc_id': str(obj.doc_id)
            })
        return data

    def get_knowledge_doc_chunk_video_detail(self, chunk_id):
        obj = self.session.query(AigcKnowledgeDocumentChunk, AigcKnowledgeDocuChunkImage, AigcKnowledgeVideoChunk)\
            .join(AigcKnowledgeDocuChunkImage, AigcKnowledgeDocumentChunk.chunk_id == AigcKnowledgeDocuChunkImage.chunk_id)\
            .join(AigcKnowledgeVideoChunk, AigcKnowledgeDocumentChunk.chunk_id == AigcKnowledgeVideoChunk.chunk_id)\
            .filter(AigcKnowledgeDocumentChunk.chunk_id == chunk_id, AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE)\
            .first()

        data = dict()
        if obj:
            # 通过索引访问查询结果中的各个模型实例
            document_chunk, image_chunk, video_chunk = obj
            video_time = video_chunk.video_time
            frame = video_chunk.frame
            video_time_str = ms_to_hms(video_time)
            time_code = concat_video_time_strings(frame, video_time_str)
            data.update({
                'chunk_id': str(document_chunk.chunk_id),
                'model_path': document_chunk.doc.knowledge.aigc_model.model_path,
                'content': document_chunk.content,
                'character_count': document_chunk.character_count,
                'result': document_chunk.result,
                'add_time': document_chunk.add_time,
                'doc_id': str(document_chunk.doc_id),
                'image_url': image_chunk.image_url,
                'frame': video_chunk.frame,
                'video_time': video_chunk.video_time,
                'audio_start': video_chunk.audio_start,
                'audio_end': video_chunk.audio_end,
                'time_code': time_code,
            })
        return data

    def openapi_knowledge_doc_detail(self, doc_id):
        """
        openapi获取知识文档分片列表
        :param doc_id:
        :return:
        """
        obj = self.session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.document_id == doc_id,
                                                               AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE).first()
        chunk_obj = self.session.query(func.sum(AigcKnowledgeDocumentChunk.tokens).label('tokens'),
                                       func.avg(AigcKnowledgeDocumentChunk.character_count).label('avg_character_count')).filter(
            AigcKnowledgeDocumentChunk.doc_id == doc_id, AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE).first()
        if obj:
            data = {
                'id': str(obj.document_id),
                'data_source_type': 'upload_file',
                'data_source_info': {
                    'upload_file': {
                        'name': obj.doc_name,
                        'size': obj.doc_size,
                        'created_at': obj.add_time.timestamp(),
                    }
                },
                'dataset_process_rule': {
                    'mode': 'automatic',
                    'rules': {
                        'segmentation': {
                            'separator': '\n',
                            'max_tokens': 256,
                            'chunk_overlap': 20

                        }
                    }

                },
                'name': obj.doc_name,
                'tokens': chunk_obj.tokens,
                'segment_count': obj.chunk_size,
                'average_segment_length': int(chunk_obj.avg_character_count)

            }
            return data
        else:
            return {}

    def get_knowledge_doc_chunk_list(self, doc_id, args):
        """
        获取知识文档分片列表
        :param doc_id:
        :param args:
        :return:
        """
        q = self.session.query(AigcKnowledgeDocumentChunk).filter(
            AigcKnowledgeDocumentChunk.doc_id == doc_id,
            AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE
            )
        
        upload_type = self.session.query(AigcKnowledgeDocument.upload_type).filter(
            AigcKnowledgeDocument.document_id == doc_id,
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).first()

        chunk_map_light = args.get('chunk_map_light', {})

        if args.get('status'):
            q = q.filter(AigcKnowledgeDocumentChunk.status == args.get('status'))

        if args.get('results'):
            q = q.filter(AigcKnowledgeDocumentChunk.result.in_(args.get('results')))

        if chunk_map_light:
            q = q.filter(AigcKnowledgeDocumentChunk.chunk_id.in_(list(chunk_map_light.keys())))

        if args.get('chunk_ids'):
            q = q.filter(AigcKnowledgeDocumentChunk.chunk_id.in_(args.get('chunk_ids')))

        if args.get('is_all') == 'all':
            objs = q.all()
        else:
            count = q.count()
            objs = q.slice(
                (int(args['page_no']) - 1) * int(args['page_size']),
                int(args['page_size']) * int(args['page_no'])
            ).all()
        data_list = []
        doc_url = args.get('doc_url', '')
        for obj in objs:
            # 查询对应的图片URL
            image_urls = self.session.query(AigcKnowledgeDocuChunkImage.image_url).filter(
                AigcKnowledgeDocuChunkImage.chunk_id == obj.chunk_id
            ).all()

            # 将图片URL列表转换为简单的list
            image_list = [url[0] for url in image_urls]

            chunk_id = str(obj.chunk_id)
            content = obj.content

            # 高亮片段替换
            for light_str in chunk_map_light.get(chunk_id, []):
                replace_str = light_str.replace("<font color='red'>", '').replace("</font>", '')
                content = content.replace(replace_str, light_str)

            frame = ''
            video_time = ''
            audio_start = 0
            audio_end = 0
            time_code = ''

            if upload_type[0] == 1:
                video_chunk = self.session.query(AigcKnowledgeVideoChunk).filter(
                        AigcKnowledgeVideoChunk.chunk_id == chunk_id
                    ).first()
                frame = video_chunk.frame
                video_time = video_chunk.video_time
                audio_start = video_chunk.audio_start
                audio_end = video_chunk.audio_end
                video_time_str = ms_to_hms(video_time)
                time_code = concat_video_time_strings(frame, video_time_str)

            data_list.append({
                'chunk_id': chunk_id,
                'doc_id': str(obj.doc_id),
                'content': content,
                'character_count': obj.character_count,
                'hit_count': obj.hit_count,
                'tokens': obj.tokens,
                'status': obj.status,
                'result': obj.result,
                'image_list': image_list,  # 添加图片URL列表
                'frame': frame,
                'video_time': video_time,
                'audio_start': audio_start,
                'audio_end': audio_end,
                'time_code': time_code,
                'upload_type': upload_type[0],
                'upload_type_content': 'int 0: 文档类型, 1: 视频类型',
                'doc_url': doc_url
            })
        if upload_type[0] == 1:
            data_list = sorted(data_list, key=lambda x: int(x['frame']) if x['frame'] else 0)

        if args.get('is_all') == 'all':
            return {'data_list': data_list}
        else:
            return {'total': count, 'po': args['page_no'], 'data_list': data_list}



    def get_knowledge_doc_chunk_list_by_knowledge(self, knowledge_id, args):

        q = self.session.query(AigcKnowledgeDocumentChunk).join(AigcKnowledgeDocument,
                                                                AigcKnowledgeDocument.document_id == AigcKnowledgeDocumentChunk.doc_id).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE, AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE)
        if args.get('doc_ids'):
            q = q.filter(AigcKnowledgeDocumentChunk.doc_id.in_(args.get('doc_ids')))
        if args.get('results'):
            q = q.filter(AigcKnowledgeDocumentChunk.result.in_(args.get('results')))
        if args.get('status'):
            q = q.filter(AigcKnowledgeDocument.status == args.get('status'))
        if args.get('results') and args.get('is_all') != 'all':
            q = q.filter(AigcKnowledgeDocument.result.in_(args.get('results')))
        # logger.info(q.statement.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True}))
        if args.get('is_all') == 'all':
            objs = q.all()
            data = dict()
            for obj in objs:
                data.update({
                    str(obj.chunk_id): obj.result
                })
            return data

        count = q.count()

        return count

    def add_chunk_hit_count(self, chunk_id, hit_count=0):
        """
        更新知识文档分片命中次数
        :param chunk_id:
        :param hit_count:
        :return:
        """
        obj = self.session.query(AigcKnowledgeDocumentChunk).filter(AigcKnowledgeDocumentChunk.chunk_id == chunk_id).first()
        obj.hit_count = obj.hit_count + hit_count
        self.session.commit()
        return str(chunk_id)

    def get_knowledge_ids(self, args):
        q = self.session.query(AigcKnowledge.knowledge_id).filter(AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE)
        if args.get('aigc_model_id'):
            q = q.filter(AigcKnowledge.aigc_model_id == args.get('aigc_model_id'))
        objs = q.all()
        if objs:
            return [str(obj.knowledge_id) for obj in objs]
        else:
            return []

    def get_knowledge_ids_by_model(self, auth_scheme_ids, knowledge_ids=None, doc_ids=None):
        # 如果knowledge_ids不为空，则不需要查全部，智能报告的临时知识库delete_Flag=1，需要在这里查询到
        # 如果doc_ids不为空，则根据doc关联knowledge查询
        # 如果knowledge_ids和doc_ids都为空，则返回delete_flag为0的全部
        if knowledge_ids:
            q = self.session.query(func.group_concat(AigcKnowledge.knowledge_id).label('knowledge_ids')).filter(
                AigcKnowledge.knowledge_id.in_(knowledge_ids))
        elif doc_ids:
            q = self.session.query(
                func.group_concat(AigcKnowledge.knowledge_id).label('knowledge_ids')
            ).join(
                AigcKnowledgeDocument, AigcKnowledge.knowledge_id == AigcKnowledgeDocument.knowledge_id
            ).filter(
                AigcKnowledgeDocument.document_id.in_(doc_ids))
        else:
            q = self.session.query(func.group_concat(AigcKnowledge.knowledge_id).label('knowledge_ids')
                                   ).filter(
                AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE)
        # 权限控制
        user_id = request.user['tp_user_id']
        if auth_scheme_ids is not None: # 代表非超管
            if len(auth_scheme_ids) == 0:
                q = q.filter(AigcKnowledge.tp_user_id==user_id)
            else:
                q = q.filter(or_(AigcKnowledge.auth_scheme_id.in_(auth_scheme_ids),AigcKnowledge.tp_user_id==user_id))

        q = q.group_by(AigcKnowledge.aigc_model_id)
        objs = q.all()
        data_list = list()
        for obj in objs:
            if obj.knowledge_ids:
                data_list.append([str(knowledge_id) for knowledge_id in obj.knowledge_ids.split(',')])
        return data_list

    def get_ai_model_by_path(self, model_path):
        obj = self.session.query(AigcModel).filter(AigcModel.model_path == model_path, AigcModel.delete_flag == settings.DELETE_FLAG_FALSE).first()

        data = dict()
        if obj:
            data.update({
                'model_id': str(obj.aigc_model_id),
                'model_path': obj.model_path,
                'model_name': obj.model_name,
                'dims': obj.dims
            })
        return data

    def get_doc_list_by_doc_ids(self, doc_ids):
        objs = self.session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.document_id.in_(doc_ids),
                                                                AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE).all()

        data_list = []
        for obj in objs:
            data_list.append({
                'document_id': str(obj.document_id),
                'knowledge_id': str(obj.knowledge_id),
                'doc_name': obj.doc_name

            })
        return data_list

    def get_doc_map_infos_by_ids(self, doc_ids):
        objs = self.session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.document_id.in_(doc_ids)).all()
        data_list = []
        for obj in objs:
            data_list.append({
                'document_id': str(obj.document_id),
                'knowledge_id': str(obj.knowledge_id),
                'doc_name': obj.doc_name,
                'knowledge_name': obj.knowledge.knowledge_name if obj.knowledge_id else ''
            })
        return data_list

    def get_chunk_map_info_by_ids(self, chunk_ids):
        objs = self.session.query(AigcKnowledgeDocumentChunk).filter(AigcKnowledgeDocumentChunk.chunk_id.in_(chunk_ids)).all()
        data_list = []
        for obj in objs:
            data_list.append({
                'chunk_id': str(obj.chunk_id),
                'document_id': str(obj.doc_id),
                'doc_name': obj.doc.doc_name,
                'knowledge_id': str(obj.doc.knowledge_id),
                'knowledge_name': obj.doc.knowledge.knowledge_name
            })
        return data_list

    def get_doc_chunk_list_by_chunk_ids(self, doc_id, chunk_ids):
        q = self.session.query(AigcKnowledgeDocumentChunk).filter(AigcKnowledgeDocumentChunk.doc_id == doc_id,
                                                                  AigcKnowledgeDocumentChunk.chunk_id.in_(chunk_ids),
                                                                  AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE)
        # logger.info(q.statement.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True}))
        objs = q.all()

        data_list = []
        for obj in objs:
            data_list.append({
                'chunk_id': str(obj.chunk_id),
                'content': obj.content,
                'character_count': obj.character_count,
                'hit_count': obj.hit_count,
                'tokens': obj.tokens,
                'index': self.get_doc_chunk_index(doc_id, obj.chunk_id)
            })
        return data_list

    def get_doc_chunk_index(self, doc_id, chunk_id):
        count = self.session.query(func.sum(AigcKnowledgeDocumentChunk.character_count)).filter(
            AigcKnowledgeDocumentChunk.chunk_id < chunk_id,
            AigcKnowledgeDocumentChunk.doc_id == doc_id).scalar()
        return count or 0

    def add_chunk_images(self, chunk_id, image_urls):
        data_to_insert = [{'chunk_image_id': int(get_snowflake_id()), 'chunk_id': int(chunk_id), 'image_url': url} for url in image_urls]
        self.session.bulk_insert_mappings(AigcKnowledgeDocuChunkImage, data_to_insert)
        self.session.commit()
        return str(chunk_id)

    def add_chunk_video(self, chunk_id: str, frame: str, video_time: str, audio_start: int, audio_end: int) -> str:
        with self.session_scope() as session:
            chunk_video_id = int(get_snowflake_id())
            obj = AigcKnowledgeVideoChunk(id=chunk_video_id, chunk_id=chunk_id, frame=frame, video_time=video_time, audio_start=audio_start, audio_end=audio_end)
            session.add(obj)
            return str(chunk_video_id)

    def add_chunk_image(self, chunk_id, image_url):

        with self.session_scope() as session:
            chunk_image_id = int(get_snowflake_id())
            obj = AigcKnowledgeDocuChunkImage(chunk_image_id=chunk_image_id, chunk_id=chunk_id, image_url=image_url)
            session.add(obj)
            return chunk_image_id

    def del_chunk_image_by_chunk_id(self, chunk_id):
        with self.session_scope() as session:
            session.query(AigcKnowledgeDocuChunkImage).filter(AigcKnowledgeDocuChunkImage.chunk_id == chunk_id).delete()
            return chunk_id

    def get_chunk_image_list(self, chunk_id):
        with self.session_scope() as session:
            objs = session.query(AigcKnowledgeDocuChunkImage).filter(AigcKnowledgeDocuChunkImage.chunk_id == chunk_id).all()
            return [obj.image_url for obj in objs]
    
    def get_chunk_image_by_id(self, chunk_id):
        obj = self.session.query(
            AigcKnowledgeDocuChunkImage,
            AigcKnowledgeDocument.doc_url,
            AigcKnowledgeDocument.doc_name,
            AigcKnowledgeDocument.knowledge_id,
            AigcKnowledgeDocument.document_id,
        ).join(
            AigcKnowledgeDocumentChunk, AigcKnowledgeDocuChunkImage.chunk_id == AigcKnowledgeDocumentChunk.chunk_id
        ).join(
            AigcKnowledgeDocument, AigcKnowledgeDocumentChunk.doc_id == AigcKnowledgeDocument.document_id
        ).filter(
            AigcKnowledgeDocuChunkImage.chunk_id == chunk_id
        ).first()
        res = {}
        if obj:
            chunk_img = obj[0]
            knowledge = self.session.query(AigcKnowledge).filter_by(knowledge_id=obj[3]).first()
            knowledge_name = knowledge.knowledge_name if knowledge else ''
            res = {'doc_url': obj[1], 'doc_name': obj[2], 'knowledge_name': knowledge_name,
                   'doc_id': obj[4], 'chunk_id': chunk_img.chunk_id, 'add_time': str(chunk_img.add_time),
                   'image_url': chunk_img.image_url
                   }
            
        return res

    def get_chunk_image_by_add_time(self, next_time):
        # 由于图片量过大，只处理4月20号的内容
        task_logger.info(f'get_chunk_image_by_add_time : {next_time}')
        if next_time == 'all':
            next_time = datetime(2025, 4, 20)
        obj_list = self.session.query(AigcKnowledgeDocuChunkImage).filter(AigcKnowledgeDocuChunkImage.add_time>next_time).all()

        return [{'id': objs.chunk_image_id, 'chunk_id':objs.chunk_id, 'image_url': objs.image_url, 'add_time': objs.add_time, 'update_time': objs.update_time} for objs in obj_list]

    def get_chunk_hits_images(self, chunk_ids):

        chunk_ids = [int(chunk_id) for chunk_id in chunk_ids]

        chunk_image_map = {chunk_id: [] for chunk_id in chunk_ids}

        # 查询所有符合条件的图片记录
        objs = self.session.query(AigcKnowledgeDocuChunkImage).filter(
            AigcKnowledgeDocuChunkImage.chunk_id.in_(chunk_ids)
        ).all()

        for obj in objs:
            # logger.info(f"Processing chunk_id: {obj.chunk_id}, image_url: {obj.image_url}")
            if obj.chunk_id in chunk_image_map:
                chunk_image_map[obj.chunk_id].append(obj.image_url)

        # 构建返回的列表
        ret_lst = []
        for chunk_id in chunk_ids:
            doc_info = self.get_doc_info_by_chunk_id(chunk_id)
            ret = {
                'chunk_id': str(chunk_id),
                'doc_name': doc_info.get('doc_name', ''),
                'doc_id': doc_info.get('doc_id', ''),
                'image_list': chunk_image_map.get(chunk_id, [])
            }
            ret_lst.append(ret)

        return ret_lst

    def get_doc_info_by_chunk_id(self, chunk_id):
        obj = self.session.query(AigcKnowledgeDocumentChunk, AigcKnowledgeDocument).join(AigcKnowledgeDocument, AigcKnowledgeDocument.document_id == AigcKnowledgeDocumentChunk.doc_id).filter(AigcKnowledgeDocumentChunk.chunk_id==chunk_id).first()
        if obj:
            return {
                'doc_id': str(obj.AigcKnowledgeDocument.document_id),
                'doc_name': obj.AigcKnowledgeDocument.doc_name,
                'doc_type': obj.AigcKnowledgeDocument.doc_type,
                'doc_size': obj.AigcKnowledgeDocument.doc_size,
                'chunk_size': obj.AigcKnowledgeDocument.chunk_size,
                'add_time': obj.AigcKnowledgeDocument.add_time.strftime('%Y-%m-%d %H:%M:%S'),
            }
        else:
            return {}

    def get_doc_info(self, doc_id):
        """
        获取指定 doc_id 的文档信息
        :param doc_id: 文档ID
        :return: 文档详细信息字典
        """
        doc_query = self.session.query(
            AigcKnowledgeDocument.document_id,
            AigcKnowledgeDocument.doc_name,
            AigcKnowledgeDocument.chunk_size,
            AigcKnowledgeDocument.doc_size,
            AigcKnowledgeDocument.doc_type,
            AigcKnowledgeDocument.status,
            AigcKnowledgeDocument.add_time,
            AigcKnowledgeDocument.doc_url,
            AigcKnowledgeDocument.tokens,
            AigcKnowledgeDocument.content_id,
            AigcKnowledge.knowledge_name,
            func.sum(AigcKnowledgeDocumentChunk.hit_count).label('hit_count'),
            func.sum(AigcKnowledgeDocumentChunk.tokens).label('chunk_tokens'),
            func.sum(AigcKnowledgeDocumentChunk.character_count).label('character_count'),
            AigcKnowledgeDocumentChunk.content.label('description')
        ).join(
            AigcKnowledge, AigcKnowledge.knowledge_id == AigcKnowledgeDocument.knowledge_id
        ).outerjoin(
            AigcKnowledgeDocumentChunk, AigcKnowledgeDocument.document_id == AigcKnowledgeDocumentChunk.doc_id
        ).filter(
            AigcKnowledgeDocument.document_id == doc_id,
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).group_by(
            AigcKnowledgeDocument.document_id
        ).first()

        if doc_query:
            return {
                'document_id': str(doc_query.document_id),
                'doc_name': doc_query.doc_name,
                'chunk_size': doc_query.chunk_size,
                'doc_size': doc_query.doc_size,
                'doc_type': doc_query.doc_type,
                'status': doc_query.status,
                'add_time': doc_query.add_time,
                'doc_url': doc_query.doc_url,
                'tokens': doc_query.tokens or doc_query.chunk_tokens,  # 使用文档本身的 tokens 或 chunks 的 tokens 总和
                'hit_count': doc_query.hit_count,
                'character_count': doc_query.character_count,
                'content_id': doc_query.content_id or '',
                'description': doc_query.description or '',  # 使用 chunk 中的内容作为 description
                'knowledge_name': doc_query.knowledge_name,
                'doc_id': str(doc_query.document_id),
                'chunk_list': []
            }
        return {}

    def get_doc_list_by_knowledge_ids(self, knowledge_ids):
        with self.session_scope() as session:
            objs = session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.knowledge_id.in_(knowledge_ids),
                                                               AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE).all()
            objs = [{"doc_id": str(obj.document_id), "description": obj.description} for obj in objs]
            return objs

    def get_doc_list_by_description(self, knowledge_ids, descriptions):
        with self.session_scope() as session:
            or_conditions = []
            for description in descriptions:
                or_conditions.append(func.find_in_set(description, AigcKnowledgeDocument.description))

            q = session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.knowledge_id.in_(knowledge_ids),
                                                            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE, or_(*or_conditions))
            # logger.info(q.statement.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True}))
            objs = q.all()
            distinct_objs = [{'doc_id': str(obj.document_id), 'description': obj.description, 'graph_content': obj.graph_content or ''} for
                             obj in objs]

            # 使用字典的键来去重
            unique_dict = {}
            for item in distinct_objs:
                unique_dict[item['description']] = item  # 如果description相同，后面的会覆盖前面的

            # 获取去重后的列表
            unique_list = list(unique_dict.values())
            return {'doc_ids': [str(obj.document_id) for obj in objs], 'graph_content_list': unique_list}

    def get_graph_list_by_doc_ids(self, doc_ids):
        with self.session_scope() as session:
            objs = session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.document_id.in_(doc_ids),
                                                               AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE).all()
            objs = [{"doc_id": str(obj.document_id), "graph_content": obj.graph_content or ''} for obj in objs]
            return objs

    def stat_chunk_data(self, is_today=False):

        q = self.session.query(AigcKnowledgeDocumentChunk.doc_id, AigcKnowledgeDocumentChunk.content,
                               func.sum(AigcKnowledgeDocumentChunk.tokens).label('tokens'),
                               func.sum(AigcKnowledgeDocumentChunk.hit_count).label('hit_count'),
                               func.sum(AigcKnowledgeDocumentChunk.character_count).label('character_count')). \
            filter(AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE). \
            group_by(AigcKnowledgeDocumentChunk.doc_id)
        # # 根据add_time 查询 今日
        if is_today:
            # logger.info((datetime.now() - timedelta(days=1)))
            add_time_filter = AigcKnowledgeDocumentChunk.update_time >= (datetime.now() - timedelta(days=1)).strftime(
                '%Y-%m-%d')
            q = q.filter(add_time_filter)
        # logger.info(q.statement.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True}))
        objs = q.all()
        data_list = [{'doc_id': str(obj.doc_id), 'tokens': obj.tokens, 'hit_count': obj.hit_count,
                      'character_count': obj.character_count} for obj in objs]
        return data_list

    def get_life_status_document(self, open_life=None, expire_date=None, kid=None):
        q = self.session.query(AigcKnowledgeDocument).filter_by(delete_flag=0)
        if open_life is not None:
            q = q.filter_by(open_life=open_life)
        if expire_date:
            q = q.filter(or_(and_(AigcKnowledgeDocument.expire_date <= expire_date, AigcKnowledgeDocument.open_life == 1),
                             and_(AigcKnowledgeDocument.add_time <= expire_date, AigcKnowledgeDocument.open_life == 0)))
        if kid:
            q = q.filter_by(knowledge_id=kid)
        return q.all()

    def bulk_del_open_life_statue_document(self, params=None, ids=None):
        with self.session_scope() as session:
            if not (params or ids):
                return
            q = session.query(AigcKnowledgeDocument).filter_by(delete_flag=0)
            if params:
                q = q.filter_by(open_life=params['open_life']).filter(AigcKnowledgeDocument.expire_date <= params['life_date'])
            if ids:
                q = q.filter(AigcKnowledgeDocument.document_id.in_(ids))
            q.update({'delete_flag': settings.DELETE_FLAG_TRUE})
        return True

    def get_knowledge_graph_detail(self, knowledge_id):
        records = (self.session
                   .query(AigcKnowledgeGraph)
                   .filter(AigcKnowledgeGraph.knowledge_id == knowledge_id)
                   .order_by(AigcKnowledgeGraph.add_time.desc())
                   .all())
        def _to_dict(record):
            record_dict = {column.name: getattr(record, column.name) for column in record.__table__.columns}
            if 'id' in record_dict and isinstance(record_dict['id'], int):
                record_dict['id'] = str(record_dict['id'])
            if 'knowledge_id' in record_dict and isinstance(record_dict['knowledge_id'], int):
                record_dict['knowledge_id'] = str(record_dict['knowledge_id'])
            return record_dict
        result = [_to_dict(record) for record in records]
        return result

    def add_knowledge_graph(self, knowledge_graph_data):
        new_record = AigcKnowledgeGraph(**knowledge_graph_data)

        self.session.add(new_record)
        self.session.commit()
        return new_record.id

    def delete_knowledge_graph(self, knowledge_id):
        record = self.session.query(AigcKnowledgeGraph).filter(AigcKnowledgeGraph.knowledge_id == knowledge_id).first()

        if record:
            self.session.delete(record)
            self.session.commit()
            return True
        return False

    def update_knowledge_graph(self, graph_id, args):
        self.session.query(AigcKnowledgeGraph).filter(AigcKnowledgeGraph.id == graph_id).update(args)
        self.session.commit()
        return str(graph_id)

    def get_knowledge_galaxy_detail(self, galaxy_id):
        records = (self.session
                   .query(AigcKnowledgeGalaxy)
                   .filter(AigcKnowledgeGalaxy.id == galaxy_id)
                   .order_by(AigcKnowledgeGalaxy.add_time.desc())
                   .all())

        def _to_dict(record):
            record_dict = {column.name: getattr(record, column.name) for column in record.__table__.columns}
            if 'id' in record_dict and isinstance(record_dict['id'], int):
                record_dict['id'] = str(record_dict['id'])
            return record_dict

        result = [_to_dict(record) for record in records]
        return result

    def get_knowledge_galaxy_history(self):
        records = (self.session
                   .query(AigcKnowledgeGalaxy)
                   .order_by(AigcKnowledgeGalaxy.add_time.desc())
                   .all())

        def _to_dict(record):
            record_dict = {column.name: getattr(record, column.name) for column in record.__table__.columns}
            if 'id' in record_dict and isinstance(record_dict['id'], int):
                record_dict['id'] = str(record_dict['id'])
            return record_dict

        result = [_to_dict(record) for record in records]
        return result

    def add_knowledge_galaxy(self, knowledge_galaxy_data):
        new_record = AigcKnowledgeGalaxy(**knowledge_galaxy_data)

        self.session.add(new_record)
        self.session.commit()
        return new_record.id

    def delete_knowledge_galaxy(self, galaxy_id):
        record = self.session.query(AigcKnowledgeGalaxy).filter(AigcKnowledgeGalaxy.id == galaxy_id).first()

        if record:
            self.session.delete(record)
            self.session.commit()
            return True
        return False

    def update_knowledge_galaxy(self, galaxy_id, args):
        self.session.query(AigcKnowledgeGalaxy).filter(AigcKnowledgeGalaxy.id == galaxy_id).update(args)
        self.session.commit()
        return str(galaxy_id)

    def get_knowledge_galaxy_record_detail(self, record_id):
        records = (self.session
                   .query(AigcKnowledgeGalaxyRecord)
                   .filter(AigcKnowledgeGalaxyRecord.record_id == record_id)
                   .order_by(AigcKnowledgeGalaxyRecord.add_time.desc())
                   .all())

        def _to_dict(record):
            record_dict = {column.name: getattr(record, column.name) for column in record.__table__.columns}
            if 'record_id' in record_dict and isinstance(record_dict['record_id'], int):
                record_dict['record_id'] = str(record_dict['record_id'])
            return record_dict

        result = [_to_dict(record) for record in records]
        return result

    def get_knowledge_galaxy_record_history_filter(self, tp_user_id, size):
        records = (self.session
                   .query(AigcKnowledgeGalaxyRecord)
                   .filter(AigcKnowledgeGalaxyRecord.tp_user_id == tp_user_id)
                   .order_by(AigcKnowledgeGalaxyRecord.add_time.desc())
                   .limit(size)
                   .all())

        def _to_dict(record):
            return {'state': getattr(record, 'state')}

        result = [_to_dict(record) for record in records]
        return result


    def get_knowledge_galaxy_record_history(self):
        records = (self.session
                   .query(AigcKnowledgeGalaxyRecord)
                   .order_by(AigcKnowledgeGalaxyRecord.add_time.desc())
                   .all())

        def _to_dict(record):
            record_dict = {column.name: getattr(record, column.name) for column in record.__table__.columns}
            if 'record_id' in record_dict and isinstance(record_dict['record_id'], int):
                record_dict['record_id'] = str(record_dict['record_id'])
            return record_dict

        result = [_to_dict(record) for record in records]
        return result

    def add_knowledge_galaxy_record(self, knowledge_galaxy_record_data):
        new_record = AigcKnowledgeGalaxyRecord(**knowledge_galaxy_record_data)

        self.session.add(new_record)
        self.session.commit()
        return new_record.record_id

    def delete_knowledge_galaxy_record(self, record_id):
        record = self.session.query(AigcKnowledgeGalaxyRecord).filter(
            AigcKnowledgeGalaxyRecord.record_id == record_id).first()

        if record:
            self.session.delete(record)
            self.session.commit()
            return True
        return False

    def update_knowledge_galaxy_record(self, record_id, args):
        self.session.query(AigcKnowledgeGalaxyRecord).filter(AigcKnowledgeGalaxyRecord.record_id == record_id).update(
            args)
        self.session.commit()
        return str(record_id)


    def modify_md(self, object_name, md_content, doc_id):
        bucket_name = settings.KNOWLEDGE_OSS_BUCKET_NAME

        with tempfile.NamedTemporaryFile(delete=False, suffix='.md') as temp_file:
            temp_file.write(md_content.encode('utf-8'))  # 将Markdown字符串写入文件
            temp_file_path = temp_file.name  # 获取临时文件路径
        try:
            minio_url = minio_util.upload_file_v2(bucket_name, object_name, temp_file_path)
            self.session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.document_id == doc_id).update({'doc_clear_url': minio_url})
            self.session.commit()
        except Exception as e:
            logger.error(f"Error {e}")
            return None

        os.remove(temp_file_path)

        return minio_url

    def get_character_count_subquery(self, knowledge_id):
        """ 获取文档字符总数的子查询 """
        return self.session.query(
            AigcKnowledgeDocument.knowledge_id,
            func.sum(AigcKnowledgeDocumentChunk.character_count).label('character_count')
        ).outerjoin(
            AigcKnowledgeDocument, AigcKnowledgeDocument.document_id == AigcKnowledgeDocumentChunk.doc_id
        ).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocumentChunk.delete_flag == settings.DELETE_FLAG_FALSE
        ).group_by(AigcKnowledgeDocument.knowledge_id).subquery()

    def get_knowledge_statistic(self, args):
        """
        获取知识库状态信息，包括文档数量、总字符数、应用数量、不同状态的文档数量等。
        """
        knowledge_id = args.get('knowledge_id')
        app_subquery = self.get_app_count_subquery(knowledge_id)
        start_time = args.get('start_time')
        end_time = args.get('end_time')
        # 获取不同状态下的文档数量子查询
        if start_time and end_time:
            doc_subquery = self.get_document_count_subquery(knowledge_id, start_time, end_time)
            # 获取已删除文档数量的子查询
            deleted_doc_count = self.get_deleted_document_count(knowledge_id, start_time, end_time)
            #成功状态子查询
            embedding_success_subquery = self.get_embedding_success_count_subquery(knowledge_id, start_time, end_time)
            # 获取知识库所有文档的命中次数总和
            total_hit_count = self.get_total_hit_count(knowledge_id,start_time, end_time)
            # 获取每天向量化成功的文档数量
            embedding_success_count_per_day = self.get_embedding_success_count_per_day(knowledge_id, start_time, end_time)
            upload_success_count_per_day = self.get_upload_success_count_per_day(knowledge_id, start_time, end_time)
            total_search_count =self.get_total_search_count(knowledge_id, start_time, end_time)


        else:
            doc_subquery = self.get_document_count_subquery(knowledge_id)
            deleted_doc_count = self.get_deleted_document_count(knowledge_id)
            embedding_success_subquery = self.get_embedding_success_count_subquery(knowledge_id)
            # 获取知识库所有文档的命中次数总和
            total_hit_count = self.get_total_hit_count(knowledge_id)
            total_search_count =self.get_total_search_count(knowledge_id)

            # 获取每天向量化成功的文档数量
            embedding_success_count_per_day = []
            upload_success_count_per_day =[]


        doc_clear_success_subquery = self.get_doc_clear_success_count_subquery(knowledge_id)
        doc_split_success_subquery = self.get_doc_split_success_count_subquery(knowledge_id)
        doc_split_clear_success_subquery = self.get_doc_split_clear_success_count_subquery(knowledge_id)

        # 失败状态子查询
        embedding_failure_subquery = self.get_embedding_failure_count_subquery(knowledge_id)
        doc_clear_failure_subquery = self.get_doc_clear_failure_count_subquery(knowledge_id)
        doc_split_failure_subquery = self.get_doc_split_failure_count_subquery(knowledge_id)
        doc_split_clear_failure_subquery = self.get_doc_split_clear_failure_count_subquery(knowledge_id)

        # 进行中状态子查询
        embedding_in_progress_subquery = self.get_embedding_in_progress_count_subquery(knowledge_id)
        doc_clear_in_progress_subquery = self.get_doc_clear_in_progress_count_subquery(knowledge_id)
        doc_split_in_progress_subquery = self.get_doc_split_in_progress_count_subquery(knowledge_id)
        doc_split_clear_in_progress_subquery = self.get_doc_split_clear_in_progress_count_subquery(knowledge_id)


        doc_cleaning_rule_count = self.get_document_cleaning_rule_count(knowledge_id)
        chunk_cleaning_rule_count = self.get_chunk_cleaning_rule_count(knowledge_id)

        get_file_failed_count_subquery = self.get_file_failed_count_subquery(knowledge_id)

        # 获取向量化成功的文档类型统计
        embedding_success_by_type = self.get_embedding_success_by_doc_type(knowledge_id)


        # 获取知识库详细信息
        q = self.session.query(
            AigcKnowledge.knowledge_id, AigcKnowledge.knowledge_name, AigcKnowledge.knowledge_desc,
            AigcKnowledge.status, AigcKnowledge.add_time, AigcKnowledge.icon_url,
            doc_subquery.c.doc_count,  app_subquery.c.app_count,
            embedding_success_subquery.c.embedding_success_count, embedding_failure_subquery.c.embedding_failure_count,
            embedding_in_progress_subquery.c.embedding_in_progress_count, doc_clear_success_subquery.c.doc_clear_success_count,
            doc_clear_failure_subquery.c.doc_clear_failure_count, doc_clear_in_progress_subquery.c.doc_clear_in_progress_count,
            doc_split_success_subquery.c.doc_split_success_count, doc_split_failure_subquery.c.doc_split_failure_count,
            doc_split_in_progress_subquery.c.doc_split_in_progress_count, doc_split_clear_success_subquery.c.doc_split_clear_success_count,
            doc_split_clear_failure_subquery.c.doc_split_clear_failure_count, doc_split_clear_in_progress_subquery.c.doc_split_clear_in_progress_count,
            get_file_failed_count_subquery.c.file_failed_count,
            AigcModel.model_name
        ).outerjoin(doc_subquery, AigcKnowledge.knowledge_id == doc_subquery.c.knowledge_id) \
        .outerjoin(app_subquery, AigcKnowledge.knowledge_id == app_subquery.c.knowledge_id) \
        .outerjoin(embedding_success_subquery, AigcKnowledge.knowledge_id == embedding_success_subquery.c.knowledge_id) \
        .outerjoin(embedding_failure_subquery, AigcKnowledge.knowledge_id == embedding_failure_subquery.c.knowledge_id) \
        .outerjoin(embedding_in_progress_subquery, AigcKnowledge.knowledge_id == embedding_in_progress_subquery.c.knowledge_id) \
        .outerjoin(doc_clear_success_subquery, AigcKnowledge.knowledge_id == doc_clear_success_subquery.c.knowledge_id) \
        .outerjoin(doc_clear_failure_subquery, AigcKnowledge.knowledge_id == doc_clear_failure_subquery.c.knowledge_id) \
        .outerjoin(doc_clear_in_progress_subquery, AigcKnowledge.knowledge_id == doc_clear_in_progress_subquery.c.knowledge_id) \
        .outerjoin(doc_split_success_subquery, AigcKnowledge.knowledge_id == doc_split_success_subquery.c.knowledge_id) \
        .outerjoin(doc_split_failure_subquery, AigcKnowledge.knowledge_id == doc_split_failure_subquery.c.knowledge_id) \
        .outerjoin(doc_split_in_progress_subquery, AigcKnowledge.knowledge_id == doc_split_in_progress_subquery.c.knowledge_id) \
        .outerjoin(doc_split_clear_success_subquery, AigcKnowledge.knowledge_id == doc_split_clear_success_subquery.c.knowledge_id) \
        .outerjoin(doc_split_clear_failure_subquery, AigcKnowledge.knowledge_id == doc_split_clear_failure_subquery.c.knowledge_id) \
        .outerjoin(doc_split_clear_in_progress_subquery, AigcKnowledge.knowledge_id == doc_split_clear_in_progress_subquery.c.knowledge_id) \
        .outerjoin(AigcModel, AigcKnowledge.aigc_model_id == AigcModel.aigc_model_id) \
        .outerjoin(get_file_failed_count_subquery, AigcKnowledge.knowledge_id == get_file_failed_count_subquery.c.knowledge_id) \
        .filter(AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE)

        # 获取知识库详细信息
        q = q.filter(AigcKnowledge.knowledge_id == knowledge_id)

        # 执行查询并处理结果
        objs = q.all()
        res = []
        for obj in objs:
            # 添加到结果列表
            # knowledge_id:知识库id
            # knowledge_name:知识库名称
            # knowledge_desc:知识库描述
            # add_time:知识库创建时间
            # doc_count:文档数量
            # model_name:模型名称
            # app_count:关联应用数量
            # embedding_success_count:向量化成功数量
            # embedding_failure_count:向量化失败数量
            # embedding_in_progress_count:向量化进行中数量
            # doc_clear_success_count:文档清洗成功数量
            # doc_clear_failure_count:文档清洗失败数量
            # doc_clear_in_progress_count:文档清洗进行中数量
            # doc_split_success_count:切片成功数量
            # doc_split_failure_count:切片失败数量
            # doc_split_in_progress_count:切片进行中数量
            # doc_split_clear_success_count:切片清洗成功数量
            # doc_split_clear_failure_count:切片清洗失败数量
            # doc_split_clear_in_progress_count:切片清洗进行中数量
            # doc_cleaning_rule_count:文档清洗规则数量
            # chunk_cleaning_rule_count:切片清洗规则数量
            # deleted_doc_count:已删除文档数量
            # embedding_success_by_type:向量化成功文档类型统计
            # total_hit_count:知识库所有文档的命中次数总和
            # embedding_success_count_per_day:每天向量化成功的文档数量
            res.append({
                'knowledge_id': str(obj.knowledge_id),
                'knowledge_name': obj.knowledge_name,
                'knowledge_desc': obj.knowledge_desc,
                'add_time': obj.add_time,
                'doc_count': obj.doc_count or 0,
                'model_name': obj.model_name,
                'app_count': obj.app_count or 0,
                'embedding_success_count': obj.embedding_success_count or 0,
                'embedding_failure_count': obj.embedding_failure_count or 0,
                'embedding_in_progress_count': obj.embedding_in_progress_count or 0,
                'doc_clear_success_count': obj.doc_clear_success_count or 0,
                'doc_clear_failure_count': obj.doc_clear_failure_count or 0,
                'doc_clear_in_progress_count': obj.doc_clear_in_progress_count or 0,
                'doc_split_success_count': obj.doc_split_success_count or 0,
                'doc_split_failure_count': obj.doc_split_failure_count or 0,
                'doc_split_in_progress_count': obj.doc_split_in_progress_count or 0,
                'doc_split_clear_success_count': obj.doc_split_clear_success_count or 0,
                'doc_split_clear_failure_count': obj.doc_split_clear_failure_count or 0,
                'doc_split_clear_in_progress_count': obj.doc_split_clear_in_progress_count or 0,
                'doc_cleaning_rule_count': doc_cleaning_rule_count or 0,
                'chunk_cleaning_rule_count': chunk_cleaning_rule_count or 0,
                'deleted_doc_count': deleted_doc_count,
                'embedding_success_by_type': embedding_success_by_type,
                'total_hit_count': total_hit_count,
                'embedding_success_count_per_day': embedding_success_count_per_day,
                'file_failed_count': obj.file_failed_count or 0,
                'upload_success_count_per_day': upload_success_count_per_day,
                "total_search_count" :total_search_count
            })

        # 返回结果
        return {'knowledge_status_list': res}



    def get_total_hit_count(self, knowledge_id, start_time=None, end_time=None):
        """ 获取知识库所有文档的命中次数总和 """
        # 基本查询
        query = self.session.query(
            func.sum(AigcKnowledgeDocument.hit_count).label('total_hit_count'),
            func.date(AigcKnowledgeDocument.add_time).label('date')  # 给日期列起个明确的名字
        ).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE  # 排除已删除文档
        )

        # 如果提供了时间范围，添加时间过滤
        if start_time:
            if isinstance(start_time, str):
                start_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')  # 转换为 datetime 类型
            query = query.filter(AigcKnowledgeDocument.add_time >= start_time)

        if end_time:
            if isinstance(end_time, str):
                end_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')  # 转换为 datetime 类型
            query = query.filter(AigcKnowledgeDocument.add_time <= end_time)

        # 按日期分组
        if start_time and end_time:
            query = query.group_by(func.date(AigcKnowledgeDocument.add_time))  # 按日期分组
            # 执行查询并获取结果
            result = query.all()

            # 生成时间范围内的所有日期
            delta = end_time - start_time
            all_dates = [start_time + timedelta(days=i) for i in range(delta.days + 1)]

            # 格式化结果
            result_dates = {str(row.date): row.total_hit_count or 0 for row in result}
            missing_dates = [date for date in all_dates if date.strftime('%Y-%m-%d') not in result_dates]

            # 返回每日的命中次数，缺失日期默认命中次数为 0
            data = [{'date': str(row.date), 'total_hit_count': row.total_hit_count or 0} for row in result]

            # 补充缺失的日期
            for missing_date in missing_dates:
                data.append({'date': missing_date.strftime('%Y-%m-%d'), 'total_hit_count': 0})

            # 按日期排序并返回
            return sorted(data, key=lambda x: x['date'])

        else:
            # 返回总命中次数
            total_hit_count = query.with_entities(func.sum(AigcKnowledgeDocument.hit_count)).scalar()
            return total_hit_count or 0  # 总命中次数，日期为空字符串

    def get_total_search_count(self, knowledge_id, start_time=None, end_time=None):
        """ 获取知识库检索命中次数 """
        # 基本查询
        query = self.session.query(
            func.sum(AigcKnowledge.search_hit_count).label('total_search_hit_count'),
            func.date(AigcKnowledge.update_time).label('date')  # 给日期列起个明确的名字
        ).filter(
            AigcKnowledge.knowledge_id == knowledge_id,
            AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE  # 排除已删除文档
        )

        if start_time and end_time:
            # 转换时间为 datetime 类型
            if isinstance(start_time, str):
                start_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
            if isinstance(end_time, str):
                end_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')

            # 过滤时间范围
            query = query.filter(AigcKnowledge.update_time >= start_time)
            query = query.filter(AigcKnowledge.update_time <= end_time)

            # 生成时间范围内的所有日期
            delta = end_time - start_time
            all_dates = [start_time + timedelta(days=i) for i in range(delta.days + 1)]

            # 按日期分组并排序
            query = query.group_by(func.date(AigcKnowledge.update_time)).order_by(func.date(AigcKnowledge.update_time))

            # 执行查询并获取结果
            result = query.all()

            # 如果有数据，则按天返回检索命中次数
            data = [{'date': str(row.date), 'total_search_hit_count': row.total_search_hit_count or 0} for row in result]

            # 如果没有数据，则生成时间范围内的日期，命中次数为0
            result_dates = {str(row.date): row.total_search_hit_count or 0 for row in result}
            missing_dates = [date for date in all_dates if date.strftime('%Y-%m-%d') not in result_dates]

            for missing_date in missing_dates:
                data.append({'date': missing_date.strftime('%Y-%m-%d'), 'total_search_hit_count': 0})

            # 返回按日期分组的命中次数
            return sorted(data, key=lambda x: x['date'])

        else:
            # 如果没有时间范围，返回总命中次数
            total_search_hit_count = query.with_entities(func.sum(AigcKnowledge.search_hit_count)).scalar()
            return  total_search_hit_count or 0  # 总命中次数，日期为空字符串

    def get_embedding_success_count_per_day(self, knowledge_id=None, start_time=None, end_time=None):
        """ 获取每天向量化成功的文档数量 """
        if not start_time:
            start_time = datetime.today().date()
        else:
            # 确保 start_time 是日期类型，如果是字符串，进行转换
            if isinstance(start_time, str):
                start_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S').date()
            elif isinstance(start_time, datetime):
                start_time = start_time.date()

        if not end_time:
            end_time = datetime.today().date()
        else:
            # 确保 end_time 是日期类型，如果是字符串，进行转换
            if isinstance(end_time, str):
                end_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S').date()
            elif isinstance(end_time, datetime):
                end_time = end_time.date()

        # 获取时间段内的所有日期
        delta = end_time - start_time
        all_dates = [start_time + timedelta(days=i) for i in range(delta.days + 1)]

        if knowledge_id:
            query = self.session.query(
                func.date(AigcKnowledgeDocument.update_time).label('date'),  # 按日期分组
                func.count(AigcKnowledgeDocument.document_id).label('embedding_success_count')
            ).filter(
                AigcKnowledgeDocument.knowledge_id == knowledge_id,
                AigcKnowledgeDocument.state == "已完成",
                AigcKnowledgeDocument.current_node == "向量化",
                AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE  # 排除已删除文档
            )
        else:
            query = self.session.query(
                func.date(AigcKnowledgeDocument.update_time).label('date'),  # 按日期分组
                func.count(AigcKnowledgeDocument.document_id).label('embedding_success_count')
            ).filter(
                AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE,
                AigcKnowledgeDocument.state == "已完成",
                AigcKnowledgeDocument.current_node == "向量化",
            )

        query = query.filter(AigcKnowledgeDocument.update_time >= start_time, AigcKnowledgeDocument.update_time <= end_time)

        # 按日期分组并排序
        query = query.group_by(func.date(AigcKnowledgeDocument.update_time)).order_by(func.date(AigcKnowledgeDocument.update_time))

        # 执行查询并获取结果
        result = query.all()

        # 将查询结果转化为字典，日期为键，upload_success_count 为值
        success_count_dict = {str(row.date): row.embedding_success_count for row in result}

        # 准备返回的结果，确保每个日期都有记录
        embedding_success_count_per_day = []
        for date in all_dates:
            # 如果某天没有记录，则默认 upload_success_count 为 0
            embedding_success_count_per_day.append({
                'date': str(date),
                'embedding_success_count': success_count_dict.get(str(date), 0)
            })

        # 返回结果，格式为 [{'date': 'YYYY-MM-DD', 'embedding_success_count': count}, ...]
        return embedding_success_count_per_day

    def get_upload_success_count_per_day(self, knowledge_id=None, start_time=None, end_time=None):
        """ 获取每天上传的文档数量 """
        # 如果没有传入时间范围，则默认为当前日期
        if not start_time:
            start_time = datetime.today().date()
        else:
            # 确保 start_time 是日期类型，如果是字符串，进行转换
            if isinstance(start_time, str):
                start_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S').date()
            elif isinstance(start_time, datetime):
                start_time = start_time.date()

        if not end_time:
            end_time = datetime.today().date()
        else:
            # 确保 end_time 是日期类型，如果是字符串，进行转换
            if isinstance(end_time, str):
                end_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S').date()
            elif isinstance(end_time, datetime):
                end_time = end_time.date()

        # 获取时间段内的所有日期
        delta = end_time - start_time
        all_dates = [start_time + timedelta(days=i) for i in range(delta.days + 1)]

        # 构造基础查询
        if knowledge_id:
            query = self.session.query(
                func.date(AigcKnowledgeDocument.add_time).label('date'),
                func.count(AigcKnowledgeDocument.document_id).label('upload_success_count')
            ).filter(
                AigcKnowledgeDocument.knowledge_id == knowledge_id,
                AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
            )
        else:
            query = self.session.query(
                func.date(AigcKnowledgeDocument.add_time).label('date'),
                func.count(AigcKnowledgeDocument.document_id).label('upload_success_count')
            ).filter(
                AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
            )

        # 如果传入了时间范围，添加时间过滤
        query = query.filter(AigcKnowledgeDocument.add_time >= start_time, AigcKnowledgeDocument.add_time <= end_time)

        # 按日期分组并排序
        query = query.group_by(func.date(AigcKnowledgeDocument.add_time)).order_by(func.date(AigcKnowledgeDocument.add_time))

        # 执行查询并获取结果
        result = query.all()

        # 将查询结果转化为字典，日期为键，upload_success_count 为值
        success_count_dict = {str(row.date): row.upload_success_count for row in result}

        # 准备返回的结果，确保每个日期都有记录
        upload_success_count_per_day = []
        for date in all_dates:
            # 如果某天没有记录，则默认 upload_success_count 为 0
            upload_success_count_per_day.append({
                'date': str(date),
                'upload_success_count': success_count_dict.get(str(date), 0)
            })

        return upload_success_count_per_day

    def get_document_count_subquery(self, knowledge_id, start_time=None, end_time=None):
        """ 获取文档数量的子查询 """
        query = self.session.query(
            AigcKnowledgeDocument.knowledge_id,
            func.count(AigcKnowledgeDocument.document_id).label('doc_count')
        ).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        )

        # 如果传入了 start_time 和 end_time，添加时间范围过滤
        if start_time:
            query = query.filter(AigcKnowledgeDocument.add_time >= start_time)
        if end_time:
            query = query.filter(AigcKnowledgeDocument.add_time <= end_time)

        return query.group_by(AigcKnowledgeDocument.knowledge_id).subquery()


    def get_document_cleaning_rule_count(self, knowledge_id):
        """ 获取文档清洗规则的数量 """
        return self.session.query(
            func.count(KnowledgeClearRelation.rule_id).label('doc_cleaning_rule_count')
        ).filter(
            KnowledgeClearRelation.knowledge_id == knowledge_id
        ).scalar()  # 使用 .scalar() 获取单一值

    def get_chunk_cleaning_rule_count(self, knowledge_id):
        """ 获取切片清洗规则的数量 """
        return self.session.query(
            func.count(RelFileRule.rule_id).label('chunk_cleaning_rule_count')
        ).filter(
            RelFileRule.knowledge_id == knowledge_id
        ).scalar()  # 使用 .scalar() 获取单一值

    def get_app_count_subquery(self, knowledge_id):
        """ 获取关联应用数量的子查询 """
        return self.session.query(
            RelAppKnowledge.knowledge_id,
            func.count(RelAppKnowledge.app_id).label('app_count')
        ).join(
            AigcApp, and_(
                RelAppKnowledge.app_id == AigcApp.app_id,
                RelAppKnowledge.delete_flag == settings.DELETE_FLAG_FALSE,
                AigcApp.delete_flag == settings.DELETE_FLAG_FALSE
            )
        ).filter(
            RelAppKnowledge.knowledge_id == knowledge_id
        ).group_by(RelAppKnowledge.knowledge_id).subquery()

    def get_embedding_success_count_subquery(self, knowledge_id, start_time=None, end_time=None):
        """ 获取向量化成功的文档数量的子查询 """
        query = self.session.query(
            AigcKnowledgeDocument.knowledge_id,
            func.count(AigcKnowledgeDocument.document_id).label('embedding_success_count')
        ).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocument.state == "已完成",
            AigcKnowledgeDocument.current_node == "向量化",
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        )

        # 如果传入了 start_time 和 end_time，添加时间范围过滤
        if start_time:
            query = query.filter(AigcKnowledgeDocument.add_time >= start_time)
        if end_time:
            query = query.filter(AigcKnowledgeDocument.add_time <= end_time)

        return query.group_by(AigcKnowledgeDocument.knowledge_id).subquery()

    def get_doc_clear_success_count_subquery(self, knowledge_id):
        """ 获取文档清洗成功的文档数量的子查询 """
        return self.session.query(
            AigcKnowledgeDocument.knowledge_id,
            func.count(AigcKnowledgeDocument.document_id).label('doc_clear_success_count')
        ).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocument.state == "已完成",
            AigcKnowledgeDocument.current_node == "文档清洗",
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).group_by(AigcKnowledgeDocument.knowledge_id).subquery()

    def get_doc_split_success_count_subquery(self, knowledge_id):
        """ 获取切片成功的文档数量的子查询 """
        return self.session.query(
            AigcKnowledgeDocument.knowledge_id,
            func.count(AigcKnowledgeDocument.document_id).label('doc_split_success_count')
        ).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocument.state == "已完成",
            AigcKnowledgeDocument.current_node == "文档切片",
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).group_by(AigcKnowledgeDocument.knowledge_id).subquery()

    def get_doc_split_clear_success_count_subquery(self, knowledge_id):
        """ 获取切片清洗成功的文档数量的子查询 """
        return self.session.query(
            AigcKnowledgeDocument.knowledge_id,
            func.count(AigcKnowledgeDocument.document_id).label('doc_split_clear_success_count')
        ).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocument.state == "已完成",
            AigcKnowledgeDocument.current_node == "切片清洗",
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).group_by(AigcKnowledgeDocument.knowledge_id).subquery()

    # 失败状态子查询
    def get_embedding_failure_count_subquery(self, knowledge_id):
        """ 获取向量化失败的文档数量的子查询 """
        return self.session.query(
            AigcKnowledgeDocument.knowledge_id,
            func.count(AigcKnowledgeDocument.document_id).label('embedding_failure_count')
        ).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocument.state == "失败",
            AigcKnowledgeDocument.current_node == "向量化",
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).group_by(AigcKnowledgeDocument.knowledge_id).subquery()

    def get_doc_clear_failure_count_subquery(self, knowledge_id):
        """ 获取文档清洗失败的文档数量的子查询 """
        return self.session.query(
            AigcKnowledgeDocument.knowledge_id,
            func.count(AigcKnowledgeDocument.document_id).label('doc_clear_failure_count')
        ).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocument.state == "失败",
            AigcKnowledgeDocument.current_node == "文档清洗",
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).group_by(AigcKnowledgeDocument.knowledge_id).subquery()

    def get_doc_split_failure_count_subquery(self, knowledge_id):
        """ 获取切片失败的文档数量的子查询 """
        return self.session.query(
            AigcKnowledgeDocument.knowledge_id,
            func.count(AigcKnowledgeDocument.document_id).label('doc_split_failure_count')
        ).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocument.state == "失败",
            AigcKnowledgeDocument.current_node == "文档切片",
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).group_by(AigcKnowledgeDocument.knowledge_id).subquery()

    def get_doc_split_clear_failure_count_subquery(self, knowledge_id):
        """ 获取切片清洗失败的文档数量的子查询 """
        return self.session.query(
            AigcKnowledgeDocument.knowledge_id,
            func.count(AigcKnowledgeDocument.document_id).label('doc_split_clear_failure_count')
        ).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocument.state == "失败",
            AigcKnowledgeDocument.current_node == "切片清洗",
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).group_by(AigcKnowledgeDocument.knowledge_id).subquery()

    # 进行中状态子查询
    def get_embedding_in_progress_count_subquery(self, knowledge_id):
        """ 获取向量化进行中的文档数量的子查询 """
        return self.session.query(
            AigcKnowledgeDocument.knowledge_id,
            func.count(AigcKnowledgeDocument.document_id).label('embedding_in_progress_count')
        ).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocument.state == "进行中",
            AigcKnowledgeDocument.current_node == "向量化",
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).group_by(AigcKnowledgeDocument.knowledge_id).subquery()

    def get_doc_clear_in_progress_count_subquery(self, knowledge_id):
        """ 获取文档清洗进行中的文档数量的子查询 """
        return self.session.query(
            AigcKnowledgeDocument.knowledge_id,
            func.count(AigcKnowledgeDocument.document_id).label('doc_clear_in_progress_count')
        ).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocument.state == "进行中",
            AigcKnowledgeDocument.current_node == "文档清洗",
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).group_by(AigcKnowledgeDocument.knowledge_id).subquery()

    def get_doc_split_in_progress_count_subquery(self, knowledge_id):
        """ 获取切片进行中的文档数量的子查询 """
        return self.session.query(
            AigcKnowledgeDocument.knowledge_id,
            func.count(AigcKnowledgeDocument.document_id).label('doc_split_in_progress_count')
        ).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocument.state == "进行中",
            AigcKnowledgeDocument.current_node == "文档切片",
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).group_by(AigcKnowledgeDocument.knowledge_id).subquery()

    def get_doc_split_clear_in_progress_count_subquery(self, knowledge_id):
        """ 获取切片清洗进行中的文档数量的子查询 """
        return self.session.query(
            AigcKnowledgeDocument.knowledge_id,
            func.count(AigcKnowledgeDocument.document_id).label('doc_split_clear_in_progress_count')
        ).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocument.state == "进行中",
            AigcKnowledgeDocument.current_node == "切片清洗",
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).group_by(AigcKnowledgeDocument.knowledge_id).subquery()


    def get_file_failed_count_subquery(self, knowledge_id):
        """ 获取文件状态失败的文档数量 """
        return self.session.query(
            AigcKnowledgeDocument.knowledge_id,
            func.count(AigcKnowledgeDocument.document_id).label('file_failed_count')
        ).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocument.state == "失败",
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).group_by(AigcKnowledgeDocument.knowledge_id).subquery()

    def get_file_pendding_count_subquery(self, knowledge_id):
        """ 获取文件状态进行中的文档数量 """
        return self.session.query(
            AigcKnowledgeDocument.knowledge_id,
            func.count(AigcKnowledgeDocument.document_id).label('file_pendding_count'),
        ).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocument.state == "进行中",
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).group_by(AigcKnowledgeDocument.knowledge_id).subquery()

    def get_file_success_count_subquery(self, knowledge_id):
        """ 获取文件状态已完成的文档数量 """
        return self.session.query(
            AigcKnowledgeDocument.knowledge_id,
            func.count(AigcKnowledgeDocument.document_id).label('file_success_count')
        ).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocument.state == "已完成",
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).group_by(AigcKnowledgeDocument.knowledge_id).subquery()

    def get_deleted_document_count(self, knowledge_id, start_time=None, end_time=None):
        """ 获取已删除文档数量 """
        # 基本查询
        query = self.session.query(
            func.count(AigcKnowledgeDocument.document_id).label('deleted_doc_count'),
            func.date(AigcKnowledgeDocument.update_time).label('date')
        ).filter(
            AigcKnowledgeDocument.knowledge_id == knowledge_id,
            AigcKnowledgeDocument.delete_flag == 1  # 已删除文档
        )

        if start_time and end_time:
            # 转换时间为 datetime 类型
            if isinstance(start_time, str):
                start_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
            if isinstance(end_time, str):
                end_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')

            # 过滤时间范围
            query = query.filter(AigcKnowledgeDocument.update_time >= start_time)
            query = query.filter(AigcKnowledgeDocument.update_time <= end_time)

            # 生成时间范围内的所有日期
            delta = end_time - start_time
            all_dates = [start_time + timedelta(days=i) for i in range(delta.days + 1)]

            # 按日期分组并排序
            query = query.group_by(func.date(AigcKnowledgeDocument.update_time)).order_by(func.date(AigcKnowledgeDocument.update_time))

            # 执行查询并获取结果
            result = query.all()

            # 如果有数据，则按天返回删除文档数量
            data = [{'date': str(row.date), 'deleted_doc_count': row.deleted_doc_count or 0} for row in result]

            # 如果没有数据，则生成时间范围内的日期，删除文档数为0
            result_dates = {str(row.date): row.deleted_doc_count or 0 for row in result}
            missing_dates = [date for date in all_dates if date.strftime('%Y-%m-%d') not in result_dates]

            for missing_date in missing_dates:
                data.append({'date': missing_date.strftime('%Y-%m-%d'), 'deleted_doc_count': 0})

            # 返回按日期分组的删除文档数量
            return sorted(data, key=lambda x: x['date'])

        else:
            # 如果没有时间范围，只返回删除文档的总数
            deleted_count = query.scalar()
            return deleted_count or 0



    def get_embedding_success_by_doc_type(self, knowledge_id=None):
        """
        统计向量化成功的不同文档类型的数量
        """
        # 进行查询，按文档类型分组统计
        if knowledge_id:
            result = self.session.query(
                AigcKnowledgeDocument.doc_type,
                func.count(AigcKnowledgeDocument.document_id).label('doc_count')
            ).filter(
                AigcKnowledgeDocument.knowledge_id == knowledge_id,
                AigcKnowledgeDocument.state == "已完成",
                AigcKnowledgeDocument.current_node == "向量化",
                AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
            ).group_by(AigcKnowledgeDocument.doc_type).all()
        else:
            result = self.session.query(
                AigcKnowledgeDocument.doc_type,
                func.count(AigcKnowledgeDocument.document_id).label('doc_count')
            ).filter(
                AigcKnowledgeDocument.state == "已完成",
                AigcKnowledgeDocument.current_node == "向量化",
                AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
            ).group_by(AigcKnowledgeDocument.doc_type).all()

        def get_doc_type_string(mime_type):
            if mime_type in ['image/jpeg', 'image/png','image/jpg']:
                return '图片'
            elif mime_type == 'application/pdf':
                return 'PDF'
            elif mime_type in ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet','application/vnd.ms-excel']:
                return '表格'
            elif mime_type == 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
                return 'PPTX'
            elif mime_type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain', 'docx','text']:
                return '文档'
            else:
                return '其他'


        # 格式化结果
        doc_type_count_map = {}
        for row in result:
            doc_type_str = get_doc_type_string(row.doc_type)
            if doc_type_str in doc_type_count_map:
                doc_type_count_map[doc_type_str] += row.doc_count  # 累加相同类型文档的数量
            else:
                doc_type_count_map[doc_type_str] = row.doc_count  # 初始化该类型文档的计数

        # 将字典中的数据转换为列表格式
        doc_type_count = [{'doc_type': doc_type, 'doc_count': count}
                        for doc_type, count in doc_type_count_map.items()]

        return doc_type_count


    def get_all_knowledge_statistic(self, args):
        """
        获取所有知识库的统计数据：

        全部目录数量
        全部知识库数量（全部个人知识库，全部公共知识库）
        全部向量化成功的文档数量（知识提取，上传）
        全部向量化成功的文档数量根据文档类型
        """
        start_time = args.get("start_time")
        end_time = args.get("end_time")
        if start_time and end_time:
            return self.get_all_knowledge_statistic_by_time(start_time, end_time)
        else:
            return self.get_all_knowledge_statistic_count()

    def get_all_knowledge_statistic_count(self):
        """ 获取所有知识库的统计数据 """
        """
        全部目录数量
        全部知识库数量（全部个人知识库，全部公共知识库）
        全部向量化成功的文档数量（知识提取，上传）
        全部向量化成功的文档数量根据文档类型   
        """
        with self.session_scope() as session:
            # 全部目录数量
            all_dir_count = session.query(KnowDirectory).filter(KnowDirectory.delete_flag == settings.DELETE_FLAG_FALSE).count()
            # 全部知识库数量（全部个人知识库，全部公共知识库）
            all_knowledge_count = session.query(AigcKnowledge).filter(AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE).count()
            all_knowledge_count_personal = session.query(AigcKnowledge).filter(AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE, AigcKnowledge.scope == 1).count()
            all_knowledge_count_public = session.query(AigcKnowledge).filter(AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE, AigcKnowledge.scope == 0).count()
            # 全部向量化成功的文档数量（知识提取，上传）
            all_embedding_success_count = session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE, AigcKnowledgeDocument.state == "已完成", AigcKnowledgeDocument.current_node == "向量化").count()
            all_embedding_success_count_extract_knlg = session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE, AigcKnowledgeDocument.state == "已完成", AigcKnowledgeDocument.current_node == "向量化",AigcKnowledgeDocument.knlg_extract==1).count()
            all_embedding_success_count_upload = session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE, AigcKnowledgeDocument.state == "已完成", AigcKnowledgeDocument.current_node == "向量化",AigcKnowledgeDocument.knlg_extract==0).count()

            # 全部向量化成功的个人文档数量
            all_embedding_success_count_personal = session.query(AigcKnowledgeDocument) \
                .join(AigcKnowledge, AigcKnowledge.knowledge_id == AigcKnowledgeDocument.knowledge_id) \
                .filter(
                    AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE,  # 过滤掉已删除的文档
                    AigcKnowledgeDocument.state == "已完成",  # 文档状态为 "已完成"
                    AigcKnowledgeDocument.current_node == "向量化",  # 当前处理节点是 "向量化"
                    AigcKnowledge.scope == 1  # 只统计个人知识库中的文档 (scope = 1)
                ).count()  # 获取符合条件的文档数量
            # 全部向量化成功的公共文档数量
            all_embedding_success_count_public = session.query(AigcKnowledgeDocument) \
                .join(AigcKnowledge, AigcKnowledge.knowledge_id == AigcKnowledgeDocument.knowledge_id) \
                .filter(
                    AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE,  # 过滤掉已删除的文档
                    AigcKnowledgeDocument.state == "已完成",  # 文档状态为 "已完成"
                    AigcKnowledgeDocument.current_node == "向量化",  # 当前处理节点是 "向量化"
                    AigcKnowledge.scope == 0  # 只统计公共知识库中的文档 (scope = 0)
                ).count()  # 获取符合条件的文档数量
            # 全部向量化成功的文档数量根据文档类型
            all_embedding_success_count_by_doc_type = self.get_embedding_success_by_doc_type()
            # 获取命中文档数的排名，按命中次数进行降序排序
            all_hit_count_ranking = self.get_hit_count_ranking()
            # 获取知识库文档数量的排名，按文档数量进行降序排序。
            all_document_count_ranking = self.get_document_count_ranking()
            # 获取用户上传文档数量的排名，按上传文档数量进行降序排序，返回用户的名称。
            all_user_document_ranking = self.get_user_document_upload_ranking()
            # 获取知识库命中文档数的排名，按命中次数进行降序排序。
            all_knowledge_hit_count_ranking = self.get_knowledge_hit_count_ranking()
        return {
            "all_dir_count": all_dir_count,
            "all_knowledge_count":all_knowledge_count,
            "all_knowledge_count_personal": all_knowledge_count_personal,
            "all_knowledge_count_public": all_knowledge_count_public,
            "all_embedding_success_count": all_embedding_success_count,
            "all_embedding_success_count_by_doc_type": all_embedding_success_count_by_doc_type,
            "all_hit_count_ranking": all_hit_count_ranking,
            "all_document_count_ranking": all_document_count_ranking,
            "all_embedding_success_count_personal": all_embedding_success_count_personal,
            "all_embedding_success_count_public": all_embedding_success_count_public,
            "all_user_document_ranking": all_user_document_ranking,
            "all_knowledge_hit_count_ranking": all_knowledge_hit_count_ranking,
            "all_embedding_success_count_extract_knlg": all_embedding_success_count_extract_knlg,
            "all_embedding_success_count_upload": all_embedding_success_count_upload
        }

    def get_all_knowledge_statistic_by_time(self, start_time, end_time):
        """ 获取所有知识库的统计数据，按时间范围查询 """
        with self.session_scope() as session:
            # 新增目录数量
            new_dir_count = session.query(KnowDirectory).filter(KnowDirectory.delete_flag == settings.DELETE_FLAG_FALSE, KnowDirectory.create_time >= start_time, KnowDirectory.create_time <= end_time).count()
            # 新增目录环比；
            input_time, start_of_period, previous_period_start, previous_period_same_time = get_ring_times(start_time,"day")
            input_time, start_of_period, previous_period_end, previous_period_same_time = get_ring_times(end_time,"day")

            previous_period_start =str(previous_period_start)
            previous_period_end =str(previous_period_end)
            new_dir_count_ring = session.query(KnowDirectory).filter(KnowDirectory.delete_flag == settings.DELETE_FLAG_FALSE, KnowDirectory.create_time >= previous_period_start, KnowDirectory.create_time <= previous_period_end).count()
            if new_dir_count_ring == 0:
                dir_ring_rate = 0
            else:
                dir_ring_rate = round(((new_dir_count - new_dir_count_ring) / new_dir_count_ring) * 100,2 )

            #全部目录环比
            all_dir_count_ring = session.query(KnowDirectory).filter(KnowDirectory.delete_flag == settings.DELETE_FLAG_FALSE, KnowDirectory.create_time <= previous_period_end).count()
            pre_all_dir_count = session.query(KnowDirectory).filter(KnowDirectory.delete_flag == settings.DELETE_FLAG_FALSE).count()
            if all_dir_count_ring == 0:
                all_dir_ring_rate = 0
            else:
                all_dir_ring_rate = round(((all_dir_count_ring - pre_all_dir_count) / pre_all_dir_count) *100 ,2)

            # 新增知识库数量
            new_knowledge_count = session.query(AigcKnowledge).filter(AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE, AigcKnowledge.add_time >= start_time, AigcKnowledge.add_time <= end_time).count()

            #新增个人知识库
            new_personal_knowledge_count = session.query(AigcKnowledge).filter(AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE, AigcKnowledge.add_time >= start_time, AigcKnowledge.add_time <= end_time, AigcKnowledge.scope==1).count()

            #新增公共知识库
            new_public_knowledge_count = session.query(AigcKnowledge).filter(AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE, AigcKnowledge.add_time >= start_time, AigcKnowledge.add_time <= end_time,AigcKnowledge.scope==0).count()

            # 新增知识库环比
            new_knowledge_count_ring = session.query(AigcKnowledge).filter(AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE, AigcKnowledge.add_time <= previous_period_end).count()
            if new_knowledge_count_ring == 0:
                knowledge_ring_rate = 0
            else:
                knowledge_ring_rate = round(((new_knowledge_count - new_knowledge_count_ring) / new_knowledge_count_ring) * 100,2)
            # 全部知识库环比
            all_knowledge_count_ring = session.query(AigcKnowledge).filter(AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE, AigcKnowledge.add_time <= previous_period_end).count()
            pre_all_knowledge_count = session.query(AigcKnowledge).filter(AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE).count()
            if all_knowledge_count_ring == 0:
                all_knowledge_ring_rate = 0
            else:
                all_knowledge_ring_rate = round(((all_knowledge_count_ring - pre_all_knowledge_count) / pre_all_knowledge_count )*100 ,2)


            # 新增向量化成功的文档数量
            new_embedding_success_count = session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE, AigcKnowledgeDocument.state == "已完成", AigcKnowledgeDocument.current_node == "向量化", AigcKnowledgeDocument.update_time >= start_time, AigcKnowledgeDocument.update_time <= end_time).count()

            # 新增向量化成功的文档数量环比
            new_embedding_success_count_ring = session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE, AigcKnowledgeDocument.state == "已完成", AigcKnowledgeDocument.current_node == "向量化", AigcKnowledgeDocument.update_time >= previous_period_start, AigcKnowledgeDocument.update_time <= previous_period_end).count()
            if new_embedding_success_count_ring == 0:
                embedding_success_ring_rate = 0
            else:
                embedding_success_ring_rate = round(((new_embedding_success_count - new_embedding_success_count_ring) / new_embedding_success_count_ring) *100 ,2 )

            # 全部向量化成功的文档数量环比
            all_embedding_success_count_ring = session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE, AigcKnowledgeDocument.state == "已完成", AigcKnowledgeDocument.current_node == "向量化", AigcKnowledgeDocument.update_time >= previous_period_start, AigcKnowledgeDocument.update_time <= previous_period_end).count()
            pre_all_embedding_success_count = session.query(AigcKnowledgeDocument).filter(AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE).count()
            if all_embedding_success_count_ring == 0:
                all_embedding_success_ring_rate = 0
            else:
                all_embedding_success_ring_rate = round(((all_embedding_success_count_ring - pre_all_embedding_success_count) / pre_all_embedding_success_count )*100 , 2)


            # 新增向量化成功的个人文档数量
            new_embedding_success_count_personal = session.query(AigcKnowledgeDocument) \
                .join(AigcKnowledge, AigcKnowledge.knowledge_id == AigcKnowledgeDocument.knowledge_id) \
                .filter(
                    AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE,  # 过滤掉已删除的文档
                    AigcKnowledgeDocument.state == "已完成",  # 文档状态为 "已完成"
                    AigcKnowledgeDocument.current_node == "向量化",  # 当前处理节点是 "向量化"
                    AigcKnowledgeDocument.update_time >= start_time,  # 更新时间大于等于开始时间
                    AigcKnowledgeDocument.update_time <= end_time,  # 更新时间小于等于结束时间
                    AigcKnowledge.scope == 1  # 只统计个人知识库中的文档 (scope = 1)
                ).count()  # 获取符合条件的文档数量
            # 新增向量化成功的公共文档数量
            new_embedding_success_count_public = session.query(AigcKnowledgeDocument) \
                .join(AigcKnowledge, AigcKnowledge.knowledge_id == AigcKnowledgeDocument.knowledge_id) \
                .filter(
                    AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE,  # 过滤掉已删除的文档
                    AigcKnowledgeDocument.state == "已完成",  # 文档状态为 "已完成"
                    AigcKnowledgeDocument.current_node == "向量化",  # 当前处理节点是 "向量化"
                    AigcKnowledgeDocument.update_time >= start_time,  # 更新时间大于等于开始时间
                    AigcKnowledgeDocument.update_time <= end_time,  # 更新时间小于等于结束时间
                    AigcKnowledge.scope == 0  # 只统计公共知识库中的文档 (scope = 0)
                ).count()  # 获取符合条件的文档数量
            logger.info(f"{start_time},{end_time}")
            # 获取每天上传的文档数量
            new_upload_success_count_per_day = self.get_upload_success_count_per_day(None,start_time, end_time)
            # 获取向量化成功文档数量每天以及总数
            embedding_success_count_total_per_day = self.get_embedding_success_count_total_per_day(start_time,end_time)
            # 获取个人知识库当天新增以及总数
            personal_knowledge_count_total_per_day = self.get_personal_knowledge_count_total_per_day(start_time,end_time)
            # 获取公共知识库当天新增以及总数
            public_knowledge_count_total_per_day = self.get_public_knowledge_count_total_per_day(start_time,end_time)

        return {
            "new_dir_count": new_dir_count,
            "new_knowledge_count": new_knowledge_count,
            "new_public_knowledge_count":new_public_knowledge_count,
            "new_personal_knowledge_count":new_personal_knowledge_count,
            "new_embedding_success_count": new_embedding_success_count,
            "new_embedding_success_count_personal": new_embedding_success_count_personal,
            "new_embedding_success_count_public": new_embedding_success_count_public,
            "new_upload_success_count_per_day": new_upload_success_count_per_day,
            "embedding_success_count_total_per_day": embedding_success_count_total_per_day,
            "personal_knowledge_count_total_per_day": personal_knowledge_count_total_per_day,
            "public_knowledge_count_total_per_day": public_knowledge_count_total_per_day,
            "dir_ring_rate": f"{dir_ring_rate}%",
            "all_dir_ring_rate": f"{all_dir_ring_rate}%",
            "knowledge_ring_rate": f"{knowledge_ring_rate}%",
            "all_knowledge_ring_rate": f"{all_knowledge_ring_rate}%",
            "embedding_success_ring_rate": f"{embedding_success_ring_rate}%",
            "all_embedding_success_ring_rate": f"{all_embedding_success_ring_rate}%"
        }


    def get_embedding_success_count_total_per_day(self, start_time=None, end_time=None):
        """ 获取每天截止到当天的向量化成功的文档数量总数 """

        # 确保 start_time 和 end_time 是日期类型
        if not start_time:
            start_time = datetime.today().date()
        else:
            if isinstance(start_time, str):
                start_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S').date()
            elif isinstance(start_time, datetime):
                start_time = start_time.date()

        if not end_time:
            end_time = datetime.today().date()
        else:
            if isinstance(end_time, str):
                end_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S').date()
            elif isinstance(end_time, datetime):
                end_time = end_time.date()

        # 获取时间段内的所有日期
        delta = end_time - start_time
        all_dates = [start_time + timedelta(days=i) for i in range(delta.days + 1)]

        # 基本查询
        query = self.session.query(
            func.date(AigcKnowledgeDocument.add_time).label('date'),  # 按日期分组
            func.count(AigcKnowledgeDocument.document_id).label('daily_count')
        ).filter(
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE,
            AigcKnowledgeDocument.state == "已完成",
            AigcKnowledgeDocument.current_node == "向量化",
        )

        # 如果传入了 start_time 和 end_time，添加时间范围过滤
        query = query.filter(AigcKnowledgeDocument.add_time >= start_time, AigcKnowledgeDocument.add_time <= end_time)

        # 按日期分组并排序
        query = query.group_by(func.date(AigcKnowledgeDocument.add_time)).order_by(func.date(AigcKnowledgeDocument.add_time))

        # 执行查询并获取结果
        result = query.all()

        # 将查询结果转化为字典，日期为键，daily_count 为值
        result_dates = {str(row.date): row.daily_count for row in result}

        # 格式化结果并填补缺失日期
        daily_counts = []
        cumulative_count = 0
        for date in all_dates:
            date_str = date.strftime('%Y-%m-%d')
            daily_count = result_dates.get(date_str, 0)  # 如果该日期没有记录，默认为 0
            cumulative_count += daily_count
            daily_counts.append({
                'date': date_str,
                'daily_count': daily_count,
                'cumulative_count': cumulative_count
            })

        return daily_counts


    def get_personal_knowledge_count_total_per_day(self, start_time=None, end_time=None):
        """ 获取每天截止到当天的个人知识库数量总数 """

        # 确保 start_time 和 end_time 是日期类型
        if not start_time:
            start_time = datetime.today().date()
        else:
            if isinstance(start_time, str):
                start_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S').date()
            elif isinstance(start_time, datetime):
                start_time = start_time.date()

        if not end_time:
            end_time = datetime.today().date()
        else:
            if isinstance(end_time, str):
                end_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S').date()
            elif isinstance(end_time, datetime):
                end_time = end_time.date()

        # 获取时间段内的所有日期
        delta = end_time - start_time
        all_dates = [start_time + timedelta(days=i) for i in range(delta.days + 1)]

        # 基本查询
        query = self.session.query(
            func.date(AigcKnowledge.add_time).label('date'),  # 按日期分组
            func.count(AigcKnowledge.knowledge_id).label('daily_count')
        ).filter(
            AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE,
            AigcKnowledge.scope == 1
        )

        # 如果传入了 start_time 和 end_time，添加时间范围过滤
        query = query.filter(AigcKnowledge.add_time >= start_time, AigcKnowledge.add_time <= end_time)

        # 按日期分组并排序
        query = query.group_by(func.date(AigcKnowledge.add_time)).order_by(func.date(AigcKnowledge.add_time))

        # 执行查询并获取结果
        result = query.all()

        # 将查询结果转化为字典，日期为键，daily_count 为值
        result_dates = {str(row.date): row.daily_count for row in result}

        # 格式化结果并填补缺失日期
        daily_counts = []
        cumulative_count = 0
        for date in all_dates:
            date_str = date.strftime('%Y-%m-%d')
            daily_count = result_dates.get(date_str, 0)  # 如果该日期没有记录，默认为 0
            cumulative_count += daily_count
            daily_counts.append({
                'date': date_str,
                'daily_count': daily_count,
                'cumulative_count': cumulative_count
            })

        return daily_counts


    def get_public_knowledge_count_total_per_day(self, start_time=None, end_time=None):
        """ 获取每天截止到当天的个人知识库数量总数 """

        # 确保 start_time 和 end_time 是日期类型
        if not start_time:
            start_time = datetime.today().date()
        else:
            if isinstance(start_time, str):
                start_time = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S').date()
            elif isinstance(start_time, datetime):
                start_time = start_time.date()

        if not end_time:
            end_time = datetime.today().date()
        else:
            if isinstance(end_time, str):
                end_time = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S').date()
            elif isinstance(end_time, datetime):
                end_time = end_time.date()

        # 获取时间段内的所有日期
        delta = end_time - start_time
        all_dates = [start_time + timedelta(days=i) for i in range(delta.days + 1)]

        # 基本查询
        query = self.session.query(
            func.date(AigcKnowledge.add_time).label('date'),  # 按日期分组
            func.count(AigcKnowledge.knowledge_id).label('daily_count')
        ).filter(
            AigcKnowledge.delete_flag == settings.DELETE_FLAG_FALSE,
            AigcKnowledge.scope == 1
        )

        # 如果传入了 start_time 和 end_time，添加时间范围过滤
        query = query.filter(AigcKnowledge.add_time >= start_time, AigcKnowledge.add_time <= end_time)

        # 按日期分组并排序
        query = query.group_by(func.date(AigcKnowledge.add_time)).order_by(func.date(AigcKnowledge.add_time))

        # 执行查询并获取结果
        result = query.all()

        # 将查询结果转化为字典，日期为键，daily_count 为值
        result_dates = {str(row.date): row.daily_count for row in result}

        # 格式化结果并填补缺失日期
        daily_counts = []
        cumulative_count = 0
        for date in all_dates:
            date_str = date.strftime('%Y-%m-%d')
            daily_count = result_dates.get(date_str, 0)  # 如果该日期没有记录，默认为 0
            cumulative_count += daily_count
            daily_counts.append({
                'date': date_str,
                'daily_count': daily_count,
                'cumulative_count': cumulative_count
            })

        return daily_counts


    def get_knowledge_hit_count_ranking(self, top_n=10):
        """
        获取命中知识库的排名，按命中次数进行降序排序。
        """
        # 基本查询，按知识库ID和命中次数进行分组
        query = self.session.query(
            AigcKnowledge.knowledge_name,
            func.sum(AigcKnowledgeDocument.hit_count).label('total_hit_count')
        ).join(
            AigcKnowledgeDocument, AigcKnowledge.knowledge_id == AigcKnowledgeDocument.knowledge_id  # 联接文档表
        ).filter(
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE  # 排除已删除文档
        ).group_by(AigcKnowledge.knowledge_name).order_by(func.sum(AigcKnowledgeDocument.hit_count).desc())

        # 限制返回前 top_n 条记录
        if top_n:
            query = query.limit(top_n)

        # 执行查询并获取结果
        result = query.all()

        # 格式化结果
        knowledge_hit_count_ranking = []
        rank = 1
        for row in result:
            knowledge_hit_count_ranking.append({
                'rank': rank,
                'knowledge_name': row.knowledge_name,
                'total_hit_count': row.total_hit_count or 0  # 确保命中次数为 0
            })
            rank += 1

        return knowledge_hit_count_ranking

    def get_hit_count_ranking(self, top_n=10):
        """
        获取命中文档数的排名，按命中次数进行降序排序。
        """
        # 基本查询，按文档类型和命中次数进行分组
        query = self.session.query(
            AigcKnowledgeDocument.doc_name,
            func.sum(AigcKnowledgeDocument.hit_count).label('total_hit_count')
        ).filter(
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE  # 排除已删除文档
        ).group_by(AigcKnowledgeDocument.doc_name).order_by(func.sum(AigcKnowledgeDocument.hit_count).desc())

        # 限制返回前 top_n 条记录
        if top_n:
            query = query.limit(top_n)

        # 执行查询并获取结果
        result = query.all()

        # 格式化结果
        hit_count_ranking = []
        rank = 1
        for row in result:
            hit_count_ranking.append({
                'rank': rank,
                'doc_name': row.doc_name,
                'total_hit_count': row.total_hit_count or 0  # 确保命中次数为 0
            })
            rank += 1

        return hit_count_ranking

    def get_document_count_ranking(self, top_n=10):
        """
        获取知识库文档数量的排名，按文档数量进行降序排序。
        """
        # 基本查询，按知识库分组统计文档数量
        query = self.session.query(
            AigcKnowledge.knowledge_name,
            func.count(AigcKnowledgeDocument.document_id).label('document_count')
        ).join(AigcKnowledgeDocument) \
        .filter(AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE)  # 排除已删除文档

        # 按文档数量降序排序
        query = query.group_by(AigcKnowledge.knowledge_name).order_by(func.count(AigcKnowledgeDocument.document_id).desc())

        # 限制返回前 top_n 条记录
        if top_n:
            query = query.limit(top_n)

        # 执行查询并获取结果
        result = query.all()

        # 格式化结果
        document_count_ranking = []
        rank = 1
        for row in result:
            document_count_ranking.append({
                'rank': rank,
                'knowledge_name': row.knowledge_name,
                'document_count': row.document_count
            })
            rank += 1

        return document_count_ranking


    def get_user_document_upload_ranking(self, top_n=10):
        """
        获取用户上传文档数量的排名，按上传文档数量进行降序排序，返回用户的名称。
        """
        # 基本查询，按用户分组统计文档数量
        query = self.session.query(
            AigcKnowledgeDocument.tp_user_id,
            func.count(AigcKnowledgeDocument.document_id).label('document_count'),
            TouchpointUser.tp_user_name  # 获取用户的名称
        ).join(
            TouchpointUser, AigcKnowledgeDocument.tp_user_id == TouchpointUser.tp_user_id  # 联接用户信息表
        ).filter(
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE  # 排除已删除文档
        ).group_by(AigcKnowledgeDocument.tp_user_id, TouchpointUser.tp_user_name)  # 按用户和用户名分组

        # 按文档数量降序排序
        query = query.order_by(func.count(AigcKnowledgeDocument.document_id).desc())

        # 限制返回前 top_n 条记录
        if top_n:
            query = query.limit(top_n)

        # 执行查询并获取结果
        result = query.all()

        # 格式化结果
        user_document_ranking = []
        rank = 1
        for row in result:
            user_document_ranking.append({
                'rank': rank,
                'tp_user_id': row.tp_user_id,
                'tp_user_name': row.tp_user_name,  # 添加用户名称
                'document_count': row.document_count
            })
            rank += 1

        return user_document_ranking

    def increment_search_hit_count(self, knowledge_id):
        """ 增加知识库的搜索命中次数 """
        self.session.query(AigcKnowledge).filter(AigcKnowledge.knowledge_id == knowledge_id).update({
            AigcKnowledge.search_hit_count: func.coalesce(AigcKnowledge.search_hit_count, 0) + 1
        })
        self.session.commit()


    def get_knowledge_individual(self, knowledge_id):
        with self.session_scope() as session:
            knowledge_individual = session.query(AigcKnowledgeIndividual).filter(
                AigcKnowledgeIndividual.knowledge_id == knowledge_id,
                AigcKnowledgeIndividual.delete_flag == settings.DELETE_FLAG_FALSE
            ).first()

            if knowledge_individual:
                return knowledge_individual
            else:
                logger.info(f"unable to get_knowledge_individual for knowledge_id: {knowledge_id}")
        return {}

    def delete_knowledge_individual(self, knowledge_id):
        with self.session_scope() as session:
            session.query(AigcKnowledgeIndividual).filter(
                AigcKnowledgeIndividual.knowledge_id == knowledge_id
            ).update({'delete_flag': settings.DELETE_FLAG_TRUE})
            session.commit()
            return knowledge_id

    def add_knowledge_individual(self, knowledge_id, knowledge_name, chunk_size, recall_size, strategy,
                                 total_chars=None,
                                 total_documents=None, avg_doc_length=None, avg_sentence_length=None,
                                 vocabulary_size=None, keyword_density=None, noun_ratio=None,
                                 avg_cosine_similarity=None, diversity_score=None, text_complexity_score=None,
                                 classify_scores=None, cluster_info=None, recall_threshold=None,
                                 avg_paragraphs_length=None, avg_paragraphs=None, estimate_density=None):

        def convert_to_string(value):
            if isinstance(value, (list, dict)):
                return json.dumps(value)
            return value

        classify_scores = convert_to_string(classify_scores)
        cluster_info = convert_to_string(cluster_info)

        obj = AigcKnowledgeIndividual(
            id=get_snowflake_id(),
            knowledge_id=knowledge_id,
            knowledge_name=knowledge_name,
            chunk_size=chunk_size,
            recall_size=recall_size,
            strategy=strategy,
            total_chars=total_chars,
            total_documents=total_documents,
            avg_doc_length=avg_doc_length,
            avg_sentence_length=avg_sentence_length,
            vocabulary_size=vocabulary_size,
            keyword_density=keyword_density,
            noun_ratio=noun_ratio,
            avg_cosine_similarity=avg_cosine_similarity,
            diversity_score=diversity_score,
            text_complexity_score=text_complexity_score,
            classify_scores=classify_scores,
            cluster_info=cluster_info,
            recall_threshold=recall_threshold,
            avg_paragraphs_length=avg_paragraphs_length,
            avg_paragraphs=avg_paragraphs,
            estimate_density=estimate_density
        )
        self.session.add(obj)
        self.session.commit()
        self.compute_statistics()
        return str(knowledge_id)


    def compute_statistics(self):
        STATISTICS_ID = -1

        records = self.session.query(AigcKnowledgeIndividual).filter(
            AigcKnowledgeIndividual.delete_flag == settings.DELETE_FLAG_FALSE,
            AigcKnowledgeIndividual.knowledge_id != STATISTICS_ID
        ).all()

        def calc_avg_non_zero(get_attr):
            values = [getattr(r, get_attr) for r in records]
            non_zero_values = [v for v in values if v != 0 and v is not None]
            return sum(non_zero_values) / len(non_zero_values) if non_zero_values else None

        avg_chunk_size = calc_avg_non_zero('chunk_size')
        avg_recall_size = calc_avg_non_zero('recall_size')
        avg_total_chars = calc_avg_non_zero('total_chars')
        avg_total_documents = calc_avg_non_zero('total_documents')
        avg_doc_length = calc_avg_non_zero('avg_doc_length')
        avg_sentence_length = calc_avg_non_zero('avg_sentence_length')
        avg_vocabulary_size = calc_avg_non_zero('vocabulary_size')
        avg_keyword_density = calc_avg_non_zero('keyword_density')
        avg_noun_ratio = calc_avg_non_zero('noun_ratio')
        avg_cosine_similarity = calc_avg_non_zero('avg_cosine_similarity')
        avg_diversity_score = calc_avg_non_zero('diversity_score')
        avg_text_complexity_score = calc_avg_non_zero('text_complexity_score')
        avg_recall_threshold = calc_avg_non_zero('recall_threshold')
        avg_paragraphs_length = calc_avg_non_zero('avg_paragraphs_length')
        avg_paragraphs = calc_avg_non_zero('avg_paragraphs')
        avg_estimate_density = calc_avg_non_zero('estimate_density')

        avg_entry = self.session.query(AigcKnowledgeIndividual).filter_by(knowledge_id=STATISTICS_ID).first()

        if avg_entry:
            avg_entry.chunk_size = avg_chunk_size
            avg_entry.recall_size = avg_recall_size
            avg_entry.total_chars = avg_total_chars
            avg_entry.total_documents = avg_total_documents
            avg_entry.avg_doc_length = avg_doc_length
            avg_entry.avg_sentence_length = avg_sentence_length
            avg_entry.vocabulary_size = avg_vocabulary_size
            avg_entry.keyword_density = avg_keyword_density
            avg_entry.noun_ratio = avg_noun_ratio
            avg_entry.avg_cosine_similarity = avg_cosine_similarity
            avg_entry.diversity_score = avg_diversity_score
            avg_entry.text_complexity_score = avg_text_complexity_score
            avg_entry.recall_threshold = avg_recall_threshold
            avg_entry.avg_paragraphs_length = avg_paragraphs_length
            avg_entry.avg_paragraphs = avg_paragraphs
            avg_entry.estimate_density = avg_estimate_density
        else:
            avg_entry = AigcKnowledgeIndividual(
                id=get_snowflake_id(),
                knowledge_id=STATISTICS_ID,
                knowledge_name='Average Values',
                strategy='average',
                chunk_size=avg_chunk_size,
                recall_size=avg_recall_size,
                total_chars=avg_total_chars,
                total_documents=avg_total_documents,
                avg_doc_length=avg_doc_length,
                avg_sentence_length=avg_sentence_length,
                vocabulary_size=avg_vocabulary_size,
                keyword_density=avg_keyword_density,
                noun_ratio=avg_noun_ratio,
                avg_cosine_similarity=avg_cosine_similarity,
                diversity_score=avg_diversity_score,
                text_complexity_score=avg_text_complexity_score,
                recall_threshold=avg_recall_threshold,
                avg_paragraphs_length=avg_paragraphs_length,
                avg_paragraphs=avg_paragraphs,
                estimate_density=avg_estimate_density
            )
            self.session.add(avg_entry)

        self.session.commit()

    def get_docs_name_list(self, doc_id_list):
        if not doc_id_list:
            return []

        docs_query = self.session.query(
            AigcKnowledgeDocument.document_id,
            AigcKnowledgeDocument.doc_name
        ).filter(
            AigcKnowledgeDocument.document_id.in_(doc_id_list),
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).all()

        result = []
        for doc in docs_query:
            result.append({
                'doc_id': str(doc.document_id),
                'doc_name': doc.doc_name
            })

        return result

    def get_docs_id_list(self, doc_name, limit=100):
        if not doc_name:
            return []

        docs_query = self.session.query(
            AigcKnowledgeDocument.document_id
        ).filter(
            AigcKnowledgeDocument.doc_name.like(f"%{doc_name}%"),
            AigcKnowledgeDocument.delete_flag == settings.DELETE_FLAG_FALSE
        ).limit(limit).all()

        result = [str(doc.document_id) for doc in docs_query]

        return result



class KnowledgeDocumentORM(BaseModel):
    def get_chunk_content_by_chunk_ids(self, chunk_ids):
        chunk_list = self.session.query(AigcKnowledgeDocumentChunk).filter(AigcKnowledgeDocumentChunk.chunk_id.in_(chunk_ids)).all()
        if chunk_list:
            return [{'chunk_id': str(obj.chunk_id), 'content': obj.content, 'doc_name': obj.doc.doc_name} for obj in chunk_list]
        return []

class LifeCycleRuleOrm(BaseModel):
    """ 生命周期规则处理逻辑 """

    def get_all_life_rule(self, kid=None, date_time=None):
        q = self.session.query(FileLifeRule)
        if kid:
            q = q.filter_by(knowledge_id=kid)
        if date_time:
            q = q.filter(or_(FileLifeRule.expire_date <= date_time, FileLifeRule.life_type == 'days'))
        q.order_by(FileLifeRule.file_scope.desc())
        return q.all()

    def add_life_rule(self, params):
        add_list = list()
        with self.session_scope() as session:
            for one in params:
                obj = FileLifeRule(**one)
                session.add(obj)
                session.commit()
                add_list.append(obj.life_rule_id)
        return add_list

    def del_life_rule(self, kid=None, rule_id=None):
        with self.session_scope() as session:
            if not (kid or rule_id):
                return
            if rule_id:
                q = session.query(FileLifeRule).filter_by(life_rule_id=rule_id)
            else:
                q = session.query(FileLifeRule).filter_by(knowledge_id=kid)
            q.delete(synchronize_session=False)
        return True

    def up_life_rule(self, rule_id, up_conf):
        with self.session_scope() as session:
            session.query(FileLifeRule).filter_by(life_rule_id=rule_id).update(up_conf)
        return True

    """ 范围规则处理逻辑 """

    def add_scope_rule(self, params):
        add_list = list()
        with self.session_scope() as session:
            for one in params:
                obj = FileScopeRule(**one)
                add_list.append(obj)
            if add_list:
                session.bulk_save_objects(add_list)
        return True

    def del_scope_rule(self, life_rule_id=None, kid=None):
        with self.session_scope() as session:
            q = session.query(FileScopeRule)
            if not (kid or life_rule_id):
                return
            if life_rule_id:
                q = q.filter_by(life_rule_id=life_rule_id)
            if kid:
                q = q.filter_by(knowledge_id=kid)
            q.delete(synchronize_session=False)
        return True

    def get_all_scope_rule(self, kid=None, life_rule_ids=None):
        q = self.session.query(FileScopeRule)
        if kid:
            q = q.filter_by(knowledge_id=kid)
        if life_rule_ids:
            q = q.filter(FileScopeRule.life_rule_id.in_(life_rule_ids))
        return q.all()


class FileClearRuleORM(BaseModel):

    def get_all_rel_clear_rule(self, kid, active_do=False):
        q = self.session.query(RelFileRule).join(FileClearRule, FileClearRule.id == RelFileRule.rule_id) \
            .filter(FileClearRule.delete_flag == settings.DELETE_FLAG_FALSE, RelFileRule.knowledge_id == kid)
        if active_do:
            q = q.filter(FileClearRule.status == 1)
        objs = q.order_by(RelFileRule.rel_id.desc()).all()
        ret = list()
        for obj in objs:
            rule = obj.rule
            m_dic = self.__base_analyse(rule)
            m_dic['rel_id'] = obj.rel_id
            m_dic['knowledge_id'] = obj.knowledge_id
            ret.append(m_dic)
        return ret

    def __base_analyse(self, obj):
        m_dic = dict(id=obj.id, name=obj.name, desc=obj.desc, content=obj.content, key_type=obj.type, source=obj.source,
                     status=obj.status, add_time=str(obj.add_time), tp_user_id=obj.tp_user_id)
        m_dic['tp_user_name'] = obj.tp_user.tp_user_name if m_dic['tp_user_id'] else ''
        return m_dic

    def get_page_rule(self, _type=None, source=None, status=None, name=None, ids=None, ps=10, po=1):
        q = self.session.query(FileClearRule).filter_by(delete_flag=0)
        if _type:
            q = q.filter_by(type=_type)
        if source:
            q = q.filter_by(source=source)
        if name:
            q = q.filter(FileClearRule.name.like(f'%{name}%'))
        if status is not None:
            q = q.filter_by(status=status)
        if ids:
            q = q.filter(FileClearRule.id.in_(ids))
        q.order_by(FileClearRule.add_time.desc())
        total = q.count()
        objs = q.slice((po - 1) * ps, ps * po).all()
        ret = list()
        for obj in objs:
            ret.append(self.__base_analyse(obj))
        return {'data_list': ret, 'total': total, 'page': po}

    def add_clear_rule(self, params):
        add_list = list()
        with self.session_scope() as session:
            for one in params:
                obj = FileClearRule(**one)
                session.add(obj)
                session.commit()
                add_list.append(obj.id)
        return add_list

    def up_clear_rule(self, rule_id, up_conf):
        with self.session_scope() as session:
            session.query(FileClearRule).filter_by(id=rule_id).update(up_conf)
        return True

    def add_rel_rule(self, kid_id, params):
        add_list = list()
        with self.session_scope() as session:
            for one in params:
                obj = RelFileRule(knowledge_id=kid_id, rule_id=one)
                add_list.append(obj)
            if add_list:
                session.bulk_save_objects(add_list)
        return True

    def del_rel_rule(self, kid_id):
        with self.session_scope() as session:
            session.query(RelFileRule).filter(RelFileRule.knowledge_id == kid_id) \
                .delete(synchronize_session=False)
            return True


class FileClearDataORM(BaseModel):

    def __my_dict(self, model):
        return {
            "rule_id": str(model.rule_id),
            "rule_name": model.rule_name,
            "rule_content": model.rule_content, 
            }
    
    def __table_dict(self, model):
        rule_dict = {0: "提示词", 1: "脚本"}
        doc_dict = {0: "文档", 1: "表格", 2: "图片"}
        return {
            "rule_id": str(model.rule_id),
            "rule_name": model.rule_name,
            "rule_content": model.rule_content,
            "rule_desc": model.rule_desc or "",
            "created_at": model.created_at,
            "rule_type": model.rule_type,
            "doc_type": model.doc_type,
            "is_builtin": model.is_builtin,
            "rule_type_name": rule_dict.get(model.rule_type, "未知类型"),
            "doc_type_name": doc_dict.get(model.doc_type, "未知类型"),
            }


    def add_file_rule(self, args):
        with self.session_scope() as session:
            rule_id = int(get_snowflake_id())
            args.update({"rule_id": rule_id})
            obj = DocClearRule(**args)
            session.add(obj)
            session.commit()
        return str(rule_id)

    def query_file_rule(self, rule_id):
        q = self.session.query(DocClearRule).filter_by(rule_id=rule_id, is_deleted=0, is_active=1).first()
        return q.rule_type, q.rule_content

    def get_all_file_rule(self, ):
        q = self.session.query(DocClearRule).filter_by(is_deleted=0, is_active=1,)
        is_builtin = q.filter_by(is_builtin=0)
        is_custom = q.filter_by(is_builtin=1, rule_type=1)
        is_builtin_doc = is_builtin.filter_by(doc_type=0).all()
        is_builtin_excel = is_builtin.filter_by(doc_type=1).all()
        is_custom_doc = is_custom.filter_by(doc_type=0).all()
        is_custom_excel = is_custom.filter_by(doc_type=1).all()
        is_builtin_doc_list = [self.__my_dict(model) for model in is_builtin_doc] if is_builtin_doc else []
        is_builtin_excel_list = [self.__my_dict(model) for model in is_builtin_excel] if is_builtin_excel else []
        is_custom_doc_list = [self.__my_dict(model) for model in is_custom_doc] if is_custom_doc else []
        is_custom_excel_list = [self.__my_dict(model) for model in is_custom_excel] if is_custom_excel else []
        res = {
            "builtin_list": {
                "doc": {"data": is_builtin_doc_list, "total": len(is_builtin_doc_list)},
                "excel": {"data": is_builtin_excel_list, "total": len(is_builtin_excel_list)}
            },
            "custom": {
                "doc": {"data": is_custom_doc_list, "total": len(is_custom_doc_list)},
                "excel": {"data": is_custom_excel_list, "total": len(is_custom_excel_list)}
            }
        }
        return res

    def get_file_rule_table(self, page, page_size):
        q = self.session.query(DocClearRule).filter_by(is_deleted=0, is_active=1,)
        count = q.count()
        objs = q.slice((int(page) - 1) * int(page_size), int(page_size) * int(page)).all()
        res = [self.__table_dict(model) for model in objs]
        return {"data": res, "total": count, "page": page, "page_size": page_size}

    def put_file_rule(self, rule_id, rule_content, rule_name, doc_type):

        self.session.query(DocClearRule).filter(DocClearRule.rule_id == int(rule_id)).update({
            "rule_content":rule_content, 
            "rule_name":rule_name,
            "doc_type":doc_type
        })
        self.session.commit()
        return 'ok'

    def delete_file_rule(self, rule_id):
        self.session.query(DocClearRule).filter(DocClearRule.rule_id == int(rule_id)).update({'is_deleted': "1"})
        self.session.commit()
        return 'ok'

        
class FileClearRelationORM(BaseModel):

    def add_file_relation_rule(self, document_id, params):
        id = int(get_snowflake_id())
        add_list = list()
        with self.session_scope() as session:
            for one in params:
                id = int(get_snowflake_id())
                obj = DocClearRelation(id=id, document_id=document_id, rule_id=one)
                add_list.append(obj)
            if add_list:
                session.bulk_save_objects(add_list)
        return True

    def get_file_relation_rule(self, document_id):

        # 进行连接查询
        q = self.session.query(
            DocClearRelation.rule_id,
            DocClearRule.rule_name,
            DocClearRule.rule_content,
            DocClearRule.rule_type,
            DocClearRule.is_builtin,
            DocClearRule.doc_type,
        ).join(
            DocClearRule,  # 连接 DocClearRule 模型
            DocClearRelation.rule_id == DocClearRule.rule_id  # 假设 rule_id 是连接两个模型的外键
        ).filter(
            DocClearRelation.document_id == document_id  # 应用筛选条件
        )

        # 迭代查询结果
        results = q.all()  # 这将返回所有匹配的行作为一个列表，每个行是一个包含所选字段的元组

        # 如果你想要一个字典列表，你可以这样做：
        result_dict_list = [
            {
                'rule_id': str(row.rule_id),
                'rule_name': row.rule_name,
                'rule_content': row.rule_content,
                'rule_type': row.rule_type,
                'is_builtin': row.is_builtin,
                'doc_type': row.doc_type,
            }
            for row in results
        ]

        return result_dict_list
    
class FileKnowledgeClearRelationORM(BaseModel):

    def add_knowledge_relation_rule(self, knowledge_id, params):
        id = int(get_snowflake_id())
        add_list = list()
        with self.session_scope() as session:
            for one in params:
                id = int(get_snowflake_id())
                obj = KnowledgeClearRelation(id=id, knowledge_id=knowledge_id, rule_id=one)
                add_list.append(obj)
            if add_list:
                session.bulk_save_objects(add_list)
        return True

    def put_knowledge_relation_rule(self, knowledge_id, sql_rule_ids, rule_ids):
        # 增
        create_list = list()
        create_list = list(set(rule_ids) - set(sql_rule_ids))
        # 删
        delete_list = list()
        delete_list = list(set(sql_rule_ids) - set(rule_ids))
        if create_list:
            self.add_knowledge_relation_rule(knowledge_id, create_list)
        if delete_list:
            self.delete_knowledge_relation_rule_by_rule_id(knowledge_id, delete_list)
        return 'ok'

    def get_knowledge_relation_rule(self, knowledge_id):

        # 进行连接查询
        q = self.session.query(
            KnowledgeClearRelation.id,
            KnowledgeClearRelation.rule_id,
            DocClearRule.rule_name,
            DocClearRule.rule_content,
            DocClearRule.rule_type,
            DocClearRule.is_builtin,
            DocClearRule.doc_type,
        ).join(
            KnowledgeClearRelation,  # 连接 DocClearRule 模型
            KnowledgeClearRelation.rule_id == DocClearRule.rule_id  # 假设 rule_id 是连接两个模型的外键
        ).filter(
            KnowledgeClearRelation.knowledge_id == knowledge_id  # 应用筛选条件
        )

        # 迭代查询结果
        results = q.all()  # 这将返回所有匹配的行作为一个列表，每个行是一个包含所选字段的元组
        logger.info(results)
        # 如果你想要一个字典列表，你可以这样做：
        result_dict_list = [
            {
                'id': str(row.id),
                'rule_id': str(row.rule_id),
                'rule_name': row.rule_name,
                'rule_content': row.rule_content,
                'rule_type': row.rule_type,
                'is_builtin': row.is_builtin,
                'doc_type': row.doc_type,
            }
            for row in results
        ]

        # 初始化分组字典
        grouped_results = {
            "builtin_list": {
                "doc": {"data": [], "total": 0},
                "excel": {"data": [], "total": 0}
            },
            "custom": {
                "doc": {"data": [], "total": 0},
                "excel": {"data": [], "total": 0}
            }
        }

        # 遍历 result_dict_list 进行分组
        for item in result_dict_list:
            is_builtin_key = "builtin_list" if item['is_builtin'] == 0 else "custom"
            doc_type_key = "doc" if item['doc_type'] == 0 else "excel"
            grouped_results[is_builtin_key][doc_type_key]["data"].append(item)

        # 更新 total 字段
        for is_builtin_key in grouped_results:
            for doc_type_key in grouped_results[is_builtin_key]:
                grouped_results[is_builtin_key][doc_type_key]["total"] = len(grouped_results[is_builtin_key][doc_type_key]["data"])

        # 输出结果
        res = grouped_results
        return res

    def delete_knowledge_relation_rule_by_rule_id(self, knowledge_id, rule_id):
        for id in rule_id:
            self.session.query(KnowledgeClearRelation).filter(KnowledgeClearRelation.knowledge_id == knowledge_id, KnowledgeClearRelation.rule_id == id).delete()
        self.session.commit()
        return 'ok'

    def delete_knowledge_relation_rule(self, ids):
        for id in ids:
            self.session.query(KnowledgeClearRelation).filter(KnowledgeClearRelation.id == id).delete()
        self.session.commit()
        return 'ok'

class DocumentClearRuleOrm(BaseModel):

    def get_rule_type(self, rule_id):
        with self.session_scope() as session:
            res = session.query(DocClearRule).filter_by(rule_id=rule_id, is_deleted=0, is_active=1).first()
            res = res.rule_type
            return res

    def get_rule_content(self, rule_id):
        with self.session_scope() as session:
            res = session.query(DocClearRule).filter_by(rule_id=rule_id, is_deleted=0, is_active=1).first()
            res = res.rule_content
            return res

if __name__ == '__main__':
    res = KnowledgeOrm('wwf556ee1bcfa5d9d6').get_knowledge_ids_by_model()
