# -*- coding: utf-8 -*-
import uuid
import datetime
import settings
from module import BaseModel
from module.mysql.models import *
from sqlalchemy import or_
from lib_func.logger import logger


# 用户
class TPUserM(BaseModel):

    def __base_analyse_user_info(self, obj):
        alias = obj.tp_user_alias
        # 部门地址优先于公司地址
        company_info = DepartmentM(self.corpid).get_organization()
        company_info['address'] = obj.dp.address or company_info['address']
        user_info = {
            'tp_user_id': obj.tp_user_id,
            'dp_id': obj.dp_id,
            'dp_name': obj.dp.department_name,
            'ww_user_id': obj.ww_user_id,
            'tp_user_name': obj.tp_user_name + '({})'.format(alias) if alias else obj.tp_user_name,
            'alias_name': alias or '',
            'user_name': obj.tp_user_name or '',
            'avatar': obj.avatar,
            'thumb_avatar': obj.thumb_avatar,
            'character_setting': obj.character_setting or '',
            'company_name': company_info,
            'phone_number': obj.phone_number,
            'email': obj.email,
            'position': obj.dp.department_name,
            'seat_flag': obj.seat_flag,
            'qrcode': obj.personal_qrcode
        }
        return user_info

    # 用户信息详情
    def user_info(self, user_id, delete_flag=None):
        q = self.session.query(TouchpointUser).filter(or_(TouchpointUser.tp_user_id == user_id,
                                                          TouchpointUser.ww_user_id == user_id))
        if delete_flag:
            q = q.filter_by(delete_flag=delete_flag)
        obj = q.first()
        if obj:
            return self.__base_analyse_user_info(obj)
        else:
            return {}

    def add_user_info(self, user_info):
        with self.session_scope() as session:
            ww_user_id = user_info.get('ww_user_id')
            obj = session.query(TouchpointUser).filter_by(ww_user_id=ww_user_id).first()
            if not obj:
                obj = TouchpointUser(**user_info)
                session.add(obj)
            else:
                self.up_user_info(None, user_info, obj)
        return True

    def bulk_add_user_info(self, data_list):
        add_list = list()
        for one in data_list:
            add_list.append(TouchpointUser(**one))
        with self.session_scope() as session:
            session.bulk_save_objects(add_list)
        return True

    def up_user_info(self, user_id, up_conf, _obj=None):
        with self.session_scope() as session:
            obj = _obj or session.query(TouchpointUser).filter(TouchpointUser.id.in_(user_id)).update({'delete_flag': settings.DELETE_FLAG_TRUE})
            for _key, _value in up_conf.items():
                if hasattr(obj, _key):
                    setattr(obj, _key, _value)
                obj.modify_time = datetime.datetime.now()
        return True

    def page_user_info(self, key_word, po, ps):
        q = self.session.query(TouchpointUser).filter_by(delete_flag=settings.DELETE_FLAG_FALSE)
        if key_word:
            q = q.filter(or_(TouchpointUser.tp_user_name.like(f'%{key_word}%'),
                             TouchpointUser.tp_user_name.like(f'%{key_word}%')))
        total = q.count()
        objs = q.slice((po - 1) * ps, ps * po).all()
        ret = list()
        for obj in objs:
            ret.append({
                'tp_user_id': obj.tp_user_id,
                'user_name': obj.tp_user_name,
                'phone_number': obj.phone_number,
                'code_id': obj.ww_user_id,
                'status': obj.status,
                'dp_id': obj.dp_id,
                'dp_name': obj.dp.department_name
            })
        return {'data_list': ret, 'page': po, 'total': total}


# 部门
class DepartmentM(BaseModel):

    # 向上部门关联列表
    def get_up_rel_dps(self, dpt_id):
        ret = list()
        def inner(dpt_id):
            obj = self.session.query(Department).filter_by(department_id=dpt_id).first()
            if obj:
                ret.append({
                    'dpt_name': obj.department_name,
                    'dpt_id': obj.department_id,
                })
                if obj.parent_dp_id:
                    inner(obj.parent_dp_id)

        inner(dpt_id)
        return ret

    # 向下部门关联列表
    def get_down_rel_dps(self, dpt_id):
        ret = list()
        def inner(dpt_id):
            objs = self.session.query(Department).filter_by(parent_dp_id=dpt_id).all()
            for obj in objs:
                ret.append({
                    'dpt_name': obj.department_name,
                    'dpt_id': obj.department_id,
                })
                inner(obj.department_id)

        inner(dpt_id)
        return ret

    # 获取公司组织信息
    def get_organization(self):
        ret = {'org_id': '', 'sale_version_id': '', 'sale_seat_num': 0, 'org_name': '未知', 'sale_version_name': '',
               'logo_url': '', 'slogan': '', 'address': '', 'email': '', 'phone': ''}
        obj = self.session.query(Organization).first()
        if obj:
            ret['org_id'] = obj.org_id
            ret['sale_version_id'] = obj.sale_version_id
            ret['sale_seat_num'] = obj.sale_seat_num
            ret['org_name'] = obj.org_name
            ret['sale_version_name'] = obj.sale_version.sale_version_name
            ret['logo_url'] = obj.logo_url
            ret['slogan'] = obj.slogan
            ret['address'] = obj.address
            ret['email'] = obj.email
            ret['phone'] = obj.phone
        return ret

    def bulk_add_dpt_info(self, data_list):
        add_list = list()
        for one in data_list:
            add_list.append(Department(**one))
        with self.session_scope() as session:
            session.bulk_save_objects(add_list)
        return True

    def add_dpt_info(self, dpt_info):
        with self.session_scope() as session:
            obj = session.query(Department).filter_by(department_id=dpt_info.get('department_id')).first()
            if not obj:
                obj = Department(**dpt_info)
                session.add(obj)
            else:
                self.up_dpf_info(None, dpt_info, obj)
        return True

    def up_dpf_info(self, dpt_id, up_conf, _obj=None):
        with self.session_scope() as session:
            obj = _obj or session.query(Department).filter_by(department_id=dpt_id).first()
            for _key, _value in up_conf.items():
                if hasattr(obj, _key):
                    setattr(obj, _key, _value)
        return True

    def page_dept_info(self, key_word, po, ps):
        q = self.session.query(Department)
        if key_word:
            q = q.filter(Department.department_name.like(f'%{key_word}%'))
        total = q.count()
        objs = q.slice((po - 1) * ps, ps * po).all()
        ret = list()
        for obj in objs:
            ret.append({
                'department_id': obj.department_id,
                'parent_dp_id': obj.parent_dp_id,
                'department_name': obj.department_name
            })
        return {'data_list': ret, 'page': po, 'total': total}


# 角色
class RoleM(BaseModel):

    # 获取所有用户的角色IDS
    def get_all_user_role_ids(self, user_id):
        objs = self.session.query(RelTpuRole).filter_by(tpu_id=user_id, valid_flag='1').all()
        ret = list()
        for one in objs:
            ret.append(one.role_id)
        # logger.info(f'role_ids: {ret}')
        for item in self.session.query(RelRoleAuth).filter(
                RelRoleAuth.role_id.in_(ret), RelRoleAuth.valid_flag == 1).all():
            ret.append(item.auth_id)
        # logger.info(f'role_ids + auth_ids: {ret}')
        return list(set(ret))

    def get_user_auth_ids(self, user_id):
        objs = self.session.query(RelRoleAuth).join(RelTpuRole, RelTpuRole.role_id == RelRoleAuth.role_id) \
            .filter(RelTpuRole.tpu_id == user_id, RelTpuRole.valid_flag == '1') \
            .filter(RelRoleAuth.valid_flag == '1').all()
        ret = list({one.auth_id for one in objs})
        return ret


class SchemeM(BaseModel):

    # 向上部门权限关联列表
    def get_scheme_up_rel_dps(self, dpt_id):
        dpt_id_list = [x['dpt_id'] for x in DepartmentM(self.corpid).get_up_rel_dps(dpt_id)]
        ret = list()
        if dpt_id_list:
            objs = self.session.query(RelAuthSchemeDepart).filter(RelAuthSchemeDepart.depart_id.in_(dpt_id_list)).all()
            for obj in objs:
                ret.append({
                    'scheme_id': obj.scheme_id,
                    'scheme_name': obj.scheme.scheme_name,
                })
        return ret

    # 向下部门权限关联列表
    def get_scheme_down_rel_dps(self, dpt_id):
        ret = list()
        dpt_id_list = [x['dpt_id'] for x in DepartmentM(self.corpid).get_down_rel_dps(dpt_id)]
        if dpt_id_list:
            objs = self.session.query(RelAuthSchemeDepart).filter(RelAuthSchemeDepart.depart_id.in_(dpt_id_list)).all()
            for obj in objs:
                ret.append({
                    'dpt_name': obj.depart.department_name,
                    'dpt_id': obj.depart_id,
                    'scheme_id': obj.scheme_id,
                    'scheme_name': obj.scheme.scheme_name,
                })
        return ret

    # 获取用户的权限方案列表
    def user_scheme_by_uid(self, user_id):
        objs = self.session.query(RelAuthSchemeUser).filter_by(tp_user_id=user_id).all()
        ret = list()
        for one in objs:
            ret.append({
                'scheme_name': one.scheme.scheme_name,
                'scheme_id': one.scheme_id
            })
        return ret

    # 获取用户对应角色的权限方案列表
    def role_scheme_by_uid(self, user_id):
        objs = self.session.query(RelAuthSchemeRole).join(RelTpuRole, RelAuthSchemeRole.role_id == RelTpuRole.role_id) \
            .filter(RelTpuRole.tpu_id == user_id).all()
        ret = list()
        for one in objs:
            ret.append({
                'scheme_name': one.scheme.scheme_name,
                'scheme_id': one.scheme_id
            })
        return ret

    # 获取用户所在部门的权限方案列表
    def dpt_scheme_by_uid(self, user_id):
        obj = self.session.query(TouchpointUser).filter_by(tp_user_id=user_id).first()
        if obj:
            return self.get_scheme_up_rel_dps(obj.dp_id)
        else:
            return []

    # 获取角色的权限方案列表
    def get_scheme_rel_role_list(self, role_id):
        ret = list()
        objs = self.session.query(RelAuthSchemeRole).filter_by(role_id=role_id).all()
        for obj in objs:
            ret.append({
                'scheme_id': obj.scheme_id,
                'scheme_name': obj.scheme.scheme_name,
            })
        return ret

# 用户操作日志
class UserLogM(BaseModel):

    # 获取用户操作日志
    def get_user_do_log(self, key_word, page_size, page_no):
        ret = list()
        q = self.session.query(OperationLog).order_by(OperationLog.operation_time.desc())
        if key_word:
            q = q.filter(OperationLog.operation_content.like("%{}%".format(key_word))).order_by(
                OperationLog.operation_time.desc())
        total = q.count()
        objs = q.slice((page_no - 1) * page_size, page_size * page_no).all()
        for obj in objs:
            try:
                obj.tp_user.tp_user_name
            except:
                logger.error('{} now'.format(obj.tp_user_id))

            ret.append({
                'logs_id': obj.operation_logs_id,
                'view_id': obj.funciton_view_id,
                'view_name': obj.funciton_view.view_name,
                'tp_user_id': obj.tp_user_id,
                'user_name': obj.tp_user.tp_user_name if obj.tp_user_id else '',
                'operation_time': obj.operation_time,
                'operation_content': obj.operation_content,
                'function_content': obj.function_content
            })
        return {'data_list': ret, 'page': page_no, 'total': total}

    # 记录用户操作
    def add_user_do_log(self, view_code, log_info, user_id):
        view_id = self.session.query(FunctionView).filter_by(view_code=view_code).first().function_view_id
        with self.session_scope() as session:
            log_obj = OperationLog(operation_logs_id=str(uuid.uuid4()), funciton_view_id=view_id,
                                   operation_content=log_info, function_content='',
                                   operation_time=datetime.datetime.now())
            if user_id:
                log_obj.tp_user_id = user_id
            session.add(log_obj)
        return True

