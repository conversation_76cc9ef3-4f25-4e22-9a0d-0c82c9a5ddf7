# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: train_orm.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 5月 29, 2024
# ---
import os
import sys

# project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__name__))))
# sys.path.append(project_root)
import settings
import json
import datetime
from module import BaseModel
from module.mysql.models import *
from sqlalchemy import func, or_, and_, select
from utils.tools import get_snowflake_id, convert_seconds
from sqlalchemy.dialects import postgresql


class TrainOrm(BaseModel):

    def get_base_model_list(self):
        objs = self.session.query(FBaseModel).filter_by(status=1, delete_flag=settings.DELETE_FLAG_FALSE).all()
        data_list = []
        for obj in objs:
            data_list.append({
                'base_model_id': obj.base_model_id,
                'base_model_name': obj.base_model_name,
                'ms': obj.ms,
                'hf': obj.hf,
                'name': obj.base_model_name,
                'value': obj.base_model_id,
                'rel_data': {
                    'template': obj.template,
                    'model_name_or_path': obj.model_name_or_path,
                    'lora_target': obj.lora_target
                }
            })
        return data_list

    def get_prompt_template_list(self):

        objs = self.session.query(FPromptTemplate).filter_by(delete_flag=settings.DELETE_FLAG_FALSE).all()
        data_list = []
        for obj in objs:
            data_list.append({
                'prompt_template_id': obj.template_id,
                'prompt_template_name': obj.template,
                'name': obj.template,
                'value': obj.template
            })
        return data_list

    def get_params_type_step_list(self):
        objs = self.session.query(FParamsTypeStep).filter_by(delete_flag=settings.DELETE_FLAG_FALSE).order_by(FParamsTypeStep.sort).all()
        data_list = []
        for obj in objs:
            data_list.append({
                'step_id': obj.step_id,
                'step_name': obj.step_name
            })
        return data_list

    def get_params_type_list(self, step_id):
        objs = self.session.query(FParamsType).filter_by(step_id=step_id, delete_flag=settings.DELETE_FLAG_FALSE).all()
        data_list = []
        for obj in objs:
            data_list.append({
                'param_type_id': obj.param_type_id,
                'param_type_name': obj.param_type_name
            })
        return data_list

    def get_params_list(self, param_type_id=None):
        q = self.session.query(FParam).filter_by(delete_flag=settings.DELETE_FLAG_FALSE).order_by(FParam.sort)
        if param_type_id:
            q = q.filter(FParam.param_type_id == param_type_id)
        objs = q.all()
        data_list = []
        for obj in objs:
            if obj.allowed_value:
                allowed_value = json.loads(obj.allowed_value)
            else:
                allowed_value = None

            if obj.value_type == 'number':
                if allowed_value.get('precision') == 0:
                    default_value = int(obj.default_value)
                else:
                    default_value = float(obj.default_value)
            else:
                default_value = obj.default_value

            data_list.append({
                'param_id': obj.param_id,
                'param_name': obj.param_name,
                'param_description': obj.param_description,
                'param_code': obj.param_code,
                'father_param_code': obj.father_param_code,
                'subsidiary_param_code': obj.subsidiary_param_code,
                'default_value': default_value,
                'value_type': obj.value_type,
                'allowed_value': allowed_value,
                'is_required': obj.is_required,
                'is_factory': obj.is_factory,
                'api_url': obj.api_url,
                'is_rel_data': obj.is_rel_data,
                'placeholder': obj.placeholder
            })
        return data_list

    def get_train_request_list(self, args):

        q = self.session.query(FTrainRequest).filter(FTrainRequest.delete_flag == settings.DELETE_FLAG_FALSE)

        q = q.order_by(FTrainRequest.add_time.desc())
        # logger.info(q.statement.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True}))
        if args.get('model_name'):
            q = q.filter(FTrainRequest.model_name.like('%' + args.get('model_name') + '%'))
        if args.get("base_model_id"):
            q = q.filter(FTrainRequest.base_model_id == args.get("base_model_id"))
        if args.get("status"):
            q = q.filter(FTrainRequest.status == args.get("status"))
        if args.get("status_list"):
            q = q.filter(FTrainRequest.status.in_(args.get("status_list")))

        if args.get("is_urgency"):
            q = q.filter(FTrainRequest.is_urgency == args.get("is_urgency"))
        if args.get("is_publish"):
            q = q.filter(FTrainRequest.is_publish == args.get("is_publish"))

        if args.get('is_all') != 'all':
            count = q.count()

            objs = q.slice((int(args['page_no']) - 1) * int(args['page_size']), int(args['page_size']) * int(args['page_no'])).all()
        else:
            objs = q.all()
            count = len(objs)
        res = []
        for obj in objs:
            # python 用 datetime 获取当前时间戳

            if obj.request_time is None:
                wait_times = 0
            elif obj.request_time and obj.status == 4:
                wait_times = int(obj.success_time.timestamp()) - int(obj.request_time.timestamp())
            else:
                wait_times = int(datetime.datetime.now().timestamp()) - int(obj.request_time.timestamp())
            queue_info = self.get_queue_detail(None, obj.train_request_id, None)
            if queue_info:
                queue_id = str(queue_info.get('location'))
            else:
                queue_id = ''

            res.append(
                {
                    'train_request_id': str(obj.train_request_id),
                    'model_name': obj.model_name,
                    'model_description': obj.model_description,
                    'status': obj.status,
                    'add_time': obj.add_time,
                    'is_urgency': obj.is_urgency,
                    'model_icon': obj.model_icon,
                    'is_publish': obj.is_publish,
                    'base_model_name': obj.base_model.base_model_name,
                    'tp_user_name': obj.tp_user.tp_user_name,
                    'queue_id': queue_id,
                    'train_times': convert_seconds(obj.elapsed_times),
                    'wait_times': convert_seconds(wait_times),
                    'train_progress': obj.percentage,
                    'process_id': obj.process_id,
                    'publish_model_path': obj.publish_model_path,
                    'publish_download_url': obj.publish_download_url
                })
        return {'total': count, 'po': args['page_no'], 'data_list': res}

    def get_train_request_detail(self, train_request_id):
        obj = self.session.query(FTrainRequest).filter_by(train_request_id=train_request_id, delete_flag=settings.DELETE_FLAG_FALSE).first()

        return {
            'train_request_id': str(obj.train_request_id),
            'tp_user_id': str(obj.tp_user_id),
            'model_name': obj.model_name,
            'model_description': obj.model_description,
            'status': obj.status,
            'base_model_id': obj.base_model_id,
            'is_urgency': str(obj.is_urgency),
            'model_icon': obj.model_icon,
            'is_publish': obj.is_publish,
            'base_model_name': obj.base_model.base_model_name,
        }
        
    def get_train_request_detail_to_base_model_ids(self, train_request_id):
        obj = self.session.query(FTrainRequest).filter_by(train_request_id=train_request_id, delete_flag=settings.DELETE_FLAG_FALSE).first()
        
        final_base_model_ids = [obj.base_model.base_model_type_id, obj.base_model_id]

        return {
            'train_request_id': str(obj.train_request_id),
            'model_name': obj.model_name,
            'model_description': obj.model_description,
            'status': obj.status,
            # 'base_model_id': obj.base_model_id,
            'base_model_id': final_base_model_ids,
            'is_urgency': str(obj.is_urgency),
            'model_icon': obj.model_icon,
            'is_publish': obj.is_publish,
            'base_model_name': obj.base_model.base_model_name,
        }

    def get_train_request_params_list(self, train_request_id):
        objs = self.session.query(FTrainRequestParam, FParam).outerjoin(FParam,
                                                                        FParam.param_code == FTrainRequestParam.param_code).filter(
            FTrainRequestParam.train_request_id == train_request_id, FTrainRequestParam.delete_flag == settings.DELETE_FLAG_FALSE).all()
        data = dict()
        for obj in objs:
            if obj.FParam and obj.FParam.param_code == 'dataset':
                dataset_names = obj.FTrainRequestParam.param_value.split(',') if obj.FTrainRequestParam.param_value else []
                param_value = self.get_datasets_list_by_name(dataset_names)
            elif obj.FParam and obj.FParam.param_type == 'number':
                allowed_value = json.loads(obj.FParam.allowed_value)
                if allowed_value['precision'] == 0:
                    param_value = int(obj.FTrainRequestParam.param_value)
                else:
                    param_value = float(obj.FTrainRequestParam.param_value)

            else:
                param_value = obj.FTrainRequestParam.param_value
            data.update({obj.FTrainRequestParam.param_code: param_value})
        # 特殊处理
        if data.get('use_unsloth') == 'True':
            data.update({'flash_attn': 'unsloth'})
        return data

    def get_base_model_detail(self, base_model_id):
        obj = self.session.query(FBaseModel).filter_by(base_model_id=base_model_id, delete_flag=settings.DELETE_FLAG_FALSE).first()
        data = {}
        if obj:
            data = {
                'base_model_id': obj.base_model_id,
                'base_model_type_id': obj.base_model_type_id,
                'base_model_name': obj.base_model_name,
                'ms': obj.ms,
                'hf': obj.hf,
                'name': obj.base_model_name,
                'value': obj.base_model_id,
                'template': obj.template,
                # 'model_name_or_path': obj.model_name_or_path
            }
        return data

    def add_train_request(self, args):

        train_request_id = get_snowflake_id()

        obj = FTrainRequest(
            train_request_id=train_request_id,
            model_name=args.get('model_name'),
            model_description=args.get('model_description'),
            base_model_id=args.get('base_model_id'),
            status=args.get('status'),
            is_urgency=args.get('is_urgency'),
            tp_user_id=args.get('tp_user_id'),
            model_icon=args.get('model_icon')
        )
        if args.get('request_time'):
            obj.request_time = args.get('request_time')

        self.session.add(obj)
        self.session.commit()
        return train_request_id

    def update_train_request(self, train_request_id, args):
        obj = self.session.query(FTrainRequest).filter_by(train_request_id=train_request_id).first()
        if obj:
            obj.model_name = args.get('model_name')
            obj.model_description = args.get('model_description')
            obj.base_model_id = args.get('base_model_id')
            obj.status = args.get('status')
            obj.is_urgency = args.get('is_urgency')
            obj.tp_user_id = args.get('tp_user_id')
            obj.model_icon = args.get('model_icon')
            obj.elapsed_times = args.get('elapsed_times')
            obj.remaining_times = args.get('remaining_times')
            obj.percentage = args.get('percentage')
            obj.process_id = args.get('process_id')
            obj.resource = args.get('resource')
            obj.request_time = args.get('request_time')

            self.session.commit()
        return train_request_id

    def add_train_request_params(self, train_request_id, param_code, param_value):

        train_param_id = get_snowflake_id()
        obj = FTrainRequestParam(
            train_param_id=train_param_id,
            train_request_id=train_request_id,
            param_code=param_code,
            param_value=param_value
        )
        self.session.add(obj)
        self.session.commit()
        return train_param_id

    def del_train_request_params(self, train_request_id):
        self.session.query(FTrainRequestParam).filter_by(train_request_id=train_request_id).update({"delete_flag": settings.DELETE_FLAG_TRUE})
        self.session.commit()
        return train_request_id

    def del_train_request(self, train_request_id):
        self.session.query(FTrainRequest).filter_by(train_request_id=train_request_id).update({"delete_flag": settings.DELETE_FLAG_TRUE})
        self.session.commit()
        return train_request_id

    def add_params_config(self, param_config_name):

        obj = FParamsConfig(param_config_name=param_config_name)
        self.session.add(obj)
        self.session.flush()
        self.session.commit()
        return obj.param_config_id

    def add_params_config_detail(self, param_config_id, param_code, param_value):

        obj = FParamsConfigDetail(
            param_config_id=param_config_id,
            param_code=param_code,
            param_value=param_value
        )
        self.session.add(obj)
        self.session.flush()
        self.session.commit()
        return obj.param_config_detail_id

    def get_params_config_list(self):
        objs = self.session.query(FParamsConfig).filter_by(delete_flag=settings.DELETE_FLAG_FALSE).order_by(FParamsConfig.add_time.desc()).all()

        data_list = []
        for obj in objs:
            data_list.append({
                'param_config_id': obj.param_config_id,
                'param_config_name': obj.param_config_name,
                'name': obj.param_config_name,
                'value': obj.param_config_id
            })
        return data_list

    def get_params_config_detail(self, param_config_id):
        objs = self.session.query(FParamsConfigDetail, FParam).outerjoin(FParam,
                                                                         FParam.param_code == FParamsConfigDetail.param_code).filter(
            FParamsConfigDetail.param_config_id == param_config_id, FParamsConfigDetail.delete_flag == settings.DELETE_FLAG_FALSE).all()
        data = dict()
        data['param_config_id'] = int(param_config_id)
        for obj in objs:
            if obj.FParam and obj.FParam.param_code == 'dataset':
                dataset_names = obj.FParamsConfigDetail.param_value.split(',') if obj.FParamsConfigDetail.param_value else []
                param_value = self.get_datasets_list_by_name(dataset_names)
            elif obj.FParam and obj.FParam.param_code == 'is_urgency':
                param_value = int(obj.FParamsConfigDetail.param_value)
            elif obj.FParam and obj.FParam.param_type == 'number':
                allowed_value = json.loads(obj.FParam.allowed_value)
                if allowed_value['precision'] == 0:
                    param_value = int(obj.FParamsConfigDetail.param_value)
                else:
                    param_value = float(obj.FParamsConfigDetail.param_value)

            else:
                param_value = obj.FParamsConfigDetail.param_value
            data[obj.FParamsConfigDetail.param_code] = param_value
        data['is_urgency'] = str(data.get('is_urgency', '0'))
        data['train_param_config_id'] = int(data.get('train_param_config_id')) if data.get('train_param_config_id') else None
        base_model_id = data['base_model_id']
        model_detail_map = self.get_base_model_detail(base_model_id)
        base_model_type_id = model_detail_map['base_model_type_id']
        data['base_model_id'] = [base_model_type_id, base_model_id]
        return data

    def get_datasets_list_by_name(self, dataset_names):
        objs = self.session.query(FData).filter(FData.register_dataset_name.in_(dataset_names), FData.delete_flag == settings.DELETE_FLAG_FALSE).all()
        datasets = [obj.data_id + "-" + obj.register_dataset_name for obj in objs]
        if not datasets:
            return ''
        return ','.join(datasets)

    def get_train_request_params_detail(self, train_request_id):
        objs = self.session.query(FTrainRequestParam).filter_by(train_request_id=train_request_id, delete_flag=settings.DELETE_FLAG_FALSE).all()
        data = dict()
        for obj in objs:
            data[obj.param_code] = obj.param_value
        return data

    def update_base_model(self, base_model_id, args):
        self.session.query(FBaseModel).filter_by(base_model_id=base_model_id).update(args)
        self.session.commit()
        return base_model_id

    def update_train_request_by_id(self, train_request_id, args):
        self.session.query(FTrainRequest).filter_by(train_request_id=train_request_id).update(args)
        self.session.commit()
        return train_request_id

    def get_train_request_queue(self):
        obj = self.session.query(func.max(FTrainRequest.queue_id).label('queue_id')).first()
        if obj:
            return obj.queue_id + 1
        else:
            return 1

    def get_train_request_wait(self, sort='default', res=''):

        q = self.session.query(FQueue.queue_id, FQueue.location, FQueue.add_time, FQueue.queue_type, FQueue.train_request_id,
                               FQueue.evaluate_id,
                               func.if_(FQueue.queue_type == 1, FTrainRequest.is_urgency, FModelEvaluate.is_urgency).label('is_urgency'),
                               func.if_(FQueue.queue_type == 1, FTrainRequest.status, FModelEvaluate.status).label('status')).outerjoin(
            FTrainRequest, and_(FQueue.train_request_id == FTrainRequest.train_request_id, FTrainRequest.delete_flag == settings.DELETE_FLAG_FALSE)).outerjoin(
            FModelEvaluate, and_(FModelEvaluate.evaluate_id == FQueue.evaluate_id, FModelEvaluate.delete_flag == settings.DELETE_FLAG_FALSE)).filter(FQueue.delete_flag == settings.DELETE_FLAG_FALSE)
        q = q.filter(or_(FTrainRequest.status == 2, FModelEvaluate.status == 2))

        # if is_queue is True:
        #     q = q.filter(FQueue.is_edit == 0)
        subquery = q.subquery()
        query = self.session.query(subquery.c.queue_id, subquery.c.location, subquery.c.add_time, subquery.c.is_urgency, subquery.c.status,
                                   subquery.c.queue_type, subquery.c.train_request_id, subquery.c.evaluate_id)
        if sort == 'location':
            query = query.order_by(subquery.c.location)
        else:
            query = query.order_by(subquery.c.is_urgency.desc(), subquery.c.add_time.asc())

        objs = query.all()
        logger.info(query.statement.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True}))
        if res == 'list_dict':
            data_list = list()
            for obj in objs:
                data_list.append({
                    'queue_id': obj.queue_id,
                    'location': obj.location,
                    'evaluate_id': obj.evaluate_id,
                    'train_request_id': obj.train_request_id,
                    'queue_type': obj.queue_type,
                    'is_urgency': obj.is_urgency,
                    'status': obj.status
                })
            return data_list
        else:
            queue_ids = []
            location_ids = []
            for obj in objs:
                queue_ids.append(obj.queue_id)
                location_ids.append(obj.location)
            return list(zip(queue_ids, location_ids))

    def add_queue(self, queue_type, train_request_id, evaluate_id):
        obj = FQueue(queue_type=queue_type)
        if train_request_id:
            obj.train_request_id = train_request_id
        if evaluate_id:
            obj.evaluate_id = evaluate_id
        self.session.add(obj)
        self.session.flush()
        self.session.commit()

        obj_info = self.session.query(FQueue).filter_by(queue_id=obj.queue_id).first()
        if obj_info:
            obj_info.location = obj.queue_id
            self.session.commit()

        return obj.queue_id

    def del_queue(self, train_request_id, evaluate_id):
        q = self.session.query(FQueue).filter(FQueue.train_request_id == train_request_id)
        if evaluate_id is None:
            q = q.filter(FQueue.evaluate_id.is_(None))
        else:
            q = q.filter(FQueue.evaluate_id == evaluate_id)
        q.update({'delete_flag': 1})
        self.session.commit()
        return True

    def update_queue(self, queue_id, args):
        self.session.query(FQueue).filter_by(queue_id=queue_id).update(args)
        self.session.commit()
        return queue_id

    def get_queue_list(self, args):

        status = [2, 3]
        q = self.session.query(FQueue.queue_id, FQueue.location, FQueue.train_request_id, FQueue.evaluate_id,
                               FTrainRequest.model_name, FQueue.queue_type, FQueue.location,
                               func.if_(FQueue.queue_type == 1, FTrainRequest.is_urgency, FModelEvaluate.is_urgency).label(
                                   'is_urgency'),
                               func.if_(FQueue.queue_type == 1, FTrainRequest.resource, FModelEvaluate.resource).label(
                                   'resource'),
                               func.if_(FQueue.queue_type == 1, FTrainRequest.status, FModelEvaluate.status).label(
                                   'status'),
                               func.if_(FQueue.queue_type == 1, FTrainRequest.percentage, FModelEvaluate.percentage).label('percentage'),
                               func.if_(FQueue.queue_type == 1, FTrainRequest.tp_user_id, FModelEvaluate.tp_user_id).label('tp_user_id'),
                               func.if_(FQueue.queue_type == 1, FTrainRequest.add_time, FModelEvaluate.add_time).label(
                                   'add_time'),
                               func.if_(FQueue.queue_type == 1, FTrainRequest.process_id, FModelEvaluate.process_id).label(
                                   'process_id'),
                               ).outerjoin(
            FTrainRequest, and_(FQueue.train_request_id == FTrainRequest.train_request_id, FTrainRequest.delete_flag == settings.DELETE_FLAG_FALSE)).outerjoin(
            FModelEvaluate, and_(FModelEvaluate.evaluate_id == FQueue.evaluate_id, FModelEvaluate.delete_flag == settings.DELETE_FLAG_FALSE)).filter(
            FQueue.delete_flag == settings.DELETE_FLAG_FALSE)
        q = q.filter(or_(FTrainRequest.status.in_(status), FModelEvaluate.status.in_(status)))
        subquery = q.subquery()
        query = self.session.query(subquery.c.queue_id, subquery.c.location, subquery.c.add_time, subquery.c.is_urgency,
                                   subquery.c.queue_type, subquery.c.percentage, subquery.c.process_id, subquery.c.resource,
                                   subquery.c.status, subquery.c.model_name, subquery.c.train_request_id, subquery.c.evaluate_id,
                                   TouchpointUser.tp_user_name).join(
            TouchpointUser, subquery.c.tp_user_id == TouchpointUser.tp_user_id)
        if args.get('model_name'):
            query = query.filter(subquery.c.model_name.like('%' + args.get('model_name') + '%'))
        if args.get('status'):
            query = query.filter(subquery.c.status == args.get('status'))
        if args.get('queue_type'):
            query = query.filter(subquery.c.queue_type == args.get('queue_type'))

        query = query.order_by(subquery.c.status.desc(), subquery.c.location)

        objs = query.slice((int(args['page_no']) - 1) * int(args['page_size']), int(args['page_size']) * int(args['page_no'])).all()
        # logger.info(query.statement.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True}))

        data_list = list()
        for obj in objs:
            data = dict()
            data['queue_id'] = obj.queue_id
            data['location'] = obj.location
            data['add_time'] = obj.add_time
            data['tp_user_name'] = obj.tp_user_name
            data['is_urgency'] = obj.is_urgency
            data['status'] = obj.status
            data['model_name'] = obj.model_name
            data['train_request_id'] = obj.train_request_id
            data['evaluate_id'] = obj.evaluate_id
            data['queue_type'] = obj.queue_type
            data['percentage'] = obj.percentage
            data['resource'] = obj.resource
            data['process_id'] = obj.process_id
            data_list.append(data)
        return data_list

    def get_queue_detail(self, queue_id, train_request_id, evaluate_id):

        q = self.session.query(FQueue.queue_id, FQueue.location, FQueue.add_time, FQueue.queue_type, FQueue.train_request_id,
                               FQueue.evaluate_id,
                               func.if_(FQueue.queue_type == 1, FTrainRequest.is_urgency, FModelEvaluate.is_urgency).label(
                                   'is_urgency'),
                               func.if_(FQueue.queue_type == 1, FTrainRequest.status, FModelEvaluate.status).label('status'),
                               func.if_(FQueue.queue_type == 1, FTrainRequest.process_id, FModelEvaluate.process_id).label('process_id')
                               ).outerjoin(
            FTrainRequest, FQueue.train_request_id == FTrainRequest.train_request_id).outerjoin(
            FModelEvaluate, FModelEvaluate.evaluate_id == FQueue.evaluate_id).filter(FQueue.delete_flag == settings.DELETE_FLAG_FALSE)
        if queue_id:
            q = q.filter(FQueue.queue_id == queue_id)
        elif train_request_id:
            q = q.filter(FQueue.queue_type == 1, FQueue.train_request_id == train_request_id)
        elif evaluate_id:
            q = q.filter(FQueue.queue_type == 2, FQueue.evaluate_id == evaluate_id)
        else:
            return None
        obj = q.first()
        data = dict()
        if obj:
            data['queue_id'] = obj.queue_id
            data['queue_type'] = obj.queue_type
            data['evaluate_id'] = obj.evaluate_id
            data['location'] = obj.location
            data['add_time'] = obj.add_time
            data['is_urgency'] = obj.is_urgency
            data['train_request_id'] = obj.train_request_id
            data['status'] = obj.status
            data['process_id'] = obj.process_id
        return data


if __name__ == "__main__":
    obj = TrainOrm('wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ')
    ret = obj.get_train_request_params_detail('4903149666927906817')

