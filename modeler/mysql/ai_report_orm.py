# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: ai_report_orm.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 7月 09, 2024
# ---
import settings
from module import BaseModel
from module.mysql.models import *
from sqlalchemy import func, or_, and_
from utils.tools import get_snowflake_id
from sqlalchemy.dialects import postgresql


class AiReportOrm(BaseModel):

    def get_ai_report_by_name(self, app_id, report_id):
        """
        根据id获取ai报告
        :param app_id:
        :param report_id:
        :return:
        """

        q = self.session.query(AigcReport).filter(AigcReport.app_id == app_id, AigcReport.delete_flag == settings.DELETE_FLAG_FALSE)
        if report_id:
            q = q.filter(AigcReport.report_id == report_id)
        else:
            q = q.filter(AigcReport.is_first == 1)
        # print(q.statement.compile(dialect=postgresql.dialect(), compile_kwargs={"literal_binds": True}))
        obj = q.first()
        data = {}
        if obj:
            data = {
                'report_id': obj.report_id,
                'title': obj.title,
                'content_prompt': obj.content_prompt,
                'content_charts': obj.content_charts,
                'charts_type': obj.charts_type,
                'sort': obj.sort,
                'code': obj.code,
                'level': obj.level,
                'next_report_id': self.get_next_ai_report(app_id, obj.sort)
            }
        # print(data)
        return data

    def get_next_ai_report(self, app_id, sort):
        objs = self.session.query(AigcReport).filter(AigcReport.app_id == app_id, AigcReport.delete_flag == settings.DELETE_FLAG_FALSE).order_by(
            AigcReport.sort).all()

        index = 0
        for obj in objs:
            if obj.sort == sort:
                break
            index += 1
        if index == len(objs) - 1:
            return ''
        else:
            return objs[index + 1].report_id

    def get_ai_report_detail(self, report_id):

        obj = self.session.query(AigcReport).filter(AigcReport.report_id == report_id).first()
        data = {}
        if obj:
            data = {
                'report_id': obj.report_id,
                'title': obj.title,
                'content': obj.content,
                'content_prompt': obj.content_prompt,
                'content_charts': obj.content_charts,
                'charts_type': obj.charts_type,
                'code': obj.code,
                'level': obj.level,
                'data_type': obj.data_type,
                'chatbi_data': obj.chatbi_data,
                'is_charts': obj.is_charts
            }
        return data