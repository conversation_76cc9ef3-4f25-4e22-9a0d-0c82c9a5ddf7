# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: celery_config_prod.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site:
# @Time: 1月 21, 2022
# ---
import os
from datetime import timedelta
from celery.schedules import crontab, crontab_parser

from kombu import Queue, Exchange

# sudo docker exec -it a2fb82fd559f redis-cli -h 112.126.99.19 -p 6379 -a yifan2019 -n 10 ltrim ai_delivery 0 0
# broker设置中间件，backend设置后端存储

# BROKER_URL = 'redis://:zsrz2024wfn031641@192.168.2.110:6379/13'
# BACKEND_URL = 'redis://:zsrz2024wfn031641@192.168.2.110:6379/13'

# redis://:zsrz2024wfn031641@redis:6379/13
BROKER_URL = os.getenv('CELERY_BROKER_URL', 'redis://:zsrz2024wfn031641@123.56.116.227:6379/13')
BACKEND_URL = os.getenv('CELERY_BACKEND_URL', 'redis://:zsrz2024wfn031641@123.56.116.227:6379/13')
#

CELERY_TASK_RESULT_EXPIRES = 3600  # 结果过期

CELERY_TIMEZONE = 'Asia/Shanghai'  # 时间格式，默认UTC

# 任务失败或超时自动确认，默认为True
CELERY_ACKS_ON_FAILURE_OR_TIMEOUT = False
# 任务完成之后再确认
CELERY_ACKS_LATE = True
# worker进程崩掉之后拒绝确认
CELERY_REJECT_ON_WORKER_LOST = True

TASK_NAME = "default_ai_prod"

CREATE_TASK_NAME = 'create_ai_prod'

CELERY_QUEUES = (
    Queue(TASK_NAME, Exchange(TASK_NAME), routing_key=TASK_NAME),
    Queue(CREATE_TASK_NAME, Exchange(CREATE_TASK_NAME), routing_key=CREATE_TASK_NAME,
          queue_arguments={'x-max-priority': 9}),
)

CELERY_DEFAULT_QUEUE = TASK_NAME
CELERY_DEFAULT_EXCHANGE = TASK_NAME
CELERY_DEFAULT_ROUTING_KEY = TASK_NAME

# 导入指定任务模块
CELERY_IMPORTS = (
    'apps.ai.task',
    'apps.chat.task',
    'apps.synthesis.task',
)

# # 路由
# CELERY_ROUTES = {
#     'apps.ad_create.task.preview_async_ad_create_template': {"queue": 'ad_create', "routing_key": 'ad_create'},
#     'apps.ad_create.task.manual_async_ad_create_template': {"queue": 'ad_create', "routing_key": 'ad_create'}
# }


# 有些情况下可以防止死锁

CELERYD_FORCE_EXECV = True

# 设置并发worker数
CELERYD_CONCURRENCY = 4

# 允许重试次数


# 每个worker最多执行100个任务被销毁，防止内存泄漏
CELERY_MAX_TASKS_PER_CHILD = 100

# 每个worker执行了多少任务就会死掉
CELERYD_MAX_TASKS_PER_CHILD = 40

# 单个任务最大运行时间

CELERYD_TASK_TIME_LIMIT = 60 * 60

# 定时任务
CELERYBEAT_SCHEDULE = {
    'refresh_embed_status': {
        'task': 'apps.ai.task.refresh_embed_status',
        'schedule': crontab(minute="*/20"),
        'args': None
    },
    'timing_update_content_embeddings': {
        'task': 'apps.ai.task.timing_update_content_embeddings',
        'schedule': crontab(minute='*/50'),
        'args': None
    },
    # 'timing_update_question_embeddings': {
    #     'task': 'apps.ai.task.timing_update_question_embeddings',
    #     'schedule': crontab(minute='*/15'),
    #     'args': None
    # },
    # 'timing_update_question_point_plan_embeddings': {
    #     'task': 'apps.ai.task.timing_update_question_point_plan_embeddings',
    #     'schedule': crontab(minute='*/20'),
    #     'args': None
    # },
    'async_radar_to_es': {
        'task': 'apps.chat.task.async_radar_to_es',
        'schedule': crontab(hour=2),
        'args': None,
        "options": {"run_immediately": True},  # 启动时默认执行一次
    },
    'file_extract_and_save_data': {
        'task': 'file_extract_and_save_data',
        'schedule': crontab(minute='*/10'),
        'args': None
    },
    'timing_update_content_images': {
        'task': 'apps.ai.task.timing_update_content_images',
        'schedule': crontab(hour=0, minute=0),
        'args': None
    },
    'timing_update_knowledge_chunk_images': {
        'task': 'apps.ai.task.timing_update_knowledge_chunk_images',
        'schedule': crontab(hour=2, minute=0),
        'args': None
    },
    'timing_update_doc_chunk_data': {
        'task': 'apps.ai.task.timing_update_doc_chunk_data',
        'schedule': crontab(hour=4, minute=1),
        'args': None,
    },
    # 'timing_update_content_info_aa': {
    #     'task': 'apps.ai.task.timing_update_content_info_aa',
    #     'schedule': crontab(hour=0, minute=0),
    #     'args': None
    # },
    'file_life_status_action': {
        'task': 'file_life_status_action',
        'schedule': crontab(minute=5, hour='*/5'),
        'args': None
    },
    'async_del_content_vector': {
        'task': 'apps.ai.task.async_del_content_vector',
        'schedule': crontab(hour='*/1'),
        'args': None
    },
    'daily_cluster_build': {
        'task': 'apps.ai.task.build_cluster',
        'schedule': crontab(hour=3, minute=30),
        'args': (None, 0),
        'options': {
            'expires': 3600,
        }
    },

}

# celery beat -A celery_app  -l info
#
# celery -A celery_app worker -l info -P eventlet
