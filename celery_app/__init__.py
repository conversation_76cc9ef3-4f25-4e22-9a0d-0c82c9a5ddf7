# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: __init__.py.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 1月 21, 2022
# ---
import os
import platform

from celery import Celery, platforms, chain
from kombu import serialization

ENVIRONMENT = os.environ.get('ENVIRONMENT', 'LOCAL' if 'Windows' in platform.platform() else None)


BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))


CELERY_CONFIG_PATH = None

if ENVIRONMENT == 'PROD':
    print('celery：定时任务-生产环境')
    from celery_app.celery_config_prod import *
    CELERY_CONFIG_PATH = 'celery_app.celery_config_prod'
elif ENVIRONMENT == 'RELEASE':
    print('celery：定时任务-测试环境')
    from celery_app.celery_config_release import *

    CELERY_CONFIG_PATH = 'celery_app.celery_config_release'
else:
    print('celery：定时任务-本地开发环境')
    from celery_app.celery_config_local import *  # todo 暂时 测试环境
    CELERY_CONFIG_PATH = 'celery_app.celery_config_local'


celery = Celery(TASK_NAME, broker=BROKER_URL)

# 通过celery实例加载配置模块

celery.config_from_object(CELERY_CONFIG_PATH)

celery.conf.CELERY_RESULT_BACKEND = BACKEND_URL

platforms.C_FORCE_ROOT = True  # 解决root用户不能启动的问题

# noinspection PyProtectedMember
serialization.registry._decoders.pop("application/x-python-serialize")

celery.conf.update(
    CELERY_ACCEPT_CONTENT=['json'],
    CELERY_TASK_SERIALIZER='json',
    CELERY_RESULT_SERIALIZER='json',
)