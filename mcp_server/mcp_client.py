import asyncio
from fastmcp import Client
from sse_starlette import EventSourceResponse

import mcp
import json


sse_url = "http://0.0.0.0:18082/sse"       # SSE server URL
# sse_url = "http://**************:18081/sse"       # SSE server URL

client_sse = Client(sse_url)
async def call_tool(func_name: str, args: dict):
    async with client_sse:
        result = await client_sse.call_tool(func_name, args)
        for res in result:
            print("res", res)
            if isinstance(res, mcp.types.TextContent):
                if isinstance(res.text, EventSourceResponse):
                    async for event in res.text:
                        print('event', type(event), event)
                        data = event.data.replace('data: ', '').strip()
                        if data:
                            try:
                                data_dict = json.loads(data)
                                print(data_dict)
                            except json.JSONDecodeError as e:
                                print(f"Failed to decode JSON: {e}")
                                print(data)
                else:
                    print(res.text)
            else:
                print(type(res), res)


async def call_image_tool(func_name:str, args: dict):
    async with client_sse:
        result = await client_sse.call_tool(func_name, args)
        print(type(result))
        print(result)


async def get_tool_list():
    async with client_sse:
        tools = await client_sse.list_tools()
        print(tools)


# 修改为异步调用
async def main():
    # await call_image_tool('image_to_text', {"image_url": "http://**************:9000/guotou/aigc_text2img/7964fda9-c271-49cb-aabd-deeb438cd06a_cc59264f-5150-495d-acaa-c94394fa590a.png"})
    # await call_image_tool('get_mermaid', {"content": "西红柿炒鸡蛋流程"})
    await get_tool_list()

asyncio.run(main())
