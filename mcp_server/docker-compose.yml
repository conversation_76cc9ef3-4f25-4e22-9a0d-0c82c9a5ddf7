version: '2'
services:
  mcp_multimodel_ai_server: 
    build:
      context: .
      dockerfile: Dockerfile
    ports:  # 端口映射
      - "18080:18080"
    restart: always
    command: ["fastmcp", "run", "multimodel_server.py:mcp", "--transport", "sse"]

  mcp_mermaid_ai_server:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "18081:18080"
    restart: always
    command: ["fastmcp", "run", "mermaid_server.py:mcp", "--transport", "sse"]

  mcp_image_genertion_ai_server:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "18082:18080"
    restart: always
    command: ["fastmcp", "run", "image_genertion_server.py:mcp", "--transport", "sse"]

