import requests
import json

def return_image_or_text(content: str, image_url: str) -> list:
    from main import CONFIG_DICT
    request_url = CONFIG_DICT.get("jusure_ai").get("base_url") + CONFIG_DICT.get("jusure_ai").get("conversation_url")
    ai_headers = CONFIG_DICT.get("jusure_ai").get("ai_headers")
    data_json = {
            "view_id": CONFIG_DICT.get("jusure_ai").get("mutil_param").get("text_to_image_view_id"),
            "model_id": CONFIG_DICT.get("jusure_ai").get("mutil_param").get("model_id"),
            "content": content
        }
    if image_url:
        del data_json["content"]
        data_json.update({"view_id": CONFIG_DICT.get("jusure_ai").get("mutil_param").get("image_to_image_view_id")})
        data_json["image_url"] = image_url
    
    table_res = requests.post(request_url, headers=ai_headers, json=data_json, stream=True)

    res = table_res.text
    chunk_list = res.splitlines()
    res_image_list = list()
    for item in chunk_list:
        chunk = item.replace("data: ", "").strip()
        if chunk:
            chunk_dict = json.loads(chunk)
            res_image_list.extend(chunk_dict.get("content", ""))
    return list(set(res_image_list))


def image_understanding(image_url: str):
    from main import CONFIG_DICT
    request_url = CONFIG_DICT.get("jusure_ai").get("base_url") + CONFIG_DICT.get("jusure_ai").get("conversation_url")
    ai_headers = CONFIG_DICT.get("jusure_ai").get("ai_headers")
    data_json = {
            "view_id": CONFIG_DICT.get("jusure_ai").get("mutil_param").get("image_to_text_view_id"),
            "model_id": CONFIG_DICT.get("jusure_ai").get("mutil_param").get("model_id"),
            "img_url": image_url
        }
    table_res = requests.post(request_url, headers=ai_headers, json=data_json, stream=True)

    res_str = ''
    for data in table_res.iter_content(chunk_size=1024, decode_unicode=True):
        item_json = data.replace('data: ', '').strip()
        if item_json:
            try:
                chunk = json.loads(item_json)
                res_str += chunk.get("content", "")
            except Exception as e:
                print('e', e)
    return res_str


def get_mermaid_code(user_input: str):
    from main import CONFIG_DICT
    request_url = CONFIG_DICT.get("jusure_ai").get("base_url") + CONFIG_DICT.get("jusure_ai").get("mermaid_url")
    ai_headers = CONFIG_DICT.get("jusure_ai").get("ai_headers")
    data_json = {
            "view_id": CONFIG_DICT.get("jusure_ai").get("mutil_param").get("mermaid_view_id"),
            "model_id": CONFIG_DICT.get("jusure_ai").get("mutil_param").get("model_id"),
            "content": user_input
        }

    table_res = requests.post(request_url, headers=ai_headers, json=data_json, stream=True)

    res_str = ''
    for data in table_res.iter_content(chunk_size=1024, decode_unicode=True):
        item_json = data.replace('data: ', '').strip()
        chunk_list = item_json.splitlines()
        for item_json in chunk_list:
            if item_json:
                try:
                    chunk = json.loads(item_json)
                    res_str += chunk.get("content", "")
                except Exception as e:
                    print('e', e)
    return res_str

if __name__ == "__main__":
    return_image_or_text("海岸美景", "")