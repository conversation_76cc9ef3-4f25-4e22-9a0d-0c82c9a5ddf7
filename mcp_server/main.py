# server.py
from fastmcp import FastMCP
from services.multimodel import return_image_or_text, image_understanding, get_mermaid_code
import yaml

def load_config():
    try:
        with open('config.yaml', 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
            return config
    except FileNotFoundError:
        print("错误：未找到配置文件 config.yaml。")
    except yaml.YAMLError as e:
        print(f"错误：解析 YAML 文件时出错 - {e}")
    return None

# 加载配置
CONFIG_DICT = load_config()

# 创建 MCP 服务器
mcp = FastMCP("jusure_ai_multi", port=18080)


@mcp.tool()
def text_to_image(content: str) -> list:
    """
    将文字变成图片
    """
    return return_image_or_text(content=content, image_url="")

@mcp.tool()
def image_to_image(image_url: str) -> list:
    """
    将图片生成图片
    """
    return return_image_or_text(content="", image_url=image_url)

@mcp.tool()
def image_to_text(image_url: str) -> str:
    """
    图片理解
    """
    return image_understanding(image_url=image_url)

@mcp.tool()
def get_mermaid(content: str) -> str:
    """
    获得流程图
    """
    return get_mermaid_code(user_input=content)
