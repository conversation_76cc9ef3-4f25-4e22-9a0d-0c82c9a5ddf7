# server.py
from fastmcp import FastMCP
from services.multimodel import get_mermaid_code
import yaml

def load_config():
    try:
        with open('config.yaml', 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
            return config
    except FileNotFoundError:
        print("错误：未找到配置文件 config.yaml。")
    except yaml.YAMLError as e:
        print(f"错误：解析 YAML 文件时出错 - {e}")
    return None

# 加载配置
CONFIG_DICT = load_config()

# 创建 MCP 服务器
mcp = FastMCP("jusure_ai_mermaid", port=18080)


@mcp.tool()
def get_mermaid(content: str) -> str:
    """
    获得流程图
    """
    return get_mermaid_code(user_input=content)