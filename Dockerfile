FROM python:3.11
ENV LANG C.UTF-8

WORKDIR /app
ADD ./requirements.txt /app/requirements.txt
RUN pip3 install --no-cache-dir -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# 安装 supervisor
RUN apt-get update && apt-get install -y supervisor
# 安装 supervisor 和 OpenCV 所需的库
RUN apt-get update && apt-get install -y supervisor libgl1-mesa-glx
RUN pip install ijson -i https://pypi.tuna.tsinghua.edu.cn/simple
RUN pip install uwsgi -i https://pypi.tuna.tsinghua.edu.cn/simple

# 设置环境变量
ENV TZ="Asia/Shanghai"

# 复制项目文件到容器
COPY . /app

# 添加 supervisor 配置文件到正确的位置 supervisord.conf [include] *.conf
COPY supervisord_bk.conf /etc/supervisor/supervisord.conf
COPY supervisor-ai-prod_bk.ini /etc/supervisor/conf.d/supervisor-ai-prod_bk.conf

# 创建日志目录和文件
RUN mkdir -p /app/logs && touch /app/logs/logger_info.log
RUN mkdir -p /var/run && touch /var/run/supervisor.sock && chmod 777 /var/run/supervisor.sock && service supervisor restart && unlink /var/run/supervisor.sock
# RUN service start supervisor

# 使用 supervisor 启动  supervisorctl start all
# CMD ["supervisord", "-c", "/etc/supervisor/conf.d/supervisor-ai-prod_bk.ini"]
CMD ["supervisord", "-c", "/etc/supervisor/supervisord.conf"]
# CMD ["python", "run.py"]