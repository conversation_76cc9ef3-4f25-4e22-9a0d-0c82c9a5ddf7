CREATE TABLE `aigc_forage_combine_record` (
  `record_id` bigint NOT NULL AUTO_INCREMENT,
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `tp_user_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作用户id',
  `task_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行任务id',
  `state` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行状态',
  `forage_id` int NOT NULL COMMENT '算料库id',
  `aigc_model_id` int NOT NULL COMMENT 'aigc模型id',
  PRIMARY KEY (`record_id`,`tp_user_id`,`forage_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`),
  KEY `forage_id` (`forage_id`),
  <PERSON><PERSON><PERSON> `aigc_model_id` (`aigc_model_id`),
  CONSTRAINT `aigc_forage_combine_record_ibfk_1` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `aigc_forage_combine_record_ibfk_2` FOREIGN KEY (`forage_id`) REFERENCES `model_forage` (`forage_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `aigc_forage_combine_record_ibfk_3` FOREIGN KEY (`aigc_model_id`) REFERENCES `aigc_model` (`aigc_model_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

alter table `aigc_model` add column `aigc_model_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型id'   CONSTRAINT `model_forage_ibfk_3` FOREIGN KEY (`aigc_model_id`) REFERENCES `aigc_model` (`aigc_model_id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

alter table `forage_file` add column `upload_type` int(1) unsigned zerofill DEFAULT '0' COMMENT '上传类型';

alter table `forage_qa` add column `qa_type` int(1) unsigned zerofill DEFAULT '0' COMMENT '类型';
