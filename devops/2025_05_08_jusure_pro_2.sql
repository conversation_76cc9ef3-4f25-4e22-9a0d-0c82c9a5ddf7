-- 2025-05-08 文档表添加字段

ALTER TABLE `jusure_pro_2`.`aigc_knowledge_document`
ADD  mind_map VARCHAR(1000) COMMENT '思维导图';

ALTER TABLE `jusure_pro_2`.`aigc_knowledge_document`
ADD  main_points VARCHAR(1000) COMMENT '主要内容';

ALTER TABLE `jusure_pro_2`.`aigc_knowledge_document`
ADD  manual_tag VARCHAR(1000) COMMENT '手动标签';

ALTER TABLE `jusure_pro_2`.`aigc_knowledge_document`
ADD  key_words VARCHAR(1000) COMMENT '关键词';

ALTER TABLE `jusure_pro_2`.`aigc_knowledge_document`
ADD category VARCHAR(1000) COMMENT '分类';

ALTER TABLE `jusure_pro_2`.`aigc_knowledge_document`
ADD is_collect INT(1) unsigned zerofill DEFAULT '0' COMMENT '是否收藏';

ALTER TABLE `jusure_pro_2`.`forage_qa`
ADD  clear_question VARCHAR(1000) COMMENT '清洗后的问题';

ALTER TABLE `jusure_pro_2`.`forage_qa`
ADD  clear_answer VARCHAR(1000) COMMENT '清洗后的答案';

ALTER TABLE `jusure_pro_2`.`model_forage`
ADD  evaluation VARCHAR(1000) COMMENT '评估结果';

ALTER TABLE `jusure_pro_2`.`model_forage`
ADD clear_type VARCHAR(100) COMMENT '清洗规则';

-- 2025-05-10 添加过滤规则和去重规则

ALTER TABLE `jusure_pro_2`.`forage_qa`
ADD `delete_filter_type` int(1) unsigned zerofill DEFAULT '0' COMMENT '删除原因，1为过滤，2为去重，默认为0正常删除';

ALTER TABLE `jusure_pro_2`.`model_forage`
ADD `filter_type` VARCHAR(100) COMMENT '过滤规则';

ALTER TABLE `jusure_pro_2`.`model_forage`
ADD `deduplication_type` VARCHAR(100) COMMENT '去重规则';

CREATE TABLE `jusure_pro_2`.`aigc_forage_cleaning_record` (
  `record_id` bigint NOT NULL AUTO_INCREMENT,
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `tp_user_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作用户id',
  `task_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行清洗任务id',
  `state` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行状态',
  `clear_log` varchar(1000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '清洗任务日志',
  `forage_id` int NOT NULL COMMENT '算料库id',
  PRIMARY KEY (`record_id`,`tp_user_id`,`forage_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`),
  KEY `forage_id` (`forage_id`),
  CONSTRAINT `aigc_forage_cleaning_record_ibfk_1` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `aigc_forage_cleaning_record_ibfk_2` FOREIGN KEY (`forage_id`) REFERENCES `model_forage` (`forage_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 2025-05-12 添加评估记录表
CREATE TABLE `aigc_forage_evaluation_record` (
  `record_id` bigint NOT NULL AUTO_INCREMENT,
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `tp_user_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作用户id',
  `task_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行任务id',
  `state` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行状态',
  `evaluation_results` varchar(1000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '评估结果',
  `forage_id` int NOT NULL COMMENT '算料库id',
  `aigc_model_id` int NOT NULL COMMENT 'aigc模型id',
  PRIMARY KEY (`record_id`,`tp_user_id`,`forage_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`),
  KEY `forage_id` (`forage_id`),
  KEY `aigc_model_id` (`aigc_model_id`),
  CONSTRAINT `aigc_forage_evaluation_record_ibfk_1` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `aigc_forage_evaluation_record_ibfk_2` FOREIGN KEY (`forage_id`) REFERENCES `model_forage` (`forage_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `aigc_forage_evaluation_record_ibfk_3` FOREIGN KEY (`aigc_model_id`) REFERENCES `aigc_model` (`aigc_model_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;