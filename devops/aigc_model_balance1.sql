/*
 Navicat Premium Dump SQL

 Source Server         : 阿里云
 Source Server Type    : MySQL
 Source Server Version : 80037 (8.0.37)
 Source Host           : **************:3306
 Source Schema         : jusure_pro_2

 Target Server Type    : MySQL
 Target Server Version : 80037 (8.0.37)
 File Encoding         : 65001

 Date: 28/11/2024 21:07:51
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for aigc_model_balance
-- ----------------------------
DROP TABLE IF EXISTS `aigc_model_balance`;
CREATE TABLE `aigc_model_balance` (
  `aigc_model_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `model_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `url_id` int NOT NULL AUTO_INCREMENT,
  `status` int DEFAULT NULL,
  PRIMARY KEY (`url_id`),
  KEY `模型ID` (`aigc_model_id`),
  CONSTRAINT `模型ID` FOREIGN KEY (`aigc_model_id`) REFERENCES `aigc_model` (`aigc_model_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of aigc_model_balance
-- ----------------------------
BEGIN;
INSERT INTO `aigc_model_balance` (`aigc_model_id`, `model_url`, `url_id`, `status`) VALUES ('6b70c380-637c-11ef-931c-4aeb9a6a45a4', 'http://114.242.210.44:5201/', 1, 0);
INSERT INTO `aigc_model_balance` (`aigc_model_id`, `model_url`, `url_id`, `status`) VALUES ('6b70c380-637c-11ef-931c-4aeb9a6a45a4', 'http://114.242.210.44:5202/', 2, 1);
INSERT INTO `aigc_model_balance` (`aigc_model_id`, `model_url`, `url_id`, `status`) VALUES ('6b70c380-637c-11ef-931c-4aeb9a6a45a4', 'http://192.168.162.84:5205/', 3, 0);
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;


INSERT INTO `aigc_model_balance` (`aigc_model_id`, `model_url`, `url_id`, `status`) VALUES ('6b70c380-637c-11ef-931c-4aeb9a6a45a4', 'http://192.168.162.84:5205/', 1, 0);
INSERT INTO `aigc_model_balance` (`aigc_model_id`, `model_url`, `url_id`, `status`) VALUES ('6b70c380-637c-11ef-931c-4aeb9a6a45a4', 'http://192.168.162.84:5206/', 2, 0);
INSERT INTO `aigc_model_balance` (`aigc_model_id`, `model_url`, `url_id`, `status`) VALUES ('6b70c380-637c-11ef-931c-4aeb9a6a45a4', 'http://192.168.162.84:5207/', 3, 0);
INSERT INTO `aigc_model_balance` (`aigc_model_id`, `model_url`, `url_id`, `status`) VALUES ('6b70c380-637c-11ef-931c-4aeb9a6a45a4', 'http://192.168.162.84:5208/', 4, 0);
INSERT INTO `aigc_model_balance` (`aigc_model_id`, `model_url`, `url_id`, `status`) VALUES ('6b70c380-637c-11ef-931c-4aeb9a6a45a4', 'http://192.168.162.84:5209/', 5, 0);
INSERT INTO `aigc_model_balance` (`aigc_model_id`, `model_url`, `url_id`, `status`) VALUES ('6b70c380-637c-11ef-931c-4aeb9a6a45a4', 'http://192.168.162.84:5210/', 6, 0);
INSERT INTO `aigc_model_balance` (`aigc_model_id`, `model_url`, `url_id`, `status`) VALUES ('6b70c380-637c-11ef-931c-4aeb9a6a45a4', 'http://192.168.162.84:5211/', 7, 0);
INSERT INTO `aigc_model_balance` (`aigc_model_id`, `model_url`, `url_id`, `status`) VALUES ('6b70c380-637c-11ef-931c-4aeb9a6a45a4', 'http://192.168.162.84:5212/', 8, 0);
INSERT INTO `aigc_model_balance` (`aigc_model_id`, `model_url`, `url_id`, `status`) VALUES ('6b70c380-637c-11ef-931c-4aeb9a6a45a4', 'http://172.104.223.14:5205/', 9, 0);
INSERT INTO `aigc_model_balance` (`aigc_model_id`, `model_url`, `url_id`, `status`) VALUES ('6b70c380-637c-11ef-931c-4aeb9a6a45a4', 'http://172.104.223.23:5205/', 10, 0);


ALTER TABLE `jusure_pro_2`.`aigc_app` 
ADD COLUMN `rerank_size` int NULL DEFAULT 100 COMMENT '重排片数' AFTER `is_rerank`,
ADD COLUMN `rarank_ignore_score` float NULL DEFAULT 0 COMMENT 'rerank 后丢弃阈值' AFTER `rerank_size`,
ADD COLUMN `global_percent` int NULL COMMENT '全局检索比例' AFTER `rarank_ignore_score`;
