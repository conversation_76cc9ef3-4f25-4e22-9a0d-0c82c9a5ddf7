/*
 Navicat Premium Dump SQL

 Source Server         : 阿里云
 Source Server Type    : MySQL
 Source Server Version : 80037 (8.0.37)
 Source Host           : **************:3306
 Source Schema         : jusure_pro_2

 Target Server Type    : MySQL
 Target Server Version : 80037 (8.0.37)
 File Encoding         : 65001

 Date: 18/11/2024 11:39:49
*/

-- ----------------------------
-- Table structure for aigc_qa_lib
-- ----------------------------
DROP TABLE IF EXISTS `aigc_qa_lib`;
CREATE TABLE `aigc_qa_lib` (
  `qa_lib_id` bigint NOT NULL COMMENT '主键',
  `qa_name` varchar(255) NOT NULL COMMENT 'qa库名称',
  `qa_desc` varchar(255) DEFAULT NULL COMMENT 'qa库描述',
  `aigc_model_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型id',
  `icon_url` varchar(526) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '图标地址',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `status` int DEFAULT '1' COMMENT '状态 1启用；0停用',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '删除状态 0；1',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`qa_lib_id`),
  KEY `aigc_model_id` (`aigc_model_id`),
  KEY `tp_user_id` (`tp_user_id`),
  CONSTRAINT `aigc_qa_lib_ibfk_1` FOREIGN KEY (`aigc_model_id`) REFERENCES `aigc_model` (`aigc_model_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `aigc_qa_lib_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='qa库';

-- ----------------------------
-- Table structure for rel_app_qa_lib
-- ----------------------------
DROP TABLE IF EXISTS `rel_app_qa_lib`;
CREATE TABLE `rel_app_qa_lib` (
  `rel_id` bigint NOT NULL,
  `app_id` bigint DEFAULT NULL COMMENT '应用id',
  `qa_lib_id` bigint DEFAULT NULL COMMENT 'qa库id',
  `delete_flag` tinyint DEFAULT '0' COMMENT '删除标识0；1',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`rel_id`),
  KEY `app_id` (`app_id`),
  KEY `tp_user_id` (`tp_user_id`),
  CONSTRAINT `rel_app_qa_lib_ibfk_1` FOREIGN KEY (`app_id`) REFERENCES `aigc_app` (`app_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_app_qa_lib_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Qa关联应用';



CREATE TABLE `jusure_pro_2`.`无标题`  (
  `aigc_model_id` varchar(64) NOT NULL,
  `model_url` varchar(255) NULL,
  `url_id` int NOT NULL AUTO_INCREMENT,
  `status` int NULL,
  PRIMARY KEY (`url_id`),
  CONSTRAINT `模型ID` FOREIGN KEY (`aigc_model_id`) REFERENCES `jusure_pro_2`.`aigc_model` (`aigc_model_id`)
);

ALTER TABLE `jusure_pro_2`.`aigc_app` 
ADD COLUMN `is_pic_rerank` int NULL DEFAULT 0 COMMENT '是否启用图片rerank' AFTER `global_percent`,
ADD COLUMN `pic_rarank_ignore_score` float NULL DEFAULT 0 COMMENT '图片丢弃阈值' AFTER `is_pic_rerank`;

-- 全场景查询
ALTER TABLE `jusure_pro_2`.`aigc_app` 
ADD COLUMN `is_global` tinyint NULL DEFAULT 0 COMMENT '聚合应用使用，是否使用全场景搜索，1：使用，0：不使用，默认0' AFTER `format_id`;
ALTER TABLE `jusure_pro_2`.`aigc_app` 
ADD COLUMN `global_rerank_num` int NULL DEFAULT 20 COMMENT '使用全场景查询最终的使用数量' AFTER `is_global`;

-- 是否开启问题推荐
ALTER TABLE `jusure_pro_2`.`aigc_app` 
ADD COLUMN `need_suggestion` tinyint NULL DEFAULT 1 COMMENT '是否开启问题推荐' AFTER `scope_id`;
