/*
 Navicat Premium Dump SQL

 Source Server         : **************
 Source Server Type    : MySQL
 Source Server Version : 80037 (8.0.37)
 Source Host           : **************:3306
 Source Schema         : jusure_pro_2

 Target Server Type    : MySQL
 Target Server Version : 80037 (8.0.37)
 File Encoding         : 65001

 Date: 01/11/2024 13:48:12
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ad_apps
-- ----------------------------
DROP TABLE IF EXISTS `ad_apps`;
CREATE TABLE `ad_apps` (
  `app_id` bigint NOT NULL COMMENT '产品ID',
  `app_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '产品名称',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '产品状态 1：启用 0:禁用',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`app_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='投放产品表';

-- ----------------------------
-- Table structure for ad_apps_tpu
-- ----------------------------
DROP TABLE IF EXISTS `ad_apps_tpu`;
CREATE TABLE `ad_apps_tpu` (
  `app_tup_id` bigint NOT NULL COMMENT '投放产品管理员工ID',
  `app_id` bigint NOT NULL DEFAULT '0' COMMENT '产品ID',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '员工ID',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0：未删除 1：已删除',
  PRIMARY KEY (`app_tup_id`) USING BTREE,
  KEY `app_id` (`app_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `ad_apps_tpu_ibfk_1` FOREIGN KEY (`app_id`) REFERENCES `ad_apps` (`app_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `ad_apps_tpu_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='员工负责投放产品表';

-- ----------------------------
-- Table structure for ad_media_account
-- ----------------------------
DROP TABLE IF EXISTS `ad_media_account`;
CREATE TABLE `ad_media_account` (
  `media_account_id` bigint NOT NULL COMMENT '媒体账号ID',
  `app_id` bigint DEFAULT NULL COMMENT '产品ID',
  `media_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '媒体ID',
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '账号',
  `user_pass` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '密码',
  `advertiser_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '广告主ID',
  `advertiser_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '广告主名称',
  `form_account_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来自授权账号ID',
  `form_account_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来自授权账号名称',
  `account_role` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色',
  `advertiser_role` int DEFAULT NULL COMMENT '角色旧版',
  `access_token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `expires_in` int DEFAULT NULL,
  `refresh_token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `refresh_token_expires_in` int DEFAULT NULL,
  `account_string_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '账号id（字符串型）\n当advertiser_role=10有效，即抖音号类型时，即为aweme_sec_uid，可用于Dou+接口调用',
  `is_valid` tinyint DEFAULT NULL COMMENT '授权是否有效 1：有效 0：无效',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '1：启用 0：停用',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `refresh_time` int NOT NULL DEFAULT '0' COMMENT '刷新时间',
  `media_channel_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '媒体渠道ID',
  `user_acct_type` tinyint DEFAULT NULL COMMENT '百度账号 账号类型\n授权账户类型\n1: 普通账户\n2：超管账户（客户中心和账户管家）',
  `openid` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '百度账号openid',
  PRIMARY KEY (`media_account_id`) USING BTREE,
  KEY `app_id` (`app_id`) USING BTREE,
  CONSTRAINT `ad_media_account_ibfk_1` FOREIGN KEY (`app_id`) REFERENCES `ad_apps` (`app_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='投放媒体账户表';

-- ----------------------------
-- Table structure for ad_mission
-- ----------------------------
DROP TABLE IF EXISTS `ad_mission`;
CREATE TABLE `ad_mission` (
  `ad_mission_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `media_channel_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '媒体渠道id',
  `ad_mission_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '投放任务名称',
  `add_time` datetime DEFAULT NULL COMMENT '添加时间',
  `add_tpu_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加用户id',
  `repeat_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '周期任务标志；0：非周期任务；1：周期任务',
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `budget` decimal(10,2) DEFAULT NULL COMMENT '预算：元',
  `repeat_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '周期任务类型；0：季度；1：月；2：周',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志位;0:未删除；1:已删除',
  `media_account_id` bigint DEFAULT NULL COMMENT '授权账号ID',
  PRIMARY KEY (`ad_mission_id`) USING BTREE,
  KEY `fk_ad_mission_media_channel_1` (`media_channel_id`) USING BTREE,
  KEY `fk_ad_mission_tpu_id_1` (`add_tpu_id`) USING BTREE,
  CONSTRAINT `ad_mission_ibfk_1` FOREIGN KEY (`media_channel_id`) REFERENCES `media_channel` (`media_channel_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `ad_mission_ibfk_2` FOREIGN KEY (`add_tpu_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='投放任务表';

-- ----------------------------
-- Table structure for admission_cost_data
-- ----------------------------
DROP TABLE IF EXISTS `admission_cost_data`;
CREATE TABLE `admission_cost_data` (
  `ad_cost_data_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `landpage_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'landpage关联id',
  `bgn_date` datetime DEFAULT NULL COMMENT '统计开始时间',
  `end_date` datetime DEFAULT NULL COMMENT '统计结束时间',
  `cost` decimal(12,2) DEFAULT NULL COMMENT '广告花费，单位：元',
  `show_num` int DEFAULT NULL COMMENT '展示量',
  `click_num` int DEFAULT NULL COMMENT '点击量',
  `commit_num` int DEFAULT NULL COMMENT '提交量',
  `ocpm` decimal(10,2) DEFAULT NULL COMMENT '千次展示成本；单位：元',
  `add_time` datetime DEFAULT NULL COMMENT '添加时间',
  `adder` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加者，将来是广告代理商id',
  `convert_cost` decimal(12,2) DEFAULT NULL COMMENT '转化成本',
  `convert_rate` float(6,2) DEFAULT NULL COMMENT '转化率',
  `media_account_id` bigint DEFAULT NULL COMMENT '授权媒体账户ID',
  `avg_click_cost` decimal(10,2) DEFAULT NULL COMMENT '平均点击单价',
  `ctr` float(6,2) DEFAULT NULL COMMENT '点击率',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ad_cost_data_id`) USING BTREE,
  KEY `fk_admission_cost_data_landpage_1` (`landpage_id`) USING BTREE,
  CONSTRAINT `admission_cost_data_ibfk_1` FOREIGN KEY (`landpage_id`) REFERENCES `landpage` (`landpage_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='任务消耗表';

-- ----------------------------
-- Table structure for admission_data
-- ----------------------------
DROP TABLE IF EXISTS `admission_data`;
CREATE TABLE `admission_data` (
  `admission_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '投放任务id',
  `show_num` int DEFAULT '0' COMMENT '展示量',
  `click_num` int DEFAULT '0' COMMENT '点击数',
  `open_num` int DEFAULT '0' COMMENT '打开数',
  `read_time` int DEFAULT '0' COMMENT '阅读时长，毫秒',
  `commit_num` int DEFAULT '0' COMMENT '提交数',
  `roi` decimal(5,2) DEFAULT '0.00' COMMENT '投入产出比；roi=total_return/total_cost',
  `click_rate` decimal(5,2) DEFAULT '0.00' COMMENT '点击率',
  `commit_rate` decimal(5,2) DEFAULT '0.00' COMMENT '提交率',
  `ocpm` decimal(10,2) DEFAULT '0.00' COMMENT '千次展示量费用',
  `add_time` datetime DEFAULT NULL COMMENT '添加时间',
  `total_cost` decimal(10,2) DEFAULT '0.00' COMMENT '总广告花费',
  `total_return` decimal(10,2) DEFAULT '0.00' COMMENT '总回款',
  `total_sign` decimal(10,2) DEFAULT '0.00' COMMENT '总签单',
  `convert_cost` decimal(12,2) DEFAULT NULL COMMENT '转化成本',
  `convert_rate` float(6,2) DEFAULT NULL COMMENT '转化率',
  `avg_click_cost` decimal(10,2) DEFAULT NULL COMMENT '平均点击单价',
  `valid_num` int DEFAULT '0' COMMENT '转化有效数、表单有效数',
  PRIMARY KEY (`admission_id`) USING BTREE,
  CONSTRAINT `admission_data_ibfk_1` FOREIGN KEY (`admission_id`) REFERENCES `ad_mission` (`ad_mission_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='任务数据表';

-- ----------------------------
-- Table structure for admission_data_time
-- ----------------------------
DROP TABLE IF EXISTS `admission_data_time`;
CREATE TABLE `admission_data_time` (
  `ad_data_time_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `stat_date` date DEFAULT NULL COMMENT '统计日期',
  `admission_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '投放任务id',
  `show_num` int DEFAULT '0' COMMENT '展示量',
  `click_num` int DEFAULT '0' COMMENT '点击数',
  `open_num` int DEFAULT '0' COMMENT '打开数',
  `read_time` int DEFAULT '0' COMMENT '阅读时长，毫秒',
  `commit_num` int DEFAULT '0' COMMENT '提交数',
  `roi` decimal(5,2) DEFAULT '0.00' COMMENT '投入产出比',
  `click_rate` decimal(5,2) DEFAULT '0.00' COMMENT '点击率',
  `commit_rate` decimal(5,2) DEFAULT '0.00' COMMENT '提交率',
  `ocpm` decimal(10,2) DEFAULT '0.00' COMMENT '千次展示量费用',
  `add_time` datetime DEFAULT NULL COMMENT '添加时间',
  PRIMARY KEY (`ad_data_time_id`) USING BTREE,
  KEY `fk_admission_data_copy_1_admission_data_1` (`admission_id`) USING BTREE,
  CONSTRAINT `admission_data_time_ibfk_1` FOREIGN KEY (`admission_id`) REFERENCES `admission_data` (`admission_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='任务数据时间趋势表';

-- ----------------------------
-- Table structure for agent_agent
-- ----------------------------
DROP TABLE IF EXISTS `agent_agent`;
CREATE TABLE `agent_agent` (
  `agent_id` int NOT NULL AUTO_INCREMENT,
  `agent_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '智能体名称',
  `scene_id` int NOT NULL COMMENT '场景id',
  `scene_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '问题描述',
  `format_id` int DEFAULT NULL COMMENT '输出格式',
  `template_id` int DEFAULT NULL COMMENT '场景模版',
  `buss_graph` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '业务流节点',
  `ft_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '用户会话调整内容',
  `buss_messages` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '业务流分解信息',
  `tech_graph` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '技术流节点',
  `tech_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '技术流默认参数',
  `user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `is_collect` tinyint(1) DEFAULT NULL COMMENT '是否收藏',
  `agent_logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '智能体logo',
  `status` tinyint(1) DEFAULT NULL COMMENT '发布状态',
  `is_commit` tinyint(1) DEFAULT NULL COMMENT '提交状态',
  `delete_flag` tinyint(1) DEFAULT NULL COMMENT '删除状态',
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  PRIMARY KEY (`agent_id`),
  KEY `scene_id` (`scene_id`),
  CONSTRAINT `agent_agent_ibfk_1` FOREIGN KEY (`scene_id`) REFERENCES `agent_scene` (`scene_id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能体数据';

-- ----------------------------
-- Table structure for agent_agent_data_collect
-- ----------------------------
DROP TABLE IF EXISTS `agent_agent_data_collect`;
CREATE TABLE `agent_agent_data_collect` (
  `id` int NOT NULL AUTO_INCREMENT,
  `agent_id` int NOT NULL COMMENT '智能体id',
  `collect_id` int NOT NULL COMMENT '数据集id',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `delete_flag` tinyint(1) DEFAULT NULL COMMENT '删除状态',
  PRIMARY KEY (`id`),
  KEY `agent_id` (`agent_id`),
  KEY `collect_id` (`collect_id`),
  CONSTRAINT `agent_agent_data_collect_ibfk_1` FOREIGN KEY (`agent_id`) REFERENCES `agent_agent` (`agent_id`),
  CONSTRAINT `agent_agent_data_collect_ibfk_2` FOREIGN KEY (`collect_id`) REFERENCES `agent_data_collect` (`collect_id`)
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能体关联数据集';

-- ----------------------------
-- Table structure for agent_agent_favorite
-- ----------------------------
DROP TABLE IF EXISTS `agent_agent_favorite`;
CREATE TABLE `agent_agent_favorite` (
  `id` int NOT NULL AUTO_INCREMENT,
  `agent_id` int NOT NULL,
  `user_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='收藏数据';

-- ----------------------------
-- Table structure for agent_agent_knowledge
-- ----------------------------
DROP TABLE IF EXISTS `agent_agent_knowledge`;
CREATE TABLE `agent_agent_knowledge` (
  `id` int NOT NULL AUTO_INCREMENT,
  `agent_id` int NOT NULL COMMENT '智能体id',
  `knowledge_id` bigint NOT NULL COMMENT '知识库id',
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `delete_flag` tinyint(1) DEFAULT NULL COMMENT '删除状态',
  PRIMARY KEY (`id`),
  KEY `agent_id` (`agent_id`),
  KEY `agent_agent_knowledge_ibfk_2` (`knowledge_id`),
  CONSTRAINT `agent_agent_knowledge_ibfk_1` FOREIGN KEY (`agent_id`) REFERENCES `agent_agent` (`agent_id`),
  CONSTRAINT `agent_agent_knowledge_ibfk_2` FOREIGN KEY (`knowledge_id`) REFERENCES `aigc_knowledge` (`knowledge_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能体关联知识库';

-- ----------------------------
-- Table structure for agent_agent_like
-- ----------------------------
DROP TABLE IF EXISTS `agent_agent_like`;
CREATE TABLE `agent_agent_like` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL,
  `agent_id` int NOT NULL,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for agent_data_collect
-- ----------------------------
DROP TABLE IF EXISTS `agent_data_collect`;
CREATE TABLE `agent_data_collect` (
  `collect_id` int NOT NULL AUTO_INCREMENT,
  `collect_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据集名称',
  `scene_id` int NOT NULL COMMENT '场景id',
  `collect_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '数据集描述',
  `collect_type` int DEFAULT NULL COMMENT '数据集分类',
  `collect_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '数据集存储路径',
  `collect_quote` int DEFAULT NULL COMMENT '流程引用数',
  `user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户id',
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `delete_flag` tinyint(1) DEFAULT NULL COMMENT '删除状态',
  PRIMARY KEY (`collect_id`),
  KEY `scene_id` (`scene_id`),
  CONSTRAINT `agent_data_collect_ibfk_1` FOREIGN KEY (`scene_id`) REFERENCES `agent_scene` (`scene_id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能体数据集表';

-- ----------------------------
-- Table structure for agent_scene
-- ----------------------------
DROP TABLE IF EXISTS `agent_scene`;
CREATE TABLE `agent_scene` (
  `scene_id` int NOT NULL AUTO_INCREMENT COMMENT '场景ID',
  `scene_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场景名称',
  `default_question` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '默认问题',
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `delete_flag` tinyint(1) DEFAULT NULL COMMENT '删除状态',
  `user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户id',
  PRIMARY KEY (`scene_id`)
) ENGINE=InnoDB AUTO_INCREMENT=68 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能体场景表';

-- ----------------------------
-- Table structure for agent_tool
-- ----------------------------
DROP TABLE IF EXISTS `agent_tool`;
CREATE TABLE `agent_tool` (
  `tool_id` int NOT NULL AUTO_INCREMENT COMMENT '组件ID',
  `tool_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件名称',
  `tool_logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件logo',
  `tool_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件URL',
  `tool_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '组件描述',
  `tool_debug` int DEFAULT NULL COMMENT '调试状态,0:未调试 1:通过 2:调试未通过',
  `tool_used` tinyint(1) DEFAULT NULL COMMENT '是否启用 0:未启用 1:启用',
  `tool_status` tinyint(1) DEFAULT NULL COMMENT '是否发布 0:未发布 1:发布',
  `tool_type` int DEFAULT NULL COMMENT '工具组件：0 算法组件：1 分析算法：2',
  `user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户id',
  `tool_auth` int DEFAULT NULL COMMENT '授权方式 0:不需要授权 1：Service 2:oauth_standard',
  `tool_star` tinyint(1) DEFAULT NULL COMMENT '收藏状态',
  `delete_flag` tinyint(1) DEFAULT NULL COMMENT '删除状态',
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  PRIMARY KEY (`tool_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能体组件表';

-- ----------------------------
-- Table structure for ai_quesion_number
-- ----------------------------
DROP TABLE IF EXISTS `ai_quesion_number`;
CREATE TABLE `ai_quesion_number` (
  `ai_question_number_id` bigint NOT NULL COMMENT 'Ai问数ID',
  `data_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据名称',
  `component_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件名称',
  `c` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据使用类 class',
  `m` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据使用方法 method',
  `w` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据过滤条件 where',
  `f` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Filter 过滤',
  `uri` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '接口地址',
  `r` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求方式',
  `delete_flag` tinyint DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `comment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件的详细介绍',
  `analysis_prompts` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '智能分析提示词',
  `after_question` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '追问数据',
  `flag_after_question` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '是否显示追问按钮',
  `drill_item` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '显示下钻维度数据',
  PRIMARY KEY (`ai_question_number_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能问数表';

-- ----------------------------
-- Table structure for aigc_app
-- ----------------------------
DROP TABLE IF EXISTS `aigc_app`;
CREATE TABLE `aigc_app` (
  `app_id` bigint NOT NULL AUTO_INCREMENT COMMENT '应用ID',
  `app_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '应用名称',
  `app_desc` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '应用描述',
  `app_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1-普通应用 2-上级应用',
  `icon_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Icon url',
  `auth_scheme_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限方案ID',
  `aigc_model_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '对应模型ID',
  `prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '提示词',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  `add_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `status` tinyint(1) DEFAULT '1' COMMENT '发布状态 1：已发布 0：停用',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `size` tinyint DEFAULT '5' COMMENT '命中最大数量',
  `mini_score` float(2,1) DEFAULT '1.4' COMMENT '阈值 1-2之间',
  `is_mixture` tinyint(1) DEFAULT '0' COMMENT '是否混合检索 1：是 0：否',
  `is_graph` tinyint(1) DEFAULT '1' COMMENT '是否启用知识图谱 1：启用 0：未启用',
  `welcome_content` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '应用欢迎语',
  `classify_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '命中关键词',
  `classify_prompt` text COLLATE utf8mb4_general_ci,
  `classify_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `classify_priority` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0-手动，1-向量，2-大模型',
  `ques_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '问题提取开启',
  `ques_prompt` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '问题提取描述',
  `ques_keywords` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '问题提取关键词',
  PRIMARY KEY (`app_id`) USING BTREE,
  KEY `auth_scheme_id` (`auth_scheme_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  KEY `aigc_model_id` (`aigc_model_id`) USING BTREE,
  CONSTRAINT `aigc_app_ibfk_2` FOREIGN KEY (`auth_scheme_id`) REFERENCES `content_auth_scheme` (`auth_scheme_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `aigc_app_ibfk_3` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `aigc_app_ibfk_4` FOREIGN KEY (`aigc_model_id`) REFERENCES `aigc_model` (`aigc_model_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4949310823815188482 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='aigc 应用表';

-- ----------------------------
-- Table structure for aigc_app_prompt
-- ----------------------------
DROP TABLE IF EXISTS `aigc_app_prompt`;
CREATE TABLE `aigc_app_prompt` (
  `prompt_id` bigint NOT NULL AUTO_INCREMENT COMMENT '提示词变量ID',
  `app_id` bigint DEFAULT NULL COMMENT '应用ID',
  `prompt_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '提示词变量类型 text:文本 paragraph：段落 drop_down:下拉\n\n',
  `prompt_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '变量关键字',
  `field_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关键字名称',
  `is_required` tinyint(1) DEFAULT '1' COMMENT '是否必填 1：是 0：否',
  `prompt_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'Type 为下拉时必填',
  `placeholeder` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'placeholeder',
  `sort` int DEFAULT '99' COMMENT '排序',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0：未删除 1：已删除',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`prompt_id`) USING BTREE,
  KEY `app_id` (`app_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `aigc_app_prompt_ibfk_1` FOREIGN KEY (`app_id`) REFERENCES `aigc_app` (`app_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `aigc_app_prompt_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4926763170296500226 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='应用提示词变量表';

-- ----------------------------
-- Table structure for aigc_app_session
-- ----------------------------
DROP TABLE IF EXISTS `aigc_app_session`;
CREATE TABLE `aigc_app_session` (
  `session_id` bigint NOT NULL AUTO_INCREMENT COMMENT '会话ID',
  `app_id` bigint DEFAULT NULL COMMENT '应用ID',
  `session_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '会话名称',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `prompt_list` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '提示词变量',
  `is_top` tinyint(1) DEFAULT '0' COMMENT '是否置顶 1：是 0：否',
  `session_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'search' COMMENT '会话类型 search：定制检索  report：智能报表',
  `report_date` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '智能报告日期',
  PRIMARY KEY (`session_id`) USING BTREE,
  KEY `app_id` (`app_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `aigc_app_session_ibfk_1` FOREIGN KEY (`app_id`) REFERENCES `aigc_app` (`app_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `aigc_app_session_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4949940968229965826 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='ai应用会话表';

-- ----------------------------
-- Table structure for aigc_content
-- ----------------------------
DROP TABLE IF EXISTS `aigc_content`;
CREATE TABLE `aigc_content` (
  `aigc_content_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'aigc产生的素材',
  `aigc_model_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `prompt_words` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT 'aigc的核心prompt',
  `desc_words` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT 'aigc生成的desc',
  `bgn_time` datetime DEFAULT NULL COMMENT '模型开始生成时间',
  `end_time` datetime DEFAULT NULL COMMENT '模型结束返回时间',
  `pic_url_array` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '模型生成照片地址数组',
  `ast_desc_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'tags描述id',
  `batch_count` int NOT NULL DEFAULT '1' COMMENT '训练生成几批图片',
  `batch_num` int NOT NULL DEFAULT '1' COMMENT '每批含有多少图片',
  `pic_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片类型id',
  `pic_resolution_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片分辨率id',
  `pic_style_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片风格id',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片制作者id',
  PRIMARY KEY (`aigc_content_id`) USING BTREE,
  KEY `fk_aigc_content_aigc_model_1` (`aigc_model_id`) USING BTREE,
  KEY `fk_aigc_content_aigc_tags_desc_1` (`ast_desc_id`) USING BTREE,
  KEY `fk_aigc_content_dict_pic_type_1` (`pic_type_id`) USING BTREE,
  KEY `fk_aigc_content_dict_pic_resolution_1` (`pic_resolution_id`) USING BTREE,
  KEY `fk_aigc_content_dict_pic_style_1` (`pic_style_id`) USING BTREE,
  KEY `fk_aigc_content_touchpoint_user_1` (`tp_user_id`) USING BTREE,
  CONSTRAINT `aigc_content_ibfk_1` FOREIGN KEY (`aigc_model_id`) REFERENCES `aigc_model` (`aigc_model_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `aigc_content_ibfk_2` FOREIGN KEY (`ast_desc_id`) REFERENCES `aigc_tags_desc` (`ast_desc_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `aigc_content_ibfk_3` FOREIGN KEY (`pic_resolution_id`) REFERENCES `dict_pic_resolution` (`pic_resolution_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `aigc_content_ibfk_4` FOREIGN KEY (`pic_style_id`) REFERENCES `dict_pic_style` (`pic_style_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `aigc_content_ibfk_5` FOREIGN KEY (`pic_type_id`) REFERENCES `dict_pic_type` (`pic_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `aigc_content_ibfk_6` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='aigc内容生成表';

-- ----------------------------
-- Table structure for aigc_knowledge
-- ----------------------------
DROP TABLE IF EXISTS `aigc_knowledge`;
CREATE TABLE `aigc_knowledge` (
  `knowledge_id` bigint NOT NULL COMMENT '知识库ID',
  `knowledge_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '知识库名称',
  `knowledge_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '知识库描述',
  `status` tinyint(1) DEFAULT '1' COMMENT '1：启用 0：停用',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加人',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `icon_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Icon url',
  `aigc_model_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'aigc_model_id',
  `auth_scheme_id` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限ID',
  `scene_id` int DEFAULT NULL,
  PRIMARY KEY (`knowledge_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  KEY `aigc_model_id` (`aigc_model_id`) USING BTREE,
  KEY `aigc_knowledge_content_auth_scheme_auth_scheme_id_fk` (`auth_scheme_id`),
  KEY `aigc_knowledge_ibfk_3` (`scene_id`),
  CONSTRAINT `aigc_knowledge_content_auth_scheme_auth_scheme_id_fk` FOREIGN KEY (`auth_scheme_id`) REFERENCES `content_auth_scheme` (`auth_scheme_id`),
  CONSTRAINT `aigc_knowledge_ibfk_1` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `aigc_knowledge_ibfk_2` FOREIGN KEY (`aigc_model_id`) REFERENCES `aigc_model` (`aigc_model_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `aigc_knowledge_ibfk_3` FOREIGN KEY (`scene_id`) REFERENCES `agent_scene` (`scene_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识库表';

-- ----------------------------
-- Table structure for aigc_knowledge_docu_chunk_images
-- ----------------------------
DROP TABLE IF EXISTS `aigc_knowledge_docu_chunk_images`;
CREATE TABLE `aigc_knowledge_docu_chunk_images` (
  `chunk_image_id` bigint NOT NULL COMMENT '主键id',
  `chunk_id` bigint DEFAULT NULL COMMENT '切片id',
  `image_url` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图片url',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`chunk_image_id`),
  KEY `aigc_knowledge_docu_chunk_images_chunk_id_index` (`chunk_id`),
  CONSTRAINT `aigc_docu_chunk_images_aigc_chunk_chunk_id_fk` FOREIGN KEY (`chunk_id`) REFERENCES `aigc_knowledge_document_chunk` (`chunk_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='切片-图片表';

-- ----------------------------
-- Table structure for aigc_knowledge_document
-- ----------------------------
DROP TABLE IF EXISTS `aigc_knowledge_document`;
CREATE TABLE `aigc_knowledge_document` (
  `document_id` bigint NOT NULL AUTO_INCREMENT COMMENT '文档ID',
  `doc_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文档名称',
  `doc_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文档类型',
  `chunk_size` int DEFAULT NULL COMMENT '切片数',
  `knowledge_id` bigint DEFAULT NULL COMMENT '知识库ID',
  `status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'TODO,RUNNING,FAILED,FINISHED',
  `doc_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文档地址',
  `result` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '结果',
  `tokens` int DEFAULT NULL COMMENT 'tokens',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `doc_size` int DEFAULT NULL COMMENT '大小',
  `content_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联素材ID',
  `abstract` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '摘要',
  `description` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文档描述',
  `graph_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '图谱内容',
  PRIMARY KEY (`document_id`) USING BTREE,
  KEY `knowledge_id` (`knowledge_id`) USING BTREE,
  KEY `content_id` (`content_id`) USING BTREE,
  CONSTRAINT `aigc_knowledge_document_ibfk_1` FOREIGN KEY (`knowledge_id`) REFERENCES `aigc_knowledge` (`knowledge_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `aigc_knowledge_document_ibfk_2` FOREIGN KEY (`content_id`) REFERENCES `content_info` (`content_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4949614250717876226 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识库文档表';

-- ----------------------------
-- Table structure for aigc_knowledge_document_chunk
-- ----------------------------
DROP TABLE IF EXISTS `aigc_knowledge_document_chunk`;
CREATE TABLE `aigc_knowledge_document_chunk` (
  `chunk_id` bigint NOT NULL AUTO_INCREMENT COMMENT '切片ID',
  `doc_id` bigint DEFAULT NULL COMMENT '文档ID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '内容',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `character_count` int DEFAULT '0' COMMENT '字符数',
  `hit_count` int DEFAULT '0' COMMENT '命中数',
  `tokens` int DEFAULT '0' COMMENT 'Tokens',
  `status` tinyint(1) DEFAULT '1' COMMENT '1：启用 0：停用',
  `result` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '向量化结果 TODO,RUNNING,FAILED,FINISHED',
  PRIMARY KEY (`chunk_id`) USING BTREE,
  KEY `doc_id` (`doc_id`) USING BTREE,
  CONSTRAINT `aigc_knowledge_document_chunk_ibfk_1` FOREIGN KEY (`doc_id`) REFERENCES `aigc_knowledge_document` (`document_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4942366274677251653 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识库文档切片表';

-- ----------------------------
-- Table structure for aigc_menu_supplement
-- ----------------------------
DROP TABLE IF EXISTS `aigc_menu_supplement`;
CREATE TABLE `aigc_menu_supplement` (
  `ms_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `view_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单id',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序字段',
  `tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '菜单标签',
  `model_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '菜单支持的模型id，多个使用,分割',
  `hello` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '打开对话时的默认打招呼语句',
  `prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '模型提示词',
  PRIMARY KEY (`ms_id`) USING BTREE,
  KEY `aigc_menu_supplement_function_view_function_view_id_fk` (`view_id`) USING BTREE,
  CONSTRAINT `aigc_menu_supplement_function_view_function_view_id_fk` FOREIGN KEY (`view_id`) REFERENCES `function_view` (`function_view_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='AI菜单补充字段';

-- ----------------------------
-- Table structure for aigc_model
-- ----------------------------
DROP TABLE IF EXISTS `aigc_model`;
CREATE TABLE `aigc_model` (
  `aigc_model_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'aigc模型id',
  `aigc_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型唯一编码',
  `model_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'aigc模型名称',
  `model_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'aigc的请求地址',
  `model_icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型icon',
  `model_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型路径',
  `status` tinyint(1) DEFAULT '1' COMMENT '1:显示 0：隐藏',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `model_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型编码',
  `use_range` tinyint(1) DEFAULT '-1' COMMENT '使用范围 -1：全部 1：素材生产 2：内容检索 3:生成向量',
  `dims` int DEFAULT '0' COMMENT '向量纬度',
  `aol` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'api' COMMENT 'api or local',
  PRIMARY KEY (`aigc_model_id`) USING BTREE,
  KEY `fk_aigc_model_dict_aigc_type_1` (`aigc_type_id`) USING BTREE,
  CONSTRAINT `fk_aigc_model_dict_aigc_type_1` FOREIGN KEY (`aigc_type_id`) REFERENCES `dict_aigc_type` (`aigc_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='aigc模型';

-- ----------------------------
-- Table structure for aigc_qa
-- ----------------------------
DROP TABLE IF EXISTS `aigc_qa`;
CREATE TABLE `aigc_qa` (
  `qa_id` bigint NOT NULL COMMENT 'qaID',
  `qa_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'qa名称',
  `qa_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '知识库描述',
  `status` tinyint(1) DEFAULT '1' COMMENT '1：启用 0：停用',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加人',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `icon_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Icon url',
  PRIMARY KEY (`qa_id`),
  KEY `tp_user_id` (`tp_user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识库表';

-- ----------------------------
-- Table structure for aigc_report
-- ----------------------------
DROP TABLE IF EXISTS `aigc_report`;
CREATE TABLE `aigc_report` (
  `report_id` int NOT NULL AUTO_INCREMENT COMMENT '报告菜单ID',
  `app_id` bigint DEFAULT NULL COMMENT '应用ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标题',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '编号',
  `level` int DEFAULT NULL COMMENT '级别',
  `content_prompt` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文本内容提示词',
  `content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文本内容',
  `content_charts` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图表内容',
  `charts_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图片类型',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `is_first` tinyint(1) DEFAULT '0' COMMENT '是否第一个 1：是 0：否',
  `sort` int DEFAULT '0' COMMENT '排序',
  `data_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据类型',
  `chatbi_data` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联chatbi data_name',
  `is_charts` tinyint(1) DEFAULT '0' COMMENT '是否有图表 1：有 0：没有',
  PRIMARY KEY (`report_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=186 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能报告配置表';

-- ----------------------------
-- Table structure for aigc_subject_tags
-- ----------------------------
DROP TABLE IF EXISTS `aigc_subject_tags`;
CREATE TABLE `aigc_subject_tags` (
  `ast_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'subject tags id',
  `tags_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主题标签名称',
  `subject_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '对应主题id',
  PRIMARY KEY (`ast_id`) USING BTREE,
  KEY `fk_aigc_subject_tags_dict_aigc_subject_1` (`subject_id`) USING BTREE,
  CONSTRAINT `aigc_subject_tags_ibfk_1` FOREIGN KEY (`subject_id`) REFERENCES `dict_aigc_subject` (`aigc_subject_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Aigc主题标签';

-- ----------------------------
-- Table structure for aigc_tags_desc
-- ----------------------------
DROP TABLE IF EXISTS `aigc_tags_desc`;
CREATE TABLE `aigc_tags_desc` (
  `ast_desc_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'tags描述表id',
  `ast_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'tags id',
  `sn` int NOT NULL DEFAULT '0' COMMENT 'tags desc的序列',
  `desc` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '无' COMMENT 'tags 描述内容',
  PRIMARY KEY (`ast_desc_id`) USING BTREE,
  KEY `fk_aigc_tags_desc_aigc_subject_tags_1` (`ast_id`) USING BTREE,
  CONSTRAINT `aigc_tags_desc_ibfk_1` FOREIGN KEY (`ast_id`) REFERENCES `aigc_subject_tags` (`ast_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='标签描述表 key words';

-- ----------------------------
-- Table structure for alembic_version
-- ----------------------------
DROP TABLE IF EXISTS `alembic_version`;
CREATE TABLE `alembic_version` (
  `version_num` varchar(32) COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`version_num`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for approve_record
-- ----------------------------
DROP TABLE IF EXISTS `approve_record`;
CREATE TABLE `approve_record` (
  `approved_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `approved_time` datetime DEFAULT NULL COMMENT '审批提交时间',
  `approve_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'NULL' COMMENT 'json格式，包含审批信息',
  `approve_template_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批模板id',
  `content_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材id',
  `sp_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批编号',
  `ect_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '名片编号',
  `cr_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '裂变雷达编号',
  `pt_id` bigint DEFAULT NULL COMMENT '合伙人编号',
  `type_key` enum('content','card','radar','partner') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'content' COMMENT '数据类型  content 素材；card 名片；radar 客户雷达',
  PRIMARY KEY (`approved_id`) USING BTREE,
  KEY `fk_approve_record_content_info_1` (`content_id`) USING BTREE,
  KEY `rel_card_radar` (`ect_id`) USING BTREE,
  KEY `rel_fission_radar` (`cr_id`) USING BTREE,
  KEY `fk_client_partner` (`pt_id`) USING BTREE,
  CONSTRAINT `approve_record_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `content_info` (`content_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `approve_record_ibfk_2` FOREIGN KEY (`pt_id`) REFERENCES `client_partner` (`partner_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `approve_record_ibfk_3` FOREIGN KEY (`ect_id`) REFERENCES `electric_card_template` (`ect_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `approve_record_ibfk_4` FOREIGN KEY (`cr_id`) REFERENCES `client_radar` (`client_radar_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='审批记录';

-- ----------------------------
-- Table structure for approve_template
-- ----------------------------
DROP TABLE IF EXISTS `approve_template`;
CREATE TABLE `approve_template` (
  `template_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `approve_rule` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'json,流程规则',
  `template_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批模板名称',
  `template_info` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模板信息，json数据',
  `departm_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业微信部门id，触发条件',
  `tags_ids` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '对应触发的标签',
  `common_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '1:通用触发流程；\n0:非通用触发流程',
  PRIMARY KEY (`template_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='素材审批模板表';

-- ----------------------------
-- Table structure for banner
-- ----------------------------
DROP TABLE IF EXISTS `banner`;
CREATE TABLE `banner` (
  `banner_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'banner ID',
  `banner_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Banner 名称',
  `banner_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'banner 图片',
  `banner_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Banner 位置 home：首页  course：课程',
  `jump_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'banner 类型 no：不跳转 mp:公众号 channels：视频号 mini:小程序内',
  `jump_rel_id` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '跳转 关联公众号 or视频ID',
  `jump_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '小程序内跳转',
  `status` tinyint DEFAULT '1' COMMENT '显示状态 1：显示 0：不显示',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加人',
  `sort` int DEFAULT '99999' COMMENT '排序',
  `channels_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '视频号ID',
  PRIMARY KEY (`banner_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  KEY `channels_id` (`channels_id`) USING BTREE,
  CONSTRAINT `banner_ibfk_1` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `banner_ibfk_2` FOREIGN KEY (`channels_id`) REFERENCES `dict_wechat_channels` (`channels_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4867670629391798274 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Banner 表';

-- ----------------------------
-- Table structure for channel_group
-- ----------------------------
DROP TABLE IF EXISTS `channel_group`;
CREATE TABLE `channel_group` (
  `channel_group_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `group_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '渠道组名称',
  `group_sn` int DEFAULT NULL COMMENT '渠道组排序',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志位：0:未删除；1:已删除',
  PRIMARY KEY (`channel_group_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='渠道组表';

-- ----------------------------
-- Table structure for chatbi_board
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_board`;
CREATE TABLE `chatbi_board` (
  `board_id` bigint NOT NULL AUTO_INCREMENT COMMENT '看板ID',
  `board_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '看板名称',
  `create_tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `graph_location_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '关联的多个图表位置信息',
  PRIMARY KEY (`board_id`) USING BTREE,
  KEY `create_tp_user_id` (`create_tp_user_id`) USING BTREE,
  KEY `update_tp_user_id` (`update_tp_user_id`) USING BTREE,
  CONSTRAINT `chatbi_board_ibfk_1` FOREIGN KEY (`create_tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `chatbi_board_ibfk_2` FOREIGN KEY (`update_tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4947005527822438402 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='chatbi 看板表';

-- ----------------------------
-- Table structure for chatbi_board_detail
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_board_detail`;
CREATE TABLE `chatbi_board_detail` (
  `board_detail_id` bigint NOT NULL COMMENT '看板关联图表',
  `board_id` bigint DEFAULT NULL COMMENT '看板ID',
  `data_graph_id` bigint DEFAULT NULL COMMENT '图表ID',
  `sort` int DEFAULT '9999' COMMENT '排序',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0：未删除 1：已删除',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`board_detail_id`) USING BTREE,
  KEY `board_id` (`board_id`) USING BTREE,
  KEY `data_graph_id` (`data_graph_id`) USING BTREE,
  CONSTRAINT `chatbi_board_detail_ibfk_1` FOREIGN KEY (`board_id`) REFERENCES `chatbi_board` (`board_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `chatbi_board_detail_ibfk_2` FOREIGN KEY (`data_graph_id`) REFERENCES `chatbi_data_graph` (`data_graph_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Chatbi 看板关联图表表';

-- ----------------------------
-- Table structure for chatbi_data_graph
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_data_graph`;
CREATE TABLE `chatbi_data_graph` (
  `data_graph_id` bigint NOT NULL COMMENT '数据图表id',
  `data_graph_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据图表名称',
  `data_view_id` bigint DEFAULT NULL COMMENT '数据视图id',
  `data_graph_type` int DEFAULT NULL COMMENT '数据图表类型编号，1: 面积图, 2: 柱状图, 3: 折线图, 4: 扇形图, 5: 散点图, 6: 条形图',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `add_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加人',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `graph_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图表url',
  `graph_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图表描述',
  `analysis_prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '智能分析提示词',
  `component_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '前端组件',
  `w` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '条件',
  `f` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `data_type` tinyint(1) DEFAULT '1' COMMENT '图表类型 1：视图配置 2：兼容ai question',
  `charts_show_type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT 'charts_bi' COMMENT 'chatbi展示样式 charts_bi：图表+智能分析 only_charts：仅图表 only_bi：仅智能分析',
  `is_vector` tinyint(1) DEFAULT '0' COMMENT '是否已经向量化 1：是 0：否',
  `report_prompt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '报告提示词',
  `graph_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '工具名称',
  PRIMARY KEY (`data_graph_id`) USING BTREE,
  KEY `data_view_id` (`data_view_id`) USING BTREE,
  KEY `add_user_id` (`add_user_id`) USING BTREE,
  KEY `update_user_id` (`update_user_id`) USING BTREE,
  KEY `data_graph_type` (`data_graph_type`) USING BTREE,
  CONSTRAINT `chatbi_data_graph_ibfk_1` FOREIGN KEY (`data_view_id`) REFERENCES `chatbi_data_view` (`data_view_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `chatbi_data_graph_ibfk_2` FOREIGN KEY (`add_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `chatbi_data_graph_ibfk_3` FOREIGN KEY (`update_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `chatbi_data_graph_ibfk_4` FOREIGN KEY (`data_graph_type`) REFERENCES `chatbi_dict_graph_type` (`graph_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数据图表配置表';

-- ----------------------------
-- Table structure for chatbi_data_graph_ask
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_data_graph_ask`;
CREATE TABLE `chatbi_data_graph_ask` (
  `graph_ask_id` bigint NOT NULL AUTO_INCREMENT COMMENT '追问ID',
  `data_graph_id` bigint DEFAULT NULL COMMENT '图表ID',
  `ask_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '追问问题',
  `ask_prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '追问提示词',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`graph_ask_id`) USING BTREE,
  KEY `data_graph_id` (`data_graph_id`) USING BTREE,
  CONSTRAINT `chatbi_data_graph_ask_ibfk_1` FOREIGN KEY (`data_graph_id`) REFERENCES `chatbi_data_graph` (`data_graph_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4922087388286554114 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='图表追问配置表';

-- ----------------------------
-- Table structure for chatbi_data_graph_copy1
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_data_graph_copy1`;
CREATE TABLE `chatbi_data_graph_copy1` (
  `data_graph_id` bigint NOT NULL COMMENT '数据图表id',
  `data_graph_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据图表名称',
  `data_view_id` bigint DEFAULT NULL COMMENT '数据视图id',
  `data_graph_type` int DEFAULT NULL COMMENT '数据图表类型编号，1: 面积图, 2: 柱状图, 3: 折线图, 4: 扇形图, 5: 散点图, 6: 条形图',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `add_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加人',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `graph_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图表url',
  `graph_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图表描述',
  `analysis_prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '智能分析提示词',
  `component_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '前端组件',
  `w` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '条件',
  `f` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `data_type` tinyint(1) DEFAULT '1' COMMENT '图表类型 1：视图配置 2：兼容ai question',
  `charts_show_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'charts_bi' COMMENT 'chatbi展示样式 charts_bi：图表+智能分析 only_charts：仅图表 only_bi：仅智能分析',
  `is_vector` tinyint(1) DEFAULT '0' COMMENT '是否已经向量化 1：是 0：否',
  `report_prompt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '报告提示词',
  `graph_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '工具名称',
  PRIMARY KEY (`data_graph_id`) USING BTREE,
  KEY `data_view_id` (`data_view_id`) USING BTREE,
  KEY `add_user_id` (`add_user_id`) USING BTREE,
  KEY `update_user_id` (`update_user_id`) USING BTREE,
  KEY `data_graph_type` (`data_graph_type`) USING BTREE,
  CONSTRAINT `chatbi_data_graph_copy1_ibfk_1` FOREIGN KEY (`data_view_id`) REFERENCES `chatbi_data_view` (`data_view_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `chatbi_data_graph_copy1_ibfk_2` FOREIGN KEY (`add_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `chatbi_data_graph_copy1_ibfk_3` FOREIGN KEY (`update_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `chatbi_data_graph_copy1_ibfk_4` FOREIGN KEY (`data_graph_type`) REFERENCES `chatbi_dict_graph_type` (`graph_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数据图表配置表';

-- ----------------------------
-- Table structure for chatbi_data_graph_setting
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_data_graph_setting`;
CREATE TABLE `chatbi_data_graph_setting` (
  `graph_setting_id` bigint NOT NULL AUTO_INCREMENT COMMENT '图表设置ID',
  `data_graph_id` bigint DEFAULT NULL COMMENT '图表ID',
  `setting_type_id` int DEFAULT NULL COMMENT '设置类型ID',
  `column_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '列名',
  `column_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '列code',
  `column_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '列类型',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `empty_style` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '空值样式',
  `data_format_id` int DEFAULT NULL COMMENT '数据展示样式ID',
  `agg_style_id` int DEFAULT NULL COMMENT '聚合方式ID',
  `sort_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '排序方式 asc 正序 desc 倒序 无排序',
  `filter_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '条件类型 or：或 and：与 single：单条件',
  `drill_down_type_id` int DEFAULT NULL COMMENT '下钻类型ID',
  `fmt_date` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '下钻时间格式',
  `drill_down_default` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '下钻条件默认值',
  `show_number` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '展示条数',
  PRIMARY KEY (`graph_setting_id`) USING BTREE,
  KEY `data_graph_id` (`data_graph_id`) USING BTREE,
  KEY `setting_type_id` (`setting_type_id`) USING BTREE,
  KEY `data_format_id` (`data_format_id`) USING BTREE,
  KEY `agg_style_id` (`agg_style_id`) USING BTREE,
  KEY `drill_down_type_id` (`drill_down_type_id`) USING BTREE,
  CONSTRAINT `chatbi_data_graph_setting_ibfk_1` FOREIGN KEY (`data_graph_id`) REFERENCES `chatbi_data_graph` (`data_graph_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `chatbi_data_graph_setting_ibfk_2` FOREIGN KEY (`setting_type_id`) REFERENCES `chatbi_dict_graph_setting` (`setting_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `chatbi_data_graph_setting_ibfk_3` FOREIGN KEY (`data_format_id`) REFERENCES `chatbi_dict_data_format` (`data_format_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `chatbi_data_graph_setting_ibfk_4` FOREIGN KEY (`agg_style_id`) REFERENCES `chatbi_dict_agg_style` (`agg_style_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `chatbi_data_graph_setting_ibfk_5` FOREIGN KEY (`drill_down_type_id`) REFERENCES `chatbi_dict_drill_down_type` (`drill_down_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4944537408336039938 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='图表设置表';

-- ----------------------------
-- Table structure for chatbi_data_source
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_data_source`;
CREATE TABLE `chatbi_data_source` (
  `data_source_id` bigint NOT NULL COMMENT '数据源id',
  `data_source_type` tinyint(1) DEFAULT NULL COMMENT '数据源类型编号，1: 本地文件, 2: 数据库',
  `data_source_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据源名称',
  `db_type` int DEFAULT NULL COMMENT '数据库类型：1: PostgreSQL 2:MySQL',
  `db_host` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据库主机地址',
  `db_port` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据库服务端口',
  `db_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据库名',
  `db_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据库用户名',
  `db_password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据库密码',
  `schema_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'public',
  `is_ssl` tinyint(1) DEFAULT NULL COMMENT '是否启用SSL连接,0:不启用,1:启用',
  `is_ssh` tinyint(1) DEFAULT NULL COMMENT '是否通过SSH连接,0:不启用,1:启用',
  `ssh_host` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据库ssh主机地址',
  `ssh_port` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'ssh主机端口',
  `ssh_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'ssh主机用户名',
  `ssh_password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'ssh主机密码',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件名称',
  `file_describe` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件型数据源描述',
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件路径',
  `file_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件类型 excel csv',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `add_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加人',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `file_upload_status` tinyint(1) DEFAULT '1' COMMENT '文件上传状态 0：失败 1:成功 2:上传中',
  `table_count` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`data_source_id`) USING BTREE,
  KEY `add_user_id` (`add_user_id`) USING BTREE,
  KEY `update_user_id` (`update_user_id`) USING BTREE,
  KEY `db_type` (`db_type`) USING BTREE,
  CONSTRAINT `chatbi_data_source_ibfk_1` FOREIGN KEY (`add_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `chatbi_data_source_ibfk_2` FOREIGN KEY (`update_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `chatbi_data_source_ibfk_3` FOREIGN KEY (`db_type`) REFERENCES `chatbi_dict_db_type` (`db_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数据源配置表';

-- ----------------------------
-- Table structure for chatbi_data_view
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_data_view`;
CREATE TABLE `chatbi_data_view` (
  `data_view_id` bigint NOT NULL COMMENT '数据视图id',
  `date_view_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据视图名称',
  `data_source_id` bigint DEFAULT NULL COMMENT '数据源id',
  `data_source_table` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据源表名',
  `status` tinyint(1) DEFAULT '1' COMMENT '1:启用 0：停用',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `add_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加人',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `data_count` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`data_view_id`) USING BTREE,
  KEY `data_source_id` (`data_source_id`) USING BTREE,
  KEY `add_user_id` (`add_user_id`) USING BTREE,
  KEY `update_user_id` (`update_user_id`) USING BTREE,
  CONSTRAINT `chatbi_data_view_ibfk_1` FOREIGN KEY (`data_source_id`) REFERENCES `chatbi_data_source` (`data_source_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `chatbi_data_view_ibfk_2` FOREIGN KEY (`add_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `chatbi_data_view_ibfk_3` FOREIGN KEY (`update_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数据视图配置表';

-- ----------------------------
-- Table structure for chatbi_data_view_column
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_data_view_column`;
CREATE TABLE `chatbi_data_view_column` (
  `column_id` bigint NOT NULL AUTO_INCREMENT COMMENT '自定义列id',
  `data_view_id` bigint DEFAULT NULL COMMENT '关联视图ID',
  `column_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '自定义列名称',
  `formula_content` varchar(1000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '计算公式',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `delete_flag` tinyint DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`column_id`),
  KEY `data_view_id` (`data_view_id`),
  CONSTRAINT `chatbi_data_view_column_ibfk_1` FOREIGN KEY (`data_view_id`) REFERENCES `chatbi_data_view` (`data_view_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4931103901899296770 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='视图自定义列表';

-- ----------------------------
-- Table structure for chatbi_dict_agg_style
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_dict_agg_style`;
CREATE TABLE `chatbi_dict_agg_style` (
  `agg_style_id` int NOT NULL AUTO_INCREMENT COMMENT '聚合方式ID',
  `agg_style_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '聚合方式名称',
  `agg_style_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '聚合方式编码',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `sql_style_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Sql聚合函数',
  PRIMARY KEY (`agg_style_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数据聚合方式字典表';

-- ----------------------------
-- Table structure for chatbi_dict_data_format
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_dict_data_format`;
CREATE TABLE `chatbi_dict_data_format` (
  `data_format_id` int NOT NULL AUTO_INCREMENT COMMENT '数据展示方式ID',
  `data_format` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据展示方式',
  `data_format_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据展示方式名称',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`data_format_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数据展示样式字典表';

-- ----------------------------
-- Table structure for chatbi_dict_db_type
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_dict_db_type`;
CREATE TABLE `chatbi_dict_db_type` (
  `db_type_id` int NOT NULL COMMENT '数据库类型ID',
  `db_type_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据类型名称',
  `icon_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'icon',
  `sort` int DEFAULT NULL COMMENT '排序',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`db_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数据库类型字典表';

-- ----------------------------
-- Table structure for chatbi_dict_drill_down_type
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_dict_drill_down_type`;
CREATE TABLE `chatbi_dict_drill_down_type` (
  `drill_down_type_id` int NOT NULL AUTO_INCREMENT COMMENT '下钻类型ID',
  `drill_down_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '下钻类型名称',
  `drill_down_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '下钻类型code',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`drill_down_type_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='下钻类型配置表';

-- ----------------------------
-- Table structure for chatbi_dict_filter_type
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_dict_filter_type`;
CREATE TABLE `chatbi_dict_filter_type` (
  `filter_type_id` int NOT NULL AUTO_INCREMENT COMMENT '过滤条件类型ID',
  `filter_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '过滤条件名称',
  `filter_type_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '过滤条件编码',
  `sort` int DEFAULT NULL COMMENT '排序',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`filter_type_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Chatbi 图表过滤条件字典表';

-- ----------------------------
-- Table structure for chatbi_dict_graph_empty
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_dict_graph_empty`;
CREATE TABLE `chatbi_dict_graph_empty` (
  `empty_id` int NOT NULL AUTO_INCREMENT COMMENT '空样式ID',
  `empty_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '展示名称',
  `empty_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '展示code',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`empty_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Chatbi 图表空数据展示字典表';

-- ----------------------------
-- Table structure for chatbi_dict_graph_setting
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_dict_graph_setting`;
CREATE TABLE `chatbi_dict_graph_setting` (
  `setting_type_id` int NOT NULL AUTO_INCREMENT COMMENT '图表设置类型ID',
  `setting_type_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图表设置类型名称',
  `setting_type_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图表设置类型code',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `field_number` int DEFAULT NULL COMMENT '允许字段数量',
  `rel_dict` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联字典',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认显示 1：是 0：否',
  `is_required` tinyint(1) DEFAULT '0' COMMENT '是否必须 1：是 0：否',
  PRIMARY KEY (`setting_type_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='图表设置类型字典表';

-- ----------------------------
-- Table structure for chatbi_dict_graph_type
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_dict_graph_type`;
CREATE TABLE `chatbi_dict_graph_type` (
  `graph_type_id` int NOT NULL AUTO_INCREMENT COMMENT '图表类型ID',
  `graph_type_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图表类型名称',
  `icon_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图表icon',
  `sort` int DEFAULT NULL COMMENT '排序',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `graph_type_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图表类型编码',
  PRIMARY KEY (`graph_type_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='图表类型字典';

-- ----------------------------
-- Table structure for chatbi_index
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_index`;
CREATE TABLE `chatbi_index` (
  `index_id` bigint NOT NULL AUTO_INCREMENT COMMENT '指标ID',
  `index_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '指标名称',
  `index_desc` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '指标描述',
  `data_graph_id` bigint DEFAULT NULL COMMENT '图表ID',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间\n',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `update_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人\n',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0：未删除 1：已删除',
  PRIMARY KEY (`index_id`),
  KEY `data_graph_id` (`data_graph_id`),
  KEY `create_user_id` (`create_user_id`),
  KEY `updat_user_id` (`update_user_id`),
  CONSTRAINT `chatbi_index_ibfk_1` FOREIGN KEY (`data_graph_id`) REFERENCES `chatbi_data_graph` (`data_graph_id`),
  CONSTRAINT `chatbi_index_ibfk_2` FOREIGN KEY (`create_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`),
  CONSTRAINT `chatbi_index_ibfk_3` FOREIGN KEY (`update_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=126 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='chatbi 指标表';

-- ----------------------------
-- Table structure for chatbi_rel_graph_setting_filter
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_rel_graph_setting_filter`;
CREATE TABLE `chatbi_rel_graph_setting_filter` (
  `rel_graph_setting_id` bigint NOT NULL AUTO_INCREMENT COMMENT '过滤条件关联ID',
  `graph_setting_id` bigint DEFAULT NULL COMMENT '图标设置ID',
  `filter_type_id` int DEFAULT NULL COMMENT '过滤条件类型ID',
  `filter_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '条件对应值',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`rel_graph_setting_id`) USING BTREE,
  KEY `graph_setting_id` (`graph_setting_id`) USING BTREE,
  KEY `filter_type_id` (`filter_type_id`) USING BTREE,
  CONSTRAINT `chatbi_rel_graph_setting_filter_ibfk_1` FOREIGN KEY (`graph_setting_id`) REFERENCES `chatbi_data_graph_setting` (`graph_setting_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `chatbi_rel_graph_setting_filter_ibfk_2` FOREIGN KEY (`filter_type_id`) REFERENCES `chatbi_dict_filter_type` (`filter_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4930777729600589826 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='图表设置过滤条件关联表';

-- ----------------------------
-- Table structure for chatbi_rel_graph_tags
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_rel_graph_tags`;
CREATE TABLE `chatbi_rel_graph_tags` (
  `rel_tag_id` bigint NOT NULL COMMENT '关联ID',
  `data_graph_id` bigint DEFAULT NULL COMMENT '图表ID',
  `tags_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签ID',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除  1：已删除',
  PRIMARY KEY (`rel_tag_id`) USING BTREE,
  KEY `data_graph_id` (`data_graph_id`) USING BTREE,
  KEY `tags_id` (`tags_id`) USING BTREE,
  CONSTRAINT `chatbi_rel_graph_tags_ibfk_1` FOREIGN KEY (`data_graph_id`) REFERENCES `chatbi_data_graph` (`data_graph_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `chatbi_rel_graph_tags_ibfk_2` FOREIGN KEY (`tags_id`) REFERENCES `tags` (`tags_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Chatbi 图表关联标签';

-- ----------------------------
-- Table structure for chatbi_session
-- ----------------------------
DROP TABLE IF EXISTS `chatbi_session`;
CREATE TABLE `chatbi_session` (
  `session_id` bigint NOT NULL COMMENT '会话ID',
  `session_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '会话名称',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户ID',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `is_top` tinyint(1) DEFAULT '0' COMMENT '是否置顶 1：是 0：否',
  PRIMARY KEY (`session_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='chatbi会话记录表';

-- ----------------------------
-- Table structure for class_client
-- ----------------------------
DROP TABLE IF EXISTS `class_client`;
CREATE TABLE `class_client` (
  `class_client_id` bigint NOT NULL COMMENT '班级学员ID',
  `class_id` bigint DEFAULT NULL COMMENT '班级ID',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '学员ID',
  `study_times` int DEFAULT '0' COMMENT '已学时长',
  `status` tinyint DEFAULT '1' COMMENT '学员状态 1：正常 0：停止',
  `add_time` datetime DEFAULT NULL COMMENT '入班时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  `delete_flag` tinyint DEFAULT '0' COMMENT '1:已删除 0：未删除',
  PRIMARY KEY (`class_client_id`) USING BTREE,
  KEY `class_id` (`class_id`) USING BTREE,
  KEY `client_id` (`client_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `class_client_ibfk_1` FOREIGN KEY (`class_id`) REFERENCES `class_info` (`class_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `class_client_ibfk_2` FOREIGN KEY (`client_id`) REFERENCES `client_info` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `class_client_ibfk_3` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='班级学员表';

-- ----------------------------
-- Table structure for class_course
-- ----------------------------
DROP TABLE IF EXISTS `class_course`;
CREATE TABLE `class_course` (
  `class_course_id` bigint NOT NULL COMMENT '班级课程ID',
  `class_id` bigint DEFAULT NULL COMMENT '班级ID',
  `course_id` bigint DEFAULT NULL COMMENT '课程ID',
  `status` tinyint DEFAULT NULL COMMENT '课程状态 1：启用 0：关闭',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`class_course_id`) USING BTREE,
  KEY `class_id` (`class_id`) USING BTREE,
  KEY `course_id` (`course_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `class_course_ibfk_1` FOREIGN KEY (`class_id`) REFERENCES `class_info` (`class_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `class_course_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `course` (`course_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `class_course_ibfk_3` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='班级课程表';

-- ----------------------------
-- Table structure for class_info
-- ----------------------------
DROP TABLE IF EXISTS `class_info`;
CREATE TABLE `class_info` (
  `class_id` bigint NOT NULL COMMENT '班级ID',
  `class_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '班级名称',
  `start_date` date DEFAULT NULL COMMENT '开始日期',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `person_number` int DEFAULT NULL COMMENT '培训人数',
  `status` tinyint DEFAULT '1' COMMENT '状态1：开启 0：关闭',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  `delete_flag` tinyint DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `master_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '负责人',
  `remarks` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '班级备注',
  `qr_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '班级码',
  PRIMARY KEY (`class_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  KEY `class_info_ibfk_2` (`master_user_id`) USING BTREE,
  CONSTRAINT `class_info_ibfk_1` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `class_info_ibfk_2` FOREIGN KEY (`master_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='班级表';

-- ----------------------------
-- Table structure for client
-- ----------------------------
DROP TABLE IF EXISTS `client`;
CREATE TABLE `client` (
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户id',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `fission_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '裂变传播员工id',
  `industry_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户行业id',
  `company_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户公司id',
  `ww_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业微信的client_id',
  `delete_flag` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志位;0:未删除；1:已删除',
  `new_clue_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '是否新线索；0:不是；1:是',
  `rel_client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联客户id',
  `mini_openid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '小程序openid',
  `client_code` bigint DEFAULT '0' COMMENT '用户编号',
  `work_client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'work平台客户ID',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `unionid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '唯一ID',
  `question_type_id` int DEFAULT NULL COMMENT '行业智库中的角色分类',
  `competitor_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '行业智库中竞品IDs',
  PRIMARY KEY (`client_id`) USING BTREE,
  KEY `fk_client_touchpoint_user_1` (`tp_user_id`) USING BTREE,
  KEY `fk_client_dict_client_industry_1` (`industry_id`) USING BTREE,
  KEY `fk_client_company_1` (`company_id`) USING BTREE,
  KEY `question_type_id` (`question_type_id`) USING BTREE,
  CONSTRAINT `client_ibfk_1` FOREIGN KEY (`question_type_id`) REFERENCES `dict_question_type` (`question_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_ibfk_2` FOREIGN KEY (`company_id`) REFERENCES `company` (`company_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_ibfk_3` FOREIGN KEY (`industry_id`) REFERENCES `dict_client_industry` (`dict_client_industry_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_ibfk_4` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='列表客户表';

-- ----------------------------
-- Table structure for client_group
-- ----------------------------
DROP TABLE IF EXISTS `client_group`;
CREATE TABLE `client_group` (
  `group_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `chat_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信客户群id',
  `group_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信群名称',
  `owner` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '群主',
  `ower_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '群主tp_user_id',
  `client_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '客户列表',
  `valid_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '1:有效；0:废弃，删除，无效',
  `create_time` datetime DEFAULT NULL COMMENT '群创建时间',
  PRIMARY KEY (`group_id`) USING BTREE,
  KEY `fk_client_group_touchpoint_user_1` (`ower_user_id`) USING BTREE,
  CONSTRAINT `client_group_ibfk_1` FOREIGN KEY (`ower_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户群';

-- ----------------------------
-- Table structure for client_group_config
-- ----------------------------
DROP TABLE IF EXISTS `client_group_config`;
CREATE TABLE `client_group_config` (
  `client_group_config_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `scene` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场景。1 - 群的小程序插件;2 - 群的二维码插件\n',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系方式的备注信息，用于助记',
  `state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业自定义的state参数，用于区分不同的入群渠道。不超过30个UTF-8字符',
  `qr_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系二维码的URL或小程序插件的URL',
  `config_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '配置id',
  `add_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加人',
  `add_time` datetime DEFAULT NULL COMMENT '添加时间',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除标志位；0:未删除；1:已删除',
  PRIMARY KEY (`client_group_config_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户群配置-便于企业微信生成';

-- ----------------------------
-- Table structure for client_info
-- ----------------------------
DROP TABLE IF EXISTS `client_info`;
CREATE TABLE `client_info` (
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户id；跟client表是一对一关系',
  `nick_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户昵称',
  `weixin_openid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信openid',
  `real_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '真实姓名',
  `phone_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '电话号码',
  `region_id` int DEFAULT NULL COMMENT '区域id',
  `age` int DEFAULT NULL COMMENT '客户年龄',
  `gender` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '0：未知 1：男性 2：女性',
  `pic_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户头像pic url',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户备注',
  `company_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '公司名称',
  `integral` int NOT NULL DEFAULT '0' COMMENT '智邦币总数',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '地址',
  `client_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户名',
  `post_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '邮政编码',
  `client_level` int NOT NULL DEFAULT '1' COMMENT '等级',
  `work_client_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'Work 平台客户名称',
  `success_client` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '是否是work平台成功客户 1：是 0：否 空未知',
  `activity_flag` tinyint NOT NULL DEFAULT '1' COMMENT '活动标识 默认是1',
  `work_person_id` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'work平台persons ID',
  `work_client_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'Work平台客户类型',
  `source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户来源',
  `growth_value` int NOT NULL DEFAULT '0' COMMENT '成长值',
  `integral1` int NOT NULL DEFAULT '0' COMMENT '可提现智邦币',
  `integral2` int NOT NULL DEFAULT '0' COMMENT '可转赠智邦币',
  `growth_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '成长等级',
  `now_role` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '当前角色 1：客户 2：合伙人',
  `last_login` datetime DEFAULT NULL COMMENT '上次登录时间',
  `id_front_pic` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证正面',
  `id_back_pic` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证背面',
  `personal_pic` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '个人正面照',
  `graduation_pic` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '毕业证照片',
  `social_id_pic` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '社保照片',
  `student_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '0:不是学生；1:是学生',
  `id_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证地址',
  `current_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '当前地址',
  `nation` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '民族',
  `political_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '政治面貌；0:党员；1:团员；2:群众；3：民主党派',
  `college_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '毕业院校',
  `max_learn` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最高学历：1:初中及以下；2:高中；3:中专；4:大专；5:大学；6:研究生；7:博士及博士后；',
  `educations_history` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参与何种培训',
  `got_ca` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '获得证书',
  `company_master` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '单位负责人姓名',
  `company_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '单位电话',
  `id_card` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证号',
  `sp_status` tinyint DEFAULT '0' COMMENT '审核状态 0：默认状态 1：待审核 2：审核通过 3：驳回',
  PRIMARY KEY (`client_id`) USING BTREE,
  KEY `fk_client_info_dict_region_1` (`region_id`) USING BTREE,
  KEY `political_id` (`political_id`) USING BTREE,
  KEY `max_learn` (`max_learn`) USING BTREE,
  CONSTRAINT `client_info_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_info_ibfk_2` FOREIGN KEY (`region_id`) REFERENCES `dict_region` (`region_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_info_ibfk_3` FOREIGN KEY (`political_id`) REFERENCES `dict_client_political` (`political_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_info_ibfk_4` FOREIGN KEY (`max_learn`) REFERENCES `dict_client_max_learn` (`max_learn_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户信息表';

-- ----------------------------
-- Table structure for client_info_history
-- ----------------------------
DROP TABLE IF EXISTS `client_info_history`;
CREATE TABLE `client_info_history` (
  `client_history_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'client_info历史信息',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`client_history_id`) USING BTREE,
  KEY `fk_client_info_history_client_info_1` (`client_id`) USING BTREE,
  CONSTRAINT `client_info_history_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client_info` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户信息历史表';

-- ----------------------------
-- Table structure for client_integral
-- ----------------------------
DROP TABLE IF EXISTS `client_integral`;
CREATE TABLE `client_integral` (
  `integral_id` bigint NOT NULL COMMENT '积分ID',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户ID',
  `integral_type` tinyint NOT NULL COMMENT '积分类型 1：获得积分 2：积分兑换',
  `integral` int NOT NULL COMMENT '积分',
  `source_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '来源名称',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `work_id` int NOT NULL DEFAULT '0' COMMENT '关联work平台id',
  `exchange_id` bigint DEFAULT NULL COMMENT '积分兑换ID',
  `rel_task_id` bigint DEFAULT NULL COMMENT '任务明细ID',
  `detail_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'client' COMMENT '明细类型 client:普通客户  partner:合伙人',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人ID',
  `together_id` bigint DEFAULT NULL COMMENT '共创ID',
  `clue_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线索ID',
  `be_partner_id` bigint DEFAULT NULL COMMENT '被邀请签约的合伙人ID',
  PRIMARY KEY (`integral_id`) USING BTREE,
  KEY `client_id` (`client_id`) USING BTREE,
  KEY `rel_task_id` (`rel_task_id`) USING BTREE,
  KEY `exchange_id` (`exchange_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  KEY `together_id` (`together_id`) USING BTREE,
  KEY `clue_id` (`clue_id`) USING BTREE,
  KEY `be_partner_id` (`be_partner_id`) USING BTREE,
  CONSTRAINT `client_integral_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_integral_ibfk_2` FOREIGN KEY (`rel_task_id`) REFERENCES `rel_client_task` (`rel_task_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_integral_ibfk_3` FOREIGN KEY (`exchange_id`) REFERENCES `client_integral_exchange` (`exchange_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_integral_ibfk_4` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_integral_ibfk_5` FOREIGN KEY (`together_id`) REFERENCES `together_create` (`together_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_integral_ibfk_6` FOREIGN KEY (`clue_id`) REFERENCES `clue` (`clue_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_integral_ibfk_7` FOREIGN KEY (`be_partner_id`) REFERENCES `client_partner` (`partner_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='权益明细表';

-- ----------------------------
-- Table structure for client_integral_exchange
-- ----------------------------
DROP TABLE IF EXISTS `client_integral_exchange`;
CREATE TABLE `client_integral_exchange` (
  `exchange_id` bigint NOT NULL COMMENT '积分兑换ID',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户ID',
  `total_integral` int DEFAULT NULL COMMENT '使用总积分数',
  `receive_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '收件人姓名',
  `receive_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '收货地址',
  `receive_phone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '联系电话',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态 订单状态0：已取消 1：待发货 2：已发货 3已确认 4：已完成',
  `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '备注',
  `express_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '快递名称',
  `express_num` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '快递单号',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '下单时时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint DEFAULT '0' COMMENT '是否删除 0：未删除 1：已删除',
  `work_id` int NOT NULL DEFAULT '0' COMMENT '对应work平台ID',
  `exchange_type` tinyint NOT NULL DEFAULT '1' COMMENT '兑换类型 1：礼品兑换 2：智邦币提现 3：现金账户提现',
  `type_code` int NOT NULL DEFAULT '1' COMMENT '具体现金类型 1自荐客户；2合伙人抽成；3现金提现',
  `clue_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '自荐客户线索id',
  `partner_id` bigint DEFAULT NULL COMMENT '裂变合伙人id',
  PRIMARY KEY (`exchange_id`) USING BTREE,
  KEY `client_id` (`client_id`) USING BTREE,
  KEY `rel_clue_key` (`clue_id`) USING BTREE,
  KEY `rel_partner_key` (`partner_id`) USING BTREE,
  CONSTRAINT `client_integral_exchange_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_integral_exchange_ibfk_2` FOREIGN KEY (`clue_id`) REFERENCES `clue` (`clue_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_integral_exchange_ibfk_3` FOREIGN KEY (`partner_id`) REFERENCES `client_partner` (`partner_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='积分兑换表';

-- ----------------------------
-- Table structure for client_integral_exchange_detail
-- ----------------------------
DROP TABLE IF EXISTS `client_integral_exchange_detail`;
CREATE TABLE `client_integral_exchange_detail` (
  `exchange_detail_id` bigint NOT NULL COMMENT '积分兑换详情ID',
  `exchange_id` bigint DEFAULT NULL COMMENT '积分兑换ID',
  `gift_id` bigint DEFAULT NULL COMMENT '礼品ID',
  `gift_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '礼品名称',
  `exchange_number` int DEFAULT NULL COMMENT '兑换数量',
  `use_integral` int DEFAULT NULL COMMENT '消耗积分数量',
  `delete_flage` tinyint DEFAULT '0' COMMENT '0：未删除 1：已删除',
  PRIMARY KEY (`exchange_detail_id`) USING BTREE,
  KEY `exchange_id` (`exchange_id`) USING BTREE,
  CONSTRAINT `client_integral_exchange_detail_ibfk_1` FOREIGN KEY (`exchange_id`) REFERENCES `client_integral_exchange` (`exchange_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='积分兑换礼品明细表';

-- ----------------------------
-- Table structure for client_invite_record
-- ----------------------------
DROP TABLE IF EXISTS `client_invite_record`;
CREATE TABLE `client_invite_record` (
  `invite_id` bigint NOT NULL COMMENT '邀请记录ID',
  `invite_client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邀请人ID',
  `be_invite_client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '被邀请人ID',
  `status` tinyint DEFAULT NULL COMMENT '1:待审核 2：审核通过 3：驳回',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '邀请时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint DEFAULT '0' COMMENT '0：未删除 1：已删除',
  `work_id` int DEFAULT NULL COMMENT 'work平台ID',
  PRIMARY KEY (`invite_id`) USING BTREE,
  KEY `invite_client_id` (`invite_client_id`) USING BTREE,
  KEY `be_invite_client_id` (`be_invite_client_id`) USING BTREE,
  CONSTRAINT `client_invite_record_ibfk_1` FOREIGN KEY (`invite_client_id`) REFERENCES `client_info` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_invite_record_ibfk_2` FOREIGN KEY (`be_invite_client_id`) REFERENCES `client_info` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='邀请记录表';

-- ----------------------------
-- Table structure for client_opinion
-- ----------------------------
DROP TABLE IF EXISTS `client_opinion`;
CREATE TABLE `client_opinion` (
  `opinion_id` bigint NOT NULL COMMENT '建议ID',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户ID',
  `star` int NOT NULL DEFAULT '5' COMMENT '星',
  `tags_ids` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签ids',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '内容',
  `img_url1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片1',
  `img_url2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片2',
  `img_url3` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片3',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `reply` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '回复内容',
  `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0：未删除 1：已删除',
  `work_id` int NOT NULL DEFAULT '0' COMMENT 'work平台id',
  PRIMARY KEY (`opinion_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户吐槽表（建议表）';

-- ----------------------------
-- Table structure for client_partner
-- ----------------------------
DROP TABLE IF EXISTS `client_partner`;
CREATE TABLE `client_partner` (
  `partner_id` bigint NOT NULL COMMENT '合伙人ID',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户ID',
  `activity_flag` tinyint DEFAULT '2' COMMENT '活动首页标志位，默认是2',
  `client_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户名称',
  `phone_number` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号',
  `wechat_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信号',
  `position` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职位',
  `region` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地区',
  `company_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公司名称',
  `industry_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '行业ID',
  `client_type_id` int DEFAULT NULL COMMENT '合伙人类型ID',
  `intention_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '合作意向 可多选1,2,3',
  `channel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '推荐人/渠道：默认智邦管家',
  `is_zb` tinyint DEFAULT NULL COMMENT '是否购买智邦产品 1：是 0:否',
  `is_erp` tinyint DEFAULT NULL COMMENT '是否用过其他erp产品',
  `erp_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Erp产品名称',
  `link_time` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '方便沟通时间 1：随时 2：上午 3：下午 4：晚上 5：周末 6：其他',
  `status` tinyint DEFAULT NULL COMMENT '状态：1：待审批 2：审批通过 3：驳回',
  `invite_id` bigint DEFAULT NULL COMMENT '成功邀请人ID',
  `share_qr_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '邀请二维码',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '申请备注',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加人',
  `introduce_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '介绍者类型 1：合伙人邀请 2：员工邀请',
  `invite_tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '员工邀请ID',
  `sp_time` datetime DEFAULT NULL COMMENT '审批时间',
  `sp_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企微审批单号',
  `integral` int NOT NULL DEFAULT '0' COMMENT '智邦币总数',
  `client_level` int NOT NULL DEFAULT '0' COMMENT '合伙人类型',
  `growth_value` int NOT NULL DEFAULT '0' COMMENT '成长值',
  `integral1` int NOT NULL DEFAULT '0' COMMENT '可提现智邦币',
  `integral2` int NOT NULL DEFAULT '0' COMMENT '可转赠智邦币',
  `growth_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '成长等级',
  `work_company_id` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Work平台公司名称',
  `work_company_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Work平台公司名称',
  `partner_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '合伙人编码',
  `partner_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '事业合伙人类型 A级 B级',
  `cash_balance` decimal(10,2) DEFAULT '0.00' COMMENT '现金余额',
  `cash_history` decimal(10,2) DEFAULT '0.00' COMMENT '现金提现历史',
  `work_person_id` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Wok平台联系人ID',
  `invite_client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '合伙人邀请ID',
  `source` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'invite' COMMENT '合伙人来源 invite: 二维码邀请 work：work平台申请 create:手动新增',
  PRIMARY KEY (`partner_id`) USING BTREE,
  KEY `client_id` (`client_id`) USING BTREE,
  KEY `invite_id` (`invite_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  KEY `invite_tp_user_id` (`invite_tp_user_id`) USING BTREE,
  KEY `invite_client_id` (`invite_client_id`) USING BTREE,
  CONSTRAINT `client_partner_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client_info` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_partner_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_partner_ibfk_3` FOREIGN KEY (`invite_tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_partner_ibfk_4` FOREIGN KEY (`invite_client_id`) REFERENCES `client_info` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='合伙申请信息表';

-- ----------------------------
-- Table structure for client_partner_contract
-- ----------------------------
DROP TABLE IF EXISTS `client_partner_contract`;
CREATE TABLE `client_partner_contract` (
  `partner_contract_id` bigint NOT NULL COMMENT '合伙人合同ID',
  `partner_id` bigint DEFAULT NULL COMMENT '合伙人ID',
  `contract_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '合同编号',
  `contract_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '合同名称',
  `file_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最终合同存储url 一般是财务盖完章后的最终版本的合同',
  `start_date` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '合同开始日期',
  `end_date` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '合同结束日期',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '合同上传添加人',
  `delete_flag` tinyint DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `idcard_front` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证正面',
  `idcard_back` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证反面',
  `sign_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '签名url',
  `sign_status` tinyint DEFAULT '1' COMMENT '签名状态 1：未签名 2：已签名',
  `tmp_contract_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '合同模版id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证姓名',
  `id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证号',
  `addr` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证地址',
  `gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证性别',
  `nationality` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证民族',
  `valid_date` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证有效期',
  `sign_file_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '合伙人签完名后保存的url',
  `file_img_list` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '合同pdf转图片列表\n',
  `sign_date` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '签约日期',
  `invite_tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邀请人',
  PRIMARY KEY (`partner_contract_id`) USING BTREE,
  KEY `partner_id` (`partner_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  KEY `tmp_contract_id` (`tmp_contract_id`) USING BTREE,
  KEY `invite_tp_user_id` (`invite_tp_user_id`) USING BTREE,
  CONSTRAINT `client_partner_contract_ibfk_1` FOREIGN KEY (`partner_id`) REFERENCES `client_partner` (`partner_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_partner_contract_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_partner_contract_ibfk_3` FOREIGN KEY (`tmp_contract_id`) REFERENCES `dict_partner_contract` (`tmp_contract_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_partner_contract_ibfk_4` FOREIGN KEY (`invite_tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='合伙人合同表';

-- ----------------------------
-- Table structure for client_portrait
-- ----------------------------
DROP TABLE IF EXISTS `client_portrait`;
CREATE TABLE `client_portrait` (
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `update_date` datetime DEFAULT NULL COMMENT '客户画像更新时间',
  `client_concern` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '客户关注；用分号隔开',
  `client_dimension` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户维度：json数据表示',
  `similar_client_list` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '近10个相似客户的列表',
  `portrait_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户画像id',
  PRIMARY KEY (`portrait_id`) USING BTREE,
  KEY `fk_client_portrait_client_id_1` (`client_id`) USING BTREE,
  CONSTRAINT `client_portrait_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户画像表';

-- ----------------------------
-- Table structure for client_radar
-- ----------------------------
DROP TABLE IF EXISTS `client_radar`;
CREATE TABLE `client_radar` (
  `client_radar_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `radar_title` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户雷达标题',
  `radar_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '客户雷达内容',
  `add_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加用户id',
  `add_time` datetime DEFAULT NULL COMMENT '添加时间',
  `modify_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人id',
  `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `default_pic_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '雷达默认图片地址',
  `radar_client_id_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '客户雷达应用的客户id列表',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志位；0:未删除；1:已删除',
  `recovery_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '恢复者',
  `recovery_time` datetime DEFAULT NULL COMMENT '恢复时间',
  `hidden_form` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '是否隐藏表单；0显示；1隐藏',
  `qr_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '小程序二维码',
  `radar_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '雷达名称',
  `publish_status` enum('0','1','2','3','4','5','6') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '5' COMMENT '0:已上架；1:审核中；2:审核拒绝；3:审核通过；4推送失败；5处理中；6已下架',
  `scope` enum('0','1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '可使范围 0:公司；1:个人；2合伙人',
  `spread` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '2' COMMENT '传播渠道；1全部；2 裂变任务',
  `share_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '小程序传播二维码',
  `switch_channel` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '0 创建人渠道；1 配置渠道',
  `channel_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '指定渠道id',
  `sn` int DEFAULT '9999' COMMENT '序号',
  `publish_method` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '0:手动；1:立即发布(通过审核后);2:自定义时间',
  `publish_time` datetime DEFAULT NULL COMMENT '自定义发布时间',
  `scheme_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限id',
  `task_id` bigint DEFAULT NULL COMMENT '合伙人任务ID',
  `key_words` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`client_radar_id`) USING BTREE,
  KEY `fk_client_radar_touchpoint_user_1` (`add_user_id`) USING BTREE,
  KEY `fk_client_radar_touchpoint_user_2` (`modify_user_id`) USING BTREE,
  KEY `fk_client_radar_touchpoint_user_3` (`recovery_user_id`) USING BTREE,
  KEY `rel_channel_id` (`channel_id`) USING BTREE,
  KEY `rel_scheme_id` (`scheme_id`) USING BTREE,
  KEY `fk_client_task` (`task_id`) USING BTREE,
  CONSTRAINT `client_radar_ibfk_1` FOREIGN KEY (`add_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_radar_ibfk_2` FOREIGN KEY (`modify_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_radar_ibfk_3` FOREIGN KEY (`recovery_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_radar_ibfk_4` FOREIGN KEY (`task_id`) REFERENCES `mini_task` (`task_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_radar_ibfk_5` FOREIGN KEY (`channel_id`) REFERENCES `config_channel` (`channel_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_radar_ibfk_6` FOREIGN KEY (`scheme_id`) REFERENCES `content_auth_scheme` (`auth_scheme_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户雷达配置存储';

-- ----------------------------
-- Table structure for client_service_evaluation
-- ----------------------------
DROP TABLE IF EXISTS `client_service_evaluation`;
CREATE TABLE `client_service_evaluation` (
  `evaluation_id` bigint NOT NULL COMMENT '服务评价ID',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户ID',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务者ID',
  `tags_ids` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签IDs',
  `star` int NOT NULL DEFAULT '5' COMMENT '评价星级 5，4，3，2，1',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '评价内容',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '编辑时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `tp_user_phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '员工手机号',
  `work_id` int NOT NULL DEFAULT '0' COMMENT '关联work平台id',
  PRIMARY KEY (`evaluation_id`) USING BTREE,
  KEY `client_id` (`client_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `client_service_evaluation_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_service_evaluation_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='服务评价';

-- ----------------------------
-- Table structure for client_service_role
-- ----------------------------
DROP TABLE IF EXISTS `client_service_role`;
CREATE TABLE `client_service_role` (
  `service_role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '服务角色ID',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户ID',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '员工ID',
  `role_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色名称',
  `phone_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '手机号',
  `is_substitution` tinyint DEFAULT '0' COMMENT '是否申请换人 1：是 0：否',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `delete_flag` tinyint DEFAULT '0' COMMENT '0：我删除 1：已删除',
  `work_id` int DEFAULT '0' COMMENT 'Work平台申请换人id',
  PRIMARY KEY (`service_role_id`) USING BTREE,
  KEY `client_id` (`client_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `client_service_role_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_service_role_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=76 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户关联服务角色';

-- ----------------------------
-- Table structure for client_service_role_history
-- ----------------------------
DROP TABLE IF EXISTS `client_service_role_history`;
CREATE TABLE `client_service_role_history` (
  `history_id` bigint NOT NULL COMMENT '历史记录ID',
  `service_role_id` bigint NOT NULL COMMENT '服务角色ID',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户ID',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '员工ID',
  `role_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `phone_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机号',
  `is_substitution` tinyint NOT NULL DEFAULT '0' COMMENT '是否申请换人 1：是 0：否',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0：我删除 1：已删除',
  `bg_qr_code_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '添加企微弹出url',
  PRIMARY KEY (`history_id`) USING BTREE,
  KEY `client_id` (`client_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  KEY `service_role_id` (`service_role_id`) USING BTREE,
  CONSTRAINT `client_service_role_history_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_service_role_history_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_service_role_history_ibfk_3` FOREIGN KEY (`service_role_id`) REFERENCES `client_service_role` (`service_role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户关联服务角色历史记录表';

-- ----------------------------
-- Table structure for client_sp_record
-- ----------------------------
DROP TABLE IF EXISTS `client_sp_record`;
CREATE TABLE `client_sp_record` (
  `record_id` bigint NOT NULL AUTO_INCREMENT,
  `client_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户ID',
  `sp_status` tinyint(1) DEFAULT NULL COMMENT '审核状态',
  `sp_result` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审核结果',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审核备注',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人\n',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '审核时间',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1:已删除 0：未删除',
  PRIMARY KEY (`record_id`) USING BTREE,
  KEY `client_id` (`client_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `client_sp_record_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client_info` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_sp_record_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4873118382901170178 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户审核记录表';

-- ----------------------------
-- Table structure for client_tp_history
-- ----------------------------
DROP TABLE IF EXISTS `client_tp_history`;
CREATE TABLE `client_tp_history` (
  `history_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `start_time` datetime DEFAULT NULL COMMENT '服务开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '服务结束时间',
  `current_service_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '当前是否服务；0:未服务；1:在服务',
  PRIMARY KEY (`history_id`) USING BTREE,
  KEY `fk_client_tp_history_touchpoint_user_1` (`tp_user_id`) USING BTREE,
  KEY `fk_client_tp_history_client_1` (`client_id`) USING BTREE,
  CONSTRAINT `client_tp_history_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `client_tp_history_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户与员工触点历史表';

-- ----------------------------
-- Table structure for clue
-- ----------------------------
DROP TABLE IF EXISTS `clue`;
CREATE TABLE `clue` (
  `clue_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '线索id',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户id',
  `last_estimate_date` datetime DEFAULT NULL COMMENT '最新线索评估日期',
  `latest_maturity_value` int DEFAULT '0' COMMENT '线索成熟度值(0-100)',
  `latest_clue_maturity_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线索成熟id',
  `clue_source_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线索来源id',
  `clue_stage_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线索阶段id',
  `channel_source_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源渠道号id',
  `fm_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '裂变来源任务id',
  `channel_fp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源策划计划id',
  `channel_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '通过渠道添加时间',
  `landpage_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '如果公域线索，通过那个landpage提交表单',
  `sign_money` decimal(10,2) DEFAULT NULL COMMENT '签单：元',
  `sign_date` datetime DEFAULT NULL COMMENT '签单时间',
  `return_money` decimal(10,2) DEFAULT NULL COMMENT '回款金额：元',
  `return_date` datetime DEFAULT NULL COMMENT '回款时间',
  `delete_flag` tinyint DEFAULT '0' COMMENT '删除标志位',
  `company_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工商注册地址',
  `industry` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所属行业',
  `partner_id` bigint DEFAULT NULL COMMENT '合伙人ID',
  `is_settle_in` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否入住园区1：是 0：否',
  `settle_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '入驻园区名称',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '备注',
  `approve_status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '审批状态 1 默认；2通过；3驳回；0检测中',
  `approve_time` datetime DEFAULT NULL COMMENT '审批时间',
  `approve_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批原因',
  `sp_amount` int DEFAULT '0' COMMENT '标准产品金额',
  `nsp_amount` int DEFAULT '0' COMMENT '非标准产品金额',
  `task_id` bigint DEFAULT NULL COMMENT '客户任务ID',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人ID',
  `ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '请求ip',
  `valid_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '2' COMMENT '0：无效；1：有效；2：待判定',
  `tip_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '提点类型',
  `work_result` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '处理结果',
  `term_protection` int DEFAULT '180' COMMENT '保护时长 / 天',
  `supporter` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '特别支持 0否；1是',
  `company_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '公司名字',
  `media_account_id` bigint DEFAULT NULL COMMENT '媒体授权账户ID',
  `mg_clue_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Mg库对应的线索ID',
  `clue_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '线索名称',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '手机号',
  `is_verified` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '是否待核验 0否；1 是',
  PRIMARY KEY (`clue_id`) USING BTREE,
  KEY `fk_clue_client_info_1` (`client_id`) USING BTREE,
  KEY `fk_clue_dict_clue_maturity _1` (`latest_clue_maturity_id`) USING BTREE,
  KEY `fk_clue_dict_clue_source_1` (`clue_source_id`) USING BTREE,
  KEY `fk_clue_dict_clue_stage_1` (`clue_stage_id`) USING BTREE,
  KEY `fk_clue_channel_id_1` (`channel_source_id`) USING BTREE,
  KEY `fk_clue_fp_id_1` (`channel_fp_id`) USING BTREE,
  KEY `fk_clue_fm_id_1` (`fm_id`) USING BTREE,
  KEY `fk_clue_landpage_id_1` (`landpage_id`) USING BTREE,
  KEY `rel_partner` (`partner_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  KEY `rel_client_task` (`task_id`) USING BTREE,
  CONSTRAINT `clue_ibfk_1` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `clue_ibfk_10` FOREIGN KEY (`task_id`) REFERENCES `mini_task` (`task_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `clue_ibfk_11` FOREIGN KEY (`partner_id`) REFERENCES `client_partner` (`partner_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `clue_ibfk_2` FOREIGN KEY (`channel_source_id`) REFERENCES `config_channel` (`channel_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `clue_ibfk_3` FOREIGN KEY (`client_id`) REFERENCES `client_info` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `clue_ibfk_4` FOREIGN KEY (`latest_clue_maturity_id`) REFERENCES `dict_clue_maturity` (`clue_maturity_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `clue_ibfk_5` FOREIGN KEY (`clue_source_id`) REFERENCES `dict_clue_source` (`clue_source_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `clue_ibfk_6` FOREIGN KEY (`clue_stage_id`) REFERENCES `dict_clue_stage` (`clue_stage_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `clue_ibfk_7` FOREIGN KEY (`fm_id`) REFERENCES `fission_mission` (`fm_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `clue_ibfk_8` FOREIGN KEY (`channel_fp_id`) REFERENCES `fission_planning` (`fission_planning_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `clue_ibfk_9` FOREIGN KEY (`landpage_id`) REFERENCES `landpage` (`landpage_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='线索表';

-- ----------------------------
-- Table structure for clue_estimation
-- ----------------------------
DROP TABLE IF EXISTS `clue_estimation`;
CREATE TABLE `clue_estimation` (
  `clue_estimation_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '线索评估id',
  `estimation_date` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '评估日期',
  `clue_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '线索id',
  `clue_maturity_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '评估时线索成熟度',
  `clue_stage_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '线索阶段id',
  `work_wechart_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '是否添加企业微信',
  `approval_score` int NOT NULL DEFAULT '0' COMMENT '线索认可值0-100',
  `interaction_rate` float(5,2) NOT NULL DEFAULT '0.00' COMMENT '互动率',
  `fission_count` int NOT NULL DEFAULT '0' COMMENT '涉及策划数',
  `interaction_fission_count` int NOT NULL DEFAULT '0' COMMENT '互动策划数',
  `interaction_num` int NOT NULL DEFAULT '0' COMMENT '互动数',
  `long_interaction_num` int NOT NULL DEFAULT '0' COMMENT '长互动数',
  `good_interaction_rate` float(5,2) NOT NULL DEFAULT '0.00' COMMENT '互动良率',
  PRIMARY KEY (`clue_estimation_id`) USING BTREE,
  KEY `fk_clue_estimation_clue_1` (`clue_id`) USING BTREE,
  KEY `fk_clue_estimation_dict_clue_maturity_1` (`clue_maturity_id`) USING BTREE,
  CONSTRAINT `clue_estimation_ibfk_1` FOREIGN KEY (`clue_id`) REFERENCES `clue` (`clue_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `clue_estimation_ibfk_2` FOREIGN KEY (`clue_maturity_id`) REFERENCES `dict_clue_maturity` (`clue_maturity_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='线索评估表';

-- ----------------------------
-- Table structure for clue_estimation_time
-- ----------------------------
DROP TABLE IF EXISTS `clue_estimation_time`;
CREATE TABLE `clue_estimation_time` (
  `clue_estimation_time_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `clue_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线索id',
  `interaction_num` int DEFAULT '0' COMMENT '每天交互数',
  `stat_date` date DEFAULT NULL COMMENT '统计日期',
  PRIMARY KEY (`clue_estimation_time_id`) USING BTREE,
  KEY `fk_clue_estimation_time_clue_1` (`clue_id`) USING BTREE,
  CONSTRAINT `clue_estimation_time_ibfk_1` FOREIGN KEY (`clue_id`) REFERENCES `clue` (`clue_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='线索评估时间趋势表';

-- ----------------------------
-- Table structure for clue_fission_history
-- ----------------------------
DROP TABLE IF EXISTS `clue_fission_history`;
CREATE TABLE `clue_fission_history` (
  `cfh_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `clue_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线索id',
  `fm_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '裂变任务id',
  `sn` int DEFAULT NULL COMMENT '裂变顺序',
  `from_client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源客户id',
  `to_client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '去向客户id',
  `fission_time` datetime DEFAULT NULL COMMENT '裂变时间',
  PRIMARY KEY (`cfh_id`) USING BTREE,
  KEY `fk_clue_fission_history_clue_1` (`clue_id`) USING BTREE,
  CONSTRAINT `clue_fission_history_ibfk_1` FOREIGN KEY (`clue_id`) REFERENCES `clue` (`clue_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for clue_forward
-- ----------------------------
DROP TABLE IF EXISTS `clue_forward`;
CREATE TABLE `clue_forward` (
  `forward_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `clue_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '线索id',
  `forward_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '转交员工id',
  `forward_person_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '转交员工姓名；如果id是空的情况下使用',
  `forward_time` datetime DEFAULT NULL COMMENT '转交时间',
  `forward_status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '转交状态；1：在服务；0：未服务（已转交他人）',
  PRIMARY KEY (`forward_id`) USING BTREE,
  KEY `fk_clue_forward_touchpoint_user_1` (`forward_user_id`) USING BTREE,
  KEY `fk_clue_forward_clue_1` (`clue_id`) USING BTREE,
  CONSTRAINT `clue_forward_ibfk_1` FOREIGN KEY (`clue_id`) REFERENCES `clue` (`clue_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `clue_forward_ibfk_2` FOREIGN KEY (`forward_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='线索转交表';

-- ----------------------------
-- Table structure for clue_improve_advise
-- ----------------------------
DROP TABLE IF EXISTS `clue_improve_advise`;
CREATE TABLE `clue_improve_advise` (
  `clue_improve_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '线索提升建议id',
  `clue_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '对于线索id',
  `improve_advise_text` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '提升建议',
  `advise_sn` int DEFAULT NULL COMMENT '提示建议序号',
  `advise_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '提升建议的依据',
  PRIMARY KEY (`clue_improve_id`) USING BTREE,
  KEY `fk_clue_improve_advise_clue_1` (`clue_id`) USING BTREE,
  CONSTRAINT `clue_improve_advise_ibfk_1` FOREIGN KEY (`clue_id`) REFERENCES `clue` (`clue_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='线索提升建议';

-- ----------------------------
-- Table structure for code_fusion
-- ----------------------------
DROP TABLE IF EXISTS `code_fusion`;
CREATE TABLE `code_fusion` (
  `fusion_id` int NOT NULL AUTO_INCREMENT COMMENT '融合映射id',
  `channel_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '对外展示渠道',
  `user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数渠道',
  `qr_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '二维码',
  `qr_config_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'qr_code的config code信息',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`fusion_id`) USING BTREE,
  KEY `rel_channel` (`channel_id`) USING BTREE,
  KEY `rel_user` (`user_id`) USING BTREE,
  CONSTRAINT `code_fusion_ibfk_1` FOREIGN KEY (`channel_id`) REFERENCES `config_channel` (`channel_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `code_fusion_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户雷达配置存储';

-- ----------------------------
-- Table structure for com_property
-- ----------------------------
DROP TABLE IF EXISTS `com_property`;
CREATE TABLE `com_property` (
  `com_property_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `property_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '熟悉名称',
  PRIMARY KEY (`com_property_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for com_type
-- ----------------------------
DROP TABLE IF EXISTS `com_type`;
CREATE TABLE `com_type` (
  `com_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `com_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `com_level` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件等级；1级无属性；2级有属性；',
  `father_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件父id',
  `com_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件的code',
  `com_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '具体组件的json数据',
  PRIMARY KEY (`com_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for company
-- ----------------------------
DROP TABLE IF EXISTS `company`;
CREATE TABLE `company` (
  `company_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司id',
  `company_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公司名称',
  `short_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公司短名称',
  `tyc_status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '天眼查状态 0：默认；1：更新成功； 2：未查询到信息',
  PRIMARY KEY (`company_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户所属公司(个人)';

-- ----------------------------
-- Table structure for component_api_data
-- ----------------------------
DROP TABLE IF EXISTS `component_api_data`;
CREATE TABLE `component_api_data` (
  `com_api_data_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `com_data_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '0:视频号；1:公众号；2:H5链接',
  `com_api_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'api的id',
  `add_time` datetime DEFAULT NULL,
  `add_tpu_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `data_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `data_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `data_cover_oss_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `data_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`com_api_data_id`) USING BTREE,
  KEY `fk_component_api_data_dict_component_api_1` (`com_api_id`) USING BTREE,
  KEY `fk_component_api_data_tpu_id` (`add_tpu_id`) USING BTREE,
  CONSTRAINT `component_api_data_ibfk_1` FOREIGN KEY (`com_api_id`) REFERENCES `dict_component_api` (`com_api_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `component_api_data_ibfk_2` FOREIGN KEY (`add_tpu_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for component_value
-- ----------------------------
DROP TABLE IF EXISTS `component_value`;
CREATE TABLE `component_value` (
  `com_val_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `component_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件id',
  `com_val` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件值',
  `com_val_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件路由(可选)',
  PRIMARY KEY (`com_val_id`) USING BTREE,
  KEY `fk_component_value_view_component_1` (`component_id`) USING BTREE,
  CONSTRAINT `component_value_ibfk_1` FOREIGN KEY (`component_id`) REFERENCES `view_component` (`component_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='页面组件值表';

-- ----------------------------
-- Table structure for conf_app_ids
-- ----------------------------
DROP TABLE IF EXISTS `conf_app_ids`;
CREATE TABLE `conf_app_ids` (
  `id` int NOT NULL,
  `app_id` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `title` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `target` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `prompt` text COLLATE utf8mb4_general_ci,
  PRIMARY KEY (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for conf_entry_ids
-- ----------------------------
DROP TABLE IF EXISTS `conf_entry_ids`;
CREATE TABLE `conf_entry_ids` (
  `name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `app_id` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `title` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `detail_app_ids` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for config_ab_test
-- ----------------------------
DROP TABLE IF EXISTS `config_ab_test`;
CREATE TABLE `config_ab_test` (
  `config_ab_test_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'abTestID',
  `fp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '策划计划id',
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `ab_test_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'abTest的名称',
  PRIMARY KEY (`config_ab_test_id`) USING BTREE,
  KEY `fk_config_ab_test_fission_planning_1` (`fp_id`) USING BTREE,
  CONSTRAINT `config_ab_test_ibfk_1` FOREIGN KEY (`fp_id`) REFERENCES `fission_planning` (`fission_planning_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='渠道配置';

-- ----------------------------
-- Table structure for config_bonus_client
-- ----------------------------
DROP TABLE IF EXISTS `config_bonus_client`;
CREATE TABLE `config_bonus_client` (
  `bonus_client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `bonus_client_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户激励名称',
  PRIMARY KEY (`bonus_client_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户激励配置';

-- ----------------------------
-- Table structure for config_bonus_tpu
-- ----------------------------
DROP TABLE IF EXISTS `config_bonus_tpu`;
CREATE TABLE `config_bonus_tpu` (
  `bonus_tpu_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触点用户激励配置id',
  `bonus_tpu_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '触点用户激励配置名称',
  PRIMARY KEY (`bonus_tpu_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户激励配置表';

-- ----------------------------
-- Table structure for config_channel
-- ----------------------------
DROP TABLE IF EXISTS `config_channel`;
CREATE TABLE `config_channel` (
  `channel_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '渠道id',
  `channel_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '渠道名称',
  `channel_pic_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '渠道二维码地址',
  `channel_group_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '渠道组id',
  `channel_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT 'channel对应的类型id',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志位；0:未删除；1:删除；',
  `upload_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '自有二维码上传标志位：0：不上传已有二维码；1：上传已有二维码',
  `client_group_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户群id',
  `add_time` datetime DEFAULT NULL COMMENT '添加时间',
  `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `recovery_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '恢复者',
  `recovery_time` datetime DEFAULT NULL COMMENT '恢复时间',
  `channel_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '对应的渠道编码',
  `activity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '活动名称',
  `modify_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改者id',
  `add_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加人id',
  `bg_qr_code_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '个人渠道背景码',
  PRIMARY KEY (`channel_id`) USING BTREE,
  KEY `fk_config_channel_channel_group_1` (`channel_group_id`) USING BTREE,
  KEY `fk_config_channel_dict_channel_type_1` (`channel_type_id`) USING BTREE,
  KEY `fk_config_channel_tpu_id_1` (`recovery_user_id`) USING BTREE,
  CONSTRAINT `config_channel_ibfk_1` FOREIGN KEY (`channel_group_id`) REFERENCES `channel_group` (`channel_group_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `config_channel_ibfk_2` FOREIGN KEY (`channel_type_id`) REFERENCES `dict_channel_type` (`channel_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `config_channel_ibfk_3` FOREIGN KEY (`recovery_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='渠道配置';

-- ----------------------------
-- Table structure for config_monitor
-- ----------------------------
DROP TABLE IF EXISTS `config_monitor`;
CREATE TABLE `config_monitor` (
  `monitor_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '监控配置id',
  `monitor_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '监控配置名称',
  PRIMARY KEY (`monitor_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='监测配置表';

-- ----------------------------
-- Table structure for config_software
-- ----------------------------
DROP TABLE IF EXISTS `config_software`;
CREATE TABLE `config_software` (
  `config_software_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `help_group_config_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '售后群config_id,获取二维码；',
  PRIMARY KEY (`config_software_id`) USING BTREE,
  KEY `fk_config_software_client_group_config_1` (`help_group_config_id`) USING BTREE,
  CONSTRAINT `config_software_ibfk_1` FOREIGN KEY (`help_group_config_id`) REFERENCES `client_group_config` (`client_group_config_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统软件配置-将来可以扩充';

-- ----------------------------
-- Table structure for content_auth_scheme
-- ----------------------------
DROP TABLE IF EXISTS `content_auth_scheme`;
CREATE TABLE `content_auth_scheme` (
  `auth_scheme_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限方案id',
  `scheme_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限方案名称',
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
  `show_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '是否展示在方案列表；0:不展示；1:展示',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志位；0:未删除；1:删除',
  PRIMARY KEY (`auth_scheme_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='内容权限方案';

-- ----------------------------
-- Table structure for content_data
-- ----------------------------
DROP TABLE IF EXISTS `content_data`;
CREATE TABLE `content_data` (
  `content_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容素材数据id',
  `spread_index` int DEFAULT NULL COMMENT '素材传播指数',
  `spread_rank` int DEFAULT NULL COMMENT '素材传播排名',
  `spread_rate` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材传播增长率',
  `basic_client_num` int DEFAULT '0' COMMENT '基本统计-覆盖客户数',
  `basic_clue_num` int DEFAULT '0' COMMENT '基本统计-新线索数',
  `basic_read_num` int DEFAULT '0' COMMENT '基本统计-阅读量',
  `basic_collect_num` int DEFAULT '0' COMMENT '基本统计-点赞量',
  `basic_trans_num` int DEFAULT '0' COMMENT '基本统计-转发数',
  `basic_interactive_num` int DEFAULT '0' COMMENT '基本统计-交互数',
  `last_update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
  PRIMARY KEY (`content_id`) USING BTREE,
  CONSTRAINT `content_data_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `content_info` (`content_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='素材数据表';

-- ----------------------------
-- Table structure for content_dowload_history
-- ----------------------------
DROP TABLE IF EXISTS `content_dowload_history`;
CREATE TABLE `content_dowload_history` (
  `dowload_history_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '触点用户id',
  `content_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材id',
  `dowload_time` datetime DEFAULT NULL COMMENT '素材下载时间',
  `down_date` date DEFAULT NULL COMMENT '素材操作时间，精确到天',
  `success_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '下载成功标志位：1：成功；0：失败',
  `operate_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作类型;1:浏览；2:下载；3:曝光',
  `client_ww_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户企业微信id',
  PRIMARY KEY (`dowload_history_id`) USING BTREE,
  KEY `fk_content_dowload_history_content_info_1` (`content_id`) USING BTREE,
  KEY `fk_content_dowload_history_touchpoint_user_1` (`tp_user_id`) USING BTREE,
  CONSTRAINT `content_dowload_history_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `content_info` (`content_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `content_dowload_history_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='素材下载历史表';

-- ----------------------------
-- Table structure for content_group
-- ----------------------------
DROP TABLE IF EXISTS `content_group`;
CREATE TABLE `content_group` (
  `group_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `group_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材组名称',
  `group_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材code',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志位；0:未删除；1:已删除',
  `cover_pic_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '封面图片oss地址',
  PRIMARY KEY (`group_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='素材组表';

-- ----------------------------
-- Table structure for content_info
-- ----------------------------
DROP TABLE IF EXISTS `content_info`;
CREATE TABLE `content_info` (
  `content_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容素材id',
  `content_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材编号字母数字组合，10字符',
  `content_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '内容素材类型id',
  `ab_test_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'ab测试id',
  `add_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '上传人id',
  `aigc_content_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'aigc模型id',
  `copywriting_pic_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文案内容markdown格式转图片的url地址',
  `copywriting` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '文案内容',
  `pic_url` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图片url',
  `video_url` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '短视频url',
  `docomnet_url` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文档类oss链接地址',
  `cover_pic_url` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '封面图片地址',
  `link_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '公众号或视频号的链接地址',
  `content_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '素材名称',
  `create_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创作者id',
  `auth_scheme_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限方案id',
  `thumbnail_url` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '缩略图url',
  `create_time` datetime DEFAULT NULL COMMENT '创作时间',
  `comment` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材备注',
  `info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '素材信息',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '0:未删除；1已删除',
  `publish_status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '0:已上架；1:审核中；2:审核拒绝；3:审核通过',
  `out_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '0:不对外公开；1:对外公开；',
  `publish_department_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '发布者部门id',
  `publish_tpu_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '发布者id',
  `publish_writing` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '发布文案',
  `publish_method` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '0:手动；1:立即发布(通过审核后);2:自定义时间',
  `publish_time` datetime DEFAULT NULL COMMENT '预设发布时间',
  `qrcode_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '二维码url地址',
  `media_id` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '企业微信media_id;临时素材id',
  `media_upload_time` datetime DEFAULT NULL COMMENT '临时素材上传时间，过期时间三天',
  `media_pic_flag` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '临时素材或素材组是否图片的标志位',
  `moment_media_id` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '朋友圈临时素材id',
  `moment_media_upload_time` datetime DEFAULT NULL COMMENT '朋友圈临时素材上传时间',
  `content_source_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材来源id',
  `content_source_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '其他素材来源',
  `content_level` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材等级：S、A、B、C',
  `key_words` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '素材关键词',
  `content_use_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '素材使用范围',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `modify_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改者id',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `auto_shelf` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '审批后自动上架标志位；0:不自动上架；1:自动上架；',
  `radar_oss` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '存储h5，pdf等客户雷达转发素材的png对应oss对象地址',
  `qr_pos` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '二维码对应的位置；(x, y)',
  `qr_size` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '二维码对应的大小：(width, height)',
  `recovery_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '回收入id',
  `recovery_time` datetime DEFAULT NULL COMMENT '回收时间',
  `stat_exposure` int DEFAULT '0' COMMENT '曝光量',
  `stat_download` int DEFAULT '0' COMMENT '下载量',
  `stat_brower` int DEFAULT '0' COMMENT '浏览量',
  `stat_read` int DEFAULT '0' COMMENT '客户阅读量；通过客户雷达来实现',
  `knowledge_id` bigint DEFAULT NULL COMMENT '管理知识库ID',
  `scope_id` int DEFAULT NULL COMMENT '应用场景',
  PRIMARY KEY (`content_id`) USING BTREE,
  KEY `fk_content_dict_content_type_1` (`content_type_id`) USING BTREE,
  KEY `fk_content_config_ab_test_1` (`ab_test_id`) USING BTREE,
  KEY `fk_content_touchpoint_user_1` (`add_user_id`) USING BTREE,
  KEY `fk_content_info_aigc_content_1` (`aigc_content_id`) USING BTREE,
  KEY `fk_content_info_content_auth_scheme_1` (`auth_scheme_id`) USING BTREE,
  KEY `fk_content_info_dict_content_source_1` (`content_source_id`) USING BTREE,
  KEY `fk_content_info_dict_content_use_1` (`content_use_id`) USING BTREE,
  KEY `fk_content_touchpoint_user_2` (`recovery_user_id`) USING BTREE,
  KEY `knowledge_id` (`knowledge_id`) USING BTREE,
  KEY `scope_id` (`scope_id`) USING BTREE,
  CONSTRAINT `content_info_ibfk_1` FOREIGN KEY (`ab_test_id`) REFERENCES `config_ab_test` (`config_ab_test_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `content_info_ibfk_10` FOREIGN KEY (`scope_id`) REFERENCES `dict_content_scope` (`scope_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `content_info_ibfk_2` FOREIGN KEY (`content_type_id`) REFERENCES `dict_content_type` (`content_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `content_info_ibfk_3` FOREIGN KEY (`aigc_content_id`) REFERENCES `aigc_content` (`aigc_content_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `content_info_ibfk_4` FOREIGN KEY (`auth_scheme_id`) REFERENCES `content_auth_scheme` (`auth_scheme_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `content_info_ibfk_5` FOREIGN KEY (`content_source_id`) REFERENCES `dict_content_source` (`content_source_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `content_info_ibfk_6` FOREIGN KEY (`content_use_id`) REFERENCES `dict_content_use` (`dict_content_use_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `content_info_ibfk_7` FOREIGN KEY (`add_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `content_info_ibfk_8` FOREIGN KEY (`recovery_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `content_info_ibfk_9` FOREIGN KEY (`knowledge_id`) REFERENCES `aigc_knowledge` (`knowledge_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='素材内容表';

-- ----------------------------
-- Table structure for content_info_extend
-- ----------------------------
DROP TABLE IF EXISTS `content_info_extend`;
CREATE TABLE `content_info_extend` (
  `content_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容ID',
  `app_id` bigint DEFAULT NULL COMMENT '智能投放产品ID',
  `size` int DEFAULT NULL COMMENT '大小',
  `width` int DEFAULT NULL COMMENT '宽度',
  `height` int DEFAULT NULL COMMENT '高度',
  `format` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '视频格式',
  `signature` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '视频或图片md5值',
  `bit_rate` int DEFAULT NULL COMMENT '码率，单位bps\n\n',
  `duration` float(6,3) DEFAULT NULL COMMENT '视频时长',
  PRIMARY KEY (`content_id`) USING BTREE,
  CONSTRAINT `content_info_extend_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `content_info` (`content_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='内容扩展表';

-- ----------------------------
-- Table structure for content_info_ocean
-- ----------------------------
DROP TABLE IF EXISTS `content_info_ocean`;
CREATE TABLE `content_info_ocean` (
  `content_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `advertiser_id` bigint DEFAULT NULL COMMENT '广告主ID',
  `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '视频or图片ID',
  `material_id` bigint DEFAULT NULL COMMENT '素材ID',
  `source` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材来源',
  `create_time` datetime DEFAULT NULL COMMENT '素材创建时间',
  `labels` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签',
  `aigc` tinyint DEFAULT NULL COMMENT '是否aigc生成 1:是 0否',
  PRIMARY KEY (`content_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='巨量广告素材相关表';

-- ----------------------------
-- Table structure for content_level
-- ----------------------------
DROP TABLE IF EXISTS `content_level`;
CREATE TABLE `content_level` (
  `level_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `level_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材等级名称',
  `level_commet` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '等级备注，描述',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志位',
  `sn` int DEFAULT NULL COMMENT '排序序号',
  PRIMARY KEY (`level_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='素材等级表';

-- ----------------------------
-- Table structure for course
-- ----------------------------
DROP TABLE IF EXISTS `course`;
CREATE TABLE `course` (
  `course_id` bigint NOT NULL COMMENT '课程Id',
  `course_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '课程名',
  `course_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '课程介绍',
  `cover_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '课程封面',
  `course_category_id` int DEFAULT NULL COMMENT '课程分类ID',
  `price` decimal(10,2) DEFAULT NULL COMMENT '价格',
  `term` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '学期数',
  `sort` int DEFAULT '999999' COMMENT '排序',
  `status` tinyint DEFAULT NULL COMMENT '1：上架  0：下架\n',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加人ID',
  `delete_flag` tinyint DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `teacher_ids` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联老师',
  `is_index` tinyint(1) DEFAULT '0' COMMENT '是否首页显示 1：是 0：否',
  PRIMARY KEY (`course_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  KEY `course_category_id` (`course_category_id`) USING BTREE,
  CONSTRAINT `course_ibfk_1` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `course_ibfk_2` FOREIGN KEY (`course_category_id`) REFERENCES `course_category` (`course_category_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='课程表';

-- ----------------------------
-- Table structure for course_category
-- ----------------------------
DROP TABLE IF EXISTS `course_category`;
CREATE TABLE `course_category` (
  `course_category_id` int NOT NULL AUTO_INCREMENT COMMENT '课程分类ID',
  `course_category_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '课程分类名称',
  `sort` int DEFAULT '99999' COMMENT '排序',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  `status` tinyint DEFAULT NULL COMMENT '1:显示 0：隐藏',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint DEFAULT '0' COMMENT '1:已删除 0：未删除',
  PRIMARY KEY (`course_category_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `course_category_ibfk_1` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='课程分类表';

-- ----------------------------
-- Table structure for course_orders
-- ----------------------------
DROP TABLE IF EXISTS `course_orders`;
CREATE TABLE `course_orders` (
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `course_id` bigint DEFAULT NULL COMMENT '课程ID',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '学员ID',
  `openid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'openid',
  `amount` decimal(10,2) DEFAULT NULL COMMENT '课程金额',
  `prepay_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '预支付id',
  `trade_state` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '交易状态，枚举值：SUCCESS：支付成功\\REFUND：转入退款 NOTPAY：未支付 CLOSED：已关闭 REVOKED：已撤销（付款码支付）USERPAYING：用户支付中（付款码支付）PAYERROR：支付失败(其他原因，如银行返回失败)',
  `bank_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '银行类型',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `time_expire` datetime DEFAULT NULL COMMENT '支付截止时间',
  `is_callback` tinyint DEFAULT '0' COMMENT '0:未回调 1：已回调',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `delete_flag` tinyint DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`) USING BTREE,
  KEY `course_id` (`course_id`) USING BTREE,
  KEY `client_id` (`client_id`) USING BTREE,
  CONSTRAINT `course_orders_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `course` (`course_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `course_orders_ibfk_2` FOREIGN KEY (`client_id`) REFERENCES `client_info` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='课程订单表';

-- ----------------------------
-- Table structure for course_teacher
-- ----------------------------
DROP TABLE IF EXISTS `course_teacher`;
CREATE TABLE `course_teacher` (
  `course_teacher_id` bigint NOT NULL COMMENT '教师ID',
  `teacher_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '教师名称',
  `phone_number` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号',
  `company` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所在单位',
  `introduce` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '介绍',
  `id_card` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证号',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
  `bank_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '银行名称',
  `open_bank` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '开户行',
  `bank_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '银行卡号',
  `img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '个人照片',
  `status` tinyint DEFAULT '1' COMMENT '状态 1：有效 0：无效',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '员工id',
  `delete_flag` tinyint DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `is_index` tinyint DEFAULT '0' COMMENT '是否首页显示 0：否 1：是',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标题',
  PRIMARY KEY (`course_teacher_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `course_teacher_ibfk_1` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='师资表';

-- ----------------------------
-- Table structure for course_teacher_file
-- ----------------------------
DROP TABLE IF EXISTS `course_teacher_file`;
CREATE TABLE `course_teacher_file` (
  `teacher_file_id` bigint NOT NULL COMMENT '附件ID',
  `teacher_id` bigint DEFAULT NULL COMMENT '老师ID',
  `file_type` tinyint DEFAULT NULL COMMENT '附件类型 1：毕业证 2：身份证 3：资格证',
  `file_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Oss 路径',
  `delete_flag` tinyint DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`teacher_file_id`) USING BTREE,
  KEY `teacher_id` (`teacher_id`) USING BTREE,
  CONSTRAINT `course_teacher_file_ibfk_1` FOREIGN KEY (`teacher_id`) REFERENCES `course_teacher` (`course_teacher_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='师资附件表';

-- ----------------------------
-- Table structure for course_video
-- ----------------------------
DROP TABLE IF EXISTS `course_video`;
CREATE TABLE `course_video` (
  `course_video_id` bigint NOT NULL COMMENT '课程视频ID',
  `course_id` bigint DEFAULT NULL COMMENT '课程ID',
  `video_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '课程名称',
  `video_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '课程视频url',
  `sort` int DEFAULT NULL COMMENT '课程排序',
  `size` int DEFAULT NULL COMMENT '大小',
  `width` int DEFAULT NULL COMMENT '宽度',
  `height` int DEFAULT NULL COMMENT '高度',
  `format` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '视频格式',
  `signature` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '视频或图片md5值',
  `bit_rate` int DEFAULT NULL COMMENT '码率，单位bps\n\n',
  `duration` float(6,3) DEFAULT NULL COMMENT '视频时长',
  `status` tinyint DEFAULT '1' COMMENT '发布状态 1：发布 0：未发布',
  `delete_flag` tinyint DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加人',
  `is_free` tinyint DEFAULT '0' COMMENT '是否免费 1：是 0：否',
  PRIMARY KEY (`course_video_id`) USING BTREE,
  KEY `course_id` (`course_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `course_video_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `course` (`course_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `course_video_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='课程视频表';

-- ----------------------------
-- Table structure for default_tpu_role
-- ----------------------------
DROP TABLE IF EXISTS `default_tpu_role`;
CREATE TABLE `default_tpu_role` (
  `default_tpu_role_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `role_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '对应的角色id',
  `valid_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '1:有效；0:无效',
  PRIMARY KEY (`default_tpu_role_id`) USING BTREE,
  KEY `fk_default_tpu_role_dict_tp_user_role_1` (`role_id`) USING BTREE,
  CONSTRAINT `default_tpu_role_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `dict_tp_user_role` (`dict_tp_user_role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for department
-- ----------------------------
DROP TABLE IF EXISTS `department`;
CREATE TABLE `department` (
  `department_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `parent_dp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '父级department id',
  `org_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组织id',
  `department_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门名称',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门地址',
  PRIMARY KEY (`department_id`) USING BTREE,
  KEY `fk_department_department_1` (`parent_dp_id`) USING BTREE,
  KEY `fk_department_organization_1` (`org_id`) USING BTREE,
  CONSTRAINT `department_ibfk_1` FOREIGN KEY (`org_id`) REFERENCES `organization` (`org_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='组织部门表';

-- ----------------------------
-- Table structure for dict_aigc_subject
-- ----------------------------
DROP TABLE IF EXISTS `dict_aigc_subject`;
CREATE TABLE `dict_aigc_subject` (
  `aigc_subject_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'aigc的主题类型id',
  `subject_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主题类型名称',
  PRIMARY KEY (`aigc_subject_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Aigc主题类型';

-- ----------------------------
-- Table structure for dict_aigc_type
-- ----------------------------
DROP TABLE IF EXISTS `dict_aigc_type`;
CREATE TABLE `dict_aigc_type` (
  `aigc_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'aigc模型类型id',
  `aigc_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'aigc模型名称',
  `api_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `secret_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`aigc_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Aigc类型字典';

-- ----------------------------
-- Table structure for dict_auth
-- ----------------------------
DROP TABLE IF EXISTS `dict_auth`;
CREATE TABLE `dict_auth` (
  `dict_auth_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `role_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色id',
  `auth_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限名称',
  PRIMARY KEY (`dict_auth_id`) USING BTREE,
  KEY `fk_dict_auth_dict_tp_user_role_1` (`role_type_id`) USING BTREE,
  CONSTRAINT `dict_auth_ibfk_1` FOREIGN KEY (`role_type_id`) REFERENCES `dict_tp_user_role` (`dict_tp_user_role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色权限表';

-- ----------------------------
-- Table structure for dict_channel_type
-- ----------------------------
DROP TABLE IF EXISTS `dict_channel_type`;
CREATE TABLE `dict_channel_type` (
  `channel_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `channel_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '渠道类型名称',
  `state_show_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否需要输入，state_code和渠道补充内容',
  `place_holder` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '渠道补充内容的place_holder',
  PRIMARY KEY (`channel_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for dict_client_industry
-- ----------------------------
DROP TABLE IF EXISTS `dict_client_industry`;
CREATE TABLE `dict_client_industry` (
  `dict_client_industry_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `industry_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '行业名称',
  `industry_desc` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '行业名称描述',
  `out_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '对外显示标志位',
  PRIMARY KEY (`dict_client_industry_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户行业表';

-- ----------------------------
-- Table structure for dict_client_intention
-- ----------------------------
DROP TABLE IF EXISTS `dict_client_intention`;
CREATE TABLE `dict_client_intention` (
  `intention_id` int NOT NULL AUTO_INCREMENT COMMENT '意向ID',
  `intention_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '意向名称',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0：未删除 1：已删除',
  PRIMARY KEY (`intention_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='意向合作方向字典表';

-- ----------------------------
-- Table structure for dict_client_level
-- ----------------------------
DROP TABLE IF EXISTS `dict_client_level`;
CREATE TABLE `dict_client_level` (
  `level_id` int NOT NULL AUTO_INCREMENT COMMENT '客户级别',
  `level_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '等级名称',
  `level_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '等级说明',
  `level_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '等级图片',
  `min_value` int NOT NULL DEFAULT '0' COMMENT '最小值',
  `max_value` int NOT NULL DEFAULT '0' COMMENT '最大值',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0 :未删除 1：已删除',
  `level_color` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '颜色值',
  `level_bg_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '背景图',
  `level_type` tinyint NOT NULL DEFAULT '1' COMMENT '1:普通类型 2：合伙人类型',
  `bg_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'bg_url',
  `btn_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'btn_url',
  `bar_color` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'bar_color',
  `landpage_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联落地页id，提交线索使用',
  PRIMARY KEY (`level_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户等级字典表';

-- ----------------------------
-- Table structure for dict_client_max_learn
-- ----------------------------
DROP TABLE IF EXISTS `dict_client_max_learn`;
CREATE TABLE `dict_client_max_learn` (
  `max_learn_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '最高学历ID',
  `max_learn_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最高学历名称',
  `sort` int DEFAULT '9999' COMMENT '排序',
  `delete_flag` tinyint DEFAULT '0' COMMENT '0：未删除 1：已删除',
  PRIMARY KEY (`max_learn_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='最高学历字典表';

-- ----------------------------
-- Table structure for dict_client_node
-- ----------------------------
DROP TABLE IF EXISTS `dict_client_node`;
CREATE TABLE `dict_client_node` (
  `node_id` int NOT NULL,
  `node_pid` int DEFAULT NULL COMMENT '父id',
  `node_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户节点名称'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Work平台客户节点表';

-- ----------------------------
-- Table structure for dict_client_political
-- ----------------------------
DROP TABLE IF EXISTS `dict_client_political`;
CREATE TABLE `dict_client_political` (
  `political_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '政治面貌ID',
  `political_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '名称',
  `sort` int DEFAULT '9999' COMMENT '排序',
  `delete_flag` tinyint DEFAULT '0' COMMENT '0：未删除 1：已删除',
  PRIMARY KEY (`political_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='政治面貌字典表';

-- ----------------------------
-- Table structure for dict_clue_manul
-- ----------------------------
DROP TABLE IF EXISTS `dict_clue_manul`;
CREATE TABLE `dict_clue_manul` (
  `clue_manul_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `url_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '对应落地页id',
  `tab_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'tab对应的名称',
  `tab_sn` int DEFAULT NULL COMMENT 'tab的序号',
  `media_channel_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '媒体渠道id',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1:删除 0：未删除',
  `status` tinyint(1) DEFAULT '1' COMMENT '1:启用 0:停用',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  `ad_mission_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务ID',
  PRIMARY KEY (`clue_manul_id`) USING BTREE,
  KEY `fk_dcm_mc_id` (`media_channel_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  KEY `ad_mission_id` (`ad_mission_id`) USING BTREE,
  CONSTRAINT `dict_clue_manul_ibfk_1` FOREIGN KEY (`media_channel_id`) REFERENCES `media_channel` (`media_channel_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `dict_clue_manul_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `dict_clue_manul_ibfk_3` FOREIGN KEY (`ad_mission_id`) REFERENCES `ad_mission` (`ad_mission_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for dict_clue_maturity
-- ----------------------------
DROP TABLE IF EXISTS `dict_clue_maturity`;
CREATE TABLE `dict_clue_maturity` (
  `clue_maturity_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '线索成熟度id',
  `maturity_level` int DEFAULT NULL COMMENT '成熟等级',
  `level_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '成熟等级名称',
  `level_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '成熟度等级英文',
  `level_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '成熟度等级描述',
  PRIMARY KEY (`clue_maturity_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='线索成熟度字典表';

-- ----------------------------
-- Table structure for dict_clue_source
-- ----------------------------
DROP TABLE IF EXISTS `dict_clue_source`;
CREATE TABLE `dict_clue_source` (
  `clue_source_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '线索来源id',
  `source_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线索来源名称',
  `sn` int DEFAULT NULL COMMENT '序号',
  PRIMARY KEY (`clue_source_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='线索来源字典表';

-- ----------------------------
-- Table structure for dict_clue_stage
-- ----------------------------
DROP TABLE IF EXISTS `dict_clue_stage`;
CREATE TABLE `dict_clue_stage` (
  `clue_stage_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '线索所在的阶段id',
  `stage_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '阶段名称',
  `stage_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '阶段描述',
  `stage_sn` int DEFAULT NULL COMMENT '阶段顺序',
  PRIMARY KEY (`clue_stage_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户线索所在阶段';

-- ----------------------------
-- Table structure for dict_color_theme
-- ----------------------------
DROP TABLE IF EXISTS `dict_color_theme`;
CREATE TABLE `dict_color_theme` (
  `color_theme_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `back_ground_color` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'rgba表示的背景颜色',
  `font_ground_color` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'rgba表示的字体颜色',
  `theme_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '配色方案名称',
  PRIMARY KEY (`color_theme_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='配色方案字典表';

-- ----------------------------
-- Table structure for dict_component_api
-- ----------------------------
DROP TABLE IF EXISTS `dict_component_api`;
CREATE TABLE `dict_component_api` (
  `com_api_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `com_api_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'api名称',
  `com_api_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'api的url链接',
  `com_api_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'api对应的描述',
  `valid_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生效标志位',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '新增时间',
  PRIMARY KEY (`com_api_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for dict_component_type
-- ----------------------------
DROP TABLE IF EXISTS `dict_component_type`;
CREATE TABLE `dict_component_type` (
  `com_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件类型名称',
  `type_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件code',
  PRIMARY KEY (`com_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='组件类型字典表';

-- ----------------------------
-- Table structure for dict_content_scope
-- ----------------------------
DROP TABLE IF EXISTS `dict_content_scope`;
CREATE TABLE `dict_content_scope` (
  `scope_id` int NOT NULL AUTO_INCREMENT COMMENT '属性列别id',
  `scope_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `pid` int DEFAULT NULL COMMENT '父级id',
  `color_theme` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '配色主题',
  `delete_flag` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除',
  PRIMARY KEY (`scope_id`) USING BTREE,
  KEY `color_theme` (`color_theme`) USING BTREE,
  KEY `dict_content_scope_ibfk_2` (`pid`) USING BTREE,
  CONSTRAINT `dict_content_scope_ibfk_1` FOREIGN KEY (`color_theme`) REFERENCES `dict_color_theme` (`color_theme_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `dict_content_scope_ibfk_2` FOREIGN KEY (`pid`) REFERENCES `dict_content_scope` (`scope_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=8045 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='素材应用场景';

-- ----------------------------
-- Table structure for dict_content_source
-- ----------------------------
DROP TABLE IF EXISTS `dict_content_source`;
CREATE TABLE `dict_content_source` (
  `content_source_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `source_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材类型名称',
  `source_input_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否需要录入其他素材来源；content_source_input',
  `place_holder` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材来源place holder',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志位',
  PRIMARY KEY (`content_source_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='素材来源字典';

-- ----------------------------
-- Table structure for dict_content_type
-- ----------------------------
DROP TABLE IF EXISTS `dict_content_type`;
CREATE TABLE `dict_content_type` (
  `content_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容素材类型id',
  `content_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '内容素材类型名称',
  `father_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '父类型id',
  `color_theme_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '配色主题',
  `show_url_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否显示url地址栏；0:无外部链接；1:外部链接格式；2:markdown格式',
  `default_pic_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '默认封面url',
  `suffix_keys` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '后缀名关键字',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志位；0:未删除；1已删除',
  PRIMARY KEY (`content_type_id`) USING BTREE,
  KEY `fk_dict_content_type_dict_color_theme_1` (`color_theme_id`) USING BTREE,
  KEY `fk_dict_content_type_dict_content_type_1` (`father_id`) USING BTREE,
  CONSTRAINT `fk_dict_content_type_dict_color_theme_1` FOREIGN KEY (`color_theme_id`) REFERENCES `dict_color_theme` (`color_theme_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_dict_content_type_dict_content_type_1` FOREIGN KEY (`father_id`) REFERENCES `dict_content_type` (`content_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='内容素材表';

-- ----------------------------
-- Table structure for dict_content_use
-- ----------------------------
DROP TABLE IF EXISTS `dict_content_use`;
CREATE TABLE `dict_content_use` (
  `dict_content_use_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `content_use` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`dict_content_use_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='素材内容使用范围表';

-- ----------------------------
-- Table structure for dict_fission_type
-- ----------------------------
DROP TABLE IF EXISTS `dict_fission_type`;
CREATE TABLE `dict_fission_type` (
  `dict_fission_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `fission_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '裂变类型',
  `color_pattern` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '颜色模式：元组；(rgba(255,255,255,0), rgba(0, 0, 0, 1))',
  PRIMARY KEY (`dict_fission_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='裂变类型字典表';

-- ----------------------------
-- Table structure for dict_function_view
-- ----------------------------
DROP TABLE IF EXISTS `dict_function_view`;
CREATE TABLE `dict_function_view` (
  `function_view_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `view_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`function_view_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='页面类型字典表';

-- ----------------------------
-- Table structure for dict_key_words
-- ----------------------------
DROP TABLE IF EXISTS `dict_key_words`;
CREATE TABLE `dict_key_words` (
  `words_id` int NOT NULL AUTO_INCREMENT,
  `words` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关键词',
  `create_time` datetime DEFAULT NULL COMMENT '创建者',
  `creator_tpu_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者id',
  PRIMARY KEY (`words_id`) USING BTREE,
  KEY `fk_dict_key_words_touchpoint_user_1` (`creator_tpu_id`) USING BTREE,
  CONSTRAINT `dict_key_words_ibfk_1` FOREIGN KEY (`creator_tpu_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=1791 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='关键字字典';

-- ----------------------------
-- Table structure for dict_login_setting
-- ----------------------------
DROP TABLE IF EXISTS `dict_login_setting`;
CREATE TABLE `dict_login_setting` (
  `login_setting_id` int NOT NULL AUTO_INCREMENT,
  `login_setting_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '登录频次名称',
  `login_number` int NOT NULL COMMENT '登录频次天数',
  `sort` int NOT NULL DEFAULT '999' COMMENT '排序',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`login_setting_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='合伙人任务登录频次设置表';

-- ----------------------------
-- Table structure for dict_media_type
-- ----------------------------
DROP TABLE IF EXISTS `dict_media_type`;
CREATE TABLE `dict_media_type` (
  `dict_media_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `media_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '媒体类型名称',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否删除；0：未删除；1：已删除',
  `sn` int DEFAULT NULL COMMENT '媒体排序，正序',
  PRIMARY KEY (`dict_media_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for dict_mini_info
-- ----------------------------
DROP TABLE IF EXISTS `dict_mini_info`;
CREATE TABLE `dict_mini_info` (
  `mini_info_id` int NOT NULL AUTO_INCREMENT,
  `field_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字段名称',
  `field_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字段类型',
  `status` tinyint DEFAULT '1' COMMENT '1:开启 0：关闭',
  `field_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字段编码',
  `field_dict` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字段字典',
  `delete_flag` tinyint DEFAULT '0' COMMENT '0：未删除 1：已删除',
  `sort` int DEFAULT '9999' COMMENT '排序',
  `is_required` tinyint DEFAULT '0' COMMENT '是否必须 1：是 0:否',
  `dict_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '对应字典表名',
  PRIMARY KEY (`mini_info_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='小程序信息维护字典表';

-- ----------------------------
-- Table structure for dict_mini_page
-- ----------------------------
DROP TABLE IF EXISTS `dict_mini_page`;
CREATE TABLE `dict_mini_page` (
  `page_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `page_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'page的url链接地址',
  `page_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '对应的page名称',
  PRIMARY KEY (`page_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for dict_org_type
-- ----------------------------
DROP TABLE IF EXISTS `dict_org_type`;
CREATE TABLE `dict_org_type` (
  `dict_org_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `org_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组织类型名称',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据创建记录',
  PRIMARY KEY (`dict_org_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='组织类型字典';

-- ----------------------------
-- Table structure for dict_partner_contract
-- ----------------------------
DROP TABLE IF EXISTS `dict_partner_contract`;
CREATE TABLE `dict_partner_contract` (
  `tmp_contract_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '合伙人合同模版ID',
  `tmp_contract_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '合同模版名称',
  `tmp_contract_pdf` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '合同模板pdf',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `delete_flag` tinyint DEFAULT '0' COMMENT '0：未删除 1：已删除',
  PRIMARY KEY (`tmp_contract_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='合伙人合同模版表';

-- ----------------------------
-- Table structure for dict_partner_growth_level
-- ----------------------------
DROP TABLE IF EXISTS `dict_partner_growth_level`;
CREATE TABLE `dict_partner_growth_level` (
  `growth_id` int NOT NULL AUTO_INCREMENT,
  `level_id` int DEFAULT NULL COMMENT '合伙人类型ID',
  `growth_level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '成长等级',
  `growth_number` int DEFAULT NULL COMMENT '等级编号',
  `min_value` int DEFAULT NULL COMMENT '所需最小值',
  `max_value` int DEFAULT NULL COMMENT '所需最大值',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权益介绍',
  `work_partner_type` int DEFAULT NULL COMMENT 'work平台对应的ID',
  `rebate_ratio` float(3,2) DEFAULT NULL COMMENT '返点比例',
  `withdrawal_ratio` float(3,2) DEFAULT NULL COMMENT '智邦币提现比例',
  PRIMARY KEY (`growth_id`) USING BTREE,
  KEY `level_id` (`level_id`) USING BTREE,
  CONSTRAINT `dict_partner_growth_level_ibfk_1` FOREIGN KEY (`level_id`) REFERENCES `dict_client_level` (`level_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='合伙人等级权益表';

-- ----------------------------
-- Table structure for dict_pic_resolution
-- ----------------------------
DROP TABLE IF EXISTS `dict_pic_resolution`;
CREATE TABLE `dict_pic_resolution` (
  `pic_resolution_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片分辨率_id',
  `pic_resolution_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片分辨率名称',
  `width_value` int NOT NULL DEFAULT '0' COMMENT '图片宽度-分辨率值',
  `height_value` int NOT NULL DEFAULT '0' COMMENT '图片高度-分辨率值',
  `resolution_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '图片分辨率描述',
  PRIMARY KEY (`pic_resolution_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='照片分辨率字典';

-- ----------------------------
-- Table structure for dict_pic_style
-- ----------------------------
DROP TABLE IF EXISTS `dict_pic_style`;
CREATE TABLE `dict_pic_style` (
  `pic_style_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片风格id',
  `pic_style_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片风格名称',
  PRIMARY KEY (`pic_style_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='图片风格字典表';

-- ----------------------------
-- Table structure for dict_pic_type
-- ----------------------------
DROP TABLE IF EXISTS `dict_pic_type`;
CREATE TABLE `dict_pic_type` (
  `pic_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片类型id',
  `pic_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片类型名称',
  `type_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片类型的值',
  PRIMARY KEY (`pic_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='图片类型字典';

-- ----------------------------
-- Table structure for dict_planning_authority
-- ----------------------------
DROP TABLE IF EXISTS `dict_planning_authority`;
CREATE TABLE `dict_planning_authority` (
  `dict_pa_flag_id` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '策划权限id等级不会超过60种',
  `pa_flag_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '策划权限名称',
  PRIMARY KEY (`dict_pa_flag_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='策划权限等级表';

-- ----------------------------
-- Table structure for dict_product_type
-- ----------------------------
DROP TABLE IF EXISTS `dict_product_type`;
CREATE TABLE `dict_product_type` (
  `product_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '销售产品类型id',
  `product_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '销售产品类型名称',
  `product_type_content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '销售产品的内容，用英文逗号隔开类型',
  `product_type_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品的内容的value值',
  `multi_select_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '0：单选；1：多选',
  `product_type_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品类型code',
  `type_id` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品类型id:1:erp;2:百雁;3:喜鹊;4:mes墨工湖;5:蜂王台',
  PRIMARY KEY (`product_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for dict_qr_pos_type
-- ----------------------------
DROP TABLE IF EXISTS `dict_qr_pos_type`;
CREATE TABLE `dict_qr_pos_type` (
  `qr_pos_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `pos_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '位置信息',
  `default_x` int DEFAULT NULL COMMENT '默认x坐标',
  `default_y` int DEFAULT NULL COMMENT '默认y坐标',
  `default_width` int DEFAULT NULL COMMENT '默认宽度',
  `default_height` int DEFAULT NULL COMMENT '默认高度',
  PRIMARY KEY (`qr_pos_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for dict_question_type
-- ----------------------------
DROP TABLE IF EXISTS `dict_question_type`;
CREATE TABLE `dict_question_type` (
  `question_type_id` int NOT NULL AUTO_INCREMENT COMMENT '类型ID',
  `pid` int DEFAULT NULL COMMENT '父级ID',
  `question_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分类名称',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除1：已删除',
  `answer_number` int NOT NULL DEFAULT '0' COMMENT '回答数量',
  `form` tinyint NOT NULL DEFAULT '1' COMMENT '类型：1：个人分类 2：标准分类',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '员工ID',
  `industry_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户行业ID',
  `sort` int NOT NULL DEFAULT '9999',
  PRIMARY KEY (`question_type_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6554 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='问题分类表';

-- ----------------------------
-- Table structure for dict_region
-- ----------------------------
DROP TABLE IF EXISTS `dict_region`;
CREATE TABLE `dict_region` (
  `region_id` int NOT NULL COMMENT '地区id',
  `pid` int DEFAULT NULL COMMENT '地区父id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '区域名称',
  `region_data` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '区域',
  `region_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '区域全名称',
  PRIMARY KEY (`region_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='区域字典表';

-- ----------------------------
-- Table structure for dict_service_role
-- ----------------------------
DROP TABLE IF EXISTS `dict_service_role`;
CREATE TABLE `dict_service_role` (
  `service_role_id` int NOT NULL AUTO_INCREMENT COMMENT '服务角色ID',
  `service_role_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '服务角色名称',
  `service_role_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '服务角色介绍',
  PRIMARY KEY (`service_role_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='服务角色字典表';

-- ----------------------------
-- Table structure for dict_tags_type
-- ----------------------------
DROP TABLE IF EXISTS `dict_tags_type`;
CREATE TABLE `dict_tags_type` (
  `type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签类型内容',
  `father_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签父id',
  `tags_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签类型id',
  PRIMARY KEY (`tags_type_id`) USING BTREE,
  KEY `tags_type_id` (`tags_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户标签类型字典';

-- ----------------------------
-- Table structure for dict_targeting_type
-- ----------------------------
DROP TABLE IF EXISTS `dict_targeting_type`;
CREATE TABLE `dict_targeting_type` (
  `targeting_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户定向类型id',
  `targeting_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户定向类型名称',
  `father_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户定向父id',
  `default_value` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '默认值',
  `value_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '0:list,列表值；可多选；\n1:连续值，取范围；如年龄：[35,60],意为35-60',
  PRIMARY KEY (`targeting_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户定向类型';

-- ----------------------------
-- Table structure for dict_task_type
-- ----------------------------
DROP TABLE IF EXISTS `dict_task_type`;
CREATE TABLE `dict_task_type` (
  `task_type_id` int NOT NULL AUTO_INCREMENT COMMENT '类型id',
  `type_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型code',
  `type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类型名称',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `delete_flag` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '是否删除 0 否； 1删除',
  PRIMARY KEY (`task_type_id`,`type_code`) USING BTREE,
  KEY `type_code` (`type_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='任务类型字典';

-- ----------------------------
-- Table structure for dict_template_fp_type
-- ----------------------------
DROP TABLE IF EXISTS `dict_template_fp_type`;
CREATE TABLE `dict_template_fp_type` (
  `tfp_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板类型id',
  `tfp_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模板类型名称',
  PRIMARY KEY (`tfp_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='裂变策划计划模板类型表';

-- ----------------------------
-- Table structure for dict_tgh_setting
-- ----------------------------
DROP TABLE IF EXISTS `dict_tgh_setting`;
CREATE TABLE `dict_tgh_setting` (
  `tgh_setting_id` int NOT NULL AUTO_INCREMENT COMMENT '共创设置ID',
  `level_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '级别名称',
  `zb_num` int DEFAULT NULL COMMENT 'Zb币数量',
  `delete_flag` tinyint DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`tgh_setting_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='共创素材AB级的智邦币数量设置表';

-- ----------------------------
-- Table structure for dict_touch_method_type
-- ----------------------------
DROP TABLE IF EXISTS `dict_touch_method_type`;
CREATE TABLE `dict_touch_method_type` (
  `tm_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触达方法类型id',
  `tm_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '触达方法类型名称',
  `tm_type_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '触达方式code',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志：0:未删除；1:已删除',
  PRIMARY KEY (`tm_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='触达方法类型字典表';

-- ----------------------------
-- Table structure for dict_tp_user_role
-- ----------------------------
DROP TABLE IF EXISTS `dict_tp_user_role`;
CREATE TABLE `dict_tp_user_role` (
  `dict_tp_user_role_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `tp_user_role_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '触点人员类型名称',
  `tmp_role_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '是否是临时角色；0:不是；1:是；',
  `role_group_id` int DEFAULT NULL COMMENT 'role组id',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '0:未删除；1:删除',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `tp_user_role_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`dict_tp_user_role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='触点用户类型表';

-- ----------------------------
-- Table structure for dict_tpu_position
-- ----------------------------
DROP TABLE IF EXISTS `dict_tpu_position`;
CREATE TABLE `dict_tpu_position` (
  `tpu_pos_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `pos_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职位名称',
  PRIMARY KEY (`tpu_pos_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for dict_view_level
-- ----------------------------
DROP TABLE IF EXISTS `dict_view_level`;
CREATE TABLE `dict_view_level` (
  `view_level_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单页面等级',
  `level_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '页面名称',
  PRIMARY KEY (`view_level_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='菜单视图等级表';

-- ----------------------------
-- Table structure for dict_view_tracking_type
-- ----------------------------
DROP TABLE IF EXISTS `dict_view_tracking_type`;
CREATE TABLE `dict_view_tracking_type` (
  `tracking_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '埋点类型id',
  `tracking_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '埋点类型名称',
  `tracking_father_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '埋点类型父id',
  PRIMARY KEY (`tracking_type_id`) USING BTREE,
  KEY `fk_dict_view_tracking_type_dict_view_tracking_type_1` (`tracking_father_id`) USING BTREE,
  CONSTRAINT `dict_view_tracking_type_ibfk_1` FOREIGN KEY (`tracking_father_id`) REFERENCES `dict_view_tracking_type` (`tracking_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='页面埋点类型';

-- ----------------------------
-- Table structure for dict_wechat_channels
-- ----------------------------
DROP TABLE IF EXISTS `dict_wechat_channels`;
CREATE TABLE `dict_wechat_channels` (
  `channels_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '视频号ID',
  `channels_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '视频号名称',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除\n1:已删除',
  PRIMARY KEY (`channels_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='微信视频号字典';

-- ----------------------------
-- Table structure for dict_wechat_mp
-- ----------------------------
DROP TABLE IF EXISTS `dict_wechat_mp`;
CREATE TABLE `dict_wechat_mp` (
  `app_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公众号appID',
  `app_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '公众号名称',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除\n1:已删除',
  PRIMARY KEY (`app_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='公众号字典表';

-- ----------------------------
-- Table structure for electric_card_componet
-- ----------------------------
DROP TABLE IF EXISTS `electric_card_componet`;
CREATE TABLE `electric_card_componet` (
  `ecc_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `com_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '电子组件标题',
  `card_type_id` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '0：企业；1：个人；2：产品；3：其他',
  `thumbnail_oss_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '缩略图oss地址',
  `add_tpu_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加人id',
  `add_time` datetime DEFAULT NULL COMMENT '添加时间',
  PRIMARY KEY (`ecc_id`) USING BTREE,
  KEY `fk_ect_tpu_id_copy_1` (`add_tpu_id`) USING BTREE,
  CONSTRAINT `electric_card_componet_ibfk_1` FOREIGN KEY (`add_tpu_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for electric_card_template
-- ----------------------------
DROP TABLE IF EXISTS `electric_card_template`;
CREATE TABLE `electric_card_template` (
  `ect_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `card_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '电子名片名称',
  `card_type_id` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '0：企业；1：个人；2：产品；3：其他',
  `card_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '电子名片内容',
  `cover_oss_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '封面oss地址',
  `add_tpu_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加人id',
  `add_time` datetime DEFAULT NULL COMMENT '添加时间',
  `client_send_count` int DEFAULT NULL COMMENT '给客户发送总次数',
  `client_open_count` int DEFAULT NULL COMMENT '客户打开总次数',
  `client_close_count` int DEFAULT NULL COMMENT '客户关闭总次数',
  `client_avg_time` int DEFAULT NULL COMMENT '客户平均停留时间',
  `client_trans_count` int DEFAULT NULL COMMENT '客户转发次数',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志位；0：未删除；1：已删除',
  `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '删除时间',
  `recovery_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '恢复者',
  `recovery_time` datetime DEFAULT NULL COMMENT '恢复时间',
  `modify_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改者id',
  `qr_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '小程序二维码',
  `publish_status` enum('0','1','2','3','4','5','6') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '5' COMMENT '0:已上架；1:审核中；2:审核拒绝；3:审核通过；4推送失败；5处理中；6已下架',
  `scope` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '可使范围 0:公司；1:个人；',
  PRIMARY KEY (`ect_id`) USING BTREE,
  KEY `fk_ect_tpu_id` (`add_tpu_id`) USING BTREE,
  KEY `fk_ect_tpu_id_2` (`recovery_user_id`) USING BTREE,
  CONSTRAINT `electric_card_template_ibfk_1` FOREIGN KEY (`add_tpu_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `electric_card_template_ibfk_2` FOREIGN KEY (`recovery_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for exam_page
-- ----------------------------
DROP TABLE IF EXISTS `exam_page`;
CREATE TABLE `exam_page` (
  `exam_id` int NOT NULL AUTO_INCREMENT COMMENT '试卷id',
  `exam_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '名字',
  `status` int DEFAULT '0' COMMENT '状态 0密封；1已启封；2已结束',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `time_long` int DEFAULT NULL COMMENT '考试时长/m',
  `delete_flag` int DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `tp_user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  `assembly` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'self' COMMENT '装配方式 self；auto',
  `score` int NOT NULL DEFAULT '100' COMMENT '总分数',
  `qb_type_range` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '题型范围',
  PRIMARY KEY (`exam_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `exam_page_ibfk_1` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='考试试卷';

-- ----------------------------
-- Table structure for f_base_model
-- ----------------------------
DROP TABLE IF EXISTS `f_base_model`;
CREATE TABLE `f_base_model` (
  `base_model_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '基础模型ID',
  `base_model_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型名称',
  `params` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数量',
  `model_name_or_path` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型名称 or  path',
  `template` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '提示模版',
  `status` tinyint(1) DEFAULT '1' COMMENT '1:启用 0：禁用',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `ms` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'modelscope path',
  `hf` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'huggingface path',
  `lora_target` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'all' COMMENT 'LoRA 作用模块',
  `space` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型大小',
  `base_model_type_id` int DEFAULT NULL COMMENT '模型类型 ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`base_model_id`) USING BTREE,
  KEY `f_base_model_base_model_type_id_index` (`base_model_type_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='训练-基础模型表';

-- ----------------------------
-- Table structure for f_base_model_branch
-- ----------------------------
DROP TABLE IF EXISTS `f_base_model_branch`;
CREATE TABLE `f_base_model_branch` (
  `base_model_branch_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '基座模型子模型ID',
  `base_model_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '基座模型ID',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人ID',
  `train_request_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型训练ID',
  `base_model_branch_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '上传基座子模型用户自定义name',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '上传描述',
  `model_name_or_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '魔搭name or 上传服务器文件路径',
  `file_proxy_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户上传文件网络代理地址',
  `machine_model_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最终模型路径',
  `model_source` tinyint(1) DEFAULT NULL COMMENT '来源 1-上传 2-微调',
  `status` tinyint(1) DEFAULT NULL COMMENT '上传状态 1：已上传 0：上传失败  2：上传中 3: 空(非上传)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `delete_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识 1-已删除 0-未删除',
  PRIMARY KEY (`base_model_branch_id`) USING BTREE,
  KEY `f_base_model_branch_base_model_id_index` (`base_model_id`) USING BTREE,
  KEY `idx_tp_user_id` (`tp_user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='基座模型训练子模型表';

-- ----------------------------
-- Table structure for f_base_model_type
-- ----------------------------
DROP TABLE IF EXISTS `f_base_model_type`;
CREATE TABLE `f_base_model_type` (
  `base_model_type_id` int NOT NULL AUTO_INCREMENT COMMENT '递增主键',
  `base_model_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '基座模型类型名',
  `icon_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型类型图标',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `delete_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1: 已删除，0: 未删除',
  PRIMARY KEY (`base_model_type_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='基座模型类型表,eg: 通义大模型';

-- ----------------------------
-- Table structure for f_calc_instances
-- ----------------------------
DROP TABLE IF EXISTS `f_calc_instances`;
CREATE TABLE `f_calc_instances` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'id标识符',
  `resource_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源名称',
  `gpu_list` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'gpu型号',
  `cpu_list` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'cpu核心数',
  `memory` int DEFAULT NULL COMMENT '内存大小',
  `disk` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '磁盘容量',
  `network` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '网络带宽',
  `timestamp` datetime NOT NULL COMMENT '注册时间',
  `status` tinyint NOT NULL COMMENT '状态 1：保存 2：注册算力 3:停用 4:启用',
  `corp_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户识别符',
  PRIMARY KEY (`id`) USING BTREE,
  CONSTRAINT `f_calc_instances_chk_1` CHECK (json_valid(`gpu_list`)),
  CONSTRAINT `f_calc_instances_chk_2` CHECK (json_valid(`cpu_list`))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='已保存、注册算力配置表';

-- ----------------------------
-- Table structure for f_datas
-- ----------------------------
DROP TABLE IF EXISTS `f_datas`;
CREATE TABLE `f_datas` (
  `data_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据集ID',
  `data_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据集名称',
  `data_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据集描述',
  `data_path` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据集存储路径',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态 1：已上传 0：上传失败  2：上传中 4：训练中',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'tp_user_id ',
  `data_source` tinyint(1) DEFAULT '1' COMMENT '数据集来源 1：内置 2：自定义',
  `data_type_id` int DEFAULT NULL COMMENT '数据集类型',
  `register_dataset_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'data_id+文件名',
  `data_format` tinyint(1) DEFAULT NULL COMMENT '0-内置 1-Alpaca 2-Sharegpt',
  PRIMARY KEY (`data_id`) USING BTREE,
  KEY `data_type_id` (`data_type_id`) USING BTREE,
  KEY `f_datas_data_path_index` (`data_path`(768)) USING BTREE COMMENT '上传数据集路径唯一索引,通过路径的uuid控制',
  CONSTRAINT `f_datas_ibfk_1` FOREIGN KEY (`data_type_id`) REFERENCES `f_dict_data_type` (`data_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='训练-数据集表';

-- ----------------------------
-- Table structure for f_dict_data_type
-- ----------------------------
DROP TABLE IF EXISTS `f_dict_data_type`;
CREATE TABLE `f_dict_data_type` (
  `data_type_id` int NOT NULL AUTO_INCREMENT COMMENT '数据集类型ID',
  `data_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据集类型名称',
  `sort` int DEFAULT '9999' COMMENT '排序',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1:已删除 0：未删除',
  PRIMARY KEY (`data_type_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数据集类型字典表';

-- ----------------------------
-- Table structure for f_gpu_specs
-- ----------------------------
DROP TABLE IF EXISTS `f_gpu_specs`;
CREATE TABLE `f_gpu_specs` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'pkey',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '显卡型号',
  `FP32` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最大单精度浮点运算性能',
  `TF32` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最大TF32张量核心运算性能',
  `TF32*` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最大带稀疏性的TF32张量核心运算性能',
  `BF16` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最大BFLOAT16张量核心运算性能',
  `BF16*` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最大带稀疏性的BFLOAT16张量核心运算性能',
  `FP16` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最大半精度浮点张量核心运算性能',
  `FP16*` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最大带稀疏性的半精度浮点张量核心运算性能',
  `INT8` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最大8位整数张量核心运算性能',
  `INT8*` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最大带稀疏性的8位整数张量核心运算性能',
  `INT4` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最大4位整数张量核心运算性能',
  `INT4*` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最大带稀疏性的4位整数张量核心运算性能',
  `FP8` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最大8位浮点运算性能',
  `FP8*` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最大带稀疏性的8位浮点运算性能',
  `GPU_memory` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'GPU显存容量',
  `memory_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '显存类型',
  `memory_bandwidth` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '显存带宽',
  `max_TDP` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最大功耗',
  `FP64` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最大双精度浮点数运算性能',
  `FP64_tf` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最大加速双精度浮点数运算性能',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='GPU算力信息参考表';

-- ----------------------------
-- Table structure for f_model_evaluate
-- ----------------------------
DROP TABLE IF EXISTS `f_model_evaluate`;
CREATE TABLE `f_model_evaluate` (
  `evaluate_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模型评估ID',
  `train_request_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型训练ID',
  `is_saves` tinyint(1) DEFAULT '1' COMMENT '是否保存预测结果 1：是 0：否',
  `output_dir` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '输出目录',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `status` tinyint(1) DEFAULT '1' COMMENT '评估状态1：已保存 2：等待中 3：评估中 4：评估完成 5 失败 6:中断 -1:已取消',
  `register_datasets` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'register_dataset_names name1,name2',
  `cutoff_len` int DEFAULT NULL COMMENT '截断长度',
  `max_samples` int DEFAULT NULL COMMENT '最大样本数',
  `per_device_eval_batch_size` int DEFAULT NULL COMMENT '批处理大小',
  `max_new_tokens` int DEFAULT NULL COMMENT '最大生成长度',
  `top_p` decimal(5,2) DEFAULT NULL COMMENT 'top-p 采样值',
  `temperature` decimal(5,2) DEFAULT NULL COMMENT '温度系数',
  `update_time` datetime DEFAULT NULL COMMENT '更新detail info时间',
  `evaluate_time` datetime DEFAULT NULL COMMENT '开始评估时间',
  `predict_results` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型评估结果',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `is_urgency` tinyint(1) DEFAULT '0' COMMENT '紧急程度 1：紧急 0：非紧急',
  `elapsed_times` int DEFAULT '0' COMMENT '已流评估时长 单位秒',
  `remaining_times` int DEFAULT '0' COMMENT '剩余评估时长 单位秒',
  `percentage` decimal(5,2) DEFAULT '0.00' COMMENT '评估进度',
  `request_time` datetime DEFAULT NULL COMMENT '提交评估申请时间',
  `success_time` datetime DEFAULT NULL COMMENT '评估完成时间',
  `process_id` int DEFAULT NULL COMMENT '进程ID',
  `resource` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所需资源',
  PRIMARY KEY (`evaluate_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  KEY `train_request_id` (`train_request_id`) USING BTREE,
  CONSTRAINT `f_model_evaluate_ibfk_1` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `f_model_evaluate_ibfk_2` FOREIGN KEY (`train_request_id`) REFERENCES `f_train_request` (`train_request_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='训练-模型评估表';

-- ----------------------------
-- Table structure for f_params
-- ----------------------------
DROP TABLE IF EXISTS `f_params`;
CREATE TABLE `f_params` (
  `param_id` int NOT NULL AUTO_INCREMENT,
  `param_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数名称',
  `param_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数描述',
  `param_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数编码',
  `param_type_id` int DEFAULT NULL COMMENT '参数类型ID',
  `default_value` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数默认值',
  `value_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数值类型 int str',
  `allowed_value` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '允许选择值',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `sort` int DEFAULT '99' COMMENT '排序',
  `is_factory` tinyint(1) DEFAULT '1' COMMENT '是否是llamafactory参数 1：是 0：否',
  `api_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'apiurl',
  `is_required` tinyint(1) DEFAULT '0' COMMENT '是否必填 1：是 0：否',
  `is_rel_data` tinyint(1) DEFAULT '0' COMMENT '是否有关联数据 1：是 0：否',
  `placeholder` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'placeholder',
  `father_param_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '依赖的param_code',
  `subsidiary_param_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '儿子的param_code',
  PRIMARY KEY (`param_id`) USING BTREE,
  KEY `param_type_id` (`param_type_id`) USING BTREE,
  CONSTRAINT `f_params_ibfk_1` FOREIGN KEY (`param_type_id`) REFERENCES `f_params_type` (`param_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='训练-参数表';

-- ----------------------------
-- Table structure for f_params_config
-- ----------------------------
DROP TABLE IF EXISTS `f_params_config`;
CREATE TABLE `f_params_config` (
  `param_config_id` int NOT NULL AUTO_INCREMENT COMMENT '保存参数ID',
  `param_config_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数保存名称',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1:已删除 0：未删除',
  PRIMARY KEY (`param_config_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=55 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='训练-参数配置保存';

-- ----------------------------
-- Table structure for f_params_config_detail
-- ----------------------------
DROP TABLE IF EXISTS `f_params_config_detail`;
CREATE TABLE `f_params_config_detail` (
  `param_config_detail_id` int NOT NULL AUTO_INCREMENT COMMENT '训练参数ID',
  `param_config_id` int DEFAULT NULL COMMENT '参数保存ID',
  `param_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数编码',
  `param_value` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数值',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1:已删除 0：未删除',
  PRIMARY KEY (`param_config_detail_id`) USING BTREE,
  KEY `train_request_id` (`param_config_id`) USING BTREE,
  CONSTRAINT `f_params_config_detail_ibfk_1` FOREIGN KEY (`param_config_id`) REFERENCES `f_params_config` (`param_config_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=2923 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='训练-训练请求参数表';

-- ----------------------------
-- Table structure for f_params_type
-- ----------------------------
DROP TABLE IF EXISTS `f_params_type`;
CREATE TABLE `f_params_type` (
  `param_type_id` int NOT NULL AUTO_INCREMENT COMMENT '参数类型ID',
  `param_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数类型名',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `step_id` int DEFAULT NULL COMMENT '步骤ID',
  `is_fold` tinyint(1) DEFAULT '0' COMMENT '是否折叠 1：是 0：否',
  PRIMARY KEY (`param_type_id`) USING BTREE,
  KEY `step_id` (`step_id`) USING BTREE,
  CONSTRAINT `f_params_type_ibfk_1` FOREIGN KEY (`step_id`) REFERENCES `f_params_type_step` (`step_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='训练-参数类型表';

-- ----------------------------
-- Table structure for f_params_type_step
-- ----------------------------
DROP TABLE IF EXISTS `f_params_type_step`;
CREATE TABLE `f_params_type_step` (
  `step_id` int NOT NULL AUTO_INCREMENT COMMENT '步骤ID',
  `step_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '步骤名称',
  `delete_flag` tinyint(1) DEFAULT NULL COMMENT '1:已删除 0：未删除',
  `sort` tinyint DEFAULT '99' COMMENT '排序',
  PRIMARY KEY (`step_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='训练-参数类型步骤表';

-- ----------------------------
-- Table structure for f_prompt_template
-- ----------------------------
DROP TABLE IF EXISTS `f_prompt_template`;
CREATE TABLE `f_prompt_template` (
  `template_id` int NOT NULL AUTO_INCREMENT,
  `template` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `delete_flag` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`template_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='训练-模型模板表';

-- ----------------------------
-- Table structure for f_queue
-- ----------------------------
DROP TABLE IF EXISTS `f_queue`;
CREATE TABLE `f_queue` (
  `queue_id` int NOT NULL AUTO_INCREMENT,
  `queue_type` tinyint DEFAULT '1' COMMENT '任务类型 1：模型训练 2模型评估',
  `train_request_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型训练请求ID',
  `evaluate_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型评估ID',
  `location` int DEFAULT NULL COMMENT '队列位置',
  `edit_tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '调整队列人员',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新队列时间',
  `is_edit` tinyint(1) DEFAULT '0' COMMENT '是否调整过队列 1：已调整 0：未调整',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1:已删除 0:未删除',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`queue_id`) USING BTREE,
  KEY `train_request_id` (`train_request_id`) USING BTREE,
  KEY `evaluate_id` (`evaluate_id`) USING BTREE,
  CONSTRAINT `f_queue_ibfk_1` FOREIGN KEY (`train_request_id`) REFERENCES `f_train_request` (`train_request_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `f_queue_ibfk_2` FOREIGN KEY (`evaluate_id`) REFERENCES `f_model_evaluate` (`evaluate_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=175 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='训练-队列表';

-- ----------------------------
-- Table structure for f_train_request
-- ----------------------------
DROP TABLE IF EXISTS `f_train_request`;
CREATE TABLE `f_train_request` (
  `train_request_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '训练请求ID',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '申请人ID',
  `status` tinyint DEFAULT '1' COMMENT '1：已保存 2：等待中 3：训练中 4：训练完成 5 训练失败 6:训练中断 -1:已取消',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `model_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型名称',
  `model_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型描述',
  `is_urgency` tinyint(1) DEFAULT NULL COMMENT '紧急程度 1：紧急 0：非紧急',
  `model_icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型图标',
  `base_model_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '基础模型ID',
  `is_publish` tinyint(1) DEFAULT '0' COMMENT '是否发布 1：已发布 0：未发布',
  `train_param_config_id` int DEFAULT NULL COMMENT '加载训练参数',
  `elapsed_times` int DEFAULT '0' COMMENT '已训练时长 单位秒',
  `remaining_times` int DEFAULT '0' COMMENT '剩余训练时长 单位秒',
  `percentage` float(5,2) DEFAULT '0.00' COMMENT '训练进度',
  `request_time` datetime DEFAULT NULL COMMENT '提交训练申请时间',
  `success_time` datetime DEFAULT NULL COMMENT '训练完成时间',
  `process_id` int DEFAULT NULL COMMENT '进程ID',
  `publish_model_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '发布模型path',
  `publish_download_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '发布模型下载url',
  `resource` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所需资源',
  PRIMARY KEY (`train_request_id`) USING BTREE,
  KEY `base_model_id` (`base_model_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `f_train_request_ibfk_1` FOREIGN KEY (`base_model_id`) REFERENCES `f_base_model` (`base_model_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `f_train_request_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='训练-训练请求表';

-- ----------------------------
-- Table structure for f_train_request_params
-- ----------------------------
DROP TABLE IF EXISTS `f_train_request_params`;
CREATE TABLE `f_train_request_params` (
  `train_param_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '训练参数ID',
  `train_request_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '训练请求ID',
  `param_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数编码',
  `param_value` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数值',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1:已删除 0：未删除',
  PRIMARY KEY (`train_param_id`) USING BTREE,
  KEY `train_request_id` (`train_request_id`) USING BTREE,
  CONSTRAINT `f_train_request_params_ibfk_1` FOREIGN KEY (`train_request_id`) REFERENCES `f_train_request` (`train_request_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='训练-训练请求参数表';

-- ----------------------------
-- Table structure for fission_mission
-- ----------------------------
DROP TABLE IF EXISTS `fission_mission`;
CREATE TABLE `fission_mission` (
  `fm_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `fm_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '裂变任务编号',
  `fp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '裂变策划id',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '触点人员id',
  `ab_test_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设置ab测试id',
  `expert_bgn_time` datetime NOT NULL COMMENT '期望开始时间',
  `expert_end_time` datetime DEFAULT NULL COMMENT '期望结束时间',
  `bgn_time` datetime DEFAULT NULL COMMENT '实际开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '实际结束时间',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '删除标志位：0：未删除；1：已删除；',
  `status_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '启动状态；0：未启动；1：进行中；2：已完成；3：已停用',
  `channel_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '渠道id',
  `edit_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '模板是否可编辑标志位；0:不可编辑；1:可编辑',
  `exec_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '任务是否执行；0：未执行；1：已执行',
  `qr_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务对应的二维码，添加了个人配置信息',
  `qr_config_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加至qr_code的config code信息',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`fm_id`) USING BTREE,
  KEY `fk_fission_mission_fission_planning_1` (`fp_id`) USING BTREE,
  KEY `fk_fission_mission_touchpoint_user_1` (`tp_user_id`) USING BTREE,
  KEY `fk_fission_mission_config_ab_test_1` (`ab_test_id`) USING BTREE,
  KEY `fk_fission_mission_config_channel_1` (`channel_id`) USING BTREE,
  CONSTRAINT `fission_mission_ibfk_1` FOREIGN KEY (`ab_test_id`) REFERENCES `config_ab_test` (`config_ab_test_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fission_mission_ibfk_2` FOREIGN KEY (`channel_id`) REFERENCES `config_channel` (`channel_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fission_mission_ibfk_3` FOREIGN KEY (`fp_id`) REFERENCES `fission_planning` (`fission_planning_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fission_mission_ibfk_4` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='裂变策划任务表\r\n跟触点发生关系';

-- ----------------------------
-- Table structure for fission_planning
-- ----------------------------
DROP TABLE IF EXISTS `fission_planning`;
CREATE TABLE `fission_planning` (
  `fission_planning_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `fission_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '裂变类型id',
  `fp_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '裂变策划名称',
  `cfg_monitor_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '监测配置id',
  `cfg_bonus_tpu_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '触点用户激励id',
  `cfg_bonus_client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户激励id',
  `template_fp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '策划模板id',
  `create_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建用户',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `subject` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '策划主题',
  `start_time` datetime DEFAULT NULL COMMENT '策划开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '策划结束时间',
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '启动状态；0：未启动；1：进行中；2：已结束； 3：已停用',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '删除标志位；0：未删除；1：删除',
  `content_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材类型id',
  `ad_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文案标题，限制50字',
  `ad_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '文案内容，限制200字',
  `fp_org_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '0:个人策划；1:组织策划；2:策划模板；',
  `client_radar_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户雷达id',
  `fp_mission_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '策划生成任务类型；0:直接任务；1:间接任务',
  `use_radar_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '0:使用普通素材；1:使用雷达素材；',
  `use_content_qr_pos` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '1:使用素材对应二维码位置；0:不使用素材对应二维码位置',
  `all_touchpoint_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '1:使用全部触点；0:不使用全部触点',
  `recovery_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '恢复人id',
  `recovery_time` datetime DEFAULT NULL COMMENT '恢复时间',
  `modify_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改者id',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `self_touchpoint_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '是否开启个人渠道触点 0：未开启 1：已开启',
  PRIMARY KEY (`fission_planning_id`) USING BTREE,
  KEY `fk_fission_planning_dict_fission_type_1` (`fission_type_id`) USING BTREE,
  KEY `fk_fission_planning_config_monitor_1` (`cfg_monitor_id`) USING BTREE,
  KEY `fk_fission_planning_config_bonus_tpu_1` (`cfg_bonus_tpu_id`) USING BTREE,
  KEY `fk_fission_planning_config_bonus_client_1` (`cfg_bonus_client_id`) USING BTREE,
  KEY `fk_fission_planning_template_fp_1` (`template_fp_id`) USING BTREE,
  KEY `fk_fission_planning_touchpoint_user_1` (`create_user_id`) USING BTREE,
  KEY `fk_fission_planning_dict_content_type_1` (`content_type_id`) USING BTREE,
  KEY `fk_fission_planning_client_radar_id_1` (`client_radar_id`) USING BTREE,
  KEY `fk_fission_planning_touchpoint_user_2` (`recovery_user_id`) USING BTREE,
  CONSTRAINT `fission_planning_ibfk_1` FOREIGN KEY (`client_radar_id`) REFERENCES `client_radar` (`client_radar_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fission_planning_ibfk_2` FOREIGN KEY (`cfg_bonus_client_id`) REFERENCES `config_bonus_client` (`bonus_client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fission_planning_ibfk_3` FOREIGN KEY (`cfg_bonus_tpu_id`) REFERENCES `config_bonus_tpu` (`bonus_tpu_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fission_planning_ibfk_4` FOREIGN KEY (`cfg_monitor_id`) REFERENCES `config_monitor` (`monitor_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fission_planning_ibfk_5` FOREIGN KEY (`content_type_id`) REFERENCES `dict_content_type` (`content_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fission_planning_ibfk_6` FOREIGN KEY (`fission_type_id`) REFERENCES `dict_fission_type` (`dict_fission_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fission_planning_ibfk_7` FOREIGN KEY (`template_fp_id`) REFERENCES `template_fp` (`template_fp_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fission_planning_ibfk_8` FOREIGN KEY (`create_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fission_planning_ibfk_9` FOREIGN KEY (`recovery_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='裂变策划计划表';

-- ----------------------------
-- Table structure for fm_client
-- ----------------------------
DROP TABLE IF EXISTS `fm_client`;
CREATE TABLE `fm_client` (
  `fm_client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `fm_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '裂变任务id',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户id',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '任务生成时间',
  PRIMARY KEY (`fm_client_id`) USING BTREE,
  KEY `fk_fm_client_fission_mission_1` (`fm_id`) USING BTREE,
  KEY `fk_fm_client_client_1` (`client_id`) USING BTREE,
  CONSTRAINT `fm_client_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fm_client_ibfk_2` FOREIGN KEY (`fm_id`) REFERENCES `fission_mission` (`fm_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='裂变任务客户表';

-- ----------------------------
-- Table structure for fm_data
-- ----------------------------
DROP TABLE IF EXISTS `fm_data`;
CREATE TABLE `fm_data` (
  `fm_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '与fission_mission表是一对一关系',
  `last_update_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `convert_client_num` int NOT NULL DEFAULT '0' COMMENT '覆盖客户数',
  `interactivity_client_num` int NOT NULL DEFAULT '0' COMMENT '互动客户数',
  `fission_point_num` int NOT NULL DEFAULT '0' COMMENT '裂变触点数',
  `content_num` int NOT NULL DEFAULT '0' COMMENT '任务素材数量',
  `fission_index` float(10,2) NOT NULL DEFAULT '0.00' COMMENT '裂变指数：0-5000',
  `interactivity_count` int NOT NULL DEFAULT '0' COMMENT '互动量',
  `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞量',
  `retweets_count` int NOT NULL DEFAULT '0' COMMENT '转发量',
  `pageview_count` int NOT NULL DEFAULT '0' COMMENT '阅读量',
  `finish_read_count` int NOT NULL DEFAULT '0' COMMENT '阅读结束量',
  `total_read_time` int NOT NULL DEFAULT '0' COMMENT '总阅读时间(毫秒)',
  `avg_read_time` int NOT NULL DEFAULT '0' COMMENT '平均阅读时间(毫秒)',
  `comment_count` int NOT NULL DEFAULT '0' COMMENT '评论量',
  `new_clue_num` int NOT NULL DEFAULT '0' COMMENT '产生新线索量',
  `active_clue_num` int NOT NULL DEFAULT '0' COMMENT '激活线索量',
  `primary_fission_num` float(50,2) NOT NULL DEFAULT '100.00' COMMENT '一级裂变率',
  `multiple_fission_num` float(50,2) NOT NULL DEFAULT '100.00' COMMENT '多级裂变率',
  `fission_comprehensive_score` int NOT NULL DEFAULT '0' COMMENT '裂变综合评分',
  `fission_communication_score` int NOT NULL DEFAULT '0' COMMENT '裂变传播评分',
  `fission_roi_score` int NOT NULL DEFAULT '0',
  `active_client_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '激活客户ids',
  PRIMARY KEY (`fm_id`) USING BTREE,
  CONSTRAINT `fm_data_ibfk_1` FOREIGN KEY (`fm_id`) REFERENCES `fission_mission` (`fm_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='裂变任务数据表';

-- ----------------------------
-- Table structure for fm_estimation
-- ----------------------------
DROP TABLE IF EXISTS `fm_estimation`;
CREATE TABLE `fm_estimation` (
  `fm_estimation_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '裂变任务的评估id',
  `fm_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '裂变任务id',
  `fission_index` decimal(8,2) DEFAULT NULL COMMENT '裂变指数',
  PRIMARY KEY (`fm_estimation_id`) USING BTREE,
  KEY `fk_fm_estimation_fission_mission_1` (`fm_id`) USING BTREE,
  CONSTRAINT `fm_estimation_ibfk_1` FOREIGN KEY (`fm_id`) REFERENCES `fission_mission` (`fm_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='裂变任务评估';

-- ----------------------------
-- Table structure for fm_estimation_time
-- ----------------------------
DROP TABLE IF EXISTS `fm_estimation_time`;
CREATE TABLE `fm_estimation_time` (
  `fme_time_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `fm_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'fm_id',
  `pageview_count` int DEFAULT NULL COMMENT '客户查看数',
  `finish_read_count` int DEFAULT NULL COMMENT '结束阅读数',
  `like_count` int DEFAULT NULL COMMENT '点赞数',
  `retweets_count` int DEFAULT NULL COMMENT '转发数',
  `comment_count` int DEFAULT NULL COMMENT '评论数',
  `total_read_time` int DEFAULT NULL COMMENT '总阅读时间：毫秒',
  `new_clue_num` int DEFAULT NULL COMMENT '新客户数',
  `fission_index` decimal(8,2) DEFAULT NULL COMMENT '裂变指数',
  `stat_date` date DEFAULT NULL COMMENT '统计时间',
  PRIMARY KEY (`fme_time_id`) USING BTREE,
  KEY `fk_fm_estimation_time_fission_mission_1` (`fm_id`) USING BTREE,
  CONSTRAINT `fm_estimation_time_ibfk_1` FOREIGN KEY (`fm_id`) REFERENCES `fission_mission` (`fm_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for fp_data
-- ----------------------------
DROP TABLE IF EXISTS `fp_data`;
CREATE TABLE `fp_data` (
  `fp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '与fission_planning表是一对一关系',
  `last_update_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `convert_client_num` int NOT NULL DEFAULT '0' COMMENT '覆盖客户数',
  `fission_point_num` int NOT NULL DEFAULT '0' COMMENT '裂变触点数',
  `content_num` int NOT NULL DEFAULT '0' COMMENT '任务素材数量',
  `fission_index` float(10,2) NOT NULL DEFAULT '0.00' COMMENT '裂变指数：0-5000',
  `interactivity_count` int NOT NULL DEFAULT '0' COMMENT '互动量',
  `like_count` int NOT NULL DEFAULT '0' COMMENT '点赞量',
  `retweets_count` int NOT NULL DEFAULT '0' COMMENT '转发量',
  `pageview_count` int NOT NULL DEFAULT '0' COMMENT '阅读量',
  `finish_read_count` int NOT NULL DEFAULT '0' COMMENT '阅读结束人数',
  `total_read_time` int NOT NULL DEFAULT '0' COMMENT '总阅读时长',
  `avg_read_time` int NOT NULL DEFAULT '0' COMMENT '平均阅读时长',
  `comment_count` int NOT NULL DEFAULT '0' COMMENT '评论量',
  `new_clue_num` int NOT NULL DEFAULT '0' COMMENT '产生新线索量',
  `active_clue_num` int NOT NULL DEFAULT '0' COMMENT '激活线索量',
  `primary_fission_num` float(50,2) NOT NULL DEFAULT '40.00' COMMENT '一级裂变量',
  `multiple_fission_num` float(50,2) NOT NULL DEFAULT '40.00' COMMENT '多级裂变量',
  `fission_comprehensive_score` int NOT NULL DEFAULT '0' COMMENT '裂变综合评分',
  `fission_communication_score` int NOT NULL DEFAULT '0' COMMENT '裂变传播评分',
  `fission_roi_score` int NOT NULL DEFAULT '0' COMMENT '裂变roi评分',
  `mission_num` int NOT NULL DEFAULT '0' COMMENT '裂变任务数',
  `tp_user_num` int NOT NULL DEFAULT '0' COMMENT '裂变触点员工数',
  `channel_num` int NOT NULL DEFAULT '0' COMMENT '落地渠道数',
  PRIMARY KEY (`fp_id`) USING BTREE,
  CONSTRAINT `fp_data_ibfk_1` FOREIGN KEY (`fp_id`) REFERENCES `fission_planning` (`fission_planning_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='裂变策划计划数据表';

-- ----------------------------
-- Table structure for fp_estimation
-- ----------------------------
DROP TABLE IF EXISTS `fp_estimation`;
CREATE TABLE `fp_estimation` (
  `fp_estimation_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '策划计划评估id',
  `fp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '策划计划id',
  `convert_client_num` int DEFAULT '0' COMMENT '覆盖客户数',
  `convert_tpu_num` int DEFAULT NULL COMMENT '覆盖裂变员工触点；tpu：touchpoint_user',
  `clue_num` int unsigned DEFAULT '0' COMMENT '生成线索数',
  `fission_index` float(8,2) DEFAULT '0.00' COMMENT '裂变指数：0-1',
  PRIMARY KEY (`fp_estimation_id`) USING BTREE,
  KEY `fk_fp_estimation_fission_planning_1` (`fp_id`) USING BTREE,
  CONSTRAINT `fp_estimation_ibfk_1` FOREIGN KEY (`fp_id`) REFERENCES `fission_planning` (`fission_planning_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='策划计划评估表';

-- ----------------------------
-- Table structure for fp_estimation_time
-- ----------------------------
DROP TABLE IF EXISTS `fp_estimation_time`;
CREATE TABLE `fp_estimation_time` (
  `fpe_time_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '策划计划评估时间数据',
  `fp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '策划评估id',
  `fission_index` float(8,2) DEFAULT NULL COMMENT '裂变指数:0-5000',
  `record_time` date DEFAULT NULL COMMENT '评估时间',
  `pageview_count` int DEFAULT NULL COMMENT '阅读量',
  `finish_read_count` int DEFAULT NULL COMMENT '完成量',
  `like_count` int DEFAULT NULL COMMENT '点赞量',
  `retweets_count` int DEFAULT NULL COMMENT '转发量',
  `comment_count` int DEFAULT NULL COMMENT '评论量',
  `total_read_time` int DEFAULT NULL COMMENT '总阅读时间',
  `new_clue_num` int DEFAULT NULL COMMENT '新线索量',
  PRIMARY KEY (`fpe_time_id`) USING BTREE,
  KEY `fk_fp_estimation_time_fission_planning_1` (`fp_id`) USING BTREE,
  CONSTRAINT `fp_estimation_time_ibfk_1` FOREIGN KEY (`fp_id`) REFERENCES `fission_planning` (`fission_planning_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='策划计划评估时序数据（同步mongodb）';

-- ----------------------------
-- Table structure for function_point
-- ----------------------------
DROP TABLE IF EXISTS `function_point`;
CREATE TABLE `function_point` (
  `func_point_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `sale_version_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '销售版本id',
  `func_point_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '功能点名称',
  `father_point_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '父功能id',
  `auth_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限id',
  PRIMARY KEY (`func_point_id`) USING BTREE,
  KEY `fk_function_point_sale_version_1` (`sale_version_id`) USING BTREE,
  KEY `fk_function_point_function_point_1` (`father_point_id`) USING BTREE,
  KEY `fk_function_point_dict_auth_1` (`auth_id`) USING BTREE,
  CONSTRAINT `function_point_ibfk_1` FOREIGN KEY (`auth_id`) REFERENCES `dict_auth` (`dict_auth_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `function_point_ibfk_2` FOREIGN KEY (`father_point_id`) REFERENCES `function_point` (`func_point_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `function_point_ibfk_3` FOREIGN KEY (`sale_version_id`) REFERENCES `sale_version` (`sale_version_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='软件功能点表';

-- ----------------------------
-- Table structure for function_view
-- ----------------------------
DROP TABLE IF EXISTS `function_view`;
CREATE TABLE `function_view` (
  `function_view_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '功能视图id',
  `function_point_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '功能点id',
  `father_view_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '父功能视图id',
  `view_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '功能视图名称',
  `view_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '功能视图url',
  `view_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '视图类型id',
  `view_icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '视图菜单icon',
  `view_level_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '视图等级id',
  `redirect_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '重定向地址',
  `component_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件地址',
  `view_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '页面代码',
  `search_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '如果是配置页面，search_content的placeholder',
  `help_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '页面帮助desc',
  `status` tinyint(1) DEFAULT '1' COMMENT '1：显示 0：隐藏',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1:已删除 0：未删除',
  PRIMARY KEY (`function_view_id`) USING BTREE,
  KEY `fk_function_view_function_point_1` (`function_point_id`) USING BTREE,
  KEY `fk_function_view_function_view_1` (`father_view_id`) USING BTREE,
  KEY `fk_function_view_dict_function_view_1` (`view_type_id`) USING BTREE,
  KEY `fk_function_view_dict_view_level_1` (`view_level_id`) USING BTREE,
  CONSTRAINT `function_view_ibfk_1` FOREIGN KEY (`view_type_id`) REFERENCES `dict_function_view` (`function_view_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `function_view_ibfk_2` FOREIGN KEY (`view_level_id`) REFERENCES `dict_view_level` (`view_level_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `function_view_ibfk_3` FOREIGN KEY (`function_point_id`) REFERENCES `function_point` (`func_point_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `function_view_ibfk_4` FOREIGN KEY (`father_view_id`) REFERENCES `function_view` (`function_view_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='功能点页面';

-- ----------------------------
-- Table structure for help_info
-- ----------------------------
DROP TABLE IF EXISTS `help_info`;
CREATE TABLE `help_info` (
  `help_info_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `sale_version_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '版本号',
  `info_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '帮助名称',
  `info_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '帮助链接',
  `order_sn` int DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`help_info_id`) USING BTREE,
  KEY `fk_help_info_sale_version_1` (`sale_version_id`) USING BTREE,
  CONSTRAINT `help_info_ibfk_1` FOREIGN KEY (`sale_version_id`) REFERENCES `sale_version` (`sale_version_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='帮助信息表-跟版本相关';

-- ----------------------------
-- Table structure for landpage
-- ----------------------------
DROP TABLE IF EXISTS `landpage`;
CREATE TABLE `landpage` (
  `landpage_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `admission_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '投放任务id',
  `father_landpage_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模板landpageid',
  `landpage_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '落地页名称',
  `landpage_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '落地页标题',
  `landpage_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '落地页url',
  `landpage_cover_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '落地页封面url',
  `add_time` datetime DEFAULT NULL COMMENT '添加时间',
  `add_tpu_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者',
  `landpage_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '落地页内容',
  `show_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '0:H5;1:web',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志位：0：未删除；1：删除；',
  `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`landpage_id`) USING BTREE,
  KEY `fk_landpage_tpu_id_1` (`add_tpu_id`) USING BTREE,
  KEY `fk_landpage_ad_mission_1` (`admission_id`) USING BTREE,
  CONSTRAINT `landpage_ibfk_1` FOREIGN KEY (`admission_id`) REFERENCES `ad_mission` (`ad_mission_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `landpage_ibfk_2` FOREIGN KEY (`add_tpu_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for landpage_component
-- ----------------------------
DROP TABLE IF EXISTS `landpage_component`;
CREATE TABLE `landpage_component` (
  `landpage_com_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `com_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `com_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件名称',
  PRIMARY KEY (`landpage_com_id`) USING BTREE,
  KEY `fk_landpage_component_com_type_1` (`com_type_id`) USING BTREE,
  CONSTRAINT `landpage_component_ibfk_1` FOREIGN KEY (`com_type_id`) REFERENCES `com_type` (`com_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for landpage_conf_json
-- ----------------------------
DROP TABLE IF EXISTS `landpage_conf_json`;
CREATE TABLE `landpage_conf_json` (
  `lcs_id` bigint NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '落地页数据',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '备注',
  `add_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '是否删除；1 已删除；0启用',
  PRIMARY KEY (`lcs_id`) USING BTREE,
  KEY `tp_user` (`add_user`) USING BTREE,
  CONSTRAINT `landpage_conf_json_ibfk_1` FOREIGN KEY (`add_user`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for landpage_data
-- ----------------------------
DROP TABLE IF EXISTS `landpage_data`;
CREATE TABLE `landpage_data` (
  `landpage_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务落地页id',
  `show_num` int DEFAULT '0' COMMENT '展示量',
  `click_num` int DEFAULT '0' COMMENT '点击数',
  `open_num` int DEFAULT '0' COMMENT '打开数',
  `read_time` int DEFAULT '0' COMMENT '阅读时长，毫秒',
  `commit_num` int DEFAULT '0' COMMENT '提交数',
  `roi` decimal(5,2) DEFAULT '0.00' COMMENT '投入产出比',
  `click_rate` decimal(5,2) DEFAULT '0.00' COMMENT '点击率',
  `commit_rate` decimal(5,2) DEFAULT '0.00' COMMENT '提交率',
  `ocpm` decimal(10,2) DEFAULT '0.00' COMMENT '千次展示量费用',
  `add_time` datetime DEFAULT NULL COMMENT '添加时间',
  `total_cost` decimal(10,2) DEFAULT '0.00' COMMENT '花费',
  `total_return` decimal(10,2) DEFAULT '0.00' COMMENT '总回款',
  `total_sign` decimal(10,2) DEFAULT '0.00' COMMENT '总签单',
  PRIMARY KEY (`landpage_id`) USING BTREE,
  CONSTRAINT `landpage_data_ibfk_1` FOREIGN KEY (`landpage_id`) REFERENCES `landpage` (`landpage_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for landpage_data_time
-- ----------------------------
DROP TABLE IF EXISTS `landpage_data_time`;
CREATE TABLE `landpage_data_time` (
  `ad_land_data_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `stat_date` date DEFAULT NULL COMMENT '统计日期',
  `landpage_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务落地页id',
  `show_num` int DEFAULT '0' COMMENT '展示量',
  `click_num` int DEFAULT '0' COMMENT '点击数',
  `open_num` int DEFAULT '0' COMMENT '打开数',
  `read_time` int DEFAULT '0' COMMENT '阅读时长，毫秒',
  `commit_num` int DEFAULT '0' COMMENT '提交数',
  `roi` decimal(5,2) DEFAULT '0.00' COMMENT '投入产出比',
  `click_rate` decimal(5,2) DEFAULT '0.00' COMMENT '点击率',
  `commit_rate` decimal(5,2) DEFAULT '0.00' COMMENT '提交率',
  `ocpm` decimal(10,2) DEFAULT '0.00' COMMENT '千次展示量费用',
  `add_time` datetime DEFAULT NULL COMMENT '添加时间',
  PRIMARY KEY (`ad_land_data_id`) USING BTREE,
  KEY `fk_landpage_data_time_landpage_data_1` (`landpage_id`) USING BTREE,
  CONSTRAINT `landpage_data_time_ibfk_1` FOREIGN KEY (`landpage_id`) REFERENCES `landpage_data` (`landpage_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for marketing
-- ----------------------------
DROP TABLE IF EXISTS `marketing`;
CREATE TABLE `marketing` (
  `mkt_id` bigint NOT NULL COMMENT '自动营销流程ID',
  `mkt_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '流程名称',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '是否启用 1：启用 0：停用',
  `run_num` int NOT NULL DEFAULT '0' COMMENT '执行次数',
  `total_client_num` int NOT NULL DEFAULT '0' COMMENT '覆盖总客户数',
  `my_client_num` int NOT NULL DEFAULT '0' COMMENT '覆盖我的客户\n',
  `mkt_type` tinyint NOT NULL DEFAULT '1' COMMENT '类型：1：公司级 2：个人级',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`mkt_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='自动营销流程表';

-- ----------------------------
-- Table structure for media_channel
-- ----------------------------
DROP TABLE IF EXISTS `media_channel`;
CREATE TABLE `media_channel` (
  `media_channel_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `media_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '媒体类型id',
  `media_channel_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '媒体渠道名称',
  `add_tpu_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加用户id',
  `add_time` datetime DEFAULT NULL COMMENT '添加时间',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '0:未删除；1：已删除',
  `channel_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '渠道码',
  `master_tpu_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '负责人',
  `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `recovery_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '恢复人',
  `recovery_time` datetime DEFAULT NULL COMMENT '恢复时间',
  `modify_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改者id',
  PRIMARY KEY (`media_channel_id`) USING BTREE,
  KEY `fk_media_channel_dict_media_type_1` (`media_type_id`) USING BTREE,
  KEY `fk_media_channel_tpu_id_1` (`add_tpu_id`) USING BTREE,
  KEY `fk_media_channel_master_tpu_id_1` (`master_tpu_id`) USING BTREE,
  KEY `fk_media_channel_tpu_id_2` (`recovery_user_id`) USING BTREE,
  CONSTRAINT `media_channel_ibfk_1` FOREIGN KEY (`media_type_id`) REFERENCES `dict_media_type` (`dict_media_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `media_channel_ibfk_2` FOREIGN KEY (`master_tpu_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `media_channel_ibfk_3` FOREIGN KEY (`add_tpu_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `media_channel_ibfk_4` FOREIGN KEY (`recovery_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for mini_article
-- ----------------------------
DROP TABLE IF EXISTS `mini_article`;
CREATE TABLE `mini_article` (
  `article_id` bigint NOT NULL COMMENT '图文ID',
  `pid` int NOT NULL DEFAULT '0' COMMENT '一级分类 0：图文分类',
  `product_id` int NOT NULL DEFAULT '0' COMMENT '二级分类',
  `article_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图文标题',
  `cover_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图文封面url',
  `jump_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '跳转url',
  `is_hot` tinyint NOT NULL DEFAULT '0' COMMENT '是否 热门 1：是 0：否',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '介绍',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_croatian_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `write_id` bigint NOT NULL DEFAULT '0' COMMENT '白皮书ID',
  `sort` int NOT NULL DEFAULT '999999' COMMENT '排序',
  PRIMARY KEY (`article_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='图文组件内容表';

-- ----------------------------
-- Table structure for mini_collect
-- ----------------------------
DROP TABLE IF EXISTS `mini_collect`;
CREATE TABLE `mini_collect` (
  `collect_id` bigint NOT NULL COMMENT '收藏ID',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户ID',
  `collect_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收藏类型 article：文章 write: 白皮书',
  `rel_id` bigint NOT NULL COMMENT '关联ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收藏标题',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`collect_id`) USING BTREE,
  KEY `client_id` (`client_id`) USING BTREE,
  CONSTRAINT `mini_collect_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='小程序我的收藏表';

-- ----------------------------
-- Table structure for mini_feedback
-- ----------------------------
DROP TABLE IF EXISTS `mini_feedback`;
CREATE TABLE `mini_feedback` (
  `feedback_id` bigint NOT NULL COMMENT '功能反馈ID',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户ID',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '意见和建议内容',
  `img_url1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片1',
  `img_url2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片2',
  `img_url3` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片3',
  `phone_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系方式',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `content_type` tinyint NOT NULL DEFAULT '1' COMMENT '类型 1：意见反馈 2：投诉',
  `work_id` int NOT NULL DEFAULT '0' COMMENT '关联work平台id',
  PRIMARY KEY (`feedback_id`) USING BTREE,
  KEY `client_id` (`client_id`) USING BTREE,
  CONSTRAINT `mini_feedback_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='小程序功能反馈表';

-- ----------------------------
-- Table structure for mini_gift
-- ----------------------------
DROP TABLE IF EXISTS `mini_gift`;
CREATE TABLE `mini_gift` (
  `gift_id` bigint NOT NULL COMMENT '礼品ID',
  `gift_type` int NOT NULL DEFAULT '1' COMMENT '礼品分类 1：默认分类 2：新人礼包 3心意好礼 4：办公优选 5：生活必须',
  `gift_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '礼品名称',
  `cover_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '礼品封面',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '礼品介绍',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `need_integral` int NOT NULL DEFAULT '1000' COMMENT '需要积分',
  `sort` int NOT NULL DEFAULT '999999' COMMENT '排序',
  PRIMARY KEY (`gift_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `mini_gift_ibfk_1` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='小程序礼品表';

-- ----------------------------
-- Table structure for mini_message
-- ----------------------------
DROP TABLE IF EXISTS `mini_message`;
CREATE TABLE `mini_message` (
  `message_id` bigint NOT NULL COMMENT '消息ID',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户ID',
  `message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '消息',
  `is_read` tinyint NOT NULL DEFAULT '0' COMMENT '是否已读 0：未读 1：已读',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除 0：未删除 1：已删除',
  PRIMARY KEY (`message_id`) USING BTREE,
  KEY `client_id` (`client_id`) USING BTREE,
  CONSTRAINT `mini_message_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='小程序 消息表';

-- ----------------------------
-- Table structure for mini_reservation
-- ----------------------------
DROP TABLE IF EXISTS `mini_reservation`;
CREATE TABLE `mini_reservation` (
  `reservation_id` bigint NOT NULL COMMENT '产品预约ID',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
  `company` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '公司名称',
  `phone_number` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `product_id` int DEFAULT NULL COMMENT '产品ID',
  `reservation_time` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '预约时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '1：待确认 2：已确认 3：已结束 4： 终止',
  `presenter` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '演示人',
  `meeting_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会议号',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `back_status` tinyint NOT NULL DEFAULT '0' COMMENT '后台状态 0：未分配 1：未联系 2：已分配 3：已联系',
  `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '负责人ID',
  `demand` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '需求',
  `product_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '产品类型',
  `work_id` int NOT NULL DEFAULT '0' COMMENT '关联work平台id',
  `demo_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '演示时间',
  `work_pj_id` int NOT NULL DEFAULT '0' COMMENT 'work 平台评价ID',
  `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '版本',
  `remote` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '演示平台 腾讯会议=1;钉钉会议=2;QQ演示=3;向日葵=4;其他=5',
  PRIMARY KEY (`reservation_id`) USING BTREE,
  KEY `client_id` (`client_id`) USING BTREE,
  KEY `product_id` (`product_id`) USING BTREE,
  CONSTRAINT `mini_reservation_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `mini_reservation_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `product` (`product_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='小程序预约表';

-- ----------------------------
-- Table structure for mini_setting
-- ----------------------------
DROP TABLE IF EXISTS `mini_setting`;
CREATE TABLE `mini_setting` (
  `set_id` int NOT NULL AUTO_INCREMENT,
  `appid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '小程序ID',
  `mini_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '小程序名称',
  `mini_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '小程序码',
  `trial_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '体验码',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '配置数据',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '小程序路径',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0：未删除 1：已删除',
  PRIMARY KEY (`set_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for mini_solution
-- ----------------------------
DROP TABLE IF EXISTS `mini_solution`;
CREATE TABLE `mini_solution` (
  `solution_id` int NOT NULL AUTO_INCREMENT COMMENT '解决方案ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '解决方案标题',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'icon',
  `bg_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '背景图',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除  1：已删除',
  PRIMARY KEY (`solution_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='小程序解决方案表';

-- ----------------------------
-- Table structure for mini_solution_detail
-- ----------------------------
DROP TABLE IF EXISTS `mini_solution_detail`;
CREATE TABLE `mini_solution_detail` (
  `solution_detail_id` bigint NOT NULL AUTO_INCREMENT COMMENT '解决方案内容ID',
  `solution_id` int NOT NULL COMMENT '解决方案ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'icon',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '内容',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1:已删除',
  PRIMARY KEY (`solution_detail_id`) USING BTREE,
  KEY `solution_id` (`solution_id`) USING BTREE,
  CONSTRAINT `mini_solution_detail_ibfk_1` FOREIGN KEY (`solution_id`) REFERENCES `mini_solution` (`solution_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='小程序解决方案内容表';

-- ----------------------------
-- Table structure for mini_task
-- ----------------------------
DROP TABLE IF EXISTS `mini_task`;
CREATE TABLE `mini_task` (
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `task_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名字',
  `start_time` datetime DEFAULT NULL COMMENT '任务开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '任务结束时间',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '状态 0 默认；1 启动；2 停止； 3 终止',
  `client_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关联客户类型；多个 ‘,’分隔',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '任务内容',
  `award` int NOT NULL DEFAULT '0' COMMENT '奖励金额',
  `souvenir` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '礼品奖励， '',''多个礼品ID',
  `priority` int DEFAULT '10' COMMENT '优先级；数值越小，优先级越高',
  `task_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '类型；0 常规兑换；1 新客有礼；2合伙人',
  `user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `get_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '2' COMMENT '任务下发方式 1被动领取； 2主动领取',
  `landpage_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联落地页id',
  `delete_flag` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '是否删除 0 否； 1删除',
  `show_flag` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '是否主动弹出 0 否；1是',
  `close_flag` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '是否可关闭 0 否；1是',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `index_task` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '是否首页任务',
  `task_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '任务描述',
  `repeat` int DEFAULT '1' COMMENT '可执行次数',
  `ap_id` int DEFAULT NULL COMMENT '登录频率id',
  `type` tinyint DEFAULT '2' COMMENT '人员任务类型 1：客户任务 2：合伙人任务',
  PRIMARY KEY (`task_id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE,
  KEY `rel_landapage` (`landpage_id`) USING BTREE,
  KEY `rel_dict_type` (`task_type`) USING BTREE,
  KEY `rel_login_setting` (`ap_id`) USING BTREE,
  CONSTRAINT `mini_task_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `mini_task_ibfk_2` FOREIGN KEY (`task_type`) REFERENCES `dict_task_type` (`type_code`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `mini_task_ibfk_3` FOREIGN KEY (`landpage_id`) REFERENCES `landpage` (`landpage_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `mini_task_ibfk_4` FOREIGN KEY (`ap_id`) REFERENCES `dict_login_setting` (`login_setting_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='活动任务表';

-- ----------------------------
-- Table structure for mini_version
-- ----------------------------
DROP TABLE IF EXISTS `mini_version`;
CREATE TABLE `mini_version` (
  `version_id` int NOT NULL AUTO_INCREMENT COMMENT '版本号',
  `app_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '小程序id',
  `version_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '版本号',
  `switch_flag` tinyint NOT NULL DEFAULT '0' COMMENT '开关状态 1：开启 0：关闭，开启后 针对小程序发布审核使用',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`version_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='小程序版本记录表';

-- ----------------------------
-- Table structure for mini_video
-- ----------------------------
DROP TABLE IF EXISTS `mini_video`;
CREATE TABLE `mini_video` (
  `video_id` bigint NOT NULL COMMENT '视频ID',
  `channels_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '视频号ID',
  `video_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '视频名称',
  `cover_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '视频封面',
  `jump_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '跳转地址',
  `is_hot` tinyint NOT NULL DEFAULT '0' COMMENT '是否是热门 1：是 0：否',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `pid` int NOT NULL DEFAULT '0' COMMENT '一级分类 0：图文分类 -1:优秀案例 -2 资质 -3：证书',
  `product_id` int NOT NULL DEFAULT '0' COMMENT '二级分类',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '介绍',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `write_id` bigint NOT NULL DEFAULT '0' COMMENT '关联白皮书ID',
  `sort` int NOT NULL DEFAULT '999999' COMMENT '排序',
  PRIMARY KEY (`video_id`) USING BTREE,
  KEY `channels_id` (`channels_id`) USING BTREE,
  CONSTRAINT `mini_video_ibfk_1` FOREIGN KEY (`channels_id`) REFERENCES `dict_wechat_channels` (`channels_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='视频组件内容表';

-- ----------------------------
-- Table structure for mini_write
-- ----------------------------
DROP TABLE IF EXISTS `mini_write`;
CREATE TABLE `mini_write` (
  `write_id` bigint NOT NULL COMMENT '白皮书ID',
  `write_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '白皮书名称',
  `cover_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '白皮书封面',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0：未删除 1：已删除',
  `pdf_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'Pdf url',
  `sort` int NOT NULL DEFAULT '9999' COMMENT '排序',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
  PRIMARY KEY (`write_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='小程序白皮书表';

-- ----------------------------
-- Table structure for mini_write_detail
-- ----------------------------
DROP TABLE IF EXISTS `mini_write_detail`;
CREATE TABLE `mini_write_detail` (
  `write_detail_id` bigint NOT NULL COMMENT '白皮书详情ID',
  `write_id` bigint NOT NULL DEFAULT '0' COMMENT '白皮书ID',
  `write_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '白皮书url',
  `sort` int NOT NULL DEFAULT '999999' COMMENT '排序',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1:已删除',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`write_detail_id`) USING BTREE,
  KEY `write_id` (`write_id`) USING BTREE,
  CONSTRAINT `mini_write_detail_ibfk_1` FOREIGN KEY (`write_id`) REFERENCES `mini_write` (`write_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='白皮书详情表';

-- ----------------------------
-- Table structure for mission_monitor
-- ----------------------------
DROP TABLE IF EXISTS `mission_monitor`;
CREATE TABLE `mission_monitor` (
  `fm_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '裂变任务id',
  `monitor_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '监控链接url',
  PRIMARY KEY (`fm_id`) USING BTREE,
  CONSTRAINT `mission_monitor_ibfk_1` FOREIGN KEY (`fm_id`) REFERENCES `fission_mission` (`fm_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='策划任务监控';

-- ----------------------------
-- Table structure for operation_logs
-- ----------------------------
DROP TABLE IF EXISTS `operation_logs`;
CREATE TABLE `operation_logs` (
  `operation_logs_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作日志id',
  `funciton_view_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '功能页面id',
  `function_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '具体功能内容',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '触点用户id',
  `operation_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '操作内容',
  `operation_time` datetime DEFAULT NULL COMMENT '操作时间',
  PRIMARY KEY (`operation_logs_id`) USING BTREE,
  KEY `fk_operation_logs_function_view_1` (`funciton_view_id`) USING BTREE,
  KEY `fk_operation_logs_touchpoint_user_1` (`tp_user_id`) USING BTREE,
  CONSTRAINT `operation_logs_ibfk_1` FOREIGN KEY (`funciton_view_id`) REFERENCES `function_view` (`function_view_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `operation_logs_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='操作日志表';

-- ----------------------------
-- Table structure for org_seat_time
-- ----------------------------
DROP TABLE IF EXISTS `org_seat_time`;
CREATE TABLE `org_seat_time` (
  `ost_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `org_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组织id',
  `seat_num` int DEFAULT NULL COMMENT '购买席位数',
  `seat_bgn_date` date DEFAULT NULL COMMENT '席位开始时间',
  `seat_end_date` date DEFAULT NULL COMMENT '席位结束时间',
  `order_time` datetime DEFAULT NULL COMMENT '订单时间',
  `buy_price` decimal(10,2) DEFAULT NULL COMMENT '实际付款金额：单位-元',
  `valid_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '0：失效；1：有效',
  PRIMARY KEY (`ost_id`) USING BTREE,
  KEY `fk_org_seat_time_organization_1` (`org_id`) USING BTREE,
  CONSTRAINT `org_seat_time_ibfk_1` FOREIGN KEY (`org_id`) REFERENCES `organization` (`org_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for organization
-- ----------------------------
DROP TABLE IF EXISTS `organization`;
CREATE TABLE `organization` (
  `org_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'id',
  `dict_org_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `sale_version_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '售卖版本id',
  `sale_seat_num` int DEFAULT '0' COMMENT '售卖席位数目',
  `org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组织名称',
  `sale_bgn_date` date DEFAULT NULL COMMENT '开始时间;当前席位的开始时间；',
  `sale_end_date` date DEFAULT NULL COMMENT '结束时间；当前席位的结束时间；',
  `logo_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组织logo',
  `slogan` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组织slogan',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组织地址',
  `web_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '官网地址',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组织公开邮箱',
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组织公开电话',
  `ww_qrcode_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组织企微通用二维码',
  `father_org_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '父组织id，适合于集团企业',
  `son_depart_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '子企业对应父整体企业的部门id',
  `industry_oriented` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '面向行业',
  `phone_400` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '400热线',
  `service_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '服务监督',
  `org_name_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组织英文名',
  `phone_400_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '400热线备注',
  `is_sp_client` tinyint(1) DEFAULT '0' COMMENT '客户信息是否需要审核 1：需要 0：不需要',
  `ww_corp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企微组织id',
  PRIMARY KEY (`org_id`) USING BTREE,
  KEY `fk_organization_dict_org_1` (`dict_org_type_id`) USING BTREE,
  KEY `fk_organization_sale_version_1` (`sale_version_id`) USING BTREE,
  CONSTRAINT `organization_ibfk_1` FOREIGN KEY (`dict_org_type_id`) REFERENCES `dict_org_type` (`dict_org_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `organization_ibfk_2` FOREIGN KEY (`sale_version_id`) REFERENCES `sale_version` (`sale_version_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='组织表（企业、团体、个人）';

-- ----------------------------
-- Table structure for product
-- ----------------------------
DROP TABLE IF EXISTS `product`;
CREATE TABLE `product` (
  `product_id` int NOT NULL AUTO_INCREMENT COMMENT '产品分类ID',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '产品名称',
  `pid` int NOT NULL DEFAULT '0' COMMENT '父级ID',
  `prefix_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类前缀',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '内容介绍',
  `detail_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详情类型 image：图片 video ：视频',
  `detail_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详情链接',
  `detail_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详情介绍',
  `qw_qrcode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '企微二维码',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类icon',
  `bg_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '背景图',
  `write_id` bigint NOT NULL DEFAULT '0' COMMENT '白皮书ID',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '添加人',
  `sort` int NOT NULL DEFAULT '9999' COMMENT '排序 9999',
  PRIMARY KEY (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=74 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='产品分类字典表';

-- ----------------------------
-- Table structure for product_info
-- ----------------------------
DROP TABLE IF EXISTS `product_info`;
CREATE TABLE `product_info` (
  `product_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'uuid',
  `product_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '1:erp;2:百雁;3:喜鹊;4:mes;5:蜂王台',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品名称',
  `product_account` int DEFAULT NULL COMMENT '产品账户数',
  `product_price` decimal(10,2) DEFAULT NULL COMMENT '产品价格元',
  `product_industry` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品行业,逗号隔开',
  `product_use` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品用途,逗号隔开',
  `valid_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '是否有效；1有效；0无效',
  `product_desc` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品描述',
  `key_words` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品相关关键词',
  `display_words` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '显示产品相关关键词',
  `display_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '显示描述',
  `write_id` bigint DEFAULT '1' COMMENT '文章id',
  PRIMARY KEY (`product_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for publish_pos
-- ----------------------------
DROP TABLE IF EXISTS `publish_pos`;
CREATE TABLE `publish_pos` (
  `publish_pos_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `pos_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '位置名称，一般是企业微信群或钉钉群',
  `webhooks_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'webhooks地址',
  `pos_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地址类型；0:企业微信；1:钉钉',
  `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `valid_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '是否启动：0:不启用；1:启用中',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志位；0:未删除；1:已删除',
  `pos_type_theme` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'label的颜色模式',
  `pos_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '位置编码',
  PRIMARY KEY (`publish_pos_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='发布位置-webhooks配置表';

-- ----------------------------
-- Table structure for qb_answer
-- ----------------------------
DROP TABLE IF EXISTS `qb_answer`;
CREATE TABLE `qb_answer` (
  `ans_id` int NOT NULL AUTO_INCREMENT COMMENT '答案id',
  `qb_id` int DEFAULT NULL COMMENT '试题id',
  `ans_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '答案',
  `score` float DEFAULT '0' COMMENT '分数值',
  `sort` int DEFAULT '9999' COMMENT '排序值',
  `tp_user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  `delete_flag` int DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`ans_id`) USING BTREE,
  KEY `qb_answer_ibfk_1` (`qb_id`) USING BTREE,
  KEY `qb_answer_ibfk_2` (`tp_user_id`) USING BTREE,
  CONSTRAINT `qb_answer_ibfk_1` FOREIGN KEY (`qb_id`) REFERENCES `question_bank` (`question_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `qb_answer_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=221 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='题库对应答案和分值';

-- ----------------------------
-- Table structure for question
-- ----------------------------
DROP TABLE IF EXISTS `question`;
CREATE TABLE `question` (
  `question_id` bigint NOT NULL COMMENT '问卷id',
  `question_type_id` int DEFAULT NULL COMMENT '问题分类ID',
  `question_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '问题名称',
  `question_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '问题类型 single：单选 multiple：多选 text:填空 number:数字 ',
  `placeholder` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提示',
  `max_num` int NOT NULL DEFAULT '-1' COMMENT '多选题是 允许选择最大数量 -1 为不限',
  `is_rel_option` tinyint NOT NULL DEFAULT '0' COMMENT '是否关联答案显示 1需要 2 不需要',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1：启用 2：禁用',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0未删除 1已删除',
  `file_flag` tinyint NOT NULL DEFAULT '0' COMMENT '上传附加标识 1：允许 0：不允许',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '员工ID',
  `key_words` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关键词',
  `category_id` tinyint NOT NULL DEFAULT '1' COMMENT '分类ID:1:行业特征 2：痛点及方案',
  PRIMARY KEY (`question_id`) USING BTREE,
  KEY `question_type_id` (`question_type_id`) USING BTREE,
  CONSTRAINT `question_ibfk_1` FOREIGN KEY (`question_type_id`) REFERENCES `dict_question_type` (`question_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='问卷问题表';

-- ----------------------------
-- Table structure for question_bank
-- ----------------------------
DROP TABLE IF EXISTS `question_bank`;
CREATE TABLE `question_bank` (
  `question_id` int NOT NULL AUTO_INCREMENT COMMENT '题库id',
  `name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '题目',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '题型 radio,checkbox,textbox',
  `criterion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'all' COMMENT '多选 评分准则 all、single',
  `tips` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '提示',
  `status` int DEFAULT '1' COMMENT '启用状态 1启用；0禁用',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `delete_flag` int DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `key_words` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关键字',
  `score` float DEFAULT '0' COMMENT '分数值',
  `tp_user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`question_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `question_bank_ibfk_1` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=75 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='试卷题库';

-- ----------------------------
-- Table structure for question_category
-- ----------------------------
DROP TABLE IF EXISTS `question_category`;
CREATE TABLE `question_category` (
  `qc_id` int NOT NULL AUTO_INCREMENT COMMENT '题类id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '题类名称',
  `delete_flag` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除状态',
  `user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `up_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`qc_id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE,
  CONSTRAINT `question_category_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='试题科类';

-- ----------------------------
-- Table structure for question_option
-- ----------------------------
DROP TABLE IF EXISTS `question_option`;
CREATE TABLE `question_option` (
  `question_option_id` bigint NOT NULL COMMENT '问题答案id',
  `question_id` bigint NOT NULL DEFAULT '0' COMMENT '问题id',
  `question_option_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '答案名称',
  `rel_question_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '答案关联的问题id，非空',
  `is_other` tinyint NOT NULL DEFAULT '0' COMMENT '是否有其他自定义 1有 0没有',
  `other_required` tinyint NOT NULL DEFAULT '0' COMMENT '自定义选项是否必填 1是 0 否',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1启用 2禁用',
  `disable_option` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '禁用选项 ID',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `max_num` int NOT NULL DEFAULT '-1' COMMENT '最大值',
  `min_num` int NOT NULL DEFAULT '-1' COMMENT '最小值',
  `placehoder` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'placehoder',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0: 未删除 1已删除',
  PRIMARY KEY (`question_option_id`) USING BTREE,
  KEY `question_id` (`question_id`) USING BTREE,
  CONSTRAINT `question_option_ibfk_1` FOREIGN KEY (`question_id`) REFERENCES `question` (`question_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='问题选项表';

-- ----------------------------
-- Table structure for question_point_plan
-- ----------------------------
DROP TABLE IF EXISTS `question_point_plan`;
CREATE TABLE `question_point_plan` (
  `qpp_id` bigint NOT NULL COMMENT '痛点及方案ID',
  `question_type_id` int DEFAULT NULL COMMENT '一级分类ID',
  `sore_point` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '痛点',
  `describe` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务场景表述',
  `function_point` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'ERP解决功能点',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '员工ID',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `answer_type` tinyint DEFAULT NULL COMMENT '回答类型 1：个人 2：标准',
  PRIMARY KEY (`qpp_id`) USING BTREE,
  KEY `question_type_id` (`question_type_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `question_point_plan_ibfk_1` FOREIGN KEY (`question_type_id`) REFERENCES `dict_question_type` (`question_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `question_point_plan_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='痛点及方案表';

-- ----------------------------
-- Table structure for question_summary
-- ----------------------------
DROP TABLE IF EXISTS `question_summary`;
CREATE TABLE `question_summary` (
  `question_summary_id` bigint NOT NULL COMMENT '总结ID',
  `summary_type` tinyint DEFAULT NULL COMMENT '总结类型1：gpt 2：标准',
  `question_id` bigint DEFAULT NULL COMMENT '问题ID',
  `question_type_id` int DEFAULT NULL COMMENT '分类ID',
  `content` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '内容',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '1：已删除 0：未删除',
  PRIMARY KEY (`question_summary_id`) USING BTREE,
  KEY `question_id` (`question_id`) USING BTREE,
  KEY `question_type_id` (`question_type_id`) USING BTREE,
  CONSTRAINT `question_summary_ibfk_1` FOREIGN KEY (`question_id`) REFERENCES `question` (`question_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `question_summary_ibfk_2` FOREIGN KEY (`question_type_id`) REFERENCES `dict_question_type` (`question_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='问题总结表';

-- ----------------------------
-- Table structure for rel_aigc_app
-- ----------------------------
DROP TABLE IF EXISTS `rel_aigc_app`;
CREATE TABLE `rel_aigc_app` (
  `rel_id` bigint NOT NULL COMMENT '关联ID',
  `app_id_entry` bigint NOT NULL COMMENT '入口ID',
  `app_id` bigint NOT NULL COMMENT '应用ID',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`rel_id`),
  KEY `rel_aigc_app_aigc_app_FK` (`app_id`),
  KEY `rel_aigc_app_aigc_app_FK_1` (`app_id_entry`),
  CONSTRAINT `rel_aigc_app_aigc_app_FK` FOREIGN KEY (`app_id`) REFERENCES `aigc_app` (`app_id`),
  CONSTRAINT `rel_aigc_app_aigc_app_FK_1` FOREIGN KEY (`app_id_entry`) REFERENCES `aigc_app` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Aigc 应用层级关系表';

-- ----------------------------
-- Table structure for rel_app_knowledge
-- ----------------------------
DROP TABLE IF EXISTS `rel_app_knowledge`;
CREATE TABLE `rel_app_knowledge` (
  `rel_id` bigint NOT NULL COMMENT '关联ID',
  `app_id` bigint DEFAULT NULL COMMENT '应用ID',
  `knowledge_id` bigint DEFAULT NULL COMMENT '知识库ID',
  `delete_flag` tinyint(1) DEFAULT NULL COMMENT '0:未删除 1：已删除',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`rel_id`) USING BTREE,
  KEY `app_id` (`app_id`) USING BTREE,
  KEY `knowledge_id` (`knowledge_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `rel_app_knowledge_ibfk_1` FOREIGN KEY (`app_id`) REFERENCES `aigc_app` (`app_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_app_knowledge_ibfk_2` FOREIGN KEY (`knowledge_id`) REFERENCES `aigc_knowledge` (`knowledge_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_app_knowledge_ibfk_3` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Aigc 应用管理模型表';

-- ----------------------------
-- Table structure for rel_app_qa
-- ----------------------------
DROP TABLE IF EXISTS `rel_app_qa`;
CREATE TABLE `rel_app_qa` (
  `rel_id` bigint NOT NULL COMMENT '关联ID',
  `app_id` bigint NOT NULL COMMENT 'app ID',
  `qa_id` bigint NOT NULL COMMENT 'qa ID',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`rel_id`),
  KEY `rel_aigc_app_aigc_app_FK` (`qa_id`) USING BTREE,
  KEY `rel_aigc_app_aigc_app_FK_1` (`app_id`) USING BTREE,
  CONSTRAINT `rel_app_qa_aigc_app_FK` FOREIGN KEY (`app_id`) REFERENCES `aigc_app` (`app_id`),
  CONSTRAINT `rel_app_qa_aigc_qa_FK` FOREIGN KEY (`qa_id`) REFERENCES `aigc_qa` (`qa_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Aigc 应用层级关系表';

-- ----------------------------
-- Table structure for rel_auth_scheme_depart
-- ----------------------------
DROP TABLE IF EXISTS `rel_auth_scheme_depart`;
CREATE TABLE `rel_auth_scheme_depart` (
  `rel_as_depart_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `scheme_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '方案id',
  `depart_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门id',
  PRIMARY KEY (`rel_as_depart_id`) USING BTREE,
  KEY `fk_rel_auth_scheme_depart_content_auth_scheme_1` (`scheme_id`) USING BTREE,
  KEY `fk_rel_auth_scheme_depart_department_1` (`depart_id`) USING BTREE,
  CONSTRAINT `rel_auth_scheme_depart_ibfk_1` FOREIGN KEY (`scheme_id`) REFERENCES `content_auth_scheme` (`auth_scheme_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_auth_scheme_depart_ibfk_2` FOREIGN KEY (`depart_id`) REFERENCES `department` (`department_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='素材权限方案';

-- ----------------------------
-- Table structure for rel_auth_scheme_role
-- ----------------------------
DROP TABLE IF EXISTS `rel_auth_scheme_role`;
CREATE TABLE `rel_auth_scheme_role` (
  `rel_as_role_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `role_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色id',
  `scheme_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '方案id',
  PRIMARY KEY (`rel_as_role_id`) USING BTREE,
  KEY `fk_rel_auth_scheme_role_content_auth_scheme_1` (`scheme_id`) USING BTREE,
  KEY `fk_rel_auth_scheme_role_dict_role_1` (`role_id`) USING BTREE,
  CONSTRAINT `rel_auth_scheme_role_ibfk_1` FOREIGN KEY (`scheme_id`) REFERENCES `content_auth_scheme` (`auth_scheme_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_auth_scheme_role_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `dict_tp_user_role` (`dict_tp_user_role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='素材权限方案角色关联表';

-- ----------------------------
-- Table structure for rel_auth_scheme_user
-- ----------------------------
DROP TABLE IF EXISTS `rel_auth_scheme_user`;
CREATE TABLE `rel_auth_scheme_user` (
  `rel_as_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'user id',
  `scheme_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '方案id',
  PRIMARY KEY (`rel_as_user_id`) USING BTREE,
  KEY `fk_rel_auth_scheme_user_content_auth_scheme_1` (`scheme_id`) USING BTREE,
  KEY `fk_rel_auth_scheme_user_touchpoint_user_1` (`tp_user_id`) USING BTREE,
  CONSTRAINT `rel_auth_scheme_user_ibfk_1` FOREIGN KEY (`scheme_id`) REFERENCES `content_auth_scheme` (`auth_scheme_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_auth_scheme_user_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='素材权限方案用户关联表';

-- ----------------------------
-- Table structure for rel_card_tpu
-- ----------------------------
DROP TABLE IF EXISTS `rel_card_tpu`;
CREATE TABLE `rel_card_tpu` (
  `rel_card_tpu_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `ect_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '电子名片模板id',
  `tpu_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '触点id',
  `rel_time` datetime DEFAULT NULL COMMENT '启用时间',
  `client_send_count` int DEFAULT NULL COMMENT '发送给客户次数',
  `client_open_count` int DEFAULT NULL COMMENT '客户打开次数',
  `client_close_count` int DEFAULT NULL COMMENT '客户关闭次数',
  `client_avg_time` int DEFAULT NULL COMMENT '客户平均停留时间',
  `client_trans_count` int DEFAULT NULL COMMENT '客户转发次数',
  `uv_open` int DEFAULT '0' COMMENT '打开客户数',
  `uv_send` int DEFAULT '0' COMMENT '发送客户数',
  `uv_close` int DEFAULT '0' COMMENT '关闭客户数',
  `uv_trans` int DEFAULT '0' COMMENT '转发客户数',
  PRIMARY KEY (`rel_card_tpu_id`) USING BTREE,
  KEY `fk_rel_card_tpu_electric_card_template_1` (`ect_id`) USING BTREE,
  KEY `fk_rel_card_tpu_touchpoint_user_1` (`tpu_id`) USING BTREE,
  CONSTRAINT `rel_card_tpu_ibfk_1` FOREIGN KEY (`ect_id`) REFERENCES `electric_card_template` (`ect_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_card_tpu_ibfk_2` FOREIGN KEY (`tpu_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for rel_channel_tags
-- ----------------------------
DROP TABLE IF EXISTS `rel_channel_tags`;
CREATE TABLE `rel_channel_tags` (
  `channel_tags_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `channel_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '渠道id',
  `tags_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签id',
  `rel_time` datetime DEFAULT NULL COMMENT '关联时间',
  PRIMARY KEY (`channel_tags_id`) USING BTREE,
  KEY `fk_rel_channel_tags_config_channel_1` (`channel_id`) USING BTREE,
  KEY `fk_rel_channel_tags_tags_1` (`tags_id`) USING BTREE,
  CONSTRAINT `rel_channel_tags_ibfk_1` FOREIGN KEY (`channel_id`) REFERENCES `config_channel` (`channel_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_channel_tags_ibfk_2` FOREIGN KEY (`tags_id`) REFERENCES `tags` (`tags_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='渠道标签关联表';

-- ----------------------------
-- Table structure for rel_channel_work_tpu
-- ----------------------------
DROP TABLE IF EXISTS `rel_channel_work_tpu`;
CREATE TABLE `rel_channel_work_tpu` (
  `rel_channel_work_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参与人id',
  `media_channel_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'media_channel id',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志位',
  PRIMARY KEY (`rel_channel_work_id`) USING BTREE,
  KEY `FK_REL_CHANNEL_1` (`media_channel_id`) USING BTREE,
  KEY `FK_REL_CHANNEL_2` (`tp_user_id`) USING BTREE,
  CONSTRAINT `FK_REL_CHANNEL_1` FOREIGN KEY (`media_channel_id`) REFERENCES `media_channel` (`media_channel_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_REL_CHANNEL_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='渠道参与人关联表';

-- ----------------------------
-- Table structure for rel_class_exam
-- ----------------------------
DROP TABLE IF EXISTS `rel_class_exam`;
CREATE TABLE `rel_class_exam` (
  `rel_id` int NOT NULL AUTO_INCREMENT COMMENT '关联id',
  `class_id` bigint DEFAULT NULL COMMENT '班级id',
  `exam_id` int DEFAULT NULL COMMENT '试卷id',
  `status` int DEFAULT '0' COMMENT '发卷状态 0未下发；1答题中；2已结束',
  `tp_user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`rel_id`) USING BTREE,
  KEY `rel_class_exam_ibfk_1` (`class_id`) USING BTREE,
  KEY `rel_class_exam_ibfk_2` (`exam_id`) USING BTREE,
  KEY `rel_class_exam_ibfk_3` (`tp_user_id`) USING BTREE,
  CONSTRAINT `rel_class_exam_ibfk_1` FOREIGN KEY (`class_id`) REFERENCES `class_info` (`class_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_class_exam_ibfk_2` FOREIGN KEY (`exam_id`) REFERENCES `exam_page` (`exam_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_class_exam_ibfk_3` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=114 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='班级配置试卷';

-- ----------------------------
-- Table structure for rel_class_tags
-- ----------------------------
DROP TABLE IF EXISTS `rel_class_tags`;
CREATE TABLE `rel_class_tags` (
  `rel_class_tag_id` bigint NOT NULL AUTO_INCREMENT,
  `class_id` bigint NOT NULL COMMENT '合伙人ID',
  `tags_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签ID',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`rel_class_tag_id`) USING BTREE,
  KEY `class_id` (`class_id`) USING BTREE,
  KEY `tags_id` (`tags_id`) USING BTREE,
  CONSTRAINT `rel_class_tags_ibfk_1` FOREIGN KEY (`class_id`) REFERENCES `class_info` (`class_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_class_tags_ibfk_2` FOREIGN KEY (`tags_id`) REFERENCES `tags` (`tags_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4860064967427100674 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='班级标签表';

-- ----------------------------
-- Table structure for rel_client_exam
-- ----------------------------
DROP TABLE IF EXISTS `rel_client_exam`;
CREATE TABLE `rel_client_exam` (
  `rel_exam_id` int NOT NULL AUTO_INCREMENT COMMENT '答卷id',
  `client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '学员id',
  `exam_id` int DEFAULT NULL COMMENT '试卷id',
  `status` int DEFAULT '0' COMMENT '答卷状态 0未开始；1答题中；2待阅卷；3已评分',
  `tp_user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  `delete_flag` int DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `score` int DEFAULT '0' COMMENT '总分',
  `auto_check` int DEFAULT '0' COMMENT '机器阅卷 0否；1是',
  PRIMARY KEY (`rel_exam_id`) USING BTREE,
  KEY `rel_client_exam_ibfk_1` (`tp_user_id`) USING BTREE,
  KEY `rel_client_exam_ibfk_2` (`exam_id`) USING BTREE,
  KEY `rel_client_exam_ibfk_3` (`client_id`) USING BTREE,
  CONSTRAINT `rel_client_exam_ibfk_1` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_client_exam_ibfk_2` FOREIGN KEY (`exam_id`) REFERENCES `exam_page` (`exam_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_client_exam_ibfk_3` FOREIGN KEY (`client_id`) REFERENCES `client_info` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='考生答卷';

-- ----------------------------
-- Table structure for rel_client_result
-- ----------------------------
DROP TABLE IF EXISTS `rel_client_result`;
CREATE TABLE `rel_client_result` (
  `ret_id` int NOT NULL AUTO_INCREMENT COMMENT '答题id',
  `client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '学员id',
  `rel_exam_id` int DEFAULT NULL COMMENT '答卷id',
  `qb_id` int DEFAULT NULL COMMENT '考题id',
  `ans_id` int DEFAULT NULL COMMENT '答案id',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '文本答案',
  `score` float DEFAULT '0' COMMENT '分数',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`ret_id`) USING BTREE,
  KEY `rel_client_result_ibfk_2` (`rel_exam_id`) USING BTREE,
  KEY `rel_client_result_ibfk_3` (`qb_id`) USING BTREE,
  KEY `rel_client_result_ibfk_4` (`ans_id`) USING BTREE,
  KEY `rel_client_result_ibfk_1` (`client_id`) USING BTREE,
  CONSTRAINT `rel_client_result_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client_info` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_client_result_ibfk_2` FOREIGN KEY (`rel_exam_id`) REFERENCES `rel_client_exam` (`rel_exam_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_client_result_ibfk_3` FOREIGN KEY (`qb_id`) REFERENCES `question_bank` (`question_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_client_result_ibfk_4` FOREIGN KEY (`ans_id`) REFERENCES `qb_answer` (`ans_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=375 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='考生答案';

-- ----------------------------
-- Table structure for rel_client_tags
-- ----------------------------
DROP TABLE IF EXISTS `rel_client_tags`;
CREATE TABLE `rel_client_tags` (
  `rel_ct_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户id',
  `ct_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签id',
  `update_time` datetime DEFAULT NULL COMMENT '客户标签生效时间',
  `valid_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '标签是否生效；1：生效；0：失效',
  `tags_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '标签值',
  PRIMARY KEY (`rel_ct_id`) USING BTREE,
  KEY `fk_rel_client_tags_client_1` (`client_id`) USING BTREE,
  KEY `fk_rel_client_tags_tags_11` (`ct_id`) USING BTREE,
  CONSTRAINT `rel_client_tags_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_client_tags_ibfk_2` FOREIGN KEY (`ct_id`) REFERENCES `tags` (`tags_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户标签关联表';

-- ----------------------------
-- Table structure for rel_client_targeting
-- ----------------------------
DROP TABLE IF EXISTS `rel_client_targeting`;
CREATE TABLE `rel_client_targeting` (
  `client_targeting_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户定向id',
  `targeting_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '定向类型id',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户id',
  `target_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '定向值',
  PRIMARY KEY (`client_targeting_id`) USING BTREE,
  KEY `fk_dict_client_targeting_dict_targeting_type_1` (`targeting_type_id`) USING BTREE,
  KEY `fk_rel_client_targeting_client_1` (`client_id`) USING BTREE,
  CONSTRAINT `rel_client_targeting_ibfk_1` FOREIGN KEY (`targeting_type_id`) REFERENCES `dict_targeting_type` (`targeting_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_client_targeting_ibfk_2` FOREIGN KEY (`client_id`) REFERENCES `client` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户定向关联表';

-- ----------------------------
-- Table structure for rel_client_task
-- ----------------------------
DROP TABLE IF EXISTS `rel_client_task`;
CREATE TABLE `rel_client_task` (
  `rel_task_id` bigint NOT NULL,
  `client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户id',
  `task_id` bigint DEFAULT NULL COMMENT '任务id',
  `status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '执行状态 0 默认；1完毕；2执行中',
  `souvenir` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '礼品奖励， '',''多个礼品ID',
  `award` int DEFAULT '0' COMMENT '奖励金额',
  `exchange_id` bigint DEFAULT NULL COMMENT '礼品兑换ID',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '添加时间',
  `delete_flag` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '是否删除 0 否； 1删除',
  `is_update_value` tinyint DEFAULT '0' COMMENT '是否更新了成长值 1：是 0：否',
  `update_value_time` datetime DEFAULT NULL COMMENT '更新成长值时间',
  `repeat` int DEFAULT '1' COMMENT '可执行次数',
  PRIMARY KEY (`rel_task_id`) USING BTREE,
  KEY `rel_exchange` (`exchange_id`) USING BTREE,
  KEY `rel_client` (`client_id`) USING BTREE,
  KEY `rel_task` (`task_id`) USING BTREE,
  CONSTRAINT `rel_client_task_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client_info` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_client_task_ibfk_2` FOREIGN KEY (`exchange_id`) REFERENCES `client_integral_exchange` (`exchange_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_client_task_ibfk_3` FOREIGN KEY (`task_id`) REFERENCES `mini_task` (`task_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户任务详情表';

-- ----------------------------
-- Table structure for rel_client_tpu
-- ----------------------------
DROP TABLE IF EXISTS `rel_client_tpu`;
CREATE TABLE `rel_client_tpu` (
  `rel_client_tpu_id` bigint NOT NULL COMMENT '客户关联员工ID',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户ID',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '员工ID',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`rel_client_tpu_id`) USING BTREE,
  KEY `client_id` (`client_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `rel_client_tpu_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `client` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_client_tpu_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户关联员工表';

-- ----------------------------
-- Table structure for rel_com_property
-- ----------------------------
DROP TABLE IF EXISTS `rel_com_property`;
CREATE TABLE `rel_com_property` (
  `rel_ctp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `com_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件id',
  `property_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '属性id',
  `basic_com_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '属性基础组件编码\r\ninput：输入框\r\nselect：选择框\r\ncolor_select：颜色选择器\r\nfile_update：文件上传框\r\nsize_select：尺寸选择器',
  `default_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '属性基础组件默认值',
  PRIMARY KEY (`rel_ctp_id`) USING BTREE,
  KEY `fk_rel_com_property_com_property_1` (`property_id`) USING BTREE,
  KEY `fk_rel_com_property_landpage_component_1` (`com_id`) USING BTREE,
  CONSTRAINT `rel_com_property_ibfk_1` FOREIGN KEY (`property_id`) REFERENCES `com_property` (`com_property_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_com_property_ibfk_2` FOREIGN KEY (`com_id`) REFERENCES `landpage_component` (`landpage_com_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for rel_content_group
-- ----------------------------
DROP TABLE IF EXISTS `rel_content_group`;
CREATE TABLE `rel_content_group` (
  `rel_content_group_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `group_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材组id',
  `content_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材id',
  `rel_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '关联时间',
  PRIMARY KEY (`rel_content_group_id`) USING BTREE,
  KEY `fk_rel_content_group_content_group_1` (`group_id`) USING BTREE,
  KEY `fk_rel_content_group_content_info_1` (`content_id`) USING BTREE,
  CONSTRAINT `rel_content_group_ibfk_1` FOREIGN KEY (`group_id`) REFERENCES `content_group` (`group_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_content_group_ibfk_2` FOREIGN KEY (`content_id`) REFERENCES `content_info` (`content_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='素材组和素材关联表';

-- ----------------------------
-- Table structure for rel_content_knowledge
-- ----------------------------
DROP TABLE IF EXISTS `rel_content_knowledge`;
CREATE TABLE `rel_content_knowledge` (
  `rel_id` bigint NOT NULL AUTO_INCREMENT,
  `content_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材ID',
  `knowledge_id` bigint DEFAULT NULL COMMENT '知识库ID',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人ID',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1：已删除 0：未删除',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`rel_id`) USING BTREE,
  KEY `content_id` (`content_id`) USING BTREE,
  KEY `knowledge_id` (`knowledge_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `rel_content_knowledge_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `content_info` (`content_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_content_knowledge_ibfk_2` FOREIGN KEY (`knowledge_id`) REFERENCES `aigc_knowledge` (`knowledge_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_content_knowledge_ibfk_3` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4947098318544048130 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='素材管理知识库表';

-- ----------------------------
-- Table structure for rel_content_pos
-- ----------------------------
DROP TABLE IF EXISTS `rel_content_pos`;
CREATE TABLE `rel_content_pos` (
  `rel_content_pos_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `content_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材id',
  `publish_pos_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'webhooks发布位置id',
  PRIMARY KEY (`rel_content_pos_id`) USING BTREE,
  KEY `fk_rel_content_pos_content_info_1` (`content_id`) USING BTREE,
  KEY `fk_rel_content_pos_publish_pos_1` (`publish_pos_id`) USING BTREE,
  CONSTRAINT `rel_content_pos_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `content_info` (`content_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_content_pos_ibfk_2` FOREIGN KEY (`publish_pos_id`) REFERENCES `publish_pos` (`publish_pos_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='素材位置关联表';

-- ----------------------------
-- Table structure for rel_content_tags
-- ----------------------------
DROP TABLE IF EXISTS `rel_content_tags`;
CREATE TABLE `rel_content_tags` (
  `rel_content_tags_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '素材标签关联id',
  `content_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材id',
  `tag_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材标签id',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '添加人id',
  PRIMARY KEY (`rel_content_tags_id`) USING BTREE,
  KEY `fk_rel_content_tags_content_1` (`content_id`) USING BTREE,
  KEY `fk_rel_content_tags_dict_content_tags_1` (`tag_id`) USING BTREE,
  KEY `fk_rel_content_tags_touchpoint_user_1` (`tp_user_id`) USING BTREE,
  CONSTRAINT `rel_content_tags_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `content_info` (`content_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_content_tags_ibfk_2` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`tags_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_content_tags_ibfk_3` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='内容标签关联表';

-- ----------------------------
-- Table structure for rel_content_tpu_collect
-- ----------------------------
DROP TABLE IF EXISTS `rel_content_tpu_collect`;
CREATE TABLE `rel_content_tpu_collect` (
  `rel_content_tpu_collect_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `content_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材id',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户id',
  `collect_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '收藏标志位',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `collect_time` datetime DEFAULT NULL COMMENT '收藏时间',
  PRIMARY KEY (`rel_content_tpu_collect_id`) USING BTREE,
  KEY `fk_rel_content_tpu_collect_content_info_1` (`content_id`) USING BTREE,
  KEY `fk_rel_content_tpu_collect_touchpoint_user_1` (`tp_user_id`) USING BTREE,
  CONSTRAINT `rel_content_tpu_collect_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `content_info` (`content_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_content_tpu_collect_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='素材信息用户收藏关联表';

-- ----------------------------
-- Table structure for rel_course_order_teacher
-- ----------------------------
DROP TABLE IF EXISTS `rel_course_order_teacher`;
CREATE TABLE `rel_course_order_teacher` (
  `rel_id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `order_id` bigint DEFAULT NULL COMMENT '课程订单ID',
  `teacher_id` bigint DEFAULT NULL COMMENT '老师ID',
  `divide_ratio` float(5,2) DEFAULT '0.00' COMMENT '分成比例',
  `divide_amount` decimal(10,2) DEFAULT '0.00' COMMENT '分成金额',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态 1：已发放 0：未发送',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`rel_id`) USING BTREE,
  KEY `order_id` (`order_id`) USING BTREE,
  KEY `teacher_id` (`teacher_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `rel_course_order_teacher_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `course_orders` (`order_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_course_order_teacher_ibfk_2` FOREIGN KEY (`teacher_id`) REFERENCES `course_teacher` (`course_teacher_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_course_order_teacher_ibfk_3` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='课程订单关联教师分成记录表';

-- ----------------------------
-- Table structure for rel_course_teacher
-- ----------------------------
DROP TABLE IF EXISTS `rel_course_teacher`;
CREATE TABLE `rel_course_teacher` (
  `rel_id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `course_id` bigint DEFAULT NULL COMMENT '课程ID',
  `teacher_id` bigint DEFAULT NULL COMMENT '教师ID',
  `divide_ratio` float(5,2) DEFAULT '0.00' COMMENT '分成比例',
  `delete_flag` tinyint DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`rel_id`) USING BTREE,
  KEY `rel_course_teacher_ibfk_1` (`course_id`) USING BTREE,
  KEY `rel_course_teacher_ibfk_2` (`teacher_id`) USING BTREE,
  KEY `rel_course_teacher_ibfk_3` (`tp_user_id`) USING BTREE,
  CONSTRAINT `rel_course_teacher_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `course` (`course_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_course_teacher_ibfk_2` FOREIGN KEY (`teacher_id`) REFERENCES `course_teacher` (`course_teacher_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_course_teacher_ibfk_3` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4872764448546230274 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='教师关联课程表';

-- ----------------------------
-- Table structure for rel_exam_qb
-- ----------------------------
DROP TABLE IF EXISTS `rel_exam_qb`;
CREATE TABLE `rel_exam_qb` (
  `rel_id` int NOT NULL AUTO_INCREMENT COMMENT '关联id',
  `exam_id` int DEFAULT NULL COMMENT '试卷id',
  `qb_id` int DEFAULT NULL COMMENT '试题id',
  `sort` int DEFAULT '0' COMMENT '顺序id',
  `client_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '考生id',
  PRIMARY KEY (`rel_id`) USING BTREE,
  KEY `rel_exam_qb_ibfk_1` (`exam_id`) USING BTREE,
  KEY `rel_exam_qb_ibfk_2` (`qb_id`) USING BTREE,
  KEY `client_id` (`client_id`) USING BTREE,
  CONSTRAINT `rel_exam_qb_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exam_page` (`exam_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_exam_qb_ibfk_2` FOREIGN KEY (`qb_id`) REFERENCES `question_bank` (`question_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_exam_qb_ibfk_3` FOREIGN KEY (`client_id`) REFERENCES `client_info` (`client_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=122658 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='试卷关联试题';

-- ----------------------------
-- Table structure for rel_exam_qc
-- ----------------------------
DROP TABLE IF EXISTS `rel_exam_qc`;
CREATE TABLE `rel_exam_qc` (
  `rel_id` int NOT NULL AUTO_INCREMENT COMMENT '关联id',
  `exam_id` int DEFAULT NULL COMMENT '试卷id',
  `qc_id` int DEFAULT NULL COMMENT '题类id',
  PRIMARY KEY (`rel_id`) USING BTREE,
  KEY `exam_id` (`exam_id`) USING BTREE,
  KEY `qc_id` (`qc_id`) USING BTREE,
  CONSTRAINT `rel_exam_qc_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exam_page` (`exam_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_exam_qc_ibfk_2` FOREIGN KEY (`qc_id`) REFERENCES `question_category` (`qc_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=75 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='试卷组卷规则';

-- ----------------------------
-- Table structure for rel_fission_tags
-- ----------------------------
DROP TABLE IF EXISTS `rel_fission_tags`;
CREATE TABLE `rel_fission_tags` (
  `rel_ft_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联裂变策划标签',
  `fp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '裂变策划id',
  `fission_tag_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签id',
  `rel_time` datetime DEFAULT NULL COMMENT '关联时间',
  PRIMARY KEY (`rel_ft_id`) USING BTREE,
  KEY `fk_rel_fission_tags_fission_planning_1` (`fp_id`) USING BTREE,
  KEY `fk_rel_fission_tags_dict_fission_tags_1` (`fission_tag_id`) USING BTREE,
  CONSTRAINT `rel_fission_tags_ibfk_1` FOREIGN KEY (`fission_tag_id`) REFERENCES `tags` (`tags_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_fission_tags_ibfk_2` FOREIGN KEY (`fp_id`) REFERENCES `fission_planning` (`fission_planning_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='裂变策划计划与标签关联表';

-- ----------------------------
-- Table structure for rel_fm_content
-- ----------------------------
DROP TABLE IF EXISTS `rel_fm_content`;
CREATE TABLE `rel_fm_content` (
  `rel_fm_content_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联内容和任务id',
  `fm_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '策划任务id',
  `content_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '内容素材id',
  PRIMARY KEY (`rel_fm_content_id`) USING BTREE,
  KEY `fk_rel_fm_content_fission_mission_1` (`fm_id`) USING BTREE,
  KEY `fk_rel_fm_content_content_1` (`content_id`) USING BTREE,
  CONSTRAINT `rel_fm_content_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `content_info` (`content_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_fm_content_ibfk_2` FOREIGN KEY (`fm_id`) REFERENCES `fission_mission` (`fm_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='内容素材-策划任务关联表';

-- ----------------------------
-- Table structure for rel_fp_channel
-- ----------------------------
DROP TABLE IF EXISTS `rel_fp_channel`;
CREATE TABLE `rel_fp_channel` (
  `rel_fp_channel_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '策划计划和渠道关联id',
  `fp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '策划计划id',
  `channel_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '渠道id',
  PRIMARY KEY (`rel_fp_channel_id`) USING BTREE,
  KEY `fk_rel_fp_channel_fission_planning_1` (`fp_id`) USING BTREE,
  KEY `fk_rel_fp_channel_config_channel_1` (`channel_id`) USING BTREE,
  CONSTRAINT `rel_fp_channel_ibfk_1` FOREIGN KEY (`channel_id`) REFERENCES `config_channel` (`channel_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_fp_channel_ibfk_2` FOREIGN KEY (`fp_id`) REFERENCES `fission_planning` (`fission_planning_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='策划计划渠道关联表';

-- ----------------------------
-- Table structure for rel_fp_content_group
-- ----------------------------
DROP TABLE IF EXISTS `rel_fp_content_group`;
CREATE TABLE `rel_fp_content_group` (
  `rel_fp_cg_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `fp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '裂变策划计划id',
  `group_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材组id',
  `rel_time` datetime DEFAULT NULL COMMENT '关联添加时间',
  `qr_pos_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '0:左上；1:左下；2:右上；3:右下；4:自定义',
  `qr_pos` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '(x, y)；单位：px',
  `qr_size` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '(35, 35);width，height;单位：px',
  PRIMARY KEY (`rel_fp_cg_id`) USING BTREE,
  KEY `fk_rel_fp_content_group_content_group_1` (`group_id`) USING BTREE,
  KEY `fk_rel_fp_content_group_fission_planning_1` (`fp_id`) USING BTREE,
  KEY `fk_rel_fp_content_group_dict_qr_pos_type_1` (`qr_pos_type_id`) USING BTREE,
  CONSTRAINT `rel_fp_content_group_ibfk_1` FOREIGN KEY (`group_id`) REFERENCES `content_group` (`group_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_fp_content_group_ibfk_2` FOREIGN KEY (`qr_pos_type_id`) REFERENCES `dict_qr_pos_type` (`qr_pos_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_fp_content_group_ibfk_3` FOREIGN KEY (`fp_id`) REFERENCES `fission_planning` (`fission_planning_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='裂变策划计划与素材组关联表';

-- ----------------------------
-- Table structure for rel_fp_tags
-- ----------------------------
DROP TABLE IF EXISTS `rel_fp_tags`;
CREATE TABLE `rel_fp_tags` (
  `rel_fpt_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '策划计划标签关联表',
  `fp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '策划计划id',
  `ct_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签id',
  `update_time` datetime DEFAULT NULL COMMENT '关联时间',
  `valid_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '0：无效；1：有效；',
  `tags_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签值',
  PRIMARY KEY (`rel_fpt_id`) USING BTREE,
  KEY `fk_rel_fp_tags_fission_planning_1` (`fp_id`) USING BTREE,
  KEY `fk_rel_fp_tags_dict_client_tags_1` (`ct_id`) USING BTREE,
  CONSTRAINT `rel_fp_tags_ibfk_1` FOREIGN KEY (`ct_id`) REFERENCES `tags` (`tags_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_fp_tags_ibfk_2` FOREIGN KEY (`fp_id`) REFERENCES `fission_planning` (`fission_planning_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='策划计划标签关联表';

-- ----------------------------
-- Table structure for rel_fp_targeting
-- ----------------------------
DROP TABLE IF EXISTS `rel_fp_targeting`;
CREATE TABLE `rel_fp_targeting` (
  `rel_fpt_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `targeting_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户定向id',
  `fp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '策划id',
  `targeting_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所选客户定向的值',
  PRIMARY KEY (`rel_fpt_id`) USING BTREE,
  KEY `fk_rel_fp_targeting_dict_targeting_type_1` (`targeting_type_id`) USING BTREE,
  KEY `fk_rel_fp_targeting_fission_planning_1` (`fp_id`) USING BTREE,
  CONSTRAINT `rel_fp_targeting_ibfk_1` FOREIGN KEY (`targeting_type_id`) REFERENCES `dict_targeting_type` (`targeting_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_fp_targeting_ibfk_2` FOREIGN KEY (`fp_id`) REFERENCES `fission_planning` (`fission_planning_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='裂变计划客户定向关联表';

-- ----------------------------
-- Table structure for rel_group_config
-- ----------------------------
DROP TABLE IF EXISTS `rel_group_config`;
CREATE TABLE `rel_group_config` (
  `rel_group_config_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `group_config_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '群配置id',
  `group_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户群id',
  `rel_time` datetime DEFAULT NULL COMMENT '关联时间',
  PRIMARY KEY (`rel_group_config_id`) USING BTREE,
  KEY `fk_rel_group_config_client_group_config_1` (`group_config_id`) USING BTREE,
  KEY `fk_rel_group_config_client_group_1` (`group_id`) USING BTREE,
  CONSTRAINT `rel_group_config_ibfk_1` FOREIGN KEY (`group_id`) REFERENCES `client_group` (`group_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_group_config_ibfk_2` FOREIGN KEY (`group_config_id`) REFERENCES `client_group_config` (`client_group_config_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户群关联表';

-- ----------------------------
-- Table structure for rel_landpage_com
-- ----------------------------
DROP TABLE IF EXISTS `rel_landpage_com`;
CREATE TABLE `rel_landpage_com` (
  `rel_landpage_com_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `landpage_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `com_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`rel_landpage_com_id`) USING BTREE,
  KEY `fk_rel_landpage_com_landpage_1` (`landpage_id`) USING BTREE,
  KEY `fk_rel_landpage_com_landpage_component_1` (`com_id`) USING BTREE,
  CONSTRAINT `rel_landpage_com_ibfk_1` FOREIGN KEY (`landpage_id`) REFERENCES `landpage` (`landpage_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_landpage_com_ibfk_2` FOREIGN KEY (`com_id`) REFERENCES `landpage_component` (`landpage_com_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for rel_landpage_com_value
-- ----------------------------
DROP TABLE IF EXISTS `rel_landpage_com_value`;
CREATE TABLE `rel_landpage_com_value` (
  `rel_lcv_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `landpage_com_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '落地页对应组件',
  `com_type_property_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件属性',
  `property_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '属性值',
  PRIMARY KEY (`rel_lcv_id`) USING BTREE,
  KEY `fk_rel_landpage_com_value_rel_landpage_com_1` (`landpage_com_id`) USING BTREE,
  KEY `fk_rel_landpage_com_value_rel_com_type_property_1` (`com_type_property_id`) USING BTREE,
  CONSTRAINT `rel_landpage_com_value_ibfk_1` FOREIGN KEY (`com_type_property_id`) REFERENCES `rel_com_property` (`rel_ctp_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_landpage_com_value_ibfk_2` FOREIGN KEY (`landpage_com_id`) REFERENCES `rel_landpage_com` (`rel_landpage_com_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for rel_mini_qrcode
-- ----------------------------
DROP TABLE IF EXISTS `rel_mini_qrcode`;
CREATE TABLE `rel_mini_qrcode` (
  `rel_id` bigint NOT NULL COMMENT '关联ID',
  `rel_type` tinyint NOT NULL COMMENT '类型 1：名片 2：雷达',
  `query_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '参数',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`rel_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='关联小程序码参数表';

-- ----------------------------
-- Table structure for rel_partner_tags
-- ----------------------------
DROP TABLE IF EXISTS `rel_partner_tags`;
CREATE TABLE `rel_partner_tags` (
  `rel_partner_tag_id` bigint NOT NULL COMMENT '合伙人关联标签',
  `partner_id` bigint NOT NULL COMMENT '合伙人ID',
  `tags_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签ID',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`rel_partner_tag_id`) USING BTREE,
  KEY `partner_id` (`partner_id`) USING BTREE,
  KEY `tags_id` (`tags_id`) USING BTREE,
  CONSTRAINT `rel_partner_tags_ibfk_1` FOREIGN KEY (`partner_id`) REFERENCES `client_partner` (`partner_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_partner_tags_ibfk_2` FOREIGN KEY (`tags_id`) REFERENCES `tags` (`tags_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='合伙人关联标签表';

-- ----------------------------
-- Table structure for rel_qb_qc
-- ----------------------------
DROP TABLE IF EXISTS `rel_qb_qc`;
CREATE TABLE `rel_qb_qc` (
  `rel_id` int NOT NULL AUTO_INCREMENT,
  `qc_id` int DEFAULT NULL COMMENT '题类id',
  `qb_id` int DEFAULT NULL COMMENT '试题id',
  PRIMARY KEY (`rel_id`) USING BTREE,
  KEY `qc_id` (`qc_id`) USING BTREE,
  KEY `qb_id` (`qb_id`) USING BTREE,
  CONSTRAINT `rel_qb_qc_ibfk_1` FOREIGN KEY (`qc_id`) REFERENCES `question_category` (`qc_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_qb_qc_ibfk_2` FOREIGN KEY (`qb_id`) REFERENCES `question_bank` (`question_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=147 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='试题分类';

-- ----------------------------
-- Table structure for rel_qb_tag
-- ----------------------------
DROP TABLE IF EXISTS `rel_qb_tag`;
CREATE TABLE `rel_qb_tag` (
  `rel_id` int NOT NULL AUTO_INCREMENT COMMENT '关联id',
  `qb_id` int DEFAULT NULL COMMENT '试题id',
  `tag_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签id',
  PRIMARY KEY (`rel_id`) USING BTREE,
  KEY `rel_qb_tag_ibfk_1` (`qb_id`) USING BTREE,
  KEY `rel_qb_tag_ibfk_2` (`tag_id`) USING BTREE,
  CONSTRAINT `rel_qb_tag_ibfk_1` FOREIGN KEY (`qb_id`) REFERENCES `question_bank` (`question_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_qb_tag_ibfk_2` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`tags_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for rel_qpp_case
-- ----------------------------
DROP TABLE IF EXISTS `rel_qpp_case`;
CREATE TABLE `rel_qpp_case` (
  `rel_case_id` bigint NOT NULL COMMENT '关联案例ID',
  `qpp_id` bigint DEFAULT NULL COMMENT '痛点及方案ID',
  `case_type` tinyint DEFAULT NULL COMMENT '案例类型 1：客户雷达 2：表单录入',
  `client_radar_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户雷达ID',
  `case_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '案例名称',
  `case_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '案例内容',
  `case_links` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '案例链接 多个用，分割',
  `delete_flag` tinyint DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`rel_case_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='痛点及方案关联案例表';

-- ----------------------------
-- Table structure for rel_qpp_content
-- ----------------------------
DROP TABLE IF EXISTS `rel_qpp_content`;
CREATE TABLE `rel_qpp_content` (
  `rel_recommend_id` bigint NOT NULL COMMENT '关联素材推荐ID',
  `qpp_id` bigint DEFAULT NULL COMMENT '痛点及方案ID',
  `content_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材ID',
  `delete_flag` tinyint DEFAULT '0' COMMENT '1：已删除 0：未删除',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`rel_recommend_id`) USING BTREE,
  KEY `qpp_id` (`qpp_id`) USING BTREE,
  KEY `content_id` (`content_id`) USING BTREE,
  CONSTRAINT `rel_qpp_content_ibfk_1` FOREIGN KEY (`qpp_id`) REFERENCES `question_point_plan` (`qpp_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_qpp_content_ibfk_2` FOREIGN KEY (`content_id`) REFERENCES `content_info` (`content_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='痛点及方案关联推荐素材表';

-- ----------------------------
-- Table structure for rel_qpp_file
-- ----------------------------
DROP TABLE IF EXISTS `rel_qpp_file`;
CREATE TABLE `rel_qpp_file` (
  `rel_file_id` bigint NOT NULL COMMENT '答案关联附件',
  `qpp_id` bigint DEFAULT NULL COMMENT '关联痛点及方案iD',
  `qpp_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联类型 sore_point:痛点 describe：业务场景描述 function_point：解决功能点',
  `file_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '附件路径',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '附件名称',
  `file_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件描述',
  `file_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '附件类型 image ：图片 file:文件',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `preview_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '文档预览',
  PRIMARY KEY (`rel_file_id`) USING BTREE,
  KEY `qpp_id` (`qpp_id`) USING BTREE,
  CONSTRAINT `rel_qpp_file_ibfk_1` FOREIGN KEY (`qpp_id`) REFERENCES `question_point_plan` (`qpp_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='行业及痛点附件表';

-- ----------------------------
-- Table structure for rel_question_tpu
-- ----------------------------
DROP TABLE IF EXISTS `rel_question_tpu`;
CREATE TABLE `rel_question_tpu` (
  `rel_question_tup_id` bigint NOT NULL COMMENT '关联ID',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '员工ID',
  `question_id` bigint DEFAULT NULL COMMENT '问题ID',
  `question_option_id` bigint DEFAULT NULL COMMENT '选项id',
  `question_type_id` int DEFAULT NULL COMMENT '分类ID',
  `other_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '其他内容',
  `content` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '问答题内容',
  `orders` tinyint NOT NULL DEFAULT '0' COMMENT '选择顺序',
  `likes` int NOT NULL DEFAULT '0' COMMENT '获得赞数',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `answer_type` tinyint NOT NULL DEFAULT '1' COMMENT '回答类型1：个人 2：标准',
  PRIMARY KEY (`rel_question_tup_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  KEY `question_id` (`question_id`) USING BTREE,
  KEY `question_option_id` (`question_option_id`) USING BTREE,
  KEY `question_type_id` (`question_type_id`) USING BTREE,
  CONSTRAINT `rel_question_tpu_ibfk_1` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_question_tpu_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `question` (`question_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_question_tpu_ibfk_3` FOREIGN KEY (`question_option_id`) REFERENCES `question_option` (`question_option_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_question_tpu_ibfk_4` FOREIGN KEY (`question_type_id`) REFERENCES `dict_question_type` (`question_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识回答';

-- ----------------------------
-- Table structure for rel_question_tpu_comment
-- ----------------------------
DROP TABLE IF EXISTS `rel_question_tpu_comment`;
CREATE TABLE `rel_question_tpu_comment` (
  `rel_comment_id` bigint NOT NULL COMMENT '评论ID',
  `rel_question_tpu_id` bigint DEFAULT NULL COMMENT '回答ID',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '员工ID',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '评论内容',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '评论时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0：已删除 1：未删除',
  PRIMARY KEY (`rel_comment_id`) USING BTREE,
  KEY `rel_question_tpu_id` (`rel_question_tpu_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `rel_question_tpu_comment_ibfk_1` FOREIGN KEY (`rel_question_tpu_id`) REFERENCES `rel_question_tpu` (`rel_question_tup_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_question_tpu_comment_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='回答明细评论表';

-- ----------------------------
-- Table structure for rel_question_tpu_file
-- ----------------------------
DROP TABLE IF EXISTS `rel_question_tpu_file`;
CREATE TABLE `rel_question_tpu_file` (
  `rel_file_id` bigint NOT NULL COMMENT '答案关联附件',
  `rel_question_tup_id` bigint DEFAULT NULL COMMENT '员工答案表',
  `file_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '附件路径',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '附件名称',
  `file_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件描述',
  `file_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '附件类型 image ：图片 file:文件',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`rel_file_id`) USING BTREE,
  KEY `rel_question_tup_id` (`rel_question_tup_id`) USING BTREE,
  CONSTRAINT `rel_question_tpu_file_ibfk_1` FOREIGN KEY (`rel_question_tup_id`) REFERENCES `rel_question_tpu` (`rel_question_tup_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='答案关联附件表';

-- ----------------------------
-- Table structure for rel_question_tpu_likes
-- ----------------------------
DROP TABLE IF EXISTS `rel_question_tpu_likes`;
CREATE TABLE `rel_question_tpu_likes` (
  `rel_like_id` bigint NOT NULL COMMENT '点赞ID',
  `rel_question_tup_id` bigint DEFAULT NULL COMMENT '答案ID',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '员工ID',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '点赞时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除  1：已删除',
  PRIMARY KEY (`rel_like_id`) USING BTREE,
  KEY `rel_question_tup_id` (`rel_question_tup_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `rel_question_tpu_likes_ibfk_1` FOREIGN KEY (`rel_question_tup_id`) REFERENCES `rel_question_tpu` (`rel_question_tup_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_question_tpu_likes_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识库回答点赞表';

-- ----------------------------
-- Table structure for rel_radar_client_level
-- ----------------------------
DROP TABLE IF EXISTS `rel_radar_client_level`;
CREATE TABLE `rel_radar_client_level` (
  `rel_id` int NOT NULL AUTO_INCREMENT COMMENT '关联id',
  `radar_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '雷达ID',
  `client_level_id` int DEFAULT NULL COMMENT '客户等级ID',
  PRIMARY KEY (`rel_id`) USING BTREE,
  KEY `rel_radar_id` (`radar_id`) USING BTREE,
  KEY `rel_client_level` (`client_level_id`) USING BTREE,
  CONSTRAINT `rel_radar_client_level_ibfk_1` FOREIGN KEY (`client_level_id`) REFERENCES `dict_client_level` (`level_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_radar_client_level_ibfk_2` FOREIGN KEY (`radar_id`) REFERENCES `client_radar` (`client_radar_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='雷达与客户等级关联关系';

-- ----------------------------
-- Table structure for rel_radar_tags
-- ----------------------------
DROP TABLE IF EXISTS `rel_radar_tags`;
CREATE TABLE `rel_radar_tags` (
  `radar_rel_tag_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联id',
  `radar_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '雷达id',
  `tag_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签id',
  PRIMARY KEY (`radar_rel_tag_id`) USING BTREE,
  KEY `re_radar_tags_ibfk_1` (`radar_id`) USING BTREE,
  KEY `re_radar_tags_ibfk_2` (`tag_id`) USING BTREE,
  CONSTRAINT `re_radar_tags_ibfk_1` FOREIGN KEY (`radar_id`) REFERENCES `client_radar` (`client_radar_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `re_radar_tags_ibfk_2` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`tags_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='雷达标签';

-- ----------------------------
-- Table structure for rel_role_auth
-- ----------------------------
DROP TABLE IF EXISTS `rel_role_auth`;
CREATE TABLE `rel_role_auth` (
  `rel_role_auth_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `auth_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '功能授权id',
  `role_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色id',
  `valid_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '是否有效；1:有效；0:无效',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`rel_role_auth_id`) USING BTREE,
  KEY `fk_rel_role_auth_dict_auth_1` (`auth_id`) USING BTREE,
  KEY `fk_rel_role_auth_dict_tp_user_role_1` (`role_id`) USING BTREE,
  CONSTRAINT `rel_role_auth_ibfk_1` FOREIGN KEY (`auth_id`) REFERENCES `dict_auth` (`dict_auth_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_role_auth_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `dict_tp_user_role` (`dict_tp_user_role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色和权限关联表';

-- ----------------------------
-- Table structure for rel_role_view
-- ----------------------------
DROP TABLE IF EXISTS `rel_role_view`;
CREATE TABLE `rel_role_view` (
  `rel_role_view_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `role_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色id',
  `view_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '视图id',
  `sn` int DEFAULT NULL COMMENT '序号',
  PRIMARY KEY (`rel_role_view_id`) USING BTREE,
  KEY `fk_rel_role_view_dict_tp_user_role_1` (`role_id`) USING BTREE,
  KEY `fk_rel_role_view_function_view_1` (`view_id`) USING BTREE,
  CONSTRAINT `rel_role_view_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `dict_tp_user_role` (`dict_tp_user_role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_role_view_ibfk_2` FOREIGN KEY (`view_id`) REFERENCES `function_view` (`function_view_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色视图关系表';

-- ----------------------------
-- Table structure for rel_teacher_tags
-- ----------------------------
DROP TABLE IF EXISTS `rel_teacher_tags`;
CREATE TABLE `rel_teacher_tags` (
  `rel_teacher_tag_id` bigint NOT NULL AUTO_INCREMENT,
  `teacher_id` bigint NOT NULL COMMENT '合伙人ID',
  `tags_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签ID',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`rel_teacher_tag_id`) USING BTREE,
  KEY `teacher_id` (`teacher_id`) USING BTREE,
  KEY `tags_id` (`tags_id`) USING BTREE,
  CONSTRAINT `rel_teacher_tags_ibfk_1` FOREIGN KEY (`teacher_id`) REFERENCES `course_teacher` (`course_teacher_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_teacher_tags_ibfk_2` FOREIGN KEY (`tags_id`) REFERENCES `tags` (`tags_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4858307859105976322 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='教师标签表';

-- ----------------------------
-- Table structure for rel_tpu_channel
-- ----------------------------
DROP TABLE IF EXISTS `rel_tpu_channel`;
CREATE TABLE `rel_tpu_channel` (
  `tpu_channel_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户id',
  `channel_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '渠道id',
  `rel_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '关联时间',
  PRIMARY KEY (`tpu_channel_id`) USING BTREE,
  KEY `fk_rel_tpu_channel_config_channel_1` (`channel_id`) USING BTREE,
  KEY `fk_rel_tpu_channel_touchpoint_user_1` (`tp_user_id`) USING BTREE,
  CONSTRAINT `rel_tpu_channel_ibfk_1` FOREIGN KEY (`channel_id`) REFERENCES `config_channel` (`channel_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_tpu_channel_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户渠道关联表';

-- ----------------------------
-- Table structure for rel_tpu_role
-- ----------------------------
DROP TABLE IF EXISTS `rel_tpu_role`;
CREATE TABLE `rel_tpu_role` (
  `rel_tpu_role_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `tpu_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '触点用户id',
  `role_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色id',
  `valid_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '1:生效；0:不生效',
  PRIMARY KEY (`rel_tpu_role_id`) USING BTREE,
  KEY `fk_rel_tpu_role_touchpoint_user_1` (`tpu_id`) USING BTREE,
  KEY `fk_rel_tpu_role_dict_tp_user_role_1` (`role_id`) USING BTREE,
  CONSTRAINT `rel_tpu_role_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `dict_tp_user_role` (`dict_tp_user_role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `rel_tpu_role_ibfk_2` FOREIGN KEY (`tpu_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户角色关联表';

-- ----------------------------
-- Table structure for report_ai
-- ----------------------------
DROP TABLE IF EXISTS `report_ai`;
CREATE TABLE `report_ai` (
  `report_id` bigint NOT NULL AUTO_INCREMENT COMMENT '智能报告ID',
  `template_id` bigint DEFAULT NULL COMMENT '模版ID',
  `report_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '报告名称',
  `report_date` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '报告时间',
  `report_prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '报告提示词',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `report_type` tinyint(1) DEFAULT NULL COMMENT '1:研究型报告，2:普通报告',
  `report_model` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '报告使用的大模型',
  `report_process` float DEFAULT NULL COMMENT '报告进度',
  `report_abstract` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '报告摘要',
  `report_subjects` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主题词（英文，分割）',
  `report_key_words` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关键词（英文，分割）',
  PRIMARY KEY (`report_id`) USING BTREE,
  KEY `template_id` (`template_id`) USING BTREE,
  KEY `report_ai_tpfk_2` (`tp_user_id`),
  CONSTRAINT `report_ai_ibfk_1` FOREIGN KEY (`template_id`) REFERENCES `report_template` (`template_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `report_ai_tpfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4947363578370854914 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能报告表';

-- ----------------------------
-- Table structure for report_dict_params
-- ----------------------------
DROP TABLE IF EXISTS `report_dict_params`;
CREATE TABLE `report_dict_params` (
  `param_id` bigint unsigned NOT NULL COMMENT '参数ID',
  `param_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数名称',
  `param_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数描述',
  `param_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数编码',
  `param_value` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '参数默认值',
  `delete_flag` tinyint DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`param_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模板参数表';

-- ----------------------------
-- Table structure for report_template
-- ----------------------------
DROP TABLE IF EXISTS `report_template`;
CREATE TABLE `report_template` (
  `template_id` bigint NOT NULL COMMENT '模板表主键',
  `template_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模板名称',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人id',
  `use_counter` int DEFAULT '0' COMMENT '使用次数',
  `is_enable` tinyint DEFAULT '0' COMMENT '是否启用 1：启用 0：不启用',
  `prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '模版提示词',
  `auth_scheme_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限ID',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delete_flag` tinyint DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `template_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '模板内容',
  `outline_level` int DEFAULT NULL COMMENT '大纲级别',
  `outline_num` int DEFAULT NULL COMMENT '大纲个数',
  `index_style` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '编号样式',
  `generate_mode` tinyint(1) DEFAULT NULL COMMENT '1:AI生成 2:自定义',
  `template_describe` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模板说明',
  `template_type` tinyint(1) DEFAULT NULL COMMENT '1:私有模板 2预置模板',
  `template_subjects` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主题词（英文，分割）',
  `template_key_words` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关键词（英文，分割）',
  PRIMARY KEY (`template_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  KEY `report_template_auth_scheme_id_index` (`auth_scheme_id`) USING BTREE,
  CONSTRAINT `report_template_content_auth_scheme_auth_scheme_id_fk` FOREIGN KEY (`auth_scheme_id`) REFERENCES `content_auth_scheme` (`auth_scheme_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `report_template_ibfk_1` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能报告模板表';

-- ----------------------------
-- Table structure for report_template_knowledge
-- ----------------------------
DROP TABLE IF EXISTS `report_template_knowledge`;
CREATE TABLE `report_template_knowledge` (
  `template_knowledge_id` int NOT NULL AUTO_INCREMENT,
  `template_id` bigint DEFAULT NULL COMMENT '模板id',
  `knowledge_id` bigint DEFAULT NULL COMMENT '知识库id',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`template_knowledge_id`) USING BTREE,
  KEY `template_id` (`template_id`) USING BTREE,
  KEY `knowledge_id` (`knowledge_id`) USING BTREE,
  CONSTRAINT `report_template_knowledge_ibfk_1` FOREIGN KEY (`template_id`) REFERENCES `report_template` (`template_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `report_template_knowledge_ibfk_2` FOREIGN KEY (`knowledge_id`) REFERENCES `aigc_knowledge` (`knowledge_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模板-知识库关系表';

-- ----------------------------
-- Table structure for report_template_params
-- ----------------------------
DROP TABLE IF EXISTS `report_template_params`;
CREATE TABLE `report_template_params` (
  `template_param_id` int NOT NULL AUTO_INCREMENT COMMENT '模板-参数关系表ID',
  `param_id` bigint unsigned DEFAULT NULL COMMENT '参数id',
  `template_id` bigint DEFAULT NULL COMMENT '模板id',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `delete_flag` tinyint(1) DEFAULT NULL COMMENT '1:已删除 0：未删除',
  PRIMARY KEY (`template_param_id`) USING BTREE,
  KEY `template_id` (`template_id`) USING BTREE,
  KEY `report_template_params_ibfk_1` (`param_id`) USING BTREE,
  CONSTRAINT `report_template_params_ibfk_1` FOREIGN KEY (`param_id`) REFERENCES `report_dict_params` (`param_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `report_template_params_ibfk_2` FOREIGN KEY (`template_id`) REFERENCES `report_template` (`template_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模板-参数关系表';

-- ----------------------------
-- Table structure for report_template_tools
-- ----------------------------
DROP TABLE IF EXISTS `report_template_tools`;
CREATE TABLE `report_template_tools` (
  `template_tool_id` int NOT NULL AUTO_INCREMENT,
  `template_id` bigint DEFAULT NULL,
  `tool_id` bigint DEFAULT NULL,
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '1:已删除 0：未删除',
  PRIMARY KEY (`template_tool_id`) USING BTREE,
  KEY `template_id` (`template_id`) USING BTREE,
  KEY `report_template_tools_ibfk_2` (`tool_id`) USING BTREE,
  CONSTRAINT `report_template_tools_ibfk_1` FOREIGN KEY (`template_id`) REFERENCES `report_template` (`template_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `report_template_tools_ibfk_2` FOREIGN KEY (`tool_id`) REFERENCES `report_tools` (`tool_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模板-工具关系表';

-- ----------------------------
-- Table structure for report_tools
-- ----------------------------
DROP TABLE IF EXISTS `report_tools`;
CREATE TABLE `report_tools` (
  `tool_id` bigint NOT NULL AUTO_INCREMENT,
  `tool_name` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工具名称',
  `tool_desc` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工具描述',
  `tool_code` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工具编码',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT NULL,
  `delete_flag` tinyint DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `sort` int DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`tool_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4911914059177136130 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工具表';

-- ----------------------------
-- Table structure for sale_version
-- ----------------------------
DROP TABLE IF EXISTS `sale_version`;
CREATE TABLE `sale_version` (
  `sale_version_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `sale_version_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '售卖版本的名称',
  PRIMARY KEY (`sale_version_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='售卖版本表';

-- ----------------------------
-- Table structure for tag_group
-- ----------------------------
DROP TABLE IF EXISTS `tag_group`;
CREATE TABLE `tag_group` (
  `tag_group_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `group_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签组名称',
  `father_group_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '父标签组id',
  `tag_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签类型id',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志位：0:未删除；1:已删除',
  `sn` int DEFAULT NULL COMMENT '排列序号',
  PRIMARY KEY (`tag_group_id`) USING BTREE,
  KEY `fk_tag_group_dict_tags_type_1` (`tag_type_id`) USING BTREE,
  CONSTRAINT `tag_group_ibfk_1` FOREIGN KEY (`tag_type_id`) REFERENCES `dict_tags_type` (`tags_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='素材组表';

-- ----------------------------
-- Table structure for tags
-- ----------------------------
DROP TABLE IF EXISTS `tags`;
CREATE TABLE `tags` (
  `tag_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签名称',
  `tags_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '素材id',
  `tag_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签类型id',
  `tag_group_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签组id',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志位；0:未删除；1:已删除',
  PRIMARY KEY (`tags_id`) USING BTREE,
  KEY `tags_id` (`tags_id`) USING BTREE,
  KEY `fk_tags_group_1` (`tag_group_id`) USING BTREE,
  KEY `fk_tags_type_tags_1` (`tag_type_id`) USING BTREE,
  CONSTRAINT `tags_ibfk_1` FOREIGN KEY (`tag_group_id`) REFERENCES `tag_group` (`tag_group_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tags_ibfk_2` FOREIGN KEY (`tag_type_id`) REFERENCES `dict_tags_type` (`tags_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='标签表（素材内容标签、客户标签、策划标签）';

-- ----------------------------
-- Table structure for template_fp
-- ----------------------------
DROP TABLE IF EXISTS `template_fp`;
CREATE TABLE `template_fp` (
  `template_fp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '策划计划模板id',
  `template_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '策划计划模板名称',
  `tfp_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模板类型id',
  `cover_pic_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模板封面id',
  `base_fp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '策划模板基于的策划计划id',
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '1:启用；0:停用',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '0:未删除；1:已删除',
  `add_time` datetime DEFAULT NULL COMMENT '添加时间',
  PRIMARY KEY (`template_fp_id`) USING BTREE,
  KEY `fk_template_fp_dict_template_fp_type_1` (`tfp_type_id`) USING BTREE,
  CONSTRAINT `template_fp_ibfk_1` FOREIGN KEY (`tfp_type_id`) REFERENCES `dict_template_fp_type` (`tfp_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='策划计划模板表';

-- ----------------------------
-- Table structure for together_approval_record
-- ----------------------------
DROP TABLE IF EXISTS `together_approval_record`;
CREATE TABLE `together_approval_record` (
  `together_approval_id` bigint NOT NULL COMMENT '审批记录ID',
  `together_id` bigint DEFAULT NULL COMMENT '共创ID',
  `status` tinyint DEFAULT NULL COMMENT '审批状态',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '原因',
  `approval_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '审批时间',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批人',
  `delete_flag` tinyint DEFAULT '0' COMMENT '0：未删除 1：已删除',
  PRIMARY KEY (`together_approval_id`) USING BTREE,
  KEY `together_id` (`together_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `together_approval_record_ibfk_1` FOREIGN KEY (`together_id`) REFERENCES `together_create` (`together_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `together_approval_record_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='共创审批记录表';

-- ----------------------------
-- Table structure for together_create
-- ----------------------------
DROP TABLE IF EXISTS `together_create`;
CREATE TABLE `together_create` (
  `together_id` bigint NOT NULL AUTO_INCREMENT COMMENT '共创ID',
  `partner_id` bigint DEFAULT NULL COMMENT '合伙人ID',
  `together_theme` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '共创主题',
  `together_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '正文',
  `status` tinyint DEFAULT '1' COMMENT '审核状态 1：待审核 2：审核通过 3：驳回',
  `is_hot` tinyint DEFAULT '0' COMMENT '是否是热门共创 1：是 0：否',
  `sort` int DEFAULT '999999' COMMENT '排序',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `sp_time` datetime DEFAULT NULL COMMENT '审批时间',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批人',
  `delete_flag` tinyint DEFAULT '0' COMMENT '1:已删除 0：未删除',
  `level_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '级别',
  `zb_num` int DEFAULT NULL COMMENT '智邦b数量',
  PRIMARY KEY (`together_id`) USING BTREE,
  KEY `partner_id` (`partner_id`) USING BTREE,
  KEY `tp_user_id` (`tp_user_id`) USING BTREE,
  CONSTRAINT `together_create_ibfk_1` FOREIGN KEY (`partner_id`) REFERENCES `client_partner` (`partner_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `together_create_ibfk_2` FOREIGN KEY (`tp_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4830834579576197122 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='合伙人共创表';

-- ----------------------------
-- Table structure for together_create_file
-- ----------------------------
DROP TABLE IF EXISTS `together_create_file`;
CREATE TABLE `together_create_file` (
  `together_file_id` bigint NOT NULL AUTO_INCREMENT COMMENT '共创关联附件ID',
  `together_id` bigint DEFAULT NULL COMMENT '共创ID',
  `file_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '附件路径',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '附件名称',
  `file_content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文件描述',
  `file_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '附件类型 image ：图片 file:文件',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`together_file_id`) USING BTREE,
  KEY `together_id` (`together_id`) USING BTREE,
  CONSTRAINT `together_create_file_ibfk_1` FOREIGN KEY (`together_id`) REFERENCES `together_create` (`together_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=4830814677289144322 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='共创关联附件表';

-- ----------------------------
-- Table structure for touch_method
-- ----------------------------
DROP TABLE IF EXISTS `touch_method`;
CREATE TABLE `touch_method` (
  `touch_method_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触达方法id',
  `fp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '策划计划id',
  `tm_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '触达方法类型id',
  PRIMARY KEY (`touch_method_id`) USING BTREE,
  KEY `fk_touch_method_fission_planning_1` (`fp_id`) USING BTREE,
  KEY `fk_touch_method_dict_touch_method_type_1` (`tm_type_id`) USING BTREE,
  CONSTRAINT `touch_method_ibfk_1` FOREIGN KEY (`tm_type_id`) REFERENCES `dict_touch_method_type` (`tm_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `touch_method_ibfk_2` FOREIGN KEY (`fp_id`) REFERENCES `fission_planning` (`fission_planning_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='触达方法';

-- ----------------------------
-- Table structure for touchpoint_user
-- ----------------------------
DROP TABLE IF EXISTS `touchpoint_user`;
CREATE TABLE `touchpoint_user` (
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'id',
  `dp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'department_id',
  `pa_flag_id` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Planning Authority 策划id等级',
  `tp_user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户名',
  `tp_user_alias` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '用户别称',
  `phone_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '电话号码',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'email地址',
  `position` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职位',
  `character_setting` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '人设树立',
  `delete_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志位；0:未删除；1:已删除',
  `ww_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企业微信user_id',
  `avatar` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信头像url',
  `thumb_avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微信头像缩略图url',
  `depart_list` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '企微部门list',
  `personal_qrcode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '个人二维码',
  `add_time` datetime DEFAULT NULL COMMENT '用户信息同步/添加时间',
  `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '用户信息修改时间',
  `seat_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '席位数；0：无效；1：有效',
  `service_role_id` int DEFAULT NULL COMMENT '服务角色ID',
  `pos_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '946f6788-7f14-11ee-900b-00163e06a675' COMMENT '角色id',
  `out_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '对外姓名',
  `out_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '对外邮箱',
  `status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '状态 0待审；1通过；2驳回',
  `pwd` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '密码 MD5 MD5 加密',
  PRIMARY KEY (`tp_user_id`) USING BTREE,
  KEY `fk_touchpoint_user_department_1` (`dp_id`) USING BTREE,
  KEY `fk_touchpoint_user_dict_planning_authority_1` (`pa_flag_id`) USING BTREE,
  KEY `service_role_id` (`service_role_id`) USING BTREE,
  KEY `fk_tpu_pos_id` (`pos_id`) USING BTREE,
  CONSTRAINT `touchpoint_user_ibfk_1` FOREIGN KEY (`dp_id`) REFERENCES `department` (`department_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `touchpoint_user_ibfk_2` FOREIGN KEY (`pa_flag_id`) REFERENCES `dict_planning_authority` (`dict_pa_flag_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `touchpoint_user_ibfk_3` FOREIGN KEY (`pos_id`) REFERENCES `dict_tpu_position` (`tpu_pos_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `touchpoint_user_ibfk_4` FOREIGN KEY (`service_role_id`) REFERENCES `dict_service_role` (`service_role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='触点之用户表';

-- ----------------------------
-- Table structure for tpu_data
-- ----------------------------
DROP TABLE IF EXISTS `tpu_data`;
CREATE TABLE `tpu_data` (
  `tpu_data_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触点用户数据id',
  `tpu_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '触点用户id',
  `latest_data_time` datetime DEFAULT NULL COMMENT '最新更新日期',
  `stat_date` date DEFAULT NULL COMMENT '统计日期',
  `convert_client_num` int DEFAULT NULL COMMENT '覆盖客户数',
  `fission_point_num` int DEFAULT NULL COMMENT '裂变触点数',
  `fission_index` int DEFAULT NULL COMMENT '统计值当日的裂变指数',
  `pageview_count` int DEFAULT NULL COMMENT '阅读量',
  `finish_read_count` int DEFAULT NULL COMMENT '完成量',
  `like_count` int DEFAULT NULL COMMENT '点赞量',
  `retweets_count` int DEFAULT NULL COMMENT '转发量',
  `comment_count` int DEFAULT NULL COMMENT '评论量',
  `total_read_time` int DEFAULT NULL COMMENT '总阅读时长',
  `new_clue_num` int DEFAULT NULL COMMENT '新客户量',
  `avg_rank` int DEFAULT NULL COMMENT '平均排名',
  `rank_date` datetime DEFAULT NULL COMMENT '排名周期时间',
  PRIMARY KEY (`tpu_data_id`) USING BTREE,
  KEY `fk_tpu_data_touchpoint_user_1` (`tpu_id`) USING BTREE,
  CONSTRAINT `tpu_data_ibfk_1` FOREIGN KEY (`tpu_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='触点用户批处理表';

-- ----------------------------
-- Table structure for tpu_seat_history
-- ----------------------------
DROP TABLE IF EXISTS `tpu_seat_history`;
CREATE TABLE `tpu_seat_history` (
  `tpu_seat_history_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `tpu_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户id',
  `seat_operate` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '+1:增加；-1:去除',
  `operate_time` datetime DEFAULT NULL COMMENT '操作时间',
  `operate_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户操作人id',
  PRIMARY KEY (`tpu_seat_history_id`) USING BTREE,
  KEY `fk_tpu_seat_history_touchpoint_user_1` (`tpu_id`) USING BTREE,
  KEY `fk_tpu_seat_history_touchpoint_user_2` (`operate_user_id`) USING BTREE,
  CONSTRAINT `tpu_seat_history_ibfk_1` FOREIGN KEY (`tpu_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tpu_seat_history_ibfk_2` FOREIGN KEY (`operate_user_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for tpu_total_data
-- ----------------------------
DROP TABLE IF EXISTS `tpu_total_data`;
CREATE TABLE `tpu_total_data` (
  `tpu_total_data_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `tpu_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `basic_read_num` int DEFAULT '0' COMMENT '客户打开量',
  `basic_collect_num` int DEFAULT '0' COMMENT '客户点赞量',
  `basic_trans_num` int DEFAULT '0' COMMENT '客户转发量',
  `basic_clue_num` int DEFAULT '0' COMMENT '新线索量',
  `basic_comment_num` int DEFAULT '0' COMMENT '评论量',
  `basic_interactive_num` int DEFAULT '0' COMMENT '客户交互量',
  `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `current_rank` int DEFAULT NULL COMMENT '最新排名',
  `current_fission_index` decimal(8,2) DEFAULT '40.00' COMMENT '裂变指数',
  `total_fission_index` decimal(8,2) DEFAULT '40.00' COMMENT '总体裂变指数',
  PRIMARY KEY (`tpu_total_data_id`) USING BTREE,
  KEY `fk_tpu_total_data_touchpoint_user_1` (`tpu_id`) USING BTREE,
  CONSTRAINT `tpu_total_data_ibfk_1` FOREIGN KEY (`tpu_id`) REFERENCES `touchpoint_user` (`tp_user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for tracking_data
-- ----------------------------
DROP TABLE IF EXISTS `tracking_data`;
CREATE TABLE `tracking_data` (
  `tracking_data_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '跟踪数据id',
  `function_view_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '功能视图id',
  `click_count` int NOT NULL DEFAULT '0' COMMENT '点击次数',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `component_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '组件id',
  `tracking_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '埋点数据类型',
  PRIMARY KEY (`tracking_data_id`) USING BTREE,
  KEY `fk_tracking_data_function_view_1` (`function_view_id`) USING BTREE,
  KEY `fk_tracking_data_view_component_1` (`component_id`) USING BTREE,
  KEY `fk_tracking_data_dict_view_tracking_type_1` (`tracking_type_id`) USING BTREE,
  CONSTRAINT `tracking_data_ibfk_1` FOREIGN KEY (`tracking_type_id`) REFERENCES `dict_view_tracking_type` (`tracking_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tracking_data_ibfk_2` FOREIGN KEY (`function_view_id`) REFERENCES `function_view` (`function_view_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tracking_data_ibfk_3` FOREIGN KEY (`component_id`) REFERENCES `view_component` (`component_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='页面埋点统计数据';

-- ----------------------------
-- Table structure for view_component
-- ----------------------------
DROP TABLE IF EXISTS `view_component`;
CREATE TABLE `view_component` (
  `component_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `component_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件名称',
  `function_view_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '页面id',
  `valid_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '是否有效',
  `com_type_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件类型id',
  `default_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '默认值',
  `placeholder` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'placeholder值',
  `sn` int DEFAULT NULL COMMENT '序号',
  `component_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件code，用来区分同一页面的组件',
  `help_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '帮助组件信息',
  PRIMARY KEY (`component_id`) USING BTREE,
  KEY `fk_view_component_function_view_1` (`function_view_id`) USING BTREE,
  KEY `fk_view_component_dict_component_type_1` (`com_type_id`) USING BTREE,
  CONSTRAINT `view_component_ibfk_1` FOREIGN KEY (`com_type_id`) REFERENCES `dict_component_type` (`com_type_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `view_component_ibfk_2` FOREIGN KEY (`function_view_id`) REFERENCES `function_view` (`function_view_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='页面组件库';

-- ----------------------------
-- Table structure for view_tips
-- ----------------------------
DROP TABLE IF EXISTS `view_tips`;
CREATE TABLE `view_tips` (
  `view_tips_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '页面提示信息id',
  `function_view_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '功能页面id',
  `tips_sn` int NOT NULL DEFAULT '0' COMMENT '提示序号',
  `tips_content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提示内容',
  PRIMARY KEY (`view_tips_id`) USING BTREE,
  KEY `fk_view_tips_function_view_1` (`function_view_id`) USING BTREE,
  CONSTRAINT `view_tips_ibfk_1` FOREIGN KEY (`function_view_id`) REFERENCES `function_view` (`function_view_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='页面提醒tips';

-- ----------------------------
-- Table structure for wework_add_msg_template
-- ----------------------------
DROP TABLE IF EXISTS `wework_add_msg_template`;
CREATE TABLE `wework_add_msg_template` (
  `add_msg_template_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `fm_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '裂变任务id',
  `external_client_id` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '外部客户id',
  `allow_select` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '是否允许成员在待发送客户列表中重新进行选择',
  `text_content` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '消息文本内容',
  `attachment_json` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '附件json数据',
  `attchment_num` int DEFAULT NULL COMMENT '附件数量',
  `image_num` int DEFAULT '0' COMMENT '附件中图片数量',
  `link_num` int DEFAULT NULL COMMENT '附件中链接数量',
  `mimi_num` int DEFAULT NULL COMMENT '附件中小程序数量',
  `video_num` int DEFAULT NULL COMMENT '附件中视频数量',
  `file_num` int DEFAULT NULL COMMENT '附件中文件数量',
  PRIMARY KEY (`add_msg_template_id`) USING BTREE,
  KEY `fk_wework_add_msg_template_fission_mission_1` (`fm_id`) USING BTREE,
  CONSTRAINT `wework_add_msg_template_ibfk_1` FOREIGN KEY (`fm_id`) REFERENCES `fission_mission` (`fm_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for wx_mp_user
-- ----------------------------
DROP TABLE IF EXISTS `wx_mp_user`;
CREATE TABLE `wx_mp_user` (
  `mp_user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'openid',
  `appid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'appid',
  `subscribe` tinyint NOT NULL DEFAULT '0' COMMENT '用户是否订阅该公众号标识，值为0时，代表此用户没有关注该公众号，拉取不到其余信息。\n',
  `language` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户的语言，简体中文为zh_CN',
  `subscribe_time` int NOT NULL DEFAULT '0' COMMENT '用户关注时间，为时间戳。如果用户曾多次关注，则取最后关注时间\n',
  `unionid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段。',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `groupid` int NOT NULL DEFAULT '0' COMMENT '用户所在的分组ID（兼容旧的用户分组接口）',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '首次更新时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint NOT NULL DEFAULT '0' COMMENT '0:未删除 1：已删除',
  `subscribe_scene` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '返回用户关注的渠道来源，ADD_SCENE_SEARCH 公众号搜索，ADD_SCENE_ACCOUNT_MIGRATION 公众号迁移，ADD_SCENE_PROFILE_CARD 名片分享，ADD_SCENE_QR_CODE 扫描二维码，ADD_SCENE_PROFILE_LINK 图文页内名称点击，ADD_SCENE_PROFILE_ITEM 图文页右上角菜单，ADD_SCENE_PAID 支付后关注，ADD_SCENE_WECHAT_ADVERTISEMENT 微信广告，ADD_SCENE_REPRINT 他人转载 ,ADD_SCENE_LIVESTREAM 视频号直播，ADD_SCENE_CHANNELS 视频号 , ADD_SCENE_OTHERS 其他',
  PRIMARY KEY (`mp_user_id`) USING BTREE,
  UNIQUE KEY `idx_openid_appid` (`openid`,`appid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=******************* DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='公众号 用户列表';

SET FOREIGN_KEY_CHECKS = 1;
