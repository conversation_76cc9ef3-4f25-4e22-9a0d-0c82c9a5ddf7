ALTER TABLE jusure_pro_2.aigc_model ADD param_size_b FLOAT DEFAULT 70 NULL COMMENT '模型参数：参数量';
ALTER TABLE jusure_pro_2.aigc_model ADD param_config TEXT NULL COMMENT '模型参数：配置信息';
ALTER TABLE jusure_pro_2.aigc_model ADD param_preference varchar(100) NULL COMMENT '模型参数：适配任务类型';
ALTER TABLE jusure_pro_2.aigc_model ADD param_human_rating INT DEFAULT 5 NULL COMMENT '模型参数：主观评分0-10';
ALTER TABLE jusure_pro_2.aigc_app ADD model_auto_enable TINYINT DEFAULT 0 NOT NULL COMMENT '是否开启自动模型选择';


CREATE TABLE `aigc_knowledge_galaxy_record` (
  `record_id` bigint NOT NULL COMMENT '记录id',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作用户id',
  `state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行状态',
  `search_log` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '查询结果',
  PRIMARY KEY (`record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识星河查询记录表';