先创建 QA 库，然后更新 mappings

```shell
PUT jusure_pro_2_knowledge_gt_demo_qa_bge-large-zh-v1.5/_mapping
{
 "properties": {
   "qa_lib_id": {
      "type": "keyword"
    },
    "status": {
      "type": "boolean"
    },
    "file": {
      "type": "keyword"
    },
    "creator": {
      "type": "keyword"
    },
    "tokens": {
      "type": "integer"
    },
    "add_time": {
      "type": "date",
      "format": "yyyy-MM-dd HH:mm:ss"
    },
    "update_time": {
      "type": "date",
      "format": "yyyy-MM-dd HH:mm:ss"
    }
  }
}
```

```shell
POST jusure_pro_2_knowledge_gt_demo_qa_bge-large-zh-v1.5/_update_by_query
{
  "script": {
    "source": """
      ctx._source.qa_lib_id = params.qa_lib_id;
      ctx._source.status = params.status;
      ctx._source.file = params.file;
      ctx._source.creator = params.creator;
      ctx._source.tokens = params.tokens;
      ctx._source.add_time = params.add_time;
      ctx._source.update_time = params.update_time;
    """,
    "lang": "painless",
    "params": {
      "qa_lib_id": "4954315800488972289",
      "status": true,
      "file": "",
      "creator": "edb15b16-2443-4314-bea1-06c5b7b944a5",
      "tokens": 0,
      "add_time": "2024-11-14 15:00:00",
      "update_time": "2024-11-14 15:00:00"
    }
  },
  "query": {
    "match_all": {}
  }
}
```
