/*
 Navicat Premium Dump SQL

 Source Server         : 阿里云
 Source Server Type    : MySQL
 Source Server Version : 80037 (8.0.37)
 Source Host           : **************:3306
 Source Schema         : jusure_pro_2

 Target Server Type    : MySQL
 Target Server Version : 80037 (8.0.37)
 File Encoding         : 65001

 Date: 28/11/2024 21:05:41
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for aigc_model_balance
-- ----------------------------
DROP TABLE IF EXISTS `aigc_model_balance`;
CREATE TABLE `aigc_model_balance` (
  `aigc_model_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `model_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `url_id` int NOT NULL AUTO_INCREMENT,
  `status` int DEFAULT NULL,
  PRIMARY KEY (`url_id`),
  KEY `模型ID` (`aigc_model_id`),
  CONSTRAINT `模型ID` FOREIGN KEY (`aigc_model_id`) REFERENCES `aigc_model` (`aigc_model_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
