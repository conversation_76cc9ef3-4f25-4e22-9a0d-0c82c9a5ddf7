#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/4/8 11:07
# <AUTHOR> zhangda
# @File    : app.py
# @Comment :
import random
import requests
import pandas as pd
import time, uuid, json
from apps.ai import task
from settings import BASE_DIR, JusureT3Url, UrlEnv
from flask import request, make_response, Response, stream_with_context, g
from flask_restful import Resource, reqparse

from horticulture.validate import json_response
from horticulture.auth_token import login_check
from controller.app_controller import AppController
from controller.report_controller import ReportController
from lib_func.const_map import CHATGPT_CODE


class DemoSessionReportView(Resource):

    @login_check
    def post(self):
        parser = reqparse.RequestParser()
        # parser.add_argument('message', type=str, location='json', help='消息', required=True)
        parser.add_argument('session_id', type=str, help='会话id', required=False)
        parser.add_argument('app_id', type=str, help='应用ID', required=True)
        parser.add_argument('report_id', type=str, help='应用ID', required=True)

        args = parser.parse_args()

        controller = AppController()
        app_info = controller.get_ai_app_info(args['app_id'])
        session_id = args['session_id']
        session_info = controller.get_app_session_info(session_id)
        report_info = ReportController().ai_report_info(args['report_id'])

        @stream_with_context
        def generate():
            if report_info and report_info['content']:
                # random_number = random.randint(1, 5)

                # knowledge_content = controller.search_report_knowledge(task, report_info['content'], app_info, size=random_number)

                if report_info['content_prompt'] is None:
                    content = "基于以下数据进行优化总结,要求：不要多次换行，字数控制在100字左右。" + report_info['content']
                else:
                    content = report_info['content_prompt'] + report_info['content']
                for item,reasoning_content in controller.ai_app_chat(content, [], app_info['aigc_type_id'], app_info['model_path'], model_id=app_info['aigc_model_id']):
                    data = json.dumps(
                        {'content': item or '', 'session_id': session_id, 'is_send': False, 'reasoning_content': reasoning_content,
                         'role': 'assistant', 'type': 'message'}, ensure_ascii=False)
                    yield f"data: {data}\n\n"
            if report_info and report_info['data_type'] == 'chatbi':
                if session_info['report_date']:
                    start_date = session_info['report_date'][0:4] + "-01-01"
                    end_date = session_info['report_date'] + "-20"
                else:
                    start_date = '2024-01-01'
                    end_date = '2024-06-30'
                yoy = 'true'
                qoq = 'true'
                param = {
                    'start_date': start_date,
                    'end_date': end_date,
                    'yoy': yoy,
                    'qoq': qoq
                }
                chatbi_list = ReportController().chatbi_data_list(report_info['chatbi_data'])
                chatbi = []
                if report_info['content_prompt'] is None:
                    content = "请基于以下内容先进行整体优化总结：\n\n 限制\n-仅返回总结优化后的内容\n -思考过程不要返回\n -不要返回任何提示语\n -每个段落分割为两个换行符\n。\n\n"

                else:
                    content = report_info['content_prompt']
                for item in chatbi_list:
                    data = self.request_api(item['uri'], item['r'], param, g.corpid, g.token)
                    chatbi_data = {
                        'ai_data': item,
                        'data': data['data']
                    }
                    chatbi.append(chatbi_data)
                    content += item['analysis_prompts'] + str(data['data']) + '\n'
                aigc_type_id = CHATGPT_CODE
                model_path = 'gpt-4'
                model_id = '4bc9b92c-ed70-11ed-9aa2-0242ac110003'
                # 数据总结
                for item,reasoning_content in controller.ai_app_chat(content, [], aigc_type_id, model_path, model_id=model_id):
                    data = json.dumps(
                        {'content': item or '', 'session_id': session_id, 'is_send': False,
                         'role': 'assistant', 'type': 'message'}, ensure_ascii=False)
                    yield f"data: {data}\n\n"
                if report_info['is_charts'] == 1:
                    for item in chatbi:
                        charts = {
                            'ai_data': item['ai_data'],
                            'data': item['data']
                        }
                        data = json.dumps(
                            {'content': '', 'session_id': session_id, 'is_send': False,
                             'role': 'assistant', 'type': 'chatbi', 'chatbi_data': charts}, ensure_ascii=False)
                        yield f"data: {data}\n\n"

            data = json.dumps(
                {'content': '', 'session_id': session_id, 'is_send': True, 'role': 'assistant',
                 'type': 'over'}, ensure_ascii=False)
            yield f"data: {data}\n\n"

        return Response(generate(), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'
        })

    @staticmethod
    def request_api(url, method, params, corpid, token):
        headers = {'Content-Type': 'application/json', 'corp-id': corpid, 'token': token}
        url = f'{JusureT3Url}{UrlEnv}{url}'
        if method.upper() == 'GET':
            param = ''.join([f'{k}={v}&' for k, v in params.items()])
            suf = '&' if '?' in url else '?'
            url += f'{suf}{param[:-1]}'
            return requests.get(url, headers=headers).json()
        elif method.upper() == 'POST':
            return requests.post(url, headers=headers, json=params).json()
        return requests.request(method, url, params).json()

    @staticmethod
    @login_check
    def get():

        parser = reqparse.RequestParser()
        parser.add_argument('app_id', type=str, required=True, location='args')
        parser.add_argument('next_report_id', type=str, required=False, default=None, location='args')
        parser.add_argument('message', type=str, required=False, default=None, location='args')
        parser.add_argument('session_id', type=str, required=False, default=None, location='args')
        parser.add_argument('report_date', type=str, required=False, default=None, location='args')
        args = parser.parse_args()
        controller = AppController()
        if args['session_id']:
            session_id = args['session_id']
        else:
            session_id = controller.create_app_session(args['app_id'], request.user['tp_user_id'], args['message'],
                                                       [], 'report', args['report_date'])
        report_controller = ReportController()
        res = report_controller.ai_report_menu(args['app_id'], args['next_report_id'])
        res.update({
            'session_id': session_id,
        })
        return json_response(data=res)


class DemoSessionReportRecordView(Resource):

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('session_id', type=str, help='会话id', required=True)
        parser.add_argument('record_info', type=list, location='json', help='报告记录', required=True)

        args = parser.parse_args()

        controller = ReportController()
        session_id = args['session_id']
        data = {
            'session_id': session_id,
            'record_info': args['record_info']
        }
        controller.update_session_record(session_id, data)
        return json_response(data={'session_id': session_id})

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('session_id', type=str, help='会话id', location='args', required=True)

        args = parser.parse_args()

        data = ReportController().get_session_record(args['session_id'])
        if data:
            return json_response(data={'data_list': data['record_info']})
        else:
            return json_response(data={'data_list': []})
