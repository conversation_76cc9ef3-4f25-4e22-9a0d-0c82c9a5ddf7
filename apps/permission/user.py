# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: user.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 3月 29, 2024
# ---
import settings
from flask import request, g
from flask_restful import Resource, reqparse
from horticulture.validate import json_response
from horticulture.auth_token import login_check, sign_check, set_token
from controller.user_controller import TPUserController, DPTController


class UserInfoView(Resource):

    @staticmethod
    @login_check
    def get():
        """
        获取用户信息
        """
        user_id = request.user['tp_user_id']
        user_info = TPUserController().user_info(user_id)
        return json_response(data=user_info)


class HandleUserInfoView(Resource):

    @staticmethod
    @sign_check
    def get():
        page_size = request.pmsd.get('page_size')
        page_no = request.pmsd.get('page_no')
        key_word = request.pmsd.get('key_word')
        ret = TPUserController().page_user(key_word, page_no, page_size)
        return json_response(data=ret)

    @staticmethod
    @sign_check
    def post():
        args = request.pmsd
        up_conf = dict(ww_user_id=args['gtid'], phone_number=args['phone'], tp_user_name=args['name'],
                       dp_id=args['dept_id'], tp_user_id=args['gtid'] or args['gtid'])
        status = str(request.pmsd.get('status') or 1)
        if status == '-1':
            up_conf['delete_flag'] = settings.DELETE_FLAG_TRUE
        else:
            up_conf['status'] = status
            if status == '1':
                up_conf['delete_flag'] = settings.DELETE_FLAG_FALSE
        ret = TPUserController().add_user_info(up_conf)
        return json_response(data=ret)


class HandleDPTInfoView(Resource):

    @staticmethod
    @sign_check
    def get():
        page_size = request.pmsd.get('page_size')
        page_no = request.pmsd.get('page_no')
        key_word = request.pmsd.get('key_word')
        ret = DPTController().page_dept(key_word, page_no, page_size)
        return json_response(data=ret)

    @staticmethod
    @sign_check
    def post():
        args = request.pmsd
        up_conf = dict(department_id=args['fd_id'], department_name=args['fd_name'], parent_dp_id=args['fd_parent_id'],
                       department_level=args['fd_org_type'])
        ret = DPTController().add_dpt_info(up_conf)
        return json_response(data=ret)


class GTTokenIssue(Resource):
    @staticmethod
    @sign_check
    @set_token
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('gtid', type=str, required=True, help='员工编号')
        args = parser.parse_args()
        user_info = TPUserController().user_info_by_code(args['gtid'])
        return user_info
