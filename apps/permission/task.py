import pandas as pd
import numpy as np
from settings import BASE_DIR
from controller.user_controller import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DPTController
from utils.tools import now_date_str


# 文件提取文本、分片、提取摘要和数据存储
def init_load_user_info(corp):
    BasePath = BASE_DIR + '/upload/user'
    user_action = TPUserController(corp)
    user_list = list()
    date_str = now_date_str()

    df_user = pd.read_excel(f'{BasePath}/{corp}/user_info.xlsx')
    df_user.replace(np.nan, None, inplace=True)
    for _, _data in df_user.iterrows():
        user_list.append({'dp_id': _data['dept_id'], 'tp_user_id': _data['fd_id'], 'phone_number': _data['phone'],
                          'tp_user_name': _data['name'], 'ww_user_id': _data['gtid'], 'add_time': date_str,
                          'modify_time': date_str})
    cut_list = user_list[:1000]
    index = 0
    while cut_list:
        index += 1000
        # index = index if index < len(user_list) else len(user_list)
        try:
            user_action.bulk_add_user_info(cut_list)
        except:
            pass
        cut_list = user_list[index: index+1000]


if __name__ == '__main__':
    init_load_user_info('wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ')