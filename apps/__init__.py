# -*- coding: utf-8 -*-
import sys
import flask_restful
from module import app
from flask_cors import CORS
from settings import BASE_DIR


class CustomApi(flask_restful.Api):

    def handle_error(self, e):
        raise Exception(e)


api = CustomApi(app)

app.url_map.strict_slashes = False

CORS(app, supports_credentials=True, resources=r'/*')

sys.path.insert(0, BASE_DIR)

from apps.permission import urls
from apps.ai import urls
from apps.chat import urls
from apps.demo import urls
from apps.openapi import urls
from apps.synthesis import urls
from apps.services import urls
