from flask import request
from flask_restful import Resource
from horticulture.auth_token import login_check
from horticulture.validate import json_response
from controller.app_controller import ServiceController


class OpenServiceList(Resource):
    @staticmethod
    @login_check
    def get():
        key_word = request.pmsd.get('key_word')
        res = ServiceController().service_list(key_word)
        return json_response(res)
