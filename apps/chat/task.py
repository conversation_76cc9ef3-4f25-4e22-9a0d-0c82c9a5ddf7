from celery_app import celery
from puppet.es_sdk import EsSdkPool
from controller.knowledge_controller import KnowledgeController
from controller.manager_controller import MasterAction
from controller.chat_controller import <PERSON><PERSON><PERSON><PERSON>roll<PERSON>
from lib_func.logger import task_logger
import time


@celery.task(bind=True, name="apps.chat.task.async_radar_to_es")
def async_radar_to_es(task):
    task_logger.info('async_radar_to_es start...')
    mapping = {
        "properties": {"radar_id": {"type": "keyword"},"radar_name": {"type": "keyword"},
        "radar_title": {"type": "keyword"},"pic_url": {"type": "keyword"},
        "qr_code": {"type": "keyword"},"vector": {"type": "dense_vector","dims": 1536,"index": True},
        "delete_flag": {"type": "boolean"},"tokens": {"type": "integer"}, "send_num": {"type": "integer"}, 
        "collect_num": {"type": "integer"}, "read_time": {"type": "integer"}}}
    index_name='radar_text-embedding-v1'
    corp_list = MasterAction().get_all_corp()
    for corp in corp_list:
        try:
            kc = KnowledgeController(corp['corpid'])
            cc = ChatController(corp['corpid'])
            es = EsSdkPool(corp['corpid'])
        except Exception as e:
            task_logger.error(f"corp: {corp['corpid']} init error: {e}")
            continue
        data = cc.get_radar_data()
        task_logger.info(f'len radar data: {len(data)}')
        if data:
            try:
                es.delete_index(index_name)
                task_logger.info(f'delete index: {index_name} success.')
            except Exception as e:
                task_logger.error(f'delete index error: {e}')
            try:
                es.crate_index(index_name, mapping=mapping)
                task_logger.info(f'create index: {index_name} success.')
            except Exception as e:
                task_logger.error(f'create index error: {e}')
                return
        for item in data:
            if not item['radar_name'] and not item['radar_title']:
                continue
            obj = {'name': item['radar_name'] or '', 'title': item['radar_title'] or '', 
                   'radar_id': item['client_radar_id'], 'pic_url': item['default_pic_url'], 
                   'qr_code': item['qr_code'], 'send_num': item['send_num'], 
                   'collect_num': item['collect_num'], 'read_time': item['read_time']}
            insert_data(obj, kc, es, index_name=index_name, mapping=mapping)
            time.sleep(0.1)


def insert_data(data={}, kc: KnowledgeController=None, es: EsSdkPool=None, index_name='radar_text-embedding-v1', mapping={}):
    name = data.get('name')
    title = data.get('title')
    radar_id = data.get('radar_id')
    pic_url = data.get('pic_url')
    qr_code = data.get('qr_code')
    send_num = data.get('send_num')
    collect_num = data.get('collect_num')
    read_time = data.get('read_time')
    user_id = 'user_id'
    res = kc.create_embeddings(name + title, user_id)
    vector = res.get('vector')
    tokens = res.get('total_tokens')
    data = {"radar_id": radar_id, "radar_name": name, "radar_title": title, 
            "pic_url": pic_url, "qr_code": qr_code, 
            "vector": vector, "tokens": tokens, "delete_flag": False, 
            "send_num": send_num, "collect_num": collect_num, "read_time": read_time}
    res = es.insert_data(index_name, data, mapping=mapping)
    task_logger.info(f'insert res: {res}')


if __name__ == '__main__':
    async_radar_to_es()

