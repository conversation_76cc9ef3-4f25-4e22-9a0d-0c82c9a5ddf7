#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/4/1 14:43
# <AUTHOR> zhang<PERSON>
# @File    : urls.py
# @Comment :
from apps import api
from apps.chat.views import *
from apps.chat.graphic_script import *

api.add_resource(ModelList, '/ai/model/list')
api.add_resource(AiModel, '/ai/model')
api.add_resource(ChatMenus, '/ai/chat/menus')
api.add_resource(Conversation, '/ai/conversation')
api.add_resource(ConversationAsync, '/ai/conversation/async')
api.add_resource(AiSession, '/ai/session')
api.add_resource(ChatRecord, '/ai/chat/record')
api.add_resource(AiQuestionNumberView, '/ai/select/ai')
api.add_resource(ChatBIRecord, '/ai/chatbi/record')

api.add_resource(AiModelManage, '/ai/model/manage')

api.add_resource(ModelTypeList, '/model/type/list')

# 图形脚本
api.add_resource(GraphicScriptView, '/ai/graphic/script')
# 获取模型详情（返回 3DES 加密数据）
api.add_resource(AiModelDetail, '/ai/model/detail')
