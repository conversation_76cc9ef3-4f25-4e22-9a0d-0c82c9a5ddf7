#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/4/1 14:43
# <AUTHOR> zhangda
# @File    : views.py
# @Comment :
import json, datetime
import settings
from flask import request, g, Response, stream_with_context
from flask_restful import Resource, reqparse
from horticulture.validate import json_response
from horticulture.auth_token import login_check
from controller.chat_controller import ChatController

from utils.tools import get_uuid, encrypt_3des
from puppet.chat_sdk import get_ai_number_prompt_free


class ServerSentEvent(object):
    def __init__(self, data, event=None, id=None):
        self.data = data
        self.event = event
        self.id = id

    def encode(self):
        if isinstance(self.data, bytes):
            payload = self.data.decode()
        else:
            payload = str(self.data)
        lines = ["data: " + line for line in payload.split("\n")]
        if self.event:
            lines.insert(0, "event: " + self.event)
        if self.id:
            lines.insert(0, "id: " + str(self.id))
        return "\n".join(lines) + "\n\n"


class ChatMenus(Resource):

    @staticmethod
    @login_check
    def get():
        """
        聊天菜单
        """
        parser = reqparse.RequestParser()
        parser.add_argument('model_id', type=str, default='', required=False, location='args')
        args = parser.parse_args()
        model_id = args.get('model_id')
        role_ids = sorted(request.user['role_ids'])
        controller = ChatController()
        res = controller.get_menus(role_ids, model_id=model_id)
        return json_response(code="SUCCESS", data=res)


class ModelList(Resource):

    @staticmethod
    @login_check
    def get():
        """
        模型列表
        """
        controller = ChatController()
        data = controller.get_model_list([2])
        return json_response(data)


class AiModel(Resource):

    @staticmethod
    @login_check
    def get():
        """
        模型列表
        """
        controller = ChatController()
        data = controller.get_models()
        return json_response(code="SUCCESS", data=data)


class AiSession(Resource):

    @staticmethod
    @login_check
    def post():
        """
        创建会话
        """
        parser = reqparse.RequestParser()
        parser.add_argument('view_id', type=str, default='', required=False, location='json')
        parser.add_argument('model_id', type=str, default='', required=False, location='json')
        args = parser.parse_args()
        view_id = args.get('view_id')
        model_id = args.get('model_id')
        tp_user_id = request.user['tp_user_id']
        controller = ChatController()
        session_id = controller.get_new_session(tp_user_id, view_id, model_id)
        return json_response(code="SUCCESS", data={'session_id': session_id})


class ChatRecord(Resource):

    @staticmethod
    @login_check
    def post():
        """
        查询当前聊天记录
        """
        parser = reqparse.RequestParser()
        parser.add_argument('view_id', type=str, default='', required=False, location='json')
        parser.add_argument('model_id', type=str, default='', required=False, location='json')
        args = parser.parse_args()
        view_id = args.get('view_id')
        model_id = args.get('model_id')
        tp_user_id = request.user['tp_user_id']
        controller = ChatController()
        data = controller.get_chat_record(tp_user_id, view_id, model_id)
        return json_response(code="SUCCESS", data=data)


class Conversation(Resource):

    @staticmethod
    @login_check
    def post():
        """
        聊天
        """
        parser = reqparse.RequestParser()
        parser.add_argument('view_id', type=str, default='', required=False, location='json')
        parser.add_argument('model_id', type=str, default='', required=False, location='json')
        parser.add_argument('session_id', type=str, default='', required=False, location='json')
        parser.add_argument('content', type=str, default='', required=False, location='json')
        parser.add_argument('message_id', type=str, default='', required=False, location='json')
        parser.add_argument('img_url', type=str, default='', required=False, location='json')
        parser.add_argument('batch', type=str, default='1', required=False, location='json')
        parser.add_argument('batch_count', type=str, default='1', required=False, location='json')
        parser.add_argument('temp_prompt', type=str, default='', required=False, location='json')
        
        args = parser.parse_args()
        view_id = args.get('view_id')
        model_id = args.get('model_id')
        session_id = args.get('session_id')
        content = args.get('content')
        message_id = args.get('message_id') or get_uuid()
        img_url = args.get('img_url')
        batch = int(args.get('batch')) if args.get('batch') else 1
        batch_count = int(args.get('batch_count')) if args.get('batch_count') else 1
        tp_user_id = request.user['tp_user_id']
        temp_prompt = args.get('temp_prompt')

        # controller.call_generate(tp_user_id, view_id, model_id, session_id, content, message_id, img_url=img_url)
        # return json_response(code="SUCCESS", data={'message_id': message_id})
        @stream_with_context
        def _generate():
            controller = ChatController()
            
            for doc in controller.call_generate(tp_user_id, view_id, model_id, session_id, content, message_id,
                                                img_url=img_url,temp_prompt=temp_prompt, batch=batch, batch_count=batch_count):

                yield f'data: {doc}\n\n'
        return Response(_generate(), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'
        })

    @staticmethod
    def get():
        """
        查聊天内容
        """
        parser = reqparse.RequestParser()
        parser.add_argument('message_id', type=str, default='', required=False, location='args')
        parser.add_argument('view_id', type=str, default='', required=False, location='args')
        args = parser.parse_args()
        message_id = args.get('message_id')
        view_id = args.get('view_id')
        if not message_id:
            # 不传时获取新的message_id
            return json_response(code="SUCCESS", data={"message_id": get_uuid()})
        controller = ChatController()
        data = controller.get_msg(message_id, view_id=view_id)
        return json_response(code="SUCCESS", data=data)
    

class ConversationAsync(Resource):
    @staticmethod
    @login_check
    def get():
        """
        查异步任务的结果
        """
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, default='', required=True, location='args')
        args = parser.parse_args()
        task_id = args.get('task_id')
        controller = ChatController()
        def event_stream():
            result = controller.get_async_task_result(task_id)
            if result:
                yield f"data: {result}\n\n"
        return Response(event_stream(), content_type='text/event-stream')


class AiQuestionNumberView(Resource):

    @staticmethod
    def post():
        """
        ai 问数
        """
        parser = reqparse.RequestParser()
        parser.add_argument('message', type=str, help='消息', required=True)

        args = parser.parse_args()
        message = args['message']
        data = get_ai_number_prompt_free(message)
        if data:
            return json_response(code='SUCCESS', data=data)
        return json_response(code='FAIL', data={})


class ChatBIRecord(Resource):

    @staticmethod
    @login_check
    def post():
        """
        chatbi 聊天 update
        """
        parser = reqparse.RequestParser()
        parser.add_argument('message_id', type=str, required=True, location='json')
        parser.add_argument('record_info', type=dict, required=True, location='json')
        args = parser.parse_args()
        message_id = args.get('message_id')
        record_info = args.get('record_info')
        controller = ChatController()
        controller.update_chatbi_record(message_id, record_info)
        return json_response(code="SUCCESS", data={'message_id': message_id})


class AiModelManage(Resource):
    @staticmethod
    @login_check
    def get():
        aigc_model_id = request.pmsd.get('aigc_model_id')
        if aigc_model_id:
            ret = ChatController().get_model_detail(aigc_model_id)
        else:
            ret = ChatController().get_model_list()
        return json_response(ret)

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('aigc_model_id', type=str, required=False)
        parser.add_argument('aigc_type_id', type=str, required=True)
        parser.add_argument('model_name', type=str, required=True)
        parser.add_argument('model_url', type=str, required=False, default='')
        parser.add_argument('model_icon', type=str, required=True)
        parser.add_argument('model_path', type=str, required=False)
        parser.add_argument('model_code', type=str, required=False)
        parser.add_argument('use_range', type=int, required=True)
        parser.add_argument('dims', type=int, required=False)
        parser.add_argument('aol', type=str, required=True)
        parser.add_argument('has_reason', type=str, default='0', required=False)
        parser.add_argument('is_default', type=str, default='0', required=False)
        args = parser.parse_args()
        ret = ChatController().add_ai_model(dict(args))
        return json_response(ret)

    @staticmethod
    @login_check
    def delete():
        parser = reqparse.RequestParser()
        parser.add_argument('aigc_model_id', type=str, required=True, location='json')
        args = parser.parse_args()
        ret = ChatController().up_ai_model(dict(args), {'delete_flag': settings.DELETE_FLAG_TRUE})
        return json_response(ret)


class ModelTypeList(Resource):
    @staticmethod
    @login_check
    def get():
        ret = ChatController().get_model_types()
        return json_response(ret)

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('aigc_type_id', type=str, required=False)
        parser.add_argument('aigc_type_name', type=str, required=True)
        parser.add_argument('api_key', type=str, required=False)
        parser.add_argument('secret_key', type=str, required=False)
        args = dict(parser.parse_args())
        aigc_type_id = args.pop('aigc_type_id')
        if aigc_type_id:
            ret = ChatController().up_model_type({'aigc_type_id': aigc_type_id}, args)
        else:
            ret = ChatController().add_model_type(args)
        return json_response(ret)


class AiModelDetail(Resource):
    @staticmethod
    @login_check
    def get():
        """
        获取模型详情（返回 3DES 加密数据）
        """
        parser = reqparse.RequestParser()
        parser.add_argument('aigc_model_id', type=str, required=True, location='args', help="aigc_model_id 不能为空")
        args = parser.parse_args()

        aigc_model_id = args['aigc_model_id']
        if not aigc_model_id:
            return json_response(code="FAIL", message="aigc_model_id 不能为空", data=None)

        ret = ChatController().get_model_detail_by_id(aigc_model_id)
        if not ret:
            return json_response(code="FAIL", message="未找到对应模型", data=None)

        # 将十六进制字符串转换为字节
        key_bytes = bytes.fromhex(settings.DES_KEY)
        iv_bytes = bytes.fromhex(settings.DES_IV)

        encrypted_data = encrypt_3des(json.dumps(ret), key_bytes, iv_bytes)
        return json_response(code="SUCCESS", data={"encrypted_data": encrypted_data})
