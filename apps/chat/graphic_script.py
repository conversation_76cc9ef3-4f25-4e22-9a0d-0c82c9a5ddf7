from flask_restful import Resource, reqparse
from horticulture.auth_token import login_check
from utils.tools import get_uuid
from controller.chat_controller import <PERSON>t<PERSON>ontroll<PERSON>
from flask import request, make_response, Response, stream_with_context, g

class GraphicScriptView(Resource):

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('view_id', type=str, default='', required=False, location='json')
        parser.add_argument('model_id', type=str, default='', required=False, location='json')
        parser.add_argument('session_id', type=str, default='', required=False, location='json')
        parser.add_argument('content', type=str, default='', required=False, location='json')
        parser.add_argument('context', type=str, default='', required=False, location='json')
        parser.add_argument('message_id', type=str, default='', required=False, location='json')
        parser.add_argument('img_url', type=str, default='', required=False, location='json')
        parser.add_argument('batch', type=str, default='1', required=False, location='json')
        parser.add_argument('batch_count', type=str, default='1', required=False, location='json')
        parser.add_argument('graphic_script_type', type=str, required=False, default='Mermaid')
        args = parser.parse_args()
        view_id = args.get('view_id')
        model_id = args.get('model_id')
        session_id = args.get('session_id')
        content = args.get('content')
        context = args.get('context')
        message_id = args.get('message_id') or get_uuid()
        img_url = args.get('img_url')
        batch = int(args.get('batch')) if args.get('batch') else 1
        batch_count = int(args.get('batch_count')) if args.get('batch_count') else 1
        tp_user_id = request.user['tp_user_id']
        graphic_script_type = args['graphic_script_type']

        @stream_with_context
        def _generate():
            controller = ChatController()
            for doc in controller.call_generate_graphic(tp_user_id, view_id, model_id, session_id, content, context, graphic_script_type, message_id,
                                                img_url=img_url, batch=batch, batch_count=batch_count):
                yield f'data: {doc}\n\n'
        return Response(_generate(), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'
        })
    