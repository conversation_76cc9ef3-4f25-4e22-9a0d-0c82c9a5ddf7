import os
from settings import KNOWLEDGE_OSS_BUCKET_NAME
from utils.tools import now_date_str, str_to_date
from celery_app import celery
from lib_func.logger import task_logger
from controller.batch_file_controller import SBFController
from controller.manager_controller import MasterAction
from module import minio_util


CacheHandleLog = dict()


# 文件提取文本、分片、提取摘要和数据存储
@celery.task(name="file_extract_and_save_data")
def file_extract_and_save_data():
    global CacheHandleLog
    corp_list = MasterAction().get_all_corp()
    date_str = now_date_str()
    for corp in corp_list:
        log_msg = f'{corp["corpid"]} {date_str}'
        task_logger.info(f'file_extract_and_save_data: {log_msg} 任务开始')
        try:
            kl_id_map_doc = dict()
            action = SBFController(corp["corpid"])
            file_map_paths = action.read_files(date_str)
            if not file_map_paths:
                task_logger.warning(f'file_extract_and_save_data: {log_msg} 任务结束-未发现任何文件')
                continue
            else:
                for kl_id, file_paths in file_map_paths.items():
                    for file_path in file_paths:
                        file_size = int(os.path.getsize(file_path) / 1024)
                        file_name = file_path.split(f'{kl_id}/')[-1]
                        doc_type = file_name.split('.')[-1]

                        # 基于内存判重处理；大于500初始化清空
                        cache_key = f'{corp}_{kl_id}_{file_name}_{file_size}'
                        if CacheHandleLog.get(cache_key):
                            if len(CacheHandleLog) >= 500:
                                CacheHandleLog = dict()
                            continue

                        if not action.knowledge_orm.check_knowledge_doc(kl_id, file_name, doc_size=file_size):

                            try:
                                object_name = f"crawler/{date_str}/{kl_id}/{file_name}"
                                oss_url = minio_util.upload_file_v2(KNOWLEDGE_OSS_BUCKET_NAME, object_name, file_path)
                            except:
                                oss_url = ''

                            try:
                                file_time = str_to_date(file_name.split('.')[-2].split('_')[-1])
                            except:
                                file_time = str_to_date(date_str)

                            doc_id = action.knowledge_orm.add_knowledge_doc(kl_id, file_name, doc_type, oss_url, file_size, "TODO", file_time=file_time)
                            kl_id_map_doc.setdefault(kl_id, []).append({'doc_id': doc_id, 'doc_url': file_path, 'doc_name': file_name})
                            CacheHandleLog[cache_key] = 1
                        else:
                            task_logger.warning(f'file_extract_and_save_data: {log_msg} 任务跳弹-{kl_id} {file_name}重复')
                            CacheHandleLog[cache_key] = 1
                            continue
                for kl_id, docs in kl_id_map_doc.items():
                    err_ret = action.cut_doc_index(kl_id, docs)
                    if err_ret:
                        task_logger.warning(f'file_extract_and_save_data: {log_msg} 部分异常任务 {kl_id} {",".join(err_ret)}')
                    if str(kl_id) == '4942745754495094785':
                        action.doc_abstract_batch(kl_id, docs)
                    task_logger.info(f'file_extract_and_save_data: {log_msg} 任务{kl_id}结束 数量：{len(docs)}')
        except Exception as e:
            task_logger.error(f'file_extract_and_save_data: {log_msg} 任务失败 {e}')
