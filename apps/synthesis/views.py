from flask import request
from flask_restful import Resource
from horticulture.validate import json_response
from controller.batch_file_controller import SBFController


class KnowledgeBaseCrawlerFile(Resource):

    @staticmethod
    def post():
        files = request.files.getlist('files') or []
        knowledge_id = request.pmsd.get('knowledge_id', '4942745754495094785')
        res = SBFController().save_web_crawler_file(knowledge_id, files)
        return json_response(res)
