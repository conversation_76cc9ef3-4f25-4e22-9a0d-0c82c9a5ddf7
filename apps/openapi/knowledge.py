# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: knowledge.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 8月 27, 2024
# ---
import json
import time
from apps.ai import task
from lib_func.const_map import StatusMap
from flask_restful import Resource, reqparse
from flask import jsonify
from horticulture.validate import json_response
from horticulture.auth_token import openapi_check
from controller.knowledge_controller import KnowledgeController
from lib_func.logger import logger
CORPID = 'wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ'


class OpenapiKnowledgeView(Resource):

    @staticmethod
    @openapi_check
    def get():
        """
        获取知识库列表
        :return:
        """
        parser = reqparse.RequestParser()
        parser.add_argument('page', type=int, default=1, location='args')
        parser.add_argument('limit', type=int, default=20, location='args')
        # parser.add_argument('name', type=str, required=False, location='args')
        args = parser.parse_args()

        data = KnowledgeController(CORPID).openapi_knowledge_list(args)
        return jsonify(data)


class OpenapiKnowledgeDocumentView(Resource):
    @staticmethod
    @openapi_check
    def get(dataset_id):
        parser = reqparse.RequestParser()
        parser.add_argument('page', type=int, default=1, location='args')
        parser.add_argument('limit', type=int, default=20, location='args')
        parser.add_argument('keyword', type=str, required=False, location='args')
        args = parser.parse_args()

        data = KnowledgeController(CORPID).openapi_knowledge_doc_list(dataset_id, args)
        return jsonify(data)


class OpenapiKnowledgeDocumentChunkView(Resource):

    @staticmethod
    @openapi_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('document_id', type=str, default=True, location='args')
        parser.add_argument('dataset_id', type=str, required=True, location='args')
        args = parser.parse_args()

        data = KnowledgeController(CORPID).openapi_knowledge_doc_chunk_detail(args['document_id'])
        return jsonify(data)


class OpenapiSearchContentKnowledgeView(Resource):
    """
    全文检索
    """

    @staticmethod
    @openapi_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('query', type=str, required=True, location='json')
        parser.add_argument('dataset_id', type=str, required=True, location='json')
        parser.add_argument('retrieval_model', type=dict, required=False, default={}, location='json')
        args = parser.parse_args()
        knowledge_id = args['dataset_id']
        if args['retrieval_model']:
            retrieval_model = args['retrieval_model']
            search_mode = retrieval_model.get('search_mode', 'semantic_search')
            reranking_enable = retrieval_model.get('reranking_enable', 'true')
            top_k = retrieval_model.get('top_k', 4)
            score_threshold_enabled = retrieval_model.get('score_threshold_enabled', 'true')
            score_threshold = retrieval_model.get('score_threshold')
            es_score = 1 + score_threshold
            full_score = 1.2 * score_threshold
        else:
            search_mode = 'semantic_search'
            reranking_enable = 'true'
            top_k = 4
            score_threshold_enabled = 'true'
            score_threshold = 0.1
            es_score = score_threshold + 1
            full_score = 1.0

        # help = 'semantic_search-词向量匹配，full_text_search-全文检索，hybrid_search-混合检索'

        # logger.info(f"search_mode: 【{search_mode}】, 1-词向量匹配，2-全文检索，3-混合检索 \nsearch_key: {args['search_key']}")

        controller = KnowledgeController(CORPID)
        knowledge_record = []
        # 根据 search_mode 执行不同的检索方式
        if search_mode in ['semantic_search', 'hybrid_search']:  # 词向量匹配或混合检索

            vector_knowledge_hits = controller.search_es_knowledge(task, args['query'], [knowledge_id], size=top_k, min_score=es_score,
                                                                   is_search=True)
            # logger.info(vector_knowledge_hits)

            for item in vector_knowledge_hits:
                item['score'] = controller.calculate_match_score_v2(item['_score'],2)
                knowledge_record.append({
                    "segment": {
                        "id": args['dataset_id'],
                        "document_id": item['_source']['doc_id'],
                        "content": item['_source']['description'],
                        "word_count": item['_source']['character_count'],
                        "tokens": item['_source']['tokens']
                    },
                    "score": item['score']
                })
        full_text_record = []
        if search_mode in ['full_text_search', 'hybrid_search']:  # 全文检索或混合检索

            full_text_knowledge_hits = controller.search_es_knowledge_full_text_v1(task, args['query'], [knowledge_id], size=top_k,
                                                                                   min_score=full_score)
            # logger.info(full_text_knowledge_hits)
            full_text_record = []
            for item in full_text_knowledge_hits:
                item['score'] = controller.calculate_match_score_v2(item['_score'])
                full_text_record.append({
                    "segment": {
                        "id": args['dataset_id'],
                        "document_id": item['_source']['doc_id'],
                        "content": item['_source']['description'],
                        "word_count": item['_source']['character_count'],
                        "tokens": item['_source']['tokens']

                    },
                    "score": item['score'],
                })
            # logger.info(full_text_record)
        knowledge_record.extend(full_text_record)
        record = sorted(knowledge_record, key=lambda x: x['score'], reverse=True)
        res_data = {
            "question": {
                "content": args['query'],
            },
            "records": record
        }
        # logger.info(res_data)
        return jsonify(res_data)
