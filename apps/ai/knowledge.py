# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: knowledge.py
# @Author: <PERSON>LiF<PERSON>
# @E-mail: <EMAIL>
# @Site:
# @Time: 3月 28, 2024
# ---
import asyncio
import json
import time

from pygments.lexer import default
from sympy import content
from apps.ai import task
from apps.ai.analyzer.cluster.cluster import LargeScaleDocumentClustering
from apps.ai.analyzer.cluster.launcher import ClusterMonitorLauncher
from apps.graphrag.graphrag import GraphRag
from apps.graphrag.configs import ENTITY_TYPES, OTHER_TYPE, TEMP_CORP_ID, EDGE_TYPE, SPACE_PREFIX
from celery_app import celery, chain
from controller.app_controller import AppController
from controller.qa_controller import QaExtractController
from controller.statistics_controller import StatisticsAppController, StatisticsAppDetailController
from lib_func.const_map import StatusMap
from flask_restful import Resource, reqparse
from horticulture.validate import json_response
from horticulture.auth_token import login_check
from horticulture.permission import permit
from horticulture.blog import action_log
from flask import request, g, stream_with_context, Response, send_file, make_response, jsonify
from controller.knowledge_controller import KnowledgeController
from controller.rule_controller import RuleController
from controller.user_controller import SchemeController
from controller.ai_model_controller import AiModelController
from lib_func.const_map import KNOWLEDGE_ROLE_CODES
from lib_func.logger import logger
from utils.tools import get_ring_times, str_to_date, compare_time_with_time_minutes,get_snowflake_id
from bson.objectid import ObjectId
from controller.user_controller import TPUserController, RoleController
import settings
from module import minio_util
import re
import os
from puppet.cache import redis_pool
from controller.es_opt_controller import EsOptController,SaveKnowledgeParams

class TestView(Resource):

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('doc_id', type=str, location='args')
        args = parser.parse_args()
        doc_id = args['doc_id']
        # data_list = MasterAction().get_model_conf()
        res = KnowledgeController().get_knowledge_doc_chunk_list(doc_id,{'is_all': 'all'})
        return json_response(data=res, code="SUCCESS", message='成功')


class TestStreamView(Resource):

    @staticmethod
    def get():
        @stream_with_context
        def generate():
            # 设置客户端连接的超时时间（可选）
            # timeout = int(request.args.get('timeout', 30))
            i = 1
            for i in range(1, 10):
                # # 模拟一些数据更新
                # if i == 5:
                #     break

                data = {'message': 'Hello from SSE!', 'time': time.time(), 'is_send': False, 'num': i}
                if i == 9:
                    data['is_send'] = True
                logger.info(data)
                time.sleep(1)  # 每秒发送一次事件
                yield f"data: {json.dumps(data)}\n\n"
                i += 1

        return Response(generate(), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'
        })

    @staticmethod
    def post():

        @stream_with_context
        def generate():
            # 设置客户端连接的超时时间（可选）
            # timeout = int(request.args.get('timeout', 30))
            i = 1
            for i in range(1, 10):
                # # 模拟一些数据更新
                # if i == 5:
                #     break

                data = {'message': 'Hello from SSE!', 'time': time.time(), 'is_send': False, 'num': i}
                if i == 9:
                    data['is_send'] = True
                logger.info(data)
                time.sleep(1)  # 每秒发送一次事件
                i += 1
                yield f"data: {json.dumps(data)}\n\n"

        return Response(generate(), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'
        })
class KnowledgeAllView(Resource):
    @staticmethod
    @login_check
    def get():
        data = KnowledgeController().get_all_knowledge_list()
        return json_response(data=data)



class KnowledgeView(Resource):

    @staticmethod
    @login_check
    def get():
        """
        获取知识库列表
        :return:
        """
        parser = reqparse.RequestParser()
        parser.add_argument('page_no', type=int, default=1, location='args')
        parser.add_argument('page_size', type=int, default=10, location='args')
        parser.add_argument('knowledge_name', type=str, required=False, location='args')
        parser.add_argument('knowledge_id', type=str, required=False, location='args')
        parser.add_argument('model_path', type=str, required=False, location='args')
        parser.add_argument('scene_id', type=str, required=False, location='args')
        parser.add_argument('scope', type=str, required=False, location='args')
        parser.add_argument('parent_directory_id', type=str, required=False, location='args')
        parser.add_argument('tag', type=str, required=False, location='args')
        parser.add_argument('status', type=int, required=False, location='args')
        parser.add_argument('scope_id', type=str, required=False, location='args')
        args = parser.parse_args()
        scope = args.get('scope', '')
        if scope and scope.isdigit():
            args['scope'] = int(scope)
        if args['scope_id']:
            args['scope_id'] = int(args['scope_id'])
        if args['knowledge_id']:
            data = KnowledgeController().get_knowledge_detail(args['knowledge_id'])
        else:
            data = KnowledgeController().get_knowledge_list(args)
        
        if args['parent_directory_id']:
            data = KnowledgeController().get_knowledge_list_by_directory_id(args)
        role_status = True if set(KNOWLEDGE_ROLE_CODES) & set(request.user['auth_ids']) else False
        data['role_status'] = role_status
        return json_response(data=data)

    @staticmethod
    @login_check
    def post():
        """
        添加知or编辑识库
        :return:
        """
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_name', type=str, required=True, location='json')
        parser.add_argument('knowledge_desc', type=str, required=True, location='json')
        parser.add_argument('knowledge_id', type=str, required=False, location='json')
        parser.add_argument('icon_url', type=str, required=False, location='json')
        parser.add_argument('aigc_model_id', type=str, required=True, location='json')
        parser.add_argument('scheme', type=dict, required=False, location='json')
        parser.add_argument('scene_id', type=int, required=False, location='json')
        parser.add_argument('scope', type=int, required=False, location='json', default=0,help='0: 公共知识库, 1: 个人知识库, 2: 私有知识库必须选择应用场景')
        parser.add_argument('graph_enable', type=int, required=False, location='json', default=0)

        parser.add_argument('parent_directory_id', type=str, required=False, location='json')
        parser.add_argument('tag', type=list, required=False, location='json')
        parser.add_argument('scope_id', type=int, required=False, location='json')
        args = parser.parse_args()
        scene_id = args.get("scene_id")
        scheme = args.get('scheme')
        if scheme and isinstance(scheme, dict):
            auth_scheme_id = scheme.get('auth_scheme_id')
            del args['scheme']
            args['auth_scheme_id'] = auth_scheme_id
        else:
            args['auth_scheme_id'] = None
            del args['scheme']
        tp_user_id = request.user['tp_user_id']
        es_opt_controller = EsOptController()
        if args['tag']:
            # 如果 tag 是列表，转为以逗号分隔的字符串
            args['tag'] = ",".join([str(tag).strip() for tag in args['tag'] if tag.strip()])
        if args['parent_directory_id'] == 'None':
            args['parent_directory_id'] = None
        if args['knowledge_id']:
            knowledge_id = args['knowledge_id']
            del args['knowledge_id']
            knowledge_id = KnowledgeController().update_knowledge(knowledge_id, args)
            save_obj = SaveKnowledgeParams(name=args.get('knowledge_name'), description=args.get('knowledge_desc'), knowledge_id=knowledge_id, scope=args.get('scope'))
            es_opt_controller.update_description(save_obj)
        else:
            knowledge_id = KnowledgeController().add_knowledge(args, tp_user_id, scene_id)
            save_obj = SaveKnowledgeParams(name=args.get('knowledge_name'), description=args.get('knowledge_desc'), knowledge_id=knowledge_id, scope=args.get('scope'))
            es_opt_controller.save_description(save_obj)
        return json_response(data={'knowledge_id': knowledge_id})

    @staticmethod
    @login_check
    def delete():
        """
        删除知识库
        :return:
        """
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        args = parser.parse_args()
        res = KnowledgeController().del_es_knowledge({'knowledge_id': args['knowledge_id']})
        es_opt_controller = EsOptController()
        del_knowledge_res = es_opt_controller.delete_knowledge_description(knowledge_id=args.get('knowledge_id'))
        if res and del_knowledge_res:
            knowledge_id = KnowledgeController().update_knowledge(args['knowledge_id'], {'delete_flag': settings.DELETE_FLAG_TRUE})
        else:
            return json_response(**StatusMap['delete_err'])
        return json_response(data={'knowledge_id': knowledge_id})

    @staticmethod
    @login_check
    def put():
        """
        修改知识库
        :return:
        """
        parser = reqparse.RequestParser()
        parser.add_argument('status', type=int, choices=[0, 1], location='json')
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        args = parser.parse_args()
        knowledge_id = KnowledgeController().update_knowledge(args['knowledge_id'], {'status': args['status']})
        return json_response(data={'knowledge_id': knowledge_id})


class KnowledgeDocumentView(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('page_no', type=int, default=1, location='args')
        parser.add_argument('page_size', type=int, default=10, location='args')
        parser.add_argument('knowledge_id', type=str, required=False, location='args')
        parser.add_argument('doc_name', type=str, required=False, location='args')
        parser.add_argument('document_id', type=str, required=False, location='args')
        parser.add_argument('current_node', type=str, required=False, location='args')
        parser.add_argument('state', type=str, required=False, location='args')
        parser.add_argument('upload_type', type=str, required=False, default='0', location='args')
        args = parser.parse_args()
        if args['document_id']:
            data = KnowledgeController().get_knowledge_document_detail(args['document_id'], args['upload_type'])
        else:
            data = KnowledgeController().get_knowledge_doc_list(args['knowledge_id'], args)
        return json_response(data=data)

    @staticmethod
    @login_check
    def delete():
        parser = reqparse.RequestParser()
        parser.add_argument('document_id', type=str, required=True, location='json')
        args = parser.parse_args()
        res = KnowledgeController().del_es_knowledge({'doc_id': args['document_id']})
        if res:
            document_id = KnowledgeController().del_knowledge_doc(args['document_id'])

        else:
            return json_response(**StatusMap['delete_err'])

        return json_response(data={'document_id': document_id})

    @staticmethod
    @login_check
    def patch():
        # 批量删除方法
        parser = reqparse.RequestParser()
        parser.add_argument('document_ids', type=list, required=True, location='json')
        args = parser.parse_args()
        document_ids = args['document_ids']
        deleted_ids = []
        failed_ids = []
        for document_id in document_ids:
            res = KnowledgeController().del_es_knowledge({'doc_id': document_id})
            if res:
                result = KnowledgeController().del_knowledge_doc(document_id)
                if result:
                    deleted_ids.append(document_id)
                else:
                    failed_ids.append(document_id)
            else:
                failed_ids.append(document_id)
        if failed_ids:
            return json_response(data={'deleted_ids': deleted_ids, 'failed_ids': failed_ids})
        else:
            return json_response(data={'deleted_ids': deleted_ids})

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('chunk_size', type=int, required=False, default=256, location='json')
        parser.add_argument('chunk_overlap', type=int, required=False, default=20, location='json')
        parser.add_argument('doc_list', type=list, required=True, location='json')
        parser.add_argument('slice_model', type=int, required=True, location='json', help='0:默认模式 1:段落分割 2:语义模式')
        parser.add_argument('pdf_model', type=int, required=False, location='json', default=0)
        args = parser.parse_args()
        doc_list = args['doc_list']
        slice_model = args['slice_model']


        for doc in doc_list:

            try:
                doc_name = doc.get('doc_name').split("_", 1)[-1]
            except Exception as e:
                logger.error(f"doc_name error: {e}")
                doc_name = doc['doc_name']
            doc['doc_id'] = KnowledgeController().add_knowledge_doc(args['knowledge_id'], doc_name, doc['doc_type'],
                                                                    doc['doc_url'], doc['doc_size'], 'TODO')

        doc_info = {
            'chunk_size': args['chunk_size'],
            'chunk_overlap': args['chunk_overlap'],
            'doc_list': doc_list,
            'pdf_model': args['pdf_model']
        }
        if len(doc_list) <= 0:
            return json_response(code='FAIL', data={'message': '文档数量不能为0'})
        # res = task.async_document_splitter.delay(g.corpid, args['knowledge_id'], doc_info)
        res = task.async_document_splitter_v1.delay(g.corpid, args['knowledge_id'], doc_info, slice_model)
        return json_response(code='SUCCESS', data={'task_id': res.id})

    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='json')
        args = parser.parse_args()
        res = celery.AsyncResult(args['task_id'])
        # chunk_list = list()
        has_fail = False
        if res and res.status == 'SUCCESS' and res.info.get('data'):
            has_fail = res.info.get('has_fail')
            
            """去除content
            chunk_list = res.info['chunk_list']
            for chunk in chunk_list:
                temp_chunk_dict = chunk.get('chunk_list')
                if not temp_chunk_dict:
                    continue
                data_list = temp_chunk_dict.get('data_list')
                if not data_list:
                    continue
                for data in data_list:
                    data.pop('content')
            """
            current = res.info.get('current')
            
        else:
            current = 0
        # return json_response(code='SUCCESS', data={'state': res.state, 'has_fail': has_fail, 'current': current, 'doc_info': res.info, 'chunk_list': chunk_list})
        return json_response(code='SUCCESS', data={'state': res.state, 'has_fail': has_fail, 'current': current, 'doc_info': res.info})

class HandleDocChainViewWithProgress(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('doc_list', type=list, required=True, location='json')
        parser.add_argument('pdf_model', type=int, required=False, location='json', default=0)
        parser.add_argument('chunk_size', type=int, required=False, location='json', default=256)
        parser.add_argument('chunk_overlap', type=int, required=False, location='json', default=20)
        parser.add_argument('slice_model', type=int, required=False, location='json', default=0)
        parser.add_argument('chunk_clear_call_words', type=str, required=False, location='json', default="")
        parser.add_argument('chunk_clear_rule_ids', type=list, required=False, location='json', default=[])
        parser.add_argument('upload_type', type=int, required=False, location='json', default=0)
        parser.add_argument('task_id', type=str, required=False, location='json', default='')
        args = parser.parse_args()
        knowledge_id = args['knowledge_id']
        doc_list = args['doc_list']
        pdf_model = args['pdf_model']
        tp_user_id = request.user['tp_user_id']
        split_chunk_size = args["chunk_size"]
        chunk_overlap = args["chunk_overlap"]
        slice_model = args["slice_model"]
        chunk_clear_call_words = args["chunk_clear_call_words"]
        chunk_clear_rule_ids = args["chunk_clear_rule_ids"]
        upload_type = args["upload_type"]
        args['tp_user_id'] = tp_user_id
        task_id = args['task_id']
        total_docs = len(doc_list)
        redis_client = redis_pool.use(redis_pool.aigc_pool)

        for index, doc in enumerate(doc_list, start=1):
            try:
                doc_name = doc.get('doc_name').split("_", 1)[-1]
            except Exception as e:
                logger.error(f"doc_name error: {e}")
                doc_name = doc['doc_name']

            doc['doc_id'] = KnowledgeController().add_knowledge_doc(
                args['knowledge_id'], doc_name, doc['doc_type'],
                doc['doc_url'], doc['doc_size'], 'TODO',
                    '文档上传',
                    '已完成',
                    None,
                    tp_user_id,
                    split_chunk_size,
                    chunk_overlap,
                    slice_model,
                    pdf_model,
                    chunk_clear_call_words,
                    upload_type
            )
            if chunk_clear_rule_ids:
                for chunk_clear_rule_id in chunk_clear_rule_ids:
                    KnowledgeController().save_chunk_clear_rule_id(doc['doc_id'],chunk_clear_rule_id)

            progress = round((index / total_docs) * 50, 2)
            redis_client.set(f'progress_{task_id}', progress)
        # 上传完成，设置阶段进度为 50
        redis_client.set(f'progress_{task_id}', 50)

        # 文档切分 + 向量生成信息
        doc_info = {
            'chunk_size': args['chunk_size'],
            'chunk_overlap': args['chunk_overlap'],
            'doc_list': doc_list,
            'pdf_model': args['pdf_model'],
            'task_id': task_id  # 传给 Celery 用于继续上报进度
        }
        # 异步处理链式任务（非阻塞）
        chain(
            task.async_document_splitter_v1.s(),
            task.async_split_connector_emb.s(g.corpid, args['knowledge_id']),
            task.async_appoint_document_chunk_embeddings_coroutine.s(),
            task.report_progress.s(task_id, 100),              # 最终完成
        ).apply_async(args=(g.corpid, args['knowledge_id'], doc_info, args['slice_model']))

        return json_response(code='SUCCESS', data={'task_id': task_id, 'doc_info': doc_info})


    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='json')
        args = parser.parse_args()

        redis_client = redis_pool.use(redis_pool.aigc_pool)
        progress = redis_client.get(f'progress_{args["task_id"]}')
        try:
            progress = float(progress) if progress else 0
        except ValueError:
            progress = 0

        return json_response(code='SUCCESS', data={'progress': progress})

class HandleDocChainView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('doc_list', type=list, required=True, location='json')
        parser.add_argument('pdf_model', type=int, required=False, location='json', default=0)
        parser.add_argument('chunk_size', type=int, required=False, location='json', default=256)
        parser.add_argument('chunk_overlap', type=int, required=False, location='json', default=20)
        parser.add_argument('slice_model', type=int, required=False, location='json', default=0)
        parser.add_argument('chunk_clear_call_words', type=str, required=False, location='json', default="")
        parser.add_argument('chunk_clear_rule_ids', type=list, required=False, location='json', default=[])
        parser.add_argument('upload_type', type=int, required=False, location='json', default=0)
        parser.add_argument('task_id', type=str, required=False, location='json', default='')
        args = parser.parse_args()
        knowledge_id = args['knowledge_id']
        doc_list = args['doc_list']
        pdf_model = args['pdf_model']
        tp_user_id = request.user['tp_user_id']
        split_chunk_size = args["chunk_size"]
        chunk_overlap = args["chunk_overlap"]
        slice_model = args["slice_model"]
        chunk_clear_call_words = args["chunk_clear_call_words"]
        chunk_clear_rule_ids = args["chunk_clear_rule_ids"]
        upload_type = args["upload_type"]
        args['tp_user_id'] = tp_user_id
        task_id = args['task_id']
        total_docs = len(doc_list)
        if task_id:
            redis_client = redis_pool.use(redis_pool.aigc_pool)
            for index, doc in enumerate(doc_list, start=1):
                try:
                    doc_name = doc.get('doc_name').split("_", 1)[-1]
                except Exception as e:
                    logger.error(f"doc_name error: {e}")
                    doc_name = doc['doc_name']

                doc['doc_id'] = KnowledgeController().add_knowledge_doc(
                    args['knowledge_id'], doc_name, doc['doc_type'],
                    doc['doc_url'], doc['doc_size'], 'TODO',
                        '文档上传',
                        '已完成',
                        None,
                        tp_user_id,
                        split_chunk_size,
                        chunk_overlap,
                        slice_model,
                        pdf_model,
                        chunk_clear_call_words,
                        upload_type
                )
                if chunk_clear_rule_ids:
                    for chunk_clear_rule_id in chunk_clear_rule_ids:
                        KnowledgeController().save_chunk_clear_rule_id(doc['doc_id'],chunk_clear_rule_id)

                progress = round((index / total_docs) * 100, 2)
                redis_client.set(f'progress_{task_id}', progress)
            # 上传完成，设置阶段进度为 50
            redis_client.set(f'progress_{task_id}', 100)

            # 文档切分 + 向量生成信息
            doc_info = {
                'chunk_size': args['chunk_size'],
                'chunk_overlap': args['chunk_overlap'],
                'doc_list': doc_list,
                'pdf_model': args['pdf_model'],
                'task_id': task_id  # 传给 Celery 用于继续上报进度
            }
            # 异步处理链式任务（非阻塞）
            chain(
                task.async_document_splitter_v1.s(),
                task.async_split_connector_emb.s(g.corpid, args['knowledge_id']),
                task.async_appoint_document_chunk_embeddings_coroutine.s()
            ).apply_async(args=(g.corpid, args['knowledge_id'], doc_info, args['slice_model']))

            return json_response(code='SUCCESS', data={'task_id': task_id})
        else:
            for doc in doc_list:

                try:
                    doc_name = doc.get('doc_name').split("_", 1)[-1]
                except Exception as e:
                    logger.error(f"doc_name error: {e}")
                    doc_name = doc['doc_name']
                doc['doc_id'] = KnowledgeController().add_knowledge_doc(args['knowledge_id'], doc_name, doc['doc_type'],
                                                                        doc['doc_url'], doc['doc_size'], 'TODO')

            doc_info = {
                'chunk_size': args['chunk_size'],
                'chunk_overlap': args['chunk_overlap'],
                'doc_list': doc_list,
                'pdf_model': args['pdf_model']
            }

            handle_chain = chain(task.async_document_splitter_v1.s(), task.async_split_connector_emb.s(g.corpid, args['knowledge_id']),
                                task.async_appoint_document_chunk_embeddings_coroutine.s())
            


            task_result = handle_chain.apply_async(args=(g.corpid, args['knowledge_id'], doc_info, slice_model))

            while not task_result.ready():
                time.sleep(0.5)
                continue
            parent_result = task_result.parent

            if parent_result:
                print(f"parent_result: {parent_result}")
            final_result = task_result.get()
            print(f"final_result: {final_result}")
            return json_response(data = final_result)  


    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='json')
        args = parser.parse_args()

        redis_client = redis_pool.use(redis_pool.aigc_pool)
        progress = redis_client.get(f'progress_{args["task_id"]}')
        try:
            progress = float(progress) if progress else 0
        except ValueError:
            progress = 0

        return json_response(code='SUCCESS', data={'progress': progress})

class KnowledgeDocumentViewV1(Resource):
    """
    @summary:知识库文档更新接口 增加文档清洗之后
    """
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('chunk_size', type=int, required=False, default=256, location='json')
        parser.add_argument('chunk_overlap', type=int, required=False, default=20, location='json')
        parser.add_argument('doc_list', type=list, required=True, location='json')
        parser.add_argument('slice_model', type=int, required=True, location='json')
        parser.add_argument('pdf_model', type=int, required=False, location='json', default=0)
        parser.add_argument('usr_prompt', type=str, required=False, default="你是一个ocr识别助手，这张图片有什么内容请全部给我",location='json')
        parser.add_argument('separator', type=str, required=False, location='json', default="")
        parser.add_argument('is_abstract', type=int, required=False, location='json', default=0)
        parser.add_argument('aigc_model_id', type=str, required=False, location='json', default=0)
        parser.add_argument('is_abstract_num', type=int, required=False, location='json', default=999)
        args = parser.parse_args()
        doc_list = args['doc_list']
        slice_model = args['slice_model']
        # 检查chunk_size和chunk_overlap是否符合要求
        if args.get('chunk_size') <= args.get('chunk_overlap') or args.get('chunk_size') <= 0 or args.get('chunk_overlap') <= 0:
            return json_response(code='FAIL', data={'message': 'chunk_size 必须大于 chunk_overlap并且chunk_size>0，chunk_overlap>0'})
        tp_user_id = request.user['tp_user_id']
        # 获取请求ip
        x_forwarded_for = request.headers.get('X-Forwarded-For')
        request_ip = ""
        if x_forwarded_for:
            request_ip = x_forwarded_for.split(',')[0]
        else:
            request_ip = request.remote_addr
        usr_prompt = args.get('usr_prompt',"你是一个ocr识别助手，这张图片有什么内容请全部给我")


        doc_info = {
            'chunk_size': args.get('chunk_size',256),
            'chunk_overlap': args.get('chunk_overlap',20),
            'doc_list': doc_list,
            'pdf_model': args.get('pdf_model',0),
            'separator': args.get('separator', ""), 
            'is_abstract': args.get('is_abstract', 0),
            'aigc_model_id': args.get('aigc_model_id', 0),
            'is_abstract_num': args.get('is_abstract_num', 999)
        }
        res = task.async_document_splitter_v2.delay(g.corpid, args['knowledge_id'], doc_info, slice_model, tp_user_id, request_ip,usr_prompt)

        return json_response(code='SUCCESS', data={'task_id': res.id})

class KnowledgeVideoFrameView(Resource):
    @staticmethod
    @login_check
    def post():
        """
        @summary: 视频帧处理
        """
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('doc_id', type=str, required=True, location='json')
        parser.add_argument('img_list', type=list, required=True, location='json')
        args = parser.parse_args()
        knowledge_id = args.get('knowledge_id', '')
        doc_id = args.get('doc_id', '')
        frame_list = args.get('img_list', [])
        if not all([knowledge_id, doc_id, frame_list]):
            return json_response(code='Error', data={'task_id': 0, 'message': "knowledge_id, doc_id, img_list 不能为空。"})

        # task 调用提取关键帧函数
        # res = task.async_video_frame_extraction.delay(g.corpid, knowledge_id, doc_id, frame_list)
        res = task.async_video_frame_extraction.delay(g.corpid, knowledge_id, doc_id, frame_list)
        return json_response(code='SUCCESS', data={'task_id': res.id})

    @staticmethod
    @login_check
    def put():
        """
        @summary: 视频帧处理进度
        """
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='json')
        args = parser.parse_args()
        res = celery.AsyncResult(args['task_id'])
        # chunk_list = list()
        has_fail = False
        if res and res.status == 'SUCCESS' and res.info.get('data'):
            has_fail = res.info.get('has_fail')
            current = res.info.get('current')
        else:
            current = 0
        # return json_response(code='SUCCESS', data={'state': res.state, 'has_fail': has_fail, 'current': current, 'doc_info': res.info, 'chunk_list': chunk_list})
        return json_response(code='SUCCESS', data={'state': res.state, 'has_fail': has_fail, 'current': current, 'doc_info': res.info})



class KnowledgeDocBatch(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='args')
        parser.add_argument('doc_id', type=str, required=True, location='args')
        args = parser.parse_args()
        data = KnowledgeController().get_knowledge_doc_chunk_list(args['doc_id'], {'is_all': 'all', 'fields': ['content']})
        return json_response(data=data)
class KnowledgeDocumentChunkView(Resource):

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('page_no', type=int, default=1, location='args')
        parser.add_argument('page_size', type=int, default=12, location='args')
        parser.add_argument('document_id', type=str, required=True, location='args')
        parser.add_argument('status', type=int, required=False, location='args')
        parser.add_argument('key_word', type=str, required=False, location='args')
        parser.add_argument('doc_url', type=str, required=False, location='args')
        args = parser.parse_args()

        data = KnowledgeController().get_knowledge_doc_chunk_list(args['document_id'], args)
        return json_response(data=data)

    @staticmethod
    @login_check
    def delete():
        parser = reqparse.RequestParser()
        parser.add_argument('chunk_id', type=str, required=True, location='json')
        args = parser.parse_args()
        chunk_list = []
        chunk_list.append(args['chunk_id'])
        new_chunk_id = KnowledgeController().get_document_chunk_id_by_chunk_source_id(args['chunk_id'])
        if new_chunk_id:
            chunk_list.append(str(new_chunk_id))
        for chunk_id in chunk_list:
            res = KnowledgeController().del_es_knowledge({'chunk_id': chunk_id})
            if res:
                chunk_id = KnowledgeController().del_knowledge_doc_chunk(chunk_id)
            else:
                return json_response(**StatusMap['delete_err'])

        return json_response(data={'chunk_id': chunk_id})

    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('chunk_id', type=str, required=True, location='json')
        parser.add_argument('status', type=int, required=True, choices=[0, 1], help='请选择启用或禁用', location='json')
        args = parser.parse_args()
        chunk_list = []
        chunk_list.append(args['chunk_id'])
        new_chunk_id = KnowledgeController().get_document_chunk_id_by_chunk_source_id(args['chunk_id'])
        if new_chunk_id:
            chunk_list.append(str(new_chunk_id))
        for chunk_id in chunk_list:
            chunk_id = KnowledgeController().update_knowledge_doc_chunk(chunk_id, {'status': args['status']})
            status = True if args['status'] == 1 else False
            KnowledgeController().update_es_knowledge({'chunk_id': chunk_id}, status)

        return json_response(data={'chunk_id': args['chunk_id']})

class KnowledgeDocChunkBulk(Resource):
    @staticmethod
    @login_check
    @action_log
    def delete():
        parser = reqparse.RequestParser()
        parser.add_argument('chunk_ids', type=list, required=True, location='json')
        parser.add_argument('doc_id', type=str, required=True)
        args = parser.parse_args()
        chunk_ids = args['chunk_ids']
        res = KnowledgeController().bulk_del_knowledge_chunk(args['doc_id'], chunk_ids)
        if not res:
            return json_response(**StatusMap['delete_err'])
        return json_response(code="SUCCESS", data={"chunk_ids": chunk_ids})


class KnowledgeModelView(Resource):

    @staticmethod
    @login_check
    def get():
        use_ranges = [x for x in str(request.pmsd.get('use_ranges', '3')).split(',') if x]
        data_list = AiModelController().get_ai_model_list({'use_ranges': use_ranges})
        return json_response(data={'data_list': data_list})


class KnowledgeEmbeddingsView(Resource):

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('chunk_ids', type=list, required=False, location='json', default=None)
        parser.add_argument('doc_ids', type=list, required=False, location='json')
        parser.add_argument('is_update', type=str, required=False, default="1", location='json')
        parser.add_argument('knlg_extract', type=int, required=False, default=0, location='json')
        args = parser.parse_args()
        record_info = KnowledgeController().get_knowledge_run_record({'knowledge_id': args['knowledge_id']})
        if record_info:
            args['task_id'] = record_info['task_id']

        is_update = args.get('is_update', "1")
        if is_update == "1":
            if args['chunk_ids'] is not None:
                # 更新es
                res = task.async_appoint_document_chunk_embeddings.delay(g.corpid, args['knowledge_id'], args['doc_ids'], args['chunk_ids'],args['knlg_extract'])      
            else:
                # 知识库下所有文档切片向量化
                res = task.async_document_chunk_embeddings.delay(g.corpid, args['knowledge_id'], args['task_id'],args['knlg_extract'])
            return json_response(data={'task_id': res.id})
        else: 
            if args['chunk_ids'] is not None:
                # 指定切片向量化 insert es

                # res = task.async_appoint_document_chunk_embeddings_v1.delay(g.corpid, args['knowledge_id'], args['doc_ids'], args['chunk_ids'],args['knlg_extract'])
                res = task.async_appoint_document_chunk_embeddings_coroutine.delay(g.corpid, args['knowledge_id'], args['doc_ids'], args['chunk_ids'],args['knlg_extract'])
                return json_response(data={'task_id': res.id})
            else:
                return json_response(data={'task_id': -1, 'message': "传值错误"})

    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='json')
        parser.add_argument('timeout', type=str, required=False, default="",location='json')

        args = parser.parse_args()
        res = celery.AsyncResult(args['task_id'])
        timeout = args.get("timeout", "")


        if res.info and 'data' in res.info:
            current = res.info.get('current')
            total = res.info.get('total')
            success = res.info.get('success')
        else:
            current = 0
            total = 0
            success = 0
        if timeout:
            if compare_time_with_time_minutes(timeout, 10) == 2:
                res.revoke(terminate=True, signal='SIGKILL')
                return json_response(code='Error',data={'state': res.state, 'current': current, 'doc_info': res.info, 'success': success, 'total': total}, message='向量化任务超时。')
            elif compare_time_with_time_minutes(timeout, 10) == 0:
                return json_response(code='Error',data={'state': res.state, 'current': current, 'doc_info': res.info, 'success': success, 'total': total}, message='timeout传值错误。')
            else:
                pass

        return json_response(code='SUCCESS',data={'state': res.state, 'current': current, 'doc_info': res.info, 'success': success, 'total': total})


class AsyncKnowledgeEmbeddingsView(Resource):

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('chunk_ids', type=list, required=False, location='json', default=None)
        parser.add_argument('doc_ids', type=list, required=False, location='json')
        args = parser.parse_args()
        record_info = KnowledgeController().get_knowledge_run_record({'knowledge_id': args['knowledge_id']})
        if record_info:
            args['task_id'] = record_info['task_id']

        # 指定切片向量化
        if args['chunk_ids'] is not None:
            res = task.async_appoint_document_chunk_embeddings.delay(g.corpid, args['knowledge_id'], args['doc_ids'], args['chunk_ids'])
        # 知识库下所有文档切片向量化
        else:
            res = task.async_document_chunk_embeddings.delay(g.corpid, args['knowledge_id'], args['task_id'])
        return json_response(data={'task_id': res.id})

    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='json')
        args = parser.parse_args()
        res = celery.AsyncResult(args['task_id'])
        
        if res.info:
            current = res.info.get('current')
            total = res.info.get('total')
            success = res.info.get('success')
        else:
            current = 0
            total = 0
            success = 0

        return json_response(code='SUCCESS',data={'state': res.state, 'current': current, 'doc_info': res.info, 'success': success, 'total': total})

class DocClearTaskView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True)
        parser.add_argument('doc_ids', type=list, required=True, location='json')
        parser.add_argument('chunk_ids', type=list, required=False, location='json')
        parser.add_argument('rule_ids', type=list, required=False, location='json')
        parser.add_argument('call_words', type=str, required=False)
        parser.add_argument('aigc_model_id', type=str, required=True)
        args = parser.parse_args()
        # args['aigc_model_id']= "45ef9d2b-c544-466f-8d24-d8ee5787ea83"

        res = task.async_document_chunk_clear.delay(g.corpid, args['aigc_model_id'], args['knowledge_id'],
                                                    args['doc_ids'], args['chunk_ids'], args['rule_ids'], args['call_words'])
        # for doc in args['doc_ids']:
        #     KnowledgeController().update_knowledge_doc(doc, {'current_node': '切片清洗','state':'进行中'})
        return json_response(data={'task_id': res.id})

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='args')
        args = parser.parse_args()
        res = celery.AsyncResult(args['task_id'])
        if res.info:
            current = res.info['current']
            total = res.info['total']
            success = res.info['success']
        else:
            current = 0
            total = 0
            success = 0
        return json_response(data={'state': res.state, 'current': current, 'doc_info': res.info, 'success': success, 'total': total})


class DocClearDataView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('data_id', type=str, required=True)
        parser.add_argument('content', type=str, required=True)
        args = parser.parse_args()
        res = RuleController().up_mg_clear_data({'_id': ObjectId(args['data_id'])}, {'content': args['content']})
        return json_response(data=res)

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='args')
        args = parser.parse_args()
        data = RuleController().get_mg_page_clear_data(args['task_id'])
        return json_response(data=data)

    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True)
        args = parser.parse_args()
        res = RuleController().mg_replace_sql_content(args['task_id'])
        return json_response(data=res)

    @staticmethod
    @login_check
    def delete():
        parser = reqparse.RequestParser()
        parser.add_argument('data_id', type=str, required=True)
        args = parser.parse_args()
        res = RuleController().del_mg_clear_data(ObjectId(args['data_id']))
        return json_response(data=res)


class KnowledgeRunView(Resource):

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='args')
        args = parser.parse_args()

        run_record = KnowledgeController().get_knowledge_run_record({'knowledge_id': args['knowledge_id']})

        if run_record:
            doc_data = run_record['doc_data']

            doc_ids = list()
            for doc_id, status in doc_data.items():
                doc_ids.append(doc_id)
            doc_list = KnowledgeController().get_knowledge_doc_list(args['knowledge_id'], {'doc_ids': doc_ids, 'is_all': 'all'})
            for doc in doc_list['data_list']:
                doc['chunk_list'] = KnowledgeController().get_knowledge_doc_chunk_list(doc['document_id'], {'is_all': 'all'})

            data_list = doc_list.get('data_list',[])
            chunk_status = run_record.get('chunk_status')
            embedding_status = run_record.get('embedding_status')
            embedding_current = run_record.get('embedding_current')
            embedding_total = run_record.get('embedding_total')
            embedding_success = run_record.get('embedding_success')


            return json_response(data={'data_list': data_list, 'chunk_status': chunk_status,
                                       'embedding_status': embedding_status, 'current': embedding_current,
                                       'total': embedding_total, 'success': embedding_success})
        else:
            return json_response(data={'data_list': []})


class KnowledgeSearchView(Resource):

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('input_text', type=str, required=True, location='json')
        parser.add_argument('knowledge_ids', type=list, required=True, location='json')
        args = parser.parse_args()
        res = KnowledgeController().search_es_knowledge(task, args['input_text'], args['knowledge_ids'], is_search=True)
        return json_response(data=res)

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('input_text', type=str, required=True, location='json')
        parser.add_argument('knowledge_ids', type=list, required=True, location='json')
        parser.add_argument('min_score', type=float, required=False, default=1.50, location='json')
        parser.add_argument('top_k', type=int, required=False, default=5, location='json')

        args = parser.parse_args()
        knowledge_ids = args['knowledge_ids']
        min_score = args['min_score']
        top_k = args['top_k']

        controller = KnowledgeController()

        embedding_search_map = {"knowledge_list": [], "content_ids": []}
        embedding_knowledge_code = 1

        def build_search_map(hits, max_score, search_map, knowledge_code_counter):
            knowledge_dict = {}
            for item in hits:
                knowledge_id = item['_source']['knowledge_id']
                doc_id = item['_source']['doc_id']
                # check knowledge_id & doc_id
                knowledge_detail = controller.get_knowledge_detail(knowledge_id)
                if not knowledge_detail:
                    logger.info(f"Knowledge_id {knowledge_id} not found in DB, skipping this record.")
                    continue

                doc_info = controller.get_doc_info(doc_id)
                if not doc_info:
                    logger.info(f"Doc_id {doc_id} not found in DB, skipping this record.")
                    continue
                chunk_id = item['_source']['chunk_id']
                highlight_description = item.get('highlight', {}).get('description', [item['_source']['description']])[
                    0]
                score = item['_score']
                match_score = controller.calculate_match_score(score, max_score=max_score)

                if knowledge_id not in knowledge_dict:
                    knowledge_dict[knowledge_id] = {}

                if doc_id not in knowledge_dict[knowledge_id]:
                    doc_info = controller.get_doc_info(doc_id)
                    knowledge_dict[knowledge_id][doc_id] = doc_info
                    knowledge_dict[knowledge_id][doc_id]['chunk_list'] = []

                knowledge_dict[knowledge_id][doc_id]['chunk_list'].append({
                    'chunk_id': chunk_id,
                    'doc_id': doc_id,
                    'knowledge_id': knowledge_id,
                    'content': highlight_description,
                    'score': score,
                    'match_score': match_score,
                })

            # 构建最终的 search_map 结构
            for knowledge_id, doc_dict in knowledge_dict.items():
                doc_list = list(doc_dict.values())
                search_map['knowledge_list'].append({
                    'knowledge_id': knowledge_id,
                    'knowledge_name': controller.get_knowledge_detail_v0(knowledge_id)['knowledge_name'],
                    'knowledge_code': knowledge_code_counter,
                    'hit_doc_counts': len(doc_list),
                    'doc_list': doc_list,
                })
                knowledge_code_counter += 1

            return knowledge_code_counter

        knowledge_ids_group_by_model = controller.get_knowledge_ids_by_model(knowledge_ids)
        logger.info(f"knowledge_ids_group_by_model: {knowledge_ids_group_by_model}")
        for in_knowledge_ids in knowledge_ids_group_by_model:
            vector_knowledge_hits = controller.search_es_knowledge(task, args['input_text'], in_knowledge_ids,
                                                                   size=top_k, min_score=min_score, is_search=True)
            embedding_knowledge_code = build_search_map(vector_knowledge_hits, max_score=2.0,
                                                        search_map=embedding_search_map,
                                                        knowledge_code_counter=embedding_knowledge_code)

        if not embedding_search_map['knowledge_list']:
            embedding_search_map = {}

        data = {
            "input_knowledge_list": knowledge_ids,
            "embedding_search_map": embedding_search_map
        }

        return json_response(data=data)


class ContentRelKnowledgeVew(Resource):

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='args')
        parser.add_argument('content_id', type=str, required=True, location='args')
        args = parser.parse_args()
        knowledge_controller = KnowledgeController()
        if knowledge_controller.check_rel_content_knowledge(args['content_id'], args['knowledge_id']) > 0:
            is_update = True
            msg = '当前内容已经关联这个知识库了,是否要进行更新'
        else:
            is_update = False
            msg = ''
        return json_response(data={"is_update": is_update}, message=msg)

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('content_id', type=str, required=True, location='json')
        args = parser.parse_args()
        knowledge_controller = KnowledgeController()
        if knowledge_controller.check_rel_content_knowledge(args['content_id'], args['knowledge_id']) > 0:
            qp = {'knowledge_id': args['knowledge_id'], 'content_id': args['content_id']}
            knowledge_controller.del_es_knowledge(qp)
            knowledge_controller.del_knowledge_doc_by_qp(qp)
        else:
            knowledge_controller.update_rel_content_knowledge(args['content_id'], args['knowledge_id'], request.user['tp_user_id'])

        content_name, content, content_list = knowledge_controller.get_content_info(args['content_id'])
        if content_list:
            content_list = json.loads(content_list)
        else:
            content_list = list()
        content = {
            'doc_name': content_name,
            'doc_type': 'text',
            'doc_url': None,
            'doc_size': 0,
            'content': content,
            'character_count': len(content),
            'content_id': args['content_id']

        }
        doc_list = list()
        doc_list.append(content)
        for content in content_list:
            if content['url'].split('.')[-1] != 'mp4':
                if 'M' in content['size']:
                    content['doc_size'] = float(content['size'].replace('MB', '').replace('M', '')) * 1024 * 1024
                elif 'K' in content['size']:
                    content['doc_size'] = float(content['size'].replace('KB', '').replace('K', '')) * 1024
                else:
                    content['doc_size'] = 0
                doc_list.append({
                    'doc_name': content['name'],
                    'doc_type': content['url'].split('.')[-1],
                    'doc_url': content['url'],
                    'doc_size': content['doc_size'],
                    'content_id': args['content_id']
                })

        for doc in doc_list:
            doc['doc_id'] = knowledge_controller.add_knowledge_doc(args['knowledge_id'], doc['doc_name'], doc['doc_type'],
                                                                   doc['doc_url'], doc['doc_size'], 'TODO', content_id=args['content_id'])

        doc_info = {
            'chunk_size': 256,
            'chunk_overlap': 20,
            'doc_list': doc_list
        }
        if len(doc_list) <= 0:
            return  json_response(code='FAIL', data={'message': '文档数量不能为0'})
        res = task.async_document_splitter.delay(g.corpid, args['knowledge_id'], doc_info)

        return json_response(code='SUCCESS', data={'task_id': res.id})


class KnowledgeDocDetailView(Resource):
    @staticmethod
    def get_doc_content_and_prompt(data_type, document_id, key_words=''):
        content = ''
        message = ''
        chunk_list = KnowledgeController().get_knowledge_doc_chunk_list(document_id, {'is_all': 'all'})
        for item in chunk_list['data_list']:
            content += item['content']
        if content:
            # 摘要提取
            if data_type == 'abstract':
                message = f"""
                请从以下文本中总结和提取摘要，返回结果不超过100字
                文本: {content}
                - 仅返回结果数据
                """
            # 标签提取
            elif data_type == 'tag':
                message = f"""
                请从以下文本中总结和提取10个标签，以','分割
                文本: {content}
                - 仅返回结果数据
                """
            elif data_type == 'qa':
                message = f"""
                给定以下文本和关键词，根据指定关键词语义相关的范围从内容中提取问题和答案(没有指定关键词则从全文中提取),再从答案中取出问题要点/关键词(多个词用、连接)，并将它们组织成多个结构化的问答对。每个问答对应该清晰地标识出来，并使用以下格式(每个问答对用"$delimiter$"拼接)：

                问题: <问题>
                答案: <答案>
                问题要点: <答案关键词1、答案关键词2、 ...>

                * 文本:
                ```{content}```

                关键词：```{key_words}```

                * 示例:
                问题: 什么是人工智能？
                答案: 人工智能是指由人工制造的智能。
                问题要点: 人工智能、智能
                $delimiter$
                问题: 人工智能的应用领域有哪些？
                答案: 人工智能的应用领域有医疗、金融、教育等。
                问题要点: 人工智能、应用领域
                ...

                * 要求：
                - 每一组问答对之间必须用$delimiter$分隔；
                - 仅返回符合指定关键词语义相关的问题和答案以及要点，参考示例，不要返回其他内容；
                - 问题中要有明确的上下文信息，不能是单独的问题；
                - 没有找到返回""。
                """
            # 数据图谱
            elif data_type == 'main_points':
                message = f"""
                请从以下文本中提取十个要点,并以','分割
                文本: {content}
                - 仅返回结果数据
                """
            elif data_type == 'key_words':
                message = f"""
                请从以下文本中提取十个关键词,并以','分割
                文本: {content}
                - 仅返回结果数据
                """
            elif data_type == 'mind_map':
                message = f"""
                使用Mermaid语法生成下文的脑图[
                上下文信息: {content}
                ]
                - 仅返回结果数据
                """
            else:
                message = f"""
                请从以下文本中提取实体、关系和属性，并将它们组织成一个结构化的图谱。每个实体和关系应该清晰地标识出来，并使用以下格式：
    
                实体: <实体名称>
                属性: <属性名称> = <属性值>
                关系: <实体1> --[<关系名称>]--> <实体2>
    
                文本:
                {content}
    
                - 仅返回实体、关系和属性；没有属性则不需要返回属性；没有关系则不需要返回关系。
                """
        return {'content': content, 'message': message}
    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('document_id', type=str, required=True, location='json')
        parser.add_argument('doc_name', type=str, required=False, location='json')
        parser.add_argument('abstract', type=str, required=False, location='json')
        parser.add_argument('description', type=str, required=False, location='json')
        parser.add_argument('graph_content', type=str, required=False, location='json')
        parser.add_argument('content_time', type=str, required=False)
        args = parser.parse_args()
        # logger.info(args)
        update_data = {}
        doc_list = []
        doc_list.append(args['document_id'])
        new_doc_id = KnowledgeController().get_knowledge_document_detail_by_doc_source_id(args['document_id'])
        if new_doc_id:
            doc_list.append(new_doc_id)
        for doc_id in doc_list:
            doc_info = KnowledgeController().get_knowledge_document_detail(doc_id)
            if doc_info:
                if doc_info['doc_name'] != args['doc_name']:
                    update_data['doc_name'] = args['doc_name']
                if doc_info['abstract'] != args['abstract']:
                    update_data['abstract'] = args['abstract']
                if doc_info['description'] != args['description']:
                    update_data['description'] = args['description']
                if doc_info['graph_content'] != args['graph_content']:
                    update_data['graph_content'] = args['graph_content']
                if args['content_time'] and (args['content_time'] != doc_info['content_time']):
                    try:
                        update_data['content_time'] = str_to_date(args['content_time'])
                    except:
                        pass
                if update_data:
                    KnowledgeController().edit_doc_info(doc_info['knowledge_id'], doc_id, update_data)

        return json_response(data={"document_id": args['document_id']})

    @staticmethod
    @login_check
    def post():

        parser = reqparse.RequestParser()
        parser.add_argument('document_id', type=str, required=True, location='json')
        parser.add_argument('aigc_model_id', type=str, required=True, location='json')
        parser.add_argument('data_type', type=str, required=False, location='json')
        parser.add_argument('is_stream', type=str, required=False, location='json')
        parser.add_argument('is_update', type=str, required=False, location='json')
        parser.add_argument('tokens', type=int, required=False, location='json')
        args = parser.parse_args()

        model_info = AppController().get_model_info(args['aigc_model_id'])
        
        data_type = args['data_type']
        is_stream = args['is_stream']
        is_update = args['is_update']
        tokens = args['tokens']
        # logger.info(chunk_list['data_list'])
        content_and_prompt_dict = KnowledgeDocDetailView.get_doc_content_and_prompt(data_type, args['document_id'])
        content = content_and_prompt_dict.get('content', "")
        message = content_and_prompt_dict.get('message', "")
        controller = KnowledgeController()
        @stream_with_context
        def generate():

            if message:
                for item, reasoning_content in AppController().ai_app_chat(message, [], model_info['aigc_type_id'], model_info['model_path'], model_id=model_info['aigc_model_id']):
                    data = json.dumps(
                        {'content': item or '', 'is_send': False, 'role': 'assistant', 'type': 'message', 'reasoning_content': reasoning_content},
                        ensure_ascii=False)
                    yield f"data: {data}\n\n"

            data = json.dumps(
                {'content': '', 'is_send': True, 'role': 'assistant', 'type': 'message'}, ensure_ascii=False)
            yield f"data: {data}\n\n"

        if is_stream == 'false':
            # 检查边界条件：content 为空或 tokens 为空或 tokens 非正数
            if content and isinstance(tokens, int) and tokens > 0:
                # 按照 tokens 的值截取字符串
                truncated_content = content[:tokens]
            else:
                # 不符合条件则不截断
                truncated_content = content

            # 使用截断后的内容生成 message
            message = f"""
            请从以下文本中提取实体、关系和属性，并将它们组织成一个结构化的图谱。每个实体和关系应该清晰地标识出来，并使用以下格式：

            实体: <实体名称>
            属性: <属性名称> = <属性值>
            关系: <实体1> --[<关系名称>]--> <实体2>

            文本:
            {truncated_content}

            - 仅返回实体、关系和属性；没有属性则不需要返回属性；没有关系则不需要返回关系。
            """
            result = []
            for item,reasoning_content in AppController().ai_app_chat(message, [], model_info['aigc_type_id'], model_info['model_path'], model_id=model_info['aigc_model_id']):
                if item is not None:  # 添加这个判断
                    result.append(item)

            # 然后将result拼接成字符串
            full_result = ''.join(result)
            doc_info = KnowledgeController().get_knowledge_document_detail(args['document_id'])
            update_data = {}
            if doc_info:
                if doc_info['graph_content'] != full_result:
                    update_data['graph_content'] = full_result
                if update_data:
                    KnowledgeController().edit_doc_info(doc_info['knowledge_id'], args['document_id'], update_data)
            return json_response(data={"document_id": args['document_id']})

        if is_update == 'false':
            app_controller = AppController()
            gen_content = controller.generate_tag_abstract_graph(message, args.get('document_id', ""),
                                                                 args.get('data_type', "graph"),
                                                                 model_info['aigc_type_id'], model_info['model_path'],
                                                                 app_controller)
            return json_response(data={"document_id": args['document_id'], "content": gen_content})

        return Response(generate(), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'
        })

class ExtractQaFromKnowledge(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_ids', type=str, required=True, location='json')
        parser.add_argument('aigc_model_id', type=str, required=True, location='json')
        parser.add_argument('key_words', type=str, required=False, location='json')
        args = parser.parse_args()
        key_words = args.get('key_words', '')
        document_list = []
        extract_controller = QaExtractController()
        # TODO: 移到controller
        for knowledge_id in args['knowledge_ids'].split(','):
            search_list = KnowledgeController().get_knowledge_doc_list(knowledge_id, {'is_all': 'all'}).get('data_list', [])
            document_list += [{**doc, 'knowledge_id': knowledge_id} for doc in search_list]

        model_info = AppController().get_model_info(args['aigc_model_id'])
        
        extract_result = []

        document_list = extract_controller.pre_handle_document_list(document_list, {'knowledge_ids': args['knowledge_ids'].split(','), "key_words": key_words})

        # 知识库下的已抽取文档列表
     
        for doc in document_list:
            try:
                full_result = ""
                if not doc.get('extracted', False): 
                    full_result = ExtractQaFromKnowledge.extract_single_doc(doc['document_id'], model_info, key_words)
                else:
                    full_result = doc.get('qa_content', '')
                extract_result.append({'document_id': doc['document_id'],
                                       'knowledge_id': doc.get('knowledge_id', ''), 
                                       'doc_name': doc.get('doc_name', ""), 
                                       'qa_list': ExtractQaFromKnowledge.split_qa(full_result), 
                                       'status': 1,
                                       'message': '提取成功',
                                       'extract_id': doc.get('extract_id', ''),
                                       "extracted": doc.get('extracted', False)})
            except Exception as e:
                logger.error(f"extract_single_doc error: {e}")
                extract_result.append({'document_id': doc['document_id'],  
                                       'knowledge_id': doc.get('knowledge_id', ''),
                                       'doc_name': doc.get('doc_name', ""), 
                                       'qa_list': [], 
                                       "extracted": doc.get('extracted', False),
                                       'extract_id': doc.get('extract_id', ''),
                                       'message': '提取失败: ' + str(e)})
                continue
     
        new_extract_dict = extract_controller.batch_add_extract([
            {
                'document_id': item.get('document_id', ''),
                'knowledge_id': item.get('knowledge_id', ''),
                'qa_content': ExtractQaFromKnowledge.concat_qa(item.get('qa_list', [])),
                'key_words': key_words,
            } for item in extract_result if item.get('extracted', False) == False
        ]) 
        for item in extract_result:
            if item.get('extracted', False) == False:
                item['extract_id'] = new_extract_dict.get(item.get('document_id', ''), '')    
        # filter_extract_result = [item for item in extract_result if item.get('extracted', False) == False]
        return json_response(data={'extract_result': extract_result})
        # return json_response(data={'extract_result': filter_extract_result})

    @staticmethod
    @login_check
    def put():
        """
        修改qa_list
        """
        parser = reqparse.RequestParser()
        parser.add_argument('extract_result', type=list, required=True, location='json')
        args = parser.parse_args()
        extract_result = args.get('extract_result', '')
        if not extract_result:
            return json_response(data={'message': 'extract_result不能为空'})
        for extract in extract_result:
            extract_id = extract.get('extract_id', '')
            qa_list = extract.get('qa_list', [])
            qa_str = ExtractQaFromKnowledge.concat_qa(qa_list)
            extract_controller = QaExtractController()
            extract_controller.update_qa_list(extract_id, qa_str)
        return json_response(data={'message': '更新成功'})


    @staticmethod
    def concat_qa(qa_list):
        qa_str = ''
        for qa in qa_list:
            qa_str += f"问题: {qa['question']}答案: {qa['answer']}问题要点: {qa['answer_keywords']}$delimiter$"
        return qa_str
    
    @staticmethod
    def split_qa(full_result):
        qa_list = []
        # 利用正则表达式提取问题和答案
        logger.info(full_result)
        full_result = full_result.replace('\n', '')
        for item in full_result.split('$delimiter$'):
            result = re.findall('问题: (.*?)答案: (.*?)问题要点: (.+)', item)
            if result:
                find = result[0]
                if find:
                    qa_list.append({'question': find[0], 'answer': find[1], 'answer_keywords': find[2]})
            else:
                logger.info(f"split_qa error: {item}")
                # qa_list.append({'question': result[0], 'answer': result[1], 'answer_keywords': result[2]})
        # handle_qa_list = map(lambda x: {'question': x[0], 'answer': x[1], 'answer_keywords': x[2]}, qa_list)
        return qa_list
    
    @staticmethod
    def extract_single_doc(document_id, model_info,key_words=''):
        content_and_prompt_dict = KnowledgeDocDetailView.get_doc_content_and_prompt('qa', document_id,key_words)
        content = content_and_prompt_dict.get('content', "")
        message = content_and_prompt_dict.get('message', "")
        result = []
        for item, reasoning_content in AppController().ai_app_chat(message, [], model_info['aigc_type_id'], model_info['model_path'], model_id=model_info['aigc_model_id']):
            if item is not None:  # 添加这个判断
                result.append(item)
        full_result = ''.join(result)
        return full_result

class KnowledgeDocumentChunkEditView(Resource):

    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('doc_id', type=str, required=True, location='json')
        parser.add_argument('chunk_id', type=str, required=True, location='json')
        parser.add_argument('content', type=str, required=True, location='json')
        parser.add_argument('chunk_img_list', type=list, required=False, location='json', default=[])
        args = parser.parse_args()
        knowledge_controller = KnowledgeController()
        chunk_list = []
        chunk_list.append(args['chunk_id'])
        new_chunk_id = knowledge_controller.get_document_chunk_id_by_chunk_source_id(args['chunk_id'])
        if new_chunk_id:
            chunk_list.append(str(new_chunk_id))
        print(f"chunk_list: {chunk_list}")
        for chunk_id in chunk_list:
            tokens = knowledge_controller.update_es_knowledge_by_chunk({'chunk_id': chunk_id}, args['content'], len(args['content']))
            if tokens:
                knowledge_controller.update_knowledge_doc_chunk(chunk_id,
                                                                {'content': args['content'], 'character_count': len(args['content']),
                                                                'tokens': tokens})
            chunk_img_list = args['chunk_img_list']
            knowledge_controller.del_chunk_image_by_chunk_id(chunk_id)
            if chunk_img_list:
                knowledge_controller.add_chunk_images(chunk_id, chunk_img_list)

        return json_response(data={"chunk_id": args['chunk_id']})

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('doc_id', type=str, required=True, location='json')
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('content', type=str, required=True, location='json')
        parser.add_argument('chunk_img_list', type=list, required=False, location='json', default=[])

        args = parser.parse_args()
        knowledge_controller = KnowledgeController()
        new_doc_id = knowledge_controller.get_knowledge_document_detail_by_doc_source_id(args['doc_id'])
        chunk_id = knowledge_controller.add_knowledge_doc_chunk(args['doc_id'], args['content'], len(args['content']), 'FINISHED')
        knowledge_controller.generate_doc_chunk(args['content'], args['knowledge_id'], args['doc_id'], chunk_id)
        chunk_img_list = args['chunk_img_list']
        if chunk_img_list:
            knowledge_controller.del_chunk_image_by_chunk_id(chunk_id)
            knowledge_controller.add_chunk_images(chunk_id, chunk_img_list)
        if new_doc_id:
            new_chunk_id = knowledge_controller.add_knowledge_doc_chunk(new_doc_id, args['content'], len(args['content']), 'FINISHED')
            knowledge_controller.generate_doc_chunk(args['content'], args['knowledge_id'], new_doc_id, new_chunk_id)
            chunk_img_list = args['chunk_img_list']
            knowledge_controller.update_knowledge_doc_chunk(new_chunk_id, {'source_chunk_id': chunk_id})
            if chunk_img_list:
                knowledge_controller.del_chunk_image_by_chunk_id(new_chunk_id)
                knowledge_controller.add_chunk_images(new_chunk_id, chunk_img_list)
        return json_response(data={"chunk_id": chunk_id})

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('chunk_id', type=str, required=True, location='args')
        args = parser.parse_args()
        chunk_info = KnowledgeController().get_knowledge_doc_chunk_detail(args['chunk_id'])
        if chunk_info:
            chunk_info['chunk_img_list'] = KnowledgeController().get_chunk_images(args['chunk_id'])
        return json_response(data=chunk_info)


class KnowledgeLifeView(Resource):

    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('data_id', type=str, required=True, help='数据id')
        parser.add_argument('data_type', type=str, required=True, help='doc或者kno')
        parser.add_argument('open_life', type=int, required=True)
        args = parser.parse_args()
        action = RuleController()
        res = action.up_data_life_rule_status(args['data_id'], args['data_type'], args['open_life'])
        return json_response(data=res)

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, help='知识库id')
        parser.add_argument('rule_list', type=list, required=True, help='规则列表', location='json')
        args = parser.parse_args()
        action = RuleController()
        res = action.up_knowledge_life_rule(args['knowledge_id'], args['rule_list'])
        return json_response(data=res)

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, help='知识库id', location='args')
        args = parser.parse_args()
        res = RuleController().get_knowledge_life_rule(args['knowledge_id'])
        return json_response(data=res)


class DocLifeView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('doc_id', type=str, required=True, help='文档id')
        parser.add_argument('life_type', type=str, required=True, help='周期类型')
        parser.add_argument('expire_date', type=str, required=False)
        parser.add_argument('life_days', type=int, required=False)
        args = parser.parse_args()
        res = RuleController().up_dic_life_rule(args['doc_id'], args['life_type'], args['expire_date'], args['life_days'])
        return json_response(data=res)

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('doc_id', type=str, required=True, help='文档id', location='args')
        args = parser.parse_args()
        res = RuleController().get_doc_life_rule(args['doc_id'])
        return json_response(data=res)


class PrepareClearRule(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('key_type', type=str, required=False, location='args')
        parser.add_argument('source', type=str, required=False, location='args', default='custom')
        parser.add_argument('name', type=str, required=False, location='args')
        parser.add_argument('page_size', type=int, required=False, location='args', default=10)
        parser.add_argument('page_no', type=int, required=False, location='args', default=1)
        args = parser.parse_args()
        res = RuleController().page_clear_rule_list(**args)
        return json_response(data=res)

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('id', type=int, required=False, help='数据id')
        parser.add_argument('name', type=str, required=False, help='名称', default='自定义规则')
        parser.add_argument('desc', type=str, required=False, help='描述')
        parser.add_argument('content', type=str, required=True, help='内容')
        parser.add_argument('key_type', type=str, required=True, help='类型分类', default='script')
        parser.add_argument('source', type=str, required=True, help='数据源', default='custom')
        args = parser.parse_args()
        args['tp_user_id'] = request.user['tp_user_id']
        args['type'] = args.pop('key_type')
        res = RuleController().add_clear_rule(args)
        return json_response(data=res)

    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('id', type=int, required=False, help='数据id')
        parser.add_argument('status', type=int, required=False, help='状态id')
        args = parser.parse_args()
        res = RuleController().up_clear_rule_data(args['id'], {'status': args['status']})
        return json_response(data=res)

    @staticmethod
    @login_check
    def delete():
        parser = reqparse.RequestParser()
        parser.add_argument('id', type=int, required=False, help='数据id')
        args = parser.parse_args()
        res = RuleController().up_clear_rule_data(args['id'], {'delete_flag': settings.DELETE_FLAG_TRUE})
        return json_response(data=res)


class KnowledgeRelClearRule(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='args')
        args = parser.parse_args()
        res = RuleController().get_knowledge_clear_rule(args['knowledge_id'])
        return json_response(data=res)

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=int, required=True, help='知识库id')
        parser.add_argument('rule_json', type=dict, required=True, help='规则列表')
        args = parser.parse_args()
        args['tp_user_id'] = request.user['tp_user_id']
        res = RuleController().up_knowledge_rel_clear_rule(args['knowledge_id'], args['rule_json'])
        return json_response(data=res)


class MinIOUploadView(Resource):

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        # parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('relative_path', type=str, required=True, location='json')
        parser.add_argument('oss_names_array', type=list, required=True, location='json')

        args = parser.parse_args()
        relative_path = args['relative_path']
        oss_names_array = args['oss_names_array'] if args['oss_names_array'] else []

        logger.info(f"=" * 25)
        logger.info(f"relative_path: {relative_path}, \noss_names_array: {oss_names_array}, {type(oss_names_array)}")
        logger.info(f"=" * 25)

        knowledge_controller = KnowledgeController()
        url_list = knowledge_controller.get_minio_urls(relative_path, oss_names_array)
        return json_response(data={'data_list': url_list})


class AppQaSearchView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('message', type=str, location='json', help='消息', required=True)
        parser.add_argument('choice_app_id', type=str, required=True, default=False)
        parser.add_argument('threshold', type=float, required=False, default=1.9)

        args = parser.parse_args()
        question_str = args['message']
        controller = AppController()
        ret = controller.search_es_gt_demo_qa(question_str, min_score=args['threshold'], is_json=True, app_id=args['choice_app_id'])
        return json_response(data={
            'answer': ret.get('answer', ''),
            'question': ret.get('question', question_str),
            'score': ret.get('score', 0.0)
        })


class KnowledgeDocumentUpdate(Resource):

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='args')
        parser.add_argument('start_time', type=str, required=False, location='args')
        parser.add_argument('end_time', type=str, required=False, location='args')
        parser.add_argument('page_no', type=int, default=1, location='args')
        parser.add_argument('page_size', type=int, default=20, location='args')
        parser.add_argument('doc_name', type=str,  required=False, location='args')
        parser.add_argument('state', type=str,  required=False, location='args')
        args = parser.parse_args()
        knowledge_id = args['knowledge_id']
        result_documents = KnowledgeController().get_knowledge_documents_by_date(knowledge_id, args)
        # 返回筛选后的结果
        return json_response(data=result_documents)

    @staticmethod
    @login_check
    def delete():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True)
        parser.add_argument('start_time', type=str, required=False)
        parser.add_argument('end_time', type=str, required=False)
        parser.add_argument('time_chose', type=str, required=False)
        parser.add_argument('key_chose', type=str, required=True)
        parser.add_argument('doc_ids', type=list, required=False, location='json')
        args = parser.parse_args()
        knowledge_id = args['knowledge_id']
        key_chose = args['key_chose']
        doc_ids = args.get('doc_ids')
        if key_chose == "date_range":
            result_documents = KnowledgeController().delete_knowledge_documents_by_date(knowledge_id, args)
            return json_response(data=result_documents)
        if key_chose == "doc_id":
            for doc_id in doc_ids:
                # 根据doc_id获取source_type
                new_doc_id = KnowledgeController().get_knowledge_document_detail_by_doc_source_id(doc_id)
                if new_doc_id:
                    doc_ids.append(str(new_doc_id))
                logger.info(f"doc_ids: {doc_ids}")
            result_documents = KnowledgeController().delete_knowledge_documents_by_doc_ids(knowledge_id,doc_ids)
            return json_response(data=result_documents)

class GTKnowledgeView(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('page_no', type=int, default=1, location='args')
        parser.add_argument('page_size', type=int, default=10, location='args')
        parser.add_argument('knowledge_name', type=str, required=False, location='args')
        parser.add_argument('type', type=int, required=False, location='args', default=1)
        args = parser.parse_args()
        tp_user_id = request.user['tp_user_id']
        args['tp_user_id'] = tp_user_id
        user_info = TPUserController().user_info(tp_user_id)
        gtid = user_info.get('ww_user_id')
        args['gtid'] = gtid
        if gtid:
            all_user_auths = SchemeController().get_user_auths(gtid)
            all_scheme_ids = [x['scheme_id'] for x in all_user_auths]
            if not all_scheme_ids:
                return json_response(data=[])
        else:
            all_scheme_ids = None
        args['all_scheme_ids'] = all_scheme_ids
        data = KnowledgeController().openapi_gt_knowledge_list(args)
        return json_response(data=data)

class KnowledgeTreeView(Resource):
    @staticmethod
    @login_check
    # 获取知识树
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('parent_directory_id', type=str, required=False, location='args')
        parser.add_argument('page_no', type=int, default=1, location='args')
        parser.add_argument('page_size', type=int, default=10, location='args')
        parser.add_argument('directory_name', type=str, required=False, location='args')
        parser.add_argument('directory_id', type=str, required=False, location='args')
        parser.add_argument('knowledge_name', type=str, required=False, location='args')

        args = parser.parse_args()
        data = KnowledgeController().get_knowledge_tree(args)
        return json_response(data=data)
    
    @staticmethod
    @login_check
    # 修改知识树
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('directory_description', type=str, required=False, location='json')
        parser.add_argument('directory_name', type=str, required=True, location='json')
        parser.add_argument('directory_id', type=str, required=False, location='json')
        parser.add_argument('parent_directory_id', type=str, required=False, location='json')
        args = parser.parse_args()
        tp_user_id = request.user['tp_user_id']
        args['tp_user_id'] = tp_user_id
        data = KnowledgeController().add_knowledge_tree(args)
        return json_response(data=data)
    
    @staticmethod
    @login_check
    # 删除知识树
    def delete():
        parser = reqparse.RequestParser()
        parser.add_argument('directory_id', type=str, required=True, location='json')
        args = parser.parse_args()
        data = KnowledgeController().delete_knowledge_tree(args)
        return json_response(data=data)


class KnowledgeGraphView(Resource):
    @staticmethod
    @login_check
    def get(action):
        if action == 'history':
            parser = reqparse.RequestParser()
            parser.add_argument('knowledge_id', type=str, required=True, location='args')
            knowledge_controller = KnowledgeController()
            args = parser.parse_args()
            knowledge_id = args.get('knowledge_id')
            data = knowledge_controller.get_knowledge_graph_detail(knowledge_id)
            return json_response(data={'data': data})

        if action == 'query':
            parser = reqparse.RequestParser()
            parser.add_argument('knowledge_id', type=str, required=True, location='args')
            parser.add_argument('limit', type=int, default=300, required=False, location='args')
            args = parser.parse_args()
            knowledge_id = args.get('knowledge_id')
            limit = args.get('limit')
            storage_namespace = SPACE_PREFIX + str(knowledge_id)
            rag = GraphRag(corp_id="", namespace=storage_namespace).get_instance()
            # rag = GraphRag(corp_id="", namespace=knowledge_id).get_instance()
            data = asyncio.run(rag.chunk_entity_relation_graph.get_data(limit))
            return json_response(data={'data': data})

        if action == 'expand':
            parser = reqparse.RequestParser()
            parser.add_argument('knowledge_id', type=str, required=True, location='args')
            parser.add_argument('node_id', type=str, required=True, location='args')
            parser.add_argument('limit', type=int, default=300, required=False, location='args')
            args = parser.parse_args()
            knowledge_id = args.get('knowledge_id')
            node_id = args.get('node_id')
            limit = args.get('limit')
            storage_namespace = SPACE_PREFIX + str(knowledge_id)
            rag = GraphRag(corp_id="", namespace=storage_namespace).get_instance()
            # rag = GraphRag(corp_id="", namespace=knowledge_id).get_instance()
            data = asyncio.run(rag.chunk_entity_relation_graph.get_expand(node_id, limit))
            return json_response(data={'data': data})

        if action == 'search':
            parser = reqparse.RequestParser()
            parser.add_argument('knowledge_id', type=str, required=True, location='args')
            parser.add_argument('prop_type', type=str, required=False, location='args')
            parser.add_argument('keywords', type=str, required=True, location='args')
            args = parser.parse_args()
            knowledge_id = args.get('knowledge_id')
            prop_type = args.get('prop_type')
            keywords = args.get('keywords')
            storage_namespace = SPACE_PREFIX + str(knowledge_id)
            rag = GraphRag(corp_id="", namespace=storage_namespace).get_instance()
            # rag = GraphRag(corp_id="", namespace=knowledge_id).get_instance()
            data = asyncio.run(rag.chunk_entity_relation_graph.get_data_search(prop_type=prop_type, keywords=keywords))
            return json_response(data={'data': data})

        if action == 'check':
            parser = reqparse.RequestParser()
            parser.add_argument('knowledge_id', type=str, required=True, location='args')
            args = parser.parse_args()
            knowledge_id = args.get('knowledge_id')
            storage_namespace = SPACE_PREFIX + str(knowledge_id)
            rag = GraphRag(corp_id="", namespace=storage_namespace).get_instance()
            _, _, exceed_limit_flag = rag.process_chunks(knowledge_id)
            print(f"exceed_limit_flag is {exceed_limit_flag} for knowledge_id: {knowledge_id}")
            return json_response(data={'data': exceed_limit_flag})


    @staticmethod
    @login_check
    def post(action):
        if action == 'build':
            parser = reqparse.RequestParser()
            parser.add_argument('knowledge_id', type=str, required=True, location='args')
            args = parser.parse_args()
            knowledge_id = args.get('knowledge_id')
            corp_id = TEMP_CORP_ID
            tp_user_name = request.user['tp_user_name']
            task.build_knowledge_graph.delay(corp_id, knowledge_id, tp_user_name)

            data = {'status': 'started', 'message': 'Graph building started.'}
            return json_response(data={'data': data})

            # parser = reqparse.RequestParser()
            # parser.add_argument('knowledge_id', type=str, required=True, location='args')
            # args = parser.parse_args()
            # knowledge_id = args.get('knowledge_id')
            # corp_id = TEMP_CORP_ID  # fixme
            # rag = GraphRag(corp_id=corp_id, namespace=knowledge_id).get_instance()
            # ENTITY_TYPES.append(OTHER_TYPE)
            # tags = ENTITY_TYPES
            # try:
            #     asyncio.run(rag.chunk_entity_relation_graph.buildup(tags, [EDGE_TYPE]))
            # except Exception as e:
            #     logger.info(f"build up error: {e}")
            # return

class KnowledgeGalaxyView(Resource):
    @staticmethod
    @login_check
    def get(action):
        if action == 'all':
            parser = reqparse.RequestParser()
            parser.add_argument('size', type=int, required=False, location='args')
            knowledge_controller = KnowledgeController()
            args = parser.parse_args()
            size = args.get('size')
            result = knowledge_controller.get_cluster_all(size)
            return json_response(data={'data': result})

        if action == 'cluster':
            parser = reqparse.RequestParser()
            parser.add_argument('cluster_id', type=str, required=True, location='args')
            parser.add_argument('size', type=str, required=False, location='args')
            knowledge_controller = KnowledgeController()
            args = parser.parse_args()
            cluster_id = args.get('cluster_id')
            size = args.get('size')
            result = knowledge_controller.get_cluster_detail(cluster_id, size)
            return json_response(data={'data': result})

        if action == 'similar':
            parser = reqparse.RequestParser()
            parser.add_argument('doc_id', type=str, required=True, location='args')
            parser.add_argument('size', type=int, required=False, location='args', default=100)
            parser.add_argument('threshold', type=float, required=False, location='args', default=1.5)
            knowledge_controller = KnowledgeController()
            args = parser.parse_args()
            doc_id = args.get('doc_id')
            size = args.get('size')
            threshold = args.get('threshold')

            data = knowledge_controller.fetch_data_similar_outer(doc_id, size)
            threshold_value = float(threshold)
            data = [doc for doc in data if float(doc.get('score', 0)) > threshold_value]

            doc_id_list = [str(doc['id']) for doc in data]
            doc_infos = knowledge_controller.get_knowledge_doc_list(None, {'doc_ids':doc_id_list,'is_all':'all'})
            id_to_doc_info = {
                str(doc['document_id']): {
                    'source_type': doc.get('source_type'),
                    'doc_url': doc.get('doc_url')
                }
                for doc in doc_infos.get('data_list', [])
            }

            for doc in data:
                doc_id = str(doc['id'])
                info = id_to_doc_info.get(doc_id, {})
                doc['source_type'] = info.get('source_type')
                doc['doc_url'] = info.get('doc_url')
                doc.pop('reduced_vectors_3d', None)
                doc.pop('cluster_id', None)

            return json_response(data={'data': data})

        if action == 'history':
            parser = reqparse.RequestParser()
            parser.add_argument('size', type=str, required=False, location='args')
            knowledge_controller = KnowledgeController()
            args = parser.parse_args()
            size = args.get('size')
            if not size:
                size = 100
            data = knowledge_controller.get_knowledge_galaxy_history()
            return json_response(data={'data': data})

        if action == 'search_history':
            parser = reqparse.RequestParser()
            parser.add_argument('size', type=str, required=False, location='args')
            knowledge_controller = KnowledgeController()
            args = parser.parse_args()
            size = args.get('size')
            if not size:
                size = 50
            tp_user_id = request.user['tp_user_id']

            data = knowledge_controller.get_knowledge_galaxy_record_history_filter(tp_user_id, size)
            return json_response(data={'data': data})


    @staticmethod
    @login_check
    def post(action):
        if action == 'search':
            parser = reqparse.RequestParser()
            parser.add_argument('doc_name', type=str, required=True, location='json')
            parser.add_argument('doc_id_list', type=list, required=False, location='json')
            parser.add_argument('size', type=int, required=False, location='json')
            knowledge_controller = KnowledgeController()
            args = parser.parse_args()
            doc_name = args.get('doc_name')
            doc_id_list = args.get('doc_id_list')
            size = args.get('size')
            tp_user_id = request.user['tp_user_id']

            if not size:
                size = 100
            data = knowledge_controller.fetch_doc_view(doc_name, doc_id_list, size)
            knowledge_controller.add_search_record(doc_name, tp_user_id)
            return json_response(data={'data': data})

        if action == 'build':
            task.build_cluster(g.corpid, mode=0)
            # task.build_cluster(g.corpid, mode=0)
            data = {'status': 'started', 'message': 'Cluster building started.'}
            return json_response(data={'data': data})



class FileClearDataView(Resource):
    """
    @summarg: 文档清洗规则配置
    """
    @staticmethod
    @login_check
    def get():
        data = KnowledgeController().get_all_file_rule()
        return json_response(data=data)

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('rule_name', type=str, required=True, location='json')
        parser.add_argument('rule_content', type=str, required=True, location='json')
        parser.add_argument('rule_desc', type=str, required=False, location='json')
        parser.add_argument('rule_type', type=int, required=True, default=0, location='json')
        parser.add_argument('doc_type', type=int, required=True, default=0, location='json')
        parser.add_argument('is_builtin', type=int, required=True, location='json')
        args = parser.parse_args()
        tp_user_id = request.user['tp_user_id']
        args['tp_user_id'] = tp_user_id
        is_goon, data = KnowledgeController().add_file_rule(args)
        if not is_goon:
            return json_response(code="FAIL", message=data)
        return json_response(data=data)
    
    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('rule_id', type=str, required=True, location='json')
        # parser.add_argument('params', type=str, required=True, location='json')
        parser.add_argument('rule_content', type=str, required=True, location='json')
        parser.add_argument('rule_name', type=str, required=True, location='json')
        parser.add_argument('doc_type', type=int, required=True, default=0, location='json')
        # parser.add_argument('doc_type', type=int, required=True, default=0, location='json')
        # parser.add_argument('is_builtin', type=int, required=True, location='json')
        args = parser.parse_args()
        rule_id = args.get("rule_id")
        rule_content = args.get("rule_content")
        rule_name = args.get("rule_name")
        doc_type = args.get("doc_type")
        data = KnowledgeController().put_file_rule(rule_id, rule_content, rule_name, doc_type)
        return json_response(data=data)
    
    @staticmethod
    @login_check
    def delete():
        parser = reqparse.RequestParser()
        parser.add_argument('rule_id', type=str, required=True, location='json')
        args = parser.parse_args()
        rule_id = args.get("rule_id")
        data = KnowledgeController().delete_file_rule(rule_id)
        return json_response(data=data)
    
class FileClearTableView(Resource):
    """
    @summarg: 文档清洗table数据
    """
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('page', type=int, required=True, location='args')
        parser.add_argument('page_size', type=int, required=True, location='args')
        args = parser.parse_args()
        
        page, page_size = args.get("page", 1), args.get("page_size", 10)
        data = KnowledgeController().get_file_rule_table(page, page_size)
        # data = KnowledgeController().get_file_rule_table(**args)
        return json_response(data=data)

class FileClearRelationView(Resource):
    """
    @summarg: 文档清洗关系
    """
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('document_id', type=int, required=True, location='json')
        args = parser.parse_args()
        knowledge_id = args.get("document_id")
        data = KnowledgeController().get_file_relation_rule(knowledge_id)
        return json_response(data=data)

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('doc_list', type=dict, required=True, location='json')
        parser.add_argument('excel_list', type=dict, required=True, location='json')
        args = parser.parse_args()
        doc_list = args.get("doc_list")
        excel_list = args.get("excel_list")
        data = KnowledgeController().add_file_relation_rule(doc_list, excel_list)
        return json_response(data=data)
    
class KnowledgeClearRelationView(Resource):
    """
    @summarg: 文档清洗知识库预配置规则
    """
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='args')
        args = parser.parse_args()
        knowledge_id = args.get("knowledge_id")
        data = KnowledgeController().get_knowledge_relation_rule(knowledge_id)
        return json_response(data=data)
    
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('rule_ids', type=list, required=True, location='json')
        args = parser.parse_args()
        knowledge_id = args.get("knowledge_id")
        rule_ids = args.get("rule_ids")
        data = KnowledgeController().add_knowledge_relation_rule(knowledge_id, rule_ids)
        return json_response(data=data)
    
    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('rule_ids', type=list, required=True, location='json')
        parser.add_argument('old_rule_ids', type=list, required=True, location='json')
        args = parser.parse_args()
        knowledge_id = args.get("knowledge_id")
        rule_ids = args.get("rule_ids")
        old_rule_ids = args.get("old_rule_ids")
        data = KnowledgeController().put_knowledge_relation_rule(knowledge_id, old_rule_ids, rule_ids)
        return json_response(data=data)

    @staticmethod
    @login_check
    def delete():
        parser = reqparse.RequestParser()
        parser.add_argument('ids', type=list, required=True, location='json')
        args = parser.parse_args()
        ids = args.get("ids")
        data = KnowledgeController().delete_knowledge_relation_rule(ids)
        return json_response(data=data)

class KnowledgeDocClearUrlView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('doc_list', type=list, required=True, location='json')
        parser.add_argument('aigc_model_id', type=str, required=True, location='json')
        parser.add_argument('pdf_model', type=int, required=False, location='json', default=0)
        parser.add_argument('rule_list', type=dict, required=False, location='json', default={})

        args = parser.parse_args()
        knowledge_id = args['knowledge_id']
        doc_list = args['doc_list']
        # aigc_model_id = "6b70c380-637c-11ef-931c-4aeb9a6a45a4"
        aigc_model_id = args['aigc_model_id']
        pdf_model = args['pdf_model']
        rule_list = args['rule_list']

        # 验证每个文档是否包含 doc_id
        for doc in doc_list:
            if 'doc_id' not in doc:
                return json_response(code='ERROR', message=f"Missing 'doc_id' in document: {doc.get('doc_name', 'Unknown')}")

        # 提取所有文档的 doc_id
        doc_ids = [doc['doc_id'] for doc in doc_list]
        for doc in doc_list:
            KnowledgeController().update_knowledge_doc(doc['doc_id'], {'current_node': '文档清洗','state':'进行中'})
        # 异步任务处理
        res = task.async_document_clear_v1.delay(
            g.corpid, doc_list, aigc_model_id, knowledge_id, doc_ids, rule_list
        )

        # 返回响应
        return json_response(
            code='SUCCESS', 
            data={
                'task_id': res.id,
                'doc_list': doc_list  
            }
        )
    

    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='json')
        args = parser.parse_args()
        has_fail = False
        res = celery.AsyncResult(args['task_id'])
        if res and res.status == 'SUCCESS' and res.info.get('data'):
            has_fail = res.info['has_fail']
            current = res.info['current']
        else:
            current = 0
        return json_response(code='SUCCESS', data={'state': res.state, 'has_fail': has_fail, 'current': current, 'doc_info': res.info})

        
class GetMdContentView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('object_name', type=str, required=True, location='json')
        args = parser.parse_args()
        object_name = args.get('object_name')
        bucket_name = settings.KNOWLEDGE_OSS_BUCKET_NAME
        content = make_response(send_file(minio_util.download_file_v2(bucket_name, object_name), as_attachment=True, download_name='example.md', mimetype='text/markdown'))
        return content
    
class KnowledgeDoc2DocxView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('relative_path', type=str, required=True, location='json')
        parser.add_argument('oss_names_array', type=list, required=True, location='json')
        args = parser.parse_args()
        relative_path = args['relative_path']
        oss_names_array = args['oss_names_array']
        url_list = KnowledgeController().get_minio_urls_v1(relative_path, oss_names_array)
        return json_response(data={'data_list': url_list})


class SaveDocView(Resource):
    @staticmethod
    @login_check
    def post():
        # 请求参数解析
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('doc_list', type=list, required=True, location='json')
        parser.add_argument('aigc_model_id', type=str, required=True, location='json')
        parser.add_argument('pdf_model', type=int, required=False, location='json', default=0)
        parser.add_argument('rule_list', type=dict, required=False, location='json', default={})
        parser.add_argument('chunk_size', type=int, required=False, location='json', default=256)
        parser.add_argument('chunk_overlap', type=int, required=False, location='json', default=20)
        parser.add_argument('slice_model', type=int, required=False, location='json', default=0)
        parser.add_argument('chunk_clear_call_words', type=str, required=False, location='json', default="")
        parser.add_argument('chunk_clear_rule_ids', type=list, required=False, location='json', default=[])
        parser.add_argument('upload_type', type=int, required=False, location='json', default=0,help='0: 文档, 1: 视频，2: 音频')
        parser.add_argument('task_id', type=str, required=False, location='json', default='')
        parser.add_argument('doc_id', type=str, required=False, location='json', default='')
        parser.add_argument('extract_tables', type=int, required=False, location='json', default=0)
        args = parser.parse_args()
        knowledge_id = args['knowledge_id']
        doc_list = args['doc_list']
        aigc_model_id = args['aigc_model_id']
        pdf_model = args['pdf_model']
        rule_list = args['rule_list']
        tp_user_id = request.user['tp_user_id']
        split_chunk_size = args["chunk_size"]
        chunk_overlap = args["chunk_overlap"]
        slice_model = args["slice_model"]
        chunk_clear_call_words = args["chunk_clear_call_words"]
        chunk_clear_rule_ids = args["chunk_clear_rule_ids"]
        upload_type = args["upload_type"]
        args['tp_user_id'] = tp_user_id
        doc_id = args.get('doc_id')
        extract_tables = args.get('extract_tables')
        # 新的文档列表，用于存储处理后的文档（包括 doc_id）
        processed_docs = []
        # 处理每个文档
        total_docs = len(doc_list)
        task_id = args['task_id']
        csv_path_list = []
        redis_client = redis_pool.use(redis_pool.aigc_pool)
        if doc_id:
            doc = doc_list[0]
            KnowledgeController().del_split_chunk(doc_id)
            KnowledgeController().update_knowledge_doc(doc_id, {'current_node': '文档上传','state':'已完成','split_chunk_size':split_chunk_size,'chunk_overlap':chunk_overlap,'slice_model':slice_model,'pdf_model':pdf_model,'chunk_clear_call_words':chunk_clear_call_words,'upload_type':upload_type})
            if chunk_clear_rule_ids:
                for chunk_clear_rule_id in chunk_clear_rule_ids:
                    KnowledgeController().save_chunk_clear_rule_id(doc_id,chunk_clear_rule_id)       
            # 提取文档中表格：
            if extract_tables == 1:
                try:
                    csv_path_list = task.extract_table_from_docx(doc,g.corpid,doc_id,aigc_model_id,task_id,redis_client)
                except Exception as e:
                    logger.error(f"Error processing document {doc.get('doc_name')}: {e}")
                    csv_path_list=[]
            else:
                csv_path_list=[]
                # output_folder_path = os.path.join(settings.BASE_DIR, f"upload/knowledge_doc_image_files/{get_snowflake_id()}")
                # doc_img_list = KnowledgeController().extract_table_images_from_docx(doc.get('doc_url'), output_folder_path)
                # KnowledgeController().add_knowledge_document_images_batch(doc_id, doc_img_list)
            if csv_path_list != [] and csv_path_list != [[]]:
                have_table = 1
                KnowledgeController().update_knowledge_doc(doc_id, {'have_table': 1})
            else:
                have_table = 0
                KnowledgeController().update_knowledge_doc(doc_id, {'have_table': 0})
            # 更新文档清洗后的 URL
            KnowledgeController().update_knowledge_doc(doc_id, {'doc_clear_url': doc.get('doc_url')}) 
            doc['have_table'] = have_table
            doc['doc_id'] = doc_id
                    # 将处理后的文档添加到新的列表
            processed_docs.append(doc)        
            redis_client.set(f'progress_{task_id}', 100)
            return json_response(code='SUCCESS', data={
                'knowledge_id': knowledge_id,
                'doc_list': processed_docs,
                'aigc_model_id': aigc_model_id,
                'pdf_model': pdf_model,
                'rule_list': rule_list,
            })
        else:
            for index, doc in enumerate(doc_list, start=1):
                try:
                    # 提取 doc_name，如果无法拆分则使用原始值
                    doc_name = doc.get('doc_name', '').split("_", 1)[-1] if doc.get('doc_name') else 'Unknown'
                    
                    # 将文档信息添加到知识库并获取 doc_id
                    doc_id = KnowledgeController().add_knowledge_doc(
                        knowledge_id,
                        doc_name,
                        doc.get('doc_type', ''),
                        doc.get('doc_url', ''),
                        doc.get('doc_size', 0),
                        'TODO' , 
                        '文档上传',
                        '已完成',
                        None,
                        tp_user_id,
                        split_chunk_size,
                        chunk_overlap,
                        slice_model,
                        pdf_model,
                        chunk_clear_call_words,
                        upload_type
                    )

                    if chunk_clear_rule_ids:
                        for chunk_clear_rule_id in chunk_clear_rule_ids:
                            KnowledgeController().save_chunk_clear_rule_id(doc_id,chunk_clear_rule_id)
                    # 提取文档中表格：
                    if extract_tables == 1:
                        try:
                            csv_path_list = task.extract_table_from_docx(doc,g.corpid,doc_id,aigc_model_id,task_id,redis_client)
                        except Exception as e:
                            logger.error(f"Error processing document {doc.get('doc_name')}: {e}")
                            csv_path_list=[]
                    else:
                        # output_folder_path = os.path.join(settings.BASE_DIR, f"upload/knowledge_doc_image_files/{get_snowflake_id()}")
                        # doc_img_list = KnowledgeController().extract_table_images_from_docx(doc.get('doc_url'), output_folder_path)
                        # KnowledgeController().add_knowledge_document_images_batch(doc_id, doc_img_list)
                        csv_path_list=[]
                    if csv_path_list != [] and csv_path_list != [[]]:
                        have_table = 1
                        KnowledgeController().update_knowledge_doc(doc_id, {'have_table': 1})
                    else:
                        have_table = 0
                        KnowledgeController().update_knowledge_doc(doc_id, {'have_table': 0})
                    # 更新文档清洗后的 URL
                    KnowledgeController().update_knowledge_doc(doc_id, {'doc_clear_url': doc.get('doc_url')})

                    # 在文档信息中添加 doc_id
                    doc['doc_id'] = doc_id
                    doc['have_table'] = have_table
                    # 将处理后的文档添加到新的列表
                    processed_docs.append(doc)
                except Exception as e:
                    # 错误处理：记录错误日志并跳过当前文档
                    logger.error(f"Error processing document {doc.get('doc_name')}: {e}")
                    continue
                progress = (index / total_docs) * 100
                redis_client.set(f'progress_{task_id}', progress)

            # 处理完成后，将整体进度设置为 100
            redis_client.set(f'progress_{task_id}', 100)
        # 返回成功响应，包含处理后的 doc_list
        return json_response(code='SUCCESS', data={
            'knowledge_id': knowledge_id,
            'doc_list': processed_docs,
            'aigc_model_id': aigc_model_id,
            'pdf_model': pdf_model,
            'rule_list': rule_list
        })
    
    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='json')
        args = parser.parse_args()
        task_id = args['task_id']
        progress = 0
        redis_client = redis_pool.use(redis_pool.aigc_pool)
        progress = redis_client.get(f'progress_{task_id}')
        if progress:
            progress = float(progress)
        else:
            progress = 0
        return json_response(code='SUCCESS', data={'progress': progress})
    
class ModifyMdView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('object_name', type=str, required=True, location='json')
        parser.add_argument('md_content', type=str, required=True, location='json')
        parser.add_argument('doc_id', type=str, required=True, location='json')
        args = parser.parse_args()
        object_name = args.get('object_name')
        md_content = args.get('md_content')
        doc_id = args.get('doc_id')
        data = KnowledgeController().modify_md(object_name, md_content, doc_id)
        return json_response(data=data)
    
class GetMdContentViewtest(Resource):
    @staticmethod
    def get():
        object_name = "/knowledge/6961db1d-c33c-4bbb-ab49-b82578ffa590_测试用本文档由中文文档库提供.md"
        bucket_name = settings.KNOWLEDGE_OSS_BUCKET_NAME
        content = make_response(send_file(minio_util.download_file_v2(bucket_name, object_name), as_attachment=True, download_name='example.md', mimetype='text/markdown'))
        return content

class KnowledgeXls2XlsxView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('relative_path', type=str, required=True, location='json')
        parser.add_argument('oss_names_array', type=list, required=True, location='json')
        args = parser.parse_args()
        relative_path = args['relative_path']
        oss_names_array = args['oss_names_array']
        url_list = KnowledgeController().get_minio_urls_xls2xlsx(relative_path, oss_names_array)
        return json_response(data={'data_list': url_list})
    
class File2MdView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('oss_names_array', type=list, required=True, location='json')
        args = parser.parse_args()
        oss_names_array = args['oss_names_array']
        data = KnowledgeController().file2md(oss_names_array)
        return json_response(data=data)
    
class KnowledgeTagView(Resource):
    @staticmethod
    @login_check
    def get():
        '''获取knowledge数据'''
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=False, location='args')
        parser.add_argument('page_no', type=int, default=1, location='args')
        parser.add_argument('page_size', type=int, default=10, location='args')
        args = parser.parse_args()
        data = KnowledgeController().get_knowledge_tag(args)
        return json_response(data=data)


    @staticmethod
    @login_check
    def post():
        '''新增knowledge数据'''
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('tags', type=list, required=True, location='json')
        args = parser.parse_args()

        data = KnowledgeController().add_knowledge_tag(args)
        return json_response(data=data)


class StatisticsApp(Resource):
    
    @staticmethod
    @login_check
    def get():
        """
        @summarg: 
        时间格式为 %Y-%m-%d %H:%M:%S
        """
        parser = reqparse.RequestParser()
        parser.add_argument('current_time', type=str, required=True, location='args', help='当前时间')
        parser.add_argument('start_time', type=str, required=True, location='args', help='开始时间')
        parser.add_argument('end_time', type=str, required=True, location='args', help='结束时间')
        args = parser.parse_args()
        current_time = args.get('current_time')
        start_time = args.get('start_time')
        end_time = args.get('end_time')
        input_time, start_of_day, previous_day_start, previous_day_same_time = get_ring_times(current_time, mode="day")
        sa_controller = StatisticsAppController()
        basic_res = sa_controller.return_statistics_basic_result(input_time, previous_day_same_time, start_of_day, previous_day_start)
        
        app_statistics_by_date_result_list = sa_controller.return_session_statistics_by_date_result(start_time, end_time)
        order_list = sa_controller.return_session_statistics_order_result()
        res = {
            "basic_statistics": {"dict": sa_controller.statistics_basic_dict, 'data': basic_res},
            "app_statistics_by_date_result_list": {"dict": sa_controller.session_statistics_by_date_dict, "data": app_statistics_by_date_result_list},
            "order_list": {"dict": sa_controller.sesstion_order_dict, "data": order_list}
            }
        return json_response(data=res)
        
class StatisticsDetailRingApp(Resource):
    @staticmethod
    @login_check
    def get():
        """
        @summarg: 
        时间格式为 %Y-%m-%d %H:%M:%S
        """
        parser = reqparse.RequestParser()
        parser.add_argument('current_time', type=str, required=True, location='args', help='当前时间')
        parser.add_argument('field_name', type=str, required=True, location='args', help='字段:')
        parser.add_argument('mode', type=str, required=True, location='args', help='模式：')
        parser.add_argument('app_id', type=str, required=True, location='args', help='app_id')
        args = parser.parse_args()
        current_time = args.get('current_time')
        field_name = args.get('field_name')
        mode = args.get('mode')
        app_id = args.get('app_id')
        sad_controller = StatisticsAppDetailController()
        res = sad_controller.get_statistics_count_ring_by_field(app_id, current_time, field_name, mode)
        result = {"filed_dict": sad_controller.field_dict, "mode_dict": sad_controller.mode_dict, "data": res}
        return json_response(data=result)


class StatisticsDetailDateApp(Resource):
    @staticmethod
    @login_check
    def get():
        """
        @summarg: 
        时间格式为 %Y-%m-%d %H:%M:%S
        """
        parser = reqparse.RequestParser()
        parser.add_argument('start_time', type=str, required=True, location='args', help='开始时间')
        parser.add_argument('end_time', type=str, required=True, location='args', help='结束时间')
        parser.add_argument('app_id', type=str, required=True, location='args', help='app_id')
        args = parser.parse_args()
        start_time = args.get('start_time')
        end_time = args.get('end_time')
        app_id = args.get('app_id')
        sad_controller = StatisticsAppDetailController()
        res = sad_controller.get_statistics_detail_by_date_result(start_time, end_time, app_id)
        result = {"dict": sad_controller.detail_dict, "data": res}
        return json_response(data=result)
    
class StatisticsQuestionApp(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('app_id', type=str, required=True, location='args', help='app_id')
        args = parser.parse_args()
        app_id = args.get('app_id')
        sad_controller = StatisticsAppDetailController()
        question_list = sad_controller.get_question_by_app_id(app_id)
        return json_response(data=question_list)


class KnowledgeStatisticView(Resource):

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=False, location='args')
        parser.add_argument('start_time', type=str, required=False, location='args')
        parser.add_argument('end_time', type=str, required=False, location='args')
        args = parser.parse_args()

        if args.get("knowledge_id"):
            data = KnowledgeController().get_knowledge_statistic(args)
        else:
            data = KnowledgeController().get_all_knowledge_statistic(args)
        return json_response(data=data)



class StatisticsCompoundApp(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('app_id', type=str, required=True, location='args', help='app_id')
        parser.add_argument('start_time', type=str, required=False, location='args', help='开始时间')
        parser.add_argument('end_time', type=str, required=False, location='args', help='结束时间')
        args = parser.parse_args()
        sa_controller = StatisticsAppController()
        data = sa_controller.get_compound_app_statistics(args)
        return json_response(data=data)



class KnowledgeFileStateView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('document_id', type=str, required=True, location='json', help='文档id')
        parser.add_argument('current_node', type=str, required=True, location='json', help='当前节点')
        args = parser.parse_args()
        data = KnowledgeController().get_file_state(args)
        return json_response(data=data)

class KnowledgeVideoUpload(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('doc_name', type=str, required=True, location='json')
        parser.add_argument('doc_type', type=str, required=True, location='json')
        parser.add_argument('doc_url', type=str, required=True, location='json')
        parser.add_argument('doc_size', type=int, required=True, location='json')
        parser.add_argument('key_frame', type=str, required=True, location='json')
        parser.add_argument('video_duration', type=str, required=True, location='json')
        parser.add_argument('frame_rate', type=str, required=True, location='json')
        parser.add_argument('upload_type', type=int, required=True, location='json',default=0)

        args = parser.parse_args()
        knowledge_id = args['knowledge_id']
        doc_name = args['doc_name']
        doc_type = args['doc_type']
        doc_url = args['doc_url']
        doc_size = args['doc_size']
        key_frame = args['key_frame']
        video_duration = args['video_duration']
        frame_rate = args['frame_rate']
        upload_type = args['upload_type']
        tp_user_id = request.user['tp_user_id']

        doc_id = KnowledgeController().video_upload(knowledge_id, doc_name, doc_type, doc_url, doc_size, tp_user_id, key_frame, video_duration, frame_rate, upload_type)
        res = task.async_extract_video_frame.delay(g.corpid, doc_url)

        data = {
            'doc_id': doc_id,
            'doc_url': doc_url,
            'doc_name': doc_name,
            'doc_type': doc_type,
            'doc_size': doc_size,
            'key_frame': key_frame,
            'video_duration': video_duration,
            'frame_rate': frame_rate,
            'upload_type': upload_type,
            'task_id': res.id
        }

        return json_response(data=data)

    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='json')
        args = parser.parse_args()
        res = celery.AsyncResult(args['task_id'])
        task_result = res.info if res.info else {'state': 'PENDING', 'image_list': []}

        return json_response(code='SUCCESS', data=task_result)

class KnowledgAudioTextView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('audio_url', type=str, required=False, location='json')
        parser.add_argument('audio_list', type=list, required=False, location='json')
        args = parser.parse_args()
        audio_list = args.get('audio_list',[])
        audio_url = args.get('audio_url',"")
        if audio_list:
            res = task.async_extract_audio_list_text.delay(g.corpid, audio_list)
        else:
            res = task.async_extract_audio_text.delay(g.corpid, audio_url)

        return json_response(data={'task_id': res.id})

    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='json')
        args = parser.parse_args()
        res = celery.AsyncResult(args['task_id'])
        task_result = res.info if res.info else {'state': 'PENDING','progress':0}

        return json_response(code='SUCCESS', data=task_result)

class KnowledgeDocumentUploadHistory(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('page_no', type=int, required=False, default=1, location='json', help='页码')
        parser.add_argument('page_size', type=int, required=False, default=10, location='json', help='每页条数')
        parser.add_argument('knowledge_id', type=str, required=True, location='json', help='知识id')

        args = parser.parse_args()

        data = KnowledgeController().get_knowledge_document_upload_history(args)
        return json_response(data=data)

class KnowledgeDocumentImagesAndTables(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('doc_id', type=str, required=True, location='args', help='文档id')
        parser.add_argument('page_no', type=int, required=False, default=1, location='args', help='页码')
        parser.add_argument('page_size', type=int, required=False, default=10, location='args', help='每页条数')
        args = parser.parse_args()
        data = KnowledgeController().get_knowledge_document_images_and_tables(args)
        return json_response(data=data)

class KnowledgeDocumentCount(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='args', help='knowledge_id')
        args = parser.parse_args()
        knowledge_id = args.get('knowledge_id')
        data = KnowledgeController().get_knowledge_document_count(knowledge_id)
        return json_response(data=data)


class KnowledgeDocTransferView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('doc_ids', type=list, required=True, location='json')
        parser.add_argument('target_knowledge_id', type=str, required=True, location='json')
        parser.add_argument('is_copy', type=int, required=False, location='json', default=0)
        args = parser.parse_args()
        is_copy = args.get('is_copy')
        if is_copy == 1:
            data = KnowledgeController().transfer_doc_copy(args)
        else:
            data = KnowledgeController().transfer_doc(args)
        return json_response(data=data)
    
    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('doc_ids', type=list, required=True, location='json')
        args = parser.parse_args()
        chunk_ids = []
        for doc_id in args['doc_ids']:
            new_doc_id = KnowledgeController().get_knowledge_document_detail_by_doc_source_id(doc_id)
            if new_doc_id:
                res = KnowledgeController().get_knowledge_doc_chunk_list(new_doc_id, {'is_all': 'all'})
                for data in res['data_list']:
                    chunk_ids.append(data['chunk_id'])
                KnowledgeController().bulk_del_es_knowledge_chunk(new_doc_id, {'chunk_id':chunk_ids})
                KnowledgeController().update_knowledge_doc_chunk_batch(chunk_ids, {'delete_flag': settings.DELETE_FLAG_TRUE})
        data = KnowledgeController().update_doc_sharing(args)
        return json_response(data=data)

class AllScopeView(Resource):
    @staticmethod
    @login_check
    def get():
        data = KnowledgeController().get_all_scope()
        return json_response(data=data)
    
class SystemListView(Resource):
    @staticmethod
    @login_check
    def get():
        data = KnowledgeController().get_system_list()
        return json_response(data=data)
    
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('system_name', type=str, required=True, location='json', help='系统名称')
        args = parser.parse_args()
        tp_user_id = request.user['tp_user_id']
        args['tp_user_id'] = tp_user_id
        data = KnowledgeController().create_system(args)
        return json_response(data=data)
    
class SystemConfigView(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('display_name', type=str, required=False, location='args', help='显示名称')
        parser.add_argument('page_no', type=int, required=False, default=1, location='args', help='页码')
        parser.add_argument('page_size', type=int, required=False, default=10, location='args', help='每页条数')
        args = parser.parse_args()

        data = KnowledgeController().get_system_config(args)
        return json_response(data=data)

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('system_id', type=str, required=True, location='json', help='系统id')
        parser.add_argument('display_name', type=str, required=True, location='json', help='显示名称')
        parser.add_argument('icon_url', type=str, required=False, location='json', help='图标')
        args = parser.parse_args()
        tp_user_id = request.user['tp_user_id']
        args['tp_user_id'] = tp_user_id
        data = KnowledgeController().create_system_config(args)
        return json_response(data=data)
    

    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('setting_id', type=str, required=True, location='json', help='配置id')
        parser.add_argument('display_name', type=str, required=True, location='json', help='显示名称')
        parser.add_argument('icon_url', type=str, required=False, location='json', help='图标')
        parser.add_argument('is_applying', type=int, required=False, default=0, location='json', help='是否应用')
        args = parser.parse_args()
        tp_user_id = request.user['tp_user_id']
        args['tp_user_id'] = tp_user_id
        data = KnowledgeController().update_system_config(args)
        return json_response(data=data)
    
    @staticmethod
    @login_check
    def delete():
        parser = reqparse.RequestParser()
        parser.add_argument('setting_id', type=str, required=True, location='json', help='配置id')
        args = parser.parse_args()
        data = KnowledgeController().delete_system_config(args)
        return json_response(data=data)
    
class AudioChainHandleView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('doc_list', type=list, required=True, location='json')
        parser.add_argument('pdf_model', type=int, required=False, location='json', default=0)
        parser.add_argument('chunk_size', type=int, required=False, location='json', default=256)
        parser.add_argument('chunk_overlap', type=int, required=False, location='json', default=20)
        parser.add_argument('slice_model', type=int, required=False, location='json', default=0)
        parser.add_argument('chunk_clear_call_words', type=str, required=False, location='json', default="")
        parser.add_argument('chunk_clear_rule_ids', type=list, required=False, location='json', default=[])
        parser.add_argument('upload_type', type=int, required=False, location='json', default=0)
        parser.add_argument('task_id', type=str, required=False, location='json', default='')
        args = parser.parse_args()
        knowledge_id = args['knowledge_id']
        doc_list = args['doc_list']
        pdf_model = args['pdf_model']
        tp_user_id = request.user['tp_user_id']
        split_chunk_size = args["chunk_size"]
        chunk_overlap = args["chunk_overlap"]
        slice_model = args["slice_model"]
        chunk_clear_call_words = args["chunk_clear_call_words"]
        chunk_clear_rule_ids = args["chunk_clear_rule_ids"]
        upload_type = args["upload_type"]
        args['tp_user_id'] = tp_user_id
        task_id = args['task_id']
        total_docs = len(doc_list)
        redis_client = redis_pool.use(redis_pool.aigc_pool)
        audio_list =[]

        for index, doc in enumerate(doc_list, start=1):
            try:
                doc_name = doc.get('doc_name').split("_", 1)[-1]
            except Exception as e:
                logger.error(f"doc_name error: {e}")
                doc_name = doc['doc_name']

            doc['doc_id'] = KnowledgeController().add_knowledge_doc(
                args['knowledge_id'], doc_name, doc['doc_type'],
                doc['doc_url'], doc['doc_size'], 'TODO',
                    '文档上传',
                    '已完成',
                    None,
                    tp_user_id,
                    split_chunk_size,
                    chunk_overlap,
                    slice_model,
                    pdf_model,
                    chunk_clear_call_words,
                    upload_type
            )
            if chunk_clear_rule_ids:
                for chunk_clear_rule_id in chunk_clear_rule_ids:
                    KnowledgeController().save_chunk_clear_rule_id(doc['doc_id'],chunk_clear_rule_id)
            audio_list.append({
                'doc_id': doc['doc_id'],
                'audio_url': doc['doc_url']
            })

            progress = round((index / total_docs) * 50, 2)
            redis_client.set(f'progress_{task_id}', progress)
        # 上传完成，设置阶段进度为 50
        redis_client.set(f'progress_{task_id}', 50)

        # 文档切分 + 向量生成信息
        doc_info = {
            'chunk_size': args['chunk_size'],
            'chunk_overlap': args['chunk_overlap'],
            'doc_list': doc_list,
            'pdf_model': args['pdf_model'],
            'task_id': task_id  
        }
        # 异步处理链式任务（非阻塞）
        chain(
            task.async_extract_audio_list_text.s( audio_list),
            task.async_audio_split_connector.s(g.corpid, args['knowledge_id'], doc_info, args['slice_model']),
            task.async_document_splitter_v1.s(),
            task.async_split_connector_emb.s(g.corpid, args['knowledge_id']),
            task.async_appoint_document_chunk_embeddings_coroutine.s(),
            task.report_progress.s(task_id, 100),             
        ).apply_async(args=(g.corpid,))

        return json_response(code='SUCCESS', data={'task_id': task_id, 'doc_info': doc_info})

    
    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='json')
        args = parser.parse_args()

        redis_client = redis_pool.use(redis_pool.aigc_pool)
        progress = redis_client.get(f'progress_{args["task_id"]}')
        try:
            progress = float(progress) if progress else 0
        except ValueError:
            progress = 0

        return json_response(code='SUCCESS', data={'progress': progress})

class VideoChainHandleView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('knowledge_id', type=str, required=True, location='json')
        parser.add_argument('doc_name', type=str, required=True, location='json')
        parser.add_argument('doc_type', type=str, required=True, location='json')
        parser.add_argument('doc_url', type=str, required=True, location='json')
        parser.add_argument('doc_size', type=int, required=True, location='json')
        parser.add_argument('key_frame', type=str, required=True, location='json')
        parser.add_argument('video_duration', type=str, required=True, location='json')
        parser.add_argument('frame_rate', type=str, required=True, location='json')
        parser.add_argument('upload_type', type=int, required=True, location='json',default=0)
        parser.add_argument('task_id', type=str, required=False, location='json', default='')
        args = parser.parse_args()
        knowledge_id = args['knowledge_id']
        doc_name = args['doc_name']
        doc_type = args['doc_type']
        doc_url = args['doc_url']
        doc_size = args['doc_size']
        key_frame = args['key_frame']
        video_duration = args['video_duration']
        frame_rate = args['frame_rate']
        upload_type = args['upload_type']
        tp_user_id = request.user['tp_user_id']
        task_id = args['task_id']
        redis_client = redis_pool.use(redis_pool.aigc_pool)

        # 异步处理链式任务（非阻塞）
        doc_id = KnowledgeController().video_upload(knowledge_id, doc_name, doc_type, doc_url, doc_size, tp_user_id, key_frame, video_duration, frame_rate, upload_type)
        doc_info = {
            'doc_id': doc_id,
            'doc_name': doc_name,
            'doc_type': doc_type,
            'doc_url': doc_url,
            'doc_size': doc_size,
        }
        redis_client.set(f'progress_{task_id}', 30)
        chain(
            task.async_extract_video_frame.s(doc_url),          
            task.async_video_connector.s(g.corpid, knowledge_id, doc_id),
            task.async_video_frame_extraction.s(),
            task.report_progress.s(task_id, 50),
            task.async_frame_connector.s(g.corpid, knowledge_id, doc_id),
            task.async_appoint_document_chunk_embeddings_coroutine.s(),
            task.report_progress.s(task_id, 100),             
        ).apply_async(args=(g.corpid,))

        return json_response(code='SUCCESS', data={'task_id': task_id, 'doc_info': doc_info})

    
    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='json')
        args = parser.parse_args()

        redis_client = redis_pool.use(redis_pool.aigc_pool)
        progress = redis_client.get(f'progress_{args["task_id"]}')
        try:
            progress = float(progress) if progress else 0
        except ValueError:
            progress = 0

        return json_response(code='SUCCESS', data={'progress': progress})

class KnowledgeDocConversationView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('doc_id', type=list, required=True, location='json', help='文档id')
        parser.add_argument('aigc_model_id', type=str, required=True, location='json', help='文档id')
        parser.add_argument('prompt', type=str, required=True, location='json', help='prompt')
        parser.add_argument('size', type=int, required=False, location='json', default=10)  
        parser.add_argument('min_score', type=float, required=False, location='json', default=1.4)
        args = parser.parse_args()

        @stream_with_context
        def _generate():
            controller = KnowledgeController()
            for doc in controller.get_knowledge_doc_conversation(args['doc_id'], args['aigc_model_id'], args['prompt'], args['size'], args['min_score']):
                yield f'data: {doc}\n\n'
        
        return Response(_generate(), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'
        })
        