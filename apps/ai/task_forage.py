import json
import time
import pandas as pd
from celery_app import celery
from lib_func.logger import task_logger
from controller.knowledge_controller import KnowledgeController
from controller.forage_controller import ForageController, DataGenerationStats
from controller.chat_view_controller import ChatViewController


@celery.task(bind=True, name="apps.ai.forage.file_splitter")
def forage_document_splitter(task, corpid, forage_id, file_ids, slice_model, model_id, args):
    if not isinstance(args, dict):
        args = {}
    # 获取 upload_type 默认值 0
    upload_type = args.get('upload_type', 0)
    # 确保 upload_type 是整数类型
    try:
        upload_type = int(upload_type)
    except (ValueError, TypeError):
        upload_type = 0

    meta = {"current": 0, "total": 1, 'status': 'PENDING', 'message': '', 'data': {}, 'exc_type': '', 'next_task_id': '',
            'exc_message': '', 'chunk_list': [], 'has_fail': False}
    state = 'PENDING'
    task.update_state(state=state, meta=meta)
    task_id = task.request.id
    task_logger.info('forage_document_splitter: 分词切片开始 %s' % task_id)
    try:
        forage_action = ForageController(corpid)
        datas = forage_action.page_forage_file(file_ids=file_ids, page_size=1000)['data_list']
        knowledge_controller = KnowledgeController(corpid)

        total = len(datas)
        success = 0
        file_ids = list()

        for doc in datas:
            doc_name = doc['name']
            file_id = doc['file_id']
            file_ids.append(file_id)
            task_logger.info(f"开始处理文档：{doc_name}\n Doc info: {doc}")
            forage_action.up_file_data({'file_id': file_id}, {'status': '1', 'stage': 'split','upload_type':upload_type})

            # 旧数据清理
            forage_action.del_forage_chunk({'doc_id': file_id})
            try:
                res = knowledge_controller.document_splitter_v1(doc['url'], chunk_size=256, chunk_overlap=20, pdf_model=0, slice_model=slice_model)
                bulk_datas = list()
                if res['code'] == 0 and len(res['data']) > 0:
                    _state = 'SUCCESS'
                    for i, chunk in enumerate(res['data']):
                        bulk_datas.append({'doc_id': file_id, 'content': chunk['content'], 'chunk_id': f'{file_id}_{i}',
                                           'character_count': chunk['character_count'], 'task_id': task_id})
                    if bulk_datas:
                        forage_action.bulk_forage_chunk(bulk_datas)
                    success += 1
                    meta['current'] = int(success / total * 100)
                else:
                    _state = 'FAILURE'
            except Exception as err:
                _state = 'FAILURE'
                print(err)
            finally:
                meta['chunk_list'].append({
                    'doc_id': file_id,
                    'doc_name': doc_name,
                    'state': _state,
                    # 'chunk_list': bulk_datas
                })

                if _state == 'SUCCESS':
                    meta['data'].update({file_id: _state})
                    forage_action.up_file_data({'file_id': file_id}, {'status': '2'})
                else:
                    meta['data'].update({file_id: _state, 'has_fail': True})
                    forage_action.up_file_data({'file_id': file_id}, {'status': '3'})
                task.update_state(state=state, meta=meta)
                task_logger.info('forage_document_splitter切片结束，状态：%s; doc_id: %s' % (_state, file_id))

        # 只要有成功数据，则执行下一节点逻辑
        suss_ids = [x for x in file_ids if meta['data'].get(x) == 'SUCCESS']
        if suss_ids:
            status = 'SUCCESS'
            meta['next_task_id'] = forage_qa_extract.delay(corpid, forage_id, model_id, file_ids=suss_ids,upload_type=upload_type).id
        else:
            status = 'FAILURE'
    except Exception as e:
        task_logger.error('forage_document_splitter:【%s】切片失败 %s' % (corpid, e), exc_info=True)
        meta.update({'message': str(e)})
        status = 'FAILURE'
    finally:
        meta['status'] = status
        meta['current'] = 100
        task.update_state(state='SUCCESS', meta=meta)


@celery.task(bind=True, name="apps.ai.forage.qa_extract")
def forage_qa_extract(task, corpid, forage_id, model_id, file_ids=None, chunk_ids=None,upload_type=None):
    meta = {"current": 0, "total": 1, 'success': 0, 'status': 'PENDING', 'message': '', 'data': {}, 'exc_type': '',
            'exc_message': '', 'file_ids': file_ids}
    state = 'PENDING'
    task.update_state(state=state, meta=meta)
    task_logger.info(f"推理模型 model_path: {model_id}")

    forage_action = ForageController(corpid)
    chart = ChatViewController(model_id, corp_id=corpid, old_flag=False)

    # 数据优先级
    if chunk_ids:
        datas = forage_action.get_forage_chunk({'chunk_id': {"$in": chunk_ids}})
    else:
        if file_ids:
            datas = forage_action.get_forage_chunk({'doc_id': {"$in": file_ids}})
        else:
            meta['current'] = 100
            meta['status'] = 'FAILED'
            task.update_state(state=state, meta=meta)
            return

    df = pd.DataFrame(datas)
    total = len(df)

    success_dict = {"success": 0, "error": 0}
    if total <= 0:
        meta['current'] = 100
        meta['status'] = 'FAILED'
        task.update_state(state=state, meta=meta)
        return

    group_df = df.groupby('doc_id')

    message = """
               任务说明：给定输入文本，从中提取语义相关、逻辑完整的问题 - 答案对（QA Pair），需严格遵循以下规则：
                一、核心提取规则
                    内容质量要求：
                        问题必须明确具体：
                            ✅ 针对文本核心信息提问，避免模糊或空泛（如 "什么是算料？" 需细化为 "文本中提到的 BOM 损耗率如何计算？"）；
                            ✅ 包含关键要素（如主体、动作、对象），如 "如何计算 XX 场景下的 XX 数据（如原材料净用量 / 安全库存）"。
                        答案必须完整准确：
                            ✅ 完整覆盖文本中对应的关键信息（数值、步骤、术语等），不得断章取义；
                            ✅ 保留专业术语（如 BOM、MOQ、损耗系数），若文本中有公式 / 数据，需原样呈现（含单位、符号）。
                    格式规范：
                        1. 单组 QA 对格式：问题=|-|=答案（等号两侧无空格，连接符为=|-|=）；
                        2. 多组 QA 对用&-&拼接（如Q1=|-|=A1&-&Q2=|-|=A2）；
                        3. 严格剔除文本中非正常词语（无意义字符、乱码、重复冗余词）和特殊符号（除公式 / 数据必需符号外，如@#$%等）。
                        4. 仅返回最终的QA结果，不需要上下文分析和推荐关联内容；如果没有结果，直接返回 `''`
                二、示例参考
                    输入文本：
                    plaintext
                        算料时需考虑BOM层级分解，例如计算原材料总需求时，需将单品耗用量乘以生产数量，再乘以1+损耗率（如K=5%）。公式为：总需求量=单品耗用量×生产数量×(1+K)  
                
                    正确输出：
                        如何计算原材料总需求量？=|-|=计算原材料总需求量时，需将单品耗用量乘以生产数量，再乘以1+损耗率（如K=5%），公式为：总需求量=单品耗用量×生产数量×(1+K)
                三、执行优先级
                    质量优先：优先保证 QA 对的准确性和完整性，格式错误或内容残缺的条目不予提取；
                    严格过滤：非关键词相关、包含无效字符、逻辑断裂的内容直接排除；
                    忠实原文：答案必须基于文本内容，不得添加原文未提及的信息（如推导、扩展）。
                四、输入文本内容
                    {}
                """
    for doc_id, group in group_df:
        success = error = 0

        # 清除历史QA数据
        forage_action.model.del_forage_qa(file_id=doc_id)

        forage_action.up_file_data({'file_id': doc_id}, {'status': '1', 'stage': 'extract'})
        for _, row in group.iterrows():
            bulk_datas = list()
            # 算料提取
            try:
                content = row['content']
                doc_id = row['doc_id']
                for item in chart.chat_model.receive(message.format(content), False):
                    json_item = json.loads(item.decode())
                    result = json_item.get('result')
                    if result:
                        for i in result.split('&-&'):
                            if '=|-|=' not in i:
                                continue
                            question, answer = i.split('=|-|=')
                            bulk_datas.append({'question': question, 'answer': answer})
                        if bulk_datas:
                            forage_action.add_forage_qa(forage_id, doc_id, bulk_datas, None, {'qa_type': upload_type})
                success += 1
                success_dict['success'] += 1
            except Exception as err:
                print(err)
                error += 1
                success_dict['error'] += 1
            finally:
                meta['current'] = int((success_dict['success'] + success_dict['error']) / total * 100)
                meta['data'].update(success_dict)
                task.update_state(state=state, meta=meta)
        # 单文件维度统计成功和失败数量
        if not error:
            status = 2
        elif success:
            status = 4
        else:
            status = 3
        forage_action.up_file_data({'file_id': doc_id}, {'status': status})
    meta['current'] = 100
    meta['status'] = 'SUCCESS' if success_dict['success'] else 'FAILED'
    task.update_state(state="SUCCESS", meta=meta)

@celery.task(bind=True, name="apps.ai.forage.generate_structured_data")
def generate_structured_data(task, corpid, forage_id, aigc_model_id, sample_ratio, 
                             num_records, is_distinct, primary_key, prompt, user_id, user_name):
    """
    异步数据合成
    """
    forage_id = str(forage_id)
    num_records = int(num_records)
    meta = {"current": 0, 'rate':0, "total": 1, 'status': 'PENDING', 'message': '', 'data': {}, 'exc_type': '', 'next_task_id': '',
            'exc_message': '', 'chunk_list': [], 'has_fail': False, 'cycle_num': 0}
    task_id = task.request.id
    
    controller = ForageController(corpid)
    controller.update_model_forage_info(forage_id, {'structured_gen_state': 1, 'structured_gen_task_id': task_id})

    last_num = controller.get_mongo_structured_len_data(forage_id)
    task_logger.info(f'generate_structured_data: 数据合成开始 {task_id}, 目前个数: {last_num} 需生成: + {num_records}' )
    try:
        meta['rate'] = 5  
        task.update_state(state='PROGRESS', meta=meta)

        stats = DataGenerationStats()
        stats.total_records = remaining_records= num_records
        
        if aigc_model_id is None:
            forage_info = controller.get_model_forage_info(forage_id)
            model_id = forage_info['aigc_model_id']
        else:
            model_id = aigc_model_id

        if prompt:
            template_data = prompt
        else:
            template_data = controller.get_template_data(forage_id,sample_ratio)
            if not template_data:
                raise Exception('模板数据为空')

        
        sys_prompt = """
                     你是一个数据生成助手，请根据给定的模板生成符合格式的随机数据。只返回Python列表格式的数据，不要包含任何其他文字。
                     要求：
                        1. 生成的数据必须与模板格式完全一致，时间字段统一转换为字符串
                        2. 返回格式必须是Python列表格式
                        3. 所有字段类型必须与模板一致
                        4. 使用英文标点符号
                        5. 不要包含任何额外的说明文字，只返回列表
                        6. 确保生成的数据不重复，且不得与模板数据或数据库已有数据重复
                        7. 若结果为空数据，返回为 []
                        8. 结果不用返回字段：structured_data_id
                        9. 请确保生成的数据具有多样性，避免模式化生成
                        10. 对于数值型字段，请使用合理的随机范围
                        11. 对于文本型字段，请使用不同的表达方式
                        12. 对于日期型字段，请使用不同的时间点
                        13. 请避免使用模板数据中的具体值
                        14. 生成的数据要符合业务逻辑和常识
                        示例返回格式：
                        [
                            {{"字段1": "值1", "字段2": "值2"}},
                            {{"字段1": "值3", "字段2": "值4"}},
                            ...
                        ]
                     """
        chat = ChatViewController(model_id, corp_id=corpid, old_flag=False,prompt=sys_prompt)
        meta['total'] = num_records
        meta['rate'] += 5  
        task.update_state(state='PROGRESS', meta=meta)
        

        def generate_data(remaining_records):
            batch_index = 1
            batch_size = 10
            total_batches = (num_records + batch_size - 1) // batch_size  # Calculate the total number of batches
            while remaining_records > 0:
                current_batch_size = min(remaining_records, batch_size)
                # 调用大模型生成数据
                res = controller.ai_generate_structured_data(stats, chat, num_records, current_batch_size, 
                                            batch_index, template_data, forage_id, user_id, user_name, primary_key)
            
                if res['code'] == 0:
                    remaining_records -= res['num']
                    batch_index += 1
                    generated_batches = batch_index - 1
                    generated_progress = (generated_batches / total_batches) * 80
                    meta["rate"] = min(90, int(10 + generated_progress))  # 确保rate不超过100
                    meta["current"] += res['num']
                    meta["message"] = f"Successfully generated {num_records - remaining_records} records"
                    task.update_state(state='PROGRESS', meta=meta)
                else:
                    meta["status"] = 'FAILURE'
                    meta["message"] = f"Failed to generate data at batch {batch_index}, error code: {res['code']}"
                    meta["has_fail"] = True
                    task.update_state(state='FAILURE', meta=meta)
                    controller.update_model_forage_info(forage_id, {'structured_gen_state': 4, 'structured_gen_task_id': ''})
                    return meta
            # 检查是否需要去重
            after_generate_num = controller.reinsert_structured_after_dup(forage_id,primary_key,is_distinct)
            return after_generate_num

        after_generate_num = generate_data(remaining_records)
        if isinstance(after_generate_num, dict): # 失败时，返回的meta
            return after_generate_num
        
        generate_num = after_generate_num - last_num # 实际生成数量

        # 如果生成数量不足，继续生成
        meta['cycle_num'] = 1
        max_cycles = 10  # 最大循环次数
        while generate_num < num_records and meta['cycle_num'] <= max_cycles:
            remaining_records = num_records - generate_num # 剩余需要生成的数量
            task_logger.info(f'forage_id: {forage_id} 循环 {meta["cycle_num"]} 次 剩余需要生成的数量: {remaining_records}')
            after_generate_num = generate_data(remaining_records)
            if isinstance(after_generate_num, dict): # 失败时，返回的meta
                return after_generate_num
            generate_num = after_generate_num - last_num
            meta['cycle_num'] += 1
            meta["rate"] = min(100, int(10 + (generate_num / num_records) * 90))  # 确保rate不超过100
            task.update_state(state='PROGRESS', meta=meta)

        if generate_num >= num_records:
            meta["rate"] = 100
            meta["message"] = "Data generation and deduplication completed"
            meta["status"] = 'SUCCESS'
            controller.update_model_forage_info(forage_id, {'structured_gen_state': 2, 'structured_gen_task_id': ''})
            task_logger.info(f'forage_id: {forage_id} 数据合成完成 : {stats.to_dict()}')
        else:
            meta["status"] = 'FAILURE'
            meta["message"] = f"Failed to generate enough records after {max_cycles} cycles"
            meta["has_fail"] = True
            task.update_state(state='FAILURE', meta=meta)
            controller.update_model_forage_info(forage_id, {'structured_gen_state': 4, 'structured_gen_task_id': ''})
            task_logger.error(f'generate_structured_data: 数据合成失败 {task_id}, 无法生成足够的记录')
        
    except Exception as e:
        meta["status"] = 'FAILURE'
        meta["exc_type"] = type(e).__name__
        meta["exc_message"] = str(e)
        meta["has_fail"] = True
        task.update_state(state='FAILURE', meta=meta)
        controller.update_model_forage_info(forage_id, {'structured_gen_state': 4, 'structured_gen_task_id': ''})
        task_logger.error(f'generate_structured_data: 数据合成失败 {task_id}, error: {e}', exc_info=True)

    task.update_state(state=meta["status"], meta=meta)
    return meta