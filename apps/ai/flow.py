from controller.knowledge_controller import <PERSON><PERSON><PERSON><PERSON><PERSON>
from controller.app_controller import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from controller.chatbi_controller import <PERSON><PERSON><PERSON>C<PERSON>roll<PERSON>
from controller.app_controller import <PERSON>AppController
from flask_restful import Resource, reqparse
from flask import stream_with_context, Response, request
from horticulture.auth_token import login_check
from horticulture.validate import json_response
from apps.ai import task
import json, uuid


class QARetView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('message', type=str, location='json', help='消息', required=True)
        parser.add_argument('min_score', type=float, required=False, default=1.9)
        parser.add_argument('app_id', type=str, help='应用ID', required=False)

        args = parser.parse_args()
        question_str = args['message']
        controller = AppController()
        ret = controller.search_es_gt_demo_qa(question_str, min_score=args['min_score'], app_id=args['app_id'])
        return json_response(ret)


class QARet2View(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('message', type=str, location='json', help='消息', required=True)
        parser.add_argument('min_score', type=float, required=False, default=1.9)
        parser.add_argument('aigc_qa_libs', type=list, help='QAids', required=True, location='json')

        args = parser.parse_args()
        question_str = args['message']
        controller = KnowledgeController()
        ret = controller.search_es_gt_demo_knowledge(question_str, min_score=args['min_score'],
                                                     qa_list=args['aigc_qa_libs']).get('answer')
        return json_response(ret)


class KnowledgeRetView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('message', type=str, location='json', help='消息', required=True)
        parser.add_argument('min_score', type=float, required=False, default=1.9)
        parser.add_argument('app_id', type=str, help='应用ID', required=True)
        parser.add_argument('prompt_list', type=list, help='应用提示词', required=False, default=[], location='json')

        args = parser.parse_args()
        controller = AppController()
        app_info = controller.get_ai_app_info(args['app_id'])
        ret = controller.set_first_message(task, args['prompt_list'], args['message'], app_info)
        return json_response(ret.get('content'))


class KnowledgeRet2View(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('message', type=str, location='json', help='消息', required=True)
        parser.add_argument('min_score', type=float, required=False, default=1.9)
        parser.add_argument('size', type=int, required=False, default=5)
        parser.add_argument('knowledge_ids', type=list, help='知识库', required=True, location='json')
        parser.add_argument('is_graph', type=int, required=False)
        parser.add_argument('is_mixture', type=int, required=False)
        parser.add_argument('digest_size', type=int, required=False, default=10)
        parser.add_argument('digest_score', type=float, required=False, default=1.9)
        parser.add_argument('is_rerank', type=int, required=False, default=0)
        parser.add_argument('rerank_size', type=int, required=False, default=50)
        parser.add_argument('global_percent', type=int, required=False, default=50)
        parser.add_argument('ignore_score', type=float, required=False, default=0.5)

        args = parser.parse_args()
        ret = AppController().set_flow_session_message(None, args['message'], args['knowledge_ids'], args['min_score'], args['size'] , args)
        return json_response(ret)


class AiAppChat(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('message', type=str, location='json', help='消息', required=True)
        parser.add_argument('app_id', type=str, help='应用ID', required=True)

        args = parser.parse_args()
        controller = AppController()
        app_info = controller.get_ai_app_info(args['app_id'])

        all_answer = ''
        for item, _ in controller.ai_app_chat(args['message'], [], app_info['aigc_type_id'], app_info['model_path'],
                                              model_id=app_info['aigc_model_id']):
            all_answer += item or ''

        return json_response(all_answer)


class AiAppChat2(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('message', type=str, help='信息', required=True)
        parser.add_argument('model_id', type=str, help='模型id', required=True)
        parser.add_argument('old_content', type=list, help='历史对话', required=False, default=[], location='json')
        parser.add_argument('prompt_list', type=list, help='提示词', required=False, default=[], location='json')

        args = parser.parse_args()
        controller = AppController()
        # app_info = controller.get_model_info(args['model_id'])

        @stream_with_context
        def generate():
            for item, _ in controller.ai_app_chat(args['message'], args['old_content'], "", "", True, args['model_id']):
                data = json.dumps({'message': item}, ensure_ascii=False)
                yield f"data: {data}\n\n"

        return Response(generate(), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'
        })


class AnalyseFileView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('files', type=list, help='文件链接', required=False, default=[], location='json')

        args = parser.parse_args()
        ret = KnowledgeController().document_splitter_v1(args['files'][0])
        if ret['code'] == 0:
            # 新旧版本 数据结构变动
            data = ret['data']
            if type(data) is str:
                ret = data
            else:
                ret = '\n'.join([x.get('content', '') for x in data])
            return json_response(ret)
        else:
            raise ValueError(ret.get('message'))


class ReRankView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('message', type=str, location='json', help='消息', required=True)
        parser.add_argument('knowledge_ids', type=list, help='知识库', required=True, location='json')
        parser.add_argument('mini_score', type=float, required=False, default=1.9)
        parser.add_argument('call_size', type=int, required=False, default=5)
        parser.add_argument('rerank_size', type=int, required=False, default=50)
        parser.add_argument('global_percent', type=int, required=False, default=50)
        parser.add_argument('ignore_score', type=float, required=False, default=0.5)
        args = parser.parse_args()
        controller = AppController()
        _, ret = controller.search_and_rerank_lib(None, args['message'], args['knowledge_ids'], args['mini_score'],
                                                  args['call_size'], args['global_percent'], args['rerank_size'],
                                                  args['ignore_score'])
        return json_response(ret)


class AppFlowSession(Resource):
    @login_check
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument('message', type=str, location='json', help='消息', required=True)
        parser.add_argument('session_id', type=str, help='会话id', required=False)
        parser.add_argument('app_id', type=str, help='应用ID', required=True)
        parser.add_argument('flow_id', type=str, required=False)
        args = parser.parse_args()
        flow_id = args['flow_id'] or str(uuid.uuid1())
        question = args['message']
        session_id = args['session_id']

        no_ret_keys = ['抱歉', '对不起']

        run_func, message_id, session_id = FlowAppController().work_flow_run(task, args['app_id'], flow_id, question, session_id)

        @stream_with_context
        def generate():
            all_answer = ''
            for answer, final_images_list, hit_doc_list, doc_images_list, position, node_type in run_func():
                all_answer = answer
                answer = answer.replace('\\r', '\n\n').replace('\r', '\n\n').replace('""', '').replace('" "', '')
                if position == 'doing':
                    if answer:
                        data = json.dumps(
                            {'content': answer, 'message_id': str(message_id), 'session_id': session_id,
                             'is_send': False, 'role': 'assistant', 'type': 'message'}, ensure_ascii=False)
                        yield f"data: {data}\n\n"
                    else:
                        item = f"抱歉，针对这个问题还没具备对应的准确内部知识。"
                        data = json.dumps(
                            {'content': item, 'message_id': str(message_id), 'session_id': session_id, 'is_send': False,
                             'role': 'assistant', 'type': 'message'}, ensure_ascii=False)
                        yield f"data: {data}\n\n"

                elif position == 'end':
                    # 模型推理之后，关联信息响应
                    if node_type == 'r-model':
                        if hit_doc_list:
                            data = json.dumps(
                                {'content': '', 'hit_content': {'hit_list': hit_doc_list}, 'message_id': str(message_id),
                                 'session_id': session_id, 'is_send': False, 'role': 'assistant',
                                 'type': 'message'}, ensure_ascii=False)
                            yield f"data: {data}\n\n"

                        if final_images_list:
                            data = json.dumps(
                                {'content': '', 'image_list': final_images_list, 'message_id': str(message_id),
                                 'session_id': session_id, 'is_send': False, 'role': 'assistant',
                                 'type': 'message'}, ensure_ascii=False)
                            yield f"data: {data}\n\n"

                        if doc_images_list:
                            # 返回带文档名称的命中图片
                            data = json.dumps(
                                {'content': '', 'doc_image_list': doc_images_list, 'message_id': str(message_id),
                                 'session_id': session_id, 'is_send': False, 'role': 'assistant',
                                 'type': 'message'}, ensure_ascii=False)
                            yield f"data: {data}\n\n"

            data = json.dumps(
                {'content': '', 'message_id': str(message_id), 'session_id': session_id, 'is_send': True,
                 'role': 'assistant', 'type': 'message'}, ensure_ascii=False)
            yield f"data: {data}\n\n"

            data = json.dumps(
                {'content': '', 'message_id': str(message_id), 'session_id': session_id, 'is_send': True,
                 'role': 'assistant', 'type': 'suggestion', 'sug_status': True}, ensure_ascii=False)
            yield f"data: {data}\n\n"

        return Response(generate(), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'
        })


class AiFlowAppView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('app_id', type=str, required=False)
        parser.add_argument('app_name', type=str, required=True)
        parser.add_argument('app_desc', type=str, required=False)
        parser.add_argument('icon_url', type=str, required=False)
        parser.add_argument('welcome_content', type=str, default='')
        parser.add_argument('scope', type=str, required=False, default=0)
        parser.add_argument('parent_directory_id', type=str, required=True)
        parser.add_argument('flow_version_ids', type=list, required=True, location='json')
        parser.add_argument('run_mode', type=int, required=True)
        parser.add_argument('session_log', type=int, required=False, default=0)
        parser.add_argument('session_no', type=int, required=False, default=10)
        parser.add_argument('auth_scheme_id', type=str, required=False)
        parser.add_argument('classify_prompt', type=str, required=False)
        parser.add_argument('need_suggestion', type=int, required=False, default=1)
        args = parser.parse_args()
        controller = AppController()
        app_id = controller.check_app_name(args['app_name'])
        if app_id and str(app_id) != args['app_id']:
            return json_response(code='FAIL', err_msg='应用名称已存在！')

        app_id = args['app_id']
        flow_version_ids = args.pop('flow_version_ids')
        if not app_id:
            args['tp_user_id'] = request.user['tp_user_id']
            app_id = controller.model.create_app(**args)
        else:
            controller.model.update_app(**args)
        controller.up_app_rel_flow_app(app_id, flow_version_ids)
        return json_response(data={'app_id': app_id})

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('app_id', type=str, required=True, location='args')
        args = parser.parse_args()
        controller = AppController()
        data = controller.get_flow_app_detail(args['app_id'])
        return json_response(data)
