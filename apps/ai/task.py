# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: task.py
# @Author: <PERSON>LiF<PERSON>
# @E-mail: <EMAIL>
# @Site: 
# @Time: 4月 01, 2024
# ---
from ast import List
import asyncio
import asyncio
import copy
import datetime
from email import message
import json
import time
import json
from functools import wraps
from concurrent.futures import ThreadPoolExecutor

from flask import current_app

from apps.ai.analyzer.cluster.configs import TEMP_CORP_ID
from apps.ai.analyzer.cluster.launcher import ClusterMonitorLauncher
from modeler.mongo.knowledge_mg import LogMg

import pandas as pd
import requests

from apps.graphrag.graphrag import GraphRag
from apps.graphrag.configs import ENTITY_TYPES, OTHER_TYPE, SPACE_PREFIX, EDGE_TYPE, VDB_ENTITIES, \
    DEFAULT_LOCAL_EMB_MODEL_PATH_GRAPH
from lib_func.const_map import *
from bson import ObjectId
from celery_app import celery
from puppet.graph_server_sdk import GraphServerSdk
from lib_func.logger import task_logger
from controller.app_controller import App<PERSON><PERSON>roller
from controller.manager_controller import MasterAction
from controller.knowledge_controller import KnowledgeController, GraphStatus, GalaxyStatus
from controller.rule_controller import RuleController
from controller.question_controller import QuestionController
from controller.content_info_controller import ContentInfoController
from controller.forage_controller import ForageController
from controller.qa_controller import QaController
from controller.chat_controller import ChatController
from utils.tools import get_snowflake_id, timestamp_to_dt_str, request_description, async_request_description, is_continuous_table, combine_tables
from puppet.cache import redis_pool
import requests
import settings
from img2table.ocr import TesseractOCR
from img2table.document import Image,PDF
import csv
import os
import urllib
import cv2
import logging
from module import minio_util
from docx import Document
import subprocess
from utils.tools import check_args
from lib_func.type_map import ForageCleanTypeMap, ForageFilterTypeMap, ForageDeduplicationTypeMap
import re
import hashlib
from sentence_transformers import util
from modeler.mysql.qa_orm import ForageOrm

@celery.task(bind=True, name="apps.ai.task.async_document_splitter")
def async_document_splitter(task, corpid, knowledge_id, doc_info):
    """
    异步文档分词
    :param task:
    :param corpid:
    :param knowledge_id:
    :param doc_info:
    :return:
    """
    meta = {"current": 0, "total": 1, 'state': '', 'message': '', 'data': {}, 'exc_type': '',
            'exc_message': '', 'chunk_list': []}
    state = 'PENDING'
    task_id = task.request.id
    task_logger.info('async_document_splitter: 分词切片开始 %s' % task_id)
    try:
        doc_list = doc_info['doc_list']
        chunk_size = doc_info['chunk_size']
        chunk_overlap = doc_info['chunk_overlap']
        knowledge_controller = KnowledgeController(corpid)
        total = len(doc_list)
        success = 0
        for doc in doc_list:
            knowledge_controller.update_knowledge_doc(doc['doc_id'], {'status': 'RUNNING'})
            if doc['doc_type'] == 'text':
                res = {
                    'code': 0,
                    'data': [
                        {
                            'content': doc['content'],
                            'character_count': doc['character_count']
                        }
                    ]
                }
            else:
                res = knowledge_controller.document_splitter_v1(doc['doc_url'], chunk_size=chunk_size, chunk_overlap=chunk_overlap)
            task_logger.info('async_document_splitter: %s' % res)
            if res['code'] == 0 and len(res['data']) > 0:

                for chunk in res['data']:
                    knowledge_controller.add_knowledge_doc_chunk(doc['doc_id'], chunk['content'], chunk['character_count'], 'TODO')
                knowledge_controller.update_knowledge_doc(doc['doc_id'],
                                                          {'status': 'FINISHED', 'chunk_size': len(res['data']), 'result': 'TODO'})

                success += 1
                if total > 0:
                    meta['current'] = int(success / total * 100)
                else:
                    meta['current'] = 0
                meta['data'].update({str(doc['doc_id']): 'SUCCESS'})
                meta['chunk_list'].append({
                    'doc_id': doc['doc_id'],
                    'doc_name': doc.get('doc_name'),
                    'state': 'SUCCESS',
                    'chunk_list': knowledge_controller.get_knowledge_doc_chunk_list(doc['doc_id'], {'is_all': 'all'})
                })
            else:
                meta['data'].update({str(doc['doc_id']): 'FAILURE'})
                meta['chunk_list'].append({
                    'doc_id': doc['doc_id'],
                    'doc_name': doc.get('doc_name'),
                    'state': 'FAILED',
                    'chunk_list': {'data_list': []}
                })
                knowledge_controller.update_knowledge_doc(doc['doc_id'], {'status': 'FAILED', 'result': 'TODO'})
            task.update_state(state=state, meta=meta)
            # 延迟0.5s
            time.sleep(0.5)

        state = 'SUCCESS'

        task.update_state(state=state, meta=meta)
        knowledge_controller.update_knowledge_run_record({'task_id': task_id, 'knowledge_id': knowledge_id},
                                                         {'task_id': task_id, 'knowledge_id': knowledge_id,
                                                          'chunk_status': state, 'update_time': int(time.time()),
                                                          'doc_data': meta['data']})
        task_logger.info(f"async_document_splitter: task_id: {task_id}, knowledge_id: {knowledge_id}, state: {state}, meta: {meta}")
        
    except Exception as e:
        task_logger.error('async_document_chunk_embeddings:【%s】切片失败 %s' % (corpid, e))
        meta.update({'message': str(e)})
        state = 'FAILURE'
        task.update_state(state=state, meta=meta)
        knowledge_controller.update_knowledge_run_record({'task_id': task_id, 'knowledge_id': knowledge_id},
                                                         {'task_id': task_id, 'knowledge_id': knowledge_id, 'chunk_status': state,
                                                          'update_time': int(time.time()), 'doc_data': meta['data']})

@celery.task(bind=True, name="apps.ai.task.async_document_chunk_clear_v1")
def async_document_clear_v1(task, corpid, doc_list, aigc_model_id, knowledge_id, doc_ids, rule_list=None):
    """
    文档清洗
    params:
        knowledge_id: 知识库id
        doc_ids: 文档id列表
        rule_ids: 规则id列表
        aigc_model_id: aigc模型id
    """ 
    knowledge_controller = KnowledgeController(corpid)  # 或者按需初始化
    knowledge_info = knowledge_controller.get_knowledge_detail(knowledge_id)
    # 知识库信息不存在，返回
    if not knowledge_info:
        meta['message'] = "知识库信息不存在"
        task.update_state(state='SUCCESS', meta=meta)
        return


    meta = {"current": 0, "total": 1, 'success': 0, 'state': '', 'message': '', 'data': {}, 'exc_type': '',
            'exc_message': '','clear_list':[]}

    state = 'PENDING'
    task_logger.info(f'开始清洗【{corpid}】【{task}】【{knowledge_id}】')
    
    # 获取知识库信息
    knowledge_controller = KnowledgeController(corpid)
    rule_controller = RuleController(corpid)
    

    # 获取文档列表
    doc_list = knowledge_controller.get_knowledge_doc_list(knowledge_id, {'is_all': 'all', 'doc_ids': doc_ids})
    task_logger.info(f"doc_list:{doc_list}")
    total = len(doc_list['data_list'])
    success = 0
    # 如果没有规则，结束任务
    if not rule_list:
        meta['message'] = "未指定清洗规则"

        task.update_state(state='SUCCESS', meta=meta)
        return
    # 获取清洗规则
    task_logger.info(f"rule_list:{rule_list}")
    if isinstance(rule_list, dict):
        rule_ids_text = rule_list.get("doc", [])
        rule_ids_excel = rule_list.get("excel", [])
    else:
        rule_ids_text = []
        rule_ids_excel = []
    # 获取AIGC模型信息
    task_logger.info(f"rule_ids_text:{rule_ids_text}, rule_ids_excel:{rule_ids_excel}")

    try:
        aigc_model_info = AppController(corpid).get_model_info(aigc_model_id)
        aigc_type_id, model_path, model_id = aigc_model_info['aigc_type_id'], aigc_model_info['model_path'], aigc_model_info['aigc_model_id']
    except Exception as e:
        task_logger.error(f'获取AIGC模型信息失败: {e}')
        meta['message'] = f'获取AIGC模型信息失败: {e}'
        state = 'FAILURE'
        task.update_state(state=state, meta=meta)
        return

    # 处理文档并进行清洗
    try:
        for doc in doc_list['data_list']:
            doc_id = doc['document_id']
            doc_name = doc['doc_name']
            doc_url = doc['doc_url']
            doc

            task_logger.info(f"doc_id:{doc_id}, rule_ids_text:{rule_ids_text}, rule_ids_excel:{rule_ids_excel}")
            # 判断文件类型并关联规则
            if doc_name:
                doc_name = doc_name.split("_", 1)[-1]
                if doc_name.lower().endswith(( ".docx", ".txt", ".pdf", ".ppt")):
                    knowledge_controller.add_file_relation_rule_v1(doc_id, rule_ids_text) 
                elif doc_name.lower().endswith((".xlsx", ".xls")):
                    knowledge_controller.add_file_relation_rule_v1(doc_id, rule_ids_excel)
            # 查询文档清洗规则
            rules = knowledge_controller.get_file_relation_rule(doc_id)
            task_logger.info(f"rules:{rules}")
            # 执行清洗操作
            try:
                task_logger.info(f"开始清洗文档：{doc_id}")
                KnowledgeController(corpid).update_knowledge_doc(doc['document_id'], {'current_node': '文档清洗','state':'进行中'})
                clear_url = rule_controller.do_clear_action_v1(task.request.id, aigc_type_id, model_path, doc_id, doc_url,rules,model_id=model_id ,kid=knowledge_id)
                success += 1
                if total > 0: 
                    meta['current'] = int(success / total * 100)
                else:
                    meta['current'] = 100
                meta['total'] = total
                meta['success'] = success
                meta['clear_list'].append({
                    'doc_id': doc_id,
                    'doc_name': doc_name,
                    'clear_url': clear_url,
                    'clear_content': "",
                    'state': 'SUCCESS'
                })
                task_logger.info(f'async_document_chunk_clear: total:{total}, success: {success}, current: {meta["current"]}')
                KnowledgeController(corpid).update_knowledge_doc(doc['document_id'], {'current_node': '文档清洗','state':'已完成'})

            except Exception as e:
                task_logger.exception(f'async_document_chunk_clear: 清洗失败【{corpid}】【{doc_id}】{e}')
                KnowledgeController(corpid).update_knowledge_doc(doc['document_id'], {'current_node': '文档清洗','state':'失败'})

            # 每处理一文档，更新任务状态
            task.update_state(state=state, meta=meta)
        
        meta['current'] = 100
        state = 'SUCCESS'
        task.update_state(state=state, meta=meta)
    except Exception as e:
        task_logger.exception(f'async_document_chunk_clear_v1: 清洗失败【{corpid}】【{knowledge_id}】{e}')
        KnowledgeController(corpid).update_knowledge_doc(doc['document_id'], {'current_node': '文档清洗','state':'失败'})
        meta.update({'message': str(e)})
        state = 'FAILURE'
        task.update_state(state=state, meta=meta)


@celery.task(bind=True, name="apps.ai.task.chat_call_generate_async")
def chat_call_generate_async(task, corpid, task_id, **kwargs):
    """
    异步  目前仅支持：文生图，图生图
    :param task:
    :param corpid:
    :param task_id:
    :param doc_info:
    :return:
    """
    i = 0
    task_logger.info(f'开始异步生成图片 【{corpid}】【{task}】【{task_id}】')
    redis_client = redis_pool.use(redis_pool.aigc_pool)
    controller = ChatController(corpid)
    view_id = kwargs['view_id']
    task_key = AI_TASK_GENERATE_ASYNC_PREFIX + task_id
    try:
        if view_id in [IMG_TO_IMG_ID_1]:
            for doc in controller.img_generate(**kwargs):
                doc = json.loads(doc)
                if doc['status'] == FINISHED:
                    redis_client.publish(task_key, json.dumps(doc))
        elif view_id in [TEXT_TO_IMG_ID]:
            for doc in controller.img_generate_v1(**kwargs):
                doc = json.loads(doc)
                if doc['status'] == FINISHED:
                    redis_client.publish(task_key, json.dumps(doc))

    except Exception as err:
        task_logger.exception(err)
        error_message = {"session_id": '', "status": "finished", "content": "解析失败：{} {}".format(task_id, str(err)),
                        "contentType": "text"}
        redis_client.publish(task_key, json.dumps(error_message))



@celery.task(bind=True, name="apps.ai.task.async_document_splitter_v1")
@check_args
def async_document_splitter_v1(task, corpid, knowledge_id, doc_info, slice_model):
    """
    异步文档分词
    :param task:
    :param corpid:
    :param knowledge_id:
    :param doc_info:
    :return:
    """
    meta = {"current": 0, "total": 1, 'state': '', 'message': '', 'data': {}, 'exc_type': '',
            'exc_message': '', 'chunk_list': [], 'has_fail': False}
    state = 'PENDING'
    task_id = task.request.id
    task_logger.info('async_document_splitter: 分词切片开始 %s' % task_id)
    try:
   
        doc_list = doc_info['doc_list']
        chunk_size = doc_info['chunk_size']
        chunk_overlap = doc_info['chunk_overlap']
        pdf_model = doc_info.get('pdf_model', 0)
        knowledge_controller = KnowledgeController(corpid)
     
        total = len(doc_list)
        success = 0

        for doc in doc_list:
            
            try:
                doc_name = doc.get('doc_name').split("_", 1)[-1]
            except Exception as e:
                task_logger.error(f"doc_name error: {e}")
                doc_name = doc['doc_name']
            
            task_logger.info(f"开始处理文档：{doc_name}\n Doc info: {doc}")
            
            knowledge_controller.update_knowledge_doc(doc['doc_id'], {'status': 'RUNNING'})

            # 处理非文本类型的文档
            res = knowledge_controller.document_splitter_v1(doc['doc_url'], chunk_size=chunk_size, chunk_overlap=chunk_overlap, pdf_model=pdf_model, slice_model=slice_model)
            
            task_logger.info('async_document_splitter: %s' % res)

            if res['code'] == 0 and len(res['data']) > 0:
                images = res.get('images', [])
                description_str = ''
                for i, chunk in enumerate(res['data']):
                    chunk_id = knowledge_controller.add_knowledge_doc_chunk(
                        doc['doc_id'], chunk['content'], chunk['character_count'], 'TODO'
                    )
                    if images:
                        # 将图片与chunk_id关联
                        task_logger.info(f"图片关联 to db：{chunk_id} - {images[i]}")
                        if images[i]:
                            knowledge_controller.add_chunk_images(chunk_id, images[i])

                    if chunk.get('doc_img_url'):  # 将文档图片与chunk_id关联
                        # task_logger.info('chunk===', chunk)
                        task_logger.info('async_document_splitter: chunk %s' % chunk)
                        # 单个图片
                        knowledge_controller.add_chunk_image(chunk_id, chunk['doc_img_url'])
                    #  取前三段，拼接为描述
                    if i < 3:
                        description_str += chunk['content']

                # 文档摘要
                abstract = None

                knowledge_controller.update_knowledge_doc(doc['doc_id'],
                                                          {'status': 'FINISHED', 'chunk_size': len(res['data']), 'result': 'TODO',
                                                           'abstract': abstract})

                success += 1
                meta['current'] = int(success / total * 100)
                meta['data'].update({str(doc['doc_id']): 'SUCCESS'})
                data_dict = knowledge_controller.get_knowledge_doc_chunk_list(doc['doc_id'], {'is_all': 'all'})
                data_list = data_dict.get('data_list')
                task_logger.info('-------------async_document_splitter: %s' % data_list)
         
                meta['chunk_list'].append({
                    'doc_id': doc['doc_id'],
                    # 'doc_name': doc.get('doc_name'),
                    'doc_name': doc_name,
                    'state': 'SUCCESS',
                    # 'chunk_list': data_list
                    'chunk_list': knowledge_controller.get_knowledge_doc_chunk_list(doc['doc_id'], {'is_all': 'all'})
                })
            else:
                meta['data'].update({str(doc['doc_id']): 'FAILURE', 'has_fail': True})
                meta['chunk_list'].append({
                    'doc_id': doc['doc_id'],
                    'doc_name': doc_name,
                    'state': 'FAILED',
                    'chunk_list': {'data_list': []}
                })
                knowledge_controller.update_knowledge_doc(doc['doc_id'], {'status': 'FAILED', 'result': 'TODO','current_node':'文档切片','state':'失败'})
            task_logger.info('切片完成, doc_id: %s' % doc['doc_id'])
            task.update_state(state=state, meta=meta)
            # time.sleep(0.5)

        state = 'SUCCESS'
        task.update_state(state=state, meta=meta)
        knowledge_controller.update_knowledge_run_record(
            {'task_id': task_id, 'knowledge_id': knowledge_id},
            {'task_id': task_id, 'knowledge_id': knowledge_id, 'chunk_status': state, 'update_time': int(time.time()),
             'doc_data': meta['data']}
        )
        return meta

    except Exception as e:
        task_logger.error('async_document_splitter:【%s】切片失败 %s' % (corpid, e), exc_info=True)
        meta.update({'message': str(e)})
        state = 'FAILURE'
        task.update_state(state=state, meta=meta)
        knowledge_controller.update_knowledge_run_record(
            {'task_id': task_id, 'knowledge_id': knowledge_id},
            {'task_id': task_id, 'knowledge_id': knowledge_id, 'chunk_status': state, 'update_time': int(time.time()),
             'doc_data': meta['data']}
        )

@celery.task(name="apps.ai.task.report_progress")
def report_progress(task,task_id, progress_value):
    redis_client = redis_pool.use(redis_pool.aigc_pool)
    redis_client.set(f'progress_{task_id}', progress_value)

@celery.task(bind=True, name="apps.ai.task.async_video_connector")
def async_video_connector(task,data, corpid, knowledge_id, doc_id):
    task_logger.info(f"async_video_connector: {data}")
    iamge_list = data.get('image_list', [])
    frame_list = []
    for image in iamge_list:
        frame_list.append({
            "frame_num": 0,
            "img_url": image.get('image_url',""),
            "audio_start": 0,
            "audio_end": 1000,
            "audio_text": "",
            "frame_time": 0
        })   
    task_logger.info(f"async_video_connector frame_list: {frame_list}")
    return corpid, knowledge_id, doc_id,frame_list


@celery.task(bind=True, name="apps.ai.task.async_frame_connector")
def async_frame_connector(task, data, corpid, knowledge_id, doc_id):
    doc_ids = []
    chunk_ids = []
    doc_chunk_list = KnowledgeController(corpid).get_knowledge_doc_chunk_list(doc_id, {'is_all': 'all'})
    for doc in doc_chunk_list['data_list']:
        doc_ids.append(doc['doc_id'])
        chunk_ids+= map(lambda x: x.get('chunk_id'), doc.get('chunk_list', {}).get('data_list', []))  
    return corpid, knowledge_id, doc_ids,chunk_ids


@celery.task(bind=True, name="apps.chat.link")
def async_split_connector_emb(task,data, corpid, knowledge_id):
    task_logger.info(data)
    doc_ids = []
    chunk_ids = []
    for doc in data.get('chunk_list', []):
        doc_ids.append(doc['doc_id'])
        chunk_ids+= map(lambda x: x.get('chunk_id'), doc.get('chunk_list', {}).get('data_list', []))  
 
    return corpid, knowledge_id, doc_ids, chunk_ids

@celery.task(bind=True, name="apps.ai.task.async_audio_split_connector")
def async_audio_split_connector(task,data, corpid, knowledge_id, doc_info, slice_model):
     # 检查 data 是否为字典且包含'results' 键
    if not isinstance(data, dict) or'results' not in data:
        task_logger.error(f"Expected 'data' to be a dictionary with'results' key, but got {type(data).__name__}")
        return corpid, knowledge_id, doc_info, slice_model
    
    results = data['results']
    for doc_data in results:
        if not isinstance(doc_data, dict):
            task_logger.error(f"Expected elements in'results' to be dictionaries, but got {type(doc_data).__name__}")
            continue
        try:
            doc_id = doc_data['doc_id']
            doc_clear_url = doc_data['doc_clear_url']
            for doc in doc_info['doc_list']:
                if doc['doc_id'] == doc_id:
                    doc['doc_url'] = doc_clear_url
                    doc['doc_type'] = 'md'
                    doc['doc_name'] = f"audio_{doc['doc_name']}.md"
        except KeyError as e:
            task_logger.error(f"KeyError: {e} not found in doc_data: {doc_data}")

    return corpid, knowledge_id, doc_info, slice_model


def handle_doc_failure(doc, meta, state, task, knowledge_controller, error_msg):
    meta['data'].update({str(doc['doc_id']): 'FAILURE', 'has_fail': True})
    meta['chunk_list'].append({
        'doc_id': doc['doc_id'],
        'doc_name': doc.get('doc_name'),
        'fail_msg': error_msg,
        'state': 'FAILED',
        'chunk_list': {'data_list': []}
    })
    knowledge_controller.update_knowledge_doc(doc['doc_id'], {'status': 'FAILED', 'result': 'TODO','current_node':'文档切片','state':'失败'})
    try:
        task.update_state(state=state, meta=meta)
    except Exception as e:
        task_logger.error(f"更新任务状态时出错: {e}")


def process_document_chunks(res, doc, corpid, meta, state, task):
    images = res.get('images', [])
    description_str = ''
    data_list = []
    is_insert_images_index = False
    knowledge_controller = KnowledgeController(corpid)
    try:
        for i, chunk in enumerate(res['data']):
            data_dict = {
                'doc_id': doc.get('doc_id', ''),
                'content': chunk.get('content', ''),
                'character_count': chunk.get('character_count', 0),
                'result': 'TODO',
                'image_index': chunk.get("image_index", -1),
                'position': chunk.get("position",i)
            }
            data_list.append(data_dict)
            if chunk.get("image_index"):
                is_insert_images_index = True

            if i < 3:
                description_str += chunk.get('content', '')
        data_list.sort(key=lambda x: x['position'])

        chunk_ids = knowledge_controller.add_knowledge_doc_chunk_in_batch(data_list)
        if not chunk_ids:
            return []

        images_dict_list = []
        if images:
            if is_insert_images_index:
                for chunk_id, data_dict in zip(chunk_ids, data_list):
                    image_index = data_dict['image_index']
                    if image_index != -1 and image_index < len(images):
                        image = images[image_index]
                        task_logger.info(f"图片关联 to db：{chunk_id} - {image}")
                        if image:
                            images_dict_list.append({
                                'chunk_id': chunk_id,
                                'image_urls': image
                            })
            else:
                for chunk_id, image in zip(chunk_ids, images):
                    task_logger.info(f"图片关联 to db：{chunk_id} - {image}")
                    if image:
                        images_dict_list.append({
                            'chunk_id': chunk_id,
                            'image_urls': image
                        })

        if images_dict_list:
            task_logger.info(f"images_dict_list: {images_dict_list}")
            success_images_list = knowledge_controller.add_chunk_images_in_batch(images_dict_list)
            if not success_images_list:
                return []
        image_dict_list = []
        for chunk_id, chunk_dict in zip(chunk_ids, res['data']):
            if chunk_dict.get('doc_img_url'):
                image_dict_list.append({
                    'chunk_id': chunk_id,
                    'image_url': chunk_dict.get('doc_img_url')
                })

        if image_dict_list:
            success_image_list = knowledge_controller.add_chunk_image_in_batch(image_dict_list)
            if not success_image_list:
                return []

        abstract = None
        knowledge_controller.update_knowledge_doc(
            doc['doc_id'],
            {
                'status': 'FINISHED',
                'chunk_size': len(res['data']),
                'result': 'TODO',
                'abstract': abstract,
                'current_node':'文档切片',
                'state':'已完成'
            }
        )
        chunk_list = knowledge_controller.get_knowledge_doc_chunk_list(doc['doc_id'],{'is_all': 'all'})
        return chunk_list
    except Exception as e:
        task_logger.exception(f"插入数据库时出错: {e}")
        handle_doc_failure(doc, meta, state, task, knowledge_controller, str(e))
        return []


@celery.task(bind=True, name="apps.ai.task.async_document_splitter_v2")
def async_document_splitter_v2(task, corpid, knowledge_id, doc_info, slice_model, tp_user_id=None, request_ip=None,
                               usr_prompt="你是一个ocr识别助手，这张图片有什么内容请全部给我"):
    """
    异步文档分词
    :param task:
    :param corpid:
    :param knowledge_id:
    :param doc_info:
    :return:
    """
    meta = {"current": 0, "total": 1, 'state': '', 'message': '', 'data': {}, 'exc_type': '',
            'exc_message': '', 'chunk_list': [], 'has_fail': False}
    state = settings.PENDING
    task_id = task.request.id

    knowledge_controller = KnowledgeController(corpid)

    doc_list = doc_info['doc_list']
    chunk_size = doc_info.get('chunk_size', 256)
    chunk_overlap = doc_info.get('chunk_overlap', 20)
    pdf_model = doc_info.get('pdf_model', 0)
    total = len(doc_list)
    success = 0
    doc_log_list=list()
    is_abstract = doc_info.get('is_abstract', 0)
    aigc_model_id = doc_info.get('aigc_model_id', "")
    is_abstract_num = doc_info.get('is_abstract_num', 999)
    for doc in doc_list:
        doc_logs = {
            'knowledge_id': knowledge_id,
            'doc_id': doc.get("doc_id"),
            'doc_name': doc.get("doc_name"),
            'doc_url': doc.get('doc_url'),
            'doc_size': doc.get('doc_size'),
            'error_msg': '',
            'error_key': '',
            'task_id': task_id,
            'split_each_doc_time': [],
            'doc_images': [],
            'doc_info': doc_info,
        }
        try:
            doc_name = doc.get('doc_name').split("_", 1)[-1]
        except Exception as e:
            task_logger.exception(f"doc_name error: {e}")
            error_msg = "文件名错误"
            handle_doc_failure(doc, meta, state, task, knowledge_controller, error_msg)
            doc_logs['error_msg'] = error_msg
            doc_logs['error_key'] = 'doc_name_error'
            doc_log_list.append(doc_logs)

        task_logger.info(f"开始处理文档：{doc_name}\n Doc info: {doc}")
        separator = doc_info.get('separator', "")
        knowledge_controller.update_knowledge_doc(doc['doc_id'], {'status': 'RUNNING','current_node':'文档切片','state':'进行中'})
        doc_image_list = knowledge_controller.get_doc_image_urls(doc['doc_id'])
        model_path = knowledge_controller.get_model_path(knowledge_id)
        if model_path is None:
            model_path = 'bge-large-zh-v1.5'
        # 处理非文本类型的文档
        try:
            doc_clear_url = knowledge_controller.get_doc_clear_url(doc.get('doc_id'))

            res = knowledge_controller.document_splitter_v1(doc_clear_url, chunk_size=chunk_size,
                                                            chunk_overlap=chunk_overlap, pdf_model=pdf_model, slice_model=slice_model, doc_image_list=doc_image_list,model_path=model_path, tp_user_id=tp_user_id,separator=separator)
        except Exception as e:

            handle_doc_failure(doc, meta, state, task, knowledge_controller, res.get('message',str(e)))
            doc_logs['error_msg'] = res.get('message',str(e))
            doc_logs['error_key'] = 'doc_split_error'
            doc_log_list.append(doc_logs)

        if res['code'] == 0 and len(res['data']) > 0:
            csv_path_list = res.get('csv_path_list', [])
            task_logger.info(f"csv_path_list: {csv_path_list},doc_id: {doc['doc_id']}")
            if csv_path_list:
                try:
                    knowledge_controller.add_knowledge_doc_chunk_csv_in_batch(doc['doc_id'], csv_path_list,[])
                except Exception as e:
                    task_logger.error(f"添加知识库文档切片CSV失败: {e}")
                    handle_doc_failure(doc, meta, state, task, knowledge_controller, str(e))
                    doc_logs['error_msg'] = str(e)
                    doc_logs['error_key'] = 'add_csv_error'
                    doc_log_list.append(doc_logs)
            try:
                chunk_list = process_document_chunks(res, doc, corpid, meta, state, task)
            except Exception as e:
                task_logger.error(f"处理文档切片失败: {e}")
                handle_doc_failure(doc, meta, state, task, knowledge_controller, str(e))
                doc_logs['error_msg'] = str(e)
                doc_logs['error_key'] = 'process_doc_chunk_error'
                doc_log_list.append(doc_logs)
                chunk_list = []
            if chunk_list:
                success += 1
                if total > 0:
                    meta['current'] = int(success / total * 100)
                else:
                    meta['current'] = 0
                meta['data'].update({str(doc['doc_id']): 'SUCCESS'})
                meta['chunk_list'].append({
                    'doc_id': doc['doc_id'],
                    'doc_name': doc_name,
                    'state': 'SUCCESS',
                    'chunk_list': chunk_list
                })
                if is_abstract ==1:
                    content = ''
                    for item in chunk_list['data_list']:
                        content += item['content']
                    message = f"""
                    请从以下文本中总结和提取摘要，尽量详细，返回结果不超过{is_abstract_num}字
                    文本: {content}
                    - 仅返回结果数据
                    """
                    result = []
                    model_info = AppController(corpid).get_model_info(aigc_model_id)
                    task_logger.info(f"model_info: {model_info}")
                    for item, reasoning_content in AppController(corpid).ai_app_chat(message, [], model_info['aigc_type_id'], model_info['model_path'], model_id=model_info['aigc_model_id']):
                        if item is not None:  # 添加这个判断
                            result.append(item)
                    full_result = ''.join(result)
                    task_logger.info(f"full_result11: {full_result}")
                    knowledge_controller.update_knowledge_doc(doc['doc_id'], {'abstract': full_result})

                task_logger.info(f"task_id: {task_id}, 里面的meta: {meta}")
                try:
                    task.update_state(state=state, meta=meta)
                except Exception as e:
                    task_logger.error(f"更新任务状态时出错: {e}")
        else:
            success += 1
            if total > 0:
                meta['current'] = int(success / total * 100)
            else:
                meta['current'] = 0
            handle_doc_failure(doc, meta, state, task, knowledge_controller, res.get('message', ''))
            doc_logs['error_msg'] = res.get('message', '')
            doc_logs['error_key'] = 'chunk_list_empty'
            doc_log_list.append(doc_logs)
            

    state = 'SUCCESS'
    if state is None or meta is None:
        task_logger.error("state 或 meta 为 None，无法更新任务状态")


    try:
        task.update_state(state=state, meta=meta)
    except Exception as e:
        task_logger.error(f"更新任务状态时出错: {e}")
    save_split_logs.delay(corpid, doc_log_list)

@celery.task(bind=True, name="apps.ai.task.save_split_logs")
def save_split_logs(task, corpid, split_logs):
    """
    存储分词日志
    :param task:
    :param corpid:
    :param split_logs:
    """
    knowledge_controller = KnowledgeController(corpid)
    knowledge_controller.document_split_mg.insert_many_document_split_log(split_logs)


async def tables_to_csv(corpid,pdf_path,doc_id,output_folder_path,doc_type,aigc_model_id):
    ocr = TesseractOCR(n_threads=10, lang="chi_sim",psm=11)
    knowledge_controller = KnowledgeController(corpid)
    oss_url = urllib.parse.unquote(pdf_path)
    relative_file_path = oss_url.split(settings.OSS_DOMAIN_NAME)[-1]
    download_path = settings.BASE_DIR+ "/upload/minio" + relative_file_path
    minio_util.download_file_use_requests(oss_url, download_path)
    file_path = download_path
    csv_paths = []
    png_paths = []
    csv_list = []
    csv_dict = { 
        'csv_content': '',
        'csv_url': '',
        'png_url': '',
        'table_len': 0
    }
    base_csv_dict={}
    bucket_name = settings.KNOWLEDGE_OSS_BUCKET_NAME
    if not os.path.exists(output_folder_path):
        os.makedirs(output_folder_path, exist_ok=True)


    if doc_type == 'application/pdf':
        doc = PDF(src=file_path)
        try:
            extracted_tables = doc.extract_tables(ocr=ocr,
                                            implicit_rows=True,
                                            implicit_columns=True,
                                            borderless_tables=True,
                                            min_confidence=20)
        except Exception as e:
            return []
        combined_tables = []
        prev_table = None
        for page_num, tables_on_page in extracted_tables.items():
            for table in tables_on_page:
                if prev_table is not None and is_continuous_table(prev_table, table):
                    combined_table = combine_tables(prev_table, table)
                    combined_tables[-1] = combined_table
                else:
                    combined_tables.append(table)
                prev_table = table

    else:
        doc = Image(src=file_path)
        try:
            extracted_tables = doc.extract_tables(ocr=ocr,
                                            implicit_rows=True,
                                            implicit_columns=True,
                                            borderless_tables=True,
                                            min_confidence=20)
        except Exception as e:
            return []
        combined_tables = extracted_tables
        if combined_tables:
            img = doc.images[0]
            img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
            png_filename = os.path.join(output_folder_path, f"table_{get_snowflake_id()}.png")
            cv2.imwrite(png_filename, img)
            with open(png_filename, 'rb') as pngfile:
                png_data = pngfile.read()
            png_url = minio_util.upload_to_minio(bucket_name, png_filename, png_data)
            png_paths.append(png_url)
            base_csv_dict = { 
                'png_url': png_url,
            }
    try:
        aigc_model_info = AppController(corpid).get_model_info(aigc_model_id)
        aigc_type_id, model_path, model_id = aigc_model_info.get('aigc_type_id'), aigc_model_info.get('model_path'), aigc_model_info.get('aigc_model_id')
    except Exception as e:
        logging.error(f'获取AIGC模型信息失败: {e}')
        return
    for idx, table in enumerate(combined_tables):
        csv_dict = base_csv_dict.copy()  # 创建一个新的字典对象
        csv_dict.update({
            'csv_content': '',
            'csv_url': '',
            'table_len': 0
        })  
        csv_filename = os.path.join(output_folder_path, f"table_{get_snowflake_id()}.csv")
        try:
            with open(csv_filename, mode='w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                for row_id, row in table.content.items():
                    row_content = [cell.value if cell.value is not None else '' for cell in row]
                    writer.writerow(row_content)
            df = pd.read_csv(csv_filename)
            csv_dict['table_len'] = len(df)
            csv_content = df.to_csv(sep='\t', na_rep='nan') 
            desc = f"请总结以下 CSV 文件的内容 \n 文件内容: {csv_content} \n"
            desc += """## 限制:\n - 直接返回内容，不要添加前言和后语 \n """
            desc += '\n总结后的内容：'
            ret = ''
            try:
                async for item in AppController(corpid).ai_app_chat_async(desc, [], aigc_type_id, model_path, model_id):
                    ret += item
            except Exception as e:
                logging.error(f"大模型总结CSV文件内容时出错: {e}")
                csv_dict['csv_content'] = ''
            csv_dict['csv_content'] = ret
            with open(csv_filename, 'rb') as csvfile:
                csv_data = csvfile.read()
            csv_url = minio_util.upload_to_minio(bucket_name, csv_filename, csv_data)
            csv_paths.append(csv_url)
            csv_dict['csv_url'] = csv_url
        except Exception as e:
            logging.error(f"Error processing CSV file: {e}")
        csv_list.append(csv_dict)
    try:
        knowledge_controller.add_knowledge_doc_chunk_csv_in_batch(doc_id, csv_list)
    except Exception as e:
        logging.error(f"Error add_knowledge_doc_chunk_csv_in_batch : {e}")
    return csv_list

async def word_tables_to_csv(doc_path, output_dir,aigc_model_id,corpid):
    try:
        aigc_model_info = AppController(corpid).get_model_info(aigc_model_id)
        aigc_type_id, model_path, model_id = aigc_model_info.get('aigc_type_id'), aigc_model_info.get('model_path'), aigc_model_info.get('aigc_model_id')
    except Exception as e:
        logging.error(f'获取AIGC模型信息失败: {e}')
        return
    current_dir = os.path.dirname(os.path.abspath(__file__))
    temp_dir = os.path.join(current_dir, 'temp_files')
    temp_file_path_docx = os.path.join(temp_dir, f"{os.path.basename(doc_path)}")
    logging.info(f"aigc_type_id: {aigc_type_id},model_path: {model_path},model_id: {model_id}")
    try:
        doc = Document(doc_path)
    except Exception as e:
        return []

    os.makedirs(output_dir, exist_ok=True)
    csv_paths = []
    csv_list = []
    csv_dict = { 
        'csv_content': '',
        'csv_url': '',
        'png_url': '',
        'table_len': 0
    }
    bucket_name = settings.KNOWLEDGE_OSS_BUCKET_NAME
    try:
        for i, table in enumerate(doc.tables, 1):
            csv_dict = { 
                'csv_content': '',
                'csv_url': '',
                'png_url': '',
                'table_len': 0
            }
            # 提取表格数据
            table_data = []
            for row in table.rows:
                row_data = [cell.text for cell in row.cells]
                table_data.append(row_data)
            # 转换为 DataFrame
            df = pd.DataFrame(table_data)

            # 生成唯一的 CSV 文件名
            csv_filename = f'table_{get_snowflake_id()}.csv'
            csv_path = os.path.join(output_dir, csv_filename)
            df.to_csv(csv_path, index=False, header=True)
            with open(csv_path, 'rb') as f:
                #跳过第一行
                next(f) 
                csv_data = f.read()
                csv_url = minio_util.upload_to_minio(bucket_name,csv_filename, csv_data)
                csv_dict['csv_url'] = csv_url

            df = pd.read_csv(csv_path)
            csv_dict['table_len'] = len(df)
            csv_content = df.to_csv(sep='\t', na_rep='nan') 
            desc = f"请总结以下 CSV 文件的内容 \n 文件内容: {csv_content} \n"
            desc += """## 限制:\n - 直接返回内容，不要添加前言和后语 \n """
            desc += '\n总结后的内容：'
            ret = ''
            try:
                async for item in AppController(corpid).ai_app_chat_async(desc, [], aigc_type_id, model_path, model_id):
                    ret += item
            except Exception as e:
                logging.error(f"大模型总结CSV文件内容时出错: {e}")
                csv_dict['csv_content'] = ''
            csv_dict['csv_content'] = ret
            csv_paths.append(csv_url)
            csv_list.append(csv_dict)
        return csv_list
    except Exception as e:
        logging.error(f"word_tables_to_csv error: {e}")
        return []
    finally:
        if os.path.exists(temp_file_path_docx):
            os.remove(temp_file_path_docx)

@celery.task(bind=False, name="apps.ai.task.extract_table_from_docx")
def extract_table_from_docx( doc, corpid, doc_id, aigc_model_id, task_id, redis_client):
    output_folder_path = os.path.join(settings.BASE_DIR, f"upload/knowledge_doc_image_files/{get_snowflake_id()}")
    csv_path_list = []
    steps = 0
    total_steps = 0

    if doc.get('doc_type') in ['application/pdf', 'image/png', 'image/jpeg', 'image/jpg']:
        total_steps = 1
        doc_type = doc.get('doc_type')
        try:
            csv_path_list =  asyncio.run(tables_to_csv(corpid, doc.get('doc_url'), doc_id, output_folder_path, doc_type, aigc_model_id))
            steps += 1
            progress = (steps / total_steps) * 100
            redis_client.set(f'extract_progress_{task_id}_{doc_id}', progress)
        except Exception as e:
            logging.error(f"Error processing document tables_to_csv {doc.get('doc_name')}: {e}")
            return []
    elif doc.get('doc_type') == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        total_steps = 3  # 提取图片、处理图片、处理 word 表格
        doc_img_list = []
        try:
            doc_img_list = KnowledgeController().extract_table_images_from_docx(doc.get('doc_url'), output_folder_path)
            KnowledgeController().add_knowledge_document_images_batch(doc_id, doc_img_list)
            steps += 1
            progress = (steps / total_steps) * 100
            redis_client.set(f'extract_progress_{task_id}_{doc_id}', progress)
        except Exception as e:
            logging.error(f"Error processing document extract_table_images_from_docx {doc.get('doc_name')}: {e}")
            return []

        img_count = len(doc_img_list)
        for index, img_url in enumerate(doc_img_list, start=1):
            doc_type = img_url.split(".")[-1]
            if doc_type == 'png':
                doc_type = 'image/png'
            elif doc_type == 'jpg':
                doc_type = 'image/jpeg'
            elif doc_type == 'jpeg':
                doc_type = 'image/jpeg'
            try:
                csv_path_list = asyncio.run(tables_to_csv(corpid, img_url, doc_id, output_folder_path, doc_type, aigc_model_id))
                sub_progress = (index / img_count) * 100
                progress = (steps + sub_progress / 100) / total_steps * 100
                redis_client.set(f'extract_progress_{task_id}_{doc_id}', progress)
            except Exception as e:
                logging.error(f"Error processing document tables_to_csv {doc.get('doc_name')}: {e}")
                return []

        steps += 1
        file_path = KnowledgeController().handle_file_download(doc.get('doc_url'))
        try:
            docx_csv_path_list = asyncio.run(word_tables_to_csv(file_path, output_folder_path, aigc_model_id, corpid))
            steps += 1
            progress = (steps / total_steps) * 100
            redis_client.set(f'extract_progress_{task_id}_{doc_id}', progress)
        except Exception as e:
            logging.error(f"Error processing document word_tables_to_csv {doc.get('doc_name')}: {e}")
            docx_csv_path_list = []
        csv_path_list.append(docx_csv_path_list)
        try:
            KnowledgeController().add_knowledge_doc_chunk_csv_in_batch(doc_id, docx_csv_path_list)
        except Exception as e:
            logging.error(f"Error processing document add_knowledge_doc_chunk_csv_in_batch {doc.get('doc_name')}: {e}")
            return []

    return csv_path_list


@celery.task(bind=True, name="apps.ai.task.async_video_frame_extraction")
@check_args
def async_video_frame_extraction(task: any, corpid: str, knowledge_id: str, doc_id: str, frame_list: list[dict]) -> None:
# def async_video_frame_extraction(corpid: str, knowledge_id: str, doc_id: str, frame_list: list[dict]) -> None:
    """
    @summary: 视频提取关键帧
    @param task:
    @param knowledge_id:
    @param doc_id:
    @param frame_list:
    @return:
    """
    meta = {"current": 0, "total": 1, 'state': 'success', 'message': '', 'data': {}, 'exc_type': '',
            'exc_message': '', 'chunk_list': [], 'has_fail': False}
    state = 'PENDING'
    task_id = task.request.id
    task_logger.info(f"async_video_frame_extraction 视频提取关键帧开始: task_id: {task_id}, knowledge_id: {knowledge_id}, doc_id: {doc_id}")
    total = len(frame_list)
    if total <= 0:
        meta['current'] = 100
        meta['message'] = 'frame_list is empty'
        state = 'Error'
        task.update_state(state=state, meta=meta)
        return

    success_dict = {"success": 0, "is_success": True, "error": 0, "temp_total": 0}
    knowledge_controller = KnowledgeController(corpid)
    # 更改doc的状态
    knowledge_controller.update_knowledge_doc(doc_id, {'status': 'RUNNING','current_node':'关键帧解析','state':'进行中'})

    async def reqeust_task(frame_dict, task, semaphore):
        nonlocal meta
        nonlocal success_dict
        nonlocal knowledge_controller
        nonlocal total
        nonlocal state

        frame_url = frame_dict.get('img_url', '')
        frame_time = frame_dict.get('frame_time', '')
        frame = frame_dict.get('frame_num', '')
        audio_start = frame_dict.get('audio_start', 0)
        audio_end = frame_dict.get('audio_end', 0)
        # 这一帧的音频文字识别结果
        audio_text = frame_dict.get("audio_text", '')

        # todo async
        return_dict = await async_request_description(frame_url, semaphore)
        images_str = return_dict.get("data", {'content': ""}).get("content", '')
        data = return_dict.get("data", {})
        images_count = data.get("character_count", 0)
        is_return_success = return_dict.get("code", 0)
        if is_return_success != 1:
            success_dict['is_success'] = False
            meta['state'] = 'error'
        if audio_text:
            images_str = images_str + '/' + audio_text
        # 大模型识别成功 or 失败 存不存？ 失败了也存，但是状态更新成失败，以便后续可以继续提取和向量
        # 存AigcKnowledgeDocumentChunk和AigcKnowledgeDocuChunkImage和AigcKnowledgeVideoChunk
        chunk_id = knowledge_controller.add_knowledge_doc_chunk(
                        doc_id, images_str, images_count, 'TODO'
                    )
        knowledge_controller.add_chunk_image(chunk_id, frame_url)
        knowledge_controller.add_chunk_video(chunk_id, frame, frame_time, audio_start, audio_end)

        success_dict['temp_total'] += 1
        meta['current'] = int(success_dict['temp_total'] / total * 100)
        meta['data'].update({str(chunk_id): 'END'})
        meta['chunk_list'].append({
            'doc_id': doc_id,
            # 'doc_name': doc_name,
            'state': meta['state'],
            'chunk': knowledge_controller.get_knowledge_doc_chunk_video_detail(chunk_id)
        })
        task.update_state(state=state, meta=meta)

        meta['state'] = 'success'
    async def async_request_description_main(frame_list, task, max_concurrency):
        semaphore = asyncio.Semaphore(max_concurrency)
        tasks = [reqeust_task(frame_dict, task, semaphore) for frame_dict in frame_list]
        results = await asyncio.gather(*tasks)
        return results

    # TODO 异步去做
    asyncio.run(async_request_description_main(frame_list, task, 5))
    # for frame_dict in frame_list:
    #     frame_url = frame_dict.get('img_url', '')
    #     frame_time = frame_dict.get('frame_time', '')
    #     frame = frame_dict.get('frame_num', '')
    #     audio_start = frame_dict.get('audio_start', 0)
    #     audio_end = frame_dict.get('audio_end', 0)
    #     # 这一帧的音频文字识别结果
    #     audio_text = frame_dict.get("audio_text", '')

    #     # todo async
    #     return_dict = request_description(frame_url)
    #     images_str = return_dict.get("data", {'content': ""}).get("content", '')
    #     images_count = return_dict.get("data", {"character_count", 0}).get("character_count", 0)
    #     is_return_success = return_dict.get("code", 0)
    #     if is_return_success != 1:
    #         success_dict['is_success'] = False
    #         meta['state'] = 'error'
    #     if audio_text:
    #         images_str = images_str + '/' + audio_text
    #     # 大模型识别成功 or 失败 存不存？ 失败了也存，但是状态更新成失败，以便后续可以继续提取和向量
    #     # 存AigcKnowledgeDocumentChunk和AigcKnowledgeDocuChunkImage和AigcKnowledgeVideoChunk
    #     chunk_id = knowledge_controller.add_knowledge_doc_chunk(
    #                     doc_id, images_str, images_count, 'TODO'
    #                 )
    #     knowledge_controller.add_chunk_image(chunk_id, frame_url)
    #     knowledge_controller.add_chunk_video(chunk_id, frame, frame_time, audio_start, audio_end)

    #     success_dict['temp_total'] += 1
    #     meta['current'] = int(success_dict['temp_total'] / total * 100)
    #     meta['data'].update({str(chunk_id): 'END'})
    #     meta['chunk_list'].append({
    #         'doc_id': doc_id,
    #         # 'doc_name': doc_name,
    #         'state': meta['state'],
    #         'chunk': knowledge_controller.get_knowledge_doc_chunk_video_detail(chunk_id)
    #     })
    #     task.update_state(state=state, meta=meta)

    #     meta['state'] = 'success'
    # 更改doc状态，根据success_dict判断
    if success_dict.get('is_success'):
        knowledge_controller.update_knowledge_doc(doc_id, {'status': 'FINISHED', 'result': 'TODO','current_node':'关键帧解析','state':'已完成'})
        task_logger.info(f"async_video_frame_extraction 视频提取关键帧成功: task_id: {task_id}, knowledge_id: {knowledge_id}, doc_id: {doc_id}")
    else:
        meta['has_fail'] = True
        knowledge_controller.update_knowledge_doc(doc_id, {'status': 'FAILED', 'result': 'TODO','current_node':'关键帧解析','state':'失败'})
        task_logger.info(f"async_video_frame_extraction 视频提取关键帧有错误， task_id: {task_id}, knowledge_id: {knowledge_id}, doc_id: {doc_id}")
    state = 'SUCCESS'
    task.update_state(state=state, meta=meta)


@celery.task(bind=True, name="apps.ai.task.async_document_chunk_embeddings")
def async_document_chunk_embeddings(task, corpid, knowledge_id, task_id,knlg_extract=0):
    """
    异步切片向量化
    :param task:
    :param corpid:
    :param knowledge_id:
    :param task_id:
    :return:
    """
    meta = {"current": 0, "total": 0, 'success': 0, 'state': '', 'message': '', 'data': {}, 'exc_type': '',
            'exc_message': ''}
    state = 'PENDING'
    knowledge_controller = KnowledgeController(corpid)
    knowledge_info = knowledge_controller.get_knowledge_detail(knowledge_id)
    model_path = knowledge_info['model_path'] if knowledge_info and knowledge_info['model_path'] else 'embedding-v1'
    task_logger.info(f"词向量模型 model_path: {model_path}")
    dims = knowledge_info['dims'] if knowledge_info and knowledge_info['dims'] else 1024
    doc_list = knowledge_controller.get_knowledge_doc_list(knowledge_id,
                                                           {'result': ['TODO', 'FAILED'], 'status': 'FINISHED', 'is_all': 'all'})
    # todo if doc_list is empty, return

    mapping = {
        "properties": {
            "chunk_id": {
                "type": "keyword"
            },
            "doc_id": {
                "type": "keyword"
            },
            "knowledge_id": {
                "type": "keyword"
            },
            "content_id": {
                "type": "keyword"
            },
            "description": {
                "type": "text",
                "analyzer": "ik_max"
            },
            "character_count": {
                "type": "integer"
            },
            "hit_count": {
                "type": "integer"
            },
            "tokens": {
                "type": "integer"
            },
            "status": {
                "type": "boolean"
            },
            "vector": {
                "type": "dense_vector",
                "dims": dims,
                "index": True
            },
            "delete_flag": {
                "type": "boolean"
            }
        }
    }

    # task_logger.info(mapping)
    es = knowledge_controller.es
    
    total = knowledge_controller.get_knowledge_doc_chunk_list_by_knowledge(knowledge_id,
                                                                           {'results': ['TODO', 'FAILED'], 'status': 'FINISHED'})
    success = 0
    is_success = True
    try:
        for doc in doc_list['data_list']:
            doc_chunk_list = knowledge_controller.get_knowledge_doc_chunk_list(doc['document_id'],
                                                                               {'is_all': 'all', 'results': ['TODO', 'FAILED']})
            total_tokens = 0
            knowledge_controller.update_knowledge_doc(doc['document_id'], {'tokens': total_tokens, 'result': 'RUNNING','current_node':'向量化','state':'进行中'})

            for doc_chunk in doc_chunk_list['data_list']:
                knowledge_controller.update_knowledge_doc_chunk(doc_chunk['chunk_id'], {'result': 'RUNNING'})
                try:
                    # todo 向量化之后running没有变，是因为else之后没有update状态，具体原因要去看create_embeddings里面怎么返回的code以及原因
                    time1 = time.time()
                    task_logger.info(f"Starting create_embeddings, doc_url: {doc_chunk['doc_url']}")
                    emb_data = knowledge_controller.create_embeddings(doc_chunk['content'], str(doc_chunk['doc_id']), model_path)
                    task_logger.warning(f"{doc_chunk['chunk_id']} 耗时: {round(time.time() - time1, 2)}")
                    if emb_data['code'] == 0:

                        data = {
                            "chunk_id": doc_chunk['chunk_id'],
                            "doc_id": doc_chunk['doc_id'],
                            "knowledge_id": knowledge_id,
                            "content_id": doc['content_id'],
                            "description": doc_chunk['content'],
                            "character_count": doc_chunk['character_count'],
                            "hit_count": doc_chunk['hit_count'],
                            "tokens": emb_data['total_tokens'],
                            "status": True,
                            "vector": emb_data['vector'],
                            "delete_flag": False
                        }
                        index_name = 'knowledge_%s' % model_path

                        res = es.insert_data(index_name, data, mapping, ik_set=True)
                        task_logger.info(f'es insert 耗时: {round(time.time() - time1, 2)}')
                        knowledge_controller.update_knowledge_doc_chunk(doc_chunk['chunk_id'],
                                                                        {'tokens': emb_data['total_tokens'], 'result': 'FINISHED'})
                        task_logger.info(f'update_knowledge_doc_chunk 耗时: {round(time.time() - time1, 2)}')
                        success += 1
                        if total > 0:
                            meta['current'] = round(success / total * 100, 2)
                        else:
                            meta['current'] = 0
                        meta['total'] = total
                        meta['success'] = success
                        data = knowledge_controller.get_knowledge_doc_chunk_list_by_knowledge(knowledge_id,
                                                                                              {'results': ['FINISHED'],
                                                                                               'status': 'FINISHED',
                                                                                               'is_all': 'all'})
                        task_logger.info(f'get_knowledge_doc_chunk_list_by_knowledge 耗时: {round(time.time() - time1, 2)}')
                        # meta['data'].update(data)
                        # meta['data'].update({})
                        task.update_state(state=state, meta=meta)
                        knowledge_controller.update_knowledge_doc(doc['document_id'], {'current_node': '向量化','state':'已完成','knlg_extract':knlg_extract})
                        task_logger.info(
                            'async_document_chunk_embeddings: total:%s, success: %s, task_id: %s, knowledge_id: %s, current: %s' % (total, success, task_id, knowledge_id, meta['current']))
                        # task_logger.info(success, total, meta['current'], meta['data'])
                    else:
                        is_success = False
                        knowledge_controller.update_knowledge_doc_chunk(doc_chunk['chunk_id'], {'result': 'FAILED'})
                        knowledge_controller.update_knowledge_doc(doc['document_id'], {'current_node': '向量化','state':'失败'})
                        task_logger.exception(
                            'async_document_chunk_embeddings:向量生成失败【%s】【%s】【%s】' % (corpid, doc_chunk['chunk_id'], res['error_msg']))
                except Exception as e:
                    is_success = False
                    knowledge_controller.update_knowledge_doc_chunk(doc_chunk['chunk_id'], {'result': 'FAILED'})
                    knowledge_controller.update_knowledge_doc(doc['document_id'], {'current_node': '向量化','state':'失败'})
                    # meta['data'].update({str(doc_chunk['chunk_id']): 'FAILURE'})
                    task_logger.exception('async_document_chunk_embeddings:向量生成失败【%s】【%s】【%s】' % (corpid, doc_chunk['chunk_id'], e))
                # task_logger.info(state, meta)
                task.update_state(state=state, meta=meta)
                result = 'FINISHED' if is_success else 'FAILED'
                knowledge_controller.update_knowledge_doc(doc['document_id'], {'tokens': total_tokens, 'result': result,'current_node':'向量化','state':"已完成"})
        state = 'SUCCESS'
        task.update_state(state=state, meta=meta)
        knowledge_controller.update_knowledge_run_record({'task_id': task_id, 'knowledge_id': knowledge_id},
                                                         {'embedding_status': state,
                                                          'update_time': int(time.time()), 'chunk_data': meta['data'],
                                                          'embedding_current': meta['current'], 'embedding_total': meta['total'],
                                                          'embedding_success': meta['success']})
    except Exception as e:
        task_logger.error('async_document_chunk_embeddings:【%s】【%s】文档分词失败 %s' % (corpid, knowledge_id, e))
        knowledge_controller.update_knowledge_doc(doc['document_id'], {'current_node': '向量化','state':'失败'})
        meta.update({'message': str(e)})
        state = 'FAILURE'
        task.update_state(state=state, meta=meta)
        knowledge_controller.update_knowledge_run_record({'task_id': task_id, 'knowledge_id': knowledge_id},
                                                         {'embedding_status': state,
                                                          'update_time': int(time.time()), 'chunk_data': meta['data'],
                                                          'embedding_current': meta['current'], 'embedding_total': meta['total'],
                                                          'embedding_success': meta['success']})


@celery.task(bind=True, name="apps.ai.task.async_appoint_document_chunk_embeddings")
def async_appoint_document_chunk_embeddings(task, corpid, knowledge_id, doc_ids, chunk_ids,knlg_extract=0):
    meta = {"current": 0, "total": 0, 'success': 0, 'state': '', 'message': '', 'data': {}, 'exc_type': '', 'exc_message': ''}
    state = 'PENDING'
    knowledge_controller = KnowledgeController(corpid)
    knowledge_info = knowledge_controller.get_knowledge_detail(knowledge_id)
    model_path = knowledge_info['model_path'] if knowledge_info and knowledge_info['model_path'] else 'embedding-v1'
    task_logger.info(f"词向量模型 model_path: {model_path}")

    success_dict = {"success": 0, "is_success": True, "error": 0, "temp_total": 0}
    total = len(chunk_ids)
    if total <= 0:
        meta['current'] = 100
        task.update_state(state='SUCCESS', meta=meta)
        return
    def update_task():
        nonlocal success_dict
        nonlocal meta
        nonlocal state
        temp_total = success_dict.get("temp_total")
        if temp_total == 1 or temp_total % 3 == 0 or temp_total >= total:
            task.update_state(state=state, meta=meta)

    def embedding_error_process_flow(func_name, error_key, corpid, knowledge_id, doc_id, chunk_id, e):
        nonlocal success_dict
        nonlocal meta
        nonlocal total
        nonlocal state
        success_dict.update({"error": success_dict.get("error")+1 if success_dict.get("error") else 1, "is_success": False, "temp_total": success_dict.get("temp_total")+1 if success_dict.get("temp_total") else 1})
        meta['data'].update({str(doc_id): 'FAILURE'})
        meta.update({"scccess": success_dict.get("success"), "current": int(success_dict.get("success") / total * 100), "total": total})
        update_task()
        knowledge_controller.update_knowledge_doc_chunk(chunk_id, {'result': 'FAILED'})
        task_logger.exception('指定向量生成失败 <%s>: %s. corpid:【%s】,aigc_knowledge_id:【%s】,doc_id:【%s】,chunk_id: 【%s】, 错误原因:【%s】' % (func_name, error_key, corpid, knowledge_id, doc_id, chunk_id, e))
        
        return
    
    # try:
    for doc_id in doc_ids:
        knowledge_controller.update_knowledge_doc(doc_id, {'current_node': '向量化','state':'进行中'})

        doc_chunk_list = knowledge_controller.get_knowledge_doc_chunk_list(doc_id, {'is_all': 'all', 'chunk_ids': chunk_ids})
        # if not doc_chunk_list or not doc_chunk_list.get('data_list'):
        #     # 分片失败 向量不继续执行
        #     all_doc_chunk_list = knowledge_controller.get_knowledge_doc_chunk_list(doc_id, {'is_all': 'all'})
        #     if not all_doc_chunk_list or not all_doc_chunk_list.get('data_list'):
        #         meta['data'].update({str(doc_id): 'FAILURE'})
        #         # 更新doc的切片状态为失败
        #         knowledge_controller.update_knowledge_doc(doc_id, {'result': 'FAILED', 'status': 'FAILED'})
        for doc_chunk in doc_chunk_list['data_list']:
            knowledge_controller.update_knowledge_doc_chunk(doc_chunk['chunk_id'], {'result': 'RUNNING'})
            # 生成向量
            try:
                task_logger.info(f"create_embeddings===={doc_chunk['doc_url']}")
                emb_data = knowledge_controller.create_embeddings(doc_chunk['content'], str(doc_chunk['doc_id']), model_path)
            except Exception as e:
                embedding_error_process_flow("async_appoint_document_chunk_embeddings", "create_embeddings", corpid, knowledge_id, doc_id, doc_chunk['chunk_id'], e)
                continue
            if emb_data['code'] == 0:
                update_query = {
                    "query": {
                        "bool": {
                            "must": [
                                {
                                "term": {
                                    "chunk_id": doc_chunk['chunk_id']
                                }
                                },
                                {
                                "term": {
                                    "doc_id": doc_chunk['doc_id']
                                }
                                },
                                {
                                "term": {
                                    "knowledge_id": knowledge_id
                                }
                                }
                            ]
                        }
                    },
                    "script": {
                        "source": """
                                ctx._source.tokens = params.tokens;
                                ctx._source.vector = params.vector;
                                """,
                        "lang": "painless",
                        "params": {
                            "tokens": emb_data['total_tokens'],
                            "vector": emb_data['vector']
                        }

                    }
                }
                index_name = knowledge_controller.index_name + model_path
                try:
                    knowledge_controller.es.update_data(index_name, update_query)
                except Exception as e:
                    embedding_error_process_flow("async_appoint_document_chunk_embeddings", "ES Update Error", corpid, knowledge_id, doc_id, doc_chunk['chunk_id'], e)
                    continue
                task_logger.info(f"token11111s: {emb_data['total_tokens']}")
                knowledge_controller.update_knowledge_doc_chunk(doc_chunk['chunk_id'], {'tokens': emb_data['total_tokens'], 'result': 'FINISHED'})
                knowledge_controller.update_knowledge_doc(doc_id, {'current_node': '向量化','state':'已完成','knlg_extract':knlg_extract})
                success_dict.update({"success": success_dict.get("success") + 1 if success_dict.get("success") else 1, "temp_total": success_dict.get("temp_total")+1 if success_dict.get("temp_total") else 1})
                success = success_dict.get("success")
                if total > 0:
                    meta['current'] = int(success / total * 100)
                else:
                    meta['current'] = 0
                meta['total'] = total
                meta['success'] = success
                task_logger.info('async_appoint_document_chunk_embeddings: knowledge_id: %s, doc_id:【%s】, chunk_id: 【%s】, current: %s, total:%s, success: %s, ' % (knowledge_id, doc_id, doc_chunk['chunk_id'], total, success, meta['current']))
            else:
                embedding_error_process_flow("async_appoint_document_chunk_embeddings", "create_embedding return code not 0.r", corpid, knowledge_id, doc_id, doc_chunk['chunk_id'], e)
                continue
            update_task()
        
        is_success = success_dict.get("is_success")
        result = 'FINISHED' if is_success else 'FAILED'
        if is_success:
            knowledge_controller.update_knowledge_doc(doc_id, {'result': result, 'current_node': '向量化','state':'已完成'})
        else:
            knowledge_controller.update_knowledge_doc(doc_id, {'result': result, 'current_node': '向量化','state':'失败'})
    state = 'SUCCESS' if is_success else 'FAILED'
    task.update_state(state=state, meta=meta)


@celery.task(bind=True, name="apps.ai.task.async_appoint_document_chunk_embeddings_v1")
def async_appoint_document_chunk_embeddings_v1(task, corpid, knowledge_id, doc_ids, chunk_ids,knlg_extract=0):
    """
    日志与逻辑优化版
    """
    meta = {"current": 0, "total": 0, 'success': 0, 'state': '', 'message': '', 'data': {}, 'exc_type': '', 'exc_message': ''}
    state = 'PENDING'
    knowledge_controller = KnowledgeController(corpid)
    knowledge_info = knowledge_controller.get_knowledge_detail(knowledge_id)
    model_path = knowledge_info['model_path'] if knowledge_info and knowledge_info['model_path'] else 'embedding-v1'
    task_logger.info(f"词向量模型 model_path: {model_path}")
    success_dict = {"success": 0, "is_success": True, "error": 0, "temp_total": 0}
    dims = knowledge_info['dims'] if knowledge_info and knowledge_info['dims'] else 1024

    total = len(chunk_ids)
    if total <= 0:
        meta['current'] = 100
        task.update_state(state='SUCCESS', meta=meta)
        return
    
    def update_task():
        nonlocal success_dict
        nonlocal meta
        nonlocal state
        temp_total = success_dict.get("temp_total")
        if temp_total == 1 or temp_total % 3 == 0 or temp_total >= total:
            task.update_state(state=state, meta=meta)

    def embedding_error_process_flow(func_name, error_key, corpid, knowledge_id, doc_id, chunk_id, e):
        nonlocal success_dict
        nonlocal meta
        nonlocal total
        nonlocal state
        success_dict.update({"error": success_dict.get("error")+1 if success_dict.get("error") else 1, "is_success": False, "temp_total": success_dict.get("temp_total")+1 if success_dict.get("temp_total") else 1})
        meta['data'].update({str(doc_id): 'FAILURE'})
        meta.update({"scccess": success_dict.get("success"), "current": int(success_dict.get("success") / total * 100), "total": total})
        update_task()
        knowledge_controller.update_knowledge_doc_chunk(chunk_id, {'result': 'FAILED'})
        task_logger.exception('指定向量生成失败 <%s>: %s. corpid:【%s】,aigc_knowledge_id:【%s】,doc_id:【%s】,chunk_id: 【%s】, 错误原因:【%s】' % (func_name, error_key, corpid, knowledge_id, doc_id, chunk_id, e))
        
        return
    
    mapping = {
        "properties": {
            "chunk_id": {
                "type": "keyword"
            },
            "doc_id": {
                "type": "keyword"
            },
            "knowledge_id": {
                "type": "keyword"
            },
            "content_id": {
                "type": "keyword"
            },
            "description": {
                "type": "text",
                "analyzer": "ik_max"
            },
            "character_count": {
                "type": "integer"
            },
            "hit_count": {
                "type": "integer"
            },
            "tokens": {
                "type": "integer"
            },
            "status": {
                "type": "boolean"
            },
            "vector": {
                "type": "dense_vector",
                "dims": dims,
                "index": True
            },
            "delete_flag": {
                "type": "boolean"
            }
        }
    }

    # try:
    for doc_id in doc_ids:
        knowledge_controller.update_knowledge_doc(doc_id, {'current_node': '向量化','state':'进行中'})
        doc = knowledge_controller.get_knowledge_document_detail(doc_id)
        doc_chunk_list = knowledge_controller.get_knowledge_doc_chunk_list(doc_id, {'is_all': 'all', 'chunk_ids': chunk_ids})
        if not doc_chunk_list or not doc_chunk_list.get('data_list'):
            # 分片失败 向量不继续执行
            all_doc_chunk_list = knowledge_controller.get_knowledge_doc_chunk_list(doc_id, {'is_all': 'all'})
            if not all_doc_chunk_list or not all_doc_chunk_list.get('data_list'):
                meta['data'].update({str(doc_id): 'FAILURE'})
                # 更新doc的切片状态为失败
                knowledge_controller.update_knowledge_doc(doc_id, {'result': 'FAILED', 'status': 'FAILED','current_node': '向量化','state':'失败'})
        for doc_chunk in doc_chunk_list['data_list']:
            knowledge_controller.update_knowledge_doc_chunk(doc_chunk['chunk_id'], {'result': 'RUNNING'})
            # 生成向量
            try:
                emb_data = knowledge_controller.create_embeddings(doc_chunk['content'], str(doc_chunk['doc_id']), model_path)
            except Exception as e:
                embedding_error_process_flow("async_appoint_document_chunk_embeddings_v1", "create_embeddings", corpid, knowledge_id, doc_id, doc_chunk['chunk_id'], str(e))
                continue
            if emb_data['code'] == 0:
                data = {
                            "chunk_id": doc_chunk['chunk_id'],
                            "doc_id": doc_chunk['doc_id'],
                            "knowledge_id": knowledge_id,
                            "content_id": doc.get('content_id', ''),
                            "description": doc_chunk['content'],
                            "character_count": doc_chunk['character_count'],
                            "hit_count": doc_chunk['hit_count'],
                            "tokens": emb_data['total_tokens'],
                            "status": True,
                            "vector": emb_data['vector'],
                            "delete_flag": False
                        }
                index_name = knowledge_controller.index_name + model_path
                try:
                    knowledge_controller.es.insert_data(index_name, data, mapping, ik_set=True)
                except Exception as e:
                    embedding_error_process_flow("async_appoint_document_chunk_embeddings_v1", "ES Update Error", corpid, knowledge_id, doc_id, doc_chunk['chunk_id'], str(e))
                    continue
                knowledge_controller.update_knowledge_doc_chunk(doc_chunk['chunk_id'], {'tokens': emb_data['total_tokens'], 'result': 'FINISHED'})
                # knowledge_controller.update_knowledge_doc(doc_id, {'current_node': '向量化','state':'已完成','knlg_extract':knlg_extract})
                success_dict.update({"success": success_dict.get("success") + 1 if success_dict.get("success") else 1, "temp_total": success_dict.get("temp_total")+1 if success_dict.get("temp_total") else 1})
                success = success_dict.get("success")
                if total > 0:
                    meta['current'] = int(success / total * 100)
                else:
                    meta['current'] = 0
                meta['total'] = total
                meta['success'] = success
                task_logger.info('async_appoint_document_chunk_embeddings_v1: knowledge_id: %s, doc_id:【%s】, chunk_id: 【%s】, current: %s, total:%s, success: %s, ' % (knowledge_id, doc_id, doc_chunk['chunk_id'], total, success, meta['current']))
            else:
                embedding_error_process_flow("async_appoint_document_chunk_embeddings_v1", "create_embedding return code not 0.r", corpid, knowledge_id, doc_id, doc_chunk['chunk_id'], emb_data.get('error_msg', "create_embedding return code not 0"))
                continue
            update_task()
        is_success = success_dict.get("is_success")
        result = 'FINISHED' if is_success else 'FAILED'
        if is_success:
            knowledge_controller.update_knowledge_doc(doc_id, {'result': result, 'current_node': '向量化','state':'已完成', 'knlg_extract':knlg_extract})
        else:
            knowledge_controller.update_knowledge_doc(doc_id, {'result': result, 'current_node': '向量化','state':'失败', 'knlg_extract':knlg_extract})
    state = 'SUCCESS' if is_success else 'FAILED'
    task.update_state(state=state, meta=meta)

@celery.task(bind=True, name="apps.ai.task.async_appoint_document_chunk_embeddings_coroutine")
@check_args
def async_appoint_document_chunk_embeddings_coroutine(task, corpid, knowledge_id, doc_ids, chunk_ids, knlg_extract=0):
    """
    异步向量
    """
    meta = {"current": 0, "total": 0, 'success': 0, 'error': 0, 'state': '', 'message': '', 'data': {}, 'exc_type': '', 'exc_message': ''}

    state = 'PENDING'
    knowledge_controller = KnowledgeController(corpid)
    knowledge_info = knowledge_controller.get_knowledge_detail(knowledge_id)
    model_path = knowledge_info['model_path'] if knowledge_info and knowledge_info['model_path'] else 'embedding-v1'
    task_logger.info(f"词向量模型 model_path: {model_path}")
    success_dict = {"success": 0, "is_success": True, "error": 0, "temp_total": 0}
    dims = knowledge_info['dims'] if knowledge_info and knowledge_info['dims'] else 1024

    doc_log_list = list()

    total = len(chunk_ids)
    if total <= 0:
        params = {'is_all': 'all', 'results': ['TODO', 'RUNNING', 'FAILED']}
        for doc_id in doc_ids:
            result = knowledge_controller.get_knowledge_doc_chunk_list(doc_id, params)
            if result is not None and 'data_list' in result:
                total += len(result['data_list'])
                chunk_ids.extend([chunk['chunk_id'] for chunk in result['data_list'] if chunk['chunk_id'] not in chunk_ids])
    if total <= 0:
        meta['current'] = 100
        task.update_state(state='SUCCESS', meta=meta)
        return
    
    def update_task():
        nonlocal success_dict
        nonlocal meta
        nonlocal state
        temp_total = success_dict.get("temp_total")
        if temp_total == 1 or temp_total % 3 == 0 or temp_total >= total:
            task.update_state(state=state, meta=meta)

    def handle_chunk_error(chunk_dict, e, chunk_start_time, chunk_log_list, default_msg=''):
        nonlocal doc_status
        doc_status = settings.FAILURE
        chunk_end_time = time.time()
        chunk_dict.update({
            'error_key': 'async_appoint_document_chunk_embeddings_coroutine: ' + str(default_msg),
            'error_message': str(e),
            'chunk_status': settings.FAILURE,
            'chunk_end_time': chunk_end_time,
            'chunk_total_time': chunk_end_time - chunk_start_time
        })
        chunk_log_list.append(chunk_dict)
        return doc_status

    def embedding_error_process_flow(func_name, error_key, corpid, knowledge_id, doc_id, chunk_ids, e):
        nonlocal success_dict
        nonlocal meta
        nonlocal total
        nonlocal state
        temp_counts = len(chunk_ids)
        success_dict.update({"error": success_dict.get("error") + temp_counts if success_dict.get("error") else temp_counts,
                            "is_success": False,
                            "temp_total": success_dict.get("temp_total") + temp_counts if success_dict.get("temp_total") else temp_counts})
        meta.update({"success": success_dict.get("success"),
                    "current": int(success_dict.get("temp_total") / total * 100),
                    "total": total,
                    "error": success_dict.get("error"),
                    "exc_message": str(e)
                    })  # 更新 meta 的 error 字段
        task.update_state(state=state, meta=meta)
        # update_task()
        knowledge_controller.update_knowledge_doc_chunk_batch(chunk_ids, {'result': 'FAILED'})
        task_logger.exception(
            '指定向量生成失败 <%s>: %s. corpid:【%s】,aigc_knowledge_id:【%s】,doc_id:【%s】,chunk_ids: 【%s】, 错误原因:【%s】' % (
                func_name, error_key, corpid, knowledge_id, doc_id, str(chunk_ids), str(e)))

    async def process_doc_chunk(semaphore, knowledge_controller, doc_chunk, model_path, corpid, knowledge_id, doc_id, chunk_dict, chunk_start_time, chunk_log_list):
        async with semaphore:
            try:
                knowledge_controller.update_knowledge_doc_chunk(doc_chunk['chunk_id'], {'result': 'RUNNING'})
                emb_data = await knowledge_controller.async_create_embeddings(doc_chunk['content'], str(doc_chunk['doc_id']), model_path)
                return emb_data
            except Exception as e:
                # handle_chunk_error(chunk_dict, e, chunk_start_time, chunk_log_list)
                # embedding_error_process_flow("async_appoint_document_chunk_embeddings_coroutine", "create_embeddings", corpid, knowledge_id, doc_id, doc_chunk['chunk_id'], str(e))
                return {'code': -1, 'error_msg': "异步函数process_doc_chunk出错：" + str(e)}

    async def process_doc_chunks(knowledge_controller, doc_chunk_list, model_path, corpid, knowledge_id, doc_id, chunk_dict, chunk_start_time, chunk_log_list):
        semaphore = asyncio.Semaphore(10)
        task_list = []
        for doc_chunk in doc_chunk_list:
            task = asyncio.create_task(process_doc_chunk(semaphore, knowledge_controller, doc_chunk, model_path, corpid, knowledge_id, doc_id, chunk_dict, chunk_start_time, chunk_log_list))
            task_list.append(task)
        results = await asyncio.gather(*task_list)
        return results


    mapping = {
        "properties": {
            "chunk_id": {
                "type": "keyword"
            },
            "doc_id": {
                "type": "keyword"
            },
            "knowledge_id": {
                "type": "keyword"
            },
            "content_id": {
                "type": "keyword"
            },
            "description": {
                "type": "text"
            },
            "character_count": {
                "type": "integer"
            },
            "hit_count": {
                "type": "integer"
            },
            "tokens": {
                "type": "integer"
            },
            "status": {
                "type": "boolean"
            },
            "vector": {
                "type": "dense_vector",
                "dims": dims,
                "index": True
            },
            "delete_flag": {
                "type": "boolean"
            }
        }
    }

    # try:
    for doc_id in doc_ids:
        doc_start_time = time.time()
        doc_status = settings.SUCCESS
        doc_log_dict = {'knowledge_id': knowledge_id, 'doc_id': doc_id, 'doc_status': doc_status}
        chunk_log_list = list()
        total_tokens = 0
        knowledge_controller.update_knowledge_doc(doc_id, {'tokens': total_tokens, 'result': 'RUNNING','current_node': '向量化','state':'进行中'})
        doc = knowledge_controller.get_knowledge_document_detail(doc_id)
        doc_chunk_dict = knowledge_controller.get_knowledge_doc_chunk_list(doc_id, {'is_all': 'all', 'chunk_ids': chunk_ids})
        if not doc_chunk_dict or not doc_chunk_dict.get('data_list'):
            # 分片失败 向量不继续执行
            all_doc_chunk_dict = knowledge_controller.get_knowledge_doc_chunk_list(doc_id, {'is_all': 'all'})
            if not all_doc_chunk_dict or not all_doc_chunk_dict.get('data_list'):
                meta['data'].update({str(doc_id): 'FAILURE'})
                # 更新doc的切片状态为失败
                knowledge_controller.update_knowledge_doc(doc_id, {'result': 'FAILED', 'status': 'FAILED','current_node': '向量化','state':'失败'})
            continue
        total_chunk_count = len(doc_chunk_dict['data_list'])
        for i in range(0, total_chunk_count, 20):
            chunk_start_time = time.time()
            chunk_id_list = [doc_chunk['chunk_id'] for doc_chunk in doc_chunk_dict['data_list'][i:i+20]]
            chunk_dict = {"chunk_ids": chunk_id_list, 'chunk_status': settings.SUCCESS, 'error_key': '', 'error_message': '', 'chunk_start_time': chunk_start_time, 'chunk_end_time': 0, 'chunk_total_time': 0}
            knowledge_controller.update_knowledge_doc_chunk_batch(chunk_id_list, {'result': 'RUNNING'})
            results = asyncio.run(process_doc_chunks(knowledge_controller, doc_chunk_dict['data_list'][i:i+20], model_path, corpid, knowledge_id, doc_id, chunk_dict, chunk_start_time, chunk_log_list))
            is_error = 0
            error_msg = ''
            for emb_data in results:
                if emb_data['code'] == -1:
                    is_error = 1
                    error_msg = emb_data.get('error_msg', "create_embedding return code not 0")
                    task_logger.info(f'{error_msg}')
                    break
            if is_error == 1:
                handle_chunk_error(chunk_dict, error_msg, chunk_start_time, chunk_log_list, 'async_create_embedding return code not 0')
                embedding_error_process_flow("async_appoint_document_chunk_embeddings_coroutine", "async_create_embedding return code not 0", corpid, knowledge_id, doc_id, chunk_id_list, emb_data.get('error_msg', "create_embedding return code not 0"))
                continue
            es_batch_create_list = list()
            for emb_data, doc_chunk in zip(results, doc_chunk_dict.get('data_list')[i:i+20]):
                # if emb_data['code'] == 0:
                data = {
                            "chunk_id": doc_chunk['chunk_id'],
                            "doc_id": doc_chunk['doc_id'],
                            "knowledge_id": knowledge_id,
                            "content_id": doc.get('content_id', ''),
                            "description": doc_chunk['content'],
                            "character_count": doc_chunk['character_count'],
                            "hit_count": doc_chunk['hit_count'],
                            "tokens": emb_data['total_tokens'],
                            "status": True,
                            "vector": emb_data['vector'],
                            "delete_flag": False
                        }
                es_batch_create_list.append(data)
                # else:
                #     handle_chunk_error(chunk_dict, emb_data.get('error_msg', "create_embedding return code not 0"), chunk_start_time, chunk_log_list)
                #     embedding_error_process_flow("async_appoint_document_chunk_embeddings_coroutine", "create_embedding return code not 0.r", corpid, knowledge_id, doc_id, doc_chunk['chunk_id'], emb_data.get('error_msg', "create_embedding return code not 0"))
                #     continue
            index_name = knowledge_controller.index_name + model_path
            try:
                knowledge_controller.es.insert_data_batch(index_name, es_batch_create_list, mapping)
            except Exception as e:
                handle_chunk_error(chunk_dict, e, chunk_start_time, chunk_log_list, 'ES batch insert Error')
                embedding_error_process_flow("async_appoint_document_chunk_embeddings_coroutine", "ES batch insert Error", corpid, knowledge_id, doc_id, chunk_id_list, str(e))
                continue
            knowledge_controller.update_knowledge_doc_chunk_batch_by_tokenlist(chunk_id_list, [emb_data.get('tokens') for emb_data in es_batch_create_list], ['FINISHED' for _ in chunk_id_list])
            total_tokens += sum([emb_data.get('tokens') for emb_data in es_batch_create_list])
            # knowledge_controller.update_knowledge_doc_chunk(doc_chunk['chunk_id'], {'tokens': emb_data['total_tokens'], 'result': 'FINISHED'})
            # knowledge_controller.update_knowledge_doc(doc_id, {'current_node': '向量化','state':'已完成','knlg_extract':knlg_extract})
            temp_count = len(chunk_id_list)
            success_dict.update({"success": success_dict.get("success") + temp_count if success_dict.get("success") else temp_count, "temp_total": success_dict.get("temp_total")+temp_count if success_dict.get("temp_total") else temp_count})
            success = success_dict.get("temp_total")
            if total > 0:
                meta['current'] = int(success / total * 100)
            else:
                meta['current'] = 0
            meta['total'] = total
            meta['success'] = success
            task_logger.info('async_appoint_document_chunk_embeddings_coroutine: knowledge_id: %s, doc_id:【%s】, chunk_id: 【%s】, current: %s, total:%s, success: %s, ' % (knowledge_id, doc_id, str(chunk_id_list), total, success, meta['current']))
            chunk_end_time = time.time()
            chunk_dict.update({'chunk_end_time': chunk_end_time, 'chunk_total_time': chunk_end_time - chunk_start_time})
            chunk_log_list.append(chunk_dict)
            # update_task()
            task.update_state(state=state, meta=meta)
        is_success = success_dict.get("is_success")
        doc_end_time = time.time()
        doc_log_dict.update({'chunk_log_list': chunk_log_list, "doc_status": doc_status, "doc_start_time": doc_start_time, "doc_end_time": doc_end_time, "doc_total_time": doc_end_time - doc_start_time})
        doc_log_list.append(doc_log_dict)
        result = 'FINISHED' if is_success else 'FAILED'
        if is_success:
            knowledge_controller.update_knowledge_doc(doc_id, {'result': result, 'current_node': '向量化','state':'已完成', 'knlg_extract': knlg_extract, 'tokens': total_tokens})
        else:
            knowledge_controller.update_knowledge_doc(doc_id, {'result': result, 'current_node': '向量化','state':'失败', 'knlg_extract': knlg_extract, 'tokens': total_tokens})

    meta.update({"doc_list": doc_log_list})
    state = 'SUCCESS'
    task.update_state(state=state, meta=meta)
    async_insert_embde_log_mongo.delay(corpid, doc_log_list)

@celery.task(bind=True, name="apps.ai.task.async_insert_embde_log_mongo")
def async_insert_embde_log_mongo(task, corpid, doc_list):
    mongo = LogMg(corpid)
    
    is_success, result = mongo.insert_many_embed_log(doc_list)
    state = settings.SUCCESS
    if is_success:
        meta = {'status': settings.SUCCESS, 'message': '文档分块embeddings插入成功', 'result': result}
    else:
        state = 'FAILED'
        meta = {'status': settings.FAILURE, 'message': '文档分块embeddings插入失败', 'result': result}
    task.update_state(state=state, meta=meta)



@celery.task(bind=True, name="apps.ai.task.async_document_chunk_clear")
def async_document_chunk_clear(task, corpid, aigc_model_id, knowledge_id, doc_ids, chunk_ids=None, rule_ids=None, call_words=None):
    meta = {"current": 0, "total": 0, 'success': 0, 'state': '', 'message': '', 'data': {}, 'exc_type': '',
            'exc_message': ''}
    state = 'PENDING'
    task_logger.info('开始清洗【%s】【%s】【%s】' % (corpid, task, knowledge_id))
    knowledge_controller = KnowledgeController(corpid)
    rule_controller = RuleController(corpid)
    knowledge_info = knowledge_controller.get_knowledge_detail(knowledge_id)
    if not knowledge_info:
        task.update_state(state='SUCCESS', meta=meta)
        return
    doc_list = knowledge_controller.get_knowledge_doc_list(knowledge_id, {'is_all': 'all', 'doc_ids': doc_ids})
    total = knowledge_controller.get_knowledge_doc_chunk_list_by_knowledge(knowledge_id, {'doc_ids': doc_ids}) if not chunk_ids else len(chunk_ids)
    success = 0
    if not rule_ids:
        rule_ids = [x['id'] for x in rule_controller.clear_model.get_all_rel_clear_rule(knowledge_id, active_do=True)]
    if not (rule_ids or call_words):
        task.update_state(state='SUCCESS', meta=meta)
        return
    else:
        aigc_model_info = AppController(corpid).get_model_info(aigc_model_id)
        aigc_type_id, model_path, model_id = aigc_model_info['aigc_type_id'], aigc_model_info['model_path'], aigc_model_info['aigc_model_id']
        if not rule_ids:
            rules = list()
        else:
            rules = rule_controller.clear_model.get_page_rule(ids=rule_ids, ps=1000)['data_list']
    try:
        for doc in doc_list['data_list']:
            knowledge_controller.update_knowledge_doc(doc['document_id'], {'current_node': '切片清洗','state':'进行中'})
            doc_chunk_list = knowledge_controller.get_knowledge_doc_chunk_list(doc['document_id'], {'is_all': 'all', 'chunk_ids': chunk_ids})
            for doc_chunk in doc_chunk_list['data_list']:
                try:
                    rule_controller.do_clear_action(task.request.id, doc_chunk['content'], str(doc_chunk['chunk_id']),
                                                    doc_chunk['doc_id'], aigc_type_id, model_path, knowledge_id, rules, model_id, call_words)
                    success += 1
                    if total > 0:
                        meta['current'] = int(success / total * 100)
                    else:
                        meta['current'] = 0
                    meta['total'] = total
                    meta['success'] = success
                    task_logger.info('async_document_chunk_clear: total:%s, success: %s, knowledge_id: %s, current: %s' % (total, success, knowledge_id, meta['current']))
                    knowledge_controller.update_knowledge_doc(doc['document_id'], {'current_node': '切片清洗','state':'已完成'})
                except Exception as e:
                    knowledge_controller.update_knowledge_doc(doc['document_id'], {'current_node': '切片清洗','state':'失败'})
                    task_logger.exception('async_document_chunk_clear:清洗失败【%s】【%s】【%s】' % (corpid, doc_chunk['chunk_id'], e))
                task.update_state(state=state, meta=meta)
        meta['current'] = 100
        state = 'SUCCESS'
        task.update_state(state=state, meta=meta)
    except Exception as e:
        task_logger.error('async_document_chunk_clear:【%s】【%s】清洗失败 %s' % (corpid, knowledge_id, e))
        knowledge_controller.update_knowledge_doc(doc['document_id'], {'current_node': '切片清洗','state':'失败'})
        meta.update({'message': str(e)})
        state = 'FAILURE'
        task.update_state(state=state, meta=meta)


@celery.task(bind=True, name="apps.ai.task.async_qa_embedding")
def async_qa_embedding(task, corpid, qa_lib_id, oss_url, creator, qa_modal):
    controller = QaController(corpid)
    try:
        task_logger.info(f"oss_url: {oss_url}")
        df = pd.read_excel(oss_url)
        df = df.fillna('')
        # 过滤 question 为空的数据
        df = df[df['question'] != '']
        total = len(df)
        task_logger.info(f"total: {total}")
        for index, row in df.iterrows():
            emb_data = controller.create_embeddings(row['question'], str(qa_lib_id))
            if emb_data['code'] == 0:
                controller.insert_qa({"vector": emb_data['vector'], "question": row['question'], "answer": row['answer'],
                                        "status": True, "creator": creator,'qa_modal':qa_modal,
                                        "tokens": emb_data['total_tokens'],
                                      "qa_lib_id": qa_lib_id, "file": oss_url, "add_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"), "update_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")})
            success = index + 1
            current = success / total
            current = int(current * 100)
            task.update_state(state='PENDING', meta={'current': current, 'total': total, 'success': success})
        task.update_state(state='SUCCESS', meta={'current': 100, 'total': total, 'success': success})
    except Exception as e:
        task_logger.error('async_qa_embedding:【%s】向量生成失败 %s' % (corpid, e))


@celery.task(bind=True, name="apps.ai.task.async_extract_video_frame")
def async_extract_video_frame(task, corp_id, doc_url):
    meta = {'state': 'PENDING', 'image_list': [], 'message':''}  # 初始化任务状态
    try:
        ip = settings.VIDEO_KEY_FRAME_EXTRACT  # API 地址

        headers = {
            "Content-Type": "application/json",
        }

        payload = json.dumps({"video_url": doc_url})

        response = requests.request("POST", ip, headers=headers, data=payload)
        if response.status_code == 200:
            result = response.json()


            image_list = result

            if not image_list:
                meta['image_list'] = []
            else:
                meta['state'] = 'SUCCESS'
                meta['image_list'] = image_list

            task.update_state(state='SUCCESS', meta=meta)
            return meta  # Celery 任务返回数据

        else:
            meta['state'] = 'FAILURE'
            meta['image_list'] = []
            raise Exception(f"API 请求失败，状态码: {response.status_code}, 响应: {response.text}")

    except Exception as e:
        meta['state'] = 'FAILURE'
        meta['image_list'] = []
        task.update_state(state='FAILURE', meta=meta)
        task_logger.error(f"async_extract_video_frame:【{corp_id}】提取关键帧生成失败 - {e}")
        return meta

@celery.task(bind=True, name="apps.ai.task.async_extract_audio_text")
def async_extract_audio_text(task, corp_id, audio_url):
    """
    异步任务：调用外部 API 进行音频转文字提取
    """
    meta = {'state': 'PENDING','audio_url':audio_url,'content': ''}  # 初始化任务状态
    try:
        ip = settings.AUDIO_TEXT_EXTRACT  # API 地址

        headers = {
            "Content-Type": "application/json",
        }

        payload = json.dumps({"url": audio_url})

        response = requests.request("POST", ip, headers=headers, data=payload)

        if response.status_code == 200:
            try:
                result = response.json()
            except json.JSONDecodeError:
                raise ValueError(f"API 返回的不是有效 JSON 格式: {response.text}")

            if not isinstance(result, dict) or "text" not in result:
                raise ValueError(f"API 返回的数据格式不正确: {result}")

            content = result["text"]  # 提取 API 解析出的文本

            meta.update({'state': 'SUCCESS', 'content': content})
            task.update_state(state='SUCCESS', meta=meta)
            return meta  # Celery 任务返回数据

        else:
            raise Exception(f"API 请求失败，状态码: {response.status_code}, 响应: {response.text}")

    except Exception as e:
        meta.update({'state': 'FAILURE', 'content': str(e)})
        task.update_state(state='FAILURE', meta=meta)
        return meta

@celery.task(bind=True, name="apps.ai.task.async_extract_audio_list_text")
def async_extract_audio_list_text(task, corp_id, audio_list):
    """
    异步任务：调用外部 API 进行音频转文字提取
    """
    results = []
    total = len(audio_list)
    success = 0
    bucket_name = settings.KNOWLEDGE_OSS_BUCKET_NAME
    relative_path = 'audio'
    task.update_state(state='PENDING', meta={'total': total, 'success': success, 'current': 0,'progress':0})

    for index, audio_info in enumerate(audio_list):
        doc_id = audio_info['doc_id']
        audio_url = audio_info['audio_url']
        object_name = f"{relative_path}/{doc_id}.md"
        meta = {
            'current': index + 1,
            'total': total,
            'success': success,
            'state': 'PENDING',
            'audio_url': audio_url,
            'content': ''
        }

        try:
            ip = settings.AUDIO_TEXT_EXTRACT
            headers = {"Content-Type": "application/json"}
            payload = json.dumps({"url": audio_url})

            response = requests.post(ip, headers=headers, data=payload, timeout=10)

            if response.status_code != 200:
                raise Exception(f"API 请求失败，状态码: {response.status_code}, 响应: {response.text}")

            try:
                result = response.json()
            except json.JSONDecodeError:
                raise ValueError(f"API 返回的不是有效 JSON 格式: {response.text}")

            if not isinstance(result, dict) or "text" not in result:
                raise ValueError(f"API 返回的数据格式不正确: {result}")

            content = result["text"]

            # content = "测试文字测试文字测试文字测试文字测试文字测试文字"
            content = content.encode('utf-8')
            doc_clear_url = minio_util.upload_to_minio(bucket_name,object_name,content)
            KnowledgeController(corp_id).update_knowledge_doc(doc_id,{'doc_clear_url':doc_clear_url})
            meta.update({'state': 'SUCCESS', 'doc_clear_url': doc_clear_url,'doc_id':doc_id})
            time.sleep(5)
            success += 1

        except Exception as e:
            meta.update({'state': 'FAILURE', 'erro': str(e)})
        meta['success'] = success
        results.append(meta)

        # 每处理完一个都更新进度
        progress = int(((index + 1) / total) * 100)
        task.update_state(state='PENDING', meta={
            'current': index + 1,
            'total': total,
            'success': success,
            'progress': progress,
            'last_result': results,
        })

    # 最终任务完成状态
    progress = int((success / total) * 100) if total > 0 else 100
    task.update_state(state='SUCCESS',  meta={"progress":progress, "results": results})
    return {"results": results}


@celery.task(bind=True, name="apps.ai.task.async_chunk_hits")
def async_chunk_hits(task, corpid, hit_list):
    knowledge_controller = KnowledgeController(corpid)
    try:
        for item in hit_list:
            chunk_id = item['_source']['chunk_id']
            knowledge_controller.add_chunk_hits(chunk_id, 1)
    except Exception as e:
        task_logger.error('async_chunk_hits:【%s】向量搜索失败 %s' % (corpid, e))


@celery.task(bind=True, name="apps.ai.task.async_update_ai_app_session_record")
def async_update_ai_app_session_record(task, corpid, answer, reasoning_content, session_id, message_id, final_images_list, hit_doc_list, doc_images_list=[]):
    app_controller = AppController(corpid)
    try:
        app_controller.update_mg_app_session_record({'session_id': session_id, '_id': ObjectId(message_id)},
                                                    {'answer': answer,
                                                     'reasoning_content': reasoning_content,
                                                     'hit_images_list': final_images_list, 'hit_doc_list': hit_doc_list,
                                                     'doc_images_list': doc_images_list,
                                                     'answer_time': datetime.datetime.strftime(datetime.datetime.now(),
                                                                                               "%Y-%m-%d %H:%M:%S")})
    except Exception as e:
        task_logger.error('async_update_ai_app_session_record:【%s】更新记录失败message_id【%s】 %s' % (corpid, message_id, e))


@celery.task(name="apps.ai.task.async_add_session_flow_record")
def async_add_session_flow_record(corpid, _index, flow_id, _type, data_json={}, content='', reasoning_content=''):
    app_controller = AppController(corpid)
    try:
        app_controller.create_app_session_flow_record(_index, flow_id, _type, data_json, content, reasoning_content)
    except Exception as e:
        task_logger.error('async_update_ai_session_flow_record:【%s】添加记录失败【%s】 %s' % (corpid, flow_id, e))


@celery.task(name="apps.ai.task.async_add_statistics_session")
def async_add_statistics_session(corpid, is_create_index, is_success_session, session_start_time, anwer_time, session_end_time, message, app_id, tp_user_id, session_id, is_new_session, the_token, old_app_id, is_aggregate, app_name, old_app_name):
    
    app_controller = AppController(corpid)
    try:
        app_controller.add_statistics_session(is_success_session, session_start_time, anwer_time, session_end_time, message, app_id, tp_user_id, session_id, is_new_session, the_token, old_app_id, is_aggregate, app_name, old_app_name, is_create_index)
    except Exception as e:
        task_logger.error(f'async_add_statistics_session:【{corpid}】添加记录失败 user_id: session_id 【{tp_user_id}: {session_id}】, error: {e}')


@celery.task(name="apps.ai.task.async_add_aggregate_statistics_session")
def async_add_aggregate_statistics_session(corpid, is_create_index, app_id, old_app_id, app_name, old_app_name, scene_type):
    app_controller = AppController(corpid)
    try:
        app_id = str(app_id)
        old_app_id = str(old_app_id)
    except Exception as e:
        task_logger.error(f'async_add_statistics_session:【{corpid}】id转str失败 应用: 命中应用 【{old_app_name}: {app_name}】, error: {e}')
        # task_logger.error(e)
    try:
        app_controller.add_aggregate_statistics_session(app_id, old_app_id, app_name, old_app_name, scene_type, is_create_index)
    except Exception as e:
        task_logger.error(f'async_add_statistics_session:【{corpid}】添加记录失败 应用: 命中应用 【{old_app_name}: {app_name}】, error: {e}')
# @celery.task(bind=True, name="apps.ai.task.update_content_info_orm")
# def update_content_info_orm(task):
#     ContentInfoController().add_content_info_by_chunk_image_id()
    

@celery.task(bind=True, name="apps.ai.task.refresh_embed_status")
def refresh_embed_status(task):
    corp_list = MasterAction().get_all_corp()
    task_logger.info('测试')
    try:
        for corp in corp_list:
            corpid = corp['corpid']
            task_logger.info(f'测试{corpid}')
            controller = KnowledgeController(corp['corpid'])
            controller.refresh_model_status()
    except:
        task_logger.error(f"执行向量查询更新失败{corpid}")
@celery.task(bind=True, name="apps.ai.task.timing_update_content_embeddings")
def timing_update_content_embeddings(task):
    corp_list = MasterAction().get_all_corp()
    mapping = {
        "properties": {
            "content_id": {
                "type": "keyword"
            },
            "content_type_id": {
                "type": "keyword"
            },
            "description": {
                "type": "text",
                "analyzer": "ik_max"
            },
            "character_count": {
                "type": "integer"
            },
            "hit_count": {
                "type": "integer"
            },
            "tokens": {
                "type": "integer"
            },
            "publish_status": {
                "type": "integer"
            },
            "vector": {
                "type": "dense_vector",
                "dims": 1536,
                "index": True
            },
            "delete_flag": {
                "type": "integer"
            },
            "add_time": {
                "type": "keyword"
            },
        }
    }
    try:
        for corp in corp_list:

            try:
                controller = KnowledgeController(corp['corpid'])
                es = controller.es
                index_name = controller.content_index_name
                model_path = controller.default_model_path
            except Exception as e:
                task_logger.error('timing_update_content_embeddings:【%s】获取es连接失败 %s' % (corp['corpid'], e))
            else:

                next_time = controller.search_es_content_last()
                # next_time = 'all'
                all_content_list = controller.get_content_train_data_list(next_time)
                task_logger.info('timing_update_content_embeddings:【%s】获取需要更新数据 %s' % (corp['corpid'], len(all_content_list)))
                total = len(all_content_list)
                i = 1
                for info in all_content_list:
                    task_logger.info(
                        'timing_update_content_embeddings:【%s】更新进度 %s/%s 素材ID【%s】' % (corp['corpid'], i, total, info['content_id']))
                    emb_data = controller.create_embeddings(info['content'], str(corp['corpid']), model_path)

                    if emb_data['code'] == 0:
                        # 清空 es
                        controller.del_es_content([info['content_id']])
                        data = {
                            "content_id": info['content_id'],
                            "content_type_id": info['content_type_id'],
                            "description": info['content'],
                            "character_count": len(info['content']),
                            "hit_count": 0,
                            "tokens": emb_data['total_tokens'],
                            "publish_status": int(info['publish_status']),
                            "vector": emb_data['vector'],
                            "delete_flag": 0,
                            'add_time': info['add_time']
                        }

                        es.insert_data(index_name, data, mapping, ik_set=True)
                        task_logger.info('timing_update_content_embeddings:【%s】基础信息更新成功【%s】' % (corp['corpid'], info['content_id']))
                    if info['doc_list']:
                        info['doc_list'] = json.loads(info['doc_list'])
                        for doc in info['doc_list']:
                            if not doc['url']:
                                res = {
                                    'code': -1,
                                    'message': '文档链接为空'
                                }
                            else:
                                res = controller.document_splitter_v1(doc['url'], chunk_size=256, chunk_overlap=20)
                            if res['code'] == 0:
                                doc_num = 0
                                description_str = ''
                                for doc_chunk in res['data']:
                                    emb_data = controller.create_embeddings(doc_chunk['content'], str(corp['corpid']), model_path)
                                    if emb_data['code'] == 0:
                                        data = {
                                            "content_id": info['content_id'],
                                            "content_type_id": info['content_type_id'],
                                            "description": doc_chunk['content'],
                                            "character_count": len(info['content']),
                                            "hit_count": 0,
                                            "tokens": emb_data['total_tokens'],
                                            "publish_status": int(info['publish_status']),
                                            "vector": emb_data['vector'],
                                            "delete_flag": 0,
                                            'add_time': info['add_time']
                                        }
                                    es.insert_data(index_name, data, mapping, ik_set=True)
                                    if doc_num < 3:
                                        description_str += doc_chunk['content']
                                    doc_num += 1
                                    task_logger.info(
                                        'timing_update_content_embeddings:【%s】文档信息更新成功【%s】' % (corp['corpid'], info['content_id']))
                                if description_str:  # 更新文档摘要
                                    message = "请根据以下内容进行摘要总结，%s。 要求：仅返回摘要内容，不返回其他内容，内容格式为文本格式，不要markdown格式。" % description_str
                                    response = AppController(corp['corpid']).ai_app_chat(message, [], QIANWEN_CODE, "qwen-turbo", is_stream=False)
                                    abstract = ''
                                    for item, _ in response:
                                        abstract = item

                                    task_logger.info('doc generated abstract: %s' % abstract)
                                    controller.content_model.update_content_info_by_comment(info['content_id'], abstract)

                            else:
                                task_logger.error('timing_update_content_embeddings:【%s】文件信息更新失败【%s】 %s' % (
                                    corp['corpid'], info['content_id'], res))
                    i += 1
    except Exception as e:
        task_logger.error('timing_update_content_embeddings:【%s】更新记录失败 %s' % (corp['corpid'], e))


@celery.task(bind=True, name="apps.ai.task.async_update_search_record")
def async_update_search_record(task, corpid, search_key, content_ids, tp_user_id):
    app_controller = AppController(corpid)
    try:
        record = {
            'search_key': search_key,
            'tp_user_id': tp_user_id,
            'add_time': datetime.datetime.strftime(datetime.datetime.now(), "%Y-%m-%d %H:%M:%S")
        }
        record_id = app_controller.insert_mg_search_record(record)
        for content_id in content_ids:
            content_record = {
                'search_record_id': record_id,
                'content_id': content_id,
                'add_time': datetime.datetime.strftime(datetime.datetime.now(), "%Y-%m-%d %H:%M:%S")
            }
            app_controller.insert_mg_search_content_record(content_record)

    except Exception as e:
        task_logger.error('async_update_search_record:【%s】更新记录失败【%】 %s' % (corpid, search_key, e))


@celery.task(bind=True, name="apps.ai.task.timing_update_question_embeddings")
def timing_update_question_embeddings(task):
    corp_list = MasterAction().get_all_corp()
    mapping = {
        "properties": {
            "rel_question_tup_id": {
                "type": "keyword"
            },
            "question_name": {
                "type": "keyword"
            },
            "question_type_name": {
                "type": "keyword"
            },
            "description": {
                "type": "text"
            },
            "answer_type": {
                "type": "integer"
            },
            "character_count": {
                "type": "integer"
            },
            "hit_count": {
                "type": "integer"
            },
            "tokens": {
                "type": "integer"
            },
            "vector": {
                "type": "dense_vector",
                "dims": 1536,
                "index": True
            },
            "delete_flag": {
                "type": "integer"
            },
            "add_time": {
                "type": "keyword"
            },
        }
    }
    try:
        for corp in corp_list:

            try:
                controller = QuestionController(corp['corpid'])
                es = controller.es
                index_name = controller.question_index_name
                model_path = controller.default_model_path
            except Exception as e:
                task_logger.error('timing_update_question_embeddings:【%s】获取es连接失败 %s' % (corp['corpid'], e))
            else:

                next_id = controller.search_es_question_last()
                all_list = controller.get_question_train_data(next_id)
                total = len(all_list)
                i = 1
                for info in all_list:
                    task_logger.info(
                        'timing_update_question_embeddings:【%s】更新进度 %s/%s ID【%s】' % (
                            corp['corpid'], i, total, info['rel_question_tup_id']))
                    content = "问题：%s\n" % info['question_name']
                    if info['question_type_name']:
                        content += "类型：%s\n" % info['question_type_name']
                    if info['content']:
                        content += "答案：%s\n" % info['content']

                    emb_data = KnowledgeController(corp['corpid']).create_embeddings(content, str(corp['corpid']), model_path)

                    if emb_data['code'] == 0:
                        data = {
                            "rel_question_tup_id": str(info['rel_question_tup_id']),
                            "question_name": info['question_name'],
                            "description": content,
                            "character_count": len(content),
                            "hit_count": 0,
                            "answer_type": info['answer_type'],
                            "tokens": emb_data['total_tokens'],
                            "question_type_name": info['question_type_name'],
                            "vector": emb_data['vector'],
                            "delete_flag": 0,
                            'add_time': info['add_time']
                        }

                        es.insert_data(index_name, data, mapping)
                        task_logger.info(
                            'timing_update_question_embeddings:【%s】基础信息更新成功【%s】' % (corp['corpid'], info['rel_question_tup_id']))

                    i += 1
    except Exception as e:
        task_logger.error('timing_update_question_embeddings:【%s】更新记录失败 %s' % (corp['corpid'], e))


@celery.task(bind=True, name="apps.ai.task.timing_update_question_point_plan_embeddings")
def timing_update_question_point_plan_embeddings(task):
    corp_list = MasterAction().get_all_corp()

    mapping = {
        "properties": {
            "qpp_id": {
                "type": "keyword"
            },
            "sore_point": {
                "type": "keyword"
            },
            "question_type_name": {
                "type": "keyword"
            },
            "description": {
                "type": "text"
            },
            "question_type_id": {
                "type": "integer"
            },
            "answer_type": {
                "type": "integer"
            },
            "character_count": {
                "type": "integer"
            },
            "hit_count": {
                "type": "integer"
            },
            "tokens": {
                "type": "integer"
            },
            "vector": {
                "type": "dense_vector",
                "dims": 1536,
                "index": True
            },
            "delete_flag": {
                "type": "integer"
            },
            "add_time": {
                "type": "keyword"
            },
        }
    }
    try:
        for corp in corp_list:

            try:
                controller = QuestionController(corp['corpid'])
                es = controller.es
                index_name = controller.question_point_plan_index_name
                model_path = controller.default_model_path
            except Exception as e:
                task_logger.error('timing_update_question_point_plan_embeddings:【%s】获取es连接失败 %s' % (corp['corpid'], e))
            else:

                next_id = controller.search_es_qpp_last()
                all_list = controller.get_qpp_train_data(next_id)
                total = len(all_list)
                i = 1
                for info in all_list:
                    task_logger.info(
                        'timing_update_question_point_plan_embeddings:【%s】更新进度 %s/%s ID【%s】' % (
                            corp['corpid'], i, total, info['qpp_id']))
                    content = "痛点：%s\n" % info['sore_point']
                    if info['question_type_name']:
                        content += "类型：%s\n" % info['question_type_name']
                    if info['describe']:
                        content += "描述：%s\n" % info['describe']
                    if info['function_point']:
                        content += "方案：%s\n" % info['function_point']

                    emb_data = KnowledgeController(corp['corpid']).create_embeddings(content, str(corp['corpid']), model_path)

                    if emb_data['code'] == 0:
                        data = {
                            "qpp_id": str(info['qpp_id']),
                            "answer_type": info['answer_type'],
                            "description": content,
                            "character_count": len(content),
                            "hit_count": 0,
                            "tokens": emb_data['total_tokens'],
                            "question_type_name": info['question_type_name'],
                            "vector": emb_data['vector'],
                            "delete_flag": 0,
                            'add_time': info['add_time']
                        }

                        es.insert_data(index_name, data, mapping)
                        task_logger.info(
                            'timing_update_question_point_plan_embeddings:【%s】基础信息更新成功【%s】' % (corp['corpid'], info['qpp_id']))

                    i += 1
    except Exception as e:
        task_logger.error('timing_update_question_point_plan_embeddings:【%s】更新记录失败 %s' % (corp['corpid'], e))


@celery.task(bind=True, name="apps.ai.task.timing_update_content_images")
def timing_update_content_images(task):
    corp_list = MasterAction().get_all_corp()
    mapping = {
        "properties": {
            "content_id": {
                "type": "keyword"
            },
            "content_chunk_id": {
                "type": "keyword"
            },
            "description": {
                "type": "text"
            },
            "tokens": {
                "type": "integer"
            },
            "vector": {
                "type": "dense_vector",
                "dims": 1024,
                "index": True
            },
            "add_time": {
                "type": "keyword"
            },
        }
    }
    for corp in corp_list:
        try:
            corp_id = corp['corpid']
            knowledge_controller = KnowledgeController(corp_id)
            es = knowledge_controller.es

        except Exception as e:
            task_logger.error('timing_update_content_images:【%s】获取es连接失败 %s' % (corp_id, e))
        else:
            next_time = knowledge_controller.search_es_content_graph_last()
            # task_logger.info(next_time)
            # break
            all_content_list = knowledge_controller.get_content_graph_data_list(next_time)
            # task_logger.info(all_content_list)
            task_logger.info('timing_update_content_images:【%s】获取需要更新数据 %s' % (corp_id, len(all_content_list)))
            i = 1
            for content in all_content_list:
                img_list = content['pic_url'].split(',')

                for index, img_url in enumerate(img_list):
                    description = GraphServerSdk().graph_to_description(img_url)
                    task_logger.info(f'timing_update_content_images:\n 图片url {img_url} description: {description}')
                    if description:
                        emb_data = knowledge_controller.create_embeddings(description, corp_id, knowledge_controller.default_local_emb_model_path)
                        if emb_data['code'] == 0:
                            data = {
                                "content_id": content['content_id'],
                                "content_chunk_id": str(index),
                                "description": description,
                                "tokens": emb_data['total_tokens'],
                                "vector": emb_data['vector'],
                                "add_time": content['add_time']
                            }
                            index_name = knowledge_controller.content_graph_index_name + knowledge_controller.default_local_emb_model_path
                            task_logger.info('index_name: %s' % index_name)
                            delete_query = {
                                "query": {
                                    "bool": {
                                        "must": [
                                            {"term": {
                                                "content_id": {
                                                    "value": content['content_id']
                                                }
                                            }
                                            },
                                            {"term": {
                                                "content_chunk_id": {
                                                    "value": str(index)
                                                }
                                            }
                                            }
                                        ]
                                    }
                                }
                            }
                            is_del = es.delete_data(index_name, delete_query)
                            task_logger.info('timing_update_content_images:【%s】is_del %s' % (corp_id, is_del))

                            es.insert_data(index_name, data, mapping)
                            task_logger.info(
                                'timing_update_content_images:【%s】图像识别完成 %s， %s' % (corp_id, content['content_id'], index))

@celery.task(bind=True, name="apps.ai.task.timing_update_knowledge_chunk_images")
def timing_update_knowledge_chunk_images(task):
    corp_list = MasterAction().get_all_corp()
    mapping = {
        "properties": {
            "doc_id": {
                "type": "keyword"
            },
            "chunk_id": {
                "type": "keyword"
            },
            "description": {
                "type": "text"
            },
            "tokens": {
                "type": "integer"
            },
            "vector": {
                "type": "dense_vector",
                "dims": 1024,
                "index": True
            },
            "add_time": {
                "type": "keyword"
            },
        }
    }
    for corp in corp_list:
        try:
            corp_id = corp['corpid']
            knowledge_controller = KnowledgeController(corp_id)
            es = knowledge_controller.es

            # 检查索引是否存在，如果不存在则创建
            index_name = knowledge_controller.knowledge_graph_index_name + knowledge_controller.default_local_emb_model_path
            task_logger.info(f'timing_update_knowledge_chunk_images:【{corp_id}】准备检查索引 {index_name}')
            if not es.index_exists(index_name):
                task_logger.info(f'timing_update_knowledge_chunk_images:【{corp_id}】索引不存在，准备创建')
                try:
                    es.create_index(index_name, mapping)
                    task_logger.info(f'timing_update_knowledge_chunk_images:【{corp_id}】成功创建索引 {index_name}')
                except Exception as e:
                    task_logger.error(f'timing_update_knowledge_chunk_images:【{corp_id}】创建索引失败: {str(e)}')
            else:
                task_logger.info(f'timing_update_knowledge_chunk_images:【{corp_id}】索引已存在')

        except Exception as e:
            task_logger.error('timing_update_knowledge_chunk_images:【%s】获取es连接失败 %s' % (corp_id, e))
        else:
            next_time = knowledge_controller.search_es_knowledge_doc_graph_last()
            all_chunk_image_list = knowledge_controller.get_knowledge_doc_chunk_image_list(next_time)
            task_logger.info('timing_update_knowledge_chunk_images:【%s】获取需要更新数据 %s' % (corp_id, len(all_chunk_image_list)))
            i = 1
            for chunk_image in all_chunk_image_list:
                img_url =  chunk_image['image_url']
                description = GraphServerSdk().graph_to_description(img_url)
                task_logger.info(f'timing_update_knowledge_chunk_images:\n 图片url {img_url} description: {description}')
                if description:
                    emb_data = knowledge_controller.create_embeddings(description, corp_id, knowledge_controller.default_local_emb_model_path)
                    if emb_data['code'] == 0:
                        data = {
                            "doc_id": chunk_image['id'],
                            "chunk_id": chunk_image['chunk_id'],
                            "description": description,
                            "tokens": emb_data['total_tokens'],
                            "vector": emb_data['vector'],
                            "add_time": chunk_image['add_time']
                        }
                        task_logger.info('index_name: %s' % index_name)
                        delete_query = {
                            "query": {
                                "bool": {
                                    "must": [
                                        {"term": {
                                            "doc_id": {
                                                "value": chunk_image['id']
                                            }
                                        }
                                        },
                                        {"term": {
                                            "chunk_id": {
                                                "value": chunk_image['chunk_id']
                                            }
                                        }
                                        }
                                    ]
                                }
                            }
                        }
                        is_del = es.delete_data(index_name, delete_query)
                        task_logger.info('timing_update_knowledge_chunk_images:【%s】is_del %s' % (corp_id, is_del))

                        es.insert_data(index_name, data, mapping)
                        task_logger.info(
                            'timing_update_knowledge_chunk_images:【%s】图像识别完成  doc_id: %s， chunk_id: %s' % (corp_id, chunk_image['id'], chunk_image['chunk_id']))
                            

@celery.task(bind=True, name="apps.ai.task.timing_update_doc_chunk_data")
def timing_update_doc_chunk_data(task):
    """
    定时更新知识库文档统计信息
    """
    corp_list = MasterAction().get_all_corp()
    for corp in corp_list:
        # try:
        knowledge_controller = KnowledgeController(corp['corpid'])
        data_list = knowledge_controller.model.stat_chunk_data(is_today=True)
        total = len(data_list)
        task_logger.info('timing_update_doc_chunk_data:【%s】获取需要更新数据 %s' % (corp['corpid'], total))
        index = 1
        for data in data_list:
            update = {'tokens': data.get('tokens'), 'hit_count': data.get('hit_count'),
                      'character_count': data.get('character_count')}
            task_logger.info(f'update_doc_chunk_data: {update}')
            knowledge_controller.update_knowledge_doc(data['doc_id'], update)
            task_logger.info('timing_update_doc_chunk_data:【%s】更新记录 %s, 进度: %s/%s' % (corp['corpid'], data['doc_id'], index, total))
            index += 1
        # except Exception as e:
        #     task_logger.error('timing_update_doc_chunk_data:【%s】更新记录失败 %s' % (corp['corpid'], e))


@celery.task(bind=True, name="apps.ai.task.timing_update_content_info_aa")
def timing_update_content_info_aa(task):
    corp_list = MasterAction().get_all_corp()
    for corp in corp_list:
        ContentInfoController(corp['corpid']).auto_update()

@celery.task(bind=True, name="apps.ai.task.forage_document_combiner")
def forage_document_combiner(task, corpid, args):
    task_id = task.request.id
    task_logger.info(f'forage_document_combiner:【{task_id}】开始合成算料,corpid: {corpid},args: {args}')
    forage_id = args.get('forage_id')
    aigc_model_id = args.get('aigc_model_id', "")
    tp_user_id = args.get('tp_user_id', "")
    combine_num = args.get('combine_num', 10)
    random_percent = args.get('random_percent', 1)
    check_duplict = args.get('check_duplict', 1)
    filtered = 0
    qa_ids = args.get('qa_ids', [])
    record_id = ForageController(corpid).model.add_forage_combine_record(
        {'forage_id': forage_id,'aigc_model_id': str(aigc_model_id),'tp_user_id': str(tp_user_id),'task_id': str(task_id),'state': 'PENDING'}
    )
    meta = {
        "current": 0,
        "total": 1,
        "success": 0,
        "state": "PENDING",
        "message": "",
        "filtered": 0,
        "data": {},
        "record_id": record_id,
        "corpid": corpid
    }
    qa_list = []

    # 获取AIGC模型信息
    try:
        aigc_model_info = AppController(corpid).get_model_info(aigc_model_id)
        aigc_type_id = aigc_model_info.get('aigc_type_id')
        model_path = aigc_model_info.get('model_path')
        model_id = aigc_model_info.get('aigc_model_id')
    except Exception as e:
        error_msg = f'获取AIGC模型信息失败: {e},aigc_model_id: {aigc_model_id}'
        meta = handle_failure(task, meta, error_msg)
        return meta  # 终止函数

    # 获取算料信息
    task_logger.info(f'forage_id: {forage_id},random_percent: {random_percent},qa_ids: {qa_ids}')
    try:
        random_qa = ForageController(corpid).get_random_qa(forage_id, random_percent, qa_ids)
        if not random_qa:
            error_msg = "获取的随机QA为空"
            meta = handle_failure(task, meta, error_msg)
            return meta
        task_logger.info(f'forage_document_combiner:【{task_id}】获取算料信息 {random_qa[:100]}...')  # 截断长日志
        meta['current'] = 50
        task.update_state(state="PENDING", meta=meta)
    except Exception as e:
        error_msg = f'获取算料信息失败: {e},forage_id: {forage_id}'
        meta = handle_failure(task, meta, error_msg)
        return meta

    # 限制内容长度（使用常量）
    MAX_CONTENT_LENGTH = 30000
    if len(random_qa) > MAX_CONTENT_LENGTH:
        random_qa = random_qa[:MAX_CONTENT_LENGTH]

    combine_message = f"""
        请将以下内容中的问题和答案进行整合成{combine_num}条，整合后的内容需要包含问题和答案，不要包含其他内容：
        内容: {random_qa}

        - 严格按照如下格式进行返回，不要返回其他任何无关内容：
        [
            {{
                "question": "问题",
                "answer": "答案"
            }},
            {{
                "question": "问题",
                "answer": "答案"
            }},
            ...
        ]
        """

    # 调用AI模型进行整合
    try:
        combine_ret = asyncio.run(get_ai_response(
            corpid, combine_message, aigc_type_id, model_path, model_id
        ))
        task_logger.info(f'forage_document_combiner:【{task_id}】整合结果 {combine_ret}...')  # 截断长日志
        meta['current'] = 80
        task.update_state(state="PENDING", meta=meta)
    except Exception as e:
        error_msg = f'整合失败: {e}'
        meta = handle_failure(task, meta, error_msg)
        return meta

    # 处理返回结果
    
    try:
        combine_ret = combine_ret.strip()  # 去除首尾空格
        # 验证JSON格式（示例：检查是否以[开头和结尾）
        if not (combine_ret.startswith('[') and combine_ret.endswith(']')):
            raise ValueError("无效的JSON格式，缺少方括号")
        combine_data = json.loads(combine_ret)
        # 去重逻辑
        seen = set()
        for item in combine_data:
            question = item.get('question')
            answer = item.get('answer')
            if not (question and answer):
                continue
            key = (question, answer)
            if key not in seen:
                seen.add(key)
                qa_list.append({
                    'question': question,
                    'answer': answer,
                    'forage_id': forage_id,
                    'source': 'combine'
                })
        
        if qa_list:
            ForageOrm(corpid).bulk_add_forage_qa(qa_list)
            #查询forage_id对应的所有question
        if check_duplict == 1:
            filtered = 0
            all_qa_list = ForageController(corpid).page_forage_qa(
                    forage_id=forage_id,
                    file_ids=None,
                    qa_ids=None,
                    question=None,
                    source=None,
                    ps=99999,
                    po=1
                )
            existing_hashes = set()
            for qa in all_qa_list.get('data_list',[]):
                hash_parts = []
                hash_parts.append(hashlib.md5(qa['question'].encode('utf-8')).hexdigest())
                combined_hash = "_".join(hash_parts)
                if combined_hash in existing_hashes:
                    continue
                existing_hashes.add(combined_hash)
                if combined_hash in existing_hashes:
                    task_logger.info(f"数据库去重: 发现重复QA - ID: {qa.get('qa_id')}")
                    ForageController(corpid).up_forage_qa(
                        {'qa_id': qa['qa_id']},
                        {'delete_flag': 1,'delete_filter_type':2}
                    )
                    filtered += 1
                    continue
                    
                existing_hashes.add(combined_hash)
        meta.update({
            "state": "SUCCESS",
            "current": 100,
            "success": len(qa_list),
            "message": f"成功合成{len(qa_list)}条QA,去重{filtered}条"
        })
        task.update_state(state="SUCCESS", meta=meta)
        ForageOrm(corpid).up_forage_combine_record(
            {'record_id': record_id},
            {'state': 'SUCCESS'}
        )
        return meta

    except json.JSONDecodeError as e:
        error_msg = f'JSON解析失败: {e}, 原始内容: {combine_ret[:200]}...'
        meta = handle_failure(task, meta, error_msg)
        return meta
    except Exception as e:
        error_msg = f'处理返回结果失败: {e}'
        meta = handle_failure(task, meta, error_msg)
        return meta

# 辅助函数（可提取到公共模块）
def handle_failure(task, meta, error_msg):
    meta.update({
        "state": "FAILURE",
        "message": error_msg,
        "current": 100  # 标记为失败时设置为100%进度
    })
    task_logger.error(error_msg)
    if meta.get('record_id'):
        ForageOrm(meta.get('corpid')).up_forage_combine_record(
            {'record_id': meta.get('record_id')},
            {'state': 'FAILURE'}
        )
    task.update_state(state="FAILURE", meta=meta)
    return meta



@celery.task(bind=True, name="apps.ai.task.forage_document_cleaner")
def forage_document_cleaner(task, corpid, args):
    task_id = task.request.id
    task_logger.info(f'forage_document_cleaner:【{task_id}】开始清洗算料')

    # 获取参数
    forage_id = args.get('forage_id')
    qa_ids = args.get('qa_ids', [])
    clean_type_list = args.get('clean_type', [])
    aigc_model_id = args.get('aigc_model_id', "")
    filter_type_list = args.get('filter_type', [])
    deduplication_types = args.get('deduplication_type', [])
    model_path = args.get('model_path', "bge-large-zh-v1.5")
    tp_user_id = args.get('tp_user_id', "")
    # 初始化元数据和状态
    add_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    clear_log = {
            "add_time": add_time,
            "clear_type": clean_type_list,
            "filter_type": filter_type_list,
            "deduplication_type": deduplication_types,
            "model_path": str(model_path),
            "tp_user_id": str(tp_user_id),
            "qa_ids": qa_ids,
            "forage_id": str(forage_id),
            "corpid": str(corpid)
    }

    
    filtered = 0
    meta = {
        "current": 0,
        "total": 1,
        "success": 0,
        "state": "PENDING",
        "message": "",
        "filtered": 0,
        "data": {}
    }
    ForageController(corpid).model.up_forage(
        {'forage_id': forage_id},
        {'clear_type': str(clean_type_list),'filter_type':str(filter_type_list),'deduplication_type':str(deduplication_types)}
    )

    clean_type = ",".join([ForageCleanTypeMap.get(clean_type_id, clean_type_id) for clean_type_id in clean_type_list])
    task_logger.info(f'forage_document_cleaner:【{task_id}】清洗算料类型: {clean_type}')
    filter_type = ",".join([ForageFilterTypeMap.get(filter_type_id, filter_type_id) for filter_type_id in filter_type_list])
    task_logger.info(f'forage_document_cleaner:【{task_id}】过滤类型: {filter_type}')
    success = 0  # 初始化成功计数
    
    def filter_chinese(text):
        # 匹配所有中文字符的正则表达式
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        # 将中文字符替换为空字符串
        return chinese_pattern.sub('', text)
    
    def get_qa_list(ps=100,po=1):
        if not qa_ids:
            result = ForageController(corpid).page_forage_qa(
                forage_id=forage_id,
                file_ids=None,
                qa_ids=None,
                question=None,
                source=None,
                ps=ps,
                po=po
            )
            real_total = result.get('total', 0)
            total_pages = (real_total + 99) // 100  # 向上取整计算总页数
        else:
            result = ForageController(corpid).page_forage_qa(
                forage_id=forage_id,
                file_ids=None,
                qa_ids=qa_ids,
                question=None,
                source=None,
                ps=ps,
                po=po
            )
            real_total = result.get('total', 0)
            total_pages = (real_total + 99) // 100

        meta["total"] = real_total

        if real_total == 0:
            meta.update({
                "state": "SUCCESS",
                "current": 100,
                "message": "没有需要清洗的算料"
            })
            task.update_state(state="SUCCESS", meta=meta)
            return []

        task_logger.info(f'开始获取模型信息: 共{real_total}条记录，共{total_pages}页')
        return result.get('data_list', [])

   # 根据去重类型列表获取去重字段
    if deduplication_types:
        deduplication_fields = set()  # 使用集合避免重复
        for dtype in deduplication_types:
            if dtype in ForageDeduplicationTypeMap:
                if ForageDeduplicationTypeMap[dtype] == "question_answer":
                    deduplication_fields.add("question")
                    deduplication_fields.add("answer")
                else:
                    deduplication_fields.add(ForageDeduplicationTypeMap[dtype])
            else:
                task_logger.warning(f"无效的去重类型: {dtype}")
        
        deduplication_fields = list(deduplication_fields)  # 转换回列表
    else:
        deduplication_fields = []
    task_logger.info(f"去重字段: {deduplication_fields}")


    # 步骤1: 数据库级别去重
    def db_level_deduplication(qa_list):
        """数据库级别去重 - 基于哈希值的精确匹配"""

        deduplicated_qa = []
        existing_hashes = set()
        
        for qa in qa_list:
            hash_parts = []
            # 根据去重字段生成哈希
            if 'question' in deduplication_fields:
                hash_parts.append(hashlib.md5(qa['question'].encode('utf-8')).hexdigest())
            if 'answer' in deduplication_fields:
                hash_parts.append(hashlib.md5(qa['answer'].encode('utf-8')).hexdigest())
                
            # 组合哈希值作为唯一标识
            combined_hash = "_".join(hash_parts)
            # 检查是否已存在
            if combined_hash in existing_hashes:
                task_logger.info(f"数据库去重: 发现重复QA - ID: {qa.get('qa_id')}")
                ForageController(corpid).up_forage_qa(
                    {'qa_id': qa['qa_id']},
                    {'delete_flag': 1,'delete_filter_type':2}
                )
                nonlocal filtered
                filtered += 1
                continue
                
            existing_hashes.add(combined_hash)
            deduplicated_qa.append(qa)
            
        return deduplicated_qa

    async def vector_level_deduplication(qa_list, corpid, deduplication_fields, model_path="bge-large-zh-v1.5"):
        """向量语义级别去重 - 基于语义相似度"""            
        task_logger.info(f"开始计算{len(qa_list)}条QA的向量表示")
        
        # 为每个去重字段生成向量
        embeddings = {}
        for field in deduplication_fields:
            # 异步生成所有文本的向量
            field_embeddings = []
            for qa in qa_list:
                vector = await KnowledgeController(corpid).async_create_embeddings(qa[field], corpid, model_path)
                vector = vector.get('vector', None)
                field_embeddings.append(vector)
            
            embeddings[field] = field_embeddings
        # task_logger.info(f"向量生成完成:{embeddings}")
        # 构建相似度矩阵
        deduplicated_qa = []
        threshold = 0.85  # 语义相似度阈值
        
        for i in range(len(qa_list)):
            is_duplicate = False
            
            # 检查与已保留的QA的相似度
            for j in range(len(deduplicated_qa)):
                similarities = []
                
                # 根据去重字段计算相似度
                for field in deduplication_fields:
                    # 跳过生成失败的向量
                    if embeddings[field][i] is None or embeddings[field][j] is None:
                        continue
                        
                    # 计算余弦相似度
                    sim = util.pytorch_cos_sim([embeddings[field][i]], [embeddings[field][j]])[0][0]
                    similarities.append(sim)
                
                # 如果所有字段都无法生成向量，则跳过比较
                if not similarities:
                    continue
                    
                # 计算平均相似度
                avg_sim = sum(similarities) / len(similarities)
                
                if avg_sim >= threshold:
                    task_logger.info(f"向量去重: 发现相似QA - ID: {qa_list[i].get('qa_id')}")
                    ForageController(corpid).up_forage_qa(
                        {'qa_id': qa_list[i]['qa_id']},
                        {'delete_flag': 1,'delete_filter_type':2}
                    )
                    nonlocal filtered
                    filtered += 1
                    is_duplicate = True
                    break
                    
            if not is_duplicate:
                deduplicated_qa.append(qa_list[i])
                
        return deduplicated_qa

    # 获取原始QA列表
    original_qa_list = get_qa_list(ps=99999,po=1)
    if deduplication_fields:
    # 多级去重
        task_logger.info(f"开始多级去重: 原始数据量 {len(original_qa_list)}")

        # 第一级：数据库去重
        clear_log['mysql_deduplicated_add_time'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        db_deduplicated_qa = db_level_deduplication(original_qa_list)
        task_logger.info(f"数据库去重完成: 剩余数据量 {len(db_deduplicated_qa)}")
        clear_log['mysql_deduplicated_end_time'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        clear_log['mysql_deduplicated_num'] = len(original_qa_list) - len(db_deduplicated_qa)
        # 第二级：向量语义去重
        clear_log['vector_deduplicated_add_time'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        vector_model_path = model_path
        try:
            vector_deduplicated_qa = asyncio.run(
                vector_level_deduplication(
                    db_deduplicated_qa,
                    corpid,
                    deduplication_fields,
                    vector_model_path
                )
            )
        except Exception as e:
            task_logger.error(f"向量语义去重失败: {e}")
            meta.update({
                "state": "FAILURE",
                "message": f"向量语义去重失败: {e}"
            })
            task.update_state(state="FAILURE", meta=meta)
        task_logger.info(f"向量去重完成: 剩余数据量 {len(vector_deduplicated_qa)}")
        clear_log['vector_deduplicated_end_time'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        clear_log['vector_deduplicated_num'] = len(db_deduplicated_qa) - len(vector_deduplicated_qa)
        # 更新总数据量和进度
        meta["total"] = len(vector_deduplicated_qa)
        task_logger.info(f"去重后实际处理数据量: {len(vector_deduplicated_qa)}")

        qa_list = vector_deduplicated_qa
    else:
        clear_log['mysql_deduplicated_num'] = 0
        clear_log['vector_deduplicated_num'] = 0
        clear_log['mysql_deduplicated_add_time'] = None
        clear_log['vector_deduplicated_add_time'] = None
        clear_log['mysql_deduplicated_end_time'] = None
        clear_log['vector_deduplicated_end_time'] = None
        qa_list = original_qa_list

    try:
        aigc_model_info = AppController(corpid).get_model_info(aigc_model_id)
        aigc_type_id = aigc_model_info.get('aigc_type_id')
        model_path = aigc_model_info.get('model_path')
        model_id = aigc_model_info.get('aigc_model_id')
    except Exception as e:
        error_msg = f'获取AIGC模型信息失败: {e},aigc_model_id: {aigc_model_id}'
        meta.update({
            "state": "FAILURE",
            "message": error_msg
        })
        task_logger.error(error_msg)
        task.update_state(state="FAILURE", meta=meta)
    clear_log['filter_add_time'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    clear_log['clear_start_time'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    # 逐页处理数据
    total_pages = (len(qa_list) + 99) // 100
    for page in range(1, total_pages + 1):
        start_idx = (page - 1) * 100
        end_idx = min(start_idx + 100, len(qa_list))
        current_page_qa = qa_list[start_idx:end_idx]

        for index, qa in enumerate(current_page_qa, 1):
            try:

                should_filter_question = False
                should_filter_answer = False
                if filter_type_list:
                    # 构建过滤问题的提示
                    filter_question_message = f"""
                    请根据以下规则判断内容是否需要过滤：
                    {filter_type}
                    
                    问题内容: {qa['question']}
                    
                    如果内容需要过滤，请只返回"True"，否则返回"False"
                    """
                    
                    # 调用AI模型进行过滤判断
                    filter_question_ret = asyncio.run(get_ai_response(
                        corpid, filter_question_message, aigc_type_id, model_path, model_id
                    ))
                    
                    # 处理过滤结果,过滤所有中文
                    filter_question_ret = filter_chinese(filter_question_ret)
                    should_filter_question = filter_question_ret.strip().lower() == "true"
                    
                    # 如果需要过滤问题，则直接标记整条QA为过滤状态
                    if should_filter_question:
                        filtered += 1
                        ForageController(corpid).up_forage_qa(
                            {'qa_id': qa['qa_id']},
                            {'delete_flag': 1,'delete_filter_type':1}
                        )
                        continue  # 跳过后续处理
                    
                    # 对答案进行过滤判断
                    if not should_filter_question:
                        filter_answer_message = f"""
                        请根据以下规则判断内容是否需要过滤：
                        {filter_type}
                        
                        答案内容: {qa['answer']}
                        
                        如果内容需要过滤，请只返回"True"，否则返回"False"
                        """
                        
                        filter_answer_ret = asyncio.run(get_ai_response(
                            corpid, filter_answer_message, aigc_type_id, model_path, model_id
                        ))
                        
                        # 处理过滤结果,过滤所有中文
                        filter_answer_ret = filter_chinese(filter_answer_ret)
                        should_filter_answer = filter_answer_ret.strip().lower() == "true"
                        
                        if should_filter_answer:
                            filtered += 1
                            ForageController(corpid).up_forage_qa(
                                {'qa_id': qa['qa_id']},
                                {'delete_flag': 1,'delete_filter_type':1}
                            )
                            continue  # 跳过后续处理
                clear_log['filter_end_time'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                if clean_type_list: 
                    # 清洗问题
                    question_message = f"""
                    请根据{clean_type}的规则对以下内容进行清洗:
                    文本内容: {qa['question']}
                    - 只返回清洗后的内容,不要返回其他内容
                    """
                    question_ret = asyncio.run(get_ai_response(
                        corpid, question_message, aigc_type_id, model_path, model_id
                    ))

                    # 清洗答案
                    answer_message = f"""
                    请根据{clean_type}的规则对以下内容进行清洗:
                    文本内容: {qa['answer']}
                    - 只返回清洗后的内容,不要返回其他内容
                    """
                    answer_ret = asyncio.run(get_ai_response(
                        corpid, answer_message, aigc_type_id, model_path, model_id
                    ))
                
                # 更新清洗结果
                    if question_ret and answer_ret:
                        ForageController(corpid).up_forage_qa(
                            {'qa_id': qa['qa_id']},
                            {'question': question_ret, 'answer': answer_ret,'clear_question':qa['question'],'clear_answer':qa['answer']}
                        )
                                            # 如果进行了过滤判断，添加过滤结果
                        success += 1

                # 计算全局进度（已处理数量/总数量）
                processed = (page - 1) * 100 + index
                progress = min(100, int((processed / len(qa_list)) * 100))

                # 更新进度
                meta.update({
                    "current": progress,
                    "success": success,
                    "filtered": filtered,
                    "message": f"正在处理 {processed}/{len(qa_list)} (第{page}/{total_pages}页)"
                })
                task.update_state(state="PROGRESS", meta=meta)

            except Exception as e:
                # 记录单个项的错误，继续处理其他项
                task_logger.error(f"处理算料 {qa.get('qa_id', '未知')} 时出错: {e}")
                continue

    clear_log['clear_end_time'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    clear_log['filter_num'] = filtered
    clear_log['clear_num'] = len(qa_list)
    # 全部处理完成
    meta.update({
        "state": "SUCCESS",
        "current": 100,
        "message": f'清洗算料成功，共处理 {len(qa_list)} 条，清洗{success} 条，过滤 {filtered} 条'
    })
    end_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    state = meta.get('state')
    clear_log['end_time'] = end_time
    task_logger.info(f'clear_log: {clear_log}')
    ForageController(corpid).add_forage_cleaning_record(
        {
            "state": str(state),
            "tp_user_id": str(tp_user_id),
            "forage_id": str(forage_id),
            "clear_log": json.dumps(clear_log),
            'task_id': str(task_id)
        }
    )
    task.update_state(state="SUCCESS", meta=meta)
    return meta

# 辅助函数：获取AI响应
async def get_ai_response(corpid, message, aigc_type_id, model_path, model_id):
    task_logger.info(f'get_ai_response:【{corpid},{message},{aigc_type_id},{model_path},{model_id}】开始获取AI响应')
    response = ''
    try:
        async for item in AppController(corpid).ai_app_chat_async(
            message, [], aigc_type_id, model_path, model_id
        ):
            response += item
        return response
    except Exception as e:
        # 这里可以添加更多的错误处理逻辑
        raise RuntimeError(f"获取AI响应失败: {e}") from e


@celery.task(name='file_life_status_action')
def file_life_status_action():

    def check_name_chose(rule_map, name):
        check_ret = list()
        for rule_key, rule_contents in rule_map.items():
            if not rule_contents:
                continue
            # 包含
            if rule_key == 'in':
                if any(keyword in name for keyword in rule_contents):
                    check_ret.append(True)
                else:
                    check_ret.append(False)

            # 全不包含
            elif rule_key == 'not_in':
                if all(keyword not in name for keyword in rule_contents):
                    check_ret.append(True)
                else:
                    check_ret.append(False)

            # 等于
            elif rule_key == 'equal':
                if any(keyword == name for keyword in rule_contents):
                    check_ret.append(True)
                else:
                    check_ret.append(False)

            # 全不等于
            elif rule_key == 'unequal':
                if all(keyword != name for keyword in rule_contents):
                    check_ret.append(True)
                else:
                    check_ret.append(False)
        if not check_ret:
            return False
        else:
            if check_ret.count(False):
                return False
            else:
                return True

    rule_keys = ['in', 'not_in', 'equal', 'unequal']
    now_date = datetime.datetime.now()
    corp_list = MasterAction().get_all_corp()
    for corp in corp_list:
        corp_id = corp['corpid']

        k_controller = KnowledgeController(corp_id)
        k_orm = k_controller.model

        r_controller = RuleController(corp_id)
        rule_orm = r_controller.rule_model

        knowledge_objs = k_orm.get_life_status_knowledge_datas()

        for k_obj in knowledge_objs:

            kid = k_obj.knowledge_id

            life_rule_objs = rule_orm.get_all_life_rule(kid, now_date)

            if life_rule_objs:

                # 拆分两类规则方式
                for life_rule_obj in life_rule_objs:
                    life_rule_id = life_rule_obj.life_rule_id
                    life_type = life_rule_obj.life_type
                    file_scope = life_rule_obj.file_scope

                    if file_scope != 1:
                        scope_rule_objs = rule_orm.get_all_scope_rule(kid, [life_rule_id])
                        # 检测是否配置范围规则；未配置规则，直接跳过处理
                        if not scope_rule_objs:
                            continue

                    if life_type == 'days':
                        long_days = life_rule_obj.life_days
                        if long_days <= 0:
                            continue
                        expire_date = now_date - datetime.timedelta(days=long_days)
                        document_objs = k_orm.get_life_status_document(open_life=0, kid=kid, expire_date=expire_date)

                    elif life_type == 'date':
                        expire_date = life_rule_obj.expire_date
                        if not expire_date:
                            continue
                        document_objs = k_orm.get_life_status_document(open_life=0, kid=kid, expire_date=expire_date)
                    else:
                        continue

                    # 作用全部范围，优先级最高，跳过其他规则
                    if file_scope == 1:
                        chose_ids = [x.document_id for x in document_objs]

                    # 综合所有规则，统一过滤 （单规则内部范围规则是and；不同规则之间是or）
                    else:
                        # 判定符号映射
                        scope_rule_key_map = dict()
                        chose_ids = list()
                        for one in scope_rule_objs:
                            rule_key = one.rule_key
                            scope_rule_key_map.setdefault(life_rule_id, {rule_key: []}).setdefault(rule_key, []).append(one.rule_content)

                        for file_obj in document_objs:
                            name = file_obj.doc_name
                            doc_id = file_obj.document_id

                            for one in scope_rule_key_map.values():
                                is_chose = check_name_chose(one, name)
                                if is_chose:
                                    chose_ids.append(doc_id)

                    # 分片标记删除sql记录和es的文档信息
                    cut_ids = chose_ids[:50]
                    for_no = 0
                    while cut_ids:
                        if k_controller.bulk_del_es_knowledge(kid, {'doc_id': cut_ids}):
                            k_orm.bulk_del_open_life_statue_document(ids=cut_ids)
                        for_no += 1
                        cut_ids = chose_ids[for_no*50:(for_no+1)*50]

            # 未发现规则，则关闭生命周期
            else:
                k_obj.open_life = 0
                k_orm.session.commit()

        # 单独开放生命周期管理文档
        open_life_objs = k_orm.get_life_status_document(open_life=1, expire_date=now_date)
        data_group_map = dict()
        for obj in open_life_objs:
            kid = obj.knowledge_id
            data_group_map.setdefault(kid, []).append(obj.document_id)

        for kid, dic_ids in data_group_map.items():
            cut_ids = dic_ids[:50]
            for_no = 0
            while cut_ids:
                if k_controller.bulk_del_es_knowledge(kid, {'doc_id': cut_ids}):
                    k_orm.bulk_del_open_life_statue_document(ids=cut_ids)
                for_no += 1
                cut_ids = dic_ids[for_no * 50:(for_no + 1) * 50]

@celery.task(bind=True, name="apps.ai.task.handle_individual_knowledge")
def handle_individual_knowledge(task, corpid, knowledge_id):
    import threading
    import queue

    result_queue = queue.Queue()

    def run_in_thread():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(
                KnowledgeController(corpid).handle_analysis(corpid, knowledge_id, AppController(corpid).ai_app_chat)
            )
            result_queue.put(result)
        except Exception as e:
            result_queue.put(e)
        finally:
            loop.close()

    thread = threading.Thread(target=run_in_thread)
    thread.start()
    thread.join()

    if not result_queue.empty():
        result = result_queue.get()
        if isinstance(result, Exception):
            raise result
        return result


@celery.task(bind=True, name="apps.ai.task.build_knowledge_graph")
def build_knowledge_graph(task, corp_id, knowledge_id: int, tp_user_name:str):
    task_logger.info(f"build_knowledge_graph for knowledge id : {knowledge_id}")

    if OTHER_TYPE not in ENTITY_TYPES:
        ENTITY_TYPES.append(OTHER_TYPE)

    storage_namespace = SPACE_PREFIX + str(knowledge_id)

    try:
        knowledge_controller = KnowledgeController(corp_id)

        knowledge_controller.update_knowledge_graph_status(
            knowledge_id,
            graph_status=GraphStatus.RUNNING
        )

        knowledge_graph_data = {
            'id': get_snowflake_id(),
            'knowledge_id': knowledge_id,
            'status': GraphStatus.RUNNING.value,
            'count_total': 0,
            'count_done': 0,
            'tp_user_id': tp_user_name,
            'count_node': 0,
            'count_edge': 0,
            'labels': ",".join(ENTITY_TYPES)
        }
        record_id = knowledge_controller.add_knowledge_graph(knowledge_graph_data)

        def sync_buildup():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(
                    rag.chunk_entity_relation_graph.buildup(ENTITY_TYPES, [EDGE_TYPE])
                )
            except Exception as e:
                task_logger.error(f"Buildup failed: {e}")
                raise
            finally:
                loop.close()

        def sync_insert():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(rag.ainsert(knowledge_id))
            except Exception as e:
                task_logger.error(f"Insert failed: {e}")
                raise
            finally:
                loop.close()

        def sync_calculate():
            try:
                return rag.chunk_entity_relation_graph.calculate()
            except Exception as e:
                task_logger.error(f"Calculate failed: {e}")
                raise

        knowledge_controller.truncate_knowledge_graph_vdb(knowledge_id)
        rag = GraphRag(corp_id=corp_id, namespace=storage_namespace, project_logger=task_logger).get_instance()

        with ThreadPoolExecutor() as executor:
            buildup_future = executor.submit(sync_buildup)
            buildup_future.result()
            knowledge_controller.update_knowledge_graph_status(
                knowledge_id,
                graph_status=GraphStatus.BUILDUP
            )
            insert_future = executor.submit(sync_insert)
            report = insert_future.result()
            calculate_future = executor.submit(sync_calculate)
            calculate_val = calculate_future.result()

        knowledge_controller.update_knowledge_graph_status(
            knowledge_id,
            graph_status=GraphStatus.FINISHED
        )

        knowledge_controller.update_knowledge_graph(record_id,{
            "count_node":calculate_val['nodes'],
            "count_edge":calculate_val['edges'],
            "build_context":report
        })
        task_logger.info(
            f'build_knowledge_graph:【{corp_id}】finish build up space: {storage_namespace}'
        )

    except Exception as e:
        task_logger.error(
            f'build_knowledge_graph:【{corp_id}】fail build up space: {storage_namespace}, error: {str(e)}')
        knowledge_controller = KnowledgeController(corp_id)
        knowledge_controller.update_knowledge_graph_status(knowledge_id, graph_status=GraphStatus.ERROR)
        raise


def with_app_context(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        with current_app.app_context():
            return f(*args, **kwargs)
    return decorated_function


@with_app_context
@celery.task(bind=True, name="apps.ai.task.build_cluster")
def build_cluster(task, corp_id, mode=0):
    task_logger.info(f"build_cluster for mode : {mode}")
    # for auto task
    if corp_id is None:
        corp_id = TEMP_CORP_ID

    knowledge_controller = KnowledgeController(corp_id)
    knowledge_galaxy_data = {
        'id': get_snowflake_id(),
        'status': GalaxyStatus.RUNNING.value,
        'cluster_total': 0,
        'doc_total': 0,
        'tp_user_id': "admin",
    }
    record_id = knowledge_controller.add_knowledge_galaxy(knowledge_galaxy_data)

    try:
        cluster = ClusterMonitorLauncher(corp_id, mode)
        # updated_count, labels_count = cluster.launch(clean_up=False, cluster_only=True)
        # pd
        updated_count, labels_count = cluster.launch(clean_up=True, cluster_only=False)
        task_logger.info(
            f'build_cluster finished.'
        )

        status = {
            "status": GalaxyStatus.FINISHED.value,
            "cluster_total": labels_count,
            "doc_total": updated_count
        }
        knowledge_controller.update_knowledge_galaxy(
            record_id,
            status
        )

    except Exception as e:
        task_logger.error(
            f'build_cluster: failed, error: {str(e)}')

        status = {
            "status": GalaxyStatus.ERROR.value
        }
        knowledge_controller.update_knowledge_galaxy(
            record_id,
            status
        )
        raise



@celery.task(bind=True, name="apps.ai.task.truncate_knowledge_graph")
def truncate_knowledge_graph(task, corp_id, knowledge_id: int, tp_user_name:str):
    print(f"build_knowledge_graph for knowledge id : {knowledge_id}")
    storage_namespace = SPACE_PREFIX + str(knowledge_id)

    try:
        knowledge_controller = KnowledgeController(corp_id)

        def sync_truncate():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(
                    rag.chunk_entity_relation_graph.truncate_all()
                )
            except Exception as e:
                task_logger.error(f"Buildup failed: {e}")
                raise
            finally:
                loop.close()

        knowledge_controller.truncate_knowledge_graph_vdb(knowledge_id)
        rag = GraphRag(corp_id=corp_id, namespace=storage_namespace, project_logger=task_logger).get_instance()

        with ThreadPoolExecutor() as executor:
            truncate_future = executor.submit(sync_truncate)
            truncate_future.result()

        task_logger.info(
            f'truncate_knowledge_graph:【{corp_id}】finish build up space: {storage_namespace}'
        )

    except Exception as e:
        task_logger.error(
            f'truncate_knowledge_graph:【{corp_id}】fail build up space: {storage_namespace}, error: {str(e)}')
        raise


@celery.task(bind=True, name="apps.ai.task.evaluate_forage")
def evaluate_forage(task, corpid, forage_id, aigc_model_id,args):
    task_id = task.request.id
    tp_user_id = args.get('tp_user_id')
    # 根据forage_id获取forage_qa
    meta = {
        "state": "PENDING",
        "message": "",
        "current": 0,
        "total": 1,
        "success": 0,
    }
    # 添加评估记录
    record_id = ForageController(corpid).add_forage_evaluation_record({'forage_id': forage_id,'state': 'PENDING', 'task_id': task_id, 'aigc_model_id': aigc_model_id,'tp_user_id': tp_user_id})
    datas = ForageOrm(corpid).all_forage_qa(forage_id, None)
    if not datas:
        meta.update({
            "state": "FAILURE",
            "message": "没有算料"
        })
        ForageOrm(corpid).update_forage_evaluation_record({'record_id': record_id}, {'state': 'FAILURE'})
        task.update_state(state="FAILURE", meta=meta)
        return meta
        
    # 构建完整的qa文本
    full_qa = ""
    for one in datas:
        full_qa += f"\n{one['question']}\n{one['answer']}\n"
    
    # 按30000字符分割文本
    chunks = []
    for i in range(0, len(full_qa), 30000):
        chunks.append(full_qa[i:i+30000])
    
    # 初始化评估结果总和
    total_scores = {
        "多样性": 0,
        "全面性": 0,
        "忠实度": 0,
        "重复性": 0,
        "通顺性": 0,
        "相关性": 0,
        "一致性": 0,
        "准确性": 0,
        "噪声含量": 0
    }
    
    # 获取模型信息
    try:
        aigc_model_info = AppController(corpid).get_model_info(aigc_model_id)
        aigc_type_id, model_path, model_id = aigc_model_info.get('aigc_type_id'), aigc_model_info.get('model_path'), aigc_model_info.get('aigc_model_id')
    except Exception as e:
        logging.error(f'获取AIGC模型信息失败: {e}')
        meta.update({
            "state": "FAILURE",
            "message": "获取AIGC模型信息失败"
        })
        ForageOrm(corpid).update_forage_evaluation_record({'record_id': record_id}, {'state': 'FAILURE'})
        task.update_state(state="FAILURE", meta=meta)
        return meta
    
    meta['current']=30
    task.update_state(state="PENDING", meta=meta)
    # 对每个文本块进行评估
    for i, chunk in enumerate(chunks):
        message = f"""
        请根据以下算料文本进行评估，评估内容包括：
        1.多样性
        2.全面性
        3.忠实度
        4.重复性
        5.通顺性
        6.相关性
        7.一致性
        8.准确性
        9.噪声含量
        每一项评估标准以整数返回，范围为0-10，0表示最差，10表示最好
        文本: {chunk}
        - 严格按照如下模版返回结果数据,不要返回其他内容:
        {{
            "多样性": 0,
            "全面性": 0,
            "忠实度": 0,
            "重复性": 0,
            "通顺性": 0,
            "相关性": 0,
            "一致性": 0,
            "准确性": 0,
            "噪声含量": 0
        }}
        """
        
        ret = ''
        try:
            response = AppController(corpid).ai_app_chat(message, [], aigc_type_id, model_path, model_id=model_id)
            for item,_ in response:
                ret += item     
        except Exception as e:
            logging.error(f"大模型评估算料第{i+1}/{len(chunks)}块时出错: {e}")
            continue
        
        if not ret:
            logging.warning(f"第{i+1}/{len(chunks)}块评估结果为空")
            continue
        
        # 清理并解析JSON
        ret = ret.replace('\n', '').replace(' ', '').replace('\'', '\"')
        match = re.search(r'\{.*\}', ret)
        if not match:
            logging.error(f"第{i+1}/{len(chunks)}块未找到有效的JSON对象: {ret}")
            continue
        json_str = match.group(0)
        try:
            chunk_scores = json.loads(json_str)
            # 累加各项分数
            for key in total_scores:
                total_scores[key] += chunk_scores.get(key, 0)
            logging.info(f"第{i+1}/{len(chunks)}块评估成功: {chunk_scores}")
        except json.JSONDecodeError as e:
            logging.error(f"第{i+1}/{len(chunks)}块JSON解析错误: {e}，待解析的字符串: {json_str}")
    

    meta['current']=60
    task.update_state(state="PENDING", meta=meta)
    # 计算平均分
    if chunks:  # 确保有分块
        avg_scores = {key: round(value / len(chunks), 1) for key, value in total_scores.items()}
        logging.info(f"最终平均评估结果: {avg_scores}")
        
        # 保存到数据库
        try:
            ForageOrm(corpid).update_forage_evaluation_record({'record_id': record_id}, {'evaluation_results': json.dumps(avg_scores), 'state': 'SUCCESS'})
        except Exception as e:
            logging.error(f"保存评估结果失败: {e}")
            ForageOrm(corpid).update_forage_evaluation_record({'record_id': record_id}, {'state': 'FAILURE'})

    meta.update({
        "state": "SUCCESS",
        "message": "评估成功",
        "current": 100,
        "total": 1,
        "success": 1,
        'evaluation': avg_scores
    })
    task.update_state(state="SUCCESS", meta=meta)
    return meta

@celery.task(bind=True, name="apps.ai.task.async_del_content_vector")
def async_del_content_vector(task):
    """
    逻辑：
    - 查询content_info/content_info_version表，获取content_id,以及对应的add_time
    - 根据content_id,add_time 删除向量化数据
    """
    corp_list = MasterAction().get_all_corp()
    for corp in corp_list:
        
        # 获取符合delete_flag=2的content_id,在content_info_version中，获取本体的add_time
        content = ContentInfoController(corp['corpid'])
        controller = KnowledgeController(corp['corpid'])
        ret = content.get_content_ids_by_delete_flag()
        task_logger.info('async_del_content_vector:【%s】获取需要删除的content_id： \n %s' % (corp['corpid'], ret))
        content_ids = []
        for key,val in ret.items():
            controller.del_one_es_content(key, val)
            content_ids.append(key)
        task_logger.info('async_del_content_vector:【%s】删除向量成功' % corp['corpid'])
        content.update_content_info_delete_flag(content_ids)
        task_logger.info('async_del_content_vector:【%s】delete_flag 更新为 2——>1' % content_ids)

if __name__ == '__main__':
    # timing_update_content_info_aa()
    # timing_update_question_point_plan_embeddings()
    # timing_update_content_embeddings()
    # chat_call_generate_async(corpid=corpid, task_id=task_id, **kwargs)
    # c = QuestionController('wwf556ee1bcfa5d9d6')
    # timing_update_doc_chunk_data()
    # task_logger.info(c.search_es_question('问题', 10, 0))
    # task_logger.info(c.search_es_question_last())
    # message = "请根据以下内容进行摘要总结，%s。 要求仅返回摘要内容，不返回其他内容。" % "智邦国际\n数智一体化先行者\n代用名\nDai Yongming\n北京市明日辉煌灿烂有限公司\n智邦国际·生态伙伴\n13701234567\n数智一体化先行者\n北京智邦国际软件技术有限公司\nBEIJING ZHIBANGINTERNATIONALSOFTWARETECHNOLOGY.,LTD\n北京海淀区下一代互联网及重大应用技术创新园\nC2座19-20层\n400热线：400-650-8060（免长途费）\n公司总机：010-62486258\n服务监督：010-62486286\n公司官网"
    # abstract = AppController('wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ').chat_qianwen(message, [], QIANWEN_CODE, 'qwen-turbo',
    #                                                                           is_stream=False)
    # for item in abstract:
    #     if item.decode():
    #         task_logger.info(json.loads(item.decode())['result'])
    # a = '请根据以下内容进行摘要总结'
    # task_logger.info(a[:-1])

    # timing_update_content_images()
    # knowledge_id = "4961505637415522305"
    # corp_id = "wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ"
    # knowledge_controller = KnowledgeController(corp_id)
    # knowledge_controller.update_knowledge_graph_status(knowledge_id, graph_status=GraphStatus.FINISHED)

    # knowledge_controller.truncate_knowledge_graph_vdb(4957229924755705857)

    # list1 = knowledge_controller.get_knowledge_graph_detail(123)
    # task_logger.info(list1)

    # knowledge_graph_data = {
    #     'id': get_snowflake_id(),
    #     'knowledge_id': 12345,
    #     'status': 0,
    #     'count_total': 10,
    #     'count_done': 5,
    #     'tp_user_id': 'user123',
    #     'count_node': 50,
    #     'count_edge': 20,
    #     'labels': 'label1,label2'
    # }
    # new_id = knowledge_controller.add_knowledge_graph(knowledge_graph_data)
    # task_logger.info(f"New record added with ID: {new_id}")
    pass


