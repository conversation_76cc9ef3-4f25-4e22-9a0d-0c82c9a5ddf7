# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: chat_record.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 2月 18, 2025
# ---
from flask import g, request
from flask_restful import Resource, reqparse

from controller.app_controller import A<PERSON><PERSON><PERSON>roller
from horticulture.auth_token import login_check
from horticulture.validate import json_response
from controller.chatbi_controller import ChatbiController


class ChatbiSessionRecordView(Resource):

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('session_id', type=str, help='会话id', required=True)
        parser.add_argument('app_id', type=str, help='app_id', required=False)
        parser.add_argument('record_info', type=list, location='json', help='报告记录', required=True)

        args = parser.parse_args()

        controller = ChatbiController(g.corpid)
        session_id = args['session_id']
        record_info = args['record_info']
        data = {
            'session_id': session_id,
            'record_info': record_info
        }
        controller.update_chatbi_session_record(session_id, data)

        message = ""
        if len(record_info) == 3:
            message = record_info[1].get('content', '')

        tp_user_id = request.user['tp_user_id']
        app_controller = AppController()
        session_info = app_controller.get_app_session_detail(session_id, args['app_id'])

        if session_info is None:
            session_name = message[0:20] if message else "New Session"
            app_controller.create_app_session_with_id(session_id, args['app_id'], tp_user_id, session_name, [])

        return json_response(data={'session_id': session_id})

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('session_id', type=str, help='会话id', location='args', required=True)

        args = parser.parse_args()

        data = ChatbiController(g.corpid).get_chatbi_session_record(args['session_id'])
        if data:
            return json_response(data={'data_list': data['record_info']})
        else:
            return json_response(data={'data_list': []})
