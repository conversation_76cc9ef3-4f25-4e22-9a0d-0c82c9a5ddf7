from celery import Celery

from horticulture.auth_token import login_check
from flask_restful import Resource, reqparse
from horticulture.validate import json_response
from celery_app import BROKER_URL, celery

app = celery

class QueueNumView(Resource):
    @staticmethod
    @login_check
    def get():
        result = get_queue_length()
        return json_response(data=result)



def get_queue_length():
    inspector = app.control.inspect()
    reserved = inspector.reserved() or []
    scheduled = inspector.scheduled() or []
    active = inspector.active() or []


    queue_length = 0

        # 计算所有任务的数量
    for tasks in [reserved, scheduled, active]:
        for worker, task_list in tasks.items():
            for task in task_list:
                queue_length += 1

    return queue_length




