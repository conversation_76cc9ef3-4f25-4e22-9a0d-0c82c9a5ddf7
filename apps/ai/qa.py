#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/4/8 11:07
# <AUTHOR> yanning
# @File    : app.py
# @Comment :

from utils.tools import get_formatted_content
from werkzeug.datastructures import FileStorage
from flask import request, g
from flask_restful import Resource, reqparse
from horticulture.validate import json_response
from horticulture.auth_token import login_check
from controller.qa_controller import QaController, QaUploadController, QaItemsController
from apps.ai import task, task_forage
from controller.demo.demo_chat_free_contoller import ChatFreeController
from controller.forage_controller import ForageController
from celery_app import celery
from flask import send_file

class QaView(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('page_size', type=str, required=False, default='10', location="args", help="分页大小不能为空")
        parser.add_argument('page_no', type=str, required=False, default="1", location="args", help="页码不能为空")
        parser.add_argument('qa_name', type=str, location="args")
        parser.add_argument('qa_lib_id', type=int, location="args")
        controller = QaController()
        if parser.parse_args().get('qa_lib_id'):
            result = controller.get_lib_detail(parser.parse_args())
            return json_response(data=result)
        result = controller.get_lib_list(parser.parse_args())
        return json_response(data=result)
    
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('qa_name', type=str, required=True, help="库名称不能为空")
        parser.add_argument('qa_desc', type=str, required=True, help="库描述不能为空")
        parser.add_argument('aigc_model_id', required=True, help="模型ID不能为空")
        parser.add_argument('icon_url', required=False)
        parser.add_argument('qa_lib_id', required=False, help="库ID")
        tp_user_id = request.user['tp_user_id']
        args = {**parser.parse_args(), 'tp_user_id': tp_user_id}
        controller = QaController()
        qa_lib_id = ""
        if args.get('qa_lib_id'):
            qa_lib_id = controller.modify_lib(args)
        else:
            filter_args = {key: value for key, value in args.items() if key != 'qa_lib_id'}
            qa_lib_id = controller.add_lib(filter_args)
        return json_response(data={'qa_lib_id': qa_lib_id})
    
    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('qa_lib_id', type=str, required=True, help="库ID不能为空")
        parser.add_argument('status', type=int, required=True, help="状态不能为空")
        controller = QaController()
        result = controller.update_status(parser.parse_args())
        return json_response(data=result)
    
    @staticmethod
    @login_check
    def delete():
        parser = reqparse.RequestParser()
        parser.add_argument('qa_lib_id', type=str, required=True, help="库ID不能为空")
        controller = QaController()
        result = controller.delete_lib(parser.parse_args()['qa_lib_id'])
        return json_response(data=result)
    

class QaUpload(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('qa_lib_id', type=str, required=True, help="库ID不能为空")
        parser.add_argument('oss_url', type=str, required=True, help="文件路径不能为空", location="json")
        parser.add_argument('qa_modal', type=str, required=False, help="", location="json", default=0)
        creator = request.user['tp_user_id']
        args = {**parser.parse_args(), 'creator': creator}
        controller = QaUploadController()
        result = controller.upload(args, task)
        return json_response(data=result)
    
    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, help="任务ID不能为空")
        args = parser.parse_args()
        controller = QaUploadController()
        result = controller.get_progress(args)
        return json_response(data=result)
    

class QaLibItems(Resource):
    @staticmethod
    @login_check
    def get(id = None):
        if id:
            pass
        else:
          return QaLibItems.get_items()
    @staticmethod
    def get_item(id):
        parser = reqparse.RequestParser()
        parser.add_argument('qa_lib_id', type=str, required=True, location='args')
        controller = QaItemsController()
        args = parser.parse_args()
        result = controller.get_qa_detail({'qa_lib_id': args['qa_lib_id'], 'record_id': id})
        return json_response(data=result)
        
    @staticmethod    
    def get_items():
        parser = reqparse.RequestParser()
        parser.add_argument('page_size', type=int, default=10, required=False, location='args')
        parser.add_argument('page_no', type=int, default=1, required=False, location='args')
        parser.add_argument('content', type=str, default=1, required=False, location='args')
        parser.add_argument('qa_lib_id', type=str, required=True, location='args')
        
        controller = QaItemsController()
        result = controller.get_qa_list(parser.parse_args())
        return json_response(data=result)

    @staticmethod
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('qa_lib_id', type=str, required=True, location='json')
        parser.add_argument('answer', type=str, required=True, location='json')
        parser.add_argument('question', type=str, required=True, location='json')
        parser.add_argument('qa_modal', type=str, required=False, help="", location="json", default=0)
        args = parser.parse_args()
        controller = QaItemsController()
        controller.add_qa(args)
        return json_response(data='success')

    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('qa_lib_id', type=str, required=True, location='json')
        parser.add_argument('id', type=str, required=True, location='json')
        parser.add_argument('answer', type=str, required=True, location='json')
        parser.add_argument('question', type=str, required=True, location='json')
        parser.add_argument('qa_modal', type=str, required=False, help="", location="json", default=0)
        creator = request.user['tp_user_id']
        args = parser.parse_args()
        controller = QaItemsController()
        controller.update_qa_by_id({**args, creator: creator})
        return json_response(data='success')
    
    @staticmethod
    @login_check
    def delete():
        parser = reqparse.RequestParser()
        parser.add_argument('qa_lib_id', type=str, required=True, location='json')
        parser.add_argument('id', type=str, required=True, location='json')
        args = parser.parse_args()
        controller = QaItemsController()
        controller.delete_qa_by_id(args)
        return json_response(data='success')


# 算料库管理
class ForageView(Resource):
    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('forage_id', type=str, required=False)
        parser.add_argument('name', type=str, required=True)
        parser.add_argument('icon', type=str, required=False)
        parser.add_argument('desc', type=str, required=False)
        parser.add_argument('auth_scheme_id', type=str, required=False, default=None)
        parser.add_argument('aigc_model_id', type=str, required=False, default=None)
        parser.add_argument('is_structured', type=str, default='0', required=False) # 默认为非结构化数据，1为结构化数据
        parser.add_argument('status', type=str, default='1', required=False) # 默认为非结构化数据，1为结构化数据
        args = parser.parse_args()
        ret = ForageController().add_or_up_forage(dict(args))
        return json_response(data=ret)

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('forage_id', type=str, required=True)
        args = parser.parse_args()
        ret = ForageController().forage_info(args['forage_id'])
        return json_response(ret)

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('page_size', type=int, default=10, required=False, location='args')
        parser.add_argument('page_no', type=int, default=1, required=False, location='args')
        parser.add_argument('name', type=str, required=False, location='args')
        parser.add_argument('status', type=str, required=False, location='args')
        parser.add_argument('is_structured', type=str, default='0', required=False, location='args') # 默认为非结构化数据，1为结构化数据
        args = parser.parse_args()
        ret = ForageController().page_forage(None, args['name'], args['status'], args['is_structured'], args['page_size'], args['page_no'])
        return json_response(ret)

    @staticmethod
    @login_check
    def delete():
        parser = reqparse.RequestParser()
        parser.add_argument('forage_id', type=str, required=True)
        args = parser.parse_args()
        ret = ForageController().del_forage(args['forage_id'])
        return json_response(ret)


class ForageStructuredFileUpload(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('forage_id', type=str, required=True, location='json')
        args = parser.parse_args()
        action = ForageController()
        excel_url = action.export_excel_file(args['forage_id'])
        return json_response(data=excel_url)
    
    @staticmethod
    @login_check
    def put():
        """
        直接上传、根据样本生成、根据提示词生成
        样本生成： 直接上传 + 数据合成
        """
        parser = reqparse.RequestParser()
        parser.add_argument('forage_id', type=str, required=True, location='json')
        parser.add_argument('file_list', type=list, required=False, location='json')

        args = parser.parse_args()
        action = ForageController()
        user_info = request.user
        args['tp_user_id'] = user_info['tp_user_id']
        args['corpid'] = g.corpid
        args['tp_user_name'] = user_info['tp_user_name']

        res = action.add_excel_file(args['forage_id'], args['file_list'], args['tp_user_id'], args['corpid'], args['tp_user_name'])
        return json_response(data=res)
    
    
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('structured_data_id', type=str, required=False, location='args')
        parser.add_argument('forage_id', type=str, required=False, location='args')
        parser.add_argument('page_size', type=int, default=10, required=False, location='args')
        parser.add_argument('page_no', type=int, default=1, required=False, location='args')
        args = parser.parse_args()
        action = ForageController()
        ret = action.get_mongo_structured_forage(args['structured_data_id'], args['forage_id'], args['page_size'], args['page_no'])
        return json_response(ret)
    
    
    @staticmethod
    @login_check
    def delete():
        parser = reqparse.RequestParser()
        parser.add_argument('structured_data_id', type=list, required=False, location='json')
        parser.add_argument('forage_id', type=str, required=False, location='json')
        args = parser.parse_args()
        action = ForageController()
        action.del_mongo_structured_data(args['structured_data_id'], args['forage_id'])
        return json_response(data='success')

class ForageStructuredTaskView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('forage_id', type=str, required=True)
        parser.add_argument('num_records', type=str, required=False)
        # 数据合成传参
        parser.add_argument('aigc_model_id', type=str, required=False, default=None)
        parser.add_argument('sample_ratio', type=str, required=False, default=None)
        parser.add_argument('is_distinct', type=str, required=False, help='是否去重 0 不去重 1 去重')
        parser.add_argument('primary_key', type=list, required=False, location='json')
        # 提示词生成数据 传参
        parser.add_argument('prompt', type=str, required=False, location='json')
        args = parser.parse_args()
        forage_info = ForageController().get_model_forage_info(args['forage_id'])

        if forage_info['structured_gen_state'] == 1:
            raise Exception('任务正在运行中，请稍后再试')
        
        if args['prompt']:
            # 根据提示词生成数据
            
            args['aigc_model_id'] = forage_info['aigc_model_id']
        else:
            if args['is_distinct'] == '1' and not args['primary_key']:
                raise Exception('primary_key is required when is_distinct is 1')
        user_info = request.user
        task_id = task_forage.generate_structured_data.delay(g.corpid, args['forage_id'], args['aigc_model_id'],
                                                             args['sample_ratio'], args['num_records'],
                                                             args['is_distinct'], args['primary_key'],
                                                             args['prompt'], user_info['tp_user_id'], user_info['tp_user_name']).id
        return json_response(data={'task_id': task_id})

    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='json')
        args = parser.parse_args()
        res = celery.AsyncResult(args['task_id'])
        task_result = res.info if res.info else {'state': 'PENDING', 'current': 0}
        return json_response(data=task_result)
    
    @staticmethod
    @login_check
    def patch():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='json')
        parser.add_argument('terminate', type=bool, required=False, default=True, location='json')
        parser.add_argument('forage_id', type=str, required=True, location='json')
        args = parser.parse_args()
        task_id = args['task_id']
        terminate = args['terminate']

        try:
            # Revoke the task
            celery.control.revoke(task_id, terminate=terminate, signal='SIGKILL')
            res = celery.AsyncResult(task_id)
            ForageController().update_model_forage_info(args['forage_id'], {'structured_gen_state': 3, 'structured_gen_task_id': ''})
            return json_response(data={'message': f'Task {task_id} has been revoked.', 'status': res.state})
        except Exception as e:
            return json_response(data={'message': f'Failed to revoke task {task_id}: {str(e)}', 'status': 'FAILURE'}, code=500)

# 算料文件管理
class ForageFileView(Resource):
    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('forage_id', type=str, required=True)
        parser.add_argument('files', type=list, required=True, location='json')
        parser.add_argument('slice_model', type=int, required=True, default=0)
        parser.add_argument('model_id', type=str, required=True)
        parser.add_argument('upload_type', type=int, required=False, default=0)
        args = parser.parse_args()
        upload_type = args.get('upload_type', 0)
        if upload_type == 0 or upload_type == 1:
            ret = ForageController().add_files(args['forage_id'], args['files'],upload_type)
            task_id = task_forage.forage_document_splitter.delay(g.corpid, args['forage_id'], ret, args['slice_model'], args['model_id'], args).id
            return json_response(data=task_id)
        else:
            ret = ForageController().add_files(args['forage_id'], args['files'],upload_type)
            return json_response(data=ret)

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('file_id', type=str, required=True)
        args = parser.parse_args()
        ret = ForageController().forage_file_detail(args['file_id'])
        return json_response(ret)

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('forage_id', type=str, required=False, location='args')
        parser.add_argument('name', type=str, required=False, location='args')
        parser.add_argument('status', type=str, required=False, location='args')
        parser.add_argument('stage', type=str, required=False, location='args')
        parser.add_argument('page_size', type=int, default=10, required=False, location='args')
        parser.add_argument('page_no', type=int, default=1, required=False, location='args')
        parser.add_argument('upload_type', type=int, required=False, default=0, location='args')
        args = parser.parse_args()
        ret = ForageController().page_forage_file(**args)
        return json_response(ret)

    @staticmethod
    @login_check
    def delete():
        parser = reqparse.RequestParser()
        parser.add_argument('file_ids', type=list, required=True, location='json')
        parser.add_argument('sync_del_qa', type=bool, required=True, default=False)
        args = parser.parse_args()
        ret = ForageController().del_file(args['file_ids'], args['sync_del_qa'])
        return json_response(ret)


# 算料QA管理
class ForageQAView(Resource):
    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('forage_id', type=str, required=True)
        parser.add_argument('kvs', type=list, required=False, location='json')
        parser.add_argument('file_id', type=str, required=False)
        parser.add_argument('file_url', type=str, required=False)
        parser.add_argument('qa_type', type=int, required=False, default=0, location='json')
        args = parser.parse_args()
        ret = ForageController().add_forage_qa(args['forage_id'], args['file_id'], args['kvs'], args['file_url'], args)
        return json_response(data=ret)

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('qa_id', type=str, required=True)
        parser.add_argument('answer', type=str, required=True)
        parser.add_argument('question', type=str, required=True)
        args = parser.parse_args()
        ret = ForageController().up_forage_qa({'qa_id': args['qa_id']}, dict(args))
        return json_response(ret)

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('forage_id', type=str, required=False, location='args')
        parser.add_argument('file_id', type=str, required=False, location='args')
        parser.add_argument('question', type=str, required=False, location='args')
        parser.add_argument('source', type=str, required=False, location='args')
        parser.add_argument('page_size', type=int, default=10, required=False, location='args')
        parser.add_argument('page_no', type=int, default=1, required=False, location='args')
        parser.add_argument('qa_type', type=int, required=False, default=0, location='args')
        args = parser.parse_args()
        args['ps'] = args.pop('page_size')
        args['po'] = args.pop('page_no')
        file_id = args.pop('file_id')
        args['file_ids'] = [file_id] if file_id else None
        ret = ForageController().page_forage_qa(**args)
        return json_response(ret)

    @staticmethod
    @login_check
    def delete():
        parser = reqparse.RequestParser()
        parser.add_argument('qa_ids', type=list, required=True, location='json')
        args = parser.parse_args()
        ret = ForageController().del_forage_qa(args['qa_ids'])
        return json_response(ret)


# 文件分片处理
class ForageFileSplitter(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='args')
        args = parser.parse_args()
        res = celery.AsyncResult(args['task_id'])
        task_result = res.info if res.info else {'status': 'PENDING', 'current': 0}
        return json_response(data=task_result)


# 算料QA提取处理
class ForageQAExtract(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='args')
        args = parser.parse_args()
        res = celery.AsyncResult(args['task_id'])
        task_result = res.info if res.info else {'status': 'PENDING', 'current': 0}
        return json_response(data=task_result)

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('forage_id', type=str, required=True)
        parser.add_argument('file_id', type=str, required=True)
        parser.add_argument('slice_model', type=int, required=True, default=0)
        parser.add_argument('model_id', type=str, required=True)
        args = parser.parse_args()
        task_id = task_forage.forage_document_splitter.delay(g.corpid, args['forage_id'], [args['file_id']], args['slice_model'], args['model_id'], args).id
        return json_response(data=task_id)


# 算料库下载
class ForageQADown(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('forage_id', type=str, required=True, location='args')
        parser.add_argument('file_id', type=str, required=False, location='args')
        parser.add_argument('file_type', type=str, required=True, location='args')
        args = parser.parse_args()
        ret = ForageController().build_down_qa_file(**args)
        return json_response(data=ret)


# 算料评估
class ForageEvaluateView(Resource):

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('forage_id', type=str, required=False, location='args')
        parser.add_argument('page_size', type=int, required=False, default=10, location='args')
        parser.add_argument('page_no', type=int, required=False, default=1, location='args')
        args = parser.parse_args()
        ret = ForageController().get_forage_evaluation_record(args)
        return json_response(data=ret)


    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('forage_id', type=str, required=True, location='json')
        parser.add_argument('aigc_model_id', type=str, required=False, location='json')
        parser.add_argument('re_evaluate', type=int, required=False, default=0, location='json')
        args = parser.parse_args()
        tp_user_id = request.user['tp_user_id']
        args['tp_user_id'] = tp_user_id
        re_evaluate = args.get('re_evaluate', 0)
        ret = ForageController().get_forage_evaluation_record(args,re_evaluate)
        if args['re_evaluate'] == 1:
            task_id = task.evaluate_forage.delay(g.corpid, args['forage_id'], args['aigc_model_id'],args).id
        elif args['re_evaluate'] == 0:                                           
            if ret:
                return json_response(data=ret)
            else:
                task_id = task.evaluate_forage.delay(g.corpid, args['forage_id'], args['aigc_model_id'],args).id
        return json_response(data={'task_id': task_id})
    
    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='json')
        args = parser.parse_args()
        res = celery.AsyncResult(args['task_id'])
        task_result = res.info if res.info else {'state': 'PENDING', 'current': 0}
        return json_response(data=task_result)
    
# 算料清洗
class ForageCleanView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('forage_id', type=str, required=True, location='json')
        parser.add_argument('qa_ids', type=list, required=False, location='json')
        parser.add_argument('clean_type', type=list, required=True, location='json')
        parser.add_argument('aigc_model_id', type=str, required=True, location='json')
        parser.add_argument('filter_type', type=list, required=True, location='json')
        parser.add_argument('deduplication_type', type=list, required=True, location='json')
        parser.add_argument('model_path', type=str, required=True, location='json')
        args = parser.parse_args()
        tp_user_id = request.user['tp_user_id']
        args['tp_user_id'] = tp_user_id
        res = task.forage_document_cleaner.delay(g.corpid,args)
        task_id = res.id
        return json_response(data={'task_id': task_id})
    
    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='json')
        args = parser.parse_args()
        res = celery.AsyncResult(args['task_id'])
        task_result = res.info if res.info else {'state': 'PENDING', 'current': 0}
        return json_response(data=task_result)
    
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('forage_id', type=str, required=False, location='args')
        parser.add_argument('page_size', type=int, required=False, default=10, location='args')
        parser.add_argument('page_no', type=int, required=False, default=1, location='args')
        parser.add_argument('record_id', type=str, required=False, location='args')
        parser.add_argument('result_type', type=str, required=False, location='args',help='evaluation,filter,clean')
        args = parser.parse_args()
        record_id = args.get('record_id')
        if record_id:
            ret = ForageController().get_forage_clean_result_detail(args)
        else:
            ret = ForageController().get_forage_clean_result(args)
        return json_response(data=ret)


class ForageCombineView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('forage_id', type=str, required=True, location='json')
        parser.add_argument('aigc_model_id', type=str, required=True, location='json')
        parser.add_argument('combine_num', type=int, required=True, location='json',default=10)
        parser.add_argument('random_percent', type=int, required=True, location='json',default=1)
        parser.add_argument('check_duplict', type=int, required=False, location='json',default=1)
        parser.add_argument('qa_ids', type=list, required=False, location='json',default=[])
        args = parser.parse_args()
        tp_user_id = request.user['tp_user_id']
        args['tp_user_id'] = tp_user_id
        data = task.forage_document_combiner.delay(g.corpid,args)
        task_id = data.id
        return json_response(data={'task_id': task_id})
    
    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('task_id', type=str, required=True, location='json')
        args = parser.parse_args()
        res = celery.AsyncResult(args['task_id'])
        task_result = res.info if res.info else {'state': 'PENDING', 'current': 0}
        return json_response(data=task_result)

    
    