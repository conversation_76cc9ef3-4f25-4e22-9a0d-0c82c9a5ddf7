from flask_restful import Resource, reqparse
from controller.es_opt_controller import <PERSON>sOptController
from horticulture.auth_token import login_check
from horticulture.validate import json_response


class RecommendKnowledge(Resource):
    """
    当知识选择为空时，根据问题中的核心词，推荐知识库和应用列表
    """
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('keywords', type=str, help='会话核心词', location='args', required=True)
        parser.add_argument('scope', type=int, help='知识库类型 0公开 1个人', location='args', required=False)
        parser.add_argument('knowledge_ids', type=list, help='查询的知识库id范围', location='args', required=False)
        args = parser.parse_args()
        es_controller = EsOptController()

        try:
            word = args['keywords'].split('。要求：')[0]
            scope = args.get('scope') or 0
            knowledge_ids = args.get('knowledge_ids')
            app_data = es_controller.recommend_app_list(word, scope, knowledge_ids)
            kn_data = es_controller.recommend_knowledge_list(word, scope, knowledge_ids)
            return json_response(data={'app': app_data, 'knowledge': kn_data})
        except Exception as e:
            return json_response(code='FAIL', message=str(e))
