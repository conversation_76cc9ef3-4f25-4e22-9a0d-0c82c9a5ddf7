#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/4/8 11:07
# <AUTHOR> zhangda
# @File    : app.py
# @Comment :
import asyncio
import os
import random
import copy
import tempfile

from enum import Enum
from typing import Any

import pandas as pd
import time, uuid, json

from apps.ai import task
from apps.ai.analyzer.config import ENABLE_SCOPE_ENHANCE
from apps.ai.analyzer.ensemble.config import ENABLE_ENSEMBLE
from apps.ai.analyzer.ensemble.ensemble_launcher import EnsembleLauncher
from apps.ai.analyzer.ensemble.input.generator import InputGenerator
from apps.ai.analyzer.ensemble.input.semantic_analyzer import SemanticAnalyzer
from apps.utils.ques_enhance import QuesEnhancement
from controller.chat_controller import ChatController
from utils.tools import get_formatted_content
import settings
from settings import IS_910B_EMBEDDINGS, DELETE_FLAG_TRUE
from flask import send_file
from settings import BASE_DIR
from flask import request, make_response, Response, stream_with_context, g
from flask_restful import Resource, reqparse
from puppet.graph_server_sdk import GraphServerSdk
from controller.knowledge_controller import KnowledgeController
from controller.content_info_controller import ContentInfoController
from horticulture.validate import json_response, CJsonEncoder
from horticulture.auth_token import login_check
from controller.app_controller import AppController
from controller.report_controller import ReportController
from controller.sample_controller import SampleController
from controller.user_controller import SchemeController, TPUserController
from lib_func.const_map import KNOWLEDGE_ROLE_CODES, CHATGPT_CODE, QIANWEN_CODE
from lib_func.logger import logger

from controller.demo.demo_chat_free_contoller import ChatFreeController
from controller.es_opt_controller import EsOptController, SaveAppParams


class AiApp(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('app_name', type=str, default='', required=False, location='args')
        parser.add_argument('app_desc', type=str, default='', required=False, location='args')
        parser.add_argument('page_size', type=str, required=False, location='args', help='每页数量')
        parser.add_argument('page_no', type=str, required=False, location='args', help='页码')
        parser.add_argument('scope', type=str, default="", required=False, location='args')
        parser.add_argument('parent_directory_id', type=str, default="", required=False, location='args')
        parser.add_argument('knowledge_id', type=str, default="", required=False, location='args')
        parser.add_argument('scope_id', type=str, default="", required=False, location='args')
        scope = parser.parse_args().get('scope')
        scope_id = parser.parse_args().get('scope_id')
        fix_flag = True if set(KNOWLEDGE_ROLE_CODES) & set(request.user['auth_ids']) else False
        if scope and scope.isdigit():
            scope = int(scope)
        args = parser.parse_args()
        controller = AppController()
        data = controller.get_ai_app_v1(args['app_name'], args['app_desc'], args['knowledge_id'], args['page_size'], args['page_no'], scope, scope_id)
        if args.get('parent_directory_id'):
            data = controller.get_app_list_by_directory_id(args)
        result = {"code": 0, "timestamp": int(time.time()), "message": '成功', "data": data,
                  "req_id": str(uuid.uuid4().hex), "fix_flag": fix_flag}
        response = make_response(json.dumps(result, ensure_ascii=False, cls=CJsonEncoder), 200)
        response.content_type = 'application/json; charset=utf-8'
        return response

class AiAppView(Resource):

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('app_name', type=str, default='', required=False, location='args')
        parser.add_argument('app_desc', type=str, default='', required=False, location='args')
        parser.add_argument('app_id', type=str, default='', required=False, location='args')
        args = parser.parse_args()
        app_name = args.get('app_name')
        app_desc = args.get('app_desc')
        app_id = args.get('app_id', '')
        fix_flag = True if set(KNOWLEDGE_ROLE_CODES) & set(request.user['auth_ids']) else False
        controller = AppController()
        data = controller.get_ai_app(app_name, app_desc, app_id)
        result = {"code": 0, "timestamp": int(time.time()), "message": '成功', "data": data,
                  "req_id": str(uuid.uuid4().hex), "fix_flag": fix_flag}
        response = make_response(json.dumps(result, ensure_ascii=False, cls=CJsonEncoder), 200)
        response.content_type = 'application/json; charset=utf-8'
        return response

    @staticmethod
    @login_check
    def post():
        app_name = request.pmsd.get('app_name')
        app_desc = request.pmsd.get('app_desc')
        icon_url = request.pmsd.get('icon_url')
        prompt = request.pmsd.get('prompt')
        prompt_info = request.pmsd.get('prompt_info')
        sensitive_words = request.pmsd.get('sensitive_words')
        aigc_model_id = request.pmsd.get('aigc_model_id')
        knowledge_ids = request.pmsd.get('knowledge_ids')
        auth_scheme_id = request.pmsd.get('auth_scheme_id') or None
        size = request.pmsd.get('size') or 5
        mini_score = request.pmsd.get('mini_score') or 1.4
        is_mixture = request.pmsd.get('is_mixture') or 0  # 混合检索
        digest_size = request.pmsd.get('digest_size') or size
        digest_score = request.pmsd.get('digest_score') or mini_score
        is_graph = request.pmsd.get('is_graph') or 1  # 知识图谱
        welcome_content = request.pmsd.get('welcome_content') or 1  # 欢迎语
        tp_user_id = request.user['tp_user_id']
        qa_lib_ids = request.pmsd.get('qa_lib_ids') or []
        qa_score = request.pmsd.get('qa_score') or 1.9
        scope = request.pmsd.get('scope') or 0
        dataset_enhance = request.pmsd.get('dataset_enhance') or 0
        app_type = request.pmsd.get('app_type')
        is_rerank = request.pmsd.get('is_rerank', 0)
        rerank_size = request.pmsd.get('rerank_size', 5)
        rarank_ignore_score = request.pmsd.get('rarank_ignore_score', 1.4)
        global_percent = request.pmsd.get('global_percent', 50)
        pic_rarank_ignore_score = request.pmsd.get('pic_rarank_ignore_score', 0)
        is_pic_rerank = request.pmsd.get('is_pic_rerank', 0)
        parent_directory_id = request.pmsd.get('parent_directory_id', None)
        graph_mode = request.pmsd.get('graph_mode', 0)
        library_ids = request.pmsd.get('library_ids') or []  # 获取library_ids参数
        is_global = request.pmsd.get('is_global') or 0
        global_rerank_num = request.pmsd.get('global_rerank_num') or 20
        need_suggestion = request.pmsd.get('need_suggestion', 1)
        # for entry app type
        sample_list = request.pmsd.get('sample_list') or []
        scope_id = request.pmsd.get('scope_id', None)
        is_multi_qa = request.pmsd.get('is_multi_qa', 0)
        multi_qa_size = request.pmsd.get('multi_qa_size', 20)
        # 初始化默认值
        children = []
        classify_name = ""
        classify_prompt = ""
        classify_target = ""
        classify_priority = 0
        ques_enabled=0
        ques_prompt = ""
        ques_keywords = ""
        ques_enhance = ""
        ques_replace = ""

        # 根据 app_type 更新值
        if AppType.is_entry_app(app_type):
            children = request.pmsd.get('children', [])
            classify_priority = request.pmsd.get('classify_priority', 0)
        elif AppType.is_normal_app(app_type):
            classify_name = request.pmsd.get('classify_name', "")
            classify_prompt = request.pmsd.get('classify_prompt', "")
            classify_target = ",".join(request.pmsd.get('classify_target', []))
            ques_enabled = request.pmsd.get('ques_enabled', 0)
            ques_prompt = request.pmsd.get('ques_prompt', "")
            ques_keywords = ",".join(request.pmsd.get('ques_keywords', []))
            ques_enhance = ",".join(request.pmsd.get('ques_enhance', []))
            ques_replace = json.dumps(request.pmsd.get('ques_replace', []))
        
        controller = AppController()
        es_opt_controller = EsOptController()
        if controller.check_app_name(app_name):
            return json_response(code='FAIL', err_msg='应用名称已存在！')
        if controller.check_prompt_drop_down(prompt_info or []):
            return json_response(code='FAIL', err_msg='提示词变量为下拉时，必须设定下拉值！')
        # 整理参数为字典
        kwargs = {
            'scope': scope,
            'app_name': app_name,
            'app_desc': app_desc,
            'icon_url': icon_url,
            'prompt': prompt,
            'prompt_info': prompt_info,
            'aigc_model_id': aigc_model_id,
            'knowledge_ids': knowledge_ids,
            'auth_scheme_id': auth_scheme_id,
            'size': size,
            'mini_score': mini_score,
            'is_mixture': is_mixture,
            'is_graph': is_graph,
            'welcome_content': welcome_content,
            'tp_user_id': tp_user_id,
            'app_type': app_type,
            'children': children,
            'classify_name': classify_name,
            'classify_prompt': classify_prompt,
            'classify_target': classify_target,
            'classify_priority': classify_priority,
            'ques_enabled': ques_enabled,
            'ques_prompt': ques_prompt,
            'ques_keywords': ques_keywords,
            'ques_enhance': ques_enhance,
            'ques_replace': ques_replace,
            'digest_size': digest_size,
            'digest_score': digest_score,
            'qa_score': qa_score,
            'qa_lib_ids': qa_lib_ids,
            'is_rerank': is_rerank,
            'rerank_size': rerank_size,
            'rarank_ignore_score': rarank_ignore_score,
            'global_percent': global_percent,
            'pic_rarank_ignore_score': pic_rarank_ignore_score,
            'is_pic_rerank': is_pic_rerank,
            'parent_directory_id': parent_directory_id,
            'graph_mode': graph_mode,
            'sensitive_words': sensitive_words,
            'dataset_enhance': dataset_enhance,
            'library_ids': library_ids,
            'is_global': is_global,
            'global_rerank_num': global_rerank_num,
            'need_suggestion': need_suggestion,
            'scope_id': scope_id,
            'is_multi_qa': is_multi_qa,
            'multi_qa_size': multi_qa_size,
        }

        # 使用字典解包调用 create_app
        app_id = controller.create_app(**kwargs)
        # [{'question': 'qaqaq'}, {'question': 'wewewe'}] 取每一项的question
        question_list = [sample['question'] for sample in sample_list]
        SampleController().create_samples(app_id, question_list)
        es_opt_controller.save_description(SaveAppParams(app_id=app_id, description=app_desc, classify_prompt=classify_prompt, scope=scope, name=app_name))
        return json_response(data={'app_id': app_id})

    @staticmethod
    @login_check
    def put():
        app_id = request.pmsd.get('app_id')
        app_name = request.pmsd.get('app_name')
        app_desc = request.pmsd.get('app_desc')
        icon_url = request.pmsd.get('icon_url')
        prompt = request.pmsd.get('prompt')
        sensitive_words = request.pmsd.get('sensitive_words')
        prompt_info = request.pmsd.get('prompt_info')
        aigc_model_id = request.pmsd.get('aigc_model_id')
        knowledge_ids = request.pmsd.get('knowledge_ids')
        auth_scheme_id = request.pmsd.get('auth_scheme_id') or None
        size = request.pmsd.get('size') or 5
        mini_score = request.pmsd.get('mini_score') or 1.4
        is_mixture = request.pmsd.get('is_mixture') or 0  # 混合检索
        digest_size = request.pmsd.get('digest_size') or size
        digest_score = request.pmsd.get('digest_score') or mini_score
        is_graph = request.pmsd.get('is_graph') and 1  # 知识图谱
        welcome_content = request.pmsd.get('welcome_content') or 1  # 欢迎语
        tp_user_id = request.user['tp_user_id']
        app_type = request.pmsd.get('app_type')
        qa_lib_ids = request.pmsd.get('qa_lib_ids') or []
        qa_score = request.pmsd.get('qa_score') or 1.9
        scope = request.pmsd.get('scope') or 0
        dataset_enhance = request.pmsd.get('dataset_enhance') or 0

        is_rerank = request.pmsd.get('is_rerank', 0)
        rerank_size = request.pmsd.get('rerank_size', 5)
        rarank_ignore_score = request.pmsd.get('rarank_ignore_score', 1.4)
        global_percent = request.pmsd.get('global_percent', 50)
        pic_rarank_ignore_score = request.pmsd.get('pic_rarank_ignore_score', 0)
        is_pic_rerank = request.pmsd.get('is_pic_rerank', 0)
        graph_mode = request.pmsd.get('graph_mode', 0)
        is_global = request.pmsd.get('is_global') or 0
        global_rerank_num = request.pmsd.get('global_rerank_num') or 20
        need_suggestion = request.pmsd.get('need_suggestion', 1)
        scope_id = request.pmsd.get('scope_id', None)
        is_multi_qa = request.pmsd.get('is_multi_qa', 0)
        multi_qa_size = request.pmsd.get('multi_qa_size', 20)

        # 初始化默认值
        children = []
        classify_name = ""
        classify_prompt = ""
        classify_target = ""
        classify_priority = 0
        ques_enabled=0
        ques_prompt = ""
        ques_keywords = ""
        ques_enhance = ""
        ques_replace = ""

        # 根据 app_type 更新值
        if AppType.is_entry_app(app_type):
            children = request.pmsd.get('children', [])
            classify_priority = request.pmsd.get('classify_priority', 0)
        elif AppType.is_normal_app(app_type):
            classify_name = request.pmsd.get('classify_name', "")
            classify_prompt = request.pmsd.get('classify_prompt', "")
            classify_target = ",".join(request.pmsd.get('classify_target', []))
            ques_enabled = request.pmsd.get('ques_enabled', 0)
            ques_prompt = request.pmsd.get('ques_prompt', "")
            ques_keywords = ",".join(request.pmsd.get('ques_keywords', []))
            ques_enhance = ",".join(request.pmsd.get('ques_enhance', []))
            ques_replace = json.dumps(request.pmsd.get('ques_replace', []))

        library_ids = request.pmsd.get('library_ids') or []  # 获取library_ids参数
        
        controller = AppController()
        es_opt_controller = EsOptController()
        if not controller.check_app_id(app_id):
            return json_response(code='FAIL', err_msg='应用ID不存在！')

        ori_data = controller.get_ai_app(None, None, app_id)
        if ori_data[0]['app_type'] != app_type:
            return json_response(code='FAIL', err_msg='不允许修改应用类型！')
        
        kwargs: dict[str, Any] = {
            'app_id': app_id,
            'scope': scope,
            'app_name': app_name,
            'app_desc': app_desc,
            'icon_url': icon_url,
            'prompt': prompt,
            'prompt_info': prompt_info,
            'aigc_model_id': aigc_model_id,
            'knowledge_ids': knowledge_ids,
            'auth_scheme_id': auth_scheme_id,
            'size': size,
            'mini_score': mini_score,
            'is_mixture': is_mixture,
            'is_graph': is_graph,
            'welcome_content': welcome_content,
            'tp_user_id': tp_user_id,
            'app_type': app_type,
            'children': children,
            'classify_name': classify_name,
            'classify_prompt': classify_prompt,
            'classify_target': classify_target,
            'classify_priority': classify_priority,
            'ques_enabled': ques_enabled,
            'ques_prompt': ques_prompt,
            'ques_keywords': ques_keywords,
            'ques_enhance': ques_enhance,
            'ques_replace': ques_replace,
            'digest_size': digest_size,
            'digest_score': digest_score,
            'qa_score': qa_score,
            'qa_lib_ids': qa_lib_ids,
            'is_rerank': is_rerank,
            'rerank_size': rerank_size,
            'rarank_ignore_score': rarank_ignore_score,
            'global_percent': global_percent,
            'pic_rarank_ignore_score': pic_rarank_ignore_score,
            'is_pic_rerank': is_pic_rerank,
            'graph_mode': graph_mode,
            'sensitive_words': sensitive_words,
            'dataset_enhance': dataset_enhance,
            'library_ids': library_ids,
            'is_global': is_global,
            'global_rerank_num': global_rerank_num,
            'need_suggestion': need_suggestion,
            'scope_id': scope_id,
            'is_multi_qa': is_multi_qa,
            'multi_qa_size': multi_qa_size
        }
        controller.update_app(**kwargs)  # 添加library_ids参数  
        
        es_opt_controller.update_description(SaveAppParams(app_id=app_id, description=app_desc, classify_prompt=classify_prompt, scope=scope, name=app_name))
        return json_response()

    @staticmethod
    @login_check
    def delete():
        app_id = request.pmsd.get('app_id')
        controller = AppController()
        es_opt_controller = EsOptController()
        if not controller.check_app_id(app_id):
            return json_response(code='FAIL', err_msg='应用ID不存在！')
        controller.delete_app(app_id)
        es_opt_controller.delete_app_description(app_id)
        return json_response()


class AiAppDetailView(Resource):

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('app_id', type=str, default='', required=True, location='args')
        args = parser.parse_args()
        app_id = args.get('app_id')
        if not app_id:
            return json_response(code='FAIL', err_msg='app_id不能为空！')
        controller = AppController()
        data = controller.get_ai_app_v2('', '', app_id=app_id)
        data = data[0] if data and data[0] else {}
        return json_response(data=data)


class AppPromptView(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('app_id', type=str, default='', required=False, location='args')
        args = parser.parse_args()
        controller = AppController()
        data_list = controller.get_prompt_by_app_ids([args.get('app_id')])
        return json_response(data={'data_list': data_list})


class AiAppSampleActionView(Resource):
    @staticmethod
    def get(action):
        if action == 'export':
            logger.info("开始导出文档")
            parser = reqparse.RequestParser()
            parser.add_argument('app_id', type=int, location='args', help='应用ID', required=True)
            parser.add_argument('corp_id', type=str, location='args', help='公司ID', required=True)
            args = parser.parse_args()

            app_id = args['app_id']
            corp_id = args['corp_id']
            controller = SampleController(corp_id=corp_id)

            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
                temp_file_path = temp_file.name  # 获取临时文件的路径

            try:
                controller.export_sample(app_id, temp_file_path)
                response = make_response(send_file(temp_file_path, as_attachment=True,
                                                   mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                                   download_name='sample_output.xlsx'))
                response.headers['Content-Disposition'] = 'attachment; filename=sample_output.xlsx'
                response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                response.headers['X-Content-Type-Options'] = 'nosniff'
                logger.info(f"准备返回文件：{response.headers['Content-Disposition']}")
                return response
            except Exception as e:
                return json_response(data={'message': 'export error'})
            finally:
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
        else:
            return json_response(code='FAIL')

    @staticmethod
    def delete(action):
        if action == 'truncate':
            # 解析请求中的参数
            parser = reqparse.RequestParser()
            parser.add_argument('app_id', type=int, location='args', help='应用ID', required=True)
            parser.add_argument('action', type=str, location='args', help='应用ID', required=True)
            args = parser.parse_args()
            controller = SampleController()

            if args['action'] == 'truncate':
                data = controller.truncate_sample_list(args['app_id'], args['action'])
                return json_response(data=data)
            else:
                return json_response(code='FAIL')
        else:
            return json_response(code='FAIL')


    @staticmethod
    def post(action):
        if action == 'import':
            # 解析请求中的参数
            parser = reqparse.RequestParser()
            parser.add_argument('app_id', type=int, location='form', help='应用ID', required=False)
            # parser.add_argument('file', type=FileStorage, location='form', required=True, help='Excel文件')
            args = parser.parse_args()

            app_id = args['app_id']
            # excel_file = args['file']
            excel_file = (request.files.getlist('file') or [])[0]
            controller = SampleController()
            file_content = excel_file.read()
            if app_id:
                try:
                    controller.import_sample(app_id, file_content)
                    return json_response(data={'message': 'import started'})

                except Exception as e:
                    return json_response(data={'message': 'import error'})
            else:
                # 解析excel第一列并形成question_list
                try:
                    df = pd.read_excel(pd.io.common.BytesIO(file_content))
                    question_list = df.iloc[:, 0].tolist()
                    return json_response(data={'question_list': question_list})
                except Exception as e:
                    return json_response(data={'message': 'import error'})
        else:
            return json_response(code='FAIL')


class AiAppSampleDetailView(Resource):

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('app_id', type=str, default='', required=True, location='args')
        parser.add_argument('page_size', type=int, default=10, required=False, location='args')
        parser.add_argument('page_no', type=int, default=1, required=False, location='args')
        parser.add_argument('content', type=str, default=1, required=False, location='args')

        args = parser.parse_args()
        controller = SampleController()

        data = controller.get_sample_list(**args)
        return json_response(data=data)

    @staticmethod
    @login_check
    def post():
        app_id = request.pmsd.get('app_id')
        questions = request.pmsd.get('questions')
        controller = SampleController()

        data = controller.create_samples(app_id, questions)
        return json_response(data=data)

    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('app_id', type=str, default='', required=True, location='args')
        parser.add_argument('id', type=str, default='', required=True, location='args')
        parser.add_argument('question', type=str, default='', required=True, location='args')
        args = parser.parse_args()
        controller = SampleController()

        data = controller.update_sample_by_id(args['app_id'], args['id'], args['question'])
        return json_response(data=data)


    @staticmethod
    @login_check
    def delete():
        app_id = request.pmsd.get('app_id')
        questions = request.pmsd.get('ids')
        controller = SampleController()
        data = controller.delete_samples_by_ids(app_id, questions)
        return json_response(data=data)


class AppSessionJudge(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('message', type=str, location='args', help='消息', required=True)
        parser.add_argument('app_id', type=int, location='args', help='应用ID', required=True)
        parser.add_argument('is_create_index', type=int, help='初始化用', required=False, default=0, location='args')
        args = parser.parse_args()
        controller = AppController()
        tp_user_id = request.user['tp_user_id']
        args['tp_user_id'] = tp_user_id
        user_info = TPUserController().user_info(tp_user_id)
        gtid = user_info.get('ww_user_id')
        args['gtid'] = gtid
        corpid = g.corpid

        if gtid:
            all_user_auths = SchemeController().get_user_auths(gtid)
            all_scheme_ids = [x['scheme_id'] for x in all_user_auths]
            if not all_scheme_ids:
                return json_response(data=[])
        else:
            all_scheme_ids = None
        args['all_scheme_ids'] = all_scheme_ids
        app_info = controller.get_ai_app_info_v2(args['app_id'], args)

        if app_info.get('app_type') == AppType.Entry.value and app_info.get('is_global'):
            # 开启全局模式不用分类
            return json_response(data={
                "app_list": [{
                    'app_id': str(args.get('app_id', '')),
                }],
                "llm_answer": [],
                "keyword_list": [],
                "scene_list": [],
            })

        # check app type
        if not AppType.is_entry_app(app_info.get("app_type")):
            raise ValueError(f"{app_info.get('app_type')} is not a valid AppType")

        question_str = copy.deepcopy(args['message'])
        answer = False
        if not IS_910B_EMBEDDINGS:
            answer = controller.search_es_gt_demo_qa(question_str, app_id=args['app_id'])
        if answer:
            # todo 关键字命中时，命中的appid和appname需要修改
            # 命中qa暂时不做命中场景统计
            # task.async_add_aggregate_statistics_session.delay(corpid, args.get("is_create_index"), app_info.get('app_id', ""), app_info.get('app_id', ""), 1, app_info.get('app_name', ""), app_info.get('app_name', ""), scene_type=5)
            return json_response(data={'app_list': []})
        else:
            ret = controller.app_id_classify_inner(question_str, app_info, controller)
            scene_type = ret.get('scene_type')
            app_list = ret.get('app_list', [])
            choice_app_id = app_list[0].get('app_id') if app_list else ""
            choice_app_name = app_list[0].get('app_name') if app_list else ""

            task.async_add_aggregate_statistics_session.delay(corpid, args.get("is_create_index"), choice_app_id,
                                                              app_info.get('app_id', ""), choice_app_name,
                                                              app_info.get('app_name', ""), scene_type)
            # app_list = [
            #     {"app_name": "智能报告", "app_id": "4942783044479094785", "app_content": "智能报告"},
            #     {"app_name": "简历助手", "app_id": "4942743862184513537", "app_content": "简历助手简历助手简历助手简历助手简历助手简历助手简历助手简历助手简历助手简历助手简历助手简历助手简历助手简历助手简历助手简历助手简历助手简历助手简历助手简历助手简历助手简历助手简历助手简历助手"},
            # ]
            return json_response(data=ret)

class AppSessionView(Resource):
    @login_check
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument('message', type=str, location='json', help='消息', required=True)
        parser.add_argument('session_id', type=str, help='会话id', required=False)
        parser.add_argument('is_first', type=bool, help='是否是首次对话', required=True)
        parser.add_argument('is_leader', type=bool, help='是否是首次对话', required=False, default=False)
        parser.add_argument('choice_app_id', type=str, help='重新选择场景ID', required=False, default=False)
        parser.add_argument('app_id', type=str, help='应用ID', required=True)
        parser.add_argument('prompt_list', type=list, help='应用提示词', required=True, default=[], location='json')
        parser.add_argument('is_create_index', type=int, help='初始化用', required=False, default=0, location='json')
        parser.add_argument('scene_type', type=int, help='是否手动选择', required=False, default=0, location='json')
        parser.add_argument('flow_id', help="会话日志", type=str, required=False)
        parser.add_argument('knowledge_ids', type=list, required=False, help="应用使用动态知识库", default=[], location='json')
        parser.add_argument('has_reason', type=int, required=False, default=0, location='json')
        session_start_time = time.time()
        the_token = 0
        args = parser.parse_args()
        has_reason = args.get('has_reason', 0)
        knowledge_ids_request = args.get('knowledge_ids')
        old_app_id = args.get("app_id")
        is_aggregate = False
        question_str = copy.deepcopy(args['message'])
        logger.info(f'****Question****: {question_str}')
        record_app_id = copy.deepcopy(args['app_id'])
        tp_user_id = request.user['tp_user_id']
        args['tp_user_id'] = tp_user_id
        logger.info(f"*** tp_user_id: {tp_user_id} ***")
        user_info = TPUserController().user_info(tp_user_id)
        gtid = user_info.get('ww_user_id')
        args['gtid'] = gtid
        corpid = g.corpid
        # 单次会话流程id
        flow_id = args['flow_id'] or str(uuid.uuid1())
     
        controller = AppController()
        if args['gtid']:
            # 获取当前人员的权限组列表
            all_user_auths = SchemeController().get_user_auths(args['gtid'])
            all_user_auth_ids = [x['scheme_id'] for x in all_user_auths]
        else:
            all_user_auth_ids = None
        args['all_scheme_ids'] = all_user_auth_ids


        app_info = controller.get_ai_app_info_v2(args['app_id'],args)


        if has_reason == 1:
            app_info['aigc_model_id'],app_info['aigc_type_id'],app_info['model_path'],app_info['model_name'] = AppController(corpid).get_aigc_model_id_by_has_reason(has_reason)

        need_suggestion = app_info.get('need_suggestion')

        is_global = app_info.get('is_global') and app_info.get('app_type') == AppType.Entry.value

        old_app_name = app_info.get("app_name", "")

        if app_info.get('app_type', 0) == 2:
            is_aggregate = True
        if app_info['graph_mode'] == GraphMode.GRAPH.value:
            try:
                graph_rag_result = controller.get_graph_rag(question_str, args['app_id'])
                if graph_rag_result is not None:
                    return graph_rag_result
                # 如果图谱查询返回None，继续执行后续逻辑
                logger.info("Graph RAG查询返回None，回退到普通检索流程")
            except Exception as e:
                # 记录异常但不中断请求
                logger.error(f"Graph RAG查询异常: {e}")

        task.async_add_session_flow_record.delay(corpid, -1, flow_id, 'question', content=question_str)

        knowledge_controller = KnowledgeController(g.corpid)


        is_multi_qa = bool(app_info.get('is_multi_qa', 0))
        multi_qa_size = app_info.get('multi_qa_size', 20)
   
        qa_need_summary = is_multi_qa

        if not is_multi_qa:
            # 获取QA阈值在函数里面
            qa_result = controller.search_es_gt_demo_qa(question_str, app_id=record_app_id, is_json=True)
            
            if qa_result.get('qa_modal', '0') == '1':
                qa_need_summary = True
            answer = [qa_result] if qa_result.get('answer', '') else [] 
        else:
            qa_need_summary = True
            answer = controller.search_es_qa_list(question_str, app_id=record_app_id, size=multi_qa_size)
            # content = 

        task.async_add_session_flow_record.delay(corpid, 1, flow_id, 'qa_check', content=answer)

        app_id = args['choice_app_id'] or args['app_id']
        logger.info(f"*** app id: {app_id} ***")

        origin_app_info = controller.get_ai_app_info(args.get('app_id'), args)
        
        if not is_global:
            app_info = controller.get_ai_app_info(app_id)
            if has_reason == 1:
                app_info['aigc_model_id'],app_info['aigc_type_id'],app_info['model_path'],app_info['model_name'] = AppController(corpid).get_aigc_model_id_by_has_reason(has_reason)
            task.async_add_session_flow_record.delay(corpid, 2, flow_id, 'app_info', data_json=app_info)

            
            date_prompt = get_formatted_content()
            if app_info['prompt']:
                app_info['prompt'] = app_info['prompt'] + "\n ## 时间信息 \n " + date_prompt + "\n"

            # question enhancement
            try:
                date_enabled_flag = "1" in app_info['ques_enhance']
                ques_enhancer = QuesEnhancement(app_info['ques_replace'], list_replace_flag=True, date_replace_flag=False,
                                                date_enabled_flag=date_enabled_flag)
                question_str = ques_enhancer.transform_string(question_str)
                logger.info('***enhance question:***: %s' % question_str)
            except Exception as e:
                logger.info(f"Error occurred: {e}")
                pass

            # 个人知识库加载配置
            if ENABLE_SCOPE_ENHANCE:
                if app_info['dataset_enhance'] and app_info['dataset_enhance'] != 0:
                    try:
                        self.update_info_by_scope(app_info, knowledge_controller)
                    except Exception as e:
                        logger.info(f"Error occurred in ENABLE_SCOPE_ENHANCE: {e}")

        if args['is_first']:
            session_id = controller.create_app_session(record_app_id, request.user['tp_user_id'], args['message'][0:20], args['prompt_list'])
        else:
            session_id = args['session_id']

        final_images_list = []
        doc_images_list = []
        hit_doc_list = []

        if answer:
        
            content_info = None
            if qa_need_summary:
                qa_list = [item.get('answer') for item in answer if item.get('answer') ]
                content = controller.set_qa_prompt(prompt_list=args['prompt_list'], message = question_str, app_info=app_info, qa_list = qa_list)
                logger.info(f'qa: {question_str}, qa_summary: {content}')
            else:
                content = args['message']
            logger.info('*********hit answer************: %s' % answer)
        else:

       
            task.async_add_session_flow_record.delay(corpid, 0, flow_id, 's_question', content=question_str)

            if is_global:
                app_id = args.get('app_id')
                simple_app_info = controller.get_ai_app_info_simple(app_id, args)
                if simple_app_info.get('no_auth_knowledge_ids'):
                    search_result = {
                        "global_search_prompt": simple_app_info.get('prompt', ''),
                        "content_info": {
                            "chunk_ids": [],
                            "chunk_contents_list": [],
                            "graph_list": [],
                            "doc_ids": [],
                            "content": "",
                            "chunk_score_map": {},
                            "doc_map_abstract": {}
                        }
                    }
                    content_info = search_result.get('content_info')
                    logger.info(f"当前用户对应用的所有知识库都没有权限，最终结果为：{search_result}")
                else:
                    search_result = controller.global_search(simple_app_info, args, knowledge_controller, question_str, tp_user_id, handle_ques = AppSessionView.handle_ques, task=task)
                    content_info = search_result.get('content_info')
                content_info.update({'chunk_score_map': {}, 'doc_map_abstract': {}})
            else:
                if knowledge_ids_request:
                    app_info['knowledge_ids'] = knowledge_ids_request

                # 差旅助手 提取 公司 找对应文档 领导问答
                if bool(app_info['ques_enabled']):
                    AppSessionView.handle_ques(app_id, app_info, args, controller, question_str, knowledge_controller)
                    task.async_add_session_flow_record.delay(corpid, 3, flow_id, 'tag', data_json={'doc_ids': app_info.get('doc_ids'), 'tag_map': app_info.get('graph_content_list')})

                content_info = controller.set_first_message(task, args['prompt_list'], question_str, app_info)


            chunk_ids = content_info['chunk_ids']
            logger.info(f'chunk_ids: {chunk_ids}')

            flow_log_json = {'chunk_score_map': content_info['chunk_score_map'], 'graph_list': content_info['graph_list'],
                             'doc_map_abstract': content_info['doc_map_abstract']}
            task.async_add_session_flow_record.delay(corpid, 4, flow_id, 'search_ret', data_json=flow_log_json)

            input_llm_chunk_contents_list = content_info['chunk_contents_list'][:3]

            # hit chunks
            if chunk_ids and not answer and app_id != '4938415833111072769':
                final_images_list = knowledge_controller.get_chunk_hits_images(chunk_ids)
                hit_doc_list = controller.get_doc_list_by_doc_ids(content_info['doc_ids'], chunk_ids)
                doc_images_list = knowledge_controller.get_chunk_hits_images_v2(chunk_ids, controller = AppController(), message=question_str, app_info = app_info)

            content = content_info['content']
            old_content = []
            logger.info(f'====多轮=====: {old_content}')


        message_id = controller.create_app_session_record(session_id, flow_id, record_app_id, args['is_first'], args['message'], content_info, app_id)
        anwer_time = time.time() - session_start_time
        is_success_session = True

        def enhance_model_setting(text_content: str):
            controller = ChatController()
            model_list = controller.get_model_list([2])
            knowledge_controller = KnowledgeController(g.corpid)
            target = EnsembleLauncher(knowledge_controller.get_ebd, knowledge_controller.es).launch(model_list, text_content)
            aigc_model_id = target['aigc_model_id']
            return next(model for model in model_list if model['aigc_model_id'] == aigc_model_id)

        @stream_with_context
        def generate():
            nonlocal final_images_list
            nonlocal doc_images_list
            nonlocal is_success_session
            all_answer = ''
            all_reasoning_content = ''
            # 模型决策
            if ENABLE_ENSEMBLE and app_info['model_auto_enable'] == 1 and content:
                model = enhance_model_setting(content)
                app_info['aigc_type_id'] = model['aigc_type_id']
                app_info['model_path'] = model['model_path']

            if answer:
                is_success_session = True

                if qa_need_summary:
                    for item,reasoning_content in controller.ai_app_chat(content, [], app_info['aigc_type_id'], app_info['model_path'],model_id=app_info['aigc_model_id']):
                        data = json.dumps(
                            {'content': item or '', 'message_id': str(message_id), 'session_id': session_id, 'is_send': False,
                            'role': 'assistant',
                            'type': 'message',
                            'reasoning_content': reasoning_content},
                            ensure_ascii=False)
                        all_answer += item or ''
                        all_reasoning_content += reasoning_content or ''
                        yield f"data: {data}\n\n"

                else:
                    real_answer = answer[0].get('answer', '')
                    qa = real_answer.replace('\\r', '\n\n').replace('\r', '\n\n').replace('""', '').replace('" "', '')
                    for item in qa:
                        data = json.dumps(
                            {'content': item or '', 'message_id': str(message_id), 'session_id': session_id, 'is_send': False,
                            'role': 'assistant',
                            'type': 'message'},
                            ensure_ascii=False)
                        all_answer += item or ''
                        time.sleep(0.01)
                        yield f"data: {data}\n\n"
            elif not answer and not chunk_ids:
                is_success_session = False
                logger.info('********bing大搜***********')
                item = f"抱歉，针对这个问题还没具备对应的准确内部知识。"
                data = json.dumps(
                    {'content': item or '', 'message_id': str(message_id), 'session_id': session_id, 'is_send': False,
                     'role': 'assistant', 'type': 'message'}, ensure_ascii=False)
                all_answer += item or ''
                yield f"data: {data}\n\n"
               
            else:
          
                for item,reasoning_content in controller.ai_app_chat(content, old_content, app_info['aigc_type_id'], app_info['model_path'],model_id=app_info['aigc_model_id']):
                    data = json.dumps(
                        {'content': item or '', 'message_id': str(message_id), 'session_id': session_id, 'is_send': False,
                         'role': 'assistant',
                         'type': 'message',
                         'reasoning_content': reasoning_content},
                        ensure_ascii=False)
                    all_answer += item or ''
                    all_reasoning_content += reasoning_content or ''
                    yield f"data: {data}\n\n"
                if '抱歉' not in all_answer and app_id != '4938415833111072769':
                    # 返回命中 doc and chunk
                    data = json.dumps(
                        {'content': '', 'hit_content': {'hit_list': hit_doc_list}, 'message_id': str(message_id), 'session_id': session_id, 'is_send': False, 'role': 'assistant',
                         'type': 'message'}, ensure_ascii=False)
                    yield f"data: {data}\n\n"

                if '抱歉' not in all_answer and '对不起' not in all_answer:
                    hard_no_img_list = ['组织', '机构', '架构']

                    if IS_910B_EMBEDDINGS:
                        if not (app_id == '4942783044479094785' or any(x in question_str for x in hard_no_img_list)):
                            final_images_list = []
                            doc_images_list = []
                    data = json.dumps(
                        {'content': '', 'image_list': final_images_list, 'message_id': str(message_id), 'session_id': session_id, 'is_send': False,
                         'role': 'assistant',
                         'type': 'message'}, ensure_ascii=False)
                    yield f"data: {data}\n\n"
                    # 返回带文档名称的命中图片
                    data = json.dumps(
                        {'content': '', 'doc_image_list': doc_images_list, 'message_id': str(message_id), 'session_id': session_id,
                         'is_send': False,
                         'role': 'assistant',
                         'type': 'message'}, ensure_ascii=False)
                    yield f"data: {data}\n\n"

            if AppType.is_entry_app(origin_app_info.get('app_type')) and not answer and not is_global:
                data = json.dumps(
                    {'content': '', 'choice_app_id': app_id, 'message_id': str(message_id), 'session_id': session_id, 'is_send': False,
                     'role': 'assistant'}, ensure_ascii=False)
                yield f"data: {data}\n\n"
            
            data = json.dumps(
                {'content': '', 'message_id': str(message_id), 'session_id': session_id, 'is_send': True, 'role': 'assistant',
                 'type': 'message'}, ensure_ascii=False)
            yield f"data: {data}\n\n"
            if '抱歉' in all_answer or '对不起' in all_answer:
                is_success_session = False
            session_end_time = time.time() - session_start_time
            is_new_session = False
            if args.get("is_first"):
                is_new_session = True
            else:
                is_new_session = controller.get_is_new_session(tp_user_id, session_id, session_start_time)
            scene_type = args.get("scene_type", 0)
            if scene_type == 1:
                # scene_type 0 为 手动选择
                task.async_add_aggregate_statistics_session.delay(corpid, args.get("is_create_index"), args['choice_app_id'], old_app_id, app_info.get('app_name', ""), old_app_name, scene_type=0)
            the_token = (len(content) + len(all_answer))
            
            task.async_add_statistics_session.delay(corpid, args.get("is_create_index"), is_success_session, session_start_time, anwer_time, session_end_time, args.get('message'), args['choice_app_id'], 
                                                    tp_user_id, session_id, is_new_session, the_token, old_app_id, is_aggregate, app_info.get('app_name', ""), old_app_name)
            task.async_update_ai_app_session_record.delay(g.corpid, all_answer, all_reasoning_content, session_id, str(message_id), final_images_list, hit_doc_list, doc_images_list)
            task.async_add_session_flow_record.delay(corpid, 5, flow_id, 'answer', content=all_answer, reasoning_content=all_reasoning_content)

            if '抱歉' in all_answer or '对不起' in all_answer or not need_suggestion:
                data = json.dumps(
                    {'content': '', 'message_id': str(message_id), 'session_id': session_id, 'is_send': True, 'role': 'assistant',
                     'type': 'suggestion', 'sug_status': True}, ensure_ascii=False)
                yield f"data: {data}\n\n"
            else:

                if settings.ENABLE_TYSK_DEMO == 1 and int(app_id) == settings.TYSK_DEMO_APP_ID:
                    logger.info(f"=== 开始问题溯源 ===")
                    hit_source_questions_list = []
                    for chunk_id in chunk_ids:
                        hit_source_questions_list.append(settings.CHUNK_ID_QUESTION_MAP.get(int(chunk_id), ''))
                    f_hit_source_questions_list = hit_source_questions_list[:3]
                    logger.info(
                        f"=== hit_source_questions_list: {hit_source_questions_list}, \nf_hit_source_questions_list: {f_hit_source_questions_list}")
                    q_index = 1
                    for sug in f_hit_source_questions_list:
                        if q_index == int(len(f_hit_source_questions_list)):
                            formatted_sug = f"{q_index}.{sug}"
                        else:
                            formatted_sug = f"{q_index}.{sug}\n"
                        data = json.dumps(
                            {'content': formatted_sug or '', 'message_id': str(message_id), 'session_id': session_id, 'is_send': True,
                             'role': 'assistant',
                             'type': 'suggestion', 'sug_status': False}, ensure_ascii=False)
                        yield f"data: {data}\n\n"
                        q_index += 1
                elif not answer:
                    logger.info(f"=== 开始问题推荐 hit counts: {len(input_llm_chunk_contents_list)}===")
                    if not input_llm_chunk_contents_list:
                        suggestion_message = controller.set_suggestion_message(all_answer)
                    else:
                        suggestion_message = controller.set_suggestion_message_by_hit_chunks(input_llm_chunk_contents_list)
                    for sug, reasoning_content in controller.ai_app_chat(suggestion_message, [], app_info['aigc_type_id'], app_info['model_path'],model_id=app_info['aigc_model_id']):
                        data = json.dumps(
                            {'content': sug or '', 'message_id': str(message_id), 'session_id': session_id, 'is_send': True,
                             'role': 'assistant', 'type': 'suggestion', 'sug_status': False}, ensure_ascii=False)
                        yield f"data: {data}\n\n"
                data = json.dumps(
                    {'content': '', 'message_id': str(message_id), 'session_id': session_id, 'is_send': True, 'role': 'assistant',
                     'type': 'suggestion', 'sug_status': True}, ensure_ascii=False)
                yield f"data: {data}\n\n"


        return Response(generate(), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'
        })

    def update_info_by_scope(self, app_info, knowledge_controller):
        SCORE_TYPE = 0

        scope = app_info.get('scope', -1)
        knowledge_ids = app_info['knowledge_ids']
        if scope != 1:
            return
        if not knowledge_ids or len(knowledge_ids) == 0 or knowledge_ids[0] is None:
            return
        knowledge_id = knowledge_ids[0]

        try:
            individual_val = knowledge_controller.get_knowledge_individual(knowledge_id)
        except Exception as e:
            logger.info(f"Error getting knowledge individual: {e}")
            return

        if individual_val is None:
            return

        def get_within_range(value, min_val, max_val):
            if value < min_val:
                return min_val
            elif value > max_val:
                return max_val
            return value

        if individual_val:
            if SCORE_TYPE == 1:
                logger.info(f"update_info_by_scope: {individual_val}")
                recall_size = getattr(individual_val, "recall_size", 8)
                recall_threshold = getattr(individual_val, "recall_threshold", 1.4)
                recall_size = get_within_range(recall_size, 3, 20)
                recall_threshold = get_within_range(recall_threshold, 1.2, 1.8)
                app_info['size'] = recall_size
                app_info['mini_score'] = recall_threshold

            elif SCORE_TYPE == 0:
                logger.info(f"update_info_by_scope: {individual_val}")
                avg_paragraphs_length = getattr(individual_val, "recall_threshold", None)
                if avg_paragraphs_length is None:
                    return
                recall_size = app_info.get('size', 8)
                recall_threshold = app_info.get('mini_score', 1.4)

                adjustments = [
                    (0, 50, 4, 0.1),
                    (50, 100, 4, 0.1),
                    (100, 200, 2, 0.0),
                    (200, 600, 1, 0.0),
                    (600, 1000, 0, 0.0)
                ]

                for lower, upper, size_increment, threshold_increment in adjustments:
                    if lower <= avg_paragraphs_length < upper:
                        logger.info(f"size_increment:{size_increment}, threshold_increment:{threshold_increment}")
                        recall_size += size_increment
                        recall_threshold += threshold_increment
                        break

                recall_size = get_within_range(recall_size, 5, 20)
                recall_threshold = get_within_range(recall_threshold, 1.2, 1.8)
                app_info['size'] = recall_size
                app_info['mini_score'] = recall_threshold

            logger.info(f"update_info_by_scope done. recall_size:{app_info['size']} recall_threshold:{app_info['mini_score']}")

    @staticmethod
    def handle_ques(app_id, app_info, args, controller, question_str, knowledge_controller=None):
        knowledge_list = controller.get_app_knowledge_list(app_id)
        rel_doc_list = controller.get_doc_list_by_session(knowledge_list)
        classifier_doc_ids = []
        if app_info.get('ques_prompt', ''):
            classifier_doc_ids = controller.knowledge_doc_classifier(
                args['message'], app_info['ques_prompt'], rel_doc_list, app_info['aigc_type_id'], app_info['model_path'], model_id=app_info['aigc_model_id'])
        logger.info(f"********classifier_doc_ids: {classifier_doc_ids}********")
        if classifier_doc_ids:
            replace_doc_ids = classifier_doc_ids.replace('公司简称：', '').replace(' ', '').replace(';', '').replace(
                '，', ',')
            classifier_doc_ids = replace_doc_ids.strip()
            classifier_doc_ids = classifier_doc_ids.split(',')
            logger.info(f"********classifier_doc_ids: {classifier_doc_ids}********")
        else:
            # classifier_doc_ids = ['国投智能']
            logger.info(f"********未匹配到 默认: {classifier_doc_ids}********")

        keywords = []
        # 获取知识库的所有关键词
        if knowledge_controller is not None:
            keywords = knowledge_controller.get_keywords_by_knowledge_ids(app_info['knowledge_ids'])
            print(f"********知识库{app_info['knowledge_ids']}keywords: {keywords}********")
        else:
            keywords = (app_info['ques_keywords'] or '').split(',')
            print(f"********应用配置关键词: {keywords}********")
        for keyword in keywords:
            keyword = keyword.strip()
            if keyword in question_str and keyword:
                classifier_doc_ids.append(keyword)
        logger.info(f"********classifier_doc_ids: {classifier_doc_ids}********")
        classifier_info = controller.get_doc_list_by_description(app_info['knowledge_ids'], classifier_doc_ids)
        if classifier_info and not classifier_info['doc_ids']:
            classifier_info = controller.get_doc_list_by_description(app_info['knowledge_ids'], [])

        app_info.update(classifier_info)


    @staticmethod
    @login_check
    def get():

        parser = reqparse.RequestParser()
        parser.add_argument('app_id', type=str, required=True, location='args')
        parser.add_argument('page_size', type=int, required=False, default=10, location='args')
        parser.add_argument('page_no', type=int, required=False, default=1, location='args')
        parser.add_argument('session_name', type=str, required=False, location='args')
        parser.add_argument('add_time', type=str, required=False, location='args')
        parser.add_argument('session_id', type=str, required=False, location='args')
        parser.add_argument('session_type', type=str, required=False, location='args', default='search')
        args = parser.parse_args()
        controller = AppController()
        args['tp_user_id'] = request.user['tp_user_id']
        if args['session_id']:
            args['session_id'] = args['session_id']
            old_content = controller.get_app_session_record(args['session_id'], False)
            return json_response(data={'data_list': old_content})
        else:
            data = controller.get_app_session_list(args)
            return json_response(data=data)

    @staticmethod
    @login_check
    def delete():
        parser = reqparse.RequestParser()
        parser.add_argument('session_id', type=str, required=True, location='json')
        args = parser.parse_args()
        controller = AppController()
        controller.update_app_session(args['session_id'], {'delete_flag': DELETE_FLAG_TRUE})
        return json_response(data={'session_id': args['session_id']})

    @staticmethod
    @login_check
    def put():
        parser = reqparse.RequestParser()
        parser.add_argument('session_id', type=str, required=True, location='json')
        parser.add_argument('action_name', type=str, required=True, choices=['session_name', 'is_top'], location='json')
        parser.add_argument('action_value', type=str, required=True, location='json')
        args = parser.parse_args()
        controller = AppController()
        controller.update_app_session(args['session_id'], {args['action_name']: args['action_value']})
        return json_response(data={'session_id': args['session_id']})


# 新增一个分组展示的接口
class AppSessionGroupView(Resource):
    @staticmethod
    @login_check
    def get():
        """
        获取按时间分组的会话列表
        GET /ai/app/session/group
        """
        parser = reqparse.RequestParser()
        parser.add_argument('app_id', type=str, required=True, location='args')
        parser.add_argument('page_size', type=int, required=False, default=10, location='args')
        parser.add_argument('page_no', type=int, required=False, default=1, location='args')
        parser.add_argument('session_name', type=str, required=False, location='args')
        parser.add_argument('add_time', type=str, required=False, location='args')
        parser.add_argument('session_id', type=str, required=False, location='args')
        parser.add_argument('session_type', type=str, required=False, location='args', default='search')

        args = parser.parse_args()
        args['tp_user_id'] = request.user['tp_user_id']
        controller = AppController()

        if args['session_id']:
            args['session_id'] = args['session_id']
            old_content = controller.get_app_session_record(args['session_id'], False)
            return json_response(data={'data_list': old_content})
        else:
            data = controller.get_app_session_list_v2(args)  #  v2 版本
            return json_response(data=data)

class AppSessionFlow(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('session_id', type=str, required=False, location='args')
        parser.add_argument('flow_id', type=str, required=True, location='args')
        args = parser.parse_args()
        data = AppController().get_session_flow_data(args['flow_id'])
        return json_response(data=data)



class SearchKnowledgeChunkImgView(Resource):
    """
    知识库图片检索，支持文搜图和图搜图
    """

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('search_key', type=str, required=True, location='args')
        parser.add_argument('search_mode', type=int, required=False, default=1, location='args', help='1-词向量匹配，2-全文检索，3-混合检索，4-文本搜图，5-已图搜图')
        parser.add_argument('recall_size', type=int, required=False, default=10, location='args', help='检索结果最大召回数量')
        parser.add_argument('min_score', type=float, required=False, default=1.6, location='args', help='最小匹配分数')
        parser.add_argument('full_text_min_score', type=float, required=False, default=1.6, location='args', help='全文检索最小匹配分数')
        args = parser.parse_args()

        search_mode = int(args.get('search_mode', 1))

        logger.info(f"search_mode: 【{search_mode}】, 1-词向量匹配，2-全文检索，3-混合检索，4-文本搜图，5-已图搜图 \nsearch_key: {args['search_key']}")

        controller = KnowledgeController()

        if search_mode in [4, 5]:  # 文搜图 图搜图
            if search_mode == 5:
                search_key = GraphServerSdk().graph_to_description(args['search_key'])
            else:
                search_key = args['search_key']
            # 知识库图片检索
            knowledge_chunk_img_embedding_data_list = controller.search_es_knowledge_chunk_graph(search_key, size=int(args['recall_size']), min_score=float(args['min_score']))
            embedding_res_list = []
            for item in knowledge_chunk_img_embedding_data_list:
                know_chunk_img_data = controller.get_knowledge_chunk_image_by_graph(item['_source']['chunk_id'])
                if know_chunk_img_data:
                    item['_source'].update(know_chunk_img_data)
                    embedding_res_list.append(item['_source'])
             # 结果去重
            res = []
            existed_know_chunk_img_ids = set()
            
            for item in embedding_res_list:
                if item['chunk_id'] not in existed_know_chunk_img_ids:
                    res.append(item)
                    existed_know_chunk_img_ids.add(item['chunk_id'])

            return json_response(res)
        

class SearchContentKnowledgeView(Resource):
    """
    全文检索
    """

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('search_key', type=str, required=True, location='args')
        parser.add_argument('search_mode', type=int, required=False, default=1, location='args', help='1-词向量匹配，2-全文检索，3-混合检索，4-文本搜图，5-已图搜图')
        parser.add_argument('recall_size', type=int, required=False, default=10, location='args', help='检索结果最大召回数量')
        parser.add_argument('min_score', type=float, required=False, default=1.6, location='args', help='最小匹配分数')
        parser.add_argument('full_text_min_score', type=float, required=False, default=1.6, location='args', help='全文检索最小匹配分数')
        args = parser.parse_args()

        search_mode = int(args.get('search_mode', 1))

        logger.info(f"search_mode: 【{search_mode}】, 1-词向量匹配，2-全文检索，3-混合检索，4-文本搜图，5-已图搜图 \nsearch_key: {args['search_key']}")

        controller = KnowledgeController()
        content_controller = ContentInfoController()
        content_ids = []

        # 初始化数据结构
        embedding_search_map = {"knowledge_list": [], "content_ids": []}
        full_text_search_map = {"knowledge_list": [], "content_ids": []}
        final_chunk_set = set()
        final_chunk_list = []

        # 用于计数的 knowledge_code
        embedding_knowledge_code = 1
        full_text_knowledge_code = 1

        def build_search_map(hits, max_score, search_map, knowledge_code_counter):
            knowledge_dict = {}  # 用于去重的知识库字典
            for item in hits:
                knowledge_id = item['_source']['knowledge_id']
                doc_id = item['_source']['doc_id']
                # check knowledge_id & doc_id
                knowledge_detail = controller.get_knowledge_detail(knowledge_id)
                if not knowledge_detail:
                    logger.info(f"Knowledge_id {knowledge_id} not found in DB, skipping this record.")
                    continue
                
                doc_info = controller.get_doc_info(doc_id)
                if not doc_info:
                    logger.info(f"Doc_id {doc_id} not found in DB, skipping this record.")
                    continue
                
                chunk_id = item['_source']['chunk_id']
                highlight_description = item.get('highlight', {}).get('description', [item['_source']['description']])[0]
                score = item['_score']
                match_score = controller.calculate_match_score(score, max_score=max_score)

                if knowledge_id not in knowledge_dict:
                    knowledge_dict[knowledge_id] = {}

                if doc_id not in knowledge_dict[knowledge_id]:
                    # doc_info = controller.get_doc_info(doc_id)
                    knowledge_dict[knowledge_id][doc_id] = doc_info
                    knowledge_dict[knowledge_id][doc_id]['chunk_list'] = []

                knowledge_dict[knowledge_id][doc_id]['chunk_list'].append({
                    'chunk_id': chunk_id,
                    'doc_id': doc_id,
                    'knowledge_id': knowledge_id,
                    'content': highlight_description,
                    'score': score,
                    'match_score': match_score,
                })
            # 每次检索到该知识库时，增加命中次数
                controller.increment_search_hit_count(knowledge_id)               

            # 构建最终的 search_map 结构
            for knowledge_id, doc_dict in knowledge_dict.items():
                doc_list = list(doc_dict.values())
                search_map['knowledge_list'].append({
                    'knowledge_id': knowledge_id,
                    'knowledge_name': controller.get_knowledge_detail(knowledge_id)['knowledge_name'],
                    'knowledge_code': knowledge_code_counter,
                    'hit_doc_counts': len(doc_list),
                    'doc_list': doc_list,
                })
                knowledge_code_counter += 1

            return knowledge_code_counter

        # 根据 search_mode 执行不同的检索方式
        if search_mode in [1, 3]:  # 词向量匹配或混合检索
            min_score = args['min_score']
            # vector_content_hits = controller.search_es_content(args['search_key'], size=10, min_score=1.6, is_search=True)
            vector_content_hits = controller.search_es_content(args['search_key'], size=10, min_score=min_score, is_search=True)
            authorized_content_ids = content_controller.check_content_ids_auth([item['_source']['content_id'] for item in vector_content_hits])[:500]
            content_ids.extend(authorized_content_ids)
            embedding_search_map['content_ids'].append([item['_source']['content_id'] for item in vector_content_hits])

            for knowledge_ids in controller.get_knowledge_ids_by_model():
                # vector_knowledge_hits = controller.search_es_knowledge(task, args['search_key'], knowledge_ids, size=4, min_score=1.8, is_search=True)
                vector_knowledge_hits = controller.search_es_knowledge(task, args['search_key'], knowledge_ids, size=4, min_score=min_score,
                                                                       is_search=True)
                embedding_knowledge_code = build_search_map(vector_knowledge_hits, max_score=2.0, search_map=embedding_search_map,
                                                            knowledge_code_counter=embedding_knowledge_code)

        if search_mode in [2, 3]:  # 全文检索或混合检索
            # full_text_content_hits = controller.search_es_content_full_text(args['search_key'], size=10, min_score=8.0)
            full_text_content_hits = controller.search_es_content_full_text(args['search_key'], size=10, min_score=8.5)
            authorized_content_ids = content_controller.check_content_ids_auth([item['_source']['content_id'] for item in full_text_content_hits])[:500]
            content_ids.extend(authorized_content_ids)
            full_text_search_map['content_ids'].append([item['_source']['content_id'] for item in full_text_content_hits])

            for knowledge_ids in controller.get_knowledge_ids_by_model():
                # full_text_knowledge_hits = controller.search_es_knowledge_full_text_v1(task, args['search_key'], knowledge_ids, size=4, min_score=8.0)
                full_text_knowledge_hits = controller.search_es_knowledge_full_text_v1(task, args['search_key'], knowledge_ids, size=4,min_score=args['full_text_min_score'])
                full_text_knowledge_code = build_search_map(full_text_knowledge_hits, max_score=12.0, search_map=full_text_search_map,
                                                            knowledge_code_counter=full_text_knowledge_code)
        
        if search_mode in [1, 2, 3]:
            # 如果 knowledge_list 为空，则设置为 {}
            if not embedding_search_map['knowledge_list']:
                embedding_search_map = {}
            if not full_text_search_map['knowledge_list']:
                full_text_search_map = {}

            # 构建最终的data
            data = {
                "search_mode": search_mode,
                "embedding_search_map": embedding_search_map if search_mode in [1, 3] else {},
                "full_text_search_map": full_text_search_map if search_mode in [2, 3] else {},
                "content_ids": list(set(content_ids))
            }
            # 记录搜索
            task.async_update_search_record.delay(g.corpid, args['search_key'], content_ids, request.user['tp_user_id'])

            if embedding_search_map:
                if embedding_search_map.get('knowledge_list'):
                    embedding_knowledge_list = embedding_search_map.get('knowledge_list')
                    for embedding_knowledge_obj in embedding_knowledge_list:
                        if embedding_knowledge_obj.get('doc_list'):
                            for doc_obj in embedding_knowledge_obj.get('doc_list'):
                                if doc_obj.get('chunk_list'):
                                    for chunk_obj in doc_obj.get('chunk_list'):
                                        if chunk_obj.get('chunk_id') not in final_chunk_set:
                                            final_chunk_set.add(chunk_obj.get('chunk_id'))
                                            final_chunk_list.append(chunk_obj)

            if full_text_search_map:
                if full_text_search_map.get('knowledge_list'):
                    full_text_knowledge_list = full_text_search_map.get('knowledge_list')
                    for full_text_knowledge_obj in full_text_knowledge_list:
                        if full_text_knowledge_obj.get('doc_list'):
                            for doc_obj in full_text_knowledge_obj.get('doc_list'):
                                if doc_obj.get('chunk_list'):
                                    for chunk_obj in doc_obj.get('chunk_list'):
                                        if chunk_obj.get('chunk_id') not in final_chunk_set:
                                            final_chunk_set.add(chunk_obj.get('chunk_id'))
                                            final_chunk_list.append(chunk_obj)

            logger.info(f"final_chunk_set: {final_chunk_set}")
            data['chunk_list'] = final_chunk_list

            logger.info(f"*" * 75)
            # logger.info(f"Search final result: {data}")
            return json_response(data)
        
        if search_mode in [4, 5]:  # 文搜图 图搜图
            if search_mode == 5:
                search_key = GraphServerSdk().graph_to_description(args['search_key'])
            else:
                search_key = args['search_key']
            
            embedding_data_list = controller.search_es_content_graph(search_key, size=int(args['recall_size']), min_score=float(args['min_score']))
            embedding_res_list = []
            for item in embedding_data_list:
                content_data = controller.get_content_info_by_graph(item['_source']['content_id'], item['_source']['content_chunk_id'])
                if content_data:
                    item['_source'].update(content_data)
                    embedding_res_list.append(item['_source'])
                    
            ft_data_list = controller.search_es_content_full_text_graph(args['search_key'], size=int(args['recall_size']), min_score=float(args['min_score']*3))
            ft_res_list = []
            for item in ft_data_list:
                content_data = controller.get_content_info_by_graph(item['_source']['content_id'], item['_source']['content_chunk_id'])
                if content_data:
                    item['_source'].update(content_data)
                    ft_res_list.append(item['_source'])
                    
            res_list = embedding_res_list + ft_res_list
            
            # 结果去重
            res = []
            existed_content_ids = set()
            
            for item in res_list:
                if item['content_id'] not in existed_content_ids:
                    res.append(item)
                    existed_content_ids.add(item['content_id'])
            
            return json_response(res)

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('chunk_list', type=list, required=True, location='json')
        parser.add_argument('content_ids', type=list, required=True, location='json')
        parser.add_argument('aigc_model_id', type=str, required=True, location='json')
        args = parser.parse_args()
        controller = AppController()
        system = (f"# 角色\n"
                  f"你是一个知识渊博的智能助手。\n\n"
                  f"## 技能\n"
                  f"### 技能 : 知识库内容总结\n"
                  f"1. 面对复杂或专业的知识，用简单易懂的语言进行总结。\n"
                  f"2. 确保使用的语言通俗化，让用户轻松理解。\n\n"
                  f"3. 返回markdown 格式\n"
                  f"## 限制:\n"
                  f"- 只能用中文回复。\n"
                  f"- 只返回总结的内容。\n"
                  f"- 总结内容按要点，使用无序列表进行输出。\n"
                  f"- 总结内容不要出现其他角色身份。\n"
                  f"- 尽量使用易懂的语言，避免专业复杂术语，方便用户理解。\n"
                  f"- 每次直接回复内容，其他无关上下文不要返回。\n"
                  f"## 知识库内容\n")
        knowledge_content = ""
        content_chunk_list = KnowledgeController().search_es_content_by_content_ids(args['content_ids'])
        for item in content_chunk_list[:4]:
            knowledge_content += f"### {item['_source']['description']}\n"
        for item in args['chunk_list'][:4]:
            knowledge_content += f"### {item['content']}\n"
        content = system + knowledge_content
        model_info = controller.get_model_info(args['aigc_model_id'])
        aigc_type_id = model_info['aigc_type_id']
        model_path = model_info['model_path']
        model_id = model_info['aigc_model_id']

        @stream_with_context
        def generate():
            if knowledge_content:
                for item, reasoning_content in controller.ai_app_chat(content, [], aigc_type_id, model_path, model_id=model_id):
                    data = json.dumps({'message': item or '', 'is_send': False, 'reasoning_content':reasoning_content}, ensure_ascii=False)
                    yield f"data: {data}\n\n"
                data = json.dumps({'message': '', 'is_send': True, 'reasoning_content':''}, ensure_ascii=False)
                yield f"data: {data}\n\n"
            else:
                data = json.dumps({'message': '抱歉，根据搜索的内容无法进行总结', 'is_send': True, 'reasoning_content':''}, ensure_ascii=False)
                yield f"data: {data}\n\n"

        return Response(generate(), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'
        })

    @staticmethod
    @login_check
    def put():
        controller = AppController()
        data = controller.search_recommend_content()
        return json_response(data=data)


class AppPromptOptimizeView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('input_prompt', type=str, required=True, location='json')
        parser.add_argument('aigc_model_id', type=str, required=False, location='json')
        args = parser.parse_args()
        controller = AppController()
        content = (f"你是一个生成提示词专家，请按照下面的格式根据：{args['input_prompt']}，内容生成如下格式的模版提示词。\n"
                   f"请注意以下关键点：\n"
                   f"1.角色的制定一定要描述成一个某方面的专家或者是资深研究员\n"
                   f"2.技能可以是一个或者是多个,技能可以分类型，每个类型可以分成多个方面，技能描述的要和提供的内容密切相关\n"
                   f"3.限制条件是一个或者多个,仅回答内容相关的问题，不要胡言乱语\n"
                   f"以下是模版\n\n"
                   f"# 角色\n"
                   f"## 技能\n"
                   f"### 技能: \n"
                   f"1.\n"
                   f"2.\n"
                   f"## 限制\n"
                   f"- \n")
        aigc_type_id = CHATGPT_CODE
        model_path = 'gpt-4'
        if args['aigc_model_id']:
            model_info = controller.get_model_info(args['aigc_model_id'])
            aigc_type_id = model_info['aigc_type_id']
            model_path = model_info['model_path']
            model_id = model_info['aigc_model_id']

        @stream_with_context
        def generate():
            for item,reasoning_content in controller.ai_app_chat(content, [], aigc_type_id, model_path, model_id=model_id):
                data = json.dumps({'message': item or '', 'is_send': False, 'reasoning_content':reasoning_content}, ensure_ascii=False)
                yield f"data: {data}\n\n"
            data = json.dumps({'message': '', 'is_send': True}, ensure_ascii=False)
            yield f"data: {data}\n\n"

        return Response(generate(), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'
        })


class AppSimilarMaterial(Resource):
    @staticmethod
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('content_name', type=str, required=True, location='args')
        parser.add_argument('content_id', type=str, required=True, location='args')
        args = parser.parse_args()

        content_ids = KnowledgeController().get_similar_material(args['content_name'])
        if args['content_id'] in content_ids:
            content_ids.remove(args['content_id'])
        return json_response(data=content_ids[:5])


class AppSessionReportView(Resource):

    @login_check
    def post(self):
        parser = reqparse.RequestParser()
        # parser.add_argument('message', type=str, location='json', help='消息', required=True)
        parser.add_argument('session_id', type=str, help='会话id', required=False)
        parser.add_argument('app_id', type=str, help='应用ID', required=True)
        parser.add_argument('report_id', type=str, help='应用ID', required=True)

        args = parser.parse_args()

        controller = AppController()
        app_info = controller.get_ai_app_info(args['app_id'])
        session_id = args['session_id']
        #
        report_info = ReportController().ai_report_info(args['report_id'])

        # message_id = controller.create_app_session_record(session_id, args['app_id'], True, report_info['title'], {}, 'report')

        @stream_with_context
        def generate():
            if report_info and report_info['content']:
                random_number = random.randint(1, 5)

                knowledge_content = controller.search_report_knowledge(task, report_info['content'], app_info, size=random_number)
                if report_info['content_prompt'] is None:
                    content = "基于以下数据进行优化总结,要求：不要多次换行，字数控制在100字左右。" + knowledge_content['content']
                else:
                    content = report_info['content_prompt'] + knowledge_content['content']
                for item, reasoning_content in controller.ai_app_chat(content, [], app_info['aigc_type_id'], app_info['model_path'], model_id=app_info['aigc_model_id']):
                    data = json.dumps(
                        {'content': item or '', 'session_id': session_id, 'is_send': False, 'reasoning_content': reasoning_content,
                         'role': 'assistant', 'type': 'message'}, ensure_ascii=False)
                    yield f"data: {data}\n\n"
            if report_info and report_info['content_charts']:
                df = pd.read_excel(BASE_DIR + '/upload/海发投资图表.xlsx', sheet_name=report_info['content_charts'])
                columns = df.columns
                charts_type_list = report_info['charts_type'].split(',')
                charts_list = []
                for i in df.values:
                    data = {}
                    if df[columns[0]].dtypes == 'float64':
                        data['x_value'] = float(i[0])
                    elif df[columns[0]].dtypes == 'int64':
                        data['x_value'] = int(i[0])
                    else:
                        data['x_value'] = str(i[0])
                    data['x_name'] = columns[0]
                    chart_num = 1
                    for chart in charts_type_list:
                        if df[columns[chart_num]].dtypes == 'float64':
                            data[chart + "_value"] = float(i[chart_num])
                        else:
                            data[chart + "_value"] = int(i[chart_num])
                        data[chart + "_name"] = columns[chart_num]
                        chart_num += 1
                    charts_list.append(data)

                charts = {
                    'charts_type': report_info['charts_type'],
                    'title': report_info['title'],
                    'data_list': charts_list
                }
                data = json.dumps(
                    {'content': '', 'session_id': session_id, 'is_send': False,
                     'role': 'assistant', 'type': 'charts', 'charts_data': charts}, ensure_ascii=False)
                yield f"data: {data}\n\n"

            if report_info and report_info['content']:
                quote_list = ReportController().quote_list(knowledge_content['doc_ids'], knowledge_content['chunk_ids'])

                quote_data = {
                    'data_list': quote_list,
                    'quote_num': len(knowledge_content['chunk_ids'])
                }
                data = json.dumps(
                    {'content': '', 'session_id': session_id, 'is_send': False,
                     'role': 'assistant', 'type': 'quote', 'quote_data': quote_data}, ensure_ascii=False)
                yield f"data: {data}\n\n"
            data = json.dumps(
                {'content': '', 'session_id': session_id, 'is_send': True, 'role': 'assistant',
                 'type': 'message'}, ensure_ascii=False)
            yield f"data: {data}\n\n"

        return Response(generate(), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'
        })

    @staticmethod
    @login_check
    def get():

        parser = reqparse.RequestParser()
        parser.add_argument('app_id', type=str, required=True, location='args')
        parser.add_argument('next_report_id', type=str, required=False, default=None, location='args')
        parser.add_argument('message', type=str, required=False, default=None, location='args')
        parser.add_argument('session_id', type=str, required=False, default=None, location='args')
        args = parser.parse_args()
        controller = AppController()
        if args['session_id']:
            session_id = args['session_id']
        else:
            session_id = controller.create_app_session(args['app_id'], request.user['tp_user_id'], args['message'],
                                                       [], 'report')
        report_controller = ReportController()
        res = report_controller.ai_report_menu(args['app_id'], args['next_report_id'])
        res.update({
            'session_id': session_id
        })
        return json_response(data=res)


class AppSessionReportRmsView(Resource):
    @login_check
    def post(self):
        parser = reqparse.RequestParser()
        # parser.add_argument('message', type=str, location='json', help='消息', required=True)
        parser.add_argument('session_id', type=str, help='会话id', required=False)
        parser.add_argument('app_id', type=str, help='应用ID', required=True)
        parser.add_argument('report_id', type=str, help='应用ID', required=True)

        args = parser.parse_args()

        controller = AppController()
        app_info = controller.get_ai_app_info(args['app_id'])
        session_id = args['session_id']
        #
        report_info = ReportController().ai_report_info(args['report_id'])

        # message_id = controller.create_app_session_record(session_id, args['app_id'], True, report_info['title'], {}, 'report')

        @stream_with_context
        def generate():
            if report_info and report_info['content']:
                random_number = random.randint(1, 5)

                knowledge_content = controller.search_report_knowledge(task, report_info['content'], app_info, size=random_number)
                if report_info['content_prompt'] is None:
                    content = "基于以下数据进行优化总结,要求：不要多次换行，字数控制在100字左右。" + knowledge_content['content']
                else:
                    content = report_info['content_prompt'] + knowledge_content['content']
                for item, reasoning_content in controller.ai_app_chat(content, [], app_info['aigc_type_id'], app_info['model_path'], model_id=app_info['aigc_model_id']):
                    data = json.dumps(
                        {'content': item or '', 'session_id': session_id, 'is_send': False, 'reasoning_content': reasoning_content,
                         'role': 'assistant', 'type': 'message'}, ensure_ascii=False)
                    yield f"data: {data}\n\n"
            if report_info and report_info['content_charts']:
                df = pd.read_excel(BASE_DIR + '/upload/海发投资图表.xlsx', sheet_name=report_info['content_charts'])
                columns = df.columns
                charts_type_list = report_info['charts_type'].split(',')
                charts_list = []
                for i in df.values:
                    data = {}
                    if df[columns[0]].dtypes == 'float64':
                        data['x_value'] = float(i[0])
                    elif df[columns[0]].dtypes == 'int64':
                        data['x_value'] = int(i[0])
                    else:
                        data['x_value'] = str(i[0])
                    data['x_name'] = columns[0]
                    chart_num = 1
                    for chart in charts_type_list:
                        if df[columns[chart_num]].dtypes == 'float64':
                            data[chart + "_value"] = float(i[chart_num])
                        else:
                            data[chart + "_value"] = int(i[chart_num])
                        data[chart + "_name"] = columns[chart_num]
                        chart_num += 1
                    charts_list.append(data)

                charts = {
                    'charts_type': report_info['charts_type'],
                    'title': report_info['title'],
                    'data_list': charts_list
                }
                data = json.dumps(
                    {'content': '', 'session_id': session_id, 'is_send': False,
                     'role': 'assistant', 'type': 'charts', 'charts_data': charts}, ensure_ascii=False)
                yield f"data: {data}\n\n"

            if report_info and report_info['content']:
                quote_list = ReportController().quote_list(knowledge_content['doc_ids'], knowledge_content['chunk_ids'])

                quote_data = {
                    'data_list': quote_list,
                    'quote_num': len(knowledge_content['chunk_ids'])
                }
                data = json.dumps(
                    {'content': '', 'session_id': session_id, 'is_send': False,
                     'role': 'assistant', 'type': 'quote', 'quote_data': quote_data}, ensure_ascii=False)
                yield f"data: {data}\n\n"
            data = json.dumps(
                {'content': '', 'session_id': session_id, 'is_send': True, 'role': 'assistant',
                 'type': 'message'}, ensure_ascii=False)
            yield f"data: {data}\n\n"

        return Response(generate(), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'
        })

    @staticmethod
    @login_check
    def get():

        parser = reqparse.RequestParser()
        parser.add_argument('app_id', type=str, required=True, location='args')
        parser.add_argument('next_report_id', type=str, required=False, default=None, location='args')
        parser.add_argument('message', type=str, required=False, default=None, location='args')
        parser.add_argument('session_id', type=str, required=False, default=None, location='args')
        args = parser.parse_args()
        controller = AppController()
        if args['session_id']:
            session_id = args['session_id']
        else:
            session_id = controller.create_app_session(args['app_id'], request.user['tp_user_id'], args['message'],
                                                       [], 'report')
        report_controller = ReportController()
        res = report_controller.ai_report_menu(args['app_id'], args['next_report_id'])
        res.update({
            'session_id': session_id
        })
        return json_response(data=res)


class AppSessionReportRecordView(Resource):

    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('session_id', type=str, help='会话id', required=True)
        parser.add_argument('record_info', type=list, location='json', help='报告记录', required=True)

        args = parser.parse_args()

        controller = ReportController()
        session_id = args['session_id']
        data = {
            'session_id': session_id,
            'record_info': args['record_info']
        }
        controller.update_session_record(session_id, data)
        return json_response(data={'session_id': session_id})

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('session_id', type=str, help='会话id', location='args', required=True)

        args = parser.parse_args()

        data = ReportController().get_session_record(args['session_id'])
        if data:
            return json_response(data={'data_list': data['record_info']})
        else:
            return json_response(data={'data_list': []})


class AppSearchRecommendKeywordView(Resource):

    @staticmethod
    @login_check
    def get():
        data_list = AppController().search_recommend_key_list()
        return json_response(data={"data_list": data_list})


class AppSearchRecommendContentView(Resource):

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('content_id', type=str, help='内容ID', location='args', required=False)

        args = parser.parse_args()
        if args['content_id']:
            data_list = KnowledgeController().search_es_content_description(args['content_id'])
        else:
            data_list = AppController().search_recommend_content_list()
        return json_response(data={"data_list": data_list})


class AppSessionHitView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('doc_id', type=str, help='文档ID', required=True)
        parser.add_argument('chunk_ids', type=list, help='切片ID', location='json', required=True)

        args = parser.parse_args()
        doc_info = KnowledgeController().get_knowledge_document_detail(args['doc_id'])
        data_list = AppController().get_doc_chunk_list_by_doc_ids(args['doc_id'], args['chunk_ids'])
        return json_response(data={"data_list": data_list, 'doc_url': doc_info['doc_url']})


class AppSessionHitV2View(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('doc_id', type=str, help='文档ID', required=True)
        parser.add_argument('chunk_ids', type=list, help='切片ID', location='json', required=True)

        args = parser.parse_args()
        doc_info = KnowledgeController().get_knowledge_document_detail(args['doc_id'])
        data_list = AppController().get_doc_chunk_list_by_doc_ids(args['doc_id'], args['chunk_ids'])
        all_list = KnowledgeController().get_knowledge_doc_chunk_list(args['doc_id'], {'is_all': 'all'})['data_list']
        return json_response(data={"data_list": data_list, 'doc_url': doc_info['doc_url'], 'all_list': all_list})


class AppSessionChoiceView(Resource):

    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('app_id', type=str, help='app_id', required=True, location='args')

        args = parser.parse_args()
        controller = AppController()
        app_info = controller.get_ai_app_info(args['app_id'])

        app_list = []
        for app in app_info['children']:
            app_list.append({
                "id": str(app["app_id"]),
                "app_id": str(app["app_id"]),
                "title": app["app_name"],
                "app_type": app["app_type"],
                # "target": app["classify_target"],
                "llm_prompt": app["classify_prompt"],
                "app_content": app["classify_prompt"],
            })
        return json_response(data={"data_list": app_list})


class AppSessionResultView(Resource):
    @login_check
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument('message', type=str, location='json', help='消息', required=True)
        parser.add_argument('choice_app_id', type=str, required=True, default=False)
        parser.add_argument('gtid', type=str, location='json', help='用户编号id', required=False)

        parser.add_argument('knowledge_ids', type=list, required=False, default=[], location='json')
        parser.add_argument('doc_ids', type=list, required=False, default=[], location='json')
        parser.add_argument('search_all', type=bool, required=False, default=False, location='json',
                            help='knowledge_ids为空时，是否全文检索，仅适用于智能报告，其他服务可以不传这个参数')

        args = parser.parse_args()
        knowledge_ids_request = args.get('knowledge_ids')
        tp_user_id = request.user['tp_user_id']
        args['tp_user_id'] = tp_user_id
        question_str = copy.deepcopy(args['message'])
        logger.info(f'****Question****: {question_str}')
        controller = AppController()
        knowledge_controller = KnowledgeController(g.corpid)

        app_id = args['choice_app_id']

        logger.info(f"*** app id: {app_id} ***")
        if args['gtid']:
            all_user_auths = SchemeController().get_user_auths(args['gtid'])
            all_user_auth_ids = [x['scheme_id'] for x in all_user_auths]
        else:
            all_user_auth_ids = None
        args['all_scheme_ids'] = all_user_auth_ids

        simple_app_info = controller.get_ai_app_info_simple(app_id, args)

        global_search_prompt = ""
        if simple_app_info.get('app_type') == AppType.Entry.value:
            if not simple_app_info.get('is_global'):
                return json_response(code="FAIL", message='该应用不支持全局搜索')
            search_result = controller.global_search(simple_app_info, args, knowledge_controller, question_str, tp_user_id, handle_ques = AppSessionView.handle_ques, task=task )
            content_info = search_result.get('content_info')
            global_search_prompt = search_result.get('global_search_prompt')

        else:
            
            app_info = controller.get_ai_app_info_v2(app_id,args)
    
            date_prompt = get_formatted_content()
            if app_info['prompt']:
                app_info['prompt'] = app_info['prompt'] + "\n ## 时间信息 \n " + date_prompt + "\n"

            try:
                date_enabled_flag = "1" in app_info['ques_enhance']
                ques_enhancer = QuesEnhancement(app_info['ques_replace'], list_replace_flag=True,
                                                date_replace_flag=False,
                                                date_enabled_flag=date_enabled_flag)
                question_str = ques_enhancer.transform_string(question_str)
                logger.info('***enhance question:***: %s' % question_str)
            except Exception as e:
                logger.exception(f"Error occurred: {e}")
                pass

            if knowledge_ids_request:
                app_info['knowledge_ids'] = knowledge_ids_request

            # 差旅助手 提取 公司 找对应文档 领导问答
            if app_info['ques_enabled']:
                AppSessionView.handle_ques(app_id, app_info, args, controller, question_str,knowledge_controller)

            # 如果输入参数有doc_id，报告场景手动选择文档
            doc_ids = args.get('doc_ids')
            if doc_ids:
                app_info['doc_ids'] = doc_ids
            # knowledge_ids接口参数为空时，全文检索
            search_all = args.get('search_all')
            search_flag = False
            if not knowledge_ids_request and search_all:
                search_flag = True

            content_info = controller.set_first_message(task, [], question_str, app_info, search_flag)

        chunk_ids = content_info['chunk_ids']
        logger.info(f'chunk_ids: {chunk_ids}')

        # hit chunks

        final_images_list = []

        hard_no_img_list = ['组织', '机构', '架构']

        if IS_910B_EMBEDDINGS and not (app_id == '4942783044479094785' or any(x in question_str for x in hard_no_img_list)):
            final_images_list = []
        else:
            final_images_list = knowledge_controller.get_chunk_hits_images(chunk_ids)

        hit_doc_list = controller.get_doc_list_by_doc_ids(content_info['doc_ids'], chunk_ids)
        # if app_info and app_info.get('graph_content_list'):
        #     graph_content_list = [{'doc_id': i['doc_id'], 'graph_content': i['graph_content']} for i in app_info['graph_content_list']]
        # else:
        #     graph_content_list = []
        return_doc_list = []
        for doc in hit_doc_list:
            chunk_list = controller.get_doc_chunk_list_by_doc_ids(doc['document_id'], doc['chunk_ids'])
            return_doc_list.append({'doc_id': doc['document_id'], 'doc_name': doc['doc_name'], 'chunk_list': chunk_list})

        data = {
            'content': content_info['content'],
            'hit_doc_list': return_doc_list or [],
            'hit_image_list': final_images_list or [],
            'graph_content_list': content_info.get('graph_list') or [],
            'prompt': global_search_prompt or app_info['prompt'],
        }
        return json_response(data=data)


class GTResultView(Resource):
    @login_check
    def get(self):
        parser = reqparse.RequestParser()
        parser.add_argument('api_key', type=str, location='args', help='api_key', required=True)
        parser.add_argument('content', type=str, required=True, location='args')
        parser.add_argument('is_chat', type=int, required=False, location='args')
        args = parser.parse_args()
        ret = AppController().gt_chat_bing(args['api_key'], args['content'], args['is_chat'])
        return json_response(data=ret)

class GTApp(Resource):
    @login_check
    def get(self):
        parser = reqparse.RequestParser()
        parser.add_argument('gtid', type=str, location='args', help='gtid', required=True)
        args = parser.parse_args()
        all_user_auths = SchemeController().get_user_auths(args['gtid'])
        all_user_auth_ids = [x['scheme_id'] for x in all_user_auths]
        controller = AppController()
        ret = controller.get_app_list({'all_user_auth_ids': all_user_auth_ids})
        return json_response(data=ret)

class AppType(Enum):
    App = 1
    Entry = 2

    @staticmethod
    def _get_app_type(value) -> 'AppType':
        try:
            return AppType(int(value))  # 尝试将值转换为整数
        except (ValueError, TypeError):
            return None  # 返回 None 表示无效的 AppType

    @staticmethod
    def is_entry_app(app_type):
        return AppType._get_app_type(app_type) == AppType.Entry

    @staticmethod
    def is_normal_app(app_type):
        return AppType._get_app_type(app_type) == AppType.App
    
class GTAppView(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('gtid', type=str, required=False, location='args')
        parser.add_argument('scope', type=int,required=False, default = 0, location='args')
        parser.add_argument('page_no', type=int, default=1, location='args')
        parser.add_argument('page_size', type=int, default=100, location='args')
        tp_user_id = request.user['tp_user_id']
        args = parser.parse_args()
        if args['gtid']:
            all_user_auths = SchemeController().get_user_auths(args['gtid'])
            all_scheme_ids = [x['scheme_id'] for x in all_user_auths]
            if not all_scheme_ids:
                return json_response(data=[])
        else:
            all_scheme_ids = None
        args['all_scheme_ids'] = all_scheme_ids
        args['tp_user_id'] = tp_user_id
        data = AppController().get_app_list_by_auth(args)
        return json_response(data=data)

class AppTreeView(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('parent_directory_id', type=str, required=False, location='args')
        parser.add_argument('page_no', type=int, default=1, location='args')
        parser.add_argument('page_size', type=int, default=10, location='args')
        parser.add_argument('directory_name', type=str, required=False, location='args')
        parser.add_argument('directory_id', type=str, required=False, location='args')
        parser.add_argument('app_name', type=str, required=False, location='args')
        args = parser.parse_args()
        data = AppController().get_app_tree(args)
        return json_response(data=data)
    
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('directory_description', type=str, required=False, location='json')
        parser.add_argument('directory_name', type=str, required=True, location='json')
        parser.add_argument('directory_id', type=str, required=False, location='json')
        parser.add_argument('parent_directory_id', type=str, required=False, location='json')
        args = parser.parse_args()
        tp_user_id = request.user['tp_user_id']
        args['tp_user_id'] = tp_user_id
        data = AppController().add_app_tree(args)
        return json_response(data=data)
    
    @staticmethod
    @login_check
    def delete():
        parser = reqparse.RequestParser()
        parser.add_argument('directory_id', type=str, required=True, location='json')
        args = parser.parse_args()
        data = AppController().delete_app_tree(args)
        return json_response(data=data)


class GraphMode(Enum):
    DEFAULT = 0
    GRAPH = 1

class KnowledgePreprocessing(Resource):
    @staticmethod
    @login_check
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('app_id', type=int, location='args', help='应用ID', required=False)
        parser.add_argument('knowledge_id', type=int, location='args', help='应用ID', required=True)
        args = parser.parse_args()

        STATISTICS_ID = -1
        knowledge_id = args['knowledge_id']
        knowledge_controller = KnowledgeController(g.corpid)

        individual_val = knowledge_controller.get_knowledge_individual(knowledge_id)
        statistics_val = knowledge_controller.get_knowledge_individual(STATISTICS_ID)

        recall_size = getattr(individual_val, "recall_size", 12) if individual_val else 12
        recall_threshold = getattr(individual_val, "recall_threshold", 1.4) if individual_val else 1.4

        view_dict = {"avg_sentence_length": "句长均值","vocabulary_size": "词表量级","keyword_density": "关键词集中度","noun_ratio": "名词比率","diversity_score": "多样性值","text_complexity_score": "句法复杂度","avg_paragraphs_length": "段长均值","avg_cosine_similarity": "语言相近度"}

        def handle_view(view, entry):
            result_dict = {}
            if entry is None:
                for key, value in view.items():
                    result_dict[value] = None
            else:
                for key, value in view.items():
                    result_dict[value] = getattr(entry, key, None)
            return result_dict

        data = {
            "individual_val": handle_view(view_dict, individual_val),
            "statistics_val": handle_view(view_dict, statistics_val),
            "recall_size": recall_size,
            "recall_threshold": recall_threshold
        }
        return json_response(data={'data': data})


    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('is_async', type=int, location='args', help='应用ID', required=False)
        parser.add_argument('knowledge_id', type=int, location='args', help='应用ID', required=True)
        args = parser.parse_args()
        knowledge_id = args['knowledge_id']
        is_async = args['is_async']

        if is_async and is_async == 1:
            AppController().individual_app_work(g.corpid, 1, [knowledge_id])
        # else:
        #     loop = asyncio.new_event_loop()
        #     asyncio.set_event_loop(loop)
        #     try:
        #         loop.run_until_complete(
        #             KnowledgeController(g.corpid).handle_analysis(knowledge_id, AppController().ai_app_chat)
        #         )
        #     finally:
        #         loop.close()
        else:
            task.handle_individual_knowledge(g.corpid, knowledge_id)
        return json_response(data={'message': 'Analysis started.'})