# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: base
# @Author: <PERSON><PERSON>
# @E-mail: sun<PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2025/2/20
# ---
from abc import ABC, abstractmethod
from typing import Dict, Any, List
from config import Config


class BaseJob(ABC):
    def __init__(self, config: Config):
        self.config = config

    @abstractmethod
    def classify_content(self, input_data: Any) -> Dict[str, float]:
        """Return probability distribution over content types"""
        pass