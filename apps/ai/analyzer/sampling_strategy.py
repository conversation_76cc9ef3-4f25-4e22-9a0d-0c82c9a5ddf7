# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: sampling_strategy
# @Author: <PERSON><PERSON>
# @E-mail: <PERSON><PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2025/3/10
# ---
from typing import Callable, List
from dataclasses import dataclass, field

@dataclass
class SamplingFunc:
    max_samples_limit: int
    description: str
    func: Callable[[int, int], List[int]]

    def __call__(self, total_length: int, max_samples: int) -> List[int]:
        return self.func(total_length, max_samples)


def evenly_distributed_sampling(total_length: int, max_samples: int) -> List[int]:
    if total_length <= max_samples:
        return list(range(total_length))

    step = total_length / max_samples
    indices = [int(i * step + step / 2) for i in range(max_samples)]

    if indices[-1] >= total_length:
        indices[-1] = total_length - 1
    return indices


class SamplingStrategy:

    EVEN_SAMPLING: SamplingFunc = SamplingFunc(
        max_samples_limit=1000,
        description="Uniform Distribution Sampling Strategy",
        func=evenly_distributed_sampling
    )