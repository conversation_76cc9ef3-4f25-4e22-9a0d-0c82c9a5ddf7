-- jusure_pro_2.aigc_knowledge_individual definition

CREATE TABLE `aigc_knowledge_individual` (
  `id` bigint NOT NULL,
  `knowledge_id` bigint NOT NULL COMMENT '知识库ID',
  `knowledge_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '知识库名称',
  `chunk_size` int NOT NULL COMMENT '分片大小',
  `recall_size` int NOT NULL COMMENT '召回数量',
  `strategy` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '知识库策略',
  `total_chars` int DEFAULT NULL,
  `total_documents` int DEFAULT NULL,
  `avg_doc_length` float DEFAULT NULL,
  `avg_sentence_length` float DEFAULT NULL,
  `vocabulary_size` int DEFAULT NULL,
  `keyword_density` float DEFAULT NULL,
  `noun_ratio` float DEFAULT NULL,
  `avg_cosine_similarity` float DEFAULT NULL,
  `diversity_score` float DEFAULT NULL,
  `text_complexity_score` float DEFAULT NULL,
  `classify_scores` longtext COLLATE utf8mb4_general_ci,
  `cluster_info` longtext COLLATE utf8mb4_general_ci,
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delete_flag` tinyint(1) DEFAULT '0' COMMENT '0:未删除 1：已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;