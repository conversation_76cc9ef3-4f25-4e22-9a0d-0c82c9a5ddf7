# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: meta_info
# @Author: sunhao
# @E-mail: sun<PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2025/2/20
# ---
import json
from typing import Dict, List, Any
from lib_func.logger import logger
from apps.ai.analyzer.config import PROMPT_CONTENT, TOKEN_LIMIT, AIGC_TYPE_ID, MODEL_PATH, ENABLE_CLASSIFY


class MetaInfoProcessor():
    def process_filename(self, filename: str) -> Dict[str, float]:
        pass

    def process_labels(self, labels: List[str]) -> Dict[str, float]:
        pass

    def process_abstract(self, abstract: str) -> Dict[str, float]:
        pass

    def classify_content(self, input_data: Any) -> Dict[str, float]:
        pass

    @staticmethod
    def gen_template(data_list):
        transformed_data = [
            {
                "document_id": item["document_id"],
                "doc_name": item["doc_name"],
                "abstract": item["abstract"]
            }
            for item in data_list
        ]
        transformed_data_str = str(transformed_data)
        if len(transformed_data_str) > TOKEN_LIMIT: # fixme
            transformed_data = [
                {
                    "document_id": item["document_id"],
                    "doc_name": item["doc_name"],
                }
                for item in data_list
            ]
        template = f"{PROMPT_CONTENT.get('CLASSIFY_TYPE')}{transformed_data}"
        return template

    def handle_meta_inner(self, data_list, ai_func):
        value = MetaInfoProcessor.gen_template(data_list)
        data = {}
        try:
            for item in ai_func(value, [], AIGC_TYPE_ID, MODEL_PATH, is_stream=False):
                data = json.dumps(
                    {'content': item or '', 'is_send': False,
                     'role': 'assistant', 'type': 'message'}, ensure_ascii=False)
                yield f"data: {data}\n\n"
        except Exception as e:
            error_message = f"Error during ai_func execution: {str(e)}"
            logger.error(error_message)
            yield f"data: {json.dumps({'content': error_message, 'is_send': False, 'role': 'assistant', 'type': 'message'}, ensure_ascii=False)}\n\n"
        return {}

    @staticmethod
    def parse_llm_response(response):
        try:
            if isinstance(response, dict):
                content = response.get('content', '')
            else:
                content = str(response)

            result = json.loads(content)
            if not isinstance(result, list):
                return []

            return result

        except (json.JSONDecodeError, AttributeError, TypeError):
            return []

    def handle_meta(self, data_list, ai_func):
        meta_info = {}
        if ENABLE_CLASSIFY:
            for result in MetaInfoProcessor().handle_meta_inner(data_list, ai_func):
                result_data = json.loads(result.replace('data: ', ''))
                if result_data.get('content'):
                    meta_info = result_data
            result = MetaInfoProcessor.parse_llm_response(meta_info)
        else:
            result = {}
        return result

