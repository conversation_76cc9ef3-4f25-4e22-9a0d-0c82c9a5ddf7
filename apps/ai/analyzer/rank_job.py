# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: rank_job
# @Author: <PERSON><PERSON>
# @E-mail: sun<PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2025/2/20
# ---
import numpy as np
from sklearn.cluster import KMeans
import os
import json
from typing import Dict, List, Tuple, Any
import random
import re
import jieba
import jieba.posseg as pseg
from jieba.analyse import extract_tags
from sklearn.decomposition import PCA
from sklearn.neighbors import KernelDensity
from sklearn.preprocessing import StandardScaler

from lib_func.logger import logger
from apps.ai.analyzer.stateless_density_analyzer import StatelessDensityAnalyzer
from apps.ai.analyzer.strategy_recommender import MLStrategyRecommender


class RAGParamRecommender:

    def __init__(self, base_data_path: str = "./data", sampling_rate: float = 0.2, max_texts: int = 100):
        self.base_data_path = base_data_path
        self.sampling_rate = sampling_rate
        self.max_texts = max_texts

        os.makedirs(os.path.join(base_data_path, "recommendations"), exist_ok=True)

        self.chunk_size_options = [128, 256, 512, 768, 1024, 1536, 2048]
        self.top_k_options = [3, 5, 7, 10, 15, 20]

        self.strategy_mapping = {
            "dense_technical": {"chunk_size": 768, "top_k": 7, "threshold": 1.4},  # 密集专业知识
            "narrative_content": {"chunk_size": 1024, "top_k": 5, "threshold": 1.4},  # 叙述性内容
            "mixed_general": {"chunk_size": 512, "top_k": 10, "threshold": 1.4},  # 混合通用内容
            "large_diverse": {"chunk_size": 1536, "top_k": 8, "threshold": 1.4},  # 大型多样内容
            "concise_factual": {"chunk_size": 512, "top_k": 5, "threshold": 1.4}  # 简明事实内容
        }

    def analyze_user_knowledge_base(self, knowledge_id: str,
                                    texts: List[str],
                                    filenames: List[str],
                                    embeddings: List[List[float]]) -> Dict[str, Any]:

        logger.info(f"分析用户 {knowledge_id} 的知识库特征 (总文本数: {len(texts)})")

        total_chars = sum(len(text) for text in texts)
        total_documents = len(filenames)
        avg_doc_length = total_chars / total_documents if total_documents > 0 else 0

        sample_texts, sample_embeddings = self._sample_data(texts, embeddings)
        logger.info(f"{knowledge_id} after sampling: {len(sample_texts)}")

        text_complexity = self._analyze_text_complexity_with_jieba(sample_texts)
        vocabulary_analysis = self._analyze_vocabulary(sample_texts)

        if sample_embeddings:
            embeddings_array = np.array(sample_embeddings)
            avg_cosine_similarity = self._calculate_avg_cosine_similarity(embeddings_array)
            n_clusters = min(5, len(sample_embeddings))
            diversity_score = self._analyze_content_diversity(embeddings_array, n_clusters)
            estimate_density = self.estimate_density(embeddings_array)


        else:
            avg_cosine_similarity = 0
            diversity_score = 0
            estimate_density = 0

        features = {
            "total_chars": total_chars,
            "total_documents": total_documents,
            "avg_doc_length": avg_doc_length,
            "avg_sentence_length": text_complexity["avg_sentence_length"],
            "avg_paragraphs_length": text_complexity["avg_paragraphs_length"],
            "avg_paragraphs": text_complexity["avg_paragraphs"],
            "vocabulary_size": vocabulary_analysis["unique_words"],
            "keyword_density": vocabulary_analysis["keyword_density"],
            "noun_ratio": text_complexity["noun_ratio"],
            "avg_cosine_similarity": avg_cosine_similarity,
            "diversity_score": diversity_score,
            "text_complexity_score": text_complexity["complexity_score"],
            "estimate_density": estimate_density
        }

        return features

    def _sample_data(self, texts: List[str], embeddings: List[List[float]]) -> Tuple[List[str], List[List[float]]]:
        if len(texts) != len(embeddings):
            logger.info(f"texts length:({len(texts)}) not match embeddings length: ({len(embeddings)})")
            min_len = min(len(texts), len(embeddings))
            texts = texts[:min_len]
            embeddings = embeddings[:min_len]

        if len(texts) <= self.max_texts:
            return texts, embeddings

        sample_size = min(self.max_texts, max(int(len(texts) * self.sampling_rate), 20))
        indices = sorted(random.sample(range(len(texts)), sample_size))

        return [texts[i] for i in indices], [embeddings[i] for i in indices]

    def _analyze_text_complexity_with_jieba(self, texts: List[str]) -> Dict[str, float]:
        max_sentences = 1000
        all_sentences = []
        noun_count = 0
        total_words = 0

        avg_paragraphs, avg_paragraphs_length = self._analyze_paragraphs(texts)
        for text in texts:
            sentences = self._split_sentences(text)
            all_sentences.extend(
                sentences[:max_sentences - len(all_sentences) if len(all_sentences) < max_sentences else 0])

            words_with_pos = list(pseg.cut(text[:10000]))  # 限制处理长度，提高效率
            total_words += len(words_with_pos)

            noun_count += sum(1 for word, flag in words_with_pos if flag.startswith('n'))

            if len(all_sentences) >= max_sentences:
                break

        if all_sentences:
            sentence_lengths = [len(list(jieba.cut(sentence))) for sentence in all_sentences]
            avg_sentence_length = sum(sentence_lengths) / len(sentence_lengths)
        else:
            avg_sentence_length = 0

        noun_ratio = noun_count / total_words if total_words > 0 else 0
        complexity_score = (avg_sentence_length * 0.6) + (noun_ratio * 10)

        return {
            "avg_sentence_length": avg_sentence_length,
            "avg_paragraphs_length": avg_paragraphs_length,
            "avg_paragraphs":avg_paragraphs,
            "noun_ratio": noun_ratio,
            "complexity_score": complexity_score
        }

    def _split_sentences(self, text: str) -> List[str]:
        text = re.sub(r'([。！？\?!])+', r'\1\n', text)
        sentences = text.split('\n')
        return [s.strip() for s in sentences if s.strip()]

    def split_paragraphs(self, text):
        return [p.strip() for p in text.split('\n') if p.strip()]

    def _analyze_paragraphs(self, chunks):
        total_paragraphs = 0
        total_chars = 0

        for chunk in chunks:
            paragraphs = self.split_paragraphs(chunk)
            total_paragraphs += len(paragraphs)
            total_chars += sum(len(p) for p in paragraphs)

        avg_paragraphs_per_chunk = total_paragraphs / len(chunks) if len(chunks) > 0 else 0
        avg_chars_per_paragraph = total_chars / total_paragraphs if total_paragraphs > 0 else 0

        return avg_paragraphs_per_chunk, avg_chars_per_paragraph



    def _analyze_vocabulary(self, texts: List[str]) -> Dict[str, Any]:
        text_sample = "。".join(texts)

        max_text_length = 100000
        if len(text_sample) > max_text_length:
            text_sample = text_sample[:max_text_length]

        words = list(jieba.cut(text_sample))

        unique_words = len(set(words))

        try:
            keywords = extract_tags(text_sample, topK=20, withWeight=True)
            keyword_density = sum(weight for _, weight in keywords) / len(keywords) if keywords else 0
        except:
            keyword_density = 0

        return {
            "unique_words": unique_words,
            "keyword_density": keyword_density
        }

    def _calculate_avg_cosine_similarity(self, embeddings: np.ndarray) -> float:
        # sample in case
        max_vectors = 200
        if len(embeddings) > max_vectors:
            indices = sorted(random.sample(range(len(embeddings)), max_vectors))
            embeddings = embeddings[indices]

        norms = np.linalg.norm(embeddings, axis=1, keepdims=True)
        norms[norms == 0] = 1
        normalized_embeddings = embeddings / norms

        similarity_matrix = np.dot(normalized_embeddings, normalized_embeddings.T)

        n = similarity_matrix.shape[0]
        similarity_sum = (np.sum(similarity_matrix) - n) / (n * n - n) if n > 1 else 0

        return similarity_sum


    def estimate_density(self, embeddings: np.ndarray) -> float:
        return StatelessDensityAnalyzer().estimate_density(embeddings)


    def estimate_density_ori(self, embeddings: np.ndarray) -> float:
        pca = PCA(n_components=2)
        embeddings_lowdim = pca.fit_transform(embeddings)

        scaler = StandardScaler()
        embeddings_scaled = scaler.fit_transform(embeddings_lowdim)

        n_samples, n_features = embeddings_scaled.shape
        silverman_factor = np.power(n_samples * (n_features + 4) / 4., -1. / (n_features + 4))
        bandwidth = silverman_factor * np.std(embeddings_scaled, axis=0).mean()  # 特征间平均标准差加权

        kde = KernelDensity(kernel='gaussian', bandwidth=bandwidth)
        kde.fit(embeddings_scaled)
        log_density = kde.score_samples(embeddings_scaled)  # 使用对数密度保证数值稳定
        density_scores = np.exp(log_density)  # 转换为概率密度

        global_density = density_scores.mean()
        density_std = density_scores.std()
        dynamic_threshold = np.quantile(density_scores, 0.85)
        high_density_ratio = (density_scores > dynamic_threshold).mean()
        cohesion = 1 - (density_std / global_density) if global_density > 0 else 0  # 密度凝聚度
        semantic_density = 0.6 * global_density + 0.3 * cohesion + 0.1 * high_density_ratio
        max_theoretical = density_scores.max()  # 理论最大密度值
        normalized_density = semantic_density / max_theoretical  # 归一化到[0,1]范围

        logger.info(f"[Optimization Report] Bandwidth automatically set to: {bandwidth:.4f}")
        logger.info(f"Global benchmark density: {global_density:.4f} (original average value)")
        logger.info(f"Density cohesion coefficient: {cohesion:.4f} (closer to 1 indicates more concentrated distribution)")
        logger.info(f"Dynamic high-density area ratio: {high_density_ratio:.4f} (threshold={dynamic_threshold:.4f})")
        logger.info(f"Comprehensive semantic density: {normalized_density:.4f} (normalized value)")

        return normalized_density



    def _analyze_content_diversity(self, embeddings: np.ndarray, n_clusters: int) -> float:
        if len(embeddings) < n_clusters:
            return 0

        max_vectors = 500
        if len(embeddings) > max_vectors:
            indices = sorted(random.sample(range(len(embeddings)), max_vectors))
            embeddings = embeddings[indices]

        try:
            kmeans = KMeans(
                n_clusters=n_clusters,
                random_state=42,
                n_init=5,
                max_iter=100,
                algorithm='elkan'
            )
            kmeans.fit(embeddings)

            centers = kmeans.cluster_centers_
            center_distances = []

            for i in range(n_clusters):
                for j in range(i + 1, n_clusters):
                    dist = np.linalg.norm(centers[i] - centers[j])
                    center_distances.append(dist)

            return np.mean(center_distances) if center_distances else 0
        except Exception as e:
            logger.error(f"analyze_content_diversity fail: {str(e)}")
            return 0

    def determine_strategy_local(self, features: Dict[str, Any]) -> str:
        # fine tune here
        if features["text_complexity_score"] > 15 and features["noun_ratio"] > 0.3:
            return "dense_technical"
        elif features["diversity_score"] > 0.7 and features["total_chars"] > 50000:
            return "large_diverse"
        elif features["avg_sentence_length"] > 20 and features["keyword_density"] < 0.2:
            return "narrative_content"
        elif features["vocabulary_size"] < 500 and features["avg_sentence_length"] < 15:
            return "concise_factual"
        else:
            return "mixed_general"

    def determine_strategy(self, features: Dict[str, Any]) -> str:
        result_val = MLStrategyRecommender().predict_example(features)
        return result_val

    def recommend_parameters(self, knowledge_id: str,
                             texts: List[str],
                             filenames: List[str],
                             embeddings: List[List[float]]) -> Dict[str, Any]:
        features = self.analyze_user_knowledge_base(knowledge_id, texts, filenames, embeddings)
        result_val = self.determine_strategy(features)

        if features["total_chars"] > 80000:
            result_val["recommended_top_k"] += 2
        elif features["total_chars"] < 20000:
            result_val["top_k"] = max(5, result_val["recommended_top_k"] - 2)

        result = {
            "knowledge_id": knowledge_id,
            "recommended_chunk_size": result_val["recommended_chunk_size"],
            "recommended_top_k": result_val["recommended_top_k"],
            "recommended_threshold": result_val["recommended_threshold"],
            "strategy": result_val["closest_strategy"],
            "features": features
        }
        return result

    def save_recommendation(self, recommendation: Dict[str, Any], local_mode = False) -> None:
        if local_mode:
            user_id = recommendation["user_id"]
            file_path = os.path.join(self.base_data_path, "recommendations", f"{user_id}.json")

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(recommendation, f, ensure_ascii=False, indent=2)
        else:
            logger.info("to be done")

    def process_all_data(self, knowledge_data: Dict[str, Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        results = {}
        total_users = len(knowledge_data)

        for i, (knowledge_id, data) in enumerate(knowledge_data.items()):
            try:
                logger.info(f"process data: {knowledge_id} ({i + 1}/{total_users})")

                recommendation = self.recommend_parameters(
                    knowledge_id=knowledge_id,
                    texts=data["texts"],
                    filenames=data["filenames"],
                    embeddings=data["embeddings"]
                )

                # self.save_recommendation(recommendation)
                results[knowledge_id] = recommendation

                logger.info(f"knowledge_id: {knowledge_id} process done:"
                      f" recommended_chunk_size={recommendation['recommended_chunk_size']}, "
                      f"recommended_top_k={recommendation['recommended_top_k']}")

            except Exception as e:
                logger.error(f"processing {knowledge_id} meets error: {str(e)}")
                results[knowledge_id] = {
                    "recommended_chunk_size": 512,
                    "recommended_top_k": 5,
                    "strategy": "default",
                    "features": None
                }

            if i % 10 == 0:
                import gc
                gc.collect()

        return results


def integrate_with_existing_data(vector_data_path, document_data_path, batch_size=10):
    recommender = RAGParamRecommender(
        sampling_rate=0.1,
        max_texts=100
    )

    all_results = {}

    logger.info(f"Loading vector data from {vector_data_path}")
    with open(vector_data_path, 'r') as f:
        vector_data = json.load(f)

    logger.info(f"Loading document data from {document_data_path}")
    with open(document_data_path, 'r') as f:
        document_data = json.load(f)

    user_ids = list(set(document_data.keys()).intersection(set(vector_data.keys())))
    logger.info(f"Found {len(user_ids)} user data entries")

    for i in range(0, len(user_ids), batch_size):
        batch_user_ids = user_ids[i:i + batch_size]
        logger.info(
            f"Processing user batch {i // batch_size + 1}/{(len(user_ids) - 1) // batch_size + 1} ({len(batch_user_ids)} users)")

        batch_user_data = {}

        for user_id in batch_user_ids:
            texts = []
            filenames = []
            embeddings = []

            for doc_id, doc_info in document_data[user_id].items():
                if doc_id in vector_data[user_id]:
                    texts.append(doc_info["text"])
                    filenames.append(doc_info["filename"])
                    embeddings.append(vector_data[user_id][doc_id])

            batch_user_data[user_id] = {
                "texts": texts,
                "filenames": filenames,
                "embeddings": embeddings
            }

        batch_results = recommender.process_all_data(batch_user_data)
        all_results.update(batch_results)

        del batch_user_data
        import gc
        gc.collect()

    output_path = "rag_recommendations.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)


class RankJob:
    def __init__(self):
        pass

    def rank_job(self, input_data: Any) -> Dict[str, float]:
        # Implementation for local distance calculation
        pass


    def process(self, knowledge_data: Dict[str, Any]):
        # integrate_with_existing_data("vector_data.json", "document_data.json", batch_size=10)
        recommender = RAGParamRecommender(sampling_rate=0.1, max_texts=50)
        results = recommender.process_all_data(knowledge_data)
        logger.info(json.dumps(results, indent=2, ensure_ascii=False))
        return results
