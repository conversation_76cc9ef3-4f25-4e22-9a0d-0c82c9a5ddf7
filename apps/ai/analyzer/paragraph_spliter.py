# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: paragraph_spliter
# @Author: sun<PERSON>
# @E-mail: <EMAIL>
# @Site: 
# @Time: 2025/3/5
# ---
import os
import jieba

import os
import jieba


def split_paragraphs(text):
    return [p.strip() for p in text.split('\n') if p.strip()]


def analyze_chunks(chunks):
    total_paragraphs = 0
    total_chars = 0

    for chunk in chunks:
        paragraphs = split_paragraphs(chunk)
        total_paragraphs += len(paragraphs)
        total_chars += sum(len(p) for p in paragraphs)

    avg_paragraphs_per_chunk = total_paragraphs / len(chunks) if len(chunks) > 0 else 0
    avg_chars_per_paragraph = total_chars / total_paragraphs if total_paragraphs > 0 else 0

    return avg_paragraphs_per_chunk, avg_chars_per_paragraph


def split_text_by_length(text, chunk_size=1000):
    return [text[i:i + chunk_size] for i in range(0, len(text), chunk_size)]


def classify_document(file_path, para_count_threshold=5, para_length_threshold=200):
    with open(file_path, 'r', encoding='utf-8') as f:
        text = f.read()

    chunks = split_text_by_length(text, 1000)
    avg_paragraphs, avg_length = analyze_chunks(chunks)

    if avg_paragraphs >= para_count_threshold and avg_length <= para_length_threshold:
        return 1
    else:
        return 0
