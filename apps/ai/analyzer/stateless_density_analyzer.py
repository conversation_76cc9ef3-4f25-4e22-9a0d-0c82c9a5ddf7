# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: stateless_density_analyzer
# @Author: <PERSON><PERSON>
# @E-mail: sun<PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2025/3/4
# ---
import numpy as np
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.neighbors import KernelDensity


class StatelessDensityAnalyzer:
    def __init__(self,
                 pca_components=2,
                 ref_bandwidth=0.5,
                 ref_dim=2,
                 z_threshold=0.7):
        """
        参数：
        pca_components : int (默认2) 固定PCA降维维度
        ref_bandwidth : float (默认0.5) 参考带宽（用于理论最大值计算）
        ref_dim : int (默认2) 参考维度（需与pca_components一致）
        z_threshold : float (默认0.7) 判断阈值（基于归一化后的0-1范围）
        """
        # Fixed parameter system
        self.pca_components = pca_components
        self.ref_bandwidth = ref_bandwidth
        self.ref_dim = ref_dim

        # Pre-calculate theoretical maximum
        self.theoretical_max = self._calc_theoretical_max()
        self.z_threshold = z_threshold

    def _calc_theoretical_max(self) -> float:
        """Calculate theoretical maximum density value"""
        # Theoretical maximum density of Gaussian kernel: 1/(h*sqrt(2π))^d
        h = self.ref_bandwidth
        d = self.ref_dim
        max_density = 1 / (h * np.sqrt(2 * np.pi)) ** d
        return max_density

    def estimate_density(self, embeddings: np.ndarray) -> float:
        """
        Core calculation method (stateless)
        Returns:
        normalized_score : float
            Standardized semantic density score (range 0-1)
        """
        # 1. Dimensionality reduction
        pca = PCA(n_components=self.pca_components)
        low_dim = pca.fit_transform(embeddings)

        # 2. Standardization
        scaler = StandardScaler()
        scaled = scaler.fit_transform(low_dim)

        # 3. Fixed bandwidth calculation
        h = self._silverman_bandwidth(scaled)

        # 4. Density estimation
        kde = KernelDensity(bandwidth=h, kernel='gaussian')
        kde.fit(scaled)
        log_density = kde.score_samples(scaled)
        density = np.exp(log_density)

        # 5. Basic metrics
        base_density = density.mean()
        density_std = density.std()
        cohesion = 1 - (density_std / base_density) if base_density > 0 else 0

        # 6. High-density region detection
        threshold = np.quantile(density, 0.85)
        high_ratio = (density > threshold).mean()

        # 7. Theoretical normalization
        raw_score = 0.5 * base_density + 0.3 * cohesion + 0.2 * high_ratio
        normalized_score = raw_score / self.theoretical_max

        # 8. Final clipping
        return np.clip(normalized_score, 0.0, 1.0)

    def _silverman_bandwidth(self, data: np.ndarray) -> float:
        """Silverman bandwidth rule (stateless version)"""
        n = data.shape[0]
        d = data.shape[1]
        return (n * (d + 4) / 4.) ** (-1. / (d + 4)) * np.std(data, axis=0).mean()

    def get_strategy(self, score: float) -> str:
        """Strategy decision"""
        return "deep_rag" if score >= self.z_threshold else "basic_rag"

    @staticmethod
    def validate_input(embeddings: np.ndarray):
        """Input validation"""
        if embeddings.shape[1] < 2:
            raise ValueError("Input dimensions must be greater than 2")
        if len(embeddings) < 10:
            raise Warning("Small sample size may affect calculation accuracy")