# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: config
# @Author: sun<PERSON>
# @E-mail: sun<PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2025/5/20
# ---
import os
import yaml
from typing import List, Dict, Optional
from lib_func.logger import logger


ENABLE_ENSEMBLE = True

class Config:
    DEFAULT_CONFIG_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.yaml")
    def __init__(self, config_path: str = DEFAULT_CONFIG_PATH):
        try:
            with open(config_path, 'r') as f:
                self.config = yaml.safe_load(f)
                if not self.config:
                    raise ValueError("Config file is empty or not properly formatted.")
        except Exception as e:
            logger.exception(f"Error loading config file: {e}")
            raise

    @property
    def dataset_categories(self) -> List[Dict[str, str]]:
        return self.config.get('DATASET_CATEGORIES', [])

MANUAL_EXPECTED_SIZES: list[float] = [32.0, 300.0, 700.0]
MANUAL_AVG_PARAM_B: float = 70.0