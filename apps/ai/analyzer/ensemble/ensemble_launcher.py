# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: ensamble_launcher
# @Author: <PERSON><PERSON>
# @E-mail: sun<PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2025/5/23
# ---
from apps.ai.analyzer.ensemble.input.generator import InputGenerator
from apps.ai.analyzer.ensemble.selector_agent import SelectorAgent


class EnsembleLauncher:
    def __init__(self, get_ebd_func, es):
        self.default_local_emb_model_path = 'bge-large-zh-v1.5'
        self.get_ebd_func = get_ebd_func
        self.es = es
        pass

    def launch(self, model_list, text_content:str):
        scores = InputGenerator(self.get_ebd_func, self.es).gen_score(text_content)
        selector_auto = SelectorAgent(auto_mode=True)
        selected_model = selector_auto.select_model(scores, model_list)
        return selected_model