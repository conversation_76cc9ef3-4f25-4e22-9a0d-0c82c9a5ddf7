# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: advisor
# @Author: sun<PERSON>
# @E-mail: sun<PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2025/5/22
# ---
import math

from apps.ai.analyzer.ensemble.config import MANUAL_EXPECTED_SIZES, MANUAL_AVG_PARAM_B


class SelectorAgent:
    def __init__(self, size_weight=1.0, preference_weight=1.0, expected_sizes=None, auto_mode=True):
        self.size_weight = max(0.0, size_weight) if isinstance(size_weight, (int, float)) else 1.0
        self.preference_weight = max(0.0, preference_weight) if isinstance(preference_weight, (int, float)) else 1.0
        self.auto_mode = auto_mode if isinstance(auto_mode, bool) else True

        if self.auto_mode:
            self.expected_sizes = None
        else:
            if expected_sizes is not None and isinstance(expected_sizes, (list, tuple)) and len(expected_sizes) >= 3:
                validated_sizes = []
                for size in expected_sizes[:3]:
                    if isinstance(size, (int, float)) and size > 0:
                        validated_sizes.append(float(size))
                    else:
                        validated_sizes.append(70.0)
                self.expected_sizes = validated_sizes
            else:
                self.expected_sizes = MANUAL_EXPECTED_SIZES

    def _validate_param_size_b(self, param_size_b):
        if param_size_b is None or not isinstance(param_size_b, (int, float)) or param_size_b <= 0:
            return MANUAL_AVG_PARAM_B
        return float(param_size_b)

    def _validate_param_preference(self, param_preference):
        if not isinstance(param_preference, str) or not param_preference.strip():
            return "general"

        try:
            preferences = [pref.strip().lower() for pref in param_preference.split(',') if pref.strip()]
            if not preferences:
                return "general"
            return ','.join(preferences)
        except Exception:
            return "general"

    def _validate_param_human_rating(self, param_human_rating):
        if not isinstance(param_human_rating, (int, float)):
            return 5.0

        rating = float(param_human_rating)
        if rating < 0 or rating > 10:
            return 5.0
        return rating

    def _validate_model(self, model):
        if not isinstance(model, dict):
            return None

        validated_model = {}

        validated_model['model_name'] = str(model.get('model_name', 'Unknown Model'))
        validated_model['aigc_model_id'] = str(model.get('aigc_model_id', 'unknown'))
        validated_model['param_size_b'] = self._validate_param_size_b(model.get('param_size_b'))
        validated_model['param_preference'] = self._validate_param_preference(model.get('param_preference'))
        validated_model['param_human_rating'] = self._validate_param_human_rating(model.get('param_human_rating'))

        return validated_model

    def _validate_model_list(self, model_list):
        if not isinstance(model_list, (list, tuple)) or not model_list:
            raise ValueError("模型列表为空或格式不正确")

        validated_models = []
        for model in model_list:
            validated_model = self._validate_model(model)
            if validated_model:
                validated_models.append(validated_model)

        if not validated_models:
            raise ValueError("没有有效的模型")

        return validated_models

    def _validate_problem_params(self, problem_params):
        if not isinstance(problem_params, dict):
            return {"score": 50.0, "result": {"general": 1.0}}

        score = problem_params.get('score', 50.0)
        if not isinstance(score, (int, float)) or score < 0 or score > 100:
            score = 50.0

        result = problem_params.get('result', {})
        if not isinstance(result, dict) or not result:
            result = {"general": 1.0}
        else:
            cleaned_result = {}
            for key, value in result.items():
                if isinstance(key, str) and key.strip() and isinstance(value, (int, float)) and value >= 0:
                    cleaned_result[key.strip().lower()] = float(value)

            if not cleaned_result:
                result = {"general": 1.0}
            else:
                result = cleaned_result

        return {"score": float(score), "result": result}

    def _get_fallback_model(self, model_list):
        try:
            if not model_list:
                return None

            best_model = None
            max_size = -1

            for model in model_list:
                if isinstance(model, dict):
                    size_b = model.get('param_size_b', 0)
                    if isinstance(size_b, (int, float)) and size_b > max_size:
                        max_size = size_b
                        best_model = model

            return best_model
        except Exception:
            return None

    def _calculate_auto_expected_sizes(self, model_list):
        try:
            if not model_list:
                return MANUAL_EXPECTED_SIZES

            sizes = [model["param_size_b"] for model in model_list if model.get("param_size_b", 0) > 0]
            if not sizes:
                return MANUAL_EXPECTED_SIZES

            min_size = float(min(sizes))
            max_size = float(max(sizes))
            avg_size = float(int(round(sum(sizes) / len(sizes))))

            return [min_size, avg_size, max_size]
        except Exception:
            return MANUAL_EXPECTED_SIZES

    def _get_max_category(self, result_dict):
        try:
            if not result_dict:
                return "general"
            return max(result_dict, key=lambda k: result_dict.get(k, 0))
        except Exception:
            return "general"

    def _compute_size_adaptation(self, model_size, problem_score, expected_sizes):
        try:
            model_size = max(0.1, float(model_size))
            problem_score = max(0.0, min(100.0, float(problem_score)))

            if not expected_sizes or len(expected_sizes) < 3:
                expected_sizes = MANUAL_EXPECTED_SIZES

            size_30, size_50, size_80 = [max(0.1, float(s)) for s in expected_sizes[:3]]

            # Linear Interpolation
            if problem_score <= 30:
                expected_size = size_30
            elif problem_score <= 50:
                ratio = (problem_score - 30) / (50 - 30)
                expected_size = size_30 * ((size_50 / size_30) ** ratio)
            elif problem_score <= 80:
                ratio = (problem_score - 50) / (80 - 50)
                expected_size = size_50 * ((size_80 / size_50) ** ratio)
            else:
                expected_size = size_80

            # Calculate the match degree between the current model and the desired scale
            log_model_size = math.log(model_size)
            log_expected_size = math.log(expected_size)
            log_distance = abs(log_model_size - log_expected_size)

            # Compute the fitness using a Gaussian function
            sigma = 0.8
            adaptation_score = 90 * math.exp(-(log_distance ** 2) / (2 * sigma ** 2))

            return max(5.0, min(100.0, adaptation_score))
        except Exception:
            return 50.0

    def _compute_preference_adaptation(self, model_preference, problem_category):
        try:
            if not isinstance(model_preference, str) or not isinstance(problem_category, str):
                return 0.0

            categories = [c.strip().lower() for c in model_preference.split(',') if c.strip()]
            problem_cat_lower = problem_category.lower().strip()

            return 100.0 if problem_cat_lower in categories else 0.0
        except Exception:
            return 0.0

    def _compute_human_rating(self, human_rating):
        # aim to 0.5-1.5
        try:
            rating = float(human_rating)
            rating = max(0.0, min(10.0, rating))
            return 0.5 + (rating / 10.0) * 1.0
        except Exception:
            return 1.0

    def _calculate_model_score(self, model, problem_score, problem_category, expected_sizes):
        try:
            size_adapt = self._compute_size_adaptation(model["param_size_b"], problem_score, expected_sizes)
            pref_adapt = self._compute_preference_adaptation(model["param_preference"], problem_category)
            human_weight = self._compute_human_rating(model["param_human_rating"])

            final_score = (size_adapt * self.size_weight + pref_adapt * self.preference_weight) * human_weight

            # only for debug
            expected_size = self._get_expected_size(problem_score, expected_sizes)
            print(f"Model {model['model_name']} (Size: {model['param_size_b']}B):")
            # print(f"  - Problem Complexity: {problem_score}, Expected Size: {expected_size:.1f}B")
            # print(f"  - Size Adaptation Score: {size_adapt:.2f}")
            # print(f"  - Preference Adaptation Score: {pref_adapt:.2f}")
            # print(f"  - Human Evaluation Weight: {human_weight:.2f}")
            print(f"  - Final Score: {final_score:.2f}")
            print()

            return max(0.0, final_score)
        except Exception as e:
            print(f"计算模型 {model.get('model_name', 'Unknown')} 得分时出错: {str(e)}")
            return 0.0

    def _get_expected_size(self, problem_score, expected_sizes):
        try:
            if not expected_sizes or len(expected_sizes) < 3:
                expected_sizes = MANUAL_EXPECTED_SIZES

            problem_score = max(0.0, min(100.0, float(problem_score)))
            size_30, size_50, size_80 = [max(0.1, float(s)) for s in expected_sizes[:3]]

            if problem_score <= 30:
                return size_30
            elif problem_score <= 50:
                ratio = (problem_score - 30) / (50 - 30)
                return size_30 * ((size_50 / size_30) ** ratio)
            elif problem_score <= 80:
                ratio = (problem_score - 50) / (80 - 50)
                return size_50 * ((size_80 / size_50) ** ratio)
            else:
                return size_80
        except Exception:
            return 70.0

    def select_model(self, problem_params, model_list):
        try:
            validated_models = self._validate_model_list(model_list)
            validated_problem_params = self._validate_problem_params(problem_params)

            problem_category = self._get_max_category(validated_problem_params["result"])
            problem_score = validated_problem_params["score"]

            if self.auto_mode:
                expected_sizes = self._calculate_auto_expected_sizes(validated_models)
                print(f"Auto Mode: {expected_sizes}")
            else:
                expected_sizes = self.expected_sizes
                print(f"Manual Mode: {expected_sizes}")

            print(f"problem_score: {problem_score}")
            print(f"problem_category: {problem_category}")
            print("=" * 50)

            best_model = None
            max_score = -1

            for model in validated_models:
                try:
                    score = self._calculate_model_score(model, problem_score, problem_category, expected_sizes)

                    if score > max_score:
                        max_score = score
                        best_model = model
                except Exception as e:
                    print(f"Error occurred while processing model {model.get('model_name', 'Unknown')}: {str(e)}")
                    continue

            if best_model is None:
                print("Warning: Failed to select a model properly, returning fallback model (the largest model)")
                best_model = self._get_fallback_model(validated_models)

            return best_model

        except Exception as e:
            print(f"Critical error occurred during model selection: {str(e)}")
            try:
                if isinstance(model_list, (list, tuple)) and model_list:
                    best_fallback = None
                    max_size = -1

                    for model in model_list:
                        if isinstance(model, dict):
                            size_b = model.get('param_size_b')
                            if isinstance(size_b, (int, float)) and size_b > max_size:
                                max_size = size_b
                                best_fallback = model

                    if best_fallback is not None:
                        validated_fallback = self._validate_model(best_fallback)
                        if validated_fallback:
                            print(f"Returning fallback model: {validated_fallback['model_name']} ({validated_fallback['param_size_b']}B)")
                            return validated_fallback

                print("Warning: Model list is empty or contains no valid models, returning None")
                return None

            except Exception as inner_e:
                print(f"An error also occurred while retrieving the fallback model: {str(inner_e)}")
                return None