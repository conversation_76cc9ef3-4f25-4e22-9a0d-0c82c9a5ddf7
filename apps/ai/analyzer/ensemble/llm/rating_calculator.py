# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: rating_calculator
# @Author: <PERSON><PERSON>
# @E-mail: sun<PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2025/5/21
# ---
class RatingCalculator:
    """
    A class to calculate the inference capability score of a model based on key configuration parameters.
    Mainly focuses on parameter sizes to distinguish between model scales (e.g., 70B vs 685B).
    """

    def __init__(self):
        # Define weights for each parameter
        self.weights = {
            'hidden_size': 1.5,  # Hidden layer size, high importance
            'num_hidden_layers': 1.2,  # Number of layers, high importance
            'intermediate_size': 0.8,  # FFN intermediate size
            'num_attention_heads': 0.6,  # Number of attention heads
            'num_key_value_heads': 0.4,  # Number of KV heads (same as attention heads if dense attention)
            'vocab_size': 0.3,  # Vocabulary size, lower impact
            # MoE-related parameters, especially important for large models
            'n_routed_experts': 2.0,  # Number of experts, crucial for MoE models
            'num_experts_per_tok': 1.5  # Number of experts activated per token
        }

        # Base score adjustment by model type
        self.model_type_bonus = {
            'llama': 1.0,
            'mistral': 1.05,
            'deepseek': 1.1,
            'deepseek_v3': 1.15,
            'gpt_neox': 0.95,
            'falcon': 1.0,
            'mpt': 0.95,
            'mixtral': 1.1
        }

        # Reference benchmarks for model scale (for normalization)
        self.size_benchmarks = {
            '7B': {'hidden_size': 4096, 'num_hidden_layers': 32, 'n_routed_experts': 0},
            '13B': {'hidden_size': 5120, 'num_hidden_layers': 40, 'n_routed_experts': 0},
            '34B': {'hidden_size': 8192, 'num_hidden_layers': 48, 'n_routed_experts': 0},
            '70B': {'hidden_size': 8192, 'num_hidden_layers': 80, 'n_routed_experts': 0},
            '8x7B_MoE': {'hidden_size': 4096, 'num_hidden_layers': 32, 'n_routed_experts': 8, 'num_experts_per_tok': 2},
            '8x22B_MoE': {'hidden_size': 6144, 'num_hidden_layers': 48, 'n_routed_experts': 8,
                          'num_experts_per_tok': 2},
            '685B_MoE': {'hidden_size': 7168, 'num_hidden_layers': 61, 'n_routed_experts': 256,
                         'num_experts_per_tok': 8}
        }

    def calculate_params(self, config):
        """
        Estimate the number of model parameters.
        Note: This is a simplified calculation. Actual parameter count is affected by more factors.
        """
        hidden_size = config.get('hidden_size', 0)
        num_layers = config.get('num_hidden_layers', 0)
        vocab_size = config.get('vocab_size', 0)
        intermediate_size = config.get('intermediate_size', hidden_size * 4)
        n_heads = config.get('num_attention_heads', 0)
        n_kv_heads = config.get('num_key_value_heads', n_heads)

        # MoE-related parameters
        n_experts = config.get('n_routed_experts', 0)
        experts_per_tok = config.get('num_experts_per_tok', 0)
        is_moe = n_experts > 0 and experts_per_tok > 0

        # Basic parameter calculation
        # 1. Embedding layer parameters
        embedding_params = hidden_size * vocab_size

        # 2. Estimate per-layer parameters
        params_per_layer = 0

        # 2.1 Self-attention layer
        attn_params = 4 * hidden_size * hidden_size  # Q, K, V projection + output projection
        if n_kv_heads != n_heads:  # If using grouped attention
            # Adjust KV parameter size
            kv_reduction_factor = n_kv_heads / n_heads
            attn_params = hidden_size * hidden_size * (2 + 2 * kv_reduction_factor)

        params_per_layer += attn_params

        # 2.2 Feed-forward layer
        if is_moe:
            # MoE FFN: each expert has a full FFN network
            ffn_params = n_experts * (hidden_size * intermediate_size * 2)
        else:
            # Standard FFN parameters
            ffn_params = hidden_size * intermediate_size * 2

        params_per_layer += ffn_params

        # 2.3 Layer normalization parameters
        norm_params = 2 * hidden_size  # Typically 2 LayerNorms per layer
        params_per_layer += norm_params

        # Total parameter count
        total_params = embedding_params + (params_per_layer * num_layers)

        # Convert to billions (B)
        return total_params / (1000 * 1000 * 1000)

    def calculate_score(self, config):
        """
        Calculate model inference capability score based on config
        """
        score = 0.0

        # Base score: weighted sum of key parameters
        for param, weight in self.weights.items():
            if param in config:
                # Special handling for MoE parameters
                if param == 'n_routed_experts' and config.get(param, 0) > 0:
                    # For MoE models, expert count score depends on experts per token
                    experts_per_tok = config.get('num_experts_per_tok', 1)
                    expert_score = config[param] * experts_per_tok / 8  # Normalize with 8 experts as base
                    score += weight * expert_score
                else:
                    # Other parameters are directly weighted
                    score += weight * config.get(param, 0)

        # Model type adjustment
        model_type = config.get('model_type', '').lower()
        if model_type in self.model_type_bonus:
            score *= self.model_type_bonus[model_type]

        # Parameter estimate and bonus
        estimated_params = self.calculate_params(config)

        # Bonus based on parameter scale
        if estimated_params >= 500:  # 500B+
            param_factor = 5.0
        elif estimated_params >= 100:  # 100B+
            param_factor = 4.0
        elif estimated_params >= 50:  # 50B+
            param_factor = 3.0
        elif estimated_params >= 30:  # 30B+
            param_factor = 2.5
        elif estimated_params >= 10:  # 10B+
            param_factor = 2.0
        elif estimated_params >= 5:  # 5B+
            param_factor = 1.5
        else:
            param_factor = 1.0

        score *= param_factor

        # Normalize for easier interpretation
        normalized_score = score / 10000

        return {
            'score': round(normalized_score, 2),
            'estimated_params_billions': round(estimated_params, 2),
            'parameter_scale': self._identify_model_scale(config)
        }

    def _identify_model_scale(self, config):
        """
        Identify approximate model scale based on config parameters
        """
        # Extract key config values
        hidden_size = config.get('hidden_size', 0)
        num_layers = config.get('num_hidden_layers', 0)
        n_experts = config.get('n_routed_experts', 0)
        experts_per_tok = config.get('num_experts_per_tok', 0)

        # Calculate match score with benchmarks
        best_match = None
        best_match_score = 0

        for scale, benchmark in self.size_benchmarks.items():
            match_score = 0

            # Match hidden_size
            if 'hidden_size' in benchmark:
                h_ratio = min(hidden_size, benchmark['hidden_size']) / max(hidden_size, benchmark['hidden_size'])
                match_score += h_ratio * 0.4  # 40% weight

            # Match number of layers
            if 'num_hidden_layers' in benchmark:
                l_ratio = min(num_layers, benchmark['num_hidden_layers']) / max(num_layers,
                                                                                benchmark['num_hidden_layers'])
                match_score += l_ratio * 0.3  # 30% weight

            # Special handling for MoE models
            if 'n_routed_experts' in benchmark and benchmark['n_routed_experts'] > 0:
                if n_experts > 0:  # Both are MoE models
                    e_ratio = min(n_experts, benchmark['n_routed_experts']) / max(n_experts,
                                                                                  benchmark['n_routed_experts'])
                    match_score += e_ratio * 0.2  # 20% weight

                    if 'num_experts_per_tok' in benchmark:
                        et_ratio = min(experts_per_tok, benchmark['num_experts_per_tok']) / max(experts_per_tok,
                                                                                                benchmark[
                                                                                                    'num_experts_per_tok'])
                        match_score += et_ratio * 0.1  # 10% weight
                else:
                    match_score = 0  # One is MoE, the other is not — no match
            elif n_experts > 0:  # Current config is MoE but benchmark is not
                match_score = 0  # No match

            if match_score > best_match_score:
                best_match_score = match_score
                best_match = scale

        if best_match_score < 0.7:  # If best match score is below 70%, it's likely an unknown scale
            estimated_params = self.calculate_params(config)
            return f"Approx. {round(estimated_params)}B parameters"

        return best_match


