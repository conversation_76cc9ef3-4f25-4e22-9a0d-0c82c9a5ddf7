# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: launcher
# @Author: sun<PERSON>
# @E-mail: sun<PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2025/5/22
# ---
from typing import Any, Dict

from apps.ai.analyzer.ensemble.input.complexity_analyzer import ContextComplexityAnalyzer
from apps.ai.analyzer.ensemble.input.semantic_analyzer import SemanticAnalyzer


class InputGenerator:
    def __init__(self, get_ebd_func, es):
        self.default_local_emb_model_path = 'bge-large-zh-v1.5'
        self.get_ebd_func = get_ebd_func
        self.es = es

    def classify_content(self, input_data: Any) -> Dict[str, float]:
        # Implementation for semantic vectorization
        pass

    def gen_score(self, content: str):
        analyzer = ContextComplexityAnalyzer()
        score = analyzer.score(content)
        result = SemanticAnalyzer(self.get_ebd_func, self.es).handle_analysis(content)
        return {
            'score': score,
            'result': result
        }


