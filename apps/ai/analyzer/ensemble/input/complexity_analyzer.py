# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: test
# @Author: <PERSON><PERSON>
# @E-mail: sun<PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2025/5/20
# ---
import jieba
import math
from collections import Counter
import jieba.posseg as pseg
import jieba.analyse
import os

# Set jieba to use a local dictionary path
def init_jieba():
    jieba.setLogLevel(20)  # Disable jieba logs
    dict_path = os.path.join(os.path.dirname(__file__), "resources/jieba/dict.txt")
    # hmm_path = os.path.join(os.path.dirname(__file__), "resources/jieba/hmm_model.utf8")
    jieba.initialize()  # Must initialize before setting the dictionary path
    jieba.set_dictionary(dict_path)
    jieba.load_userdict(dict_path)
    # jieba.load_hmm_model(hmm_path)

init_jieba()

class ContextComplexityAnalyzer:
    def __init__(self, idf_path=None):
        # Use default path if the user does not provide an idf.txt path
        if idf_path is None:
            idf_path = os.path.join(os.path.dirname(__file__), "resources/jieba/idf.txt")

        self.idf_freq = {}
        self.median_idf = 0.0
        self._load_idf(idf_path)

    def _load_idf(self, idf_path):
        idf_values = []
        with open(idf_path, 'r', encoding='utf-8') as f:
            for line in f:
                try:
                    word, freq = line.strip().split()
                    freq = float(freq)
                    self.idf_freq[word] = freq
                    idf_values.append(freq)
                except:
                    continue
        # Calculate median
        if idf_values:
            idf_values.sort()
            mid = len(idf_values) // 2
            self.median_idf = idf_values[mid]

    def calc_rare_word_score(self, tokens):
        if not tokens:
            return 0
        idf_sum = 0
        for word in tokens:
            idf = self.idf_freq.get(word, self.median_idf)
            idf_sum += idf
        return idf_sum / len(tokens)

    def tokenize(self, text):
        return list(jieba.cut(text))

    def calc_word_count(self, tokens):
        return len(tokens)

    def calc_entity_count(self, text):
        # Use part-of-speech tags as a proxy for named entity recognition
        words = pseg.cut(text)
        entity_flags = {'nr', 'ns', 'nt', 't'}  # person names, place names, organization names, time
        return sum(1 for w in words if w.flag in entity_flags)

    def calc_entropy(self, tokens, n=2):
        ngrams = zip(*[tokens[i:] for i in range(n)])
        freq = Counter(ngrams)
        total = sum(freq.values())
        entropy = -sum((count / total) * math.log2(count / total) for count in freq.values())
        return entropy

    def calc_punctuation_score(self, text):
        punctuations = '，。、；：！？（）《》“”'
        return sum(1 for c in text if c in punctuations)

    def score(self, question: str):
        tokens = self.tokenize(question)
        word_count = self.calc_word_count(tokens)
        entity_count = self.calc_entity_count(question)
        rare_score = self.calc_rare_word_score(tokens)
        entropy = self.calc_entropy(tokens)
        punct_score = self.calc_punctuation_score(question)

        # Normalize and combine with weights (can be adjusted)
        score = (
            min(word_count / 20, 1.0) * 0.2 +
            min(entity_count / 5, 1.0) * 0.2 +
            min(rare_score, 1.0) * 0.2 +
            min(entropy / 5, 1.0) * 0.2 +
            min(punct_score / 5, 1.0) * 0.2
        )

        # Map to 0~100
        return round(score * 100, 2)