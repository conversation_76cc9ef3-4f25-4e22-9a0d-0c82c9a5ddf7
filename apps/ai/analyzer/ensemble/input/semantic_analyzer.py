# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: semantic_vectorization
# @Author: sun<PERSON>
# @E-mail: <EMAIL>
# @Site: 
# @Time: 2025/5/20
# ---
from enum import Enum
from typing import Any, Dict
from apps.ai.analyzer.config import Config
from lib_func.logger import logger

from enum import Enum
from typing import Any, Dict
import numpy as np
from apps.ai.analyzer.ensemble.config import Config
from lib_func.logger import logger


class SemanticAnalyzer:
    def __init__(self, get_ebd_func, es):
        self.default_local_emb_model_path = 'bge-large-zh-v1.5'
        self.get_ebd_func = get_ebd_func
        self.es = es

    def classify_content(self, input_data: Any) -> Dict[str, float]:
        # Implementation for semantic vectorization
        pass

    @staticmethod
    def cosine_similarity(vector1, vector2):
        vector1 = np.array(vector1)
        vector2 = np.array(vector2)

        dot_product = np.dot(vector1, vector2)
        norm1 = np.linalg.norm(vector1)
        norm2 = np.linalg.norm(vector2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)


    @staticmethod
    def softmax(scores, temperature=1.0):
        scores_array = np.array(list(scores.values()))
        exp_scores = np.exp(scores_array / temperature)
        softmax_scores = exp_scores / exp_scores.sum()

        return dict(zip(scores.keys(), softmax_scores))


    @staticmethod
    def weighted_average(doc_scores):
        weights = {}
        for doc_id, scores in doc_scores.items():
            max_score = max(scores.values())
            weights[doc_id] = max_score

        total_weight = sum(weights.values())
        weights = {k: v / total_weight for k, v in weights.items()}

        final_scores = {label: 0.0 for label in doc_scores[list(doc_scores.keys())[0]]}
        for doc_id, scores in doc_scores.items():
            for label, score in scores.items():
                final_scores[label] += score * weights[doc_id]

        total = sum(final_scores.values())
        return {k: v / total for k, v in final_scores.items()}


    @staticmethod
    def top_k(doc_scores, k=3):
        import numpy as np

        doc_max_scores = {doc_id: max(scores.values()) for doc_id, scores in doc_scores.items()}

        top_docs = sorted(doc_max_scores.items(), key=lambda x: x[1], reverse=True)[:k]
        top_doc_ids = [doc_id for doc_id, _ in top_docs]

        final_scores = {label: [] for label in doc_scores[list(doc_scores.keys())[0]]}
        for doc_id in top_doc_ids:
            for label, score in doc_scores[doc_id].items():
                final_scores[label].append(score)

        mean_scores = {label: np.mean(scores) for label, scores in final_scores.items()}
        total = sum(mean_scores.values())
        return {k: v / total for k, v in mean_scores.items()}


    def handle_analysis(self, input_content: str) -> Dict[str, float]:
        try:

            dataset_categories_raw = Config().dataset_categories

            dataset_categories = {item['key']: item['value'] for item in dataset_categories_raw}

            input_vector = self.get_ebd_func(input_content)

            label_vectors = {key: self.get_ebd_func(label_name)
                             for key, label_name in dataset_categories.items()}

            similarities = {}
            for key, label_vector in label_vectors.items():
                similarity = self.cosine_similarity(input_vector, label_vector)
                similarities[key] = similarity

            normalized_scores = self.softmax(similarities)

            sorted_scores = dict(sorted(normalized_scores.items(),
                                        key=lambda x: x[1],
                                        reverse=True))

            return sorted_scores

        except Exception as e:
            logger.error(f"Error in handle_analysis: {e}")

    @staticmethod
    def get_max_score_key(score_dict: dict) -> str:
        return max(score_dict, key=score_dict.get)