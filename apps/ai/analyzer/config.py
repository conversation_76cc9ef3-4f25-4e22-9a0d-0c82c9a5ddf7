# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: config
# @Author: <PERSON><PERSON>
# @E-mail: sun<PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2025/2/20
# ---
import os
import yaml
from typing import List, Dict, Optional
from lib_func.logger import logger



class Config:
    DEFAULT_CONFIG_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.yaml")
    def __init__(self, config_path: str = DEFAULT_CONFIG_PATH):
        try:
            with open(config_path, 'r') as f:
                self.config = yaml.safe_load(f)
                if not self.config:
                    raise ValueError("Config file is empty or not properly formatted.")
        except Exception as e:
            logger.exception(f"Error loading config file: {e}")
            raise

    @property
    def content_types(self) -> List[str]:
        return self.config.get('content_types', [])

    @property
    def dataset_categories(self) -> List[str]:
        return self.config.get('DATASET_CATEGORIES', [])

# enable module feature or not
ENABLE_SCOPE_ENHANCE = True

# Limit for llm
TOKEN_LIMIT = 10000

# Should not be too large, considering the sampling process
SAMPLE_SIZE = 200


# Ensure model params work if set to True here
ENABLE_CLASSIFY = False
AIGC_TYPE_ID = "tongyiqianwen"
MODEL_PATH = "qwen-max"

PROMPT_CONTENT = {
    "CLASSIFY_TYPE": '''
Objective: YOU ARE AN INTELLIGENT CLASSIFICATION ASSISTANT. YOU MUST classify the documents in the list from the following JSON into 2 to 5 subcategories.

CRITICAL REQUIREMENTS:
1. YOU MUST OUTPUT AT LEAST 2 CATEGORIES - SINGLE CATEGORY OUTPUT IS NOT ALLOWED
2. YOU MUST ONLY OUTPUT THE REQUESTED JSON FORMAT
3. DO NOT ADD ANY ADDITIONAL TEXT

Classification Criteria: YOU MUST classify the documents based on the content direction, style, characteristics, and other relevant information.

Input Format:

[
    {"document_id": "4946040667580141569", "doc_name": "Management Regulations.docx", "abstract": "Cloud Intelligence Equipment Co., Ltd."},
    {"document_id": "4946040667064242177", "doc_name": "Travel Subsidy.docx", "abstract": "Software Technology Co., Ltd."},
    {"document_id": "4946040667580141591", "doc_name": "November Key News.docx", "abstract": "News and newspaper summary"}
]

Output Format: YOU MUST return a JSON with AT LEAST 2 CATEGORIES in the following fixed list format:

[
    {
        "type_name": "Data Report",
        "document_list": ["4946040667580141569", "4946040667064242177"]
    },
    {
        "type_name": "News & Current Affairs", 
        "document_list": ["4946040667580141591"]
    }
]

IMPORTANT REQUIREMENTS:
1. YOU MUST CLASSIFY INTO AT LEAST 2 CATEGORIES - SINGLE CATEGORY IS NOT ALLOWED
2. YOU MUST ONLY OUTPUT THE JSON ARRAY
3. DO NOT ADD ANY EXPLANATIONS 
4. DO NOT ADD ANY COMMENTS
5. DO NOT ADD ANY TEXT BEFORE OR AFTER THE JSON
6. THE OUTPUT MUST START WITH [ AND END WITH ]
7. MINIMUM 2 CATEGORIES, MAXIMUM 5 CATEGORIES

YOU MUST return the target JSON with at least 2 categories in the format above. Now, I will give you the real JSON data below:
    '''
}
