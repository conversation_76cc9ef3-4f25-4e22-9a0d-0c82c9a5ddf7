# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: analytical_jobs
# @Author: <PERSON><PERSON>
# @E-mail: sun<PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2025/2/20
# ---
import json
import re
import numpy as np
from typing import Dict, List, Any
import jieba
import jieba.posseg as pseg
from collections import Counter
from sklearn.ensemble import RandomForestRegressor
from lib_func.logger import logger


class FeatureWeighter:

    def __init__(self):
        self.domain_weights = {
            'general': {
                'complexity': 0.25,
                'sentence_length': 0.25,
                'word_complexity': 0.25,
                'pos_distribution': 0.25
            },
            'technical': {
                'complexity': 0.20,
                'sentence_length': 0.15,
                'word_complexity': 0.40,
                'pos_distribution': 0.25
            },
            'literary': {
                'complexity': 0.20,
                'sentence_length': 0.35,
                'word_complexity': 0.15,
                'pos_distribution': 0.30
            },
            'academic': {
                'complexity': 0.30,
                'sentence_length': 0.25,
                'word_complexity': 0.30,
                'pos_distribution': 0.15
            },
            'conversational': {
                'complexity': 0.15,
                'sentence_length': 0.20,
                'word_complexity': 0.15,
                'pos_distribution': 0.50
            }
        }

    # delete this
    def detect_domain(self, text: str) -> str:
        words = list(jieba.cut(text))

        technical_indicators = ['研究', '技术', '方法', '系统', '数据', '分析', '算法']
        literary_indicators = ['美丽', '故事', '生活', '感受', '心情', '描述', '诗歌']
        academic_indicators = ['论文', '研究', '理论', '学术', '证明', '假设', '引用']
        conversational_indicators = ['你好', '谢谢', '请问', '聊天', '怎么样', '是吗', '认为']

        technical_count = sum(1 for w in words if w in technical_indicators)
        literary_count = sum(1 for w in words if w in literary_indicators)
        academic_count = sum(1 for w in words if w in academic_indicators)
        conversational_count = sum(1 for w in words if w in conversational_indicators)

        avg_word_len = sum(len(w) for w in words) / len(words) if words else 0
        has_question = any(q in text for q in ['吗', '？', '什么', '如何', '为什么'])

        scores = {
            'technical': technical_count * 1.2,
            'literary': literary_count * 1.2,
            'academic': academic_count * 1.5 + (avg_word_len > 2.5) * 2,
            'conversational': conversational_count * 1.5 + has_question * 2,
            'general': 1
        }

        return max(scores, key=scores.get)

    def get_weights(self, text: str) -> Dict[str, float]:
        domain = self.detect_domain(text)
        return self.domain_weights[domain]


class RecallOptimizer:

    def __init__(self):
        self.feedback_log = []
        self.model = None

    def record_feedback(self, query: str, top_k: int, relevance_scores: List[float]):
        quality = sum(score * (1.0 / (i + 1)) for i, score in enumerate(relevance_scores))
        self.feedback_log.append({
            'query': query,
            'top_k': top_k,
            'quality': quality
        })

    def prepare_training_data(self):
        analyzer = ChineseTextAnalyzer()
        X = []
        y = []

        for entry in self.feedback_log:
            features = analyzer.calculate_complexity(entry['query'])
            X.append([features])
            y.append(entry['quality'])

        return np.array(X), np.array(y)

    def train(self):
        if len(self.feedback_log) < 10:  # 数据太少，不训练
            return

        X, y = self.prepare_training_data()
        self.model = RandomForestRegressor(n_estimators=100)
        self.model.fit(X, y)


class RecallAnalyzer:

    def __init__(self):
        self.text_analyzer = ChineseTextAnalyzer()
        self.weighter = FeatureWeighter()
        self.optimizer = RecallOptimizer()

    def calculate_top_k(self, query: str, complexity, word_stats, pos_stats, domain_weights) -> int:
        sentence_count = self.text_analyzer.count_sentences(query)

        features = np.array([
            complexity,
            word_stats['word_count'] / max(1, sentence_count),
            word_stats['avg_word_length'],
            pos_stats['noun_ratio'] + pos_stats['verb_ratio']
        ])

        weights = self.weighter.get_weights(query)
        weight_vector = np.array([
            weights.get('complexity', 0.3),
            weights.get('sentence_length', 0.3),
            weights.get('word_complexity', 0.2),
            weights.get('pos_distribution', 0.2)
        ])

        score = np.dot(features, weight_vector)

        base_k = 3
        max_k = 15
        k = min(max_k, base_k + int(np.exp(score * 0.8)))

        return max(3, min(k, 20))


    def update_from_feedback(self, query: str, top_k: int, relevance_scores: List[float]):
        self.optimizer.record_feedback(query, top_k, relevance_scores)
        self.optimizer.train()


class RAGParamsTester:

    def __init__(self):
        self.analyzer = RecallAnalyzer()

    def analyze_query(self, query: str) -> Dict:
        complexity = self.analyzer.text_analyzer.calculate_complexity(query)
        word_stats = self.analyzer.text_analyzer.get_word_stats(query)
        pos_stats = self.analyzer.text_analyzer.get_pos_stats(query)
        domain_weights = self.analyzer.weighter.get_weights(query)

        top_k = self.analyzer.calculate_top_k(query, complexity, word_stats, pos_stats, domain_weights)

        rag_params = {
            "query_stats": {
                "complexity_score": round(complexity, 3),
                "word_count": word_stats["word_count"],
                "avg_word_length": round(word_stats["avg_word_length"], 2),
                "sentence_count": self.analyzer.text_analyzer.count_sentences(query)
            },
            "text_features": {
                "pos_distribution": {
                    "noun_ratio": round(pos_stats["noun_ratio"], 3),
                    "verb_ratio": round(pos_stats["verb_ratio"], 3),
                    "adj_ratio": round(pos_stats["adj_ratio"], 3)
                },
                "domain_weights": domain_weights
            },
            "rag_parameters": {
                "top_k": top_k,
                "complexity_level": self._get_complexity_level(complexity),
                "suggested_rerank_threshold": self._get_rerank_threshold(complexity)
            }
        }

        return rag_params

    def _get_complexity_level(self, complexity: float) -> str:
        if complexity < 0.3:
            return "Simple"
        elif complexity < 0.6:
            return "Medium"
        else:
            return "Complex"

    def _get_rerank_threshold(self, complexity: float) -> float:
        base_threshold = 0.7
        return min(0.9, base_threshold + complexity * 0.2)

    def run_test_cases(self):
        test_cases = ["今天的天气怎么样？"]

        logger.info("=== RAG参数分析测试 ===\n")

        for i, query in enumerate(test_cases, 1):
            logger.info(f"测试用例 {i}:")
            logger.info(f"查询: {query}")

            params = self.analyze_query(query)

            logger.info("\n分析结果:")
            logger.info(json.dumps(params, ensure_ascii=False, indent=2))
            logger.info("\n" + "=" * 50 + "\n")

            self._simulate_retrieval(query, params)

    def _simulate_retrieval(self, query: str, params: Dict):
        mock_relevance_scores = [0.95, 0.85, 0.75, 0.65, 0.55][:params['rag_parameters']['top_k']]
        self.analyzer.update_from_feedback(query, params['rag_parameters']['top_k'], mock_relevance_scores)

class ChineseTextAnalyzer:

    def __init__(self):
        pass

    def count_sentences(self, text: str) -> int:
        sentences = re.split('[。！？；]', text)
        return len([s for s in sentences if s.strip()])

    def get_word_stats(self, text: str) -> Dict[str, float]:
        words = list(jieba.cut(text))
        return {
            'word_count': len(words),
            'avg_word_length': sum(len(w) for w in words) / len(words) if words else 0
        }

    def get_pos_stats(self, text: str) -> Dict[str, float]:
        words = pseg.cut(text)
        pos_counts = Counter(w.flag for w in words)
        total = sum(pos_counts.values())
        return {
            'noun_ratio': pos_counts.get('n', 0) / total,
            'verb_ratio': pos_counts.get('v', 0) / total,
            'adj_ratio': pos_counts.get('a', 0) / total
        }

    def calculate_complexity(self, text: str) -> float:
        stats = self.get_word_stats(text)
        sentence_count = self.count_sentences(text)
        avg_sentence_length = stats['word_count'] / sentence_count if sentence_count else 0

        pos_stats = self.get_pos_stats(text)

        complexity = (
                0.3 * (avg_sentence_length / 20) +
                0.3 * pos_stats['noun_ratio'] +
                0.2 * stats['avg_word_length'] +
                0.2 * pos_stats['verb_ratio']
        )

        return min(1.0, max(0.0, complexity))
