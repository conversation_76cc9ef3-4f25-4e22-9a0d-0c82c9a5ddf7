# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: strategy_recommender
# @Author: <PERSON><PERSON>
# @E-mail: sun<PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2025/3/3
# ---
import os
import pickle

import joblib
import numpy as np
import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
from typing import List, Dict, Any, Tuple
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
from lib_func.logger import logger

# from imblearn.over_sampling import SMOTE

N_SAMPLE = 3000
N_RATIO = 0.7


class MLStrategyRecommender:
    def __init__(self, base_data_path: str = "./data", model_path: str = "models"):
        current_dir = os.path.dirname(os.path.abspath(__file__))
        self.base_data_path = os.path.join(current_dir, base_data_path)
        self.model_path = os.path.join(current_dir, model_path)
        self.model = None
        self.scaler = None
        self.feature_keys = [
            "total_chars", "total_documents", "avg_doc_length", "avg_sentence_length",
            "vocabulary_size", "keyword_density", "noun_ratio",
            "avg_cosine_similarity", "diversity_score", "text_complexity_score",
            "estimate_density", "avg_paragraphs", "avg_paragraphs_length"
        ]

        self.strategy_mapping = {
            "narrative_content": {"chunk_size": 1024, "top_k": 6, "threshold": 1.4},
            "mixed_general": {"chunk_size": 512, "top_k": 8, "threshold": 1.4},
            "large_diverse": {"chunk_size": 1536, "top_k": 12, "threshold": 1.4},
        }

        self.output_params = ["chunk_size", "top_k", "threshold"]

        self.param_ranges = {
            "chunk_size": [512, 1024, 1536],
            "top_k": (5, 20),
            "threshold": (1.0, 2.0)
        }

        self.default_strategy = "mixed_general"

        self.rules_config = [
            {
                "strategy": "large_diverse",
                "conditions": [
                    ("avg_paragraphs_length", "<", 100)
                ],
                "rule_name": "rule_large_diverse",
                "priority": 2,
                "weights": 1.2
            },
        ]

        self.default_params = self.strategy_mapping[self.default_strategy]
        self.rule_keys = [rule["rule_name"] for rule in self.rules_config]

        # add later
        self.additional_rules_config = []

        os.makedirs(model_path, exist_ok=True)

    def _evaluate_rule(self, features: Dict[str, Any], rule: Dict[str, Any]) -> bool:
        for condition in rule["conditions"]:
            if not self._evaluate_condition(features, condition):
                return False
        return True

    def _create_rule_based_features(self, features: Dict[str, Any]) -> List[float]:
        rule_features = []

        for rule in self.rules_config:
            rule_features.append(1.0 if self._evaluate_rule(features, rule) else 0.0)

        for rule in self.additional_rules_config:
            rule_features.append(1.0 if self._evaluate_rule(features, rule) else 0.0)

        return rule_features

    def _prepare_features(self, features: Dict[str, Any]) -> np.ndarray:
        for k in self.feature_keys:
            if not (-1e6 < features[k] < 1e6):
                raise ValueError(f"feature val {k} error: {features[k]}")  # FIXME

        original_features = [features[key] for key in self.feature_keys]
        # rule_features = self._create_rule_based_features(features)
        # all_features = original_features + rule_features
        all_features = original_features
        return np.array(all_features).reshape(1, -1)

    def determine_strategy_rule(self, features: Dict[str, Any]) -> str:
        # match strategy with priority and default
        sorted_rules = sorted(self.rules_config, key=lambda x: x["priority"])
        for rule in sorted_rules:
            if self._evaluate_rule(features, rule):
                return rule["strategy"]

        return self.default_strategy

    def _evaluate_condition(self, features: Dict[str, Any], condition: Tuple) -> bool:
        feature_name, operator, threshold = condition
        feature_value = features[feature_name]

        if operator == ">":
            return feature_value > threshold
        elif operator == "<":
            return feature_value < threshold
        elif operator == ">=":
            return feature_value >= threshold
        elif operator == "<=":
            return feature_value <= threshold
        elif operator == "==":
            return feature_value == threshold
        else:
            raise ValueError(f"evaluate not support: {operator}")

    def _find_nearest_valid_chunk_size(self, chunk_size):
        valid_sizes = self.param_ranges["chunk_size"]
        return min(valid_sizes, key=lambda x: abs(x - chunk_size))

    def predict(self, features: Dict[str, Any]) -> Dict[str, Any]:
        X = self._prepare_features(features)

        # Get rule-based strategy for fallback
        rule_strategy = self.determine_strategy_rule(features)
        rule_params = self.strategy_mapping[rule_strategy]

        if self.model is None:
            return rule_params

        predicted_params = self.model.best_estimator_.predict(X)[0]

        params = {}

        params["chunk_size"] = self._find_nearest_valid_chunk_size(predicted_params[0])

        min_top_k, max_top_k = self.param_ranges["top_k"]
        params["top_k"] = int(round(max(min_top_k, min(max_top_k, predicted_params[1]))))

        min_threshold, max_threshold = self.param_ranges["threshold"]
        params["threshold"] = max(min_threshold, min(max_threshold, predicted_params[2]))

        if features["total_chars"] < 5000 or features["total_documents"] < 3:
            # fallback to rule-based
            return rule_params

        return params

    def train_model(self, training_data: List[Dict[str, Any]]) -> None:
        logger.info(f"data size: {len(training_data)}")

        X = []
        y = []

        for entry in training_data:
            features = entry["features"]
            if "params" in entry:
                param_values = [entry["params"][param_name] for param_name in self.output_params]
            else:
                strategy = entry["strategy"]
                params = self.strategy_mapping[strategy]
                param_values = [params[param_name] for param_name in self.output_params]

            feature_vector = self._prepare_features(features).flatten()
            X.append(feature_vector)
            y.append(param_values)

        X = np.array(X)
        y = np.array(y)

        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        from sklearn.model_selection import GridSearchCV
        sample_weights = np.ones(len(y_train))

        for rule in self.rules_config:
            weight = rule["weights"]
            if weight != 1.0:
                strategy = rule["strategy"]
                target_params = self.strategy_mapping[strategy]
                target_values = [target_params[param_name] for param_name in self.output_params]

                # Apply weights to samples that are close to this strategy's parameters
                for i, sample_params in enumerate(y_train):
                    # Simple heuristic: if predicted values are close to target strategy values
                    if np.mean(np.abs(sample_params - target_values)) < 0.1:
                        sample_weights[i] = weight

        param_grid = {
            'n_estimators': [100, 200],
            'max_depth': [5, 10, None],
            'min_samples_split': [2, 5]
        }

        self.model = GridSearchCV(RandomForestRegressor(), param_grid, cv=3, scoring='neg_mean_squared_error')
        self.model.fit(X_train, y_train, sample_weight=sample_weights)
        self._save_model()

        return self.test_model(X_test=X_test, y_test=y_test)

    def test_model(self, test_data: List[Dict[str, Any]] = None, X_test=None, y_test=None) -> Tuple:
        if self.model is None:
            raise ValueError("no trained model")

        if test_data is not None:
            X = []
            y = []

            for entry in test_data:
                features = entry["features"]
                strategy = entry["strategy"]

                # Get target parameters from strategy
                params = self.strategy_mapping[strategy]
                param_values = [params[param_name] for param_name in self.output_params]

                feature_vector = self._prepare_features(features).flatten()
                X.append(feature_vector)
                y.append(param_values)

            X_test = np.array(X)
            y_test = np.array(y)

        if X_test is None or y_test is None:
            raise ValueError("X_test is None or y_test is None")

        y_pred = self.model.best_estimator_.predict(X_test)

        for i, param_name in enumerate(self.output_params):
            mse = mean_squared_error(y_test[:, i], y_pred[:, i])
            r2 = r2_score(y_test[:, i], y_pred[:, i])
            logger.info(f"{param_name}: MSE = {mse:.4f}, R² = {r2:.4f}")

        overall_mse = mean_squared_error(y_test, y_pred)
        logger.info(f"Overall MSE: {overall_mse:.4f}")

        # feature_names = self.feature_keys + self.rule_keys
        feature_names = self.feature_keys
        importances = self.model.best_estimator_.feature_importances_
        indices = np.argsort(importances)[::-1]

        logger.info("\nFeature importance:")
        for i in range(len(feature_names)):
            logger.info(f"{feature_names[indices[i]]}: {importances[indices[i]]:.4f}")

        return X_test, y_test, y_pred, feature_names, importances

    def _save_model(self) -> None:
        model_file = os.path.join(self.model_path, "strategy_regressor.pkl")
        joblib.dump(self.model, model_file)
        logger.info(f"Model has been saved to: {model_file}")

    def load_model(self) -> bool:
        model_file = os.path.join(self.model_path, "strategy_regressor.pkl")
        if os.path.exists(model_file):
            self.model = joblib.load(model_file)
            logger.info(f"Successfully loaded the model: {model_file}")
            return True
        else:
            logger.info(f"Model file not found: {model_file}")
            return False

    def recommend_parameters(self, knowledge_id: str,
                             texts: List[str],
                             filenames: List[str],
                             embeddings: List[List[float]],
                             analyzer) -> Dict[str, Any]:
        features = analyzer.analyze_user_knowledge_base(
            knowledge_id, texts, filenames, embeddings
        )

        recommended_params = self.predict(features)

        if features["total_chars"] > 80000:
            recommended_params["top_k"] += 2
        elif features["total_chars"] < 20000:
            recommended_params["top_k"] = max(5, recommended_params["top_k"] - 2)

        closest_strategy = self._find_closest_strategy(recommended_params)

        result = {
            "knowledge_id": knowledge_id,
            "recommended_chunk_size": recommended_params["chunk_size"],
            "recommended_top_k": recommended_params["top_k"],
            "recommended_threshold": recommended_params["threshold"],
            "closest_strategy": closest_strategy,
            "features": features
        }
        return result

    def _find_closest_strategy(self, params):
        """Find which predefined strategy the predicted parameters are closest to"""
        min_distance = float('inf')
        closest_strategy = self.default_strategy

        for strategy, strategy_params in self.strategy_mapping.items():
            distance = 0
            distance += ((params["chunk_size"] - strategy_params["chunk_size"]) / 1024) ** 2
            distance += ((params["top_k"] - strategy_params["top_k"]) / 10) ** 2
            distance += ((params["threshold"] - strategy_params["threshold"]) / 0.5) ** 2
            distance = np.sqrt(distance)

            if distance < min_distance:
                min_distance = distance
                closest_strategy = strategy

        return closest_strategy

    def visualize_model_performance(self, X_test, y_test, y_pred, feature_names, importances) -> None:
        import matplotlib.font_manager as fm
        font_path = '/System/Library/Fonts/STHeiti Light.ttc'
        prop = fm.FontProperties(fname=font_path)
        plt.rcParams["font.family"] = prop.get_name()
        plt.rcParams['font.sans-serif'] = prop.get_name()
        plt.rcParams['axes.unicode_minus'] = False

        """Visualize model performance"""
        plt.figure(figsize=(18, 12))

        # 1. Predicted vs Actual for each parameter
        for i, param_name in enumerate(self.output_params):
            plt.subplot(2, 2, i + 1)
            plt.scatter(y_test[:, i], y_pred[:, i], alpha=0.5)
            plt.plot([min(y_test[:, i]), max(y_test[:, i])], [min(y_test[:, i]), max(y_test[:, i])], 'r--')
            plt.title(f'Predicted vs Actual {param_name}')
            plt.xlabel('Actual')
            plt.ylabel('Predicted')
            plt.grid(True)

        # 2. Feature Importance
        plt.subplot(2, 2, 4)
        indices = np.argsort(importances)[-10:]  # Show only the top 10 important features
        plt.barh(range(len(indices)), importances[indices], align='center')
        plt.yticks(range(len(indices)), [feature_names[i] for i in indices])
        plt.title('Feature Importance')

        plt.tight_layout()

        # Save the chart
        plt.savefig(os.path.join(self.base_data_path, "model_performance.png"))
        plt.close()

        logger.info(f"Model performance visualization saved to: {os.path.join(self.base_data_path, 'model_performance.png')}")

        # Additional visualization for parameter distribution
        plt.figure(figsize=(15, 5))

        for i, param_name in enumerate(self.output_params):
            plt.subplot(1, 3, i + 1)
            plt.hist(y_pred[:, i], bins=10, alpha=0.5, label='Predicted')
            plt.hist(y_test[:, i], bins=10, alpha=0.5, label='Actual')
            plt.title(f'{param_name} Distribution')
            plt.legend()

        plt.tight_layout()
        plt.savefig(os.path.join(self.base_data_path, "parameter_distribution.png"))
        plt.close()

        logger.info(
            f"Parameter distribution visualization saved to: {os.path.join(self.base_data_path, 'parameter_distribution.png')}")

    def predict_example(self, features: Dict[str, Any]):
        if self.load_model():
            logger.info("load success.")
        else:
            logger.info("load failed, use rule-based prediction.")

        recommended_params = self.predict(features)
        closest_strategy = self._find_closest_strategy(recommended_params)

        logger.info(f"recommended_params: {recommended_params}")
        logger.info(f"closest_strategy: {closest_strategy}")

        if features["total_chars"] > 80000:
            recommended_params["top_k"] += 2
        elif features["total_chars"] < 20000:
            recommended_params["top_k"] = max(5, recommended_params["top_k"] - 2)

        import random
        recommended_params["threshold"] = round(recommended_params["threshold"] + random.choice([-0.1, 0, 0.1]), 1)

        result_val = {
            "knowledge_id": "knowledge_id",  # TODO
            "recommended_chunk_size": recommended_params["chunk_size"],
            "recommended_top_k": recommended_params["top_k"],
            "recommended_threshold": recommended_params["threshold"],
            "closest_strategy": closest_strategy,
            "features": features
        }
        return result_val


def build():
    recommender = MLStrategyRecommender()
    logger.info("Generating synthetic training data...")
    all_data = generate_synthetic_data(n_samples=N_SAMPLE)

    train_size = int(len(all_data) * 0.8)
    training_data = all_data[:train_size]
    test_data = all_data[train_size:]

    logger.info(f"Training data: {len(training_data)} samples")
    logger.info(f"Test data: {len(test_data)} samples")

    # X_test, y_test, y_pred, feature_names, importances = recommender.train_model(training_data)
    # recommender.visualize_model_performance(X_test, y_test, y_pred, feature_names, importances)

    logger.info("\nTesting prediction functionality:")
    for i in range(5):
        sample = test_data[i]
        features = sample["features"]
        rule_strategy = recommender.determine_strategy_rule(features)
        ml_params = recommender.predict(features)
        closest_strategy = recommender._find_closest_strategy(ml_params)

        logger.info(f"Sample {i + 1}:")
        logger.info(f"  - Actual strategy: {sample['strategy']}")
        logger.info(f"  - Rule prediction: {rule_strategy}")
        logger.info(f"  - Model prediction: {ml_params}")
        logger.info(f"  - Closest strategy: {closest_strategy}")


def generate_synthetic_data(n_samples=200):
    import random

    strategy_mapping = MLStrategyRecommender().strategy_mapping
    data = []

    for i in range(n_samples):

        features = {
            "total_chars": random.randint(10000, 100000),
            "total_documents": random.randint(5, 50),
            "avg_doc_length": random.randint(500, 5000),
            "avg_sentence_length": random.randint(10, 30),
            "vocabulary_size": random.randint(300, 2000),
            "keyword_density": random.random() * 0.5,
            "noun_ratio": random.random() * 0.5,
            "avg_cosine_similarity": random.random() * 0.8,
            "diversity_score": random.random(),
            "text_complexity_score": random.randint(5, 25),
            "estimate_density": random.random() * 0.9,
            "avg_paragraphs": random.randint(1, 12),
            "avg_paragraphs_length": random.randint(30, 300),
        }

        if features["avg_paragraphs_length"] < 100:
            strategy = "large_diverse"
        elif features["avg_doc_length"] > 800:
            strategy = "narrative_content"
        else:
            strategy = "mixed_general"

        params = strategy_mapping[strategy].copy()
        params["chunk_size"] = int(params["chunk_size"] * (0.8 + random.random() * 0.4))
        params["top_k"] = params["top_k"] * (0.8 + random.random() * 0.4)
        params["threshold"] = params["threshold"] * (0.9 + random.random() * 0.2)

        # params = {
        #     "chunk_size": random.choice([512, 1024, 1536]),
        #     "top_k": random.randint(5, 20),
        #     "threshold": round(random.uniform(1.0, 2.0), 1)
        # }

        data.append({
            "knowledge_id": f"knowledge_{i}",
            "strategy": strategy,
            "params": params,
            "features": features
        })

    return data