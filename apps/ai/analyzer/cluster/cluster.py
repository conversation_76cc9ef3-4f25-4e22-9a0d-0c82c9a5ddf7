# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: cluster
# @Author: sunhao
# @E-mail: <EMAIL>
# @Site: 
# @Time: 2025/4/22
# ---
import numpy as np
import umap
from flask import g
from sklearn.preprocessing import normalize
import hdbscan
import os
import time
from sklearn.metrics import silhouette_score
import psutil
import logging

from apps.ai.analyzer.cluster.configs import N_DIM, PROJECT_QUANTITY_LIMIT, SCROLL_CONFIG, SCROLL_SIZE
from puppet.es_sdk import EsSdkPool
from lib_func.logger import logger


class LargeScaleDocumentClustering:

    def __init__(self,
                 corpid,
                 processed_index_name,
                 result_index_name,
                 batch_capacity,
                 enable_monitoring=True):

        # core params
        self.es = EsSdkPool(corpid)
        self.processed_index_name = processed_index_name
        self.result_index_name = result_index_name

        self.batch_capacity = batch_capacity
        self.min_cluster_size = 50
        self.core_sample_ratio = 0.2
        self.confidence_threshold = 0.3
        self.min_samples = 10
        self.cluster_selection_epsilon = 0.0

        self.core_samples = []
        self.clusterer = None

        # monitor
        self.enable_monitoring = enable_monitoring
        if enable_monitoring:
            self.monitor = ClusterMonitor()

        self.umap_reducer = umap.UMAP(
            n_components=N_DIM,
            n_neighbors=15,
            min_dist=0.05,
            metric='cosine',
            random_state=42
        )

        # logger
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

    def cluster_documents(self):
        logger.info("Start cluster_documents process...")
        start_time = time.time()

        scroll_id = None
        batch_count = 0
        total_documents = 0

        count_query = {"query": {"match_all": {}}}
        total_count = self.es.count_data(self.processed_index_name, count_query)['count']
        logger.info(f"Total count for this job is {total_count}.")

        if total_count > PROJECT_QUANTITY_LIMIT:
            logger.error(f"Total count ({total_count}), the quantity limit is exceeded, stop the operation.")

        self._adjust_initial_parameters(total_count)

        while True:
            batch_start = time.time()
            vectors, doc_ids, scroll_id = self._fetch_batch(scroll_id)

            if vectors.size == 0:
                logger.info("no more data, stop batch.")
                break

            self._process_batch(vectors, doc_ids, is_first=(batch_count == 0))

            batch_count += 1
            total_documents += len(vectors)
            batch_time = time.time() - batch_start

            logger.info(
                f"Processed batch {batch_count}, document count {len(vectors)}, total {total_documents}/{total_count}, "
                f"time taken {batch_time:.2f}s, core sample size {sum(len(cs) for cs in self.core_samples)}")

            del vectors

        if scroll_id:
            try:
                self.es.clear_scroll(scroll_id=scroll_id)
            except Exception as e:
                logger.warning(f"clear scroll failed: {str(e)}")

        logger.info("all batches done...")
        self._final_clustering()
        updated_count, labels_count = self._update_all_labels()

        total_time = time.time() - start_time
        logger.info(f"Clustering complete! Total time: {total_time:.2f}s, Documents updated: {updated_count}")

        if self.enable_monitoring:
            self.monitor.summary()

        return updated_count, labels_count


    def _fetch_batch(self, scroll_id=None):

        vectors = []
        doc_ids = []

        if scroll_id is None:
            query = {
                "_source": ["doc_id", "aggregated_vector"],
                "query": {"match_all": {}},
                "size": self.batch_capacity
            }

            try:
                response = self.es.search_data_scroll(
                    index_name=self.processed_index_name,
                    query_dict=query,
                    scroll=SCROLL_CONFIG
                )
                next_scroll_id = response["_scroll_id"]
                hits = response["hits"]["hits"]

            except Exception as e:
                logger.error(f"fail to search by error: {str(e)}")
                return np.array([]), [], None
        else:
            try:
                response = self.es.scroll(
                    scroll_id=scroll_id,
                    scroll=SCROLL_CONFIG
                )

                next_scroll_id = response["_scroll_id"]
                hits = response["hits"]["hits"]

            except Exception as e:
                logger.error(f"fail to search by error: {str(e)}")
                return np.array([]), [], None

        if not hits:
            if scroll_id:
                try:
                    self.es.clear_scroll(scroll_id=scroll_id)
                except Exception as e:
                    logger.warning(f"fail to clear: {str(e)}")
            return np.array([]), [], None

        for hit in hits:
            try:
                doc_ids.append(hit["_source"]["doc_id"])
                vectors.append(hit["_source"]["aggregated_vector"])
            except KeyError as e:
                logger.warning(f"fail: {str(e)}")
                continue

        # Here is for batch_capacity oversize.
        # 获取更多批次以达到目标批次大小
        # total_collected = batch_size
        # batch_count = 1
        #
        # while total_collected < self.batch_capacity:
        #     batch_count += 1
        #     try:
        #         response = self.es.scroll(
        #             scroll_id=next_scroll_id,
        #             scroll=SCROLL_CONFIG
        #         )
        #
        #         next_scroll_id = response["_scroll_id"]
        #         hits = response["hits"]["hits"]
        #
        #         if not hits:
        #             break
        #
        #         before_count = total_collected
        #
        #         for hit in hits:
        #             try:
        #                 doc_ids.append(hit["_source"]["doc_id"])
        #                 vectors.append(hit["_source"]["aggregated_vector"])
        #                 total_collected += 1
        #
        #                 # 如果达到批次大小，停止添加
        #                 if total_collected >= self.batch_capacity:
        #                     break
        #             except KeyError as e:
        #                 logger.warning(f"文档缺少必要字段: {str(e)}")
        #                 continue
        #
        #         new_added = total_collected - before_count
        #         logger.info(
        #             f"批次 {batch_count}: 处理了 {len(hits)} 文档，新增 {new_added} 条数据，总计 {total_collected} 条")
        #
        #         # 如果这批没有新增或没有更多结果，停止循环
        #         if new_added == 0 or len(hits) == 0:
        #             break
        #
        #     except Exception as e:
        #         logger.error(f"获取额外数据失败: {str(e)}")
        #         break
        #
        # logger.info(f"共获取到 {len(vectors)} 条文档数据")
        # return np.array(vectors), doc_ids, next_scroll_id

        logger.info(f"Get {len(vectors)} docs.")
        return np.array(vectors), doc_ids, next_scroll_id


    def reassign_noise_points(self):
        logger.info("reassign_noise_points...")

        # 1. obtain the cluster centers of the non-noise points
        cluster_centers = {}
        scroll_id = None

        while True:
            vectors, doc_ids, labels = self._fetch_batch_with_labels(scroll_id)
            if vectors.size == 0:
                break

            for label in set(labels):
                if label != -1:
                    mask = labels == label
                    if label not in cluster_centers:
                        cluster_centers[label] = [np.zeros(vectors.shape[1]), 0]

                    cluster_centers[label][0] += np.sum(vectors[mask], axis=0)
                    cluster_centers[label][1] += np.sum(mask)

        for label in cluster_centers:
            if cluster_centers[label][1] > 0:
                cluster_centers[label] = cluster_centers[label][0] / cluster_centers[label][1]

        # 2. Redistribute the noise points
        scroll_id = None
        updated = 0

        while True:
            vectors, doc_ids, labels = self._fetch_batch_with_labels(scroll_id)
            if vectors.size == 0:
                break

            noise_mask = labels == -1
            if np.any(noise_mask):
                noise_vectors = vectors[noise_mask]
                noise_ids = [doc_ids[i] for i, is_noise in enumerate(noise_mask) if is_noise]

                new_labels = np.full(len(noise_vectors), -1)
                confidences = np.zeros(len(noise_vectors))

                for i, vec in enumerate(noise_vectors):
                    best_label = -1
                    best_sim = 0.3

                    for label, center in cluster_centers.items():
                        sim = np.dot(vec, center) / (np.linalg.norm(vec) * np.linalg.norm(center))
                        if sim > best_sim:
                            best_sim = sim
                            best_label = label

                    new_labels[i] = best_label
                    confidences[i] = best_sim if best_label != -1 else 0

                # update ES
                self.update_cluster_labels(noise_ids, new_labels, confidences)
                updated += np.sum(new_labels != -1)

        logger.info(f"Noise point reassignment complete, {updated} documents updated")
        return updated

    def _process_batch(self, vectors, doc_ids, is_first=False):
        if self.enable_monitoring:
            self.monitor.record_memory("batch_start")

        normalized = normalize(vectors)

        if is_first:
            normalized = self.umap_reducer.fit_transform(normalized)
        else:
            normalized = self.umap_reducer.transform(normalized)

        if is_first:
            self._check_vector_quality(vectors)

            logger.info(f"init using (min_cluster_size={self.min_cluster_size})...")

            self.clusterer = hdbscan.HDBSCAN(
                min_cluster_size=self.min_cluster_size,
                min_samples=int(self.min_cluster_size * 0.5),
                # min_samples=self.min_samples,
                cluster_selection_epsilon=0.15,
                core_dist_n_jobs=4,
                prediction_data=True
            )
            self.clusterer.fit(normalized)

            labels = self.clusterer.labels_
            logger.info(f"First batch of clustering complete, {len(set(labels)) - (1 if -1 in labels else 0)} clusters found")

            core_samples_mask = labels != -1
            if np.sum(core_samples_mask) > 0:
                keep_count = max(1, int(np.sum(core_samples_mask) * self.core_sample_ratio))
                if keep_count < np.sum(core_samples_mask):
                    core_indices = np.where(core_samples_mask)[0]
                    selected_indices = np.random.choice(core_indices, size=keep_count, replace=False)
                    new_mask = np.zeros_like(core_samples_mask)
                    new_mask[selected_indices] = True
                    core_samples_mask = new_mask

            core_samples = normalized[core_samples_mask]
            self.core_samples.append(core_samples)
            self.update_cluster_labels(doc_ids, labels)

            if self.enable_monitoring:
                self.monitor.update({
                    'batch_size': len(normalized),
                    'core_ratio': len(core_samples) / len(normalized),
                    'cluster_count': len(set(labels)) - (1 if -1 in labels else 0),
                    'noise_ratio': np.sum(labels == -1) / len(labels)
                })

        else:
            labels, strengths = hdbscan.approximate_predict(
                self.clusterer, normalized)

            mask = (strengths >= self.confidence_threshold) & (labels != -1)

            if np.sum(mask) > 0:
                keep_count = max(1, int(np.sum(mask) * self.core_sample_ratio))
                if keep_count < np.sum(mask):
                    indices = np.where(mask)[0]
                    selected_indices = np.random.choice(indices, size=keep_count, replace=False)
                    new_mask = np.zeros_like(mask)
                    new_mask[selected_indices] = True
                    mask = new_mask

            new_cores = normalized[mask]

            if len(new_cores) > 0:
                logger.info(f"Retained {len(new_cores)} core samples (confidence >= {self.confidence_threshold})")
                self.core_samples.append(new_cores)
            else:
                logger.warning(
                    f"No high-confidence samples found in the current batch, threshold may need adjustment")

            self.update_cluster_labels(doc_ids, labels)

            if self.enable_monitoring:
                self.monitor.update({
                    'batch_size': len(normalized),
                    'core_ratio': len(new_cores) / len(normalized),
                    'confidence_avg': np.mean(strengths[labels != -1]) if np.any(labels != -1) else 0,
                    'noise_ratio': np.sum(labels == -1) / len(labels)
                })

        self._adjust_parameters(len(normalized))

        del normalized

        if self.enable_monitoring:
            self.monitor.record_memory("batch_end")

    # TODO
    def _check_vector_quality(self, vectors, sample_size=100):
        """Enhanced vector quality check with more dynamic parameter adjustments"""
        if len(vectors) <= 1:
            return

        # Sample and compute similarity as before
        if len(vectors) > sample_size:
            indices = np.random.choice(len(vectors), sample_size, replace=False)
            sample = vectors[indices]
        else:
            sample = vectors

        normalized = normalize(sample)
        similarity = normalized @ normalized.T
        np.fill_diagonal(similarity, 0)

        avg_sim = np.mean(similarity)
        std_sim = np.std(similarity)

        logger.info(f"Vector quality check: Average similarity={avg_sim:.4f}, Standard deviation={std_sim:.4f}")

        # More sophisticated parameter tuning based on distribution
        if avg_sim > 0.7:  # Highly similar vectors
            self.min_cluster_size = max(3, int(self.min_cluster_size * 0.5))
            self.confidence_threshold = max(0.3, self.confidence_threshold * 0.7)
            logger.info(
                f"High vector similarity, lowering clustering thresholds: min_cluster_size={self.min_cluster_size}, "
                f"confidence_threshold={self.confidence_threshold}")
        elif avg_sim < 0.3:  # Very dissimilar vectors
            self.min_cluster_size = min(50, int(self.min_cluster_size * 1.5))
            self.confidence_threshold = min(0.7, self.confidence_threshold * 1.3)
            logger.info(
                f"Low vector similarity, raising clustering thresholds: min_cluster_size={self.min_cluster_size}, "
                f"confidence_threshold={self.confidence_threshold}")
        elif std_sim < 0.1:  # Very uniform similarity
            # Adjust epsilon to handle uniform distributions better
            cluster_selection_epsilon = 0.2
            logger.info(f"Uniform similarity distribution, adjusting epsilon={cluster_selection_epsilon}")

    # def _adjust_initial_parameters(self, total_count):
    #     """
    #     根据数据总量调整HDBSCAN参数
    #
    #     参数:
    #     - total_count: 数据总量
    #     """
    #     # 使用对数因子进行非线性调整
    #     log_factor = max(1, np.log10(total_count) - 1)
    #
    #     if total_count < 5000:
    #         self.min_cluster_size = max(5, int(total_count * 0.01))
    #         self.core_sample_ratio = 0.5
    #         self.batch_capacity = min(500, total_count // 3)
    #         self.min_samples = max(1, self.min_cluster_size // 5)
    #
    #     elif total_count < 50000:
    #         self.min_cluster_size = max(10, int(total_count * 0.005))
    #         self.core_sample_ratio = 0.4
    #         self.batch_capacity = min(3000, total_count // 4)
    #         self.min_samples = max(1, self.min_cluster_size // 6)
    #
    #     elif total_count < 100000:
    #         self.min_cluster_size = max(20, int(total_count * 0.005))
    #         self.core_sample_ratio = 0.3
    #         self.batch_capacity = min(5000, total_count // 5)
    #         self.min_samples = max(1, self.min_cluster_size // 8)
    #
    #     elif total_count < 500000:
    #         nonlinear_factor = 0.8 * (1 + 3 / (1 + np.log10(total_count)))
    #         self.min_cluster_size = max(50, int(total_count * 0.005 * nonlinear_factor))
    #         self.core_sample_ratio = 0.1
    #         self.batch_capacity = min(10000, total_count // (5 + log_factor))
    #         self.min_samples = max(1, self.min_cluster_size // 10)
    #
    #     else:
    #         nonlinear_factor = 0.7 * (1 + 4 / (1 + np.log10(total_count)))
    #         self.min_cluster_size = max(50, int(total_count * 0.003 * nonlinear_factor))
    #         self.core_sample_ratio = 0.05
    #         self.batch_capacity = min(15000, total_count // (8 + log_factor))
    #         self.min_samples = max(1, self.min_cluster_size // 12)
    #
    #     # 可选：设置cluster_selection_epsilon
    #     # 对于大型数据集，适当设置epsilon可以防止过度分裂
    #     if hasattr(self, 'cluster_selection_epsilon'):
    #         if total_count > 100000:
    #             self.cluster_selection_epsilon = 0.05
    #         elif total_count > 50000:
    #             self.cluster_selection_epsilon = 0.02
    #         else:
    #             self.cluster_selection_epsilon = 0.0
    #
    #
    #     logger.info(f"Parameters adjusted for {total_count} points: "
    #                      f"min_cluster_size={self.min_cluster_size}, "
    #                      f"core_sample_ratio={self.core_sample_ratio:.2f}, "
    #                      f"batch_capacity={self.batch_capacity}")


    def _adjust_initial_parameters(self, total_count):
        if total_count < 5000:
            self.min_cluster_size = max(5, int(total_count * 0.005))
            self.core_sample_ratio = 0.5
            self.batch_capacity = min(500, total_count // 3)
        elif total_count < 50000:
            self.min_cluster_size = max(10, int(total_count * 0.005))
            self.core_sample_ratio = 0.4
            self.batch_capacity = min(3000, total_count // 4)
        elif total_count < 100000:
            self.min_cluster_size = max(20, int(total_count * 0.005))
            self.core_sample_ratio = 0.3
            self.batch_capacity = min(5000, total_count // 5)
        elif total_count < 500000:
            self.min_cluster_size = max(50, int(total_count * 0.005))
            self.core_sample_ratio = 0.1
            self.batch_capacity = min(10000, total_count // 10)
        else:
            self.min_cluster_size = max(50, int(total_count * 0.003))
            self.core_sample_ratio = 0.05
            self.batch_capacity = min(10000, total_count // 20)

        logger.info(f"_adjust_initial_parameters base on total count: ({total_count}). "
                         f"min_cluster_size={self.min_cluster_size}, "
                         f"core_sample_ratio={self.core_sample_ratio}, "
                         f"batch_capacity={self.batch_capacity}")


    def _adjust_parameters(self, batch_size):
        new_min_cluster_size = max(50, int(batch_size * 0.005))
        if new_min_cluster_size != self.min_cluster_size:
            self.min_cluster_size = new_min_cluster_size
            logger.info(f"adjust min_cluster_size to {self.min_cluster_size}")

        total_core_samples = sum(len(cs) for cs in self.core_samples)

        MAX_CORE_SAMPLES = 10000

        if total_core_samples > MAX_CORE_SAMPLES:
            logger.info(f"Total core samples ({total_core_samples}) exceed the limit ({MAX_CORE_SAMPLES}), performing uniform sampling")

            total_batches = len(self.core_samples)
            if total_batches > 0:
                samples_per_batch = MAX_CORE_SAMPLES // total_batches
                new_core_samples = []

                for batch in self.core_samples:
                    if len(batch) <= samples_per_batch:
                        new_core_samples.append(batch)
                    else:
                        indices = np.random.choice(len(batch), size=samples_per_batch, replace=False)
                        new_core_samples.append(batch[indices])

                self.core_samples = new_core_samples
                new_total = sum(len(cs) for cs in self.core_samples)
                logger.info(f"Total core samples after sampling: {new_total}")

        elif len(self.core_samples) > 10:
            logger.info(f"Too many core sample batches, reduced from {len(self.core_samples)} to 5")
            kept_samples = []
            kept_count = 0

            for samples in reversed(self.core_samples):
                kept_samples.append(samples)
                kept_count += len(samples)
                if kept_count >= total_core_samples * 0.7:  # 保留70%的核心样本
                    break

            self.core_samples = list(reversed(kept_samples))
            logger.info(f"Core samples retained after reduction: {sum(len(cs) for cs in self.core_samples)}")

    def _final_clustering(self):
        if not self.core_samples or sum(len(cs) for cs in self.core_samples) == 0:
            raise ValueError("Not enough core samples accumulated, unable to perform final clustering")

        X = np.vstack(self.core_samples)
        logger.info(f"Final clustering using {X.shape[0]} core samples")

        if self.enable_monitoring:
            self.monitor.record_memory("final_clustering_start")

        final_min_cluster_size = max(5, min(50, int(X.shape[0] * 0.015)))  # 5%-8% of core samples
        logger.info(f"Final clustering parameters: min_cluster_size={final_min_cluster_size}")

        try:
            self.clusterer = hdbscan.HDBSCAN(
                min_cluster_size=final_min_cluster_size,
                min_samples=int(final_min_cluster_size * 0.1),
                cluster_selection_epsilon=0.05,
                cluster_selection_method='leaf',
                core_dist_n_jobs=8,
                prediction_data=True
            ).fit(X)

            labels = self.clusterer.labels_
            cluster_count = len(set(labels)) - (1 if -1 in labels else 0)
            noise_count = np.sum(labels == -1)
            noise_ratio = noise_count / len(labels)

            logger.info(f"Final clustering complete: {cluster_count} clusters found, "
                             f"{noise_count} noise points ({noise_ratio:.2%})")

            if self.enable_monitoring and X.shape[0] <= 10000:
                try:
                    if cluster_count > 1:
                        sample_idx = np.random.choice(range(len(X)), min(5000, len(X)), replace=False)
                        sample_X = X[sample_idx]
                        sample_labels = labels[sample_idx]
                        valid_mask = sample_labels != -1
                        if np.sum(valid_mask) > 100:
                            score = silhouette_score(
                                sample_X[valid_mask],
                                sample_labels[valid_mask],
                                sample_size=1000
                            )
                            logger.info(f"silhouette score: {score:.4f}")
                except Exception as e:
                    logger.warning(f"silhouette calculate error: {str(e)}")

        except Exception as e:
            logger.error(f"_final_clustering failed: {str(e)}")
            raise
        finally:
            del X
            self.core_samples = []

            if self.enable_monitoring:
                self.monitor.record_memory("final_clustering_end")

    def _update_all_labels(self):
        import numpy as np
        from sklearn.preprocessing import normalize
        ENABLE_PLOT = False

        total_updated = 0

        all_reduced_vectors = []
        all_labels = []
        all_doc_ids = []

        scroll_id = None
        batch_num = 0

        while True:
            vectors, doc_ids, scroll_id = self._fetch_batch(scroll_id)
            if vectors.size == 0:
                break

            normalized_vectors = normalize(vectors)

            if not hasattr(self, 'umap_reducer_2d'):
                # self.umap_reducer_2d = umap.UMAP(
                #     n_components=2,
                #     min_dist=0.5,  # 默认是0.1，增加到0.3-0.5
                #     n_neighbors=5,  # 默认通常是15，可以尝试减小到10-5
                #     random_state=42
                # )

                self.umap_reducer_2d = umap.UMAP(
                    n_components=2,
                    n_neighbors=15,
                    min_dist=0.05,
                    metric='cosine',
                    random_state=42
                )

                # self.umap_reducer_3d = umap.UMAP(
                #     n_components=3,
                #     n_neighbors=5,
                #     min_dist=0.5,
                #     spread=1.5,
                #     metric='cosine',
                #     random_state=42
                # )

                self.umap_reducer_3d = umap.UMAP(
                    n_components=3,
                    n_neighbors=4,
                    min_dist=0.8,
                    spread=1.5,
                    repulsion_strength=2.0,
                    metric='cosine',
                    random_state=42
                )

                reduced_vectors_2d = self.umap_reducer_2d.fit_transform(normalized_vectors)
                reduced_vectors_3d = self.umap_reducer_3d.fit_transform(normalized_vectors)
            else:
                reduced_vectors_2d = self.umap_reducer_2d.transform(normalized_vectors)
                reduced_vectors_3d = self.umap_reducer_3d.transform(normalized_vectors)

            reduced_vectors = self.umap_reducer.transform(normalized_vectors)
            labels, strengths = hdbscan.approximate_predict(
                self.clusterer, reduced_vectors)
            self.update_cluster_labels(doc_ids, labels, strengths, reduced_vectors_2d, reduced_vectors_3d)
            total_updated += len(doc_ids)

            all_reduced_vectors.append(reduced_vectors_2d)
            all_labels.extend(labels)
            all_doc_ids.extend(doc_ids)

            batch_num += 1
            logger.info(f"Label update progress: Batch {batch_num}, document count {len(doc_ids)}, total {total_updated}")

            del normalized_vectors, vectors

        if ENABLE_PLOT:
            if all_reduced_vectors:
                all_reduced_vectors = np.vstack(all_reduced_vectors)
                self._create_cluster_visualization(all_reduced_vectors, all_labels, all_doc_ids)

        unique_labels = np.unique(self.clusterer.labels_).size
        logger.info(f"Label update complete, a total of {total_updated} documents updated, unique_labels: {unique_labels}")
        return total_updated, unique_labels

    def update_cluster_labels(self, doc_ids, labels, strengths=None,
                              reduced_vectors_2d=None,
                              reduced_vectors_3d=None):
        if not doc_ids or len(doc_ids) == 0:
            return

        bulk_actions = []

        for i, (doc_id, label) in enumerate(zip(doc_ids, labels)):
            update_doc = {
                "cluster_id": int(label) if label != -1 else -1
            }

            if strengths is not None:
                update_doc["cluster_confidence"] = float(strengths[i])

            if reduced_vectors_2d is not None:
                if hasattr(reduced_vectors_2d[i], 'tolist'):
                    vector_values = reduced_vectors_2d[i].tolist()
                else:
                    vector_values = list(reduced_vectors_2d[i])

                update_doc["reduced_vectors"] = [float(val) for val in vector_values]

            if reduced_vectors_3d is not None:
                if hasattr(reduced_vectors_3d[i], 'tolist'):
                    vector_values_3d = reduced_vectors_3d[i].tolist()
                else:
                    vector_values_3d = list(reduced_vectors_3d[i])

                update_doc["reduced_vectors_3d"] = [float(val) for val in vector_values_3d]

            action = {
                "_op_type": "update",
                "_id": doc_id,
                "doc": update_doc,
                "doc_as_upsert": True
            }
            bulk_actions.append(action)

        try:
            success, errors = self.es.bulk(self.result_index_name, bulk_actions, raise_on_error=False)
            if errors:
                logger.warning(f"ES error count: {len(errors)}.")
            return success

        except Exception as e:
            logger.error(f"batch update es failed: {str(e)} .")
            return 0

    def _create_cluster_visualization(self, vectors_2d, labels, doc_ids):
        import matplotlib.pyplot as plt
        import numpy as np
        from matplotlib.colors import ListedColormap

        logger.info("start _create_cluster_visualization...")

        vectors_2d = np.array(vectors_2d)
        labels = np.array(labels)
        unique_labels = np.unique(labels)

        num_clusters = len(unique_labels)
        if -1 in unique_labels:
            num_clusters -= 1

        colors = plt.cm.rainbow(np.linspace(0, 1, max(num_clusters, 1)))
        color_map = {}
        color_idx = 0

        for label in unique_labels:
            if label == -1:
                color_map[label] = [0.7, 0.7, 0.7, 1.0]
            else:
                color_map[label] = colors[color_idx]
                color_idx += 1

        plt.figure(figsize=(12, 10))

        centroids = {}

        for label in unique_labels:
            mask = labels == label
            points = vectors_2d[mask]

            if label == -1:
                marker = 'x'
                label_name = "Noise"
            else:
                marker = 'o'
                label_name = f"Cluster {label}"
                centroids[label] = np.mean(points, axis=0)

            plt.scatter(
                points[:, 0], points[:, 1],
                color=color_map[label],
                marker=marker,
                label=f"{label_name} ({np.sum(mask)} dots)",
                alpha=0.6,
                s=30
            )

        for label, centroid in centroids.items():
            plt.scatter(
                centroid[0], centroid[1],
                color=color_map[label],
                marker='*',
                s=300,
                edgecolors='black',
                linewidths=1.5,
                alpha=1.0,
                zorder=10
            )

            plt.annotate(
                f"Cluster {label}",
                xy=(centroid[0], centroid[1]),
                xytext=(10, 10),
                textcoords='offset points',
                fontsize=12,
                fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="black", alpha=0.8),
                zorder=11
            )

        plt.title(f"Document Clustering Visualization (Total {len(vectors_2d)} documents, {num_clusters} clusters)")
        plt.xlabel("UMAP Dimension 1")
        plt.ylabel("UMAP Dimension 2")
        plt.legend(loc='upper right', bbox_to_anchor=(1.1, 1.05))
        plt.tight_layout()

        save_path = "cluster_visualization.png"
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Clustering visualization saved to: {save_path}")

        try:
            plt.show()
        except:
            pass

        plt.close()


class ClusterMonitor:

    def __init__(self):
        self.metrics = {
            'batch_size': [],
            'core_ratio': [],
            'cluster_count': [],
            'noise_ratio': [],
            'confidence_avg': [],
            'memory_usage': {},
            'timestamps': {}
        }
        self.start_time = time.time()

    def update(self, batch_data):
        for key, value in batch_data.items():
            if key in self.metrics:
                self.metrics[key].append(value)

    def record_memory(self, stage):
        memory_info = psutil.Process(os.getpid()).memory_info()
        self.metrics['memory_usage'][stage] = memory_info.rss / (1024 * 1024)  # MB
        self.metrics['timestamps'][stage] = time.time() - self.start_time

    def summary(self):
        logger = logging.getLogger('ClusterMonitor')

        logger.info("===== Clustering Monitor Summary =====")

        if self.metrics['memory_usage']:
            logger.info("Memory Usage (MB):")
            for stage, usage in self.metrics['memory_usage'].items():
                logger.info(f"  {stage}: {usage:.2f} MB")

        if self.metrics['batch_size']:
            avg_batch = sum(self.metrics['batch_size']) / len(self.metrics['batch_size'])
            logger.info(f"Average Batch Size: {avg_batch:.2f}")

        if self.metrics['core_ratio']:
            avg_core = sum(self.metrics['core_ratio']) / len(self.metrics['core_ratio'])
            logger.info(f"Average Core Sample Ratio: {avg_core:.2%}")

        if self.metrics['noise_ratio']:
            avg_noise = sum(self.metrics['noise_ratio']) / len(self.metrics['noise_ratio'])
            logger.info(f"Average Noise Ratio: {avg_noise:.2%}")

        if self.metrics['cluster_count']:
            logger.info(f"Number of Clusters: {self.metrics['cluster_count'][0]}")

        logger.info("========================")