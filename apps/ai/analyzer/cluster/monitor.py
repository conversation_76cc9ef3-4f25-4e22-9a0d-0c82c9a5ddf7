# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: monitor
# @Author: <PERSON><PERSON>
# @E-mail: <EMAIL>
# @Site: 
# @Time: 2025/4/24
# ---
import os
import time
import psutil
from lib_func.logger import logger

class ClusterMonitor:
    """
    聚类性能监控器
    """

    def __init__(self):
        self.metrics = {
            'batch_size': [],
            'core_ratio': [],
            'cluster_count': [],
            'noise_ratio': [],
            'confidence_avg': [],
            'memory_usage': {},
            'timestamps': {}
        }
        self.start_time = time.time()

    def update(self, batch_data):
        """
        更新批次监控指标
        """
        for key, value in batch_data.items():
            if key in self.metrics:
                self.metrics[key].append(value)

    def record_memory(self, stage):
        """
        记录内存使用情况
        """
        memory_info = psutil.Process(os.getpid()).memory_info()
        self.metrics['memory_usage'][stage] = memory_info.rss / (1024 * 1024)  # MB
        self.metrics['timestamps'][stage] = time.time() - self.start_time

    def summary(self):
        """
        Print monitoring summary
        """

        logger.info("===== Clustering Monitoring Summary =====")

        # Memory usage
        if self.metrics['memory_usage']:
            logger.info("Memory Usage (MB):")
            for stage, usage in self.metrics['memory_usage'].items():
                logger.info(f"  {stage}: {usage:.2f} MB")

        # Batch statistics
        if self.metrics['batch_size']:
            avg_batch = sum(self.metrics['batch_size']) / len(self.metrics['batch_size'])
            logger.info(f"Average batch size: {avg_batch:.2f}")

        # Core sample ratio
        if self.metrics['core_ratio']:
            avg_core = sum(self.metrics['core_ratio']) / len(self.metrics['core_ratio'])
            logger.info(f"Average core sample ratio: {avg_core:.2%}")

        # Noise ratio
        if self.metrics['noise_ratio']:
            avg_noise = sum(self.metrics['noise_ratio']) / len(self.metrics['noise_ratio'])
            logger.info(f"Average noise ratio: {avg_noise:.2%}")

        # Number of clusters
        if self.metrics['cluster_count']:
            logger.info(f"Number of clusters: {self.metrics['cluster_count'][0]}")

        logger.info("========================")