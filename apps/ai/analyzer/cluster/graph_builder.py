# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: graph_builder
# @Author: <PERSON><PERSON>
# @E-mail: <PERSON><PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2025/4/29
# ---
import numpy as np
import logging
from scipy.spatial import cKDTree
from elasticsearch import Elasticsearch, helpers
from typing import List, Dict, Tuple, Optional, Any
from lib_func.logger import logger

class GraphBuilder:

    # ES相关配置
    SCROLL_CONFIG = "5m"  # 游标有效时间

    def __init__(
            self,
            es_client: Elasticsearch,
            source_index: str,
            graph_index: str,
            x_field: str = "x",
            y_field: str = "y",
            id_field: str = "doc_id",
            batch_capacity: int = 1000,
            k_neighbors: int = 5,
            distance_threshold: Optional[float] = None,
            logger=None
    ):
        """
        初始化GraphBuilder

        Args:
            es_client: Elasticsearch客户端
            source_index: 存储原始坐标数据的索引
            graph_index: 存储图结构的索引
            x_field: x坐标在ES中的字段名
            y_field: y坐标在ES中的字段名
            id_field: 文档ID在ES中的字段名
            batch_capacity: 批处理容量
            k_neighbors: 为每个点保留的最近邻数量
            distance_threshold: 距离阈值，超过此值的边会被过滤
            logger: 日志记录器
        """
        self.es = es_client
        self.source_index = source_index
        self.graph_index = graph_index
        self.x_field = x_field
        self.y_field = y_field
        self.id_field = id_field
        self.batch_capacity = batch_capacity
        self.k_neighbors = k_neighbors
        self.distance_threshold = distance_threshold

    def setup_graph_index(self) -> None:
        # 定义图索引的映射
        mapping = {
            "mappings": {
                "properties": {
                    "doc_id": {"type": "keyword"},
                    "x": {"type": "float"},
                    "y": {"type": "float"},
                    "neighbors": {
                        "type": "nested",
                        "properties": {
                            "doc_id": {"type": "keyword"},
                            "weight": {"type": "float"}
                        }
                    }
                }
            }
        }

        # 检查索引是否存在
        if not self.es.indices.exists(index=self.graph_index):
            # 创建新索引
            self.es.indices.create(index=self.graph_index, body=mapping)
            logger.info(f"创建图索引: {self.graph_index}")
        else:
            # 可选: 更新映射
            logger.info(f"图索引 {self.graph_index} 已存在")

    def _fetch_batch(self, scroll_id=None):
        coords = []  # 坐标列表
        doc_ids = []  # 文档ID列表

        if scroll_id is None:
            # 初始查询
            query = {
                "_source": [self.id_field, self.x_field, self.y_field],
                "query": {"match_all": {}},
                "size": self.batch_capacity
            }

            try:
                # 假设es客户端有search_data_scroll方法
                response = self.es.search(
                    index=self.source_index,
                    body=query,
                    scroll=self.SCROLL_CONFIG
                )
                next_scroll_id = response["_scroll_id"]
                hits = response["hits"]["hits"]
            except Exception as e:
                logger.error(f"查询失败: {str(e)}")
                return np.array([]), [], None
        else:
            # 继续滚动查询
            try:
                response = self.es.scroll(
                    scroll_id=scroll_id,
                    scroll=self.SCROLL_CONFIG
                )
                next_scroll_id = response["_scroll_id"]
                hits = response["hits"]["hits"]
            except Exception as e:
                logger.error(f"滚动查询失败: {str(e)}")
                return np.array([]), [], None

        # 检查是否已经没有数据
        if not hits:
            if scroll_id:
                try:
                    self.es.clear_scroll(scroll_id=scroll_id)
                except Exception as e:
                    logger.warning(f"清除滚动失败: {str(e)}")
            return np.array([]), [], None

        # 提取坐标和ID
        for hit in hits:
            try:
                source = hit["_source"]
                doc_ids.append(source[self.id_field])
                coords.append([float(source[self.x_field]), float(source[self.y_field])])
            except (KeyError, ValueError) as e:
                logger.warning(f"提取数据失败: {str(e)}")
                continue

        logger.info(f"获取了 {len(coords)} 个文档。")
        return np.array(coords), doc_ids, next_scroll_id

    def build_neighbor_relations(self, coords: np.ndarray, doc_ids: List[str]) -> List[Dict[str, Any]]:
        """
        为一批坐标构建邻居关系

        Args:
            coords: 坐标数组
            doc_ids: 文档ID列表

        Returns:
            List[Dict]: 包含邻居关系的文档列表
        """
        if len(coords) == 0:
            return []

        # 构建KD树用于高效的最近邻搜索
        tree = cKDTree(coords)

        # 查询K+1个最近邻（包括自身）
        distances, indices = tree.query(coords, k=min(self.k_neighbors + 1, len(coords)))

        # 构建带有邻居关系的文档
        documents = []
        for i, doc_id in enumerate(doc_ids):
            # 收集邻居
            neighbors = []

            # 从索引1开始，跳过自身
            for j in range(1, len(indices[i])):
                neighbor_idx = indices[i][j]
                distance = distances[i][j]

                # 应用距离阈值过滤（如果设置）
                if self.distance_threshold is None or distance <= self.distance_threshold:
                    neighbors.append({
                        "doc_id": doc_ids[neighbor_idx],
                        "weight": float(distance)
                    })

            # 创建包含邻居关系的文档
            document = {
                "doc_id": doc_id,
                "x": float(coords[i][0]),
                "y": float(coords[i][1]),
                "neighbors": neighbors
            }

            documents.append(document)

        return documents

    def _bulk_index_documents(self, documents: List[Dict]) -> None:
        """
        批量索引文档到ES

        Args:
            documents: 要索引的文档列表
        """
        if not documents:
            return

        actions = [
            {
                "_index": self.graph_index,
                "_id": doc["doc_id"],  # 使用文档ID作为ES文档ID
                "_source": doc
            }
            for doc in documents
        ]

        try:
            success, failed = helpers.bulk(
                self.es,
                actions,
                stats_only=True
            )
            logger.info(f"索引成功: {success}, 失败: {failed}")
        except Exception as e:
            logger.error(f"批量索引失败: {str(e)}")

    def build_graph(self) -> None:
        """
        构建完整的图结构
        """
        # 确保图索引已设置
        self.setup_graph_index()

        # 追踪处理进度
        processed_count = 0
        scroll_id = None

        while True:
            # 获取一批坐标数据
            coords, doc_ids, scroll_id = self._fetch_batch(scroll_id)

            if len(coords) == 0:
                logger.info("数据处理完成")
                break

            # 构建邻居关系
            documents = self.build_neighbor_relations(coords, doc_ids)

            # 批量索引到ES
            self._bulk_index_documents(documents)

            # 更新计数
            processed_count += len(documents)
            logger.info(f"已处理 {processed_count} 个文档")

            # 如果没有更多数据，退出循环
            if scroll_id is None:
                break

    def get_random_subgraph(self, node_count: int = 500, include_neighbors: bool = True) -> Dict:
        """
        获取随机子图

        Args:
            node_count: 随机选择的节点数量
            include_neighbors: 是否包含邻居节点信息

        Returns:
            Dict: 包含节点和边的子图
        """
        # 1. 获取随机节点
        random_nodes = self._get_random_nodes(node_count)

        if not random_nodes:
            return {"nodes": [], "edges": []}

        # 2. 获取这些节点的详细信息（包括邻居）
        nodes_data = self._get_nodes_with_neighbors(random_nodes)

        # 3. 构建图数据结构
        graph_data = self._build_graph_data(nodes_data, include_neighbors)

        return graph_data

    def _get_random_nodes(self, count: int) -> List[str]:
        """
        获取随机节点ID

        Args:
            count: 要获取的节点数量

        Returns:
            List[str]: 随机节点ID列表
        """
        query = {
            "size": count,
            "query": {
                "function_score": {
                    "query": {"match_all": {}},
                    "random_score": {}
                }
            },
            "_source": ["doc_id"]
        }

        try:
            response = self.es.search(
                index=self.graph_index,
                body=query
            )

            return [hit["_source"]["doc_id"] for hit in response["hits"]["hits"]]
        except Exception as e:
            logger.error(f"获取随机节点失败: {str(e)}")
            return []

    def _get_nodes_with_neighbors(self, node_ids: List[str]) -> List[Dict]:
        """
        获取节点及其邻居信息

        Args:
            node_ids: 节点ID列表

        Returns:
            List[Dict]: 节点数据列表
        """
        query = {
            "ids": node_ids
        }

        try:
            response = self.es.mget(
                index=self.graph_index,
                body=query
            )

            # 提取所有文档
            return [doc["_source"] for doc in response["docs"] if doc["found"]]
        except Exception as e:
            logger.error(f"获取节点详情失败: {str(e)}")
            return []

    def _build_graph_data(self, nodes_data: List[Dict], include_neighbors: bool) -> Dict:
        """
        从节点数据构建图数据结构

        Args:
            nodes_data: 节点数据列表
            include_neighbors: 是否包含邻居节点

        Returns:
            Dict: 图数据结构
        """
        nodes = []
        edges = set()  # 使用集合避免重复边
        neighbor_ids = set()  # 收集需要额外获取的邻居节点ID
        main_node_ids = {node["doc_id"] for node in nodes_data}

        # 处理主要节点和它们的边
        for node in nodes_data:
            node_id = node["doc_id"]

            # 添加节点
            nodes.append({
                "id": node_id,
                "x": node["x"],
                "y": node["y"],
                "main": True  # 标记为主要节点
            })

            # 处理邻居关系
            if "neighbors" in node:
                for neighbor in node["neighbors"]:
                    neighbor_id = neighbor["doc_id"]
                    weight = neighbor["weight"]

                    # 构造边ID，确保唯一性
                    if node_id < neighbor_id:
                        edge_id = f"{node_id}-{neighbor_id}"
                    else:
                        edge_id = f"{neighbor_id}-{node_id}"

                    # 添加边
                    edges.add((edge_id, node_id, neighbor_id, weight))

                    # 如果邻居不在主节点列表中，记录下来
                    if neighbor_id not in main_node_ids:
                        neighbor_ids.add(neighbor_id)

        # 如果需要包含邻居节点，获取它们的详细信息
        neighbor_nodes = []
        if include_neighbors and neighbor_ids:
            neighbor_nodes = self._get_neighbor_nodes(list(neighbor_ids))

        # 合并所有节点
        all_nodes = nodes + [
            {
                "id": node["doc_id"],
                "x": node["x"],
                "y": node["y"],
                "main": False  # 标记为次要节点
            }
            for node in neighbor_nodes
        ]

        # 转换边集合为列表
        edge_list = [
            {
                "id": edge[0],
                "source": edge[1],
                "target": edge[2],
                "weight": edge[3]
            }
            for edge in edges
        ]

        return {
            "nodes": all_nodes,
            "edges": edge_list
        }

    def _get_neighbor_nodes(self, neighbor_ids: List[str]) -> List[Dict]:
        """
        获取邻居节点信息

        Args:
            neighbor_ids: 邻居节点ID列表

        Returns:
            List[Dict]: 邻居节点数据
        """
        if not neighbor_ids:
            return []

        query = {
            "ids": neighbor_ids
        }

        try:
            response = self.es.mget(
                index=self.graph_index,
                body=query,
                _source=["doc_id", "x", "y"]  # 只获取必要的字段
            )

            return [doc["_source"] for doc in response["docs"] if doc["found"]]
        except Exception as e:
            logger.error(f"获取邻居节点失败: {str(e)}")
            return []

    def get_node_with_neighbors(self, node_id: str) -> Dict:
        """
        获取单个节点及其所有邻居

        Args:
            node_id: 节点ID

        Returns:
            Dict: 节点及其邻居的信息
        """
        try:
            response = self.es.get(
                index=self.graph_index,
                id=node_id
            )

            if not response["found"]:
                return {}

            node_data = response["_source"]
            return self._build_single_node_graph(node_data)
        except Exception as e:
            logger.error(f"获取节点失败: {str(e)}")
            return {}

    def _build_single_node_graph(self, node_data: Dict) -> Dict:
        """
        为单个节点构建图数据

        Args:
            node_data: 节点数据

        Returns:
            Dict: 包含节点及其邻居的图数据
        """
        node_id = node_data["doc_id"]
        neighbors = node_data.get("neighbors", [])

        # 创建节点列表
        nodes = [{
            "id": node_id,
            "x": node_data["x"],
            "y": node_data["y"],
            "main": True
        }]

        # 创建边列表
        edges = []

        # 收集需要获取的邻居ID
        neighbor_ids = [n["doc_id"] for n in neighbors]

        # 如果有邻居，获取它们的信息
        if neighbor_ids:
            neighbor_nodes = self._get_neighbor_nodes(neighbor_ids)

            # 添加邻居节点
            for n in neighbor_nodes:
                nodes.append({
                    "id": n["doc_id"],
                    "x": n["x"],
                    "y": n["y"],
                    "main": False
                })

            # 添加边
            for neighbor in neighbors:
                edges.append({
                    "id": f"{node_id}-{neighbor['doc_id']}",
                    "source": node_id,
                    "target": neighbor["doc_id"],
                    "weight": neighbor["weight"]
                })

        return {
            "nodes": nodes,
            "edges": edges
        }

class GraphBuilderLauncher:
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger("graph_example")

    # 初始化ES客户端
    es_client = Elasticsearch(
        hosts=["http://localhost:9200"]
    )

    def build_graph():
        """构建图结构示例"""
        # 创建GraphBuilder实例
        builder = GraphBuilder(
            es_client=es_client,
            source_index="coordinates_index",  # 包含坐标数据的源索引
            graph_index="graph_structure",  # 用于存储图结构的目标索引
            x_field="x",  # x坐标字段名
            y_field="y",  # y坐标字段名
            id_field="doc_id",  # 文档ID字段名
            batch_capacity=2000,  # 每批处理的文档数
            k_neighbors=5,  # 每个点保留的邻居数量
            distance_threshold=0.5,  # 距离阈值(可选)
            logger=logger
        )

        # 构建图结构
        builder.build_graph()
        logger.info("图结构构建完成")

    def query_random_subgraph():
        """查询随机子图示例"""
        builder = GraphBuilder(
            es_client=es_client,
            source_index="coordinates_index",
            graph_index="graph_structure",
            logger=logger
        )

        # 获取500个随机节点及其邻居构成的子图
        subgraph = builder.get_random_subgraph(
            node_count=500,
            include_neighbors=True
        )

        print(f"获取了 {len(subgraph['nodes'])} 个节点和 {len(subgraph['edges'])} 条边")
        return subgraph

    def query_specific_node(node_id):
        """查询特定节点及其邻居示例"""
        builder = GraphBuilder(
            es_client=es_client,
            source_index="coordinates_index",
            graph_index="graph_structure",
            logger=logger
        )

        # 获取指定节点及其邻居
        node_graph = builder.get_node_with_neighbors(node_id)

        if node_graph:
            print(f"获取了节点 {node_id} 及其 {len(node_graph['edges'])} 个邻居")
        else:
            print(f"节点 {node_id} 不存在")

        return node_graph

    if __name__ == "__main__":
        # 构建图结构(仅需执行一次)
        # build_graph()

        # 查询随机子图
        subgraph = query_random_subgraph()