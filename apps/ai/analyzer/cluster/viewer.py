# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: viewer
# @Author: sunhao
# @E-mail: <EMAIL>
# @Site: 
# @Time: 2025/4/27
# ---
from flask import g

from apps.ai.analyzer.cluster.configs import VIEW_INDEX_NAME, VIEW_MAX_DOC_PER_CCLUSTER

import concurrent.futures
from typing import List, Dict, Any, Optional

from puppet.es_sdk import EsSdkPool
from lib_func.logger import logger


class DataCollector:
    def __init__(self, corpid):
        self.es_sdk_pool = EsSdkPool(corpid)
        self.index_name = VIEW_INDEX_NAME

    def get_distinct_clusters(self) -> List[int]:
        cluster_ids = set()
        scroll_id = None

        query_dict = {
            "_source": ["cluster_id"],
            "query": {"match_all": {}},
            "size": 10000
        }

        try:
            response = self.es_sdk_pool.search_data_scroll(
                index_name=self.index_name,
                query_dict=query_dict,
                scroll="1m"
            )

            scroll_id = response["_scroll_id"]
            hits = response["hits"]["hits"]

            for hit in hits:
                if '_source' in hit and 'cluster_id' in hit['_source']:
                    cluster_ids.add(hit['_source']['cluster_id'])

            while True:
                if not scroll_id:
                    break

                response = self.es_sdk_pool.scroll(
                    scroll_id=scroll_id,
                    scroll="1m"
                )
                scroll_id = response["_scroll_id"]
                hits = response["hits"]["hits"]

                if not hits:
                    break

                for hit in hits:
                    if '_source' in hit and 'cluster_id' in hit['_source']:
                        cluster_ids.add(hit['_source']['cluster_id'])

        except Exception as e:
            logger.info(f"get_distinct_clusters error: {e}")
        finally:
            if scroll_id:
                try:
                    self.es_sdk_pool.clear_scroll(scroll_id=scroll_id)
                except Exception as e:
                    logger.info(f"clear scroll error: {e}")

        return list(cluster_ids)

    def fetch_cluster_data(self,
                           cluster_id: int,
                           contains_doc_id: bool = False,
                           max_size: Optional[int] = None
                           ) -> Dict[str, Any]:
        if max_size is None:
            max_size = VIEW_MAX_DOC_PER_CCLUSTER

        query_dict = {
            "query": {
                "term": {
                    "cluster_id": cluster_id
                }
            },
            "_source": ["reduced_vectors", "reduced_vectors_3d"],
            "size": max_size
        }

        result = self.es_sdk_pool.search_data(self.index_name, query_dict)

        documents = []
        documents_3d = []
        if result and 'hits' in result and 'hits' in result['hits']:
            hits = result['hits']['hits']
            for hit in hits:
                if '_source' in hit and 'reduced_vectors' in hit['_source']:
                    doc = {"v": hit['_source']['reduced_vectors']}
                    if contains_doc_id:
                        doc["id"] = hit['_id']
                    documents.append(doc)

                if '_source' in hit and 'reduced_vectors_3d' in hit['_source']:
                    doc = {"v": hit['_source']['reduced_vectors_3d']}
                    if contains_doc_id:
                        doc["id"] = hit['_id']
                    documents_3d.append(doc)

        return {
            "cluster_id": cluster_id,
            # "documents": documents,
            "documents_3d": documents_3d
        }

    def fetch_all_clusters_data_parallel(self, size) -> List[Dict[str, Any]]:
        cluster_ids = self.get_distinct_clusters()
        results = []

        with concurrent.futures.ThreadPoolExecutor() as executor:
            future_to_cluster = {
                executor.submit(self.fetch_cluster_data, cluster_id, True, size): cluster_id
                for cluster_id in cluster_ids
            }

            for future in concurrent.futures.as_completed(future_to_cluster):
                try:
                    cluster_data = future.result()
                    results.append(cluster_data)
                except Exception as exc:
                    cluster_id = future_to_cluster[future]
                    logger.info(f"Error occurred while fetching data for cluster {cluster_id}: {exc}")

        return results

    def collect_and_get_results(self, size) -> List[Dict[str, Any]]:
        clusters_data = self.fetch_all_clusters_data_parallel(size)

        # result = {
        #     "total_clusters": len(clusters_data),
        #     "clusters": clusters_data
        # }

        return clusters_data

    def fetch_doc_view(self, doc_ids: list, size: int):
        query_dict = {
            "query": {
                "ids": {
                    "values": doc_ids
                }
            },
            "_source": ["reduced_vectors_3d"],
            "size": size
        }

        result = self.es_sdk_pool.search_data(self.index_name, query_dict)

        documents = []
        if result and 'hits' in result and 'hits' in result['hits']:
            hits = result['hits']['hits']
            for hit in hits:
                if '_source' in hit and 'reduced_vectors_3d' in hit['_source']:
                    doc = {
                        "id": hit['_id'],
                        "v": hit['_source']['reduced_vectors_3d']
                    }
                    documents.append(doc)

        return documents


    def fetch_data_similar(self, doc_id, size=50, min_score=0.5):
        try:
            get_query = {
                "query": {
                    "ids": {
                        "values": [doc_id]
                    }
                }
            }

            target_res = self.es_sdk_pool.search_data(VIEW_INDEX_NAME, get_query)
            hits = target_res.get("hits", {}).get("hits", [])

            if not hits:
                logger.info(f"Document {doc_id} does not exist")
                return []

            target_doc = hits[0]
            if "reduced_vectors_3d" not in target_doc.get("_source", {}):
                logger.info(f"Document {doc_id} does not contain reduced_vectors_3d")
                return []

            target_vector = target_doc["_source"]["reduced_vectors_3d"]

            sim_query = {
                "_source": {
                    "includes": ["cluster_id", "cluster_confidence", "reduced_vectors_3d"]
                },
                "query": {
                    "bool": {
                        "must": [
                            {
                                "script_score": {
                                    "query": {
                                        "match_all": {}
                                    },
                                    "script": {
                                        "source": "cosineSimilarity(params.query_vector, 'reduced_vectors_3d') + 1.0",
                                        "params": {
                                            "query_vector": target_vector
                                        }
                                    }
                                }
                            }
                        ],
                        "must_not": [
                            {
                                "ids": {
                                    "values": [doc_id]
                                }
                            }
                        ]
                    }
                },
                "size": size,
                "min_score": min_score,
                "sort": [
                    {"_score": {"order": "desc"}}
                ]
            }

            search_result = self.es_sdk_pool.search_data(VIEW_INDEX_NAME, sim_query)
            result_hits = search_result.get("hits", {}).get("hits", [])

            logger.info(f"Querying similar documents for document {doc_id}, found {len(result_hits)} results")
            return result_hits

        except Exception as e:
            logger.info(f"Error occurred while querying similar documents: {e}")
            return []





