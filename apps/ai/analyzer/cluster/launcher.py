# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: launcher
# @Author: sunhao
# @E-mail: <EMAIL>
# @Site: 
# @Time: 2025/4/24
# ---
from apps.ai.analyzer.cluster.classify import LargeScaleDocumentClusteringLauncher
from apps.ai.analyzer.cluster.cluster import LargeScaleDocumentClustering
from apps.ai.analyzer.cluster.configs import PROCESSED_INDEX, VIEW_INDEX_NAME
from lib_func.logger import logger

class ClusterMonitorLauncher:
    def __init__(self,
                 corpid,
                 mode):
        # core params
        self.corpid = corpid
        self.mode = mode


    def launch(self, clean_up=False, cluster_only=False):
        logger.info("Cluster Monitor Start...")

        cluster = LargeScaleDocumentClusteringLauncher(
            corpid=self.corpid,
            n_clusters=8,
            batch_size=100000,
            max_iter=100)

        if clean_up:
            logger.info("Cluster Monitor Cleanup Start...")
            cluster.clean_up()

        if not cluster_only:
            logger.info("Cluster Launcher Start...")
            cluster.processing()

        logger.info("Large Scale Document Clustering Start...")
        clusterer = LargeScaleDocumentClustering(
            corpid=self.corpid,
            processed_index_name=PROCESSED_INDEX,
            result_index_name=VIEW_INDEX_NAME,
            batch_capacity=500,
            enable_monitoring=True
        )
        updated_count, labels_count = clusterer.cluster_documents()
        logger.info(f"Clustering complete, {updated_count} documents processed in total")
        return updated_count, labels_count