PUT jusure_pro_2_clustered_documents
{
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 1
  },
  "mappings": {
    "properties": {
      "cluster_confidence": {
        "type": "float"
      },
      "cluster_id": {
        "type": "long"
      },
      "reduced_vectors": {
        "type": "dense_vector",
        "dims": 2,
        "index": true,
        "similarity": "cosine"
      }
    }
  }
}



PUT jusure_pro_2_knowledge_processed_vectors
{
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 1
  },
  "mappings": {
    "properties": {
      "aggregated_vector": {
        "type": "dense_vector",
        "dims": 1024,
        "index": true,
        "similarity": "cosine"
      },
      "cluster_id": {
        "type": "integer"
      },
      "doc_id": {
        "type": "keyword"
      },
      "processed_timestamp": {
        "type": "date"
      },
      "umap_vector": {
        "type": "dense_vector",
        "dims": 2,
        "index": true,
        "similarity": "cosine"
      },
      "reduced_vectors_3d": {
          "type": "dense_vector",
          "dims": 3,
          "index": true,
          "similarity": "cosine"
      }
    }
  }
}


CREATE TABLE jusure_pro_2.aigc_knowledge_galaxy (
	id bigint NOT NULL COMMENT '知识星河表id',
	status int DEFAULT 0 NOT NULL COMMENT '构建状态',
	cluster_total int NULL COMMENT '聚类总数',
	doc_total int NULL COMMENT '作业完成数',
	tp_user_id varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '添加人',
	add_time datetime DEFAULT CURRENT_TIMESTAMP  NULL COMMENT '添加时间',
	update_time datetime DEFAULT CURRENT_TIMESTAMP  on update CURRENT_TIMESTAMP NULL COMMENT '更新时间',
	build_context text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '构建信息',
	CONSTRAINT `PRIMARY` PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci
COMMENT='知识星河构建表';


CREATE TABLE `aigc_knowledge_galaxy_record` (
  `record_id` bigint NOT NULL COMMENT '记录id',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `tp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作用户id',
  `state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行状态',
  `search_log` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '查询结果',
  PRIMARY KEY (`record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识星河查询记录表';
