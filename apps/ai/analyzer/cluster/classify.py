# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: classify
# @Author: sunhao
# @E-mail: <EMAIL>
# @Site: 
# @Time: 2025/3/28
# ---

import numpy as np
import time
import json
from elasticsearch import Elasticsearch, helpers
from flask import g
from sklearn.cluster import MiniBatchKMeans
import hdbscan
from sklearn.preprocessing import normalize
from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
import umap
import pickle
import base64
from lib_func.logger import logger

from apps.ai.analyzer.cluster.configs import CLUSTER_TYPE, SOURCE_INDEX, PROCESSED_INDEX, SINGLE_DOC_CALCULATE_CHUNKS, \
    SCROLL_CONFIG, VECTOR_DIM, VECTOR_DIM_UMAP, VIEW_INDEX_NAME
from puppet.es_sdk import EsSdkPool


class LargeScaleDocumentClustering:
    def __init__(self,
                 corpid,
                 index_name,
                 processed_index_name,
                 n_clusters,
                 batch_size=1000,
                 max_iter=50):

        self.es = EsSdkPool(corpid)
        self.index_name = index_name
        self.processed_index_name = processed_index_name

        self.n_clusters = n_clusters
        self.batch_size = batch_size
        self.max_iter = max_iter
        self.cluster_type = CLUSTER_TYPE

        self.kmeans = MiniBatchKMeans(
            n_clusters=n_clusters,
            batch_size=batch_size,
            max_iter=max_iter,
            random_state=42
        )

        self.clusterer = hdbscan.HDBSCAN(
            min_cluster_size=50,
            min_samples=10,
            cluster_selection_method='eom',
            metric='cosine',
            # prediction_data=True
        )

        self.umap_reducer = umap.UMAP(
            n_components=2,
            n_neighbors=15,
            min_dist=0.05,
            metric='cosine',
            random_state=42
        )

        self.cluster_centers_ = None
        self.labels_ = None
        self.document_ids = []

    def create_processed_index(self):
        vector_dim = VECTOR_DIM
        vector_dim_umap = VECTOR_DIM_UMAP
        if not self.es.index_exists(self.processed_index_name):
            mappings = {
                "properties": {
                    "doc_id": {"type": "keyword"},
                    "aggregated_vector": {"type": "dense_vector", "dims": vector_dim},
                    "umap_vector": {"type": "dense_vector", "dims": vector_dim_umap},
                    "cluster_id": {"type": "integer"},
                    "processed_timestamp": {"type": "date"}
                }
            }

            self.es.crate_index(
                index_name=self.processed_index_name,
                mapping=mappings
            )
            logger.info(f"Create index {self.processed_index_name} success.")
        else:
            logger.info(f"Already exist index {self.processed_index_name}.")

    def aggregate_document_vectors(self, vectors):
        if len(vectors) == 0:
            return None

        valid_vectors = [v for v in vectors if v is not None]
        if len(valid_vectors) == 0:
            return None

        return np.mean(valid_vectors, axis=0)

    def get_all_document_ids_paginated(self, max_num):
        basic_query_size = 5000
        query = {
            "_source": ["doc_id"],
            "query": {
                "bool": {
                    "must": [{"term": {"delete_flag": False}}]
                }
            },
            "size": basic_query_size
        }

        response = self.es.search_data_scroll(
            index_name=self.index_name,
            query_dict=query,
            scroll=SCROLL_CONFIG
        )

        scroll_id = response["_scroll_id"]
        hits = response["hits"]["hits"]
        unique_doc_ids = set()
        total_processed = 0

        for hit in hits:
            unique_doc_ids.add(hit["_source"]["doc_id"])
            if len(unique_doc_ids) >= max_num:
                break

        total_processed += len(hits)
        logger.info(f"Initial batch: Processed {len(hits)} documents, found {len(unique_doc_ids)} unique doc_ids")

        batch_count = 1
        while len(hits) > 0 and len(unique_doc_ids) < max_num:
            batch_count += 1
            response = self.es.scroll(
                scroll_id=scroll_id,
                scroll=SCROLL_CONFIG
            )

            scroll_id = response["_scroll_id"]
            hits = response["hits"]["hits"]

            before_count = len(unique_doc_ids)

            for hit in hits:
                unique_doc_ids.add(hit["_source"]["doc_id"])
                if len(unique_doc_ids) >= max_num:
                    break

            total_processed += len(hits)
            new_unique = len(unique_doc_ids) - before_count

            if len(hits) > 0:
                logger.info(
                    f"Batch {batch_count}: Processed {len(hits)} documents, added {new_unique} new unique doc_ids, total {len(unique_doc_ids)}")
                logger.info(f"Current progress: {len(unique_doc_ids)}/{max_num}")

        self.es.clear_scroll(
            scroll_id=scroll_id
        )
        doc_id_list = list(unique_doc_ids)
        if len(doc_id_list) > max_num:
            doc_id_list = doc_id_list[:max_num]

        return doc_id_list

    def fetch_document_vectors(self, doc_ids):
        size_limit = SINGLE_DOC_CALCULATE_CHUNKS
        doc_vectors = {}

        for doc_id in doc_ids:
            query = {
                "_source": {
                    "includes": ["chunk_id", "doc_id", "vector"]
                },
                "query": {
                    "bool": {
                        "must": [
                            {
                                "term": {
                                    "doc_id": doc_id
                                }
                            },
                            {
                                "term": {
                                    "delete_flag": False
                                }
                            }
                        ]
                    }
                },
                "size": size_limit
            }

            response = self.es.search_data(self.index_name, query)
            hits = response['hits']['hits']

            vectors = []
            for hit in hits:
                if 'vector' in hit['_source'] and hit['_source']['vector'] is not None:
                    vector = hit['_source']['vector']
                    vectors.append(vector)

            if vectors:  # 只有当vectors非空时才处理
                aggregated_vector = self.aggregate_document_vectors(vectors)
                if aggregated_vector is not None:  # 只有当聚合结果非None时才存储
                    doc_vectors[doc_id] = aggregated_vector

        return doc_vectors

    def preprocess_documents(self):
        chunk_size = 100
        io_period = 1000
        max_num = 100000
        self.create_processed_index()

        logger.info("get doc info...")
        all_doc_ids = self.get_all_document_ids_paginated(max_num=max_num)
        logger.info(f"Find {len(all_doc_ids)} docs")

        total_docs = len(all_doc_ids)
        processed_count = 0
        vectors_for_umap = []
        doc_ids_for_umap = []

        for i in range(0, total_docs, chunk_size):
            chunk_doc_ids = all_doc_ids[i:i + chunk_size]
            logger.info(f"Processing batch {i // chunk_size + 1}, total {len(chunk_doc_ids)} documents")

            doc_vectors = self.fetch_document_vectors(chunk_doc_ids)

            valid_count = 0
            for doc_id, vector in doc_vectors.items():
                if vector is not None:
                    vectors_for_umap.append(vector)
                    doc_ids_for_umap.append(doc_id)
                    valid_count += 1

            processed_count += len(doc_vectors)
            logger.info(
                f"Processed {processed_count}/{total_docs} documents, {valid_count} valid vectors in this batch")

            if len(vectors_for_umap) >= io_period or (i + chunk_size >= total_docs and vectors_for_umap):
                logger.info(f"Storing {len(vectors_for_umap)} vectors")
                self.process_and_store_vectors(vectors_for_umap, doc_ids_for_umap)
                vectors_for_umap = []
                doc_ids_for_umap = []

        logger.info("preprocess_documents Done!")


    def process_and_store_vectors(self, vectors, doc_ids):
        logger.info(f"Performing UMAP dimensionality reduction on {len(vectors)} vectors...")

        normalized_vectors = normalize(vectors)

        umap_vectors = self.umap_reducer.fit_transform(normalized_vectors)

        actions = []
        timestamp = time.strftime('%Y-%m-%dT%H:%M:%S', time.localtime())

        for i, doc_id in enumerate(doc_ids):
            action = {
                "_id": doc_id,
                "_source": {
                    "doc_id": doc_id,
                    "aggregated_vector": vectors[i].tolist(),
                    "umap_vector": umap_vectors[i].tolist(),
                    "processed_timestamp": timestamp
                }
            }
            actions.append(action)

        try:
            success, failed = self.es.bulk_index(self.processed_index_name, actions)
            logger.info(f"Successfully indexed {success} documents, {failed} failures")
        except Exception as e:
            logger.info(f"An error occurred while indexing documents: {str(e)}")

    def cluster_documents(self):
        logger.info("Start cluster_documents...")

        all_vectors = []
        all_doc_ids = []

        search_after = None
        while True:
            query = {
                "_source": ["doc_id", "aggregated_vector"],
                "query": {"match_all": {}},
                "size": 5000,
                "sort": [{"doc_id": "asc"}]
            }
            if search_after:
                query["search_after"] = [search_after]

            response = self.es.search_data(self.processed_index_name, query)
            hits = response['hits']['hits']
            if not hits:
                break

            for hit in hits:
                all_doc_ids.append(hit['_source']['doc_id'])
                all_vectors.append(hit['_source']['aggregated_vector'])

            search_after = hits[-1]['sort'][0]

        # Perform clustering
        X = np.array(all_vectors)
        X_normalized = normalize(X)

        self.clusterer.fit(X_normalized)
        labels = self.clusterer.labels_

        self.update_cluster_labels(all_doc_ids, labels)

        self.save_hdbscan_metadata()

        return len(all_doc_ids)


    def save_hdbscan_metadata(self):
        model_bytes = pickle.dumps(self.clusterer)
        model_b64 = base64.b64encode(model_bytes).decode('utf-8')

        unique_labels, counts = np.unique(
            self.clusterer.labels_[self.clusterer.labels_ != -1],
            return_counts=True
        )

        stats = {
            "n_clusters": len(unique_labels),
            "noise_points": np.sum(self.clusterer.labels_ == -1),
            "cluster_distribution": dict(zip(unique_labels.tolist(), counts.tolist())),
            "model_base64": model_b64,
            "timestamp": time.strftime('%Y-%m-%d %H:%M:%S')
        }

        with open('hdbscan_metadata.json', 'w') as f:
            json.dump(stats, f)


    def update_cluster_labels(self, doc_ids, labels):
        actions = []

        for doc_id, label in zip(doc_ids, labels):
            action = {
                "_id": doc_id,
                "_op_type": "update",
                "doc": {
                    "cluster_id": int(label) if label != -1 else None
                }
            }
            actions.append(action)

        try:
            success, failed = self.es.bulk_index(self.processed_index_name, actions)
            if failed:
                logger.info(f"There were {failed} failures when updating cluster labels")
        except Exception as e:
            logger.info(f"An error occurred while updating cluster labels: {str(e)}")


    def save_clustering_statistics(self):
        cluster_counts = {}

        for cluster_id in range(self.n_clusters):
            query = {
                "query": {
                    "term": {
                        "cluster_id": cluster_id
                    }
                },
                "size": 0
            }

            response = self.es.search_data(self.processed_index_name, query)
            count = response['hits']['total']['value']
            cluster_counts[cluster_id] = count

        stats = {
            "cluster_counts": cluster_counts,
            "total_documents": sum(cluster_counts.values()),
            "num_clusters": self.n_clusters,
            "timestamp": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
        }

        with open('clustering_statistics.json', 'w') as f:
            json.dump(stats, f, indent=2)

        logger.info("Saved to clustering_statistics.json")

    def evaluate_clustering(self):
        labels = self.clusterer.labels_
        valid_labels = labels[labels != -1]

        metrics = {
            "davies_bouldin_score": davies_bouldin_score("X_normalized", labels),
            "noise_ratio": np.mean(labels == -1),
            "cluster_stability": self.clusterer.probabilities_.mean()
        }

        if hasattr(self.clusterer, 'relative_validity_'):
            metrics["relative_validity"] = self.clusterer.relative_validity_

        logger.info(json.dumps(metrics, indent=2))
        return metrics

    def _evaluate_clustering(self, sample_size=5000):
        logger.info("_evaluate_clustering...")

        query = {
            "_source": ["doc_id", "aggregated_vector", "cluster_id"],
            "query": {"match_all": {}},
            "size": sample_size
        }

        response = self.es.search_data(self.processed_index_name, query)
        hits = response['hits']['hits']

        if len(hits) < 2:
            logger.info("The sample size is insufficient to evaluate")
            return {}

        vectors = []
        labels = []

        for hit in hits:
            if 'aggregated_vector' in hit['_source'] and 'cluster_id' in hit['_source']:
                vectors.append(hit['_source']['aggregated_vector'])
                labels.append(hit['_source']['cluster_id'])

        if len(vectors) < 2 or len(np.unique(labels)) < 2:
            logger.info("Insufficient number of samples or clusters, unable to compute evaluation metrics")
            return {}

        normalized_vectors = normalize(vectors)

        metrics = {
            'silhouette_score': silhouette_score(normalized_vectors, labels),
            'calinski_harabasz_score': calinski_harabasz_score(normalized_vectors, labels)
        }

        logger.info("\nClustering evaluation index:")
        for metric_name, value in metrics.items():
            logger.info(f"{metric_name}: {value:.4f}")

        return metrics

    def export_visualization_data(self, output_file='visualization_data.json'):
        logger.info("export_visualization_data...")

        query = {
            "_source": ["doc_id", "umap_vector", "cluster_id"],
            "query": {"match_all": {}},
            "size": 10000
        }

        response = self.es.search_data(self.processed_index_name, query)
        hits = response['hits']['hits']

        visualization_data = {
            "points": [],
            "clusters": {}
        }

        for hit in hits:
            if 'umap_vector' in hit['_source'] and 'cluster_id' in hit['_source']:
                point = {
                    "id": hit['_source']['doc_id'],
                    "x": hit['_source']['umap_vector'][0],
                    "y": hit['_source']['umap_vector'][1],
                    "cluster": hit['_source']['cluster_id']
                }
                visualization_data["points"].append(point)

                cluster_id = hit['_source']['cluster_id']
                if cluster_id not in visualization_data["clusters"]:
                    visualization_data["clusters"][cluster_id] = {
                        "count": 0,
                        "sum_x": 0,
                        "sum_y": 0
                    }

                visualization_data["clusters"][cluster_id]["count"] += 1
                visualization_data["clusters"][cluster_id]["sum_x"] += hit['_source']['umap_vector'][0]
                visualization_data["clusters"][cluster_id]["sum_y"] += hit['_source']['umap_vector'][1]

        for cluster_id, data in visualization_data["clusters"].items():
            if data["count"] > 0:
                data["center_x"] = data["sum_x"] / data["count"]
                data["center_y"] = data["sum_y"] / data["count"]
                del data["sum_x"]
                del data["sum_y"]

        with open(output_file, 'w') as f:
            json.dump(visualization_data, f)

        logger.info(f"Saved to {output_file}")


class LargeScaleDocumentClusteringLauncher:

    def __init__(self,
                 corpid,
                 n_clusters,
                 batch_size,
                 max_iter):
        # core params
        self.n_clusters = n_clusters
        self.batch_size = batch_size
        self.max_iter = max_iter
        self.es = EsSdkPool(corpid)
        self.corpid = corpid


    def processing(self):
        clusterer = LargeScaleDocumentClustering(
            corpid=self.corpid,
            index_name=SOURCE_INDEX,
            processed_index_name=PROCESSED_INDEX,
            n_clusters=self.n_clusters,
            batch_size=self.batch_size,
            max_iter=self.max_iter
        )

        logger.info("==== Starting Preprocessing Phase ====")
        start_time = time.time()
        clusterer.preprocess_documents()
        end_time = time.time()
        logger.info(f"Preprocessing phase took: {end_time - start_time:.2f} seconds")

        # logger.info("\n==== Starting Clustering Phase ====")
        # start_time = time.time()
        # total_clustered = clusterer.cluster_documents()
        # end_time = time.time()
        # logger.info(f"Clustering phase took: {end_time - start_time:.2f} seconds")
        # logger.info(f"Total documents clustered: {total_clustered}")
        #
        # logger.info("\n==== Evaluating Clustering Performance ====")
        # metrics = clusterer.evaluate_clustering(sample_size=5000)
        # clusterer.export_visualization_data()

        logger.info("\n==== All Processing Completed ====")

    def clean_up(self):
        index_1 = PROCESSED_INDEX
        index_2 = VIEW_INDEX_NAME
        query_dict = {"query": {"match_all": {}}}

        result_1 = self.es.delete_data(index_1, query_dict)
        result_2 = self.es.delete_data(index_2, query_dict)

        if result_1:
            logger.info(f"Successfully deleted all data under {index_1}")
        else:
            logger.info(f"Failed to delete data under {index_1}")

        if result_2:
            logger.info(f"Successfully deleted all data under {index_2}")
        else:
            logger.info(f"Failed to delete data under {index_2}")



class ElasticsearchClient:

    def __init__(self, hosts, username=None, password=None):
        """
        初始化ES客户端

        :param hosts: ES主机地址，如['http://localhost:9200']
        :param username: ES用户名
        :param password: ES密码
        """
        self.es = Elasticsearch(
            hosts=hosts,
            basic_auth=(username, password) if username and password else None
        )

    def search_data(self, index_name, query):
        """
        执行ES查询

        :param index_name: 索引名称
        :param query: 查询语句
        :return: 查询结果
        """
        return self.es.search(index=index_name, body=query)

    def index_exists(self, index_name):
        """
        检查索引是否存在

        :param index_name: 索引名称
        :return: 是否存在
        """
        return self.es.indices.exists(index=index_name)

    def create_index(self, index_name, settings=None, mappings=None):
        """
        创建索引

        :param index_name: 索引名称
        :param settings: 索引设置
        :param mappings: 索引映射
        :return: 创建结果
        """
        body = {}
        if settings:
            body["settings"] = settings
        if mappings:
            body["mappings"] = mappings

        return self.es.indices.create(index=index_name, body=body)

    def bulk_index(self, actions):
        """
        批量索引文档

        :param actions: 批量操作
        :return: 批量操作结果
        """
        return helpers.bulk(self.es, actions)