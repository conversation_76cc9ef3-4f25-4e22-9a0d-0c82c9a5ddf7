# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: semantic_vectorization
# @Author: sunhao
# @E-mail: <EMAIL>
# @Site: 
# @Time: 2025/2/20
# ---
from enum import Enum
from typing import Any, Dict
from apps.ai.analyzer.config import Config
from lib_func.logger import logger


class SemanticVectorization():
    def __init__(self, get_ebd_func, es):
        self.default_local_emb_model_path = 'bge-large-zh-v1.5'
        self.get_ebd_func = get_ebd_func
        self.es = es

    def classify_content(self, input_data: Any) -> Dict[str, float]:
        # Implementation for semantic vectorization
        pass

    @staticmethod
    def weighted_average(doc_scores):
        weights = {}
        for doc_id, scores in doc_scores.items():
            max_score = max(scores.values())
            weights[doc_id] = max_score

        total_weight = sum(weights.values())
        weights = {k: v / total_weight for k, v in weights.items()}

        final_scores = {label: 0.0 for label in doc_scores[list(doc_scores.keys())[0]]}
        for doc_id, scores in doc_scores.items():
            for label, score in scores.items():
                final_scores[label] += score * weights[doc_id]

        total = sum(final_scores.values())
        return {k: v / total for k, v in final_scores.items()}

    @staticmethod
    def softmax(doc_scores, temperature=0.1):
        import numpy as np

        label_scores = {label: [] for label in doc_scores[list(doc_scores.keys())[0]]}
        for scores in doc_scores.values():
            for label, score in scores.items():
                label_scores[label].append(score)

        mean_scores = {label: np.mean(scores) for label, scores in label_scores.items()}

        scores_array = np.array(list(mean_scores.values()))
        exp_scores = np.exp(scores_array / temperature)
        softmax_scores = exp_scores / exp_scores.sum()

        return dict(zip(mean_scores.keys(), softmax_scores))

    @staticmethod
    def top_k(doc_scores, k=3):
        import numpy as np

        doc_max_scores = {doc_id: max(scores.values()) for doc_id, scores in doc_scores.items()}

        top_docs = sorted(doc_max_scores.items(), key=lambda x: x[1], reverse=True)[:k]
        top_doc_ids = [doc_id for doc_id, _ in top_docs]

        final_scores = {label: [] for label in doc_scores[list(doc_scores.keys())[0]]}
        for doc_id in top_doc_ids:
            for label, score in doc_scores[doc_id].items():
                final_scores[label].append(score)

        mean_scores = {label: np.mean(scores) for label, scores in final_scores.items()}
        total = sum(mean_scores.values())
        return {k: v / total for k, v in mean_scores.items()}

    @staticmethod
    def quantile_weighted(doc_scores):
        import numpy as np

        label_scores = {label: [] for label in doc_scores[list(doc_scores.keys())[0]]}
        for scores in doc_scores.values():
            for label, score in scores.items():
                label_scores[label].append(score)

        final_scores = {}
        for label, scores in label_scores.items():
            q75 = np.percentile(scores, 75)
            q25 = np.percentile(scores, 25)
            iqr = q75 - q25

            final_scores[label] = np.mean(scores) * (1 + iqr)

        total = sum(final_scores.values())
        return {k: v / total for k, v in final_scores.items()}

    SCRIPT_SOURCE = """
                    double dotProduct = 0.0;
                    double norm1 = 0.0;
                    double norm2 = 0.0;
                    def docVector = doc.vector.vectorValue;
                    for (int i = 0; i < params.label_vector.length; i++) {
                        dotProduct += params.label_vector[i] * docVector[i];
                        norm1 += params.label_vector[i] * params.label_vector[i];
                        norm2 += docVector[i] * docVector[i];
                    }
                    return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
                    """

    @staticmethod
    def create_vector_classification_queries(knowledge_id, label_vectors, size=1000):
        queries = []
        for label, vector in label_vectors.items():
            query = {
                "size": size,
                "query": {
                    "script_score": {
                        "query": {
                            "term": {
                                "knowledge_id": knowledge_id
                            }
                        },
                        "script": {
                            "source": SemanticVectorization.SCRIPT_SOURCE,
                            "params": {
                                "label_vector": vector
                            }
                        }
                    }
                },
                "_source": ["id"]
            }

            queries.append((label, query))
        return queries

    def handle_analysis(self, knowledge_id):
        try:
            labels = Config().dataset_categories
            label_vectors = {label: self.get_ebd_func(label) for label in labels}
            queries = SemanticVectorization.create_vector_classification_queries(knowledge_id, label_vectors)
            index_name = 'knowledge_' + self.default_local_emb_model_path

            results = {}
            for label, query in queries:
                response = self.es.search_data(index_name, query)
                results[label] = response['hits']['hits']

            doc_scores = {}

            for label in labels:
                for hit in results[label]:
                    doc_id = hit['_id']
                    score = hit['_score']

                    if doc_id not in doc_scores:
                        doc_scores[doc_id] = {}
                    doc_scores[doc_id][label] = score

            class Strategy(Enum):
                SOFTMAX = 'softmax'
                WEIGHTED_AVERAGE = 'weighted_average'
                TOP_K = 'top_k'
                QUANTILE_WEIGHTED = 'quantile_weighted'

            strategy_map = {
                Strategy.SOFTMAX: SemanticVectorization.softmax,
                Strategy.WEIGHTED_AVERAGE: SemanticVectorization.weighted_average,
                Strategy.TOP_K: SemanticVectorization.top_k,
                Strategy.QUANTILE_WEIGHTED: SemanticVectorization.quantile_weighted,
            }

            def apply_strategy(doc_scores, strategy: Strategy, **kwargs):
                strategy_function = strategy_map.get(strategy)
                if strategy_function:
                    return strategy_function(doc_scores, **kwargs)
                else:
                    raise ValueError(f"Strategy '{strategy.value}' not found.")

            def transform_dict(original_dict):
                initial_indexed_dict = {i: value for i, value in enumerate(original_dict.values())}
                indexed_items = list(initial_indexed_dict.items())
                sorted_items = sorted(indexed_items, key=lambda x: x[1], reverse=True)
                final_scores = {item[0]: item[1] for item in sorted_items}
                return final_scores

            chosen_strategy = Strategy.SOFTMAX
            scores = apply_strategy(doc_scores, chosen_strategy)
            final_scores = transform_dict(scores)
            return final_scores

        except Exception as e:
            logger.error(e)
            return {}



