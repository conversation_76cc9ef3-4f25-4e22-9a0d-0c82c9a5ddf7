# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: urls.py
# @Author: <PERSON><PERSON>i<PERSON><PERSON>
# @E-mail: <EMAIL>
# @Site: 
# @Time: 3月 28, 2024
# ---
from apps import api
from apps.ai.knowledge import *
from apps.ai.app import *
from apps.ai.agent_demo import *
from apps.ai.agent import *
from apps.ai.qa import *
from apps.ai.minio import *
from apps.ai.celery_num import *
from apps.ai.recommend_opt import RecommendKnowledge
from apps.ai.util import *
from apps.ai.chat_record import *
from apps.ai.flow import *


api.add_resource(TestView, '/test')
api.add_resource(TestStreamView, '/test/stream')
# 知识库
api.add_resource(KnowledgeView, '/ai/knowledge')
# 全部知识库查询接口
api.add_resource(KnowledgeAllView, '/ai/knowledge/all')

# 知识库文档
api.add_resource(KnowledgeDocumentView, '/ai/knowledge/doc')

# 知识库文档 增加文档清洗后的文档切片
api.add_resource(KnowledgeDocumentViewV1, '/ai/knowledge/docv1')

# 知识库 视频关键帧图片信息提取
api.add_resource(KnowledgeVideoFrameView, '/ai/knowledge/video/frame')

# Minio 预签名文件上传
api.add_resource(MinIOUploadView, '/ai/files/upload')

# 文档切片
api.add_resource(KnowledgeDocumentChunkView, '/ai/knowledge/doc/chunk')

api.add_resource(KnowledgeDocChunkBulk, '/ai/chunk/bulk/do')

api.add_resource(KnowledgeDocBatch, '/ai/knowledge/doc/chunk/all')
# 应用
api.add_resource(AiAppView, '/ai/app')

# 工序流式应用
api.add_resource(AiFlowAppView, '/ai/flow/app')

api.add_resource(AppTreeView, '/ai/app/tree')
# 应用新列表
api.add_resource(AiApp, '/v2/ai/app')
# model list
api.add_resource(KnowledgeModelView, '/ai/knowledge/model')
# 切片向量化
api.add_resource(KnowledgeEmbeddingsView, '/ai/knowledge/embeddings')

# 文档清洗-规则配置
api.add_resource(FileClearDataView, '/ai/file/clear/config')
# 文档清洗-关系配置
api.add_resource(FileClearRelationView, '/ai/file/clear/relation')
# 文档清洗-规则表格数据
api.add_resource(FileClearTableView, '/ai/file/clear/table')
# 文档清洗-知识库规则预配置
api.add_resource(KnowledgeClearRelationView, '/ai/file/clear/knowledge')
# doc to docx
api.add_resource(KnowledgeDoc2DocxView, '/ai/knowledge/doc2docx')

# 切片清洗任务
api.add_resource(DocClearTaskView, '/ai/document/clear/task')

# 切片清洗数据
api.add_resource(DocClearDataView, '/ai/document/clear/data')
# 运行记录
api.add_resource(KnowledgeRunView, '/ai/knowledge/run')
# 知识库查询
api.add_resource(KnowledgeSearchView, '/ai/knowledge/search')
# 素材关联知识库
api.add_resource(ContentRelKnowledgeVew, '/ai/content/rel/knowledge')
# 获取应用变量
api.add_resource(AppPromptView, '/ai/app/prompt')
# 应用会话
api.add_resource(AppSessionView, '/ai/app/session')

# 工作流式会话
api.add_resource(AppFlowSession, '/ai/flow/session')

# 新增分组接口
api.add_resource(AppSessionGroupView, '/ai/app/session/group')


# 应用会话流程记录
api.add_resource(AppSessionFlow, '/app/session/flow')

# 应用会话分类
api.add_resource(AppSessionJudge, '/ai/app/judge')
# 应用详情
api.add_resource(AiAppDetailView, '/ai/app/detail')

# 应用样本详情
api.add_resource(AiAppSampleDetailView, '/ai/app/sample/detail/')
# 应用样本操作
api.add_resource(AiAppSampleActionView, '/ai/app/sample/<string:action>')

# 全文检索
api.add_resource(SearchContentKnowledgeView, '/ai/search/content')
# 知识库图片检索
api.add_resource(SearchKnowledgeChunkImgView, '/ai/search/chunk_image')
# 提示词优化
api.add_resource(AppPromptOptimizeView, '/ai/app/prompt/optimize')
# 获取相似素材
api.add_resource(AppSimilarMaterial, '/ai/app/similar/material')

# 智能报告
api.add_resource(AppSessionReportView, '/ai/app/report')
# 智能报告rms
api.add_resource(AppSessionReportRmsView, '/ai/app/report/rms')

# agent
# 技术工作流
api.add_resource(TechFlowView, '/ai/agent/tech')
# 智能报告记录
api.add_resource(AppSessionReportRecordView, '/ai/app/report/record')

# agent知识库查询与匹配
api.add_resource(AgentSearchKnowledgeView, "/ai/agent/search/knowledge")

# 知识门户 热词推荐
api.add_resource(AppSearchRecommendKeywordView, '/ai/knowledge/portal/recommend')

# 知识门户 知识推荐
api.add_resource(AppSearchRecommendContentView, '/ai/knowledge/portal/recommend/content')

# 会话引用
api.add_resource(AppSessionHitView, '/ai/app/session/hit')
# 会话引用v2
api.add_resource(AppSessionHitV2View, '/ai/app/session/hit/v2')

# 场景列表
api.add_resource(AppSessionChoiceView, '/ai/session/choice')

# 获取应用会话结果
api.add_resource(AppSessionResultView, '/ai/app/session/result')

# 国投大搜结果
api.add_resource(GTResultView, '/ai/gt/bing/result')

# 文档详情
api.add_resource(KnowledgeDocDetailView, '/ai/knowledge/doc/detail')

# 从文档中提取QA对
api.add_resource(ExtractQaFromKnowledge, '/ai/knowledge/doc/extract/qa')

# 知识库生命周期管理
api.add_resource(KnowledgeLifeView, '/ai/knowledge/life/detail')

# 文档生命周期管理
api.add_resource(DocLifeView, '/ai/doc/life/detail')

# 数据预处理规则管理
api.add_resource(PrepareClearRule, '/ai/manage/clear/rule')

# 知识库预处理规则配置管理
api.add_resource(KnowledgeRelClearRule, '/ai/knowledge/clear/rule')

# 切片 新增 or 修改
api.add_resource(KnowledgeDocumentChunkEditView, '/ai/knowledge/doc/chunk/edit')

# 获取QA结果
api.add_resource(AppQaSearchView, '/ai/app/session/qa')

# 根据知识库id、文档添加时间的时间段或者文档名字进行检索或者删除
api.add_resource(KnowledgeDocumentUpdate, '/ai/knowledge/doc/update')

# 国投知识库
api.add_resource(GTKnowledgeView, '/ai/gt/knowledge')
# 国投应用
api.add_resource(GTAppView, '/ai/gt/app')

# QA 库
api.add_resource(QaView, '/ai/app/qa')
#qa上传文档
api.add_resource(QaUpload, '/ai/app/qa/upload')
api.add_resource(QaLibItems, '/ai/app/qa/items', "/ai/app/qa/items/<string:id>")

api.add_resource(MinioView, '/minio')

#知识库树形结构
api.add_resource(KnowledgeTreeView, '/ai/knowledge/tree')
 
api.add_resource(KnowledgeGraphView, '/ai/knowledge/graph/<string:action>')

api.add_resource(KnowledgeGalaxyView, '/ai/knowledge/galaxy/<string:action>')

api.add_resource(UtilView, '/ai/util')
#文档清洗
api.add_resource(KnowledgeDocClearUrlView, '/ai/knowledge/doc/clear/url')
#获取上传文档后转换md内容
api.add_resource(GetMdContentView, '/ai/knowledge/mdcontent')

#文档上传存储
api.add_resource(SaveDocView, '/ai/knowledge/doc/save')
#只转md的接口
api.add_resource(File2MdView, '/ai/knowledge/file2md')
#修改md内容
api.add_resource(ModifyMdView, '/ai/knowledge/modify/md')
# 队列数量
api.add_resource(QueueNumView, '/ai/queue/num')
# xls转xlsx
api.add_resource(KnowledgeXls2XlsxView, '/ai/knowledge/xls2xlsx')
# tag标签查询接口
api.add_resource(KnowledgeTagView, '/ai/knowledge/tag')
#知识库统计
api.add_resource(KnowledgeStatisticView, '/ai/statistic/knowledge')

# 统计智能体
api.add_resource(StatisticsApp, '/ai/statistics/app')
# 统计复合智能体
api.add_resource(StatisticsCompoundApp, '/ai/statistics/compound/app')


# 统计智能体 detail by date
api.add_resource(StatisticsDetailDateApp, '/ai/statistics/app/detail')
# 统计智能体 ring
api.add_resource(StatisticsDetailRingApp, '/ai/statistics/app/ring')
# 统计智能体 问题
api.add_resource(StatisticsQuestionApp, '/ai/statistics/app/question')
# 前端获取上传文档每一阶段的所需值
api.add_resource(KnowledgeFileStateView, '/ai/knowledge/file/state')

# 视频上传
api.add_resource(KnowledgeVideoUpload, '/ai/knowledge/video/upload')

# 音频转文字
api.add_resource(KnowledgAudioTextView, '/ai/knowledge/audio/text')

# 融合版聊天记录
api.add_resource(ChatbiSessionRecordView, '/ai/conversation/record')


# 算料库管理
api.add_resource(ForageView, '/ai/model/forage')

# 算料文件管理
api.add_resource(ForageFileView, '/ai/forage/file')

# 结构化算料文件上传
api.add_resource(ForageStructuredFileUpload, '/ai/forage/structured/file')

# 结构化数据合成任务
api.add_resource(ForageStructuredTaskView, '/ai/forage/structured/task')
# 算料QA管理
api.add_resource(ForageQAView, '/ai/forage/qa')

# 算料文件分片处理
api.add_resource(ForageFileSplitter, '/forage/file/split')

# 算料QA提取处理
api.add_resource(ForageQAExtract, '/forage/qa/extract')

# 算料库下载
api.add_resource(ForageQADown, '/forage/qa/download')


# QA查询
api.add_resource(QARetView, '/ai/qa/ret')

# 知识库查询
api.add_resource(KnowledgeRetView, '/ai/knowledge/ret')

# 模型推理
api.add_resource(AiAppChat, '/ai/app/chat')

# QA查询
api.add_resource(QARet2View, '/ai/qa/ret2')

# 知识库查询
api.add_resource(KnowledgeRet2View, '/ai/knowledge/ret2')

# 模型推理
api.add_resource(AiAppChat2, '/ai/app/chat2')

# 文件分析
api.add_resource(AnalyseFileView, '/ai/file/analyse')

# ReRank
api.add_resource(ReRankView, '/ai/knowledge/rerank')

# 数据集预处理
api.add_resource(KnowledgePreprocessing, '/ai/app/knowledge/preprocessing')

# 获取上传历史记录
api.add_resource(KnowledgeDocumentUploadHistory, '/ai/knowledge/doc/upload/history')

# 获取文档中图片和表格列表
api.add_resource(KnowledgeDocumentImagesAndTables, '/ai/knowledge/doc/images/tables')

# 获取知识库文档、应用以及视频的数量
api.add_resource(KnowledgeDocumentCount, '/ai/knowledge/doc/count')

# 推荐知识库和应用
api.add_resource(RecommendKnowledge, '/ai/knowledge/recommend')

# 文档复制迁移
api.add_resource(KnowledgeDocTransferView, '/ai/knowledge/doc/transfer')

# 文档链式处理
api.add_resource(HandleDocChainView, '/ai/knowledge/doc/chain_handle')

# 文档链式处理带进度
api.add_resource(HandleDocChainViewWithProgress, '/ai/knowledge/doc/chain_handle_progress')

# 获取应用场景列表
api.add_resource(AllScopeView, '/ai/all/scope')

#  获取系统列表
api.add_resource(SystemListView, '/ai/system/list')

# 系统配置
api.add_resource(SystemConfigView, '/ai/system/config')

# 音频链式处理带进度
api.add_resource(AudioChainHandleView, '/ai/audio/chain_handle')

# 视频链式处理带进度
api.add_resource(VideoChainHandleView, '/ai/video/chain_handle')

# 算料评估
api.add_resource(ForageEvaluateView, '/ai/forage/evaluate')

# 算料清洗
api.add_resource(ForageCleanView, '/ai/forage/clean')

# 算料合成
api.add_resource(ForageCombineView, '/ai/forage/combine')
# 基于某个文档的RAG
api.add_resource(KnowledgeDocConversationView, '/ai/knowledge/doc/conversation')