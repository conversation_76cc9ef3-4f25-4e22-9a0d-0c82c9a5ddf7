import json
import re

from flask_restful import Resource, reqparse
from horticulture.validate import json_response
from horticulture.auth_token import login_check
from controller.app_controller import AppController
from lib_func.const_map import *
from lib_func.logger import logger
from modeler.mysql.app_orm import AppOrm
from modeler.mongo.chat_mongo import ChatMongo

example_intput_json = {
    "nodes": [
      {
        "id": "1",
        "type": "Start",
        "name": "开始",
        "description": "流程开始节点"
      },
      {
        "id": "2",
        "type": "Normal",
        "name": "评估公司团队的技术能力",
        "description": "对公司团队的技术背景进行评估"
      },
      {
        "id": "3",
        "type": "Judge",
        "name": "技术能力评估结果",
        "description": "判断公司技术能力是否符合要求"
      },
      {
        "id": "4",
        "type": "Normal",
        "name": "评估公司团队的管理经验",
        "description": "对公司团队的管理层经验进行评估"
      },
      {
        "id": "5",
        "type": "Judge",
        "name": "管理经验评估结果",
        "description": "判断公司管理经验是否符合要求"
      },
      {
        "id": "6",
        "type": "Normal",
        "name": "确定投资项目能力",
        "description": "确定公司是否有能力完成所选投资项目"
      },
      {
        "id": "7",
        "type": "Judge",
        "name": "投资项目能力判断",
        "description": "判断公司是否具备完成投资项目的能力"
      },
      {
        "id": "8",
        "type": "Normal",
        "name": "评估预期回报",
        "description": "评估投资项目能否实现预期回报"
      },
      {
        "id": "9",
        "type": "Judge",
        "name": "预期回报评估结果",
        "description": "判断投资项目是否能实现预期回报"
      },
      {
        "id": "10",
        "type": "End",
        "name": "结束",
        "description": "流程结束节点"
      }
    ],
    "edges": [
      {
        "id": "1",
        "label": "技术能力不符合",
        "source": "2",
        "target": "3",
        "description": "公司技术能力不符合要求"
      },
      {
        "id": "2",
        "label": "技术能力符合",
        "source": "2",
        "target": "4",
        "description": "公司技术能力符合要求"
      },
      {
        "id": "3",
        "label": "管理经验不足",
        "source": "4",
        "target": "5",
        "description": "公司管理经验不足"
      },
      {
        "id": "4",
        "label": "管理经验符合",
        "source": "4",
        "target": "6",
        "description": "公司管理经验符合要求"
      },
      {
        "id": "5",
        "label": "无能力完成投资项目",
        "source": "6",
        "target": "7",
        "description": "公司无能力完成投资项目"
      },
      {
        "id": "6",
        "label": "具备能力完成投资项目",
        "source": "6",
        "target": "8",
        "description": "公司具备完成投资项目的能力"
      },
      {
        "id": "7",
        "label": "无法实现预期回报",
        "source": "8",
        "target": "9",
        "description": "无法实现预期回报"
      },
      {
        "id": "8",
        "label": "可实现预期回报",
        "source": "8",
        "target": "10",
        "description": "可实现预期回报"
      },
      {
        "id": "9",
        "label": "放弃投资",
        "source": "9",
        "target": "10",
        "description": "决定放弃投资"
      }
    ]
  }

example_output_json = {
    "nodes": [
      {
        "id": "2_1",
        "type": "toolsItem",
        "name": "算法组件",
        "_description": "算法组件使用",
        "_parentId": 2
      },
      {
        "id": "2_2",
        "type": "knowNode",
        "name": "知识库",
        "_description": "知识库使用",
        "_parentId": 2
      },
      {
        "id": "2_3",
        "type": "dataSet",
        "name": "数据集",
        "_description": "数据集使用",
        "_parentId": 2
      },
      {
        "id": "4_1",
        "type": "knowNode",
        "name": "知识库",
        "_description": "知识库使用",
        "_parentId": 4
      },
      {
        "id": "4_2",
        "type": "dataSet",
        "name": "数据集",
        "_description": "数据集使用",
        "_parentId": 4
      },
      {
        "id": "6_1",
        "type": "toolsItem",
        "name": "算法组件",
        "_description": "算法组件使用",
        "_parentId": 6
      },
      {
        "id": "6_2",
        "type": "knowNode",
        "name": "知识库",
        "_description": "知识库使用",
        "_parentId": 6
      },
      {
        "id": "6_3",
        "type": "dataSet",
        "name": "数据集",
        "_description": "数据集使用",
        "_parentId": 6
      },
      {
        "id": "8_1",
        "type": "toolsItem",
        "name": "算法组件",
        "_description": "算法组件使用",
        "_parentId": 8
      },
      {
        "id": "8_2",
        "type": "knowNode",
        "name": "知识库",
        "_description": "知识库使用",
        "_parentId": 8
      },
      {
        "id": "8_3",
        "type": "dataSet",
        "name": "数据集",
        "_description": "数据集使用",
        "_parentId": 8
      },
    ],
    "edges": [
      {
        "id": "1",
        "label": "",
        "source": "2_1",
        "target": "2_2",
        "_description": "算法->知识库"
      },
      {
        "id": "2",
        "label": "",
        "source": "2_2",
        "target": "2_3",
        "_description": "知识库->数据集"
      },
      {
        "id": "3",
        "label": "",
        "source": "2_3",
        "target": "4_1",
        "_description": "数据集->知识库"
      },
      {
        "id": "5",
        "label": "",
        "source": "4_1",
        "target": "4_2",
        "_description": "知识库->数据集"
      },
      {
        "id": "5",
        "label": "",
        "source": "4_2",
        "target": "6_1",
        "_description": "数据集->算法"
      },
      {
        "id": "8",
        "label": "",
        "source": "6_1",
        "target": "6_2",
        "_description": "算法->知识库"
      },
      {
        "id": "9",
        "label": "",
        "source": "6_2",
        "target": "6_3",
        "_description": "知识库->数据集"
      },
      {
        "id": "9",
        "label": "",
        "source": "6_3",
        "target": "8_1",
        "_description": "数据集->算法"
      },
      {
        "id": "12",
        "label": "",
        "source": "8_1",
        "target": "8_2",
        "_description": "算法->知识库"
      },
      {
        "id": "13",
        "label": "",
        "source": "8_1",
        "target": "8_3",
        "_description": "知识库->数据集"
      }
    ]
  }
  
tech_flow_map = {
  "toolsItem": "算法组件",
  "knowNode": "知识库",
  "dataSet": "数据集",
  "module": "大模型",
  "code": "代码",
  "variable": "变量",
}

class TechFlowView(Resource):
    """
    技术工作流 View
    """
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('workflow', required=True, type=dict, location='json')
        parser.add_argument('workflow_description', required=False, type=str, location='json')
        args = parser.parse_args()
        workflow = args['workflow']
        workflow_description = args['workflow_description']
        
        if not workflow:
            return json_response(code='FAIL', err_msg=f"workflow {workflow} 不能为空！")
        
        logger.info(f"workflow: {workflow}, type: {type(workflow)}")
        logger.info(f"业务流描述: {workflow_description}")

        # 构造GPT-4 API调用的prompt
        prompt = generate_prompt(workflow)
        
        # 调用GPT-4 API
        tech_flow_json = call_gpt4_api(prompt)
        
        return json_response(tech_flow_json)



  
def generate_prompt(workflow):
    prompt = (
        "下面是前端传的业务流节点（nodes）和节点间的连线（edges）：\n"
        "nodes数组里代表着每个节点图，type代表着类型，例如：\n"
        "- 'Start' 表示开始节点\n"
        "- 'Normal' 表示正常节点\n"
        "- 'Judge' 表示判断结果的节点\n"
        "edges代表着节点间的连线。\n"
        "现在需要将上述业务流程图转换为技术流程图，技术流程图可用的模块有：toolsItem-算法组件、knowNode-知识库、dataSet-数据集、module-大模型、code-代码、variable-变量。其中英文写到type字段，中文写到name字段\n"
        "这些组件可以帮助实现业务流程的某个节点。例如：评估技术能力节点可以使用算法组件和知识库等实现。\n"
        "技术流程图需要按照以下格式生成：\n"
        "1. 将每个需要评估的业务节点转换为技术节点，并为其分配相关的技术组件，例如算法组件、知识库和数据集。\n"
        "2. 'Judge' 节点是结果节点，不需要技术模块。\n"
        "3. 生成新的nodes和连线edges，只包含技术节点，并且要保证技术节点之间的edges完整且不断,如下述示例，把所有节点在edges中连起来并且id从1开始递增。\n"
        f"输入示例格式：\n{json.dumps(example_intput_json, ensure_ascii=False, indent=2)}\n"
        f"输出示例格式：\n{json.dumps(example_output_json, ensure_ascii=False, indent=2)}\n"
        "以下是当前业务流程图 JSON：\n"
        f"{json.dumps(workflow, ensure_ascii=False, indent=2)}\n"
        "请根据上述示例和信息生成对应的技术流程图 JSON：\n"
    )
    return prompt


def call_gpt4_api(input_content):
    # TODO /ai/agent/tech接口，待测试
    messages = [{'role': 'system', 'content': input_content}]
    
    corpid = "wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ"
    response = AppController(corpid).ai_app_chat(messages, [], CHATGPT_MODEL_ID, model='gpt-4', is_stream=False)
    # 添加日志记录响应内容
    # logger.info(f"Response status code: {response.status_code}")
    # logger.info(f"Response content: {response.text}")
    res_str = ''
    for item,_ in response:
        res_str = item
        
    if not res_str:
        raise ValueError("No data returned from GPT-4 API")

    # 使用正则表达式提取 JSON 部分
    json_match = re.search(r'\{.*\}', res_str, re.DOTALL)
    if not json_match:
        raise ValueError("No JSON found in the response")

    json_str = json_match.group(0)
    rsp_json = json.loads(json_str)
    
    logger.info(f"Call gpt api rsp_json: {rsp_json}, {type(rsp_json)}")
    
    # 检查并纠正类型字段
    def correct_type(type_field):
        if type_field in tech_flow_map:
            return type_field
        for key in tech_flow_map:
            if key.startswith(type_field):
                return key
        return None

    def filter_and_correct_nodes(nodes):
        corrected_nodes = []
        for node in nodes:
            if 'type' in node:
                corrected_type = correct_type(node['type'])
                if corrected_type:
                    node['type'] = corrected_type
                    node['name'] = tech_flow_map[corrected_type]
                    corrected_nodes.append(node)
        return corrected_nodes

    rsp_json['nodes'] = filter_and_correct_nodes(rsp_json.get('nodes', []))
    
    logger.info("*" * 20)
    
    logger.info(f"Corrected rsp_json: {rsp_json}, {type(rsp_json)}")
    
    return rsp_json
    

# def generate_prompt(workflow):
#     prompt = (
#         "下面是前端传的业务流节点（nodes）和节点间的连线（edges）：\n"
#         "nodes数组里代表着每个节点图，type代表着类型，例如：\n"
#         "- 'Start' 表示开始节点\n"
#         "- 'Normal' 表示正常节点\n"
#         "- 'Judge' 表示判断结果的节点\n"
#         "- 'group' 表示包含子节点的节点\n"
#         "edges代表着节点间的连线。\n"
#         "现在需要将上述业务流程图转换为技术流程图，技术流程图可用的模块有：算法组件、知识库、数据集、大模型、代码、变量。\n"
#         "这些组件可以帮助实现业务流程的某个节点。例如：评估技术能力节点可以使用算法组件和知识库等实现。\n"
#         "技术流程图需要按照以下格式生成：\n"
#         "1. 将每个需要评估的业务节点转换为技术节点，并为其分配相关的技术组件，例如算法组件、知识库和数据集。\n"
#         "2. 'Normal' 节点类型应改为 'group' 类型，并在其下增加子节点，每个子节点代表一个技术组件，"
#         "如算法组件、知识库和数据集。\n"
#         "3. 'Judge' 节点是结果节点，不需要技术模块。\n"
#         "4. 保持节点间的连线，并在技术节点之间增加必要的连线。\n"
#         "5. 在技术流程图中，子节点应具有 '_parentId' 字段以标识其父节点。\n"
#         f"输入示例格式：\n{json.dumps(example_intput_json, ensure_ascii=False, indent=2)}\n"
#         f"输出示例格式：\n{json.dumps(example_output_json, ensure_ascii=False, indent=2)}\n"
#         "以下是业务流程图 JSON：\n"
#         f"{json.dumps(workflow, ensure_ascii=False, indent=2)}\n"
#         "请根据上述示例和信息生成对应的技术流程图 JSON：\n"
#     )
#     return prompt


if __name__ == "__main__":
    workflow1 = {
    "nodes": [
      {
        "id": "1",
        "type": "Start",
        "name": "开始",
        "_description": "流程开始节点"
      },
      {
        "id": "2",
        "type": "Normal",
        "name": "评估公司团队的技术能力",
        "_description": "对公司团队的技术背景进行评估"
      },
      {
        "id": "3",
        "type": "Judge",
        "name": "技术能力评估结果",
        "_description": "判断公司技术能力是否符合要求"
      },
      {
        "id": "4",
        "type": "Normal",
        "name": "评估公司团队的管理经验",
        "_description": "对公司团队的管理层经验进行评估"
      },
      {
        "id": "5",
        "type": "Judge",
        "name": "管理经验评估结果",
        "_description": "判断公司管理经验是否符合要求"
      },
      {
        "id": "6",
        "type": "Normal",
        "name": "确定投资项目能力",
        "_description": "确定公司是否有能力完成所选投资项目"
      },
      {
        "id": "7",
        "type": "Judge",
        "name": "投资项目能力判断",
        "_description": "判断公司是否具备完成投资项目的能力"
      },
      {
        "id": "8",
        "type": "Normal",
        "name": "评估预期回报",
        "_description": "评估投资项目能否实现预期回报"
      },
      {
        "id": "9",
        "type": "Judge",
        "name": "预期回报评估结果",
        "_description": "判断投资项目是否能实现预期回报"
      },
      {
        "id": "10",
        "type": "End",
        "name": "结束",
        "_description": "流程结束节点"
      }
    ],
    "edges": [
      {
        "id": "1",
        "label": "技术能力不符合",
        "source": "2",
        "target": "3",
        "_description": "公司技术能力不符合要求"
      },
      {
        "id": "2",
        "label": "技术能力符合",
        "source": "2",
        "target": "4",
        "_description": "公司技术能力符合要求"
      },
      {
        "id": "3",
        "label": "管理经验不足",
        "source": "4",
        "target": "5",
        "_description": "公司管理经验不足"
      },
      {
        "id": "4",
        "label": "管理经验符合",
        "source": "4",
        "target": "6",
        "_description": "公司管理经验符合要求"
      },
      {
        "id": "5",
        "label": "无能力完成投资项目",
        "source": "6",
        "target": "7",
        "_description": "公司无能力完成投资项目"
      },
      {
        "id": "6",
        "label": "具备能力完成投资项目",
        "source": "6",
        "target": "8",
        "_description": "公司具备完成投资项目的能力"
      },
      {
        "id": "7",
        "label": "无法实现预期回报",
        "source": "8",
        "target": "9",
        "_description": "无法实现预期回报"
      },
      {
        "id": "8",
        "label": "可实现预期回报",
        "source": "8",
        "target": "10",
        "_description": "可实现预期回报"
      },
      {
        "id": "9",
        "label": "放弃投资",
        "source": "9",
        "target": "10",
        "_description": "决定放弃投资"
      }
    ]
  }
    
    workflow2 = {
      "nodes": [
        {
          "id": "1",
          "type": "Start",
          "name": "开始",
          "_description": "开始节点"
        },
        {
          "id": "2",
          "type": "Normal",
          "name": "评估市场风险",
          "_description": "对市场进行风险评估"
        },
        {
          "id": "3",
          "type": "Normal",
          "name": "评估行业风险",
          "_description": "对行业进行风险评估"
        },
        {
          "id": "4",
          "type": "Normal",
          "name": "评估技术风险",
          "_description": "对技术进行风险评估"
        },
        {
          "id": "5",
          "type": "Judge",
          "name": "市场风险是否可控",
          "_description": "判断市场风险是否可控"
        },
        {
          "id": "6",
          "type": "Normal",
          "name": "市场风险可控策略制定",
          "_description": "制定市场风险可控策略"
        },
        {
          "id": "7",
          "type": "End",
          "name": "市场风险评估完成",
          "_description": "市场风险评估流程结束"
        },
        {
          "id": "8",
          "type": "Normal",
          "name": "市场风险不可控策略制定",
          "_description": "制定市场风险不可控策略"
        },
        {
          "id": "9",
          "type": "End",
          "name": "市场风险评估完成",
          "_description": "市场风险评估流程结束"
        },
        {
          "id": "10",
          "type": "End",
          "name": "结束",
          "_description": "结束节点"
        }
      ],
      "edges": [
        {
          "id": "1",
          "label": "",
          "source": "1",
          "target": "2",
          "_description": "开始评估流程"
        },
        {
          "id": "2",
          "label": "",
          "source": "2",
          "target": "5",
          "_description": "市场风险评估"
        },
        {
          "id": "3",
          "label": "",
          "source": "2",
          "target": "3",
          "_description": "行业风险评估"
        },
        {
          "id": "4",
          "label": "",
          "source": "3",
          "target": "4",
          "_description": "技术风险评估"
        },
        {
          "id": "5",
          "label": "是",
          "source": "5",
          "target": "6",
          "_description": "市场风险可控"
        },
        {
          "id": "6",
          "label": "否",
          "source": "5",
          "target": "8",
          "_description": "市场风险不可控"
        },
        {
          "id": "7",
          "label": "",
          "source": "6",
          "target": "7",
          "_description": "市场风险规避策略制定完成"
        },
        {
          "id": "8",
          "label": "",
          "source": "8",
          "target": "9",
          "_description": "市场风险转移策略制定完成"
        },
        {
          "id": "9",
          "label": "",
          "source": "9",
          "target": "10",
          "_description": "市场风险评估流程结束"
        }
      ]
    }
    
    prompt = generate_prompt(workflow2)
    
    tech_flow_json = call_gpt4_api(prompt)
    logger.info("End")

