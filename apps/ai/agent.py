#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/4/8 11:07
# <AUTHOR> zhang<PERSON>
# @File    : app.py
# @Comment :
from apps.ai import task
from flask_restful import Resource, reqparse
from controller.knowledge_controller import Knowledge<PERSON>ontroller
from horticulture.validate import json_response
from lib_func.logger import logger

def format_output_data(code=0, data=None, message="成功", ):
    return {"code": code, "data": data, "message": message}

def remove_duplicates(data, key):
    seen = set()
    new_data = []
    for item in data:
        if item[key] not in seen:
            new_data.append(item)
            seen.add(item[key])
    return new_data


class AgentSearchKnowledgeView(Resource):

    agent_search_knowledge_content=None
    @staticmethod
    def get():
        parser = reqparse.RequestParser()
        parser.add_argument('scene_desc', type=str, required=False, location='args')
        # parser.add_argument('desc',type=str,required=False,location='args')
        # parser.add_argument('search_mode', type=int, required=False, default=1, location='args',
        #                     help='1-词向量匹配，2-全文检索，3-混合检索')
        args = parser.parse_args()
        # search_mode = int(args.get('search_mode', 1))
        search_mode = 3
        logger.info(f"search_mode: 【{search_mode}】, 1-词向量匹配，2-全文检索，3-混合检索 \nsearch_key: {args['scene_desc']}")
        controller = KnowledgeController()
        content_ids = []

        # 初始化数据结构
        embedding_search_map = {"knowledge_list": [], "content_ids": []}
        full_text_search_map = {"knowledge_list": [], "content_ids": []}
        final_chunk_set = set()
        final_chunk_list = []

        # 用于计数的 knowledge_code
        embedding_knowledge_code = 1
        full_text_knowledge_code = 1

        def build_search_map(hits, max_score, search_map, knowledge_code_counter):
            knowledge_dict = {}  # 用于去重的知识库字典
            for item in hits:
                knowledge_id = item['_source']['knowledge_id']
                doc_id = item['_source']['doc_id']
                chunk_id = item['_source']['chunk_id']
                highlight_description = item.get('highlight', {}).get('description', [item['_source']['description']])[
                    0]
                score = item['_score']
                match_score = controller.calculate_match_score(score, max_score=max_score)

                if knowledge_id not in knowledge_dict:
                    knowledge_dict[knowledge_id] = {}

                if doc_id not in knowledge_dict[knowledge_id]:
                    doc_info = controller.get_doc_info(doc_id)
                    knowledge_dict[knowledge_id][doc_id] = doc_info
                    knowledge_dict[knowledge_id][doc_id]['chunk_list'] = []

                knowledge_dict[knowledge_id][doc_id]['chunk_list'].append({
                    'chunk_id': chunk_id,
                    'doc_id': doc_id,
                    'knowledge_id': knowledge_id,
                    'content': highlight_description,
                    'score': score,
                    'match_score': match_score,
                })

            # 构建最终的 search_map 结构
            for knowledge_id, doc_dict in knowledge_dict.items():
                doc_list = list(doc_dict.values())
                search_map['knowledge_list'].append({
                    'knowledge_id': knowledge_id,
                    'knowledge_name': controller.get_knowledge_detail(knowledge_id)['knowledge_name'],
                    'knowledge_code': knowledge_code_counter,
                    'hit_doc_counts': len(doc_list),
                    'doc_list': doc_list,
                })
                knowledge_code_counter += 1

            return knowledge_code_counter

        # 根据 search_mode 执行不同的检索方式
        if search_mode in [1, 3]:  # 词向量匹配或混合检索
            # vector_content_hits = controller.search_es_content(args['search_key'], size=10, min_score=1.6, is_search=True)
            vector_content_hits = controller.search_es_content(args['scene_desc'], size=10, min_score=0.3,
                                                               is_search=True)
            content_ids.extend([item['_source']['content_id'] for item in vector_content_hits])
            embedding_search_map['content_ids'].append([item['_source']['content_id'] for item in vector_content_hits])

            for knowledge_ids in controller.get_knowledge_ids_by_model():
                # vector_knowledge_hits = controller.search_es_knowledge(task, args['search_key'], knowledge_ids, size=4, min_score=1.8, is_search=True)
                vector_knowledge_hits = controller.search_es_knowledge(task, args['scene_desc'], knowledge_ids, size=4,
                                                                       min_score=0.3, is_search=True)
                embedding_knowledge_code = build_search_map(vector_knowledge_hits, max_score=2.0,
                                                            search_map=embedding_search_map,
                                                            knowledge_code_counter=embedding_knowledge_code)

        if search_mode in [2, 3]:  # 全文检索或混合检索
            # full_text_content_hits = controller.search_es_content_full_text(args['search_key'], size=10, min_score=8.0)
            full_text_content_hits = controller.search_es_content_full_text(args['scene_desc'], size=10, min_score=0.3)
            content_ids.extend([item['_source']['content_id'] for item in full_text_content_hits])
            full_text_search_map['content_ids'].append(
                [item['_source']['content_id'] for item in full_text_content_hits])

            for knowledge_ids in controller.get_knowledge_ids_by_model():
                # full_text_knowledge_hits = controller.search_es_knowledge_full_text_v1(task, args['search_key'], knowledge_ids, size=4, min_score=8.0)
                full_text_knowledge_hits = controller.search_es_knowledge_full_text_v1(task, args['scene_desc'],
                                                                                       knowledge_ids, size=4,
                                                                                       min_score=0.3)
                full_text_knowledge_code = build_search_map(full_text_knowledge_hits, max_score=12.0,
                                                            search_map=full_text_search_map,
                                                            knowledge_code_counter=full_text_knowledge_code)

        # 如果 knowledge_list 为空，则设置为 {}
        if not embedding_search_map['knowledge_list']:
            embedding_search_map = {}
        if not full_text_search_map['knowledge_list']:
            full_text_search_map = {}

        # 构建最终的data
        data = {
            "search_mode": search_mode,
            "embedding_search_map": embedding_search_map if search_mode in [1, 3] else {},
            "full_text_search_map": full_text_search_map if search_mode in [2, 3] else {},
            "content_ids": list(set(content_ids))
        }

        if embedding_search_map:
            if embedding_search_map.get('knowledge_list'):
                embedding_knowledge_list = embedding_search_map.get('knowledge_list')
                for embedding_knowledge_obj in embedding_knowledge_list:
                    if embedding_knowledge_obj.get('doc_list'):
                        for doc_obj in embedding_knowledge_obj.get('doc_list'):
                            if doc_obj.get('chunk_list'):
                                for chunk_obj in doc_obj.get('chunk_list'):
                                    if chunk_obj.get('chunk_id') not in final_chunk_set:
                                        final_chunk_set.add(chunk_obj.get('chunk_id'))
                                        final_chunk_list.append(chunk_obj)

        if full_text_search_map:
            if full_text_search_map.get('knowledge_list'):
                full_text_knowledge_list = full_text_search_map.get('knowledge_list')
                for full_text_knowledge_obj in full_text_knowledge_list:
                    if full_text_knowledge_obj.get('doc_list'):
                        for doc_obj in full_text_knowledge_obj.get('doc_list'):
                            if doc_obj.get('chunk_list'):
                                for chunk_obj in doc_obj.get('chunk_list'):
                                    if chunk_obj.get('chunk_id') not in final_chunk_set:
                                        final_chunk_set.add(chunk_obj.get('chunk_id'))
                                        final_chunk_list.append(chunk_obj)

        logger.info(f"final_chunk_set: {final_chunk_set}")
        data['chunk_list'] = final_chunk_list

        logger.info(f"*" * 75)
        logger.info(f"Search final result: {data}")

        # result_data={}
        # l_embedding_search_list=[]
        # l_full_text_search_list=[]
        # if data["embedding_search_map"]:
        #     l_embedding_search_list=[{"knowledge_id":item["knowledge_id"],"knowledge_name":item["knowledge_name"]}for item in data["embedding_search_map"]["knowledge_list"]]
        # if data["full_text_search_map"]:
        #     l_full_text_search_list=[{"knowledge_id":item["knowledge_id"],"knowledge_name":item["knowledge_name"]}for item in data["full_text_search_map"]["knowledge_list"]]
        # result_data.update({"knowledge_ids":remove_duplicates(l_embedding_search_list + l_full_text_search_list, "knowledge_id")})
        # if data["chunk_list"]:
        #     AgentSearchKnowledgeView.agent_search_knowledge_content=[{item["knowledge_id"]:item["content"]} for item in data["chunk_list"]]
        # return format_output_data(data=data)
        return json_response(data)

    @staticmethod
    def post():
        return format_output_data(data=AgentSearchKnowledgeView.agent_search_knowledge_content)







