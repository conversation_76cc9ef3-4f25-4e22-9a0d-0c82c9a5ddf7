#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/4/8 11:07
# <AUTHOR> yanning
# @File    : app.py
# @Comment :

from utils.tools import get_formatted_content

from flask import request
from flask_restful import Resource, reqparse
from horticulture.validate import json_response
from horticulture.auth_token import login_check
from controller.qa_controller import <PERSON><PERSON><PERSON><PERSON>roll<PERSON>, QaUploadController, QaItemsController
from apps.ai import task
from controller.demo.demo_chat_free_contoller import ChatFreeController
import requests
import json
from settings import JUSURE_SPEECH_CLIENT_ID, JUSURE_SPEECH_CLIENT_SECRET
from puppet.cache import redis_pool
from horticulture.cache_decoration import cache_ret
import random

class UtilView(Resource):
    @staticmethod
    @login_check
    
    def get():
        @cache_ret('baidu_record_token', expire_time=60* 60 * 21)
        def get_token():
            payload = json.dumps("")
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            url = "https://aip.baidubce.com/oauth/2.0/token?client_id={client_id}&client_secret={client_secret}&grant_type=client_credentials".format(
                client_id= JUSURE_SPEECH_CLIENT_ID,
                client_secret=JUSURE_SPEECH_CLIENT_SECRET
            )
            response = requests.request("POST", url, headers=headers, data=payload)
            return response.json()
        
        return json_response(data=get_token())
    @staticmethod
    @login_check
    def post():    
        
        parser = reqparse.RequestParser()
        parser.add_argument('format', type=str, required=True, help='format is required')
        parser.add_argument('rate', type=int, required=True, help='rate is required')
        parser.add_argument('channel', type=int, required=True, help='channel is required')
        parser.add_argument('cuid', type=str, required=True, help='cuid is required')
        parser.add_argument('speech', type=str, required=True, help='speech is required')
        parser.add_argument('len', type=int, required=True, help='len is required')
        parser.add_argument('dev_pid', type=int, required=False, default=1537)
        parser.add_argument('token', type=str, required=True, help='token is required')

        url = "https://vop.baidu.com/server_api"
        
        payload = json.dumps(parser.parse_args())
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        response = requests.request("POST", url, headers=headers, data=payload)

        return json_response(data=response.json())
        
