from flask import request
from flask_restful import Resource, reqparse
from horticulture.validate import json_response
from horticulture.auth_token import login_check
from lib_func.logger import logger
from module import minio_util
import settings
class MinioView(Resource):
    @staticmethod
    @login_check
    def post():
        parser = reqparse.RequestParser()
        parser.add_argument('oss_names_array', type=list, required=True, help="oss_names_array不能为空", location="json")
        parser.add_argument('relative_path', type=str, required=True, help="relative_path不能为空")

        args = parser.parse_args()
        relative_path = args.get('relative_path')
        bucket_name = settings.KNOWLEDGE_OSS_BUCKET_NAME
        result = []
        # http://oss.ai.zhongshuruizhi.com/knowledge-docs/681eb0ba-e93b-48fb-b570-9cbcb1dc9e50/WechatIMG71.png
        for oss_name in args.get('oss_names_array'):
            object_name = f"{relative_path}/{oss_name}"
            logger.info(f"object_name: {object_name}")
            presigned_url, oss_url = minio_util.generate_presigned_url(bucket_name, object_name)
            result.append({
                'presigned_url': presigned_url,
                'oss_url': oss_url,
                'oss_name': oss_name
            })
        return json_response(data=result)