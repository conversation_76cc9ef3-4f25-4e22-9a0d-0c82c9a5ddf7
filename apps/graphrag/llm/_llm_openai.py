"""LLM Services module."""

import asyncio
from dataclasses import dataclass, field
from itertools import chain
from typing import Any, List, Optional, Tuple, Type, cast

import numpy as np
from openai import APIConnectionError, AsyncOpenAI, RateLimitError
from pydantic import BaseModel
from tenacity import (
    AsyncRetrying,
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

from ...graphrag._exceptions import LLMServiceNoResponseError
from ...graphrag._types import BaseModelAlias
from ...graphrag._utils import logger, throttle_async_func_call

from ._base import BaseEmbeddingService, BaseLLMService, T_model

TIMEOUT_SECONDS = 180.0


@dataclass
class OpenAILLMService(BaseLLMService):
    """LLM Service for OpenAI LLMs."""

    model: Optional[str] = field(default="gpt-4o-mini")
    mode: str = field(default="json")  # 替换 instructor.Mode 为简单字符串

    def __post_init__(self):
        logger.debug("Initialized OpenAILLMService with patched OpenAI client.")
        self.llm_async_client = AsyncOpenAI(  # 使用 openai 库或其他逻辑替代
            base_url=self.base_url, api_key=self.api_key, timeout=TIMEOUT_SECONDS
        )

    @throttle_async_func_call(max_concurrent=256, stagger_time=0.001, waiting_time=0.001)
    async def send_message(
        self,
        prompt: str,
        model: str | None = None,
        system_prompt: str | None = None,
        history_messages: list[dict[str, str]] | None = None,
        response_model: Type[T_model] | None = None,
        **kwargs: Any,
    ) -> Tuple[T_model, list[dict[str, str]]]:
        """Send a message to the language model and receive a response."""
        logger.debug(f"Sending message with prompt: {prompt}")
        model = model or self.model
        if model is None:
            raise ValueError("Model name must be provided.")
        messages: list[dict[str, str]] = []

        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
            logger.debug(f"Added system prompt: {system_prompt}")

        if history_messages:
            messages.extend(history_messages)
            logger.debug(f"Added history messages: {history_messages}")

        messages.append({"role": "user", "content": prompt})

        # 替换 instructor 的逻辑为 openai 原生 API
        llm_response = await self.llm_async_client.chat.completions.create(
            model=model,
            messages=messages,
            max_retries=3,
            **kwargs
        )

        if not llm_response:
            logger.error("No response received from the language model.")
            raise LLMServiceNoResponseError("No response received from the language model.")

        messages.append(
            {
                "role": "assistant",
                "content": llm_response["choices"][0]["message"]["content"],
            }
        )
        logger.debug(f"Received response: {llm_response}")

        return llm_response, messages

@dataclass
class OpenAIEmbeddingService(BaseEmbeddingService):
    """Base class for Language Model implementations."""

    embedding_dim: int = field(default=1536)
    max_elements_per_request: int = field(default=32)
    model: Optional[str] = field(default="text-embedding-3-small")

    def __post_init__(self):
        self.embedding_async_client: AsyncOpenAI = AsyncOpenAI(
            base_url=self.base_url, api_key=self.api_key, timeout=TIMEOUT_SECONDS
        )
        logger.debug("Initialized OpenAIEmbeddingService with OpenAI client.")

    async def encode(self, texts: list[str], model: Optional[str] = None) -> np.ndarray[Any, np.dtype[np.float32]]:
        """Get the embedding representation of the input text."""
        logger.debug(f"Getting embedding for texts: {texts}")
        model = model or self.model
        if model is None:
            raise ValueError("Model name must be provided.")

        response = await self.embedding_async_client.embeddings.create(
            model=model,
            input=texts,
        )

        embeddings = np.array([item["embedding"] for item in response["data"]])
        logger.debug(f"Received embedding response: {len(embeddings)} embeddings")

        return embeddings