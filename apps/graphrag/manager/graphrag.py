import asyncio
import json
import logging
from dataclasses import asdict, dataclass, field
from datetime import datetime
from functools import partial
from typing import Type, cast

from apps.graphrag.configs import *
from .kg.es_impl import EsVectorDBStorage
from .kg.mysql_impl import MysqlKVStorage, MysqlRDBStorage
from .llm import (
    bge_async_embedding,
    aigc_app_complete,
    aigc_app_client
)
from .operate import (
    chunking_by_token_size,
    extract_entities,
    local_query,
    global_query,
    hybrid_query,
    naive_query,
    lookup_process_vdb, kg_query_with_keywords, extract_keywords_only
)
from .prompt import GRAPH_FIELD_SEP

from .utils import (
    EmbeddingFunc,
    compute_mdhash_id,
    limit_async_func_call,
    convert_response_to_json,
    logger,
    set_logger,
    reset_logger,
)
from .base import (
    BaseGraphStorage,
    BaseKVStorage,
    BaseVectorStorage,
    StorageNameSpace,
    QueryParam,
    RagMode,
    BaseRDBStorage,
)

from .storage import (
    <PERSON>son<PERSON>VStorage,
    NanoVectorDBStorage,
    NetworkXStorage,
)

from .kg.neo4j_impl import Neo4JStorage
from .kg.gql_impl import GqlStorage

from .kg.oracle_impl import OracleKVStorage, OracleGraphStorage, OracleVectorDBStorage
from controller.knowledge_controller import KnowledgeController
from ..exceptions.graph_rag_exception import GraphRagException


def always_get_an_event_loop() -> asyncio.AbstractEventLoop:
    try:
        current_loop = asyncio.get_event_loop()
        if current_loop.is_closed():
            raise RuntimeError("Event loop is closed.")
        return current_loop
    except RuntimeError:
        logger.info("Creating a new event loop in main thread.")
        new_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(new_loop)
        return new_loop


@dataclass
class GraphRAG:
    storage_namespace: str = field(default=DEFAULT_STORAGE_NAMESPACE)
    working_dir: str = field(
        default_factory=lambda: f"./cache_dir_{datetime.now().strftime('%Y-%m-%d-%H:%M:%S')}"
    )

    kv_storage: str = field(default=KV_STORAGE)
    rdb_storage: str = field(default=RDB_STORAGE)
    vector_storage: str = field(default=VECTOR_STORAGE)
    graph_storage: str = field(default=GRAPH_STORAGE)

    current_log_level = logger.level
    log_level: str = field(default=current_log_level)

    # text chunking
    chunk_token_size: int = CHUNK_TOKEN_SIZE
    chunk_overlap_token_size: int = CHUNK_OVERLAP_TOKEN_SIZE
    tiktoken_model_name: str = TIKTOKEN_MODEL_NAME

    # entity extraction
    entity_extract_max_gleaning: int = ENTITY_EXTRACT_MAX_GLEANING
    entity_summary_to_max_tokens: int = ENTITY_SUMMARY_TO_MAX_TOKENS

    # node embedding
    node_embedding_algorithm: str = "node2vec"
    node2vec_params: dict = field(
        default_factory=lambda: {
            "dimensions": 1536,
            "num_walks": 10,
            "walk_length": 40,
            "window_size": 2,
            "iterations": 3,
            "random_seed": 3,
        }
    )

    embedding_cache_config: dict = field(
        default_factory=lambda: {
            "enabled": False,
            "similarity_threshold": 0.95,
            "use_llm_check": False,
        }
    )
    embedding_func: EmbeddingFunc = field(default_factory=lambda: bge_async_embedding)
    embedding_batch_num: int = 16
    embedding_func_max_async: int = 16

    # LLM
    llm_model_func: callable = aigc_app_complete  # hf_model_complete#
    llm_model_func_client: callable = aigc_app_client
    llm_model_name: str = "qwen1.5-7b-chat"
    llm_model_max_token_size: int = 32768
    llm_model_max_async: int = 16
    llm_model_kwargs: dict = field(default_factory=dict)

    # storage
    vector_db_storage_cls_kwargs: dict = field(default_factory=dict)

    enable_llm_cache: bool = False

    # extension
    addon_params: dict = field(default_factory=dict)
    convert_response_to_json_func: callable = convert_response_to_json

    # project
    corp_id: str = field(default="")
    record_id: int = field(default=0)
    project_logger: logging.Logger = field(default=None)

    def __post_init__(self):
        if self.project_logger is None:
            set_logger()
            logger.setLevel(self.log_level)
        else:
            reset_logger(self.project_logger)

        _print_config = ",\n  ".join([f"{k} = {v}" for k, v in asdict(self).items()])
        logger.info(f"Logger initialized with level {self.log_level}, param: :\n  {_print_config}\n")

        self.key_string_value_json_storage_cls: Type[BaseKVStorage] = (
            self._get_storage_class()[self.kv_storage]
        )
        self.record_db_storage_cls: Type[BaseRDBStorage] = (
            self._get_storage_class()[self.rdb_storage]
        )
        self.vector_db_storage_cls: Type[BaseVectorStorage] = self._get_storage_class()[
            self.vector_storage
        ]
        self.graph_storage_cls: Type[BaseGraphStorage] = self._get_storage_class()[
            self.graph_storage
        ]

        # if not os.path.exists(self.working_dir):
        #     logger.info(f"Creating working directory {self.working_dir}")
        #     os.makedirs(self.working_dir)

        self.llm_response_cache = (
            self.key_string_value_json_storage_cls(
                namespace="llm_response_cache",
                global_config=asdict(self),
                embedding_func=None,
            )
            if self.enable_llm_cache
            else None
        )

        self.embedding_func = limit_async_func_call(self.embedding_func_max_async)(
            self.embedding_func
        )

        ####
        # add embedding func by walter
        ####
        self.full_docs = self.key_string_value_json_storage_cls(
            namespace="full_docs",
            global_config=asdict(self),
            embedding_func=self.embedding_func,
        )
        self.text_chunks = self.key_string_value_json_storage_cls(
            namespace="text_chunks",
            global_config=asdict(self),
            embedding_func=self.embedding_func,
        )

        self.record_store = self.record_db_storage_cls(
            namespace="record_store",
            global_config=asdict(self),
        )

        # consider singleton and use namespace?
        self.chunk_entity_relation_graph = self.graph_storage_cls(
            namespace=self.storage_namespace,
            global_config=asdict(self)
        )
        ####
        # add embedding func by walter over
        ####

        self.entities_vdb = self.vector_db_storage_cls(
            namespace="entities",
            global_config=asdict(self),
            embedding_func=self.embedding_func,
            meta_fields={"entity_name", "knowledge_id", "doc_id", "chunk_id", "delete_flag", "hit_count"},
        )
        self.relationships_vdb = self.vector_db_storage_cls(
            namespace="relationships",
            global_config=asdict(self),
            embedding_func=self.embedding_func,
            meta_fields={"src_id", "tgt_id", "knowledge_id", "doc_id", "chunk_id", "delete_flag", "hit_count"},
        )
        self.chunks_vdb = self.vector_db_storage_cls(
            namespace="chunks",
            global_config=asdict(self),
            embedding_func=self.embedding_func,
        )

        self.llm_model_func = limit_async_func_call(self.llm_model_max_async)(
            partial(
                self.llm_model_func,
                hashing_kv=self.llm_response_cache,
                **self.llm_model_kwargs,
            )
        )

    def _get_storage_class(self) -> Type[BaseGraphStorage]:
        return {
            # kv storage
            "JsonKVStorage": JsonKVStorage,
            "OracleKVStorage": OracleKVStorage,
            "MysqlKVStorage": MysqlKVStorage,
            # vector storage
            "NanoVectorDBStorage": NanoVectorDBStorage,
            "OracleVectorDBStorage": OracleVectorDBStorage,
            "EsVectorDBStorage": EsVectorDBStorage,
            # graph storage
            "NetworkXStorage": NetworkXStorage,
            "Neo4JStorage": Neo4JStorage,
            "GqlStorage": GqlStorage,
            "OracleGraphStorage": OracleGraphStorage,
            # "ArangoDBStorage": ArangoDBStorage,
            # rdb storage
            "MysqlRDBStorage": MysqlRDBStorage
        }

    def insert(self, work_id:int):
        loop = always_get_an_event_loop()
        return loop.run_until_complete(self.ainsert(work_id))

    async def ainsert(self, work_id:int):
        if not work_id:
            logger.info("No work_id provided")
            return
        update_storage = True
        report = ""
        try:
            chunks, report, exceed_limit_flag = self.process_chunks(work_id)
            if exceed_limit_flag:
                logger.warning(f"exceed_limit_flag is True, work id: {work_id}")
            inserting_chunks = {}
            i = 0
            for chunk in chunks:
                inserting_chunks[chunk["chunk_id"]] = {
                    "chunk_order_index": i,
                    "content": chunk["content"],
                    "full_doc_id": work_id,
                    "tokens": chunk["tokens"]
                }
                i += 1

            logger.info("[Entity Extraction]...")
            maybe_new_kg = await extract_entities(
                inserting_chunks,
                knowledge_graph_inst=self.chunk_entity_relation_graph,
                entity_vdb=self.entities_vdb,
                relationships_vdb=self.relationships_vdb,
                record_store=self.record_store,
                global_config=asdict(self),
                work_id=work_id
            )
            if maybe_new_kg is None:
                logger.warning("No new entities and relationships found")
                return
            self.chunk_entity_relation_graph = maybe_new_kg
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
        finally:
            if update_storage:
                await self._insert_done()
        return report

    def process_chunks(self, knowledge_id):
        exceed_limit_flag = False
        knowledge_controller = KnowledgeController(self.corp_id)
        doc_list = knowledge_controller.get_knowledge_doc_list(knowledge_id, {'is_all': 'all'})

        total_count = 0
        collected_chunks = {}
        all_chunks = []

        for doc in doc_list['data_list']:
            doc_id = doc['document_id']
            doc_name = doc['doc_name']

            args = {
                'page_no': 1,
                'page_size': CHUNKS_LIMIT + 1,
                'document_id': doc_id,
                'status': 1,
                'key_word': ''
            }

            data = knowledge_controller.get_knowledge_doc_chunk_list(doc_id, args)

            chunks = data['data_list']
            current_count = data['total']

            for chunk in chunks:
                if chunk['character_count'] > CHUNK_CHAR_MAX:
                    raise GraphRagException(
                        message="A chunk exceeds the maximum allowed character count.",
                        error_code=400,
                        details={"knowledge_id": knowledge_id}
                    )

            total_count += current_count

            if total_count >= CHUNKS_LIMIT:
                print(f"Reached CHUNKS_LIMIT: {CHUNKS_LIMIT}. Stopping.")
                exceed_limit_flag = True
                break

            print(f"Use doc_id: {doc_id}, current_count: {current_count}, total_count: {total_count}")
            collected_chunks[doc_id] = {
                'doc_name': doc_name,
                'count': current_count
            }

            all_chunks.extend(chunks)

        collected_chunks_json = json.dumps(collected_chunks, ensure_ascii=False)
        print(collected_chunks_json)
        return all_chunks, collected_chunks_json, exceed_limit_flag


    async def _naive_handle(self, string_or_strings):
        update_storage = False
        try:
            if isinstance(string_or_strings, str):
                string_or_strings = [string_or_strings]

            new_docs = {
                compute_mdhash_id(c.strip(), prefix="doc-"): {"content": c.strip()}
                for c in string_or_strings
            }
            _add_doc_keys = await self.full_docs.filter_keys(list(new_docs.keys()))
            new_docs = {k: v for k, v in new_docs.items() if k in _add_doc_keys}
            if not len(new_docs):
                logger.warning("All docs are already in the storage")
                return
            update_storage = True
            logger.info(f"[New Docs] inserting {len(new_docs)} docs")

            inserting_chunks = {}
            for doc_key, doc in new_docs.items():
                chunks = {
                    compute_mdhash_id(dp["content"], prefix="chunk-"): {
                        **dp,
                        "full_doc_id": doc_key,
                    }
                    for dp in chunking_by_token_size(
                        doc["content"],
                        overlap_token_size=self.chunk_overlap_token_size,
                        max_token_size=self.chunk_token_size,
                        tiktoken_model=self.tiktoken_model_name,
                    )
                }
                inserting_chunks.update(chunks)
            _add_chunk_keys = await self.text_chunks.filter_keys(
                list(inserting_chunks.keys())
            )
            inserting_chunks = {
                k: v for k, v in inserting_chunks.items() if k in _add_chunk_keys
            }
            if not len(inserting_chunks):
                logger.warning("All chunks are already in the storage")
                return
            logger.info(f"[New Chunks] inserting {len(inserting_chunks)} chunks")
        finally:
            if update_storage:
                await self._insert_done()

    async def _insert_done(self):
        tasks = []
        total_tasks = sum(
            1 for storage_inst in [
                self.full_docs,
                self.text_chunks,
                self.llm_response_cache,
                self.entities_vdb,
                self.relationships_vdb,
                self.chunks_vdb,
                self.chunk_entity_relation_graph,
            ] if storage_inst is not None
        )

        completed_tasks = 0

        async def track_progress(task, task_index):
            nonlocal completed_tasks
            result = await task
            completed_tasks += 1
            print(f"Task {task_index + 1}/{total_tasks} completed. Progress: {completed_tasks}/{total_tasks}")
            return result

        for index, storage_inst in enumerate([
            self.full_docs,
            self.text_chunks,
            self.llm_response_cache,
            self.entities_vdb,
            self.relationships_vdb,
            self.chunks_vdb,
            self.chunk_entity_relation_graph,
        ]):
            if storage_inst is None:
                continue
            task = cast(StorageNameSpace, storage_inst).index_done_callback()
            tasks.append(track_progress(task, index))

        await asyncio.gather(*tasks)

    def query(self, query: str, param: QueryParam = QueryParam()):
        loop = always_get_an_event_loop()
        return loop.run_until_complete(self.aquery(query, param))

    async def aquery(self, query: str, param: QueryParam = QueryParam()):
        if param.mode == RagMode.LOCAL.value:
            response = await local_query(
                query,
                self.chunk_entity_relation_graph,
                self.entities_vdb,
                self.relationships_vdb,
                self.text_chunks,
                param,
                asdict(self),
            )
        elif param.mode == RagMode.GLOBAL.value:
            response = await global_query(
                query,
                self.chunk_entity_relation_graph,
                self.entities_vdb,
                self.relationships_vdb,
                self.text_chunks,
                param,
                asdict(self),
                hashing_kv=self.llm_response_cache
                if self.llm_response_cache
                   and hasattr(self.llm_response_cache, "global_config")
                else self.key_string_value_json_storage_cls(
                    namespace="llm_response_cache",
                    global_config=asdict(self),
                    embedding_func=None,
                ),
            )
        elif param.mode == RagMode.HYBRID.value:
            response = await hybrid_query(
                query,
                self.chunk_entity_relation_graph,
                self.entities_vdb,
                self.relationships_vdb,
                self.text_chunks,
                param,
                asdict(self),
                hashing_kv=self.llm_response_cache
                if self.llm_response_cache
                   and hasattr(self.llm_response_cache, "global_config")
                else None,
            )
        elif param.mode == RagMode.NAIVE.value:
            response = await naive_query(
                query,
                self.chunks_vdb,
                self.text_chunks,
                param,
                asdict(self),
            )
        else:
            raise ValueError(f"Unknown mode {param.mode}")
        await self._query_done()
        return response

    def query_wrapper(self, query: str, work_id: int, app_info: dict):
        loop = always_get_an_event_loop()
        use_model_func = asdict(self)["llm_model_func_client"]
        query_param = QueryParam()
        query_param.work_id = work_id
        query_param.app_info = app_info
        query_param.mode = RAG_MODE
        return use_model_func(query, loop.run_until_complete(self.aquery(query, query_param)), **app_info)


    async def _query_done(self):
        tasks = []
        for storage_inst in [self.llm_response_cache]:
            if storage_inst is None:
                continue
            tasks.append(cast(StorageNameSpace, storage_inst).index_done_callback())
        await asyncio.gather(*tasks)


    def insert_custom_kg(self, custom_kg: dict):
        loop = always_get_an_event_loop()
        return loop.run_until_complete(self.ainsert_custom_kg(custom_kg))

    async def ainsert_custom_kg(self, custom_kg: dict):
        update_storage = False
        try:
            all_chunks_data = {}
            chunk_to_source_map = {}
            for chunk_data in custom_kg.get("chunks", []):
                chunk_content = chunk_data["content"]
                source_id = chunk_data["source_id"]
                chunk_id = compute_mdhash_id(chunk_content.strip(), prefix="chunk-")

                chunk_entry = {"content": chunk_content.strip(), "source_id": source_id}
                all_chunks_data[chunk_id] = chunk_entry
                chunk_to_source_map[source_id] = chunk_id
                update_storage = True

            if self.chunks_vdb is not None and all_chunks_data:
                await self.chunks_vdb.upsert(all_chunks_data)
            if self.text_chunks is not None and all_chunks_data:
                await self.text_chunks.upsert(all_chunks_data)

            all_entities_data = []
            for entity_data in custom_kg.get("entities", []):
                entity_name = f'"{entity_data["entity_name"].upper()}"'
                entity_type = entity_data.get("entity_type", "UNKNOWN")
                description = entity_data.get("description", "No description provided")
                # source_id = entity_data["source_id"]
                source_chunk_id = entity_data.get("source_id", "UNKNOWN")
                source_id = chunk_to_source_map.get(source_chunk_id, "UNKNOWN")

                if source_id == "UNKNOWN":
                    logger.warning(
                        f"Entity '{entity_name}' has an UNKNOWN source_id. Please check the source mapping."
                    )

                node_data = {
                    "entity_type": entity_type,
                    "description": description,
                    "source_id": source_id,
                }
                await self.chunk_entity_relation_graph.upsert_node(
                    entity_name, node_data=node_data
                )
                node_data["entity_name"] = entity_name
                all_entities_data.append(node_data)
                update_storage = True

            all_relationships_data = []
            for relationship_data in custom_kg.get("relationships", []):
                src_id = f'"{relationship_data["src_id"].upper()}"'
                tgt_id = f'"{relationship_data["tgt_id"].upper()}"'
                description = relationship_data["description"]
                keywords = relationship_data["keywords"]
                weight = relationship_data.get("weight", 1.0)
                # source_id = relationship_data["source_id"]
                source_chunk_id = relationship_data.get("source_id", "UNKNOWN")
                source_id = chunk_to_source_map.get(source_chunk_id, "UNKNOWN")

                if source_id == "UNKNOWN":
                    logger.warning(
                        f"Relationship from '{src_id}' to '{tgt_id}' has an UNKNOWN source_id. Please check the source mapping."
                    )

                for need_insert_id in [src_id, tgt_id]:
                    if not (
                        await self.chunk_entity_relation_graph.has_node(need_insert_id)
                    ):
                        await self.chunk_entity_relation_graph.upsert_node(
                            need_insert_id,
                            node_data={
                                "source_id": source_id,
                                "description": "UNKNOWN",
                                "entity_type": "UNKNOWN",
                            },
                        )

                await self.chunk_entity_relation_graph.upsert_edge(
                    src_id,
                    tgt_id,
                    edge_data={
                        "weight": weight,
                        "description": description,
                        "keywords": keywords,
                        "source_id": source_id,
                    },
                )
                edge_data = {
                    "src_id": src_id,
                    "tgt_id": tgt_id,
                    "description": description,
                    "keywords": keywords,
                }
                all_relationships_data.append(edge_data)
                update_storage = True

            if self.entities_vdb is not None:
                data_for_vdb = {
                    compute_mdhash_id(dp["entity_name"], prefix="ent-"): {
                        "content": dp["entity_name"] + dp["description"],
                        "entity_name": dp["entity_name"],
                    }
                    for dp in all_entities_data
                }
                await self.entities_vdb.upsert(data_for_vdb)

            if self.relationships_vdb is not None:
                data_for_vdb = {
                    compute_mdhash_id(dp["src_id"] + dp["tgt_id"], prefix="rel-"): {
                        "src_id": dp["src_id"],
                        "tgt_id": dp["tgt_id"],
                        "content": dp["keywords"]
                        + dp["src_id"]
                        + dp["tgt_id"]
                        + dp["description"],
                    }
                    for dp in all_relationships_data
                }
                await self.relationships_vdb.upsert(data_for_vdb)
        finally:
            if update_storage:
                await self._insert_done()


    async def aquery_with_separate_keyword_extraction(
        self, query: str, prompt: str, param: QueryParam = QueryParam()
    ):
        hl_keywords, ll_keywords = await extract_keywords_only(
            text=query,
            param=param,
            global_config=asdict(self),
            hashing_kv=self.llm_response_cache
            or self.key_string_value_json_storage_cls(
                namespace="llm_response_cache",
                global_config=asdict(self),
                embedding_func=None,
            ),
        )

        param.hl_keywords = (hl_keywords,)
        param.ll_keywords = (ll_keywords,)
        ll_keywords_str = ", ".join(ll_keywords)
        hl_keywords_str = ", ".join(hl_keywords)
        formatted_question = f"{prompt}\n\n### Keywords:\nHigh-level: {hl_keywords_str}\nLow-level: {ll_keywords_str}\n\n### Query:\n{query}"

        if param.mode in ["local", "global", "hybrid"]:
            response = await kg_query_with_keywords(
                formatted_question,
                self.chunk_entity_relation_graph,
                self.entities_vdb,
                self.relationships_vdb,
                self.text_chunks,
                param,
                asdict(self),
                hashing_kv=self.llm_response_cache
                if self.llm_response_cache
                and hasattr(self.llm_response_cache, "global_config")
                else self.key_string_value_json_storage_cls(
                    namespace="llm_response_cache",
                    global_config=asdict(self),
                    embedding_func=None,
                ),
            )
        elif param.mode == "naive":
            response = await naive_query(
                formatted_question,
                self.chunks_vdb,
                self.text_chunks,
                param,
                asdict(self),
                hashing_kv=self.llm_response_cache
                if self.llm_response_cache
                and hasattr(self.llm_response_cache, "global_config")
                else self.key_string_value_json_storage_cls(
                    namespace="llm_response_cache",
                    global_config=asdict(self),
                    embedding_func=None,
                ),
            )
        else:
            raise ValueError(f"Unknown mode {param.mode}")

        await self._query_done()
        return response

    def query_with_separate_keyword_extraction(
        self, query: str, prompt: str, param: QueryParam = QueryParam()
    ):
        loop = always_get_an_event_loop()
        return loop.run_until_complete(
            self.aquery_with_separate_keyword_extraction(query, prompt, param)
        )


    def lookup_process_vdb(self, work_id:int):
        loop = always_get_an_event_loop()
        return loop.run_until_complete(
            lookup_process_vdb(
                self.entities_vdb,
                self.relationships_vdb,
                self.chunk_entity_relation_graph,
                work_id
            )
        )

    async def handle_graph_change(self, chunk_ids, doc_id, edges, nodes):
        entities_to_delete = set()
        entities_to_update = {}  # entity_name -> new_source_id
        relationships_to_delete = set()
        relationships_to_update = {}  # (src, tgt) -> new_source_id

        for node, data in nodes:
            if "source_id" in data:
                sources = set(data["source_id"].split(GRAPH_FIELD_SEP))
                sources.difference_update(chunk_ids)
                if not sources:
                    entities_to_delete.add(node)
                    logger.debug(
                        f"Entity {node} marked for deletion - no remaining sources"
                    )
                else:
                    new_source_id = GRAPH_FIELD_SEP.join(sources)
                    entities_to_update[node] = new_source_id
                    logger.debug(
                        f"Entity {node} will be updated with new source_id: {new_source_id}"
                    )

        for src, tgt, data in edges:
            if "source_id" in data:
                sources = set(data["source_id"].split(GRAPH_FIELD_SEP))
                sources.difference_update(chunk_ids)
                if not sources:
                    relationships_to_delete.add((src, tgt))
                    logger.debug(
                        f"Relationship {src}-{tgt} marked for deletion - no remaining sources"
                    )
                else:
                    new_source_id = GRAPH_FIELD_SEP.join(sources)
                    relationships_to_update[(src, tgt)] = new_source_id
                    logger.debug(
                        f"Relationship {src}-{tgt} will be updated with new source_id: {new_source_id}"
                    )
        if entities_to_delete:
            for entity in entities_to_delete:
                await self.entities_vdb.delete_entity(entity)
                logger.debug(f"Deleted entity {entity} from vector DB")
            self.chunk_entity_relation_graph.remove_nodes(list(entities_to_delete))
            logger.debug(f"Deleted {len(entities_to_delete)} entities from graph")

        for entity, new_source_id in entities_to_update.items():
            node_data = self.chunk_entity_relation_graph._graph.nodes[entity]
            node_data["source_id"] = new_source_id
            await self.chunk_entity_relation_graph.upsert_node(entity, node_data)
            logger.debug(
                f"Updated entity {entity} with new source_id: {new_source_id}"
            )

        if relationships_to_delete:
            for src, tgt in relationships_to_delete:
                rel_id_0 = compute_mdhash_id(src + tgt, prefix="rel-")
                rel_id_1 = compute_mdhash_id(tgt + src, prefix="rel-")
                await self.relationships_vdb.delete([rel_id_0, rel_id_1])
                logger.debug(f"Deleted relationship {src}-{tgt} from vector DB")
            self.chunk_entity_relation_graph.remove_edges(
                list(relationships_to_delete)
            )
            logger.debug(
                f"Deleted {len(relationships_to_delete)} relationships from graph"
            )

        for (src, tgt), new_source_id in relationships_to_update.items():
            edge_data = self.chunk_entity_relation_graph._graph.edges[src, tgt]
            edge_data["source_id"] = new_source_id
            await self.chunk_entity_relation_graph.upsert_edge(src, tgt, edge_data)
            logger.debug(
                f"Updated relationship {src}-{tgt} with new source_id: {new_source_id}"
            )

        # handle this in outer function
        # await self.full_docs.delete([doc_id])
        # await self.doc_status.delete([doc_id])

        await self._insert_done()
        logger.info(
            f"Successfully deleted document {doc_id} and related data. "
            f"Deleted {len(entities_to_delete)} entities and {len(relationships_to_delete)} relationships. "
            f"Updated {len(entities_to_update)} entities and {len(relationships_to_update)} relationships."
        )


    async def adelete_by_doc_id(self, doc_id: str):
        try:
            # delete doc ori data
            doc_status = await self.doc_status.get(doc_id)
            if not doc_status:
                logger.warning(f"Document {doc_id} not found")
                return

            logger.debug(f"Starting deletion for document {doc_id}")

            chunks = await self.text_chunks.filter(
                lambda x: x.get("full_doc_id") == doc_id
            )
            chunk_ids = list(chunks.keys())
            logger.debug(f"Found {len(chunk_ids)} chunks to delete")

            # Notice: currently need to record the doc and chunk ids to proceed
            for chunk_id in chunk_ids:
                entities = [
                    dp
                    for dp in self.entities_vdb.client_storage["data"]
                    if dp.get("source_id") == chunk_id
                ]
                logger.debug(f"Chunk {chunk_id} has {len(entities)} related entities")

                relations = [
                    dp
                    for dp in self.relationships_vdb.client_storage["data"]
                    if dp.get("source_id") == chunk_id
                ]
                logger.debug(f"Chunk {chunk_id} has {len(relations)} related relations")

            # need not delete here
            # if chunk_ids:
            #     await self.chunks_vdb.delete(chunk_ids)
            #     await self.text_chunks.delete(chunk_ids)

            nodes = self.chunk_entity_relation_graph._graph.nodes(data=True)
            edges = self.chunk_entity_relation_graph._graph.edges(data=True)

            await self.handle_graph_change(chunk_ids, doc_id, edges, nodes)

            # Add verification step
            async def verify_deletion():
                # Verify if the document has been deleted
                if await self.full_docs.get_by_id(doc_id):
                    logger.error(f"Document {doc_id} still exists in full_docs")

                # Verify if chunks have been deleted
                remaining_chunks = await self.text_chunks.filter(
                    lambda x: x.get("full_doc_id") == doc_id
                )
                if remaining_chunks:
                    logger.error(f"Found {len(remaining_chunks)} remaining chunks")

                # Verify entities and relationships
                for chunk_id in chunk_ids:
                    # Check entities
                    entities_with_chunk = [
                        dp
                        for dp in self.entities_vdb.client_storage["data"]
                        if chunk_id
                        in (dp.get("source_id") or "").split(GRAPH_FIELD_SEP)
                    ]
                    if entities_with_chunk:
                        logger.error(
                            f"Found {len(entities_with_chunk)} entities still referencing chunk {chunk_id}"
                        )

                    # Check relationships
                    relations_with_chunk = [
                        dp
                        for dp in self.relationships_vdb.client_storage["data"]
                        if chunk_id
                        in (dp.get("source_id") or "").split(GRAPH_FIELD_SEP)
                    ]
                    if relations_with_chunk:
                        logger.error(
                            f"Found {len(relations_with_chunk)} relations still referencing chunk {chunk_id}"
                        )

            await verify_deletion()

        except Exception as e:
            logger.error(f"Error while deleting document {doc_id}: {e}")


    def delete_by_doc_id(self, doc_id: str):
        """Synchronous version of adelete"""
        return asyncio.run(self.adelete_by_doc_id(doc_id))

    async def get_entity_info(
        self, entity_name: str, include_vector_data: bool = False
    ):
        entity_name = f'"{entity_name.upper()}"'

        # Get information from the graph
        node_data = await self.chunk_entity_relation_graph.get_node(entity_name)
        source_id = node_data.get("source_id") if node_data else None

        result = {
            "entity_name": entity_name,
            "source_id": source_id,
            "graph_data": node_data,
        }

        # Optional: Get vector database information
        if include_vector_data:
            entity_id = compute_mdhash_id(entity_name, prefix="ent-")
            vector_data = self.entities_vdb._client.get([entity_id])
            result["vector_data"] = vector_data[0] if vector_data else None

        return result

    def get_entity_info_sync(self, entity_name: str, include_vector_data: bool = False):
        try:
            import tracemalloc

            tracemalloc.start()
            return asyncio.run(self.get_entity_info(entity_name, include_vector_data))
        finally:
            tracemalloc.stop()

    async def get_relation_info(
        self, src_entity: str, tgt_entity: str, include_vector_data: bool = False
    ):
        src_entity = f'"{src_entity.upper()}"'
        tgt_entity = f'"{tgt_entity.upper()}"'

        # Get information from the graph
        edge_data = await self.chunk_entity_relation_graph.get_edge(
            src_entity, tgt_entity
        )
        source_id = edge_data.get("source_id") if edge_data else None

        result = {
            "src_entity": src_entity,
            "tgt_entity": tgt_entity,
            "source_id": source_id,
            "graph_data": edge_data,
        }

        # Optional: Get vector database information
        if include_vector_data:
            rel_id = compute_mdhash_id(src_entity + tgt_entity, prefix="rel-")
            vector_data = self.relationships_vdb._client.get([rel_id])
            result["vector_data"] = vector_data[0] if vector_data else None

        return result

    def get_relation_info_sync(
        self, src_entity: str, tgt_entity: str, include_vector_data: bool = False
    ):
        try:
            import tracemalloc

            tracemalloc.start()
            return asyncio.run(
                self.get_relation_info(src_entity, tgt_entity, include_vector_data)
            )
        finally:
            tracemalloc.stop()

    def insert_custom_chunks(self, full_text: str, text_chunks: list[str]):
        loop = always_get_an_event_loop()
        return loop.run_until_complete(
            self.ainsert_custom_chunks(full_text, text_chunks)
        )

    async def ainsert_custom_chunks(self, full_text: str, text_chunks: list[str]):
        update_storage = False
        try:
            doc_key = compute_mdhash_id(full_text.strip(), prefix="doc-")
            new_docs = {doc_key: {"content": full_text.strip()}}

            _add_doc_keys = await self.full_docs.filter_keys([doc_key])
            new_docs = {k: v for k, v in new_docs.items() if k in _add_doc_keys}
            if not len(new_docs):
                logger.warning("This document is already in the storage.")
                return

            update_storage = True
            logger.info(f"[New Docs] inserting {len(new_docs)} docs")

            inserting_chunks = {}
            for chunk_text in text_chunks:
                chunk_text_stripped = chunk_text.strip()
                chunk_key = compute_mdhash_id(chunk_text_stripped, prefix="chunk-")

                inserting_chunks[chunk_key] = {
                    "content": chunk_text_stripped,
                    "full_doc_id": doc_key,
                }

            _add_chunk_keys = await self.text_chunks.filter_keys(
                list(inserting_chunks.keys())
            )
            inserting_chunks = {
                k: v for k, v in inserting_chunks.items() if k in _add_chunk_keys
            }
            if not len(inserting_chunks):
                logger.warning("All chunks are already in the storage.")
                return

            logger.info(f"[New Chunks] inserting {len(inserting_chunks)} chunks")

            await self.chunks_vdb.upsert(inserting_chunks)

            logger.info("[Entity Extraction]...")
            maybe_new_kg = await extract_entities(
                inserting_chunks,
                knowledge_graph_inst=self.chunk_entity_relation_graph,
                entity_vdb=self.entities_vdb,
                relationships_vdb=self.relationships_vdb,
                global_config=asdict(self),
            )

            if maybe_new_kg is None:
                logger.warning("No new entities and relationships found")
                return
            else:
                self.chunk_entity_relation_graph = maybe_new_kg

            await self.full_docs.upsert(new_docs)
            await self.text_chunks.upsert(inserting_chunks)

        finally:
            if update_storage:
                await self._insert_done()

    def delete_by_entity(self, entity_name: str):
        loop = always_get_an_event_loop()
        return loop.run_until_complete(self.adelete_by_entity(entity_name))

    async def adelete_by_entity(self, entity_name: str):
        entity_name = f'"{entity_name.upper()}"'

        try:
            await self.entities_vdb.delete_entity(entity_name)
            await self.relationships_vdb.delete_relation(entity_name)
            await self.chunk_entity_relation_graph.delete_node(entity_name)

            logger.info(
                f"Entity '{entity_name}' and its relationships have been deleted."
            )
            await self._delete_by_entity_done()
        except Exception as e:
            logger.error(f"Error while deleting entity '{entity_name}': {e}")

    async def _delete_by_entity_done(self):
        tasks = []
        for storage_inst in [
            self.entities_vdb,
            self.relationships_vdb,
            self.chunk_entity_relation_graph,
        ]:
            if storage_inst is None:
                continue
            tasks.append(cast(StorageNameSpace, storage_inst).index_done_callback())
        await asyncio.gather(*tasks)
