# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: es_impl.py
# @Author: sun<PERSON>
# @E-mail: <EMAIL>
# @Site: 
# @Time: 2024/11/26
# ---
import asyncio
import os
from dataclasses import dataclass

import numpy as np

from apps.graphrag.manager.base import BaseVectorStorage
from puppet.es_sdk import EsSdkPool
from apps.graphrag.configs import TEMP_CORP_ID, ES_BULK_BATCH_SIZE

from ..utils import (
    compute_mdhash_id,
    logger,
)

@dataclass
class EsVectorDBStorage(BaseVectorStorage):
    cosine_better_than_threshold: float = 0.2

    def __post_init__(self):
        self._client_file_name = os.path.join(
            self.global_config["working_dir"], f"vdb_{self.namespace}.json"
        )
        self._max_batch_size = self.global_config["embedding_batch_num"]

        # TODO
        self._client = EsSdkPool(TEMP_CORP_ID)
        self.cosine_better_than_threshold = self.global_config.get(
            "cosine_better_than_threshold", self.cosine_better_than_threshold
        )

    async def upsert(self, data: dict[str, dict], **kwargs):
        logger.info(f"Inserting {len(data)} vectors to {self.namespace}")

        index_name = kwargs.get("index_name", None)
        mapping = kwargs.get("mapping", None)
        if not index_name:
            raise ValueError("index_name is required for this implementation")
        if not mapping:
            raise ValueError("mapping is required for this implementation")

        if not len(data):
            logger.warning("You insert an empty data to vector DB")
            return []
        list_data = [
            {
                "__id__": k,
                **{k1: v1 for k1, v1 in v.items() if k1 in self.meta_fields},
            }
            for k, v in data.items()
        ]
        contents = [v["content"] for v in data.values()]
        batches = [
            contents[i: i + self._max_batch_size]
            for i in range(0, len(contents), self._max_batch_size)
        ]
        embeddings_list = await asyncio.gather(
            *[self.embedding_func(batch) for batch in batches]
        )
        embeddings = np.concatenate(embeddings_list)
        for i, d in enumerate(list_data):
            d["__vector__"] = embeddings[i]

        # provide some buffer
        batch_size = ES_BULK_BATCH_SIZE
        results = []
        for i in range(0, len(list_data), batch_size):
            batch = list_data[i:i + batch_size]
            batch_result = self._client.insert_bulk_data(index_name, batch, mapping)
            results.extend(batch_result)

        return results

    async def query(self, query: str, top_k=5, **kwargs):
        index_name = kwargs.get("index_name", None)
        if not index_name:
            raise ValueError("index_name is required for this implementation")
        knowledge_id = kwargs.get("knowledge_id", None)
        if not knowledge_id:
            raise ValueError("knowledge_id is required for this implementation")

        embedding = await self.embedding_func([query])
        embedding = embedding[0]
        results = self._search_vdb_top(embedding, index_name, knowledge_id, top_k)
        results = [
            {**dp["_source"], "id": dp["_id"], "distance": dp["_score"]} for dp in results
        ]
        return results

    def _search_vdb_top(self, embedding, index_name, knowledge_id, top_k=10, min_score=2.0):
        try:
            body_dict = {
                "_source": {
                    "includes": ["entity_name", "knowledge_id", "src_id", "tgt_id", "_score"]
                },
                "query": {
                    "bool": {
                        "must": [
                            {
                                "terms": {
                                    "knowledge_id": [knowledge_id]
                                }
                            },
                            {
                                "script_score": {
                                    "query": {
                                        "match_all": {}
                                    },
                                    "script": {
                                        "source": "cosineSimilarity(params.query_vector, '__vector__') + 1.0",
                                        "params": {
                                            "query_vector": embedding
                                        }
                                    }
                                }
                            }
                        ],
                    }
                },
                "size": top_k,
                "min_score": min_score,
                "sort": [
                    {"_score": {"order": "desc"}}
                ]
            }

            hits_ = self._client.search_data(index_name, body_dict)
            hits = hits_.body['hits']['hits']

            print("*" * 35)
            print(f"Search es embeddings demo app index: {index_name}, hits size: {len(hits)}")
            return hits

        except Exception as e:
            print(e, '==============')
            return []

    @property
    def client_storage(self):
        return getattr(self._client, "_NanoVectorDB__storage")

    async def delete_entity(self, entity_name: str):
        try:
            entity_id = [compute_mdhash_id(entity_name, prefix="ent-")]

            if self._client.get(entity_id):
                self._client.delete(entity_id)
                logger.info(f"Entity {entity_name} have been deleted.")
            else:
                logger.info(f"No entity found with name {entity_name}.")
        except Exception as e:
            logger.error(f"Error while deleting entity {entity_name}: {e}")

    async def delete_relation(self, entity_name: str):
        try:
            relations = [
                dp
                for dp in self.client_storage["data"]
                if dp["src_id"] == entity_name or dp["tgt_id"] == entity_name
            ]
            ids_to_delete = [relation["__id__"] for relation in relations]

            if ids_to_delete:
                self._client.delete(ids_to_delete)
                logger.info(
                    f"All relations related to entity {entity_name} have been deleted."
                )
            else:
                logger.info(f"No relations found for entity {entity_name}.")
        except Exception as e:
            logger.error(
                f"Error while deleting relations for entity {entity_name}: {e}"
            )

    async def index_done_callback(self):
        pass
        # self._client.save()