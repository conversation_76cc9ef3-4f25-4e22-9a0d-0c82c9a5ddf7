# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: gql_impl.py
# @Author: sunhao
# @E-mail: <EMAIL>
# @Site:
# @Time: 2024/12/2
# ---
import asyncio
import inspect
from dataclasses import dataclass
from functools import wraps
from typing import Any, Union, Tuple, List, Dict

import aiofiles
import pytest
from nebula3.gclient.net import ConnectionPool
from nebula3.Config import Config

from apps.graphrag.configs import *
from apps.graphrag.manager.prompt import GRAPH_FIELD_SEP
from apps.graphrag.manager.utils import logger
from apps.graphrag.netowrk.net_utils import KGNetConfig
from apps.graphrag.netowrk.query_reader import <PERSON><PERSON><PERSON>er, process_net
from ..base import BaseGraphStorage
from concurrent.futures import ThreadPoolExecutor
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)


@dataclass
class GqlStorage(BaseGraphStorage):
    @staticmethod
    def load_nx_graph(file_name):
        print("no preloading of graph")

    @staticmethod
    def preprocessing(params: list[str], vid_limit=VID_LIMIT, replace_chars: list[str] = None):
        if replace_chars is None:
            replace_chars = ["'", '"']

        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                func_sig = inspect.signature(func)
                param_names = list(func_sig.parameters.keys())
                filtered_args = args[:len(param_names)]
                filtered_kwargs = {k: v for k, v in kwargs.items() if k in param_names}

                bound_args = func_sig.bind(*filtered_args, **filtered_kwargs)
                bound_args.apply_defaults()

                for param_name in params:
                    if param_name in bound_args.arguments:
                        value = bound_args.arguments[param_name]
                        if isinstance(value, str):
                            for char in replace_chars:
                                value = value.replace(char, "")
                            if len(value) > vid_limit:
                                value = value[:vid_limit]
                            bound_args.arguments[param_name] = value

                return await func(*bound_args.args, **bound_args.kwargs)
            return wrapper
        return decorator

    def __init__(self, namespace, global_config):
        super().__init__(namespace=namespace, global_config=global_config)

        # Create connection pool
        self._connection_pool = ConnectionPool()
        config = Config()

        config.min_connection_pool_size = CONNECTION_POOL_SIZE
        config.max_connection_pool_size = CONNECTION_POOL_SIZE * 2
        config.idle_time = IDLE_TIME
        config.interval_check = INTERVAL_CHECK

        try:
            # Initialize connection pool
            self._connection_pool.init([(GRAPH_HOSTS, GRAPH_PORT)], config)

            with self._connection_pool.session_context(GRAPH_USERNAME, GRAPH_PASSWORD) as session:
                pass

            # Create thread pool for async operations
            self._executor = ThreadPoolExecutor(max_workers=5)
        except Exception as e:
            logger.error(f"Connection error: {e}")
            raise

    def __post_init__(self):
        self._node_embed_algorithms = {
            "node2vec": self._node2vec_embed,
        }

    async def close(self):
        if self._executor:
            self._executor.shutdown(wait=True)

        if self._connection_pool:
            self._connection_pool.close()

    def _sync_close(self):
        if self._connection_pool:
            self._connection_pool.close()

    async def __aexit__(self, exc_type, exc, tb):
        await self.close()

    async def index_done_callback(self):
        print("KG successfully indexed.")

    def log_connection_pool_status(self):
        total_connections = self._connection_pool.connects()
        used_connections = self._connection_pool.in_used_connects()
        available_connections = total_connections - used_connections

        logger.info(f"Connection Pool Status: "
                    f"Total Connections: {total_connections}, "
                    f"Used Connections: {used_connections}, "
                    f"Available Connections: {available_connections}")

        print(f"Connection Pool Status: "
                    f"Total Connections: {total_connections}, "
                    f"Used Connections: {used_connections}, "
                    f"Available Connections: {available_connections}")

    def _execute_query_sync(self, query, namespace, args=None):
        """
        Execute a query with optional arguments in a thread-safe manner
        """
        try:
            with self._connection_pool.session_context(
            GRAPH_USERNAME, GRAPH_PASSWORD
            ) as session:
                # Ensure the space is used
                session.execute_py(f"USE {namespace}")

                # Execute the actual query
                if args:
                    result = session.execute_py(query, args)
                else:
                    result = session.execute_py(query)
                return result
        except Exception as e:
            logger.error(f"Query execution error: {e}")
            raise

    async def _execute_query(self, query, namespace=None, args=None, meta_mode=False):
        if not meta_mode and namespace is None:
            raise ValueError("namespace cannot be None.")

        def _sync_execute():
            try:
                with self._connection_pool.session_context(
                        GRAPH_USERNAME, GRAPH_PASSWORD
                ) as session:
                    # self.log_connection_pool_status()
                    if not meta_mode:
                        session.execute_py(f"USE {namespace}")
                    if args:
                        result = session.execute_py(query, args)
                    else:
                        result = session.execute_py(query)

                    return result
            except Exception as e:
                logger.error(f"Query execution error: {e}")
                raise

        return await asyncio.to_thread(_sync_execute)

    @preprocessing(params=["node_id"], vid_limit=VID_LIMIT)
    async def has_node(self, node_id: str) -> bool:
        node_id = node_id.strip('"').replace('"', '').replace("'", "")
        query = f"MATCH (n) WHERE id(n) == '{node_id}' RETURN COUNT(n) > 0"
        result = await self._execute_query(query, self.namespace)
        return result.as_primitive()[0] if result and result.is_succeeded() else False

    @preprocessing(params=["source_node_id", "target_node_id"], vid_limit=VID_LIMIT)
    async def has_edge(self, source_node_id: str, target_node_id: str) -> bool:
        source_node_id = source_node_id.strip('"').replace('"', '').replace("'", "")
        target_node_id = target_node_id.strip('"').replace('"', '').replace("'", "")
        query = f"MATCH (a)-[]->(b) WHERE id(a) == '{source_node_id}' AND id(b) == '{target_node_id}' RETURN COUNT(*) > 0"

        result = await self._execute_query(query, self.namespace)
        if not (result and result.is_succeeded()):
            return False

        primitive_result = result.as_primitive()
        if not primitive_result or not isinstance(primitive_result, list):
            return False

        first_item = primitive_result[0]
        if not isinstance(first_item, dict):
            return False

        first_value = next(iter(first_item.values()), None)
        return bool(first_value)

    @preprocessing(params=["node_id"], vid_limit=VID_LIMIT)
    async def get_node(self, node_id: str) -> Union[dict, None]:
        node_id = node_id.strip('"').replace('"', '').replace("'", "")
        query = f"MATCH (n) WHERE id(n) == '{node_id}' RETURN n"

        result = await self._execute_query(query, self.namespace)
        if result and result.is_succeeded():
            primitive_result = result.as_primitive()
            return GqlStorage.flatten_data(primitive_result[0]) if primitive_result else None
        return None

    @preprocessing(params=["node_id"], vid_limit=VID_LIMIT)
    async def node_degree(self, node_id: str) -> int:
        node_id = node_id.strip('"').replace('"', '').replace("'", "")
        query = f"MATCH (n)-[]->() WHERE id(n) == '{node_id}' RETURN COUNT(*)"

        result = await self._execute_query(query, self.namespace)
        return result.as_primitive()[0].get('COUNT(*)', 0) if result and result.is_succeeded() else 0

    @preprocessing(params=["src_id", "tgt_id"], vid_limit=VID_LIMIT)
    async def edge_degree(self, src_id: str, tgt_id: str) -> int:
        entity_name_label_source = src_id.strip('"').replace('"', '').replace("'", "")
        entity_name_label_target = tgt_id.strip('"').replace('"', '').replace("'", "")
        src_degree = await self.node_degree(entity_name_label_source, self.namespace)
        trg_degree = await self.node_degree(entity_name_label_target, self.namespace)

        # Convert None to 0 for addition
        src_degree = src_degree['COUNT(*)'] if isinstance(src_degree, dict) else 0
        trg_degree = trg_degree['COUNT(*)'] if isinstance(trg_degree, dict) else 0

        degrees = int(src_degree) + int(trg_degree)
        logger.debug(
            f"{inspect.currentframe().f_code.co_name}:query:src_Degree+trg_degree:result:{degrees}"
        )
        return degrees

    @preprocessing(params=["source_node_id", "target_node_id"], vid_limit=VID_LIMIT)
    async def get_edge(self, source_node_id: str, target_node_id: str) -> Union[dict, None]:
        source_node_id = source_node_id.strip('"').replace('"', '').replace("'", "")
        target_node_id = target_node_id.strip('"').replace('"', '').replace("'", "")
        query = f"MATCH (a)-[e]->(b) WHERE id(a) == '{source_node_id}' AND id(b) == '{target_node_id}' RETURN e LIMIT 1"

        result = await self._execute_query(query, self.namespace)
        if result and result.is_succeeded():
            primitive_result = result.as_primitive()
            return GqlStorage.flatten_dict(primitive_result[0]) if primitive_result and len(primitive_result) > 0 else None
        return None

    @preprocessing(params=["source_node_id"], vid_limit=VID_LIMIT)
    async def get_node_edges(self, source_node_id: str) -> List[Tuple[str, str]]:
        source_node_id = source_node_id.strip('"').replace('"', '').replace("'", "")
        query = f"MATCH (a)-[]-(b) WHERE id(a) == '{source_node_id}' RETURN a, b"

        result = await self._execute_query(query, self.namespace)
        if result and result.is_succeeded():
            primitive_result = result.as_primitive()
            edges = []
            for record in primitive_result:
                source_label = record.get('a', {}).get('vid', 'unknown')
                target_label = record.get('b', {}).get('vid', 'unknown')
                edges.append((source_label, target_label))
            return edges
        return []


    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(Exception),
    )
    @preprocessing(params=["node_id"], vid_limit=VID_LIMIT)
    async def upsert_node_(self, node_id: str, node_data: Dict[str, Any]):
        loop = asyncio.get_running_loop()
        node_id = node_id.strip('"').replace('"', '').replace("'", "")
        properties = node_data

        label = "node_type"
        prop_names = ', '.join(properties.keys())
        query = f"""
        INSERT VERTEX `{label}` ({prop_names}) 
        VALUES "{node_id}":({', '.join(f'${k}' for k in properties.keys())})
        """

        await loop.run_in_executor(
            self._executor,
            self._execute_query,
            query,
            self.namespace,
            properties
        )

    @staticmethod
    def clean_up(node_id: str) -> str:
        node_id = node_id.strip("'\"“”")
        node_id = node_id.replace("'", "").replace('"', "").replace("“", "").replace("”", "")
        return node_id

    @staticmethod
    def handle_graph(
            nebula_data: List[Dict],
            node_props: bool = False,
            edge_props: bool = False
    ) -> Dict:
        unique_nodes = {}
        for item in nebula_data:
            src_node = item['n']['src']
            dst_node = item['n']['dst']
            src_type = item.get('src_type', [])
            dst_type = item.get('dst_type', [])
            if src_node not in unique_nodes:
                unique_nodes[src_node] = {
                    'props': item['n']['props'] if node_props else None,
                    'type': src_type
                }
            if dst_node not in unique_nodes:
                unique_nodes[dst_node] = {
                    'props': item['n']['props'] if node_props else None,
                    'type': dst_type
                }

        nodes = []
        node_id_map = {}
        for idx, (node, node_info) in enumerate(unique_nodes.items()):
            node_id = node
            node_id_map[node] = node_id
            node_item = {
                "id": node_id,
                "name": node,
                "type": node_info['type'][0] if node_info['type'] else None  # for nebula
            }
            if node_props and node_info['props'] is not None:
                node_item["props"] = node_info['props']
            nodes.append(node_item)

        edges = []
        for item in nebula_data:
            first_keyword = ''
            if item['n']['props'] and 'keywords' in item['n']['props']:
                keywords = item['n']['props']['keywords'].strip('"').split(',')
                first_keyword = keywords[0].strip().strip('"') if keywords else ''

            edge = {
                "source": node_id_map[item['n']['src']],
                "target": node_id_map[item['n']['dst']],
                "keyword": first_keyword
            }
            if edge_props:
                edge["props"] = item['n']['props']
            edges.append(edge)

        return {
            "nodes": nodes,
            "edges": edges
        }

    @staticmethod
    def handle_expand(
            nebula_data: List[Dict],
            node_props: bool = False,
            edge_props: bool = False
    ) -> Dict:
        nodes_set = set()
        nodes_map = {}
        node_types = {}

        for graph_block in nebula_data:
            for node in graph_block.get('nodes', []):
                vid = node.get('vid')
                if vid not in nodes_set:
                    nodes_set.add(vid)
                    node_type = list(node.get('tags', {}).keys())[0] if node.get('tags') else 'unknown'
                    node_types[vid] = node_type

        nodes_list = [
            {
                'id': name,
                'name': name,
                'type': node_types.get(name, 'unknown')
            }
            for i, name in enumerate(sorted(nodes_set))
        ]

        node_name_to_id = {node['name']: node['id'] for node in nodes_list}

        edges_set = set()
        edges_data = {}

        for graph_block in nebula_data:
            for rel in graph_block.get('relationships', []):
                src = rel.get('src')
                dst = rel.get('dst')

                first_keyword = ''
                if rel.get('props') and 'keywords' in rel['props']:
                    keywords = rel['props']['keywords'].strip('"').split(',')
                    first_keyword = keywords[0].strip().strip('"') if keywords else ''

                if src in node_name_to_id and dst in node_name_to_id:
                    source_id = node_name_to_id[src]
                    target_id = node_name_to_id[dst]
                    edge_key = (source_id, target_id)

                    if edge_key not in edges_set or not edges_data.get(edge_key, {}).get('keyword'):
                        edges_set.add(edge_key)
                        edges_data[edge_key] = {
                            'source': source_id,
                            'target': target_id,
                            'keyword': first_keyword
                        }

        edges_list = list(edges_data.values())

        return {
            'nodes': nodes_list,
            'edges': edges_list
        }

    @staticmethod
    def handle_graph_search(
            nebula_data: List[Dict],
            node_props: bool = False,
            edge_props: bool = False
    ) -> Dict:
        nodes_set = set()
        node_types = {}
        node_descriptions = {}

        for item in nebula_data:
            if 'v' in item and 'tags' in item['v']:
                node_data = item['v']
                vid = node_data.get('vid')

                tags = node_data.get('tags', {})
                node_type = list(tags.keys())[0] if tags else 'unknown'

                description = tags.get(node_type, {}).get('description', '')

                nodes_set.add(vid)
                node_types[vid] = node_type
                node_descriptions[vid] = description

            elif 'nodes' in item:
                for node in item['nodes']:
                    vid = node.get('vid')
                    node_type = list(node.get('tags', {}).keys())[0] if node.get('tags') else 'unknown'

                    nodes_set.add(vid)
                    node_types[vid] = node_type

        nodes_list = [
            {
                'id': name,
                'name': name,
                'type': node_types.get(name, 'unknown'),
                **(
                    {'description': node_descriptions.get(name, '')}
                    if node_props and node_descriptions.get(name)
                    else {}
                )
            }
            for i, name in enumerate(sorted(nodes_set))
        ]

        return {
            'nodes': nodes_list,
            'edges': []
        }

    @staticmethod
    def flatten_data(data: dict) -> dict:
        vid = data.get('n', {}).get('vid')

        tags = data.get('n', {}).get('tags', {})
        if tags:
            first_key = next(iter(tags))  # 获取第一个 key
            tag_data = tags[first_key]
        else:
            tag_data = {}

        result = {'vid': vid, **tag_data}
        return result

    @staticmethod
    def flatten_dict(data: dict) -> dict:
        if 'e' not in data:
            return {}

        result = {}
        e_data = data['e']

        result.update({
            'dst': e_data.get('dst'),
            'rank': e_data.get('rank'),
            'src': e_data.get('src'),
            'type': e_data.get('type'),
        })

        props = e_data.get('props')
        if props:
            result.update(props)

        return result

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(Exception),
    )
    @preprocessing(params=["node_id"], vid_limit=VID_LIMIT)
    async def upsert_node(self, node_id: str, node_data: Dict[str, Any],
                          node_type: str):
        """
        Upsert a node in the Nebula Graph database.
        """
        logger.debug(f"Upserting node with ID: {node_id}, Type: {node_type}, Data: {node_data}")
        properties = node_data
        node_id = node_id.strip('"').replace('"', '').replace("'", "")
        label = node_type.strip('"').replace('"', '').replace("'", "")

        if label not in ENTITY_TYPES:
            label = OTHER_TYPE

        async def _do_upsert():
            query_check = f'MATCH (n:{label}) WHERE id(n) == "{node_id}" RETURN COUNT(n) as count'
            result_check = await self._execute_query(query_check, self.namespace)

            if result_check.is_succeeded():
                count = result_check.as_primitive()[0]['count']

                if count == 0:
                    prop_names = ', '.join(properties.keys())
                    query_insert = f"""
                    INSERT VERTEX `{label}` ({prop_names}) 
                    VALUES "{node_id}":({', '.join(f'${k}' for k in properties.keys())})
                    """
                    await self._execute_query(query_insert, self.namespace, properties)
                    logger.debug(f"Inserted new node with label '{label}' and properties: {properties}")
                else:
                    query_update = f"""
                    UPDATE VERTEX ON {label} '{node_id}' 
                    SET {', '.join(f'{k} = ${k}' for k in properties.keys())}
                    """
                    await self._execute_query(query_update, self.namespace, properties)
                    logger.debug(f"Updated node with label '{label}' and properties: {properties}")

        try:
            await _do_upsert()
        except Exception as e:
            logger.error(f"Error during upsert: {str(e)}")
            raise

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(Exception),
    )
    @preprocessing(params=["source_node_id", "target_node_id"], vid_limit=VID_LIMIT)
    async def upsert_edge(self, source_node_id: str, target_node_id: str, edge_data: Dict[str, Any]):
        source_node_id = source_node_id.strip('"').replace('"', '').replace("'", "")
        target_node_id = target_node_id.strip('"').replace('"', '').replace("'", "")
        edge_properties = edge_data

        async def _do_upsert_edge():
            query_check = f"""
            MATCH (source) WHERE id(source) == "{source_node_id}"
            MATCH (target) WHERE id(target) == "{target_node_id}"
            RETURN COUNT(source) as source_count, COUNT(target) as target_count
            """
            # cartesian product
            result_check = await self._execute_query(query_check, self.namespace)

            if result_check.is_succeeded():
                check_result = result_check.as_primitive()[0]

                if check_result['source_count'] > 0 and check_result['target_count'] > 0:
                    query_edge_check = f"""
                    MATCH (source)-[e:DIRECTED]->(target) 
                    WHERE id(source) == "{source_node_id}" AND id(target) == "{target_node_id}" 
                    RETURN COUNT(e) as edge_count
                    """
                    result_edge_check = await self._execute_query(query_edge_check, self.namespace)

                    if result_edge_check.is_succeeded():
                        edge_count = result_edge_check.as_primitive()[0]['edge_count']

                        if edge_count == 0:
                            query_insert = f"""
                            INSERT EDGE `DIRECTED` ({', '.join(edge_properties.keys())}) 
                            VALUES "{source_node_id}" -> "{target_node_id}":({', '.join(f'${k}' for k in edge_properties.keys())})
                            """
                            await self._execute_query(query_insert, self.namespace, edge_properties)
                        else:
                            query_update = f"""
                            UPDATE EDGE ON DIRECTED "{source_node_id}" -> "{target_node_id}" 
                            SET {', '.join(f'{k} = ${k}' for k in edge_properties.keys())}
                            """
                            await self._execute_query(query_update, self.namespace, edge_properties)

                        logger.debug(
                            f"Upserted edge from '{source_node_id}' to '{target_node_id}' with properties: {edge_properties}"
                        )
                else:
                    logger.error(
                        f"Source or target node does not exist. Source: {source_node_id}, Target: {target_node_id}")
            else:
                logger.error("Failed to check node existence")

        try:
            await _do_upsert_edge()
        except Exception as e:
            logger.error(f"Error during edge upsert: {str(e)}")
            raise

    async def _node2vec_embed(self):
        print("Implemented but never called.")

    def generate_tag_statements(self, name_list: List[str]):
        """
        :param name_list: list of tag names
        :return: list of CREATE TAG statements as strings
        """
        template = "CREATE TAG {name} (entity_type STRING, description STRING, source_id STRING, name STRING);"
        statements = [template.format(name=name) for name in name_list]
        return statements

    def generate_edge_statements(self, name_list: List[str]):
        """
        :param name_list: list of edge names
        :return: list of CREATE EDGE statements as strings
        """
        template = "CREATE EDGE {name} (weight FLOAT, description STRING, keywords STRING, source_id STRING)"
        statements = [template.format(name=name) for name in name_list]
        return statements

    async def buildup(self, tags: List[str], edges: List[str]):
        """
        :param namespace: Nebula Graph space name
        :param tags: list of tag names
        :param edges: list of edge names
        :return: None
        """
        state_drop = f"DROP SPACE IF EXISTS {self.namespace}"
        state_create = (f"CREATE SPACE IF NOT EXISTS {self.namespace} "
                        f"(partition_num={PARTITION_NUM},"
                        f" replica_factor={REPLICA_FACTOR},"
                        f" vid_type={VID_TYPE})")
        await self._execute_query(state_drop, None, meta_mode=True)
        await self._execute_query(state_create, None, meta_mode=True)
        # According to the best practice recommendations in the vendor’s documentation, this method is adopted.
        await asyncio.sleep(HEARTBEAT_INTERVAL_SECS * 2)
        statements = self.generate_tag_statements(tags) + self.generate_edge_statements(edges)
        for statement in statements:
            await self._execute_query(statement, self.namespace)
        return

    async def truncate_all(self):
        """
        :param namespace: Nebula Graph space name
        :return: None
        """
        state_drop = f"DROP SPACE IF EXISTS {self.namespace}"
        await self._execute_query(state_drop, None, meta_mode=True)
        await asyncio.sleep(HEARTBEAT_INTERVAL_SECS * 2)
        return

    def buildup_wrapper(self, tags: List[str], edges: List[str]):
        asyncio.run(self.buildup(tags, edges))
        return

    async def _dump_vertices(self, config_template, export_dir):
        vertices_template = config_template.get('vertices', [])
        for entity_type in ENTITY_TYPES:
            dynamic_config = [
                {
                    'type': vertex_config['type'].format(type=entity_type),
                    'output_file': vertex_config['output_file'].format(type=entity_type),
                    'properties': vertex_config['properties']
                }
                for vertex_config in vertices_template
            ]

            for vertex_config in dynamic_config:
                output_path = os.path.join(export_dir, vertex_config['output_file'])
                tag_name = vertex_config['type']
                properties = vertex_config['properties']

                os.makedirs(os.path.dirname(output_path), exist_ok=True)

                async def write_async():
                    async with aiofiles.open(output_path, 'w', newline='', encoding='utf-8') as file:
                        await file.write('|'.join(properties) + '\n')

                        query = f"MATCH (n:{tag_name}) RETURN n"
                        result = await self._execute_query(query, self.namespace)
                        result = result.as_primitive()

                        for row in result:
                            vid = row['n']['vid']
                            tags = row['n']['tags']

                            tag_props = tags.get(tag_name, {})
                            row_data = [
                                vid,
                                *[
                                    str(tag_props.get(prop, '')).replace('\n', ' ').replace('\r', ' ')
                                    for prop in properties[1:]
                                ]
                            ]
                            await file.write('|'.join(row_data) + '\n')

                await write_async()


    async def _dump_edges(self, config, export_dir):
        config_edges = config.get('edges', [])

        for edge_config in config_edges:
            output_path = os.path.join(export_dir, edge_config['output_file'])
            edge_type = edge_config['type']
            properties = edge_config['properties']

            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            async def write_async():
                async with aiofiles.open(output_path, 'w', newline='', encoding='utf-8') as file:
                    await file.write('|'.join(properties) + '\n')

                    query = f"MATCH (a)-[e:{edge_type}]->(b) RETURN a, e, b"
                    result = await self._execute_query(query, self.namespace)
                    result = result.as_primitive()

                    for row in result:
                        src = row['a']['vid']
                        dst = row['b']['vid']
                        edge_props = row['e']['props']

                        row_data = [
                            src,
                            dst,
                            *[
                                str(edge_props.get(prop, '')).replace('\n', ' ').replace('\r', ' ')
                                for prop in properties[2:]
                            ]
                        ]
                        await file.write('|'.join(row_data) + '\n')

            await write_async()

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(Exception),
    )
    async def get_data(self, limit: int = QUERY_SIZE_LIMIT) -> dict:
        query = f"MATCH (v)-[n]->(t) RETURN labels(v) as src_type, labels(t) as dst_type, n LIMIT {limit}"
        result = await self._execute_query(query, self.namespace)
        if result and result.is_succeeded():
            primitive_result = result.as_primitive()
            if primitive_result and len(primitive_result) > 0:
                return GqlStorage.handle_graph(primitive_result)
        logger.info("Error in graph data...")
        return {}

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(Exception),
    )
    async def get_data_raw(self, limit: int = QUERY_SIZE_LIMIT) -> dict:
        query = f"MATCH (v)-[n]->(t) RETURN labels(v) as src_type, labels(t) as dst_type, n LIMIT {limit}"
        result = await self._execute_query(query, self.namespace)
        if result and result.is_succeeded():
            primitive_result = result.as_primitive()
            if primitive_result and len(primitive_result) > 0:
                edges = primitive_result
        query = f"MATCH (n) RETURN (n) LIMIT {limit}"
        result = await self._execute_query(query, self.namespace)
        if result and result.is_succeeded():
            primitive_result = result.as_primitive()
            if primitive_result and len(primitive_result) > 0:
                nodes = primitive_result
        return {"nodes": nodes,"edges": edges}

    # fixme: limit
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(Exception),
    )
    @preprocessing(params=["node_id"], vid_limit=VID_LIMIT)
    async def get_expand(self, node_id:str, limit: int = QUERY_SIZE_LIMIT) -> dict:
        query = f"GET SUBGRAPH WITH PROP 1 STEPS FROM '{node_id}' YIELD VERTICES AS nodes, EDGES AS relationships"
        result = await self._execute_query(query, self.namespace)
        if result and result.is_succeeded():
            primitive_result = result.as_primitive()
            if primitive_result and len(primitive_result) > 0:
                return GqlStorage.handle_expand(primitive_result)
        logger.info("Error in graph data...")
        return {}


    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(Exception),
    )
    @preprocessing(params=["keywords"], vid_limit=VID_LIMIT)
    async def get_data_search(self, prop_type:str, keywords: str, limit: int = QUERY_SIZE_LIMIT) -> dict:
        query = f"MATCH (v) WHERE id(v) == '{keywords}' RETURN v"
        result = await self._execute_query(query, self.namespace)
        if result and result.is_succeeded():
            primitive_result = result.as_primitive()
            if primitive_result and len(primitive_result) > 0:
                return GqlStorage.handle_graph_search(primitive_result)
        logger.info("Error in graph data...")
        return {}

    def calculate(self):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            query_vertex = f"MATCH (n) RETURN COUNT(n) AS node_count"
            query_edge = f"MATCH ()-[e]->() RETURN COUNT(e) AS edge_count"

            vertex_result = loop.run_until_complete(self._execute_query(query_vertex, self.namespace))
            edge_result = loop.run_until_complete(self._execute_query(query_edge, self.namespace))

            return {
                "nodes": vertex_result.as_primitive()[0]['node_count'],
                "edges": edge_result.as_primitive()[0]['edge_count']
            }
        finally:
            loop.close()


    @preprocessing(params=["start_node", "end_node"], vid_limit=VID_LIMIT)
    async def find_all_paths(
            self, start_node: str, end_node: str
    ) -> List[Dict[str, Any]]:
        """
        Query all paths between two nodes.

        :param start_node: The starting node's ID
        :param end_node: The ending node's ID
        :return: A list of paths, where each path is represented as a dictionary
        """
        start_node = start_node.strip('"').replace('"', '').replace("'", "")
        end_node = end_node.strip('"').replace('"', '').replace("'", "")

        query = (
            f'FIND ALL PATH FROM "{start_node}" TO "{end_node}" '
            f'OVER * BIDIRECT YIELD path AS p;'
        )

        result = await self._execute_query(query, self.namespace)

        if result and result.is_succeeded():
            primitive_result = result.as_primitive()
            paths = []
            for record in primitive_result:
                path = record.get('p', {})
                paths.append(path)
            return paths
        return []


    @preprocessing(params=["start_node", "end_node"], vid_limit=VID_LIMIT)
    async def find_short_paths(
            self, start_node: str, end_node: str
    ) -> List[Dict[str, Any]]:
        """
        Query all paths between two nodes.

        :param start_node: The starting node's ID
        :param end_node: The ending node's ID
        :return: A list of paths, where each path is represented as a dictionary
        """
        start_node = start_node.strip('"').replace('"', '').replace("'", "")
        end_node = end_node.strip('"').replace('"', '').replace("'", "")

        query = (
            f"FIND SHORTEST PATH WITH PROP FROM '{start_node}' TO '{end_node}' OVER * BIDIRECT UPTO 10 STEPS YIELD path AS p"
        )

        result = await self._execute_query(query, self.namespace)

        if result and result.is_succeeded():
            primitive_result = result.as_primitive()
            paths = []
            for record in primitive_result:
                path = record.get('p', {})
                paths.append(path)
            return paths
        return []

    @preprocessing(params=["start_node", "end_node"], vid_limit=VID_LIMIT)
    async def find_short_paths_desc(
            self, start_node: str, end_node: str
    ) -> str:
        path_data = await self.find_short_paths(start_node, end_node)
        if not path_data:
            return ""
        return GqlStorage.convert_graph_path(path_data[0])


    @staticmethod
    def convert_graph_path(path_data: Dict) -> str:
        result_parts = []
        result_parts.append("path description：")
        for edge in path_data['edges']:
            keywords = edge['props']['keywords'].strip('"')  # Remove surrounding quotes
            relationship = f"({edge['src']})-[{keywords}]->({edge['dst']})"
            result_parts.append(relationship)

        result_parts.append("\nnode description：")
        for node in path_data['nodes']:
            # Iterate through all tag types (person, category, others, etc.)
            for tag_type, tag_data in node['tags'].items():
                if 'description' in tag_data:
                    description = tag_data['description'].strip('"')  # Remove surrounding quotes
                    if GRAPH_FIELD_SEP in description:
                        description = description.replace(GRAPH_FIELD_SEP, ' ')
                    result_parts.append(f"node \"{node['vid']}\"：{description}")

        result_parts.append("\nedge description：")
        for edge in path_data['edges']:
            src = edge['src']
            dst = edge['dst']
            description = edge['props']['description'].strip('"')  # Remove surrounding quotes
            result_parts.append(f"{src} to {dst} relation：{description}")

        # Join all parts with newlines
        return '\n'.join(result_parts)


    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(Exception),
    )
    def process_net(self, limit_all: int = 10000, limit: int = 10) -> dict:
        kg_reader = KGReader(
            edges=[EDGE_TYPE],
            properties=[["weight", "keywords"]],
            nebula_config=KGNetConfig(
                graphd_hosts=f"{GRAPH_HOSTS}:{GRAPH_PORT}",
                metad_hosts="",
                space=self.namespace,
                user=GRAPH_USERNAME,
                password=GRAPH_PASSWORD
            ),
            limit=limit_all,
            with_rank=False
        )
        result = process_net(kg_reader.read())
        kg_reader.release()
        return result


@pytest.mark.asyncio
async def test_nebulagraph_storage():
    """
    Comprehensive test for NebulaGraphStorage methods using ConnectionPool
    """
    # NebulaGraph connection parameters
    SERVER = os.environ.get("GRAPH_HOSTS", "127.0.0.1")
    PORT = int(os.environ.get("GRAPH_PORT", 9669))
    USER = GRAPH_USERNAME
    PASSWORD = GRAPH_PASSWORD
    SPACE = os.environ.get("GRAPH_SPACE", "test_space")

    # Initialize connection pool
    connection_pool = ConnectionPool()
    config = Config()

    try:
        # Initialize the connection pool
        if not connection_pool.init([(SERVER, PORT)], config):
            raise RuntimeError("Failed to initialize connection pool")

        # Perform setup operations in a session context
        with connection_pool.session_context(USER, PASSWORD) as session:
            # Create space if not exists
            session.execute_py(f"""
                CREATE SPACE IF NOT EXISTS {SPACE} (
                    vid_type=FIXED_STRING(30)
                );
                USE {SPACE};
                CREATE TAG IF NOT EXISTS test_node(
                    name string, 
                    type string, 
                    value int
                );
                CREATE EDGE IF NOT EXISTS test_edge(
                    relation string, 
                    weight double
                );
            """)

        # Perform tests within a session context
        with connection_pool.session_context(USER, PASSWORD) as session:
            session.execute_py(f"USE {SPACE};")

            # Test node insertion
            test_node_id = "test_node_01"
            test_node_data = {
                "name": "Test Node",
                "type": "test",
                "value": 42
            }

            # Insert node
            logger.info("Testing node insertion...")
            insert_node_query = f"""
            INSERT VERTEX test_node(name, type, value) 
            VALUES "{test_node_id}":($name, $type, $value)
            """
            session.execute_py(insert_node_query, test_node_data)

            # Check node existence
            logger.info("Testing node existence...")
            check_node_query = f"""
            MATCH (v) WHERE id(v) == "{test_node_id}" RETURN COUNT(v) > 0
            """
            result = session.execute_py(check_node_query)
            node_exists = result.as_primitive()[0]
            assert node_exists, "Node should exist after insertion"
            logger.info("Node exists check passed ✓")

            # Retrieve node
            logger.info("Testing node retrieval...")
            get_node_query = f"""
            MATCH (v) WHERE id(v) == "{test_node_id}" RETURN v
            """
            result = session.execute_py(get_node_query)
            retrieved_node = result.as_primitive()[0] if result.as_primitive() else None
            assert retrieved_node is not None, "Retrieved node should not be None"
            logger.info(f"Retrieved node: {retrieved_node}")

            # Test edge insertion
            test_source_node = "source_node_01"
            test_target_node = "target_node_01"
            test_edge_data = {
                "relation": "test_relation",
                "weight": 1.0
            }

            # Insert edge
            logger.info("Testing edge insertion...")
            insert_edge_query = f"""
            INSERT EDGE test_edge(relation, weight) 
            VALUES "{test_source_node}" -> "{test_target_node}":($relation, $weight)
            """
            session.execute_py(insert_edge_query, test_edge_data)

            # Check edge existence
            logger.info("Testing edge existence...")
            check_edge_query = f"""
            MATCH (src)-[e:test_edge]->(dst) 
            WHERE id(src) == "{test_source_node}" AND id(dst) == "{test_target_node}" 
            RETURN COUNT(e) > 0
            """
            result = session.execute_py(check_edge_query)
            edge_exists = result.as_primitive()[0]
            assert edge_exists, "Edge should exist after insertion"
            logger.info("Edge exists check passed ✓")

        logger.info("All tests completed successfully! ✨")

    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise
    finally:
        # Always close the connection pool
        connection_pool.close()

def main():
    """
    Main function to run the async test
    """
    try:
        asyncio.run(test_nebulagraph_storage())
        # asyncio.run(test_nebulagraph_storage())
    except Exception as e:
        logger.error(f"Test run failed: {e}")

if __name__ == "__main__":
    main()