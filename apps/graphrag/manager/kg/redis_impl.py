# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: redis_impl
# @Author: <PERSON><PERSON>
# @E-mail: sun<PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2024/11/29
# ---
import asyncio
import json
from typing import Union, Any

from celery.contrib import pytest

from apps.graphrag.manager.base import BaseKVStorage
from puppet.cache import redis_pool, CacheAction


class RedisKVStorage(BaseKVStorage):
    def __init__(self, redis_pool=None, namespace=None, expire_time=3600*24):
        cache = CacheAction()
        self.redis_pool = cache.llm_pool
        self.namespace = namespace
        self.expire_time = expire_time

    async def get_by_id(self, key: str) -> Union[dict, None]:
        """根据 id 获取数据"""
        result = redis_pool.get_comment_data(self._get_full_key(key))
        return json.loads(result) if result else None

    async def get_by_ids(self, keys: list[str], fields=None) -> Union[list[dict], None]:
        """批量获取数据"""
        results = [await self.get_by_id(key) for key in keys]
        return [r for r in results if r is not None]

    async def upsert(self, data: dict[str, dict]):
        """更新或插入数据"""
        for key, value in data.items():
            full_key = self._get_full_key(key)
            redis_pool.set_comment_data(full_key, json.dumps(value), expire_time=self.expire_time)
        return data

    async def delete(self, key: str):
        """删除数据"""
        full_key = self._get_full_key(key)
        redis_pool.del_set_data(full_key)

    async def filter_keys(self, keys: list[str]) -> set[str]:
        """过滤已存在的 keys"""
        existing_keys = set()
        for key in keys:
            if await self.get_by_id(key) is not None:
                existing_keys.add(key)
        return set(keys) - existing_keys

    def _get_full_key(self, key: str) -> str:
        """生成带命名空间的完整 key"""
        return f"{self.namespace}:{key}" if self.namespace else key

    # 可选：支持哈希表操作
    async def hset(self, name: str, key: str, value: Any):
        """在哈希表中设置值"""
        full_name = self._get_full_key(name)
        redis_pool.hset_comment_data(full_name, key, value)

    async def hget(self, name: str, key: str):
        """从哈希表中获取值"""
        full_name = self._get_full_key(name)
        return redis_pool.hget_comment_data(full_name, key)


class TestRedisKVStorage:

    async def kv_storage(self):
        # 使用测试专用的命名空间，避免干扰生产数据
        return RedisKVStorage(namespace="test_namespace")

    def test_upsert_and_get_by_id(self):
        kv_storage = RedisKVStorage(namespace="test_namespace")

        # 测试单个数据的插入和获取
        test_data = {"user_1": {"name": "张三", "age": 30}}

        # 插入数据
        kv_storage.upsert(test_data)

        # 获取数据
        retrieved_data = kv_storage.get_by_id("user_1")

        assert retrieved_data == test_data["user_1"], "数据插入和获取失败"

    async def test_get_by_ids(self, kv_storage):
        # 测试批量插入和获取
        test_data = {
            "user_1": {"name": "张三", "age": 30},
            "user_2": {"name": "李四", "age": 25}
        }

        # 插入数据
        await kv_storage.upsert(test_data)

        # 批量获取
        retrieved_data = await kv_storage.get_by_ids(["user_1", "user_2"])

        assert len(retrieved_data) == 2, "批量获取数据失败"
        assert {"name": "张三", "age": 30} in retrieved_data
        assert {"name": "李四", "age": 25} in retrieved_data

    async def test_delete(self, kv_storage):
        # 测试删除功能
        test_data = {"user_1": {"name": "张三", "age": 30}}

        # 插入数据
        await kv_storage.upsert(test_data)

        # 删除数据
        await kv_storage.delete("user_1")

        # 验证删除
        retrieved_data = await kv_storage.get_by_id("user_1")
        assert retrieved_data is None, "删除数据失败"

    async def test_filter_keys(self, kv_storage):
        # 测试过滤已存在的键
        test_data = {
            "user_1": {"name": "张三", "age": 30},
            "user_2": {"name": "李四", "age": 25}
        }

        # 插入数据
        await kv_storage.upsert(test_data)

        # 过滤键
        filtered_keys = await kv_storage.filter_keys(["user_1", "user_2", "user_3"])

        assert filtered_keys == {"user_3"}, "键过滤失败"

    async def test_hset_and_hget(self, kv_storage):
        # 测试哈希表操作
        # 设置哈希表值
        await kv_storage.hset("user_profile", "interests", ["reading", "coding"])
        await kv_storage.hset("user_profile", "skills", ["Python", "Redis"])

        # 获取哈希表值
        interests = await kv_storage.hget("user_profile", "interests")
        skills = await kv_storage.hget("user_profile", "skills")

        assert interests == ["reading", "coding"], "哈希表设置或获取失败"
        assert skills == ["Python", "Redis"], "哈希表设置或获取失败"

    async def test_namespace_isolation(self):
        # 测试命名空间隔离
        storage1 = RedisKVStorage(namespace="namespace1")
        storage2 = RedisKVStorage(namespace="namespace2")

        test_data1 = {"key": {"value": "namespace1"}}
        test_data2 = {"key": {"value": "namespace2"}}

        await storage1.upsert(test_data1)
        await storage2.upsert(test_data2)

        # 验证数据在不同命名空间下相互隔离
        retrieved1 = await storage1.get_by_id("key")
        retrieved2 = await storage2.get_by_id("key")

        assert retrieved1 == {"value": "namespace1"}
        assert retrieved2 == {"value": "namespace2"}

    async def test_expiration(self, expire_time):
        # 测试过期功能
        kv_storage = RedisKVStorage(namespace="test_expire", expire_time=expire_time)

        test_data = {"user_1": {"name": "临时用户"}}
        await kv_storage.upsert(test_data)

        # 等待过期
        await asyncio.sleep(expire_time + 1)

        # 验证数据是否过期
        retrieved_data = await kv_storage.get_by_id("user_1")
        assert retrieved_data is None, f"数据在 {expire_time} 秒后未过期"


# 运行测试
if __name__ == "__main__":
    pytest.main([__file__])