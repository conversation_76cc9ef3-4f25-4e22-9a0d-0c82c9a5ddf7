# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: __init__.py
# @Author: sunhao
# @E-mail: sun<PERSON>@zhongshuruizhi.com
# @Site:
# @Time: 2024/11/26
# ---

GRAPH_ENTITIES_MAPPING = {
    "properties": {
        "__id__": {
            "type": "keyword"
        },
        "entity_name": {
            "type": "keyword"
        },
        "knowledge_id": {
            "type": "keyword"
        },
        "doc_id": {
            "type": "keyword"
        },
        "chunk_id": {
            "type": "keyword"
        },
        "delete_flag": {
            "type": "boolean"
        },
        "hit_count": {
            "type": "integer"
        },
        "vector": {
            "type": "dense_vector",
            "dims": 1024,
            "index": True,
            "similarity": "cosine"
        }
    }
}

GRAPH_RELATIONSHIPS_MAPPING = {
    "properties": {
        "__id__": {
            "type": "keyword"
        },
        "src_id": {
            "type": "keyword"
        },
        "tgt_id": {
            "type": "keyword"
        },

        "knowledge_id": {
            "type": "keyword"
        },
        "doc_id": {
            "type": "keyword"
        },
        "chunk_id": {
            "type": "keyword"
        },
        "delete_flag": {
            "type": "boolean"
        },
        "hit_count": {
            "type": "integer"
        },
        "vector": {
            "type": "dense_vector",
            "dims": 1024,
            "index": True,
            "similarity": "cosine"
        }
    }

}
