# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: mysql_impl.py
# @Author: sun<PERSON>
# @E-mail: <EMAIL>
# @Site:
# @Time: 2024/11/28
# ---
import asyncio

# import html
# import os
from dataclasses import dataclass
from typing import Union, Dict
import numpy as np
import array

from modeler.mysql.knowledge_orm import KnowledgeOrm
from puppet.es_sdk import EsSdkPool
from apps.graphrag.configs import TEMP_CORP_ID, VDB_CHUNKS, DEFAULT_LOCAL_EMB_MODEL_PATH_GRAPH
from ..utils import logger
from ..base import (
    BaseKVStorage,
    BaseRDBStorage,
)

import oracledb


class MysqlDB:
    def __init__(self, config, **kwargs):
        self.model = KnowledgeOrm(TEMP_CORP_ID)
        self.client = EsSdkPool(TEMP_CORP_ID)
        # self.client = None
        self.dsn = config.get("dsn", None)
        self.config_dir = config.get("config_dir", None)
        self.workspace = config.get("workspace", None)
        self.max = 12
        self.increment = 1

    def numpy_converter_in(self, value):
        """Convert numpy array to array.array"""
        if value.dtype == np.float64:
            dtype = "d"
        elif value.dtype == np.float32:
            dtype = "f"
        else:
            dtype = "b"
        return array.array(dtype, value)

    def input_type_handler(self, cursor, value, arraysize):
        """Set the type handler for the input data"""
        if isinstance(value, np.ndarray):
            return cursor.var(
                oracledb.DB_TYPE_VECTOR,
                arraysize=arraysize,
                inconverter=self.numpy_converter_in,
            )

    def numpy_converter_out(self, value):
        """Convert array.array to numpy array"""
        if value.typecode == "b":
            dtype = np.int8
        elif value.typecode == "f":
            dtype = np.float32
        else:
            dtype = np.float64
        return np.array(value, copy=False, dtype=dtype)

    def output_type_handler(self, cursor, metadata):
        """Set the type handler for the output data"""
        if metadata.type_code is oracledb.DB_TYPE_VECTOR:
            return cursor.var(
                metadata.type_code,
                arraysize=cursor.arraysize,
                outconverter=self.numpy_converter_out,
            )

    async def check_tables(self):
        pass
    
    
    async def query(
        self, sql: str, params: dict = None, multirows: bool = False
    ) -> Union[dict, None]:
        async with self.pool.acquire() as connection:
            connection.inputtypehandler = self.input_type_handler
            connection.outputtypehandler = self.output_type_handler
            with connection.cursor() as cursor:
                try:
                    await cursor.execute(sql, params)
                except Exception as e:
                    logger.error(f"Mysql database error: {e}")
                    print(sql)
                    print(params)
                    raise
                columns = [column[0].lower() for column in cursor.description]
                if multirows:
                    rows = await cursor.fetchall()
                    if rows:
                        data = [dict(zip(columns, row)) for row in rows]
                    else:
                        data = []
                else:
                    row = await cursor.fetchone()
                    if row:
                        data = dict(zip(columns, row))
                    else:
                        data = None
                return data

    async def execute(self, sql: str, data: list | dict = None):
        # logger.info("go into MysqlDB execute method")
        try:
            async with self.pool.acquire() as connection:
                connection.inputtypehandler = self.input_type_handler
                connection.outputtypehandler = self.output_type_handler
                with connection.cursor() as cursor:
                    if data is None:
                        await cursor.execute(sql)
                    else:
                        # print(data)
                        # print(sql)
                        await cursor.execute(sql, data)
                    await connection.commit()
        except Exception as e:
            logger.error(f"Mysql database error: {e}")
            print(sql)
            print(data)
            raise


@dataclass
class MysqlRDBStorage(BaseRDBStorage):
    # should pass db object to self.db
    def __post_init__(self):
        self.db = MysqlDB(self.global_config)
        self._data = {}
        self._max_batch_size = self.global_config["embedding_batch_num"]

    ################ RDB METHODS ################
    async def fetch(self, query: str, top_k: int, **kwargs) -> list[dict]:
        pass

    async def record(self, work_id: int, record: Dict, **kwargs):
        # notice: add a few requests for the sake of not contaminating the original params
        print(f"Recording to db for id: {work_id}, content: {record}")
        records = self.db.model.get_knowledge_graph_detail(work_id)
        if not records:
            return
        graph_id = records[0].get("id")
        if not graph_id:
            return
        self.db.model.update_knowledge_graph(graph_id, record)


@dataclass
class MysqlKVStorage(BaseKVStorage):

    # should pass db object to self.db
    def __post_init__(self):
        self.db = MysqlDB(self.global_config)
        self._data = {}
        self._max_batch_size = self.global_config["embedding_batch_num"]


    ################ QUERY METHODS ################

    async def get_by_id(self, id: str) -> Union[dict, None]:
        res = await self.get_by_ids([id])
        if res:
            data = res  # {"data":res}
            # print (data)
            return data
        else:
            return None

    # Query by id
    # Fixme: async
    async def get_by_ids(self, ids: list[str], fields=None) -> Union[list[dict], None]:
        try:
            if self.namespace == "text_chunks":
                index_name = '%s_%s' % (VDB_CHUNKS, DEFAULT_LOCAL_EMB_MODEL_PATH_GRAPH)
                res = await self.search_chunks(index_name, ids)
                if res:
                    data = {
                        "content": res[0]['_source']['description']
                    }
                    return data
                else:
                    return None
            else:
                return None
        except Exception as e:
            logger.error(f"Error in get_by_ids: {e}")
            return None

    async def search_chunks(self, index_name, ids, size=5):
        body_dict = {
            "_source": {
                "includes": ["chunk_id", "doc_id", "knowledge_id", "description", "tokens"]
            },
            "query": {
                "bool": {
                    "must": [
                        {
                            "terms": {
                                "chunk_id": ids
                            }
                        },
                        {
                            "term": {
                                "delete_flag": False
                            }
                        }
                    ]
                }
            },
            "size": size  # 可选：限制返回的结果数量
        }

        try:
            res = self.db.client.search_data(index_name, body_dict)
            hits = res['hits']['hits']
            if len(hits) == 0:
                return []
            else:
                return hits
        except Exception as e:
            return []

    # native
    async def filter_keys(self, keys: list[str]) -> set[str]:
        """过滤掉重复内容"""
        SQL = SQL_TEMPLATES["filter_keys"].format(
            table_name=N_T[self.namespace], ids=",".join([f"'{id}'" for id in keys])
        )
        params = {"workspace": self.db.workspace}
        try:
            await self.db.query(SQL, params)
        except Exception as e:
            logger.error(f"Mysql database error: {e}")
            print(SQL)
            print(params)
        res = await self.db.query(SQL, params, multirows=True)
        data = None
        if res:
            exist_keys = [key["id"] for key in res]
            data = set([s for s in keys if s not in exist_keys])
        else:
            exist_keys = []
            data = set([s for s in keys if s not in exist_keys])
        return data

    ################ INSERT METHODS ################
    async def upsert(self, data: dict[str, dict]):
        left_data = {k: v for k, v in data.items() if k not in self._data}
        self._data.update(left_data)
        # print(self._data)
        # values = []
        if self.namespace == "text_chunks":
            list_data = [
                {
                    "__id__": k,
                    **{k1: v1 for k1, v1 in v.items()},
                }
                for k, v in data.items()
            ]
            contents = [v["content"] for v in data.values()]
            batches = [
                contents[i : i + self._max_batch_size]
                for i in range(0, len(contents), self._max_batch_size)
            ]
            embeddings_list = await asyncio.gather(
                *[self.embedding_func(batch) for batch in batches]
            )
            embeddings = np.concatenate(embeddings_list)
            for i, d in enumerate(list_data):
                d["__vector__"] = embeddings[i]
            # print(list_data)
            for item in list_data:
                merge_sql = SQL_TEMPLATES["merge_chunk"]
                data = {
                    "check_id": item["__id__"],
                    "id": item["__id__"],
                    "content": item["content"],
                    "workspace": self.db.workspace,
                    "tokens": item["tokens"],
                    "chunk_order_index": item["chunk_order_index"],
                    "full_doc_id": item["full_doc_id"],
                    "content_vector": item["__vector__"],
                }
                # print(merge_sql)
                await self.db.execute(merge_sql, data)

        if self.namespace == "full_docs":
            for k, v in self._data.items():
                # values.clear()
                merge_sql = SQL_TEMPLATES["merge_doc_full"]
                data = {
                    "check_id": k,
                    "id": k,
                    "content": v["content"],
                    "workspace": self.db.workspace,
                }
                # print(merge_sql)
                await self.db.execute(merge_sql, data)
        return left_data

    async def index_done_callback(self):
        if self.namespace in ["full_docs", "text_chunks"]:
            logger.info("full doc and chunk data had been saved into oracle db!")

    async def all_keys(self) -> list[str]:
        pass

    async def drop(self):
        pass


SQL_TEMPLATES = {
    # SQL for KVStorage
    "get_by_id_full_docs": "select ID,NVL(content,'') as content from LIGHTRAG_DOC_FULL where workspace=:workspace and ID=:id",
    "get_by_id_text_chunks": "select ID,TOKENS,NVL(content,'') as content,CHUNK_ORDER_INDEX,FULL_DOC_ID from LIGHTRAG_DOC_CHUNKS where workspace=:workspace and ID=:id",
    "get_by_ids_full_docs": "select ID,NVL(content,'') as content from LIGHTRAG_DOC_FULL where workspace=:workspace and ID in ({ids})",
    "get_by_ids_text_chunks": "select ID,TOKENS,NVL(content,'') as content,CHUNK_ORDER_INDEX,FULL_DOC_ID  from LIGHTRAG_DOC_CHUNKS where workspace=:workspace and ID in ({ids})",
    "filter_keys": "select id from {table_name} where workspace=:workspace and id in ({ids})",
    "merge_doc_full": """ MERGE INTO LIGHTRAG_DOC_FULL a
                    USING DUAL
                    ON (a.id = :check_id)
                    WHEN NOT MATCHED THEN
                    INSERT(id,content,workspace) values(:id,:content,:workspace)
                    """,
    "merge_chunk": """MERGE INTO LIGHTRAG_DOC_CHUNKS a
                    USING DUAL
                    ON (a.id = :check_id)
                    WHEN NOT MATCHED THEN
                    INSERT(id,content,workspace,tokens,chunk_order_index,full_doc_id,content_vector)
                    values (:id,:content,:workspace,:tokens,:chunk_order_index,:full_doc_id,:content_vector) """,
}
