# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: graph_rag_exception
# @Author: <PERSON><PERSON>
# @E-mail: <EMAIL>
# @Site: 
# @Time: 2024/12/19
# ---

class GraphRagException(Exception):
    """Custom exception class for GraphRag-related errors."""

    def __init__(self, message: str, error_code: int = 500, details: dict = None):
        """
        Initialize a GraphRagException instance.

        :param message: A brief description of the error.
        :param error_code: An error code (default is 500).
        :param details: Additional details about the error (as a dictionary, default is None).
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}

    def __str__(self):
        """Return a string representation of the exception."""
        return f"[{self.error_code}] {self.message} - Details: {self.details}"

    def to_dict(self):
        """
        Convert the exception into a dictionary format.

        :return: A dictionary containing the error details.
        """
        return {
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details,
        }