# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: exporter
# @Author: sun<PERSON>
# @E-mail: <EMAIL>
# @Site: 
# @Time: 2024/12/5
# ---

from nebula3.Config import Config
from nebula3.gclient.net import ConnectionPool
from nebula3.gclient.net import AuthR<PERSON>ult
from nebula3.common import ttypes
import csv

# 配置连接参数
NEBULA_HOST = '127.0.0.1'
NEBULA_PORT = 9669
USER = 'root'
PASSWORD = 'nebula'
SPACE_NAME = 'space03'

def execute_query(session, query):
    try:
        result = session.execute(query)
        if result.error_code != ttypes.ErrorCode.SUCCEEDED:
            print(f"Error executing query: {result.error_msg}")
            return None
        return result
    except Exception as e:
        print(f"Query failed: {e}")
        return None

def export_vertices(session, output_file):
    query = f"USE {SPACE_NAME}; SHOW TAGS;"
    result = execute_query(session, query)
    if not result:
        return

    tags = [row[0] for row in result.rows]
    with open(output_file, 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['VertexID', 'Tag', 'Properties'])  # 表头

        for tag in tags:
            query = f"FETCH PROP ON {tag} * YIELD vertex AS v;"
            result = execute_query(session, query)
            if not result:
                continue
            for row in result.rows:
                vertex_id = row['v'].get_id()
                props = row['v'].get_props()
                writer.writerow([vertex_id, tag, props])

def export_edges(session, output_file):
    query = f"USE {SPACE_NAME}; SHOW EDGES;"
    result = execute_query(session, query)
    if not result:
        return

    edges = [row[0] for row in result.rows]
    with open(output_file, 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['Source', 'Destination', 'EdgeType', 'Rank', 'Properties'])  # 表头

        for edge in edges:
            query = f"FETCH PROP ON {edge} * YIELD src_id(vertex) AS src, dst_id(vertex) AS dst, rank(edge) AS rank, properties(edge) AS props;"
            result = execute_query(session, query)
            if not result:
                continue
            for row in result.rows:
                src = row['src']
                dst = row['dst']
                rank = row['rank']
                props = row['props']
                writer.writerow([src, dst, edge, rank, props])

def main():
    config = Config()
    config.max_connection_pool_size = 10
    connection_pool = ConnectionPool()
    assert connection_pool.init([(NEBULA_HOST, NEBULA_PORT)], config)

    session = connection_pool.get_session(USER, PASSWORD)
    if not session:
        print("Failed to create session.")
        return

    export_vertices(session, 'vertices.csv')
    export_edges(session, 'edges.csv')

    # 关闭 Session
    session.release()
    connection_pool.close()

if __name__ == "__main__":
    main()