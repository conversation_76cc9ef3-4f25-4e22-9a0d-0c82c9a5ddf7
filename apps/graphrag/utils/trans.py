# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: trans
# @Author: sun<PERSON>
# @E-mail: <EMAIL>
# @Site: 
# @Time: 2025/1/10
# ---

import json

with open('response.txt', 'r', encoding='utf-8') as file:
    lines = file.readlines()

contents = []

for line in lines:
    try:
        data = json.loads(line.strip().split('data: ')[1])
        contents.append(data['content'])
    except (IndexError, KeyError, json.JSONDecodeError):
        continue

result_text = ''.join(contents)

with open('result.txt', 'w', encoding='utf-8') as result_file:
    result_file.write(result_text)

print("finished")