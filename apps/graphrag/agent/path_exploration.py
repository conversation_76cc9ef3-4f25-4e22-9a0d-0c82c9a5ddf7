# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: path_exploration
# @Author: sun<PERSON>
# @E-mail: <EMAIL>
# @Site: 
# @Time: 2024/12/15
# ---
import asyncio
from typing import List, Dict, Any

from apps.graphrag.agent.app_state import AppState
from apps.graphrag.agent.base_handler import <PERSON><PERSON>andler
from itertools import combinations

from apps.graphrag.manager.kg.gql_impl import GqlStorage
from apps.graphrag.manager.prompt import HIGH_RANK_ANALYSIS_PROMPT, PATH_ANALYSIS_PROMPT


class PathExploration(BaseHandler):
    """
    Path Exploration Agent: Focuses on exploring and analyzing path relationships in the graph
    """

    def is_agent_enabled(self, query: str) -> bool:
        """
        Determine if it's a path-related query

        :param query: Input question or query string
        :return: Whether to enable Path Exploration Agent
        """
        keywords = ['789']
        print(keywords)
        return any(keyword in query.lower() for keyword in keywords)

    def extract_keywords(self, query: str) -> List[str]:
        """
        Extract path-related keywords

        :param query: Input question or query string
        :return: List of keywords
        """
        return [word for word in query.split() if len(word) > 1]

    def retrieve_graph_info(self, query: str, keywords: List[str]) -> Dict[str, Any]:
        """
        Retrieve path-related graph information

        :param query: Input question or query string
        :param keywords: Keywords extracted from the query
        :return: Graph information dictionary
        """
        # TODO: Implement actual graph query logic
        return {
            'paths': [],
            'length': 0,
            'start_nodes': [],
            'end_nodes': []
        }

    def post_action(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Retrieve text chunks based on graph path information

        :param params: Graph information dictionary
        :return: List of text chunks
        """
        new_params = params.copy()
        entities = params.get("entities", [])
        if len(entities) < 2:
            return new_params

        entity_combinations = combinations(entities, 2)
        records = []
        # TODO: only 2 content
        for src, dst in entity_combinations:
            path = self._get_path(src, dst)
            vid_list = [item['vid'] for item in path['nodes']]
            path_content = GqlStorage.convert_graph_path(path)

            new_params = params.copy()
            new_params["graph_data"] = PATH_ANALYSIS_PROMPT.format(
                path_content=path_content
            )
            new_params["ll_keywords"] = vid_list

            records.append(path_content)

        return new_params

    @staticmethod
    def _get_path(src: str, dst: str) -> Dict[str, Any]:
        """Get path between two entities in the knowledge graph."""
        state = AppState.get_instance()
        # content = asyncio.run(state.kg_inst.find_short_paths_desc(src, dst))
        content = asyncio.run(state.kg_inst.find_short_paths(src, dst))
        return content[0]

