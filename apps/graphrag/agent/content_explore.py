# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: community_detection
# @Author: sunhao
# @E-mail: <EMAIL>
# @Site: 
# @Time: 2024/12/15
# ---
from typing import List, Dict, Any
from apps.graphrag.agent.base_handler import BaseHandler


class ContentExplore(BaseHandler):
    """
    Community Detection Agent: Focuses on identifying and analyzing communities and clusters in the graph
    """

    def is_agent_enabled(self, query: str) -> bool:
        """
        Determine if it's a community-related query

        :param query: Input question or query string
        :return: Whether to enable Community Detection Agent
        """
        keywords = ['123']
        print(keywords)
        return any(keyword in query.lower() for keyword in keywords)

    def extract_keywords(self, query: str) -> List[str]:
        """
        Extract community-related keywords

        :param query: Input question or query string
        :return: List of keywords
        """
        return [word for word in query.split() if len(word) > 1]

    def retrieve_graph_info(self, query: str, keywords: List[str]) -> Dict[str, Any]:
        """
        Retrieve community graph information

        :param query: Input question or query string
        :param keywords: Keywords extracted from the query
        :return: Graph information dictionary
        """
        # TODO: Implement actual community detection query logic
        return {
            'communities': [],
            'community_sizes': {},
            'inter_community_connections': []
        }

    def post_action(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Retrieve text chunks based on community information

        :param params: Graph information dictionary
        :return: List of text chunks
        """
        # TODO: Implement logic to get text from communities
        new_params = params.copy()
        return new_params