# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: navigator
# @Author: sun<PERSON>
# @E-mail: <EMAIL>
# @Site:
# @Time: 2025/1/3
# ---
import ast
import asyncio
import json
import re
from typing import List, Dict, Any, Optional, TypedDict, cast
from dataclasses import dataclass, field, asdict
from enum import Enum

from langchain_core.language_models import BaseChatModel
from pydantic import BaseModel, Field

from langchain_core.messages import SystemMessage, HumanMessage, AIMessage, BaseMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import START, StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from apps.graphrag.agent.base_handler import BaseHandler, QuestionType
from apps.graphrag.agent.community_detection import CommunityDetection
from apps.graphrag.agent.content_explore import ContentExplore
from apps.graphrag.agent.highrank_analysis import HighRankAnalysis
from apps.graphrag.agent.path_exploration import PathExploration
from apps.graphrag.manager.prompt import QUESTION_REFINEMENT_PROMPT, ENTITY_EXTRACTION_PROMPT, QUESTION_TYPE_PROMPT, \
    ENTITY_CONTEXT_PROMPT, VALIDATION_PROMPT, CONFIDENCE_CHECK_PROMPT, PROMPTS
from apps.graphrag.configs import EXECUTION_TYPE, DEFAULT_EXAMPLE_NUM, DEFAULT_LANG
from apps.graphrag.manager.llm import aigc_app_complete_if_cache

# load_dotenv()
mode = EXECUTION_TYPE
enable_refine = 0
LITE_LLM_MODE = 1  # 1 for lite mode


class ExecutionType(Enum):
    DYNAMIC = "DYNAMIC"
    LOCAL = "LOCAL"
    OPENAI = "OPENAI"


@dataclass
class AnalysisResult:
    entities: List[str]
    question_type: QuestionType
    is_valid: bool = False
    confidence: float = 0.0


class EntityContext(BaseModel):
    entity: str
    context: str = Field(default="")
    importance_score: float = Field(default=0.0)
    related_concepts: List[str] = Field(default_factory=list)


class QuestionAnalysis(BaseModel):
    original_question: str
    refined_question: str
    context: str
    focus_points: List[str] = Field(default_factory=list)
    confidence: float


class ProcessedResult(BaseModel):
    original_question: str
    refined_question: str
    entities: List[str]
    entity_contexts: List[EntityContext]
    question_type: str
    confidence: float
    graph_data: Optional[Dict[str, Any]] = None


# Tool functions
def get_path(entity1: str, entity2: str) -> List[str]:
    """Get path between two entities in the knowledge graph."""
    # Mock implementation - replace with actual graph database call
    return [entity1, "intermediate_node", entity2]


def get_subgraph(entity: str, depth: int = 2) -> Dict[str, List[str]]:
    """Get subgraph around an entity with given depth."""
    # Mock implementation - replace with actual graph database call
    return {
        "nodes": [entity, "related1", "related2"],
        "edges": [(entity, "related1"), (entity, "related2")]
    }


def get_entity_context(entity: str) -> str:
    """Get context information for an entity."""
    # Mock implementation - replace with actual knowledge base query
    return f"Context information for {entity}"


def calculate_entity_importance(entity: str, question: str) -> float:
    """Calculate importance score of an entity in the question context."""
    # Mock implementation - replace with actual calculation logic
    return 0.8


class DynamicStateVersion(TypedDict):
    messages: List[BaseMessage]
    params: Dict[str, Any]
    version_id: str
    parent_version_id: Optional[str]
    checkpoint_data: Dict[str, Any]


@dataclass
class DynamicState:
    messages: List[BaseMessage]
    params: Dict[str, Any]
    version_id: str = field(default="root")
    parent_version_id: Optional[str] = field(default=None)
    checkpoint_data: Dict[str, Any] = field(default_factory=dict)

    def __getitem__(self, key):
        if key == "messages":
            return self.messages
        elif key in self.__dict__:
            return self.__dict__[key]
        raise KeyError(f"Key '{key}' not found in DynamicState.")

    def to_dict(self) -> DynamicStateVersion:
        """Convert to dictionary for state versioning"""
        return cast(DynamicStateVersion, asdict(self))

    @classmethod
    def from_dict(cls, data: DynamicStateVersion) -> "DynamicState":
        """Create state from dictionary"""
        return cls(**data)

    def create_checkpoint(self, node_name: str) -> "DynamicState":
        new_version_id = f"{self.version_id}_{node_name}_{id(self)}"
        checkpoint = DynamicState(
            messages=self.messages.copy(),
            params=self.params.copy(),
            version_id=new_version_id,
            parent_version_id=self.version_id,
            checkpoint_data={**self.checkpoint_data, "last_node": node_name}
        )
        return checkpoint

    def rollback_to_parent(self) -> "DynamicState":
        if self.parent_version_id is None:
            return self

        # retrieve the parent state from storage
        parent_checkpoint = DynamicState(
            messages=self.messages[:-1] if len(self.messages) > 1 else self.messages,
            params=self.params,
            version_id=self.parent_version_id,
            parent_version_id=self.parent_version_id.split('_')[0] if '_' in self.parent_version_id else None,
            checkpoint_data=self.checkpoint_data
        )
        return parent_checkpoint


class DynamicChatLLM(BaseChatModel):
    """Dynamic Chat LLM that gets parameters from state"""

    def _generate(
            self,
            messages: List[BaseMessage],
            stop: Optional[List[str]] = None,
            run_manager: Optional[Any] = None,
            **kwargs,
    ) -> str:
        """Sync wrapper around async call"""
        return asyncio.run(self._async_generate(messages, stop, run_manager, **kwargs))

    async def _async_generate(
            self,
            messages: List[BaseMessage],
            stop: Optional[List[str]] = None,
            run_manager: Optional[Any] = None,
            **kwargs,
    ) -> str:
        # Get parameters from run_manager's state
        if not run_manager or not hasattr(run_manager, 'state'):
            raise ValueError("LLM parameters not found in state")

        params = run_manager.state.get('params', {})

        # Convert LangChain messages
        history_messages = []
        system_prompt = None

        for message in messages[:-1]:
            if message.type == "system":
                system_prompt = message.content
            else:
                history_messages.append({
                    "role": message.type,
                    "content": message.content
                })

        prompt = messages[-1].content

        # Call with dynamic parameters
        response = await aigc_app_complete_if_cache(
            model=None,
            prompt=prompt,
            system_prompt=system_prompt,
            history_messages=history_messages,
            **params
        )

        return AIMessage(content=response)

    @property
    def _llm_type(self) -> str:
        return "dynamic_chat_llm"


class SimpleLLM:

    def __init__(self, **kwargs):
        self.params = kwargs

    async def __call__(self, messages: List[BaseMessage], state_params: Dict[str, Any]) -> str:
        system_prompt = None
        history_messages = []

        for msg in messages[:-1]:
            if isinstance(msg, SystemMessage):
                system_prompt = msg.content
            else:
                history_messages.append({
                    "role": "user" if isinstance(msg, HumanMessage) else "assistant",
                    "content": msg.content
                })

        prompt = messages[-1].content

        response = await aigc_app_complete_if_cache(
            model=None,
            prompt=prompt,
            system_prompt=system_prompt,
            history_messages=history_messages,
            **state_params
        )

        return AIMessage(content=response)


async def execute_async(messages: List[Any], params: Dict[str, Any]) -> Any:
    if mode == ExecutionType.DYNAMIC.value:
        llm = DynamicChatLLM(model="dynamic", temperature=0)
        return await llm.invoke(
            messages,
            run_manager=type('RunManager', (), {'state': {'params': params}})()
        )
    elif mode == ExecutionType.LOCAL.value:
        return await SimpleLLM()(messages, params)
    elif mode == ExecutionType.OPENAI.value:
        llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
        return await llm.invoke(messages)
    else:
        raise ValueError(f"Unsupported mode: {mode}")


# Processing functions
async def refine_question(state: DynamicState) -> DynamicState:
    """Refine and optimize the original question."""
    checkpoint = state.create_checkpoint("refine_question")

    if enable_refine == 1:
        messages = [
            SystemMessage(content=QUESTION_REFINEMENT_PROMPT.format(
                question=state["messages"][0].content
            ))
        ]
        result = await execute_async(messages, state.params)
        return DynamicState(
            messages=state.messages + [result],
            params=state.params,
            version_id=checkpoint.version_id,
            parent_version_id=checkpoint.parent_version_id,
            checkpoint_data={**checkpoint.checkpoint_data, "refined": True}
        )
    else:
        return DynamicState(
            messages=state.messages + [AIMessage(content=state.messages[0].content)],
            params=state.params,
            version_id=checkpoint.version_id,
            parent_version_id=checkpoint.parent_version_id,
            checkpoint_data={**checkpoint.checkpoint_data, "refined": False}
        )


async def extract_entities_base(state: DynamicState) -> list:
    """Extract entities from the optimized question."""
    messages = [
        SystemMessage(content=ENTITY_EXTRACTION_PROMPT.format(
            question=state["messages"][-1].content
        ))
    ]
    result = await execute_async(messages, state.params)
    return result.content


async def extract_entities(state: DynamicState) -> DynamicState:
    checkpoint = state.create_checkpoint("extract_entities")

    async def analyze_level(state: DynamicState) -> tuple[str, str]:
        query = state["messages"][-1].content
        example_number = DEFAULT_EXAMPLE_NUM
        if example_number and example_number < len(PROMPTS["keywords_extraction_examples"]):
            examples = "\n".join(
                PROMPTS["keywords_extraction_examples"][: int(example_number)]
            )
        else:
            examples = "\n".join(PROMPTS["keywords_extraction_examples"])
        language = DEFAULT_LANG

        kw_prompt_temp = PROMPTS["keywords_extraction"]
        kw_prompt = kw_prompt_temp.format(query=query, examples=examples, language=language)
        messages = [
            SystemMessage(content=kw_prompt)
        ]
        result = await execute_async(messages, state.params)
        result = result.content

        try:
            # json_text = locate_json_string_body_from_string(result) # handled in use_model_func
            match = re.search(r"\{.*\}", result, re.DOTALL)
            if match:
                result = match.group(0)
                keywords_data = json.loads(result)
                hl_keywords = keywords_data.get("high_level_keywords", [])
                ll_keywords = keywords_data.get("low_level_keywords", [])
            else:
                print("structure not fit.")
                return PROMPTS["fail_response"]

        except json.JSONDecodeError as e:
            print(f"JSON parsing error: {e} {result}")
            return PROMPTS["fail_response"]

        if hl_keywords == [] or ll_keywords == []:
            print("hl_keywords or ll_keywords is empty")
            return PROMPTS["fail_response"]

        return str(ll_keywords), str(hl_keywords)

    async def extract_entities_base(state: DynamicState) -> str:
        if LITE_LLM_MODE == 1:
            return '[]'
        messages = [
            SystemMessage(content=ENTITY_EXTRACTION_PROMPT.format(
                question=state["messages"][-1].content
            ))
        ]
        result = await execute_async(messages, state.params)
        return result.content

    entities, (ll_keywords, hl_keywords) = await asyncio.gather(
        extract_entities_base(state),
        analyze_level(state)
    )

    result = AIMessage(content=[entities, ll_keywords, hl_keywords])

    return DynamicState(
        messages=state.messages + [result],
        params=state.params,
        version_id=checkpoint.version_id,
        parent_version_id=checkpoint.parent_version_id,
        checkpoint_data={**checkpoint.checkpoint_data, "entities_extracted": True}
    )


async def analyze_question_type(state: DynamicState) -> DynamicState:
    checkpoint = state.create_checkpoint("analyze_question_type")

    if LITE_LLM_MODE == 1:
        result = AIMessage(content="None")
        return DynamicState(
            messages=state.messages + [result],
            params=state.params,
            version_id=checkpoint.version_id,
            parent_version_id=checkpoint.parent_version_id,
            checkpoint_data={**checkpoint.checkpoint_data, "question_analyzed": False}
        )

    original_question = state["messages"][0].content
    entities = eval(state["messages"][-1].content[0])

    messages = [
        SystemMessage(content=QUESTION_TYPE_PROMPT.format(
            question=original_question,
            entities=entities
        ))
    ]
    result = await execute_async(messages, state.params)
    return DynamicState(
        messages=state.messages + [result],
        params=state.params,
        version_id=checkpoint.version_id,
        parent_version_id=checkpoint.parent_version_id,
        checkpoint_data={**checkpoint.checkpoint_data, "question_analyzed": True}
    )


async def analyze_entity_context(state: DynamicState) -> DynamicState:
    checkpoint = state.create_checkpoint("analyze_entity_context")

    if LITE_LLM_MODE == 1:
        result = AIMessage(content="{}")
        return DynamicState(
            messages=state.messages + [result],
            params=state.params,
            version_id=checkpoint.version_id,
            parent_version_id=checkpoint.parent_version_id,
            checkpoint_data={**checkpoint.checkpoint_data, "context_analyzed": False}
        )
    question = state["messages"][0].content
    entities = eval(state["messages"][-2].content[0])

    messages = [
        SystemMessage(content=ENTITY_CONTEXT_PROMPT.format(
            entities=entities,
            question=question
        ))
    ]
    result = await execute_async(messages, state.params)
    return DynamicState(
        messages=state.messages + [result],
        params=state.params,
        version_id=checkpoint.version_id,
        parent_version_id=checkpoint.parent_version_id,
        checkpoint_data={**checkpoint.checkpoint_data, "context_analyzed": True}
    )


async def validate_analysis(state: DynamicState) -> DynamicState:
    """Validate the complete analysis results."""
    checkpoint = state.create_checkpoint("validate_analysis")

    original_question = state["messages"][0].content
    refined_question = state["messages"][1].content
    entities = eval(state["messages"][2].content[0])
    question_type = state["messages"][3].content
    entity_context = eval(state["messages"][4].content)

    if LITE_LLM_MODE == 1:
        return DynamicState(
            messages=state.messages + [AIMessage(content="True")],
            params=state.params,
            version_id=checkpoint.version_id,
            parent_version_id=checkpoint.parent_version_id,
            checkpoint_data={**checkpoint.checkpoint_data, "validation_passed": True}
        )

    messages = [
        SystemMessage(content=VALIDATION_PROMPT.format(
            question=original_question,
            refined_question=refined_question,
            entities=entities,
            question_type=question_type,
            entity_context=entity_context
        ))
    ]
    result = await execute_async(messages, state.params)

    # Parse validation result
    is_valid = result.content.strip().lower() == "true"

    return DynamicState(
        messages=state.messages + [result],
        params=state.params,
        version_id=checkpoint.version_id,
        parent_version_id=checkpoint.parent_version_id,
        checkpoint_data={**checkpoint.checkpoint_data, "validation_passed": is_valid}
    )


async def check_confidence(state: DynamicState) -> DynamicState:
    """Check confidence of the analysis results."""
    checkpoint = state.create_checkpoint("check_confidence")

    original_question = state["messages"][0].content
    refined_question = state["messages"][1].content
    entities = eval(state["messages"][2].content[0])
    question_type = state["messages"][3].content
    entity_context = eval(state["messages"][4].content)
    if LITE_LLM_MODE == 1:
        confidence_result = {"confidence": 1.0}
        return DynamicState(
            messages=state.messages + [AIMessage(content=str(confidence_result))],
            params=state.params,
            version_id=checkpoint.version_id,
            parent_version_id=checkpoint.parent_version_id,
            checkpoint_data={**checkpoint.checkpoint_data, "confidence": 1.0}
        )

    messages = [
        SystemMessage(content=CONFIDENCE_CHECK_PROMPT.format(
            question=original_question,
            refined_question=refined_question,
            entities=entities,
            question_type=question_type,
            entity_context=entity_context
        ))
    ]
    result = await execute_async(messages, state.params)

    # Try to parse the confidence value
    try:
        confidence_data = eval(result.content.strip())
        confidence = confidence_data.get("confidence", 0.0)
    except:
        confidence = 0.0

    return DynamicState(
        messages=state.messages + [result],
        params=state.params,
        version_id=checkpoint.version_id,
        parent_version_id=checkpoint.parent_version_id,
        checkpoint_data={**checkpoint.checkpoint_data, "confidence": confidence}
    )


def process_results(state: DynamicState) -> DynamicState:
    """Process and combine all analysis results."""
    checkpoint = state.create_checkpoint("process_results")

    original_question = state["messages"][0].content
    refined_question = state["messages"][1].content
    entities = eval(state["messages"][2].content[0])
    ll_keywords = eval(state["messages"][2].content[1])
    hl_keywords = eval(state["messages"][2].content[2])
    entity_context = eval(state["messages"][4].content)
    confidence_analysis = eval(state["messages"][6].content)
    try:
        question_type = QuestionType(state["messages"][3].content.strip())
    except ValueError:
        question_type = QuestionType.ABSTRACT # FIXME: tmp use here

    result = {
        "original_question": original_question,
        "refined_question": refined_question,
        "entities": entities,
        "ll_keywords": ll_keywords,
        "hl_keywords": hl_keywords,
        "entity_contexts": entity_context,
        "question_type": question_type.value,
        "confidence": confidence_analysis["confidence"],
        "graph_data": None
    }

    if LITE_LLM_MODE == 1:
        return DynamicState(
            messages=state["messages"] + [AIMessage(content=str(result))],
            params=state.params,
            version_id=checkpoint.version_id,
            parent_version_id=checkpoint.parent_version_id,
            checkpoint_data={**checkpoint.checkpoint_data, "processing_complete": True}
        )

    if confidence_analysis["confidence"] > 0.01:
        handler = BaseHandler.get_instance(question_type)
        result = handler.post_action(result)

    return DynamicState(
        messages=state["messages"] + [AIMessage(content=str(result))],
        params=state.params,
        version_id=checkpoint.version_id,
        parent_version_id=checkpoint.parent_version_id,
        checkpoint_data={**checkpoint.checkpoint_data, "processing_complete": True}
    )


def register_handler():
    BaseHandler.register_handler(QuestionType.EXPLANATORY, ContentExplore)
    BaseHandler.register_handler(QuestionType.RELATIONAL, PathExploration)
    BaseHandler.register_handler(QuestionType.COMMUNITY, CommunityDetection)
    BaseHandler.register_handler(QuestionType.ABSTRACT, HighRankAnalysis)


class StateVersionManager:
    """Manages state versions and checkpoints"""

    def __init__(self):
        self.checkpoints = {}

    def save_checkpoint(self, state: DynamicState):
        """Save a checkpoint of the current state"""
        self.checkpoints[state.version_id] = state.to_dict()

    def load_checkpoint(self, version_id: str) -> Optional[DynamicState]:
        """Load a state from a checkpoint"""
        if version_id in self.checkpoints:
            return DynamicState.from_dict(self.checkpoints[version_id])
        return None

    def get_version_history(self, state: DynamicState) -> List[str]:
        """Get the version history for a state"""
        history = []
        current_id = state.version_id

        while current_id:
            history.append(current_id)
            state_data = self.checkpoints.get(current_id)
            if not state_data:
                break
            current_id = state_data.get("parent_version_id")

        return history


def build_graph() -> StateGraph:
    """Build and return the processing graph with state management."""
    # Create state version manager
    state_manager = StateVersionManager()

    # Create memory saver for checkpoints
    memory_saver = MemorySaver()

    # Configure tool nodes for potential future parallel execution
    # tools = {
    #     "entity_extraction": ToolNode(
    #         name="entity_extraction",
    #         description="Extracts entities from text",
    #         func=extract_entities_base
    #     ),
    #     "analyze_level": ToolNode(
    #         name="analyze_level",
    #         description="Analyzes the level of entities",
    #         func=lambda state: None  # Placeholder for future implementation
    #     )
    # }

    builder = StateGraph(DynamicState)

    # Add nodes
    builder.add_node("refine_question", refine_question)
    builder.add_node("extract_entities", extract_entities)
    builder.add_node("analyze_question", analyze_question_type)
    builder.add_node("analyze_entity_context", analyze_entity_context)
    builder.add_node("validate", validate_analysis)
    builder.add_node("check_confidence", check_confidence)
    builder.add_node("process", process_results)

    # Add edges
    builder.add_edge(START, "refine_question")
    builder.add_edge("refine_question", "extract_entities")
    builder.add_edge("extract_entities", "analyze_question")
    builder.add_edge("analyze_question", "analyze_entity_context")
    builder.add_edge("analyze_entity_context", "validate")
    builder.add_edge("validate", "check_confidence")

    def confidence_router(state: DynamicState) -> str:
        """Route based on validation and confidence results with version control."""
        # Save current state checkpoint
        state_manager.save_checkpoint(state)

        validation_result = eval(state["messages"][5].content.strip())
        confidence_result = eval(state["messages"][6].content.strip())

        if not validation_result:
            return "extract_entities"
        elif confidence_result["confidence"] < 0.7:
            return "refine_question"
        else:
            return "process"

    builder.add_conditional_edges(
        "check_confidence",
        confidence_router,
        {
            "process": "process",
            "extract_entities": "extract_entities",
            "refine_question": "refine_question"
        }
    )

    # Add final edge to END
    builder.add_edge("process", END)

    return builder.compile()


def process_question(question: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
    """Process a question through the graph with state management."""
    graph = build_graph()

    initial_params = params or {}

    initial_state = DynamicState(
        messages=[HumanMessage(content=question)],
        params=initial_params,
        version_id="root",
        parent_version_id=None,
        checkpoint_data={"start_time": "2025-03-20T12:00:00Z"}
    )

    result = graph.invoke(initial_state)

    return ast.literal_eval(result["messages"][-1].content)

register_handler()
graph = build_graph()