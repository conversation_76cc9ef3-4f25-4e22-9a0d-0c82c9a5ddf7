# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: graph_processor
# @Author: sun<PERSON>
# @E-mail: sun<PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2025/1/7
# ---
from typing import Dict, Any

from langchain_core.messages import HumanMessage
from apps.graphrag.agent.navigator import graph
from apps.graphrag.agent.app_state import AppConfig, AppState
from apps.graphrag.configs import TEMP_CORP_ID


class GraphProcessor:
    def __init__(self, config: AppConfig):
        AppState.get_instance().initialize(config)
        self.graph = graph

    async def process(
            self,
            question: str,
            aigc_type_id: str,
            model_path: str,
            corp_id: str = TEMP_CORP_ID,
            **additional_params
    ) -> Dict[str, Any]:
        params = {
            "aigc_type_id": aigc_type_id,
            "model_path": model_path,
            "corp_id": corp_id,
            **additional_params
        }

        result = await self.graph.ainvoke({
            "messages": [HumanMessage(content=question)],
            "params": params
        })

        final_analysis = eval(result["messages"][-1].content)
        return final_analysis