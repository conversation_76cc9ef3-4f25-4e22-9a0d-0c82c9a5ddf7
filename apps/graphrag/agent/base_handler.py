# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: graphrag_agent
# @Author: sun<PERSON>
# @E-mail: <EMAIL>
# @Site: 
# @Time: 2024/12/15
# ---
from enum import Enum
from typing import List, Dict, Any

class QuestionType(Enum):
    EXPLANATORY = "explanatory"
    RELATIONAL = "relational"
    COMMUNITY = "community"
    ABSTRACT = "abstract"

class BaseHandler:
    """
    Base class: Graph RAG (Retrieval-Augmented Generation) Agent
    Used for retrieving and analyzing specific problem information from graph data
    """
    _handlers = {}

    def __init__(self, graph_db=None, config: Dict[str, Any] = None):
        """
        Initialize GraphRag Agent

        :param graph_db: Graph database connection
        :param config: Configuration parameter dictionary
        """
        self.graph_db = graph_db
        self.config = config or {}

    def is_agent_enabled(self, query: str) -> bool:
        """
        Determine if the current Agent is applicable to the given query

        :param query: Input question or query string
        :return: Whether to enable the current Agent
        """
        raise NotImplementedError("Subclass must implement is_agent_enabled method")

    def extract_keywords(self, query: str) -> List[str]:
        """
        Extract keywords from the query

        :param query: Input question or query string
        :return: List of keywords
        """
        raise NotImplementedError("Subclass must implement extract_keywords method")

    def retrieve_graph_info(self, query: str, keywords: List[str]) -> Dict[str, Any]:
        """
        Retrieve graph information from the database based on query and keywords

        :param query: Input question or query string
        :param keywords: Keywords extracted from the query
        :return: Graph information dictionary
        """
        raise NotImplementedError("Subclass must implement retrieve_graph_info method")

    def post_action(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Retrieve relevant text chunks based on graph information

        :param params: Graph information dictionary
        :return: List of text chunks
        """
        raise NotImplementedError("Subclass must implement retrieve_text_chunks method")


    @classmethod
    def register_handler(cls, question_type: QuestionType, handler_class: type):
        cls._handlers[question_type] = handler_class

    @classmethod
    def get_instance(cls, question_type: QuestionType):
        handler_class = cls._handlers.get(question_type)
        if handler_class:
            return handler_class()
        raise ValueError(f"Handler for {question_type} not found")
