# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: highrank_analysis
# @Author: sun<PERSON>
# @E-mail: <EMAIL>
# @Site: 
# @Time: 2024/12/15
# ---
from typing import List, Dict, Any

from apps.graphrag.agent.app_state import AppState
from apps.graphrag.agent.base_handler import <PERSON><PERSON>andler
from apps.graphrag.configs import NETWORK_SIZE_LIMIT
from apps.graphrag.manager.prompt import HIGH_RANK_ANALYSIS_PROMPT


class HighRankAnalysis(BaseHandler):
    """
    High Rank Analysis Agent: Focuses on identifying and analyzing high-importance nodes in the graph
    """

    def is_agent_enabled(self, query: str) -> bool:
        """
        Determine if it's a community-related query

        :param query: Input question or query string
        :return: Whether to enable Community Detection Agent
        """
        keywords = ['456']
        print(keywords)
        return any(keyword in query.lower() for keyword in keywords)

    def extract_keywords(self, query: str) -> List[str]:
        """
        Extract community-related keywords

        :param query: Input question or query string
        :return: List of keywords
        """
        return [word for word in query.split() if len(word) > 1]

    def retrieve_graph_info(self, query: str, keywords: List[str]) -> Dict[str, Any]:
        """
        Retrieve community graph information

        :param query: Input question or query string
        :param keywords: Keywords extracted from the query
        :return: Graph information dictionary
        """
        # TODO: Implement actual community detection query logic
        return {
            'communities': [],
            'community_sizes': {},
            'inter_community_connections': []
        }

    def post_action(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Retrieve text chunks based on community information

        :param params: Graph information dictionary
        :return: List of text chunks
        """
        def _get_net() -> list[Any]:
            percent, limit = 10, 10

            def top_p(data, percentage):
                num = int(len(data) * percentage / 100)
                return [key for key, _ in data[:num]]

            def top_threshold(data, percentage):
                max_value = max(value for _, value in data)
                threshold = max_value * (percentage / 100)
                return [key for key, value in data if value >= threshold]

            state = AppState.get_instance()
            content = state.kg_inst.process_net(NETWORK_SIZE_LIMIT, limit)
            result = top_threshold(content['degree_centrality'], percent)
            return result

        net_list = _get_net()
        new_params = params.copy()
        new_params["graph_data"] = HIGH_RANK_ANALYSIS_PROMPT.format(
            concept_list=net_list,
            concept_count=len(net_list)
        )
        new_params["ll_keywords"] = net_list
        return new_params

