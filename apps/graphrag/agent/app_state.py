# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: app_state
# @Author: <PERSON><PERSON>
# @E-mail: sun<PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2025/1/7
# ---
from dataclasses import dataclass
from apps.graphrag.manager.base import BaseGraphStorage


@dataclass
class AppConfig:
    kg_inst: BaseGraphStorage

class AppState:
    _instance = None

    def __init__(self):
        if AppState._instance is not None:
            raise Exception("AppState fail to init!")
        self._kg_inst = None
        self._config = None
        AppState._instance = self

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = AppState()
        return cls._instance

    def initialize(self, config: AppConfig):
        self._config = config

    @property
    def kg_inst(self):
        if not self._kg_inst:
            self._kg_inst = self._config.kg_inst
        return self._kg_inst