# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: graphrag
# @Author: <PERSON><PERSON>
# @E-mail: sun<PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2024/12/9
# ---
import logging

from apps.graphrag.manager import GraphRAG
from apps.graphrag.configs import DEFAULT_LANG, DEFAULT_EXAMPLE_NUM


class GraphRag:
    def __init__(
            self,
            corp_id:str,
            namespace: str,
            project_logger:logging.Logger = None
    ):
        self._graphrag = GraphRAG(
            corp_id = corp_id,
            storage_namespace = namespace,
            addon_params={
                "example_number": DEFAULT_EXAMPLE_NUM,
                "language": DEFAULT_LANG
            },
            project_logger=project_logger
        )

    def get_instance(self) -> GraphRAG:
        return self._graphrag
