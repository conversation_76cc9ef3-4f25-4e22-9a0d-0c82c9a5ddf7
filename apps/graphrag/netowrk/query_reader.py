# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: query_reader.py
# @Author: sun<PERSON>
# @E-mail: <EMAIL>
# @Site:
# @Time: 2025/1/8
# ---
import networkx as nx
from nebula3.Config import Config
from nebula3.gclient.net import ConnectionPool
from nebula3.data.ResultSet import ResultSet
from apps.graphrag.netowrk.net_utils import KGNetConfig, result_to_df

class KGReader:
    def __init__(
        self,
        edges: list,
        properties: list,
        nebula_config: KGNetConfig,
        limit: int,
        with_rank: bool = False,
    ):
        self.edges = edges
        self.properties = properties
        self.limit = limit

        config = Config()
        graphd_hosts = nebula_config.graphd_hosts.split(",")
        graphd_host_list = [
            (host.split(":")[0], int(host.split(":")[1])) for host in graphd_hosts
        ]
        self.space = nebula_config.space
        self.nebula_user = nebula_config.user
        self.nebula_password = nebula_config.password
        self.connection_pool = ConnectionPool()
        assert self.connection_pool.init(
            graphd_host_list, config
        ), "Init Connection Pool Failed"
        assert len(edges) > 0 and len(edges) == len(
            properties
        ), "edges and properties should have the same length"
        self.with_rank = with_rank

    def read(self):
        with self.connection_pool.session_context(
            self.nebula_user, self.nebula_password
        ) as session:
            assert session.execute(
                f"USE {self.space}"
            ).is_succeeded(), f"Failed to use space {self.space}"
            result_list = []
            g = nx.MultiDiGraph()
            for i in range(len(self.edges)):
                edge = self.edges[i]
                properties = self.properties[i]
                properties_query_field = ""
                for property in properties:
                    properties_query_field += f", e.{property} AS {property}"
                if self.with_rank:
                    properties_query_field += ", rank(e) AS `__rank__`"
                result = session.execute(
                    f"MATCH ()-[e:`{edge}`]->() RETURN src(e) AS src, dst(e) AS dst{properties_query_field} LIMIT {self.limit}"
                )
                assert result.is_succeeded()
                result_list.append(result)

            # merge all result
            for i, result in enumerate(result_list):
                _df = result_to_df(result)
                # TBD, consider add label of edge
                properties = self.properties[i] if self.properties[i] else None
                if self.with_rank:
                    properties = properties + ["__rank__"]
                    _g = nx.from_pandas_edgelist(
                        _df,
                        "src",
                        "dst",
                        properties,
                        create_using=nx.MultiDiGraph(),
                        edge_key="__rank__",
                    )
                else:
                    _g = nx.from_pandas_edgelist(
                        _df,
                        "src",
                        "dst",
                        properties,
                        create_using=nx.MultiDiGraph(),
                    )
                g = nx.compose(g, _g)
            return g

    def release(self):
        self.connection_pool.close()

    def __del__(self):
        self.release()


class KGQueryReader:
    def __init__(self, nebula_config: KGNetConfig):
        self.config = nebula_config
        self.connection_pool = ConnectionPool()
        graphd_hosts = nebula_config.graphd_hosts.split(",")
        graphd_host_list = [
            (host.split(":")[0], int(host.split(":")[1])) for host in graphd_hosts
        ]
        config = Config()
        assert self.connection_pool.init(
            graphd_host_list, config
        ), "Init Connection Pool Failed"

    def read(self, query: str) -> nx.MultiDiGraph:
        with self.connection_pool.session_context(
            self.config.user, self.config.password
        ) as session:
            assert session.execute(
                f"USE {self.config.space}"
            ).is_succeeded(), f"Failed to use space {self.config.space}"

            result: ResultSet = session.execute(query)
            assert (
                result.is_succeeded()
            ), f"Query execution failed: {result.error_msg()}"

            vis_data = result.dict_for_vis()
            return self._construct_graph(vis_data)

    def _construct_graph(self, vis_data: dict) -> nx.MultiDiGraph:
        g = nx.MultiDiGraph()

        for node_data in vis_data["nodes"]:
            g.add_node(
                node_data["id"], **node_data["props"], labels=node_data["labels"]
            )

        for edge_data in vis_data["edges"]:
            g.add_edge(
                edge_data["src"],
                edge_data["dst"],
                key=edge_data["name"],
                **edge_data["props"],
            )

        return g

    def release(self):
        self.connection_pool.close()

    def __del__(self):
        self.release()


def process_net(graph, limit:int = 10):
    graph = preprocess_graph(graph)
    pagerank = compute_pagerank(graph, limit)
    degree_centrality = compute_degree_centrality(graph, limit)
    weighted_degree = compute_weighted_degree(graph, limit)
    k_core = compute_k_core(graph, limit)
    closeness_centrality = compute_closeness_centrality(graph, limit)
    result = {
        "pagerank": pagerank,
        "degree_centrality": degree_centrality,
        "weighted_degree": weighted_degree,
        "k_core": k_core,
        "closeness_centrality": closeness_centrality,
    }
    return result


def preprocess_graph(graph):
    if isinstance(graph, nx.MultiDiGraph):
        graph = nx.DiGraph(graph)
    return graph


def compute_pagerank(graph, limit, alpha=0.85):
    pagerank = nx.pagerank(graph, alpha=alpha)
    core_nodes = sorted(pagerank.items(), key=lambda x: x[1], reverse=True)
    return core_nodes[:limit]

def compute_degree_centrality(graph, limit):
    degree_centrality = nx.degree_centrality(graph)
    core_nodes = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)
    return core_nodes[:limit]

def compute_weighted_degree(graph, limit):
    weighted_degree = {node: sum(weight for _, _, weight in graph.edges(node, data='weight')) for node in graph.nodes()}
    core_nodes = sorted(weighted_degree.items(), key=lambda x: x[1], reverse=True)
    return core_nodes[:limit]

def compute_k_core(graph, limit):
    graph.remove_edges_from(nx.selfloop_edges(graph))
    k_core = nx.core_number(graph)
    core_nodes = sorted(k_core.items(), key=lambda x: x[1], reverse=True)
    return core_nodes[:limit]

def compute_closeness_centrality(graph, limit):
    closeness_centrality = nx.closeness_centrality(graph)
    core_nodes = sorted(closeness_centrality.items(), key=lambda x: x[1], reverse=True)
    return core_nodes[:limit]
