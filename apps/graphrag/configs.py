# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: configs
# @Author: <PERSON><PERSON>
# @E-mail: sun<PERSON>@zhongshuruizhi.com
# @Site: 
# @Time: 2024/11/26
# ---
import os

from settings import DEFAULT_LOCAL_EMB_MODEL_PATH, INS_GRAPH_CONF

DEFAULT_LOCAL_EMB_MODEL_PATH_GRAPH = DEFAULT_LOCAL_EMB_MODEL_PATH


VDB_ENTITIES = "graph_entities"
VDB_RELATIONSHIPS = "graph_relationships"
VDB_CHUNKS = "knowledge"
RAG_MODE = "hybrid"
DEFAULT_LANG = "Simplified Chinese"
DEFAULT_EXAMPLE_NUM = 2

# Storage
KV_STORAGE = "MysqlKVStorage"
RDB_STORAGE = "MysqlRDBStorage"
VECTOR_STORAGE = "EsVectorDBStorage"
GRAPH_STORAGE = "GqlStorage"
TIKTOKEN_MODEL_NAME = "gpt-3.5"
DEFAULT_STORAGE_NAMESPACE = "default"

# text chunking 配置
CHUNK_TOKEN_SIZE = 1200
CHUNK_OVERLAP_TOKEN_SIZE = 100

# entity extraction 配置
ENTITY_EXTRACT_MAX_GLEANING = 1
ENTITY_SUMMARY_TO_MAX_TOKENS = 5000

ENTITY_TYPES = ["organization", "person", "geo", "event", "category"]
OTHER_TYPE = "others"
EDGE_TYPE = "DIRECTED"


# GRAPH
GRAPH_HOSTS = INS_GRAPH_CONF['host']
GRAPH_PORT = INS_GRAPH_CONF['port']
GRAPH_USERNAME = INS_GRAPH_CONF['username']
GRAPH_PASSWORD = INS_GRAPH_CONF['password']

SPACE_PREFIX = "g_"
HEARTBEAT_INTERVAL_SECS = 10
CONNECTION_POOL_SIZE = 100
IDLE_TIME = 180
INTERVAL_CHECK = 60
PARTITION_NUM = 10
REPLICA_FACTOR = 1
# using utf-8 encoding
VID_TYPE = "FIXED_STRING(100)"
VID_LIMIT = 30
QUERY_SIZE_LIMIT = 300
NETWORK_SIZE_LIMIT = 10000

# PROJECT
# used for non-multi-tenant
TEMP_CORP_ID = "wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ"
CHUNKS_LIMIT = 500
CHUNK_CHAR_MAX = 2000
RECORD_ENABLE = True
RECORD_INTERVAL = 5
EXECUTION_TYPE = "LOCAL"
LINK_MODE = "GRAPH"
ES_BULK_BATCH_SIZE = 100
ENTITY_DESC_MAX_LEN = 2000

# TEMP
DEFAULT_AIGC_TYPE_ID = INS_GRAPH_CONF['aigc_type_id']
DEFAULT_MODEL_PATH=INS_GRAPH_CONF['model_path']

# DEFAULT_AIGC_TYPE_ID = "DeepSeek-R1"
# DEFAULT_MODEL_PATH="deepseek-reasoner"