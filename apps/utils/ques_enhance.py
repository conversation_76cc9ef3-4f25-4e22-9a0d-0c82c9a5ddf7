from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import re

class QuesEnhancement:
    # 定义中文数字映射到阿拉伯数字
    CHINESE_NUM_MAP = {
        "一": 1, "二": 2, "三": 3, "四": 4, "五": 5, "六": 6,
        "七": 7, "八": 8, "九": 9, "十": 10, "十一": 11, "十二": 12
    }

    def __init__(self, list_rules, list_replace_flag=True, date_replace_flag=False, date_enabled_flag=True):
        self.list_rules = list_rules
        self.list_replace_flag = list_replace_flag
        self.date_replace_flag = date_replace_flag
        self.date_enabled_flag = date_enabled_flag

    @staticmethod
    def generate_date_string(days_offset=0, months_offset=0, years_offset=0):
        """根据偏移量生成日期字符串，支持天、月、年偏移，并显示星期几（中文）。"""
        target_date = datetime.now() + timedelta(days=days_offset) + relativedelta(months=months_offset,
                                                                                   years=years_offset)

        week_day_english = target_date.strftime("%A")
        week_day_mapping = {
            "Monday": "星期一",
            "Tuesday": "星期二",
            "Wednesday": "星期三",
            "Thursday": "星期四",
            "Friday": "星期五",
            "Saturday": "星期六",
            "Sunday": "星期天"
        }
        week_day_chinese = week_day_mapping.get(week_day_english, "")
        return target_date.strftime(f"%Y年%m月%d日 {week_day_chinese}")

    @classmethod
    def chinese_to_number(cls, chinese_str):
        """将中文数字转换为阿拉伯数字。"""
        return cls.CHINESE_NUM_MAP.get(chinese_str, None) or int(chinese_str)

    def handle_relative_date(self, match):
        """处理包含数字的相对日期，如 '3天前'，'5个月之前'，'二年前'。"""
        num_str = match.group(1)  # 获取匹配的数字（可以是中文）
        unit = match.group(2)  # 获取匹配的时间单位（天、月、年）
        direction = match.group(3)  # 获取匹配的方向部分

        # 将中文数字转换为阿拉伯数字
        num = self.chinese_to_number(num_str)

        # 根据单位和方向调整日期
        if unit == "天":
            calculated_date = QuesEnhancement.generate_date_string(days_offset=num if direction in ["后", "之后"] else -num)
        elif unit == "月":
            calculated_date = QuesEnhancement.generate_date_string(months_offset=num if direction in ["后", "之后"] else -num)
        elif unit == "年":
            calculated_date = QuesEnhancement.generate_date_string(years_offset=num if direction in ["后", "之后"] else -num)
        else:
            return match.group(0)  # 如果没有匹配到正确的单位，则返回原始文本

        # 根据 date_replace_flag 返回不同的替换格式
        if self.date_replace_flag:
            return calculated_date  # 直接替换成日期
        else:
            return f"{match.group(0)}({calculated_date})"  # 括号内保留计算后的日期

    def create_replacement_rules(self):
        """创建最终替换规则，传入规则优先级高于日期替换规则。"""
        # 如果禁用了日期替换，则直接返回传入的规则
        if not self.date_enabled_flag:
            # 新的list_rules格式是列表中的字典，处理方式不同
            return {rule["origin"]: rule["replace"] for rule in self.list_rules}

        # 定义日期相关规则
        date_replacements = {
            "今天": QuesEnhancement.generate_date_string(0),
            "昨天": QuesEnhancement.generate_date_string(-1),
            "明天": QuesEnhancement.generate_date_string(1),
            "前天": QuesEnhancement.generate_date_string(-2),
            "大前天": QuesEnhancement.generate_date_string(-3),
            "后天": QuesEnhancement.generate_date_string(2),
            "大后天": QuesEnhancement.generate_date_string(3),
        }

        # 合并规则，传入的规则优先级高于日期替换规则
        combined_rules = {**date_replacements, **{rule["origin"]: rule["replace"] for rule in self.list_rules}}

        return combined_rules

    def transform_string(self, input_str):
        """根据最终替换规则转换输入字符串，处理传入规则和日期规则的替换标志。"""
        # 如果启用了日期替换规则
        if self.date_enabled_flag:
            # 使用正则表达式处理 "n天前"、"n天后"、"n月前"、"n年前"、"n天之前" 等
            input_str = re.sub(r"(十一|十二|[一二三四五六七八九十]|\d+)个?(天|月|年)(前|后|之前|之后)",
                               self.handle_relative_date, input_str)

        # 获取最终的替换规则
        rules = self.create_replacement_rules()

        # 执行替换
        for rule in self.list_rules:
            key = rule["origin"]
            value = rule["replace"]

            # 判断当前替换规则属于哪一类，并应用对应的替换标志
            if self.list_replace_flag:
                replacement = value
            else:
                replacement = f"{key}({value})"

            # 执行替换
            input_str = input_str.replace(key, replacement)

        # 执行日期替换
        for key, value in rules.items():
            if key not in [rule["origin"] for rule in self.list_rules]:
                replacement = value if self.date_replace_flag else f"{key}({value})"
                input_str = input_str.replace(key, replacement)

        return input_str
    


if __name__ == '__main__':
    # 示例：传入规则和替换配置
    list_rules = [
        {"origin": "apple", "replace": "pen"},
        {"origin": "have", "replace": "123"}
    ]

    # 实例化类，传入规则直接替换，日期规则加括号显示（date_replace_flag=False）
    ques_enhancer = QuesEnhancement(list_rules, list_replace_flag=True, date_replace_flag=True, date_enabled_flag=True)

    # 测试输入
    input_str = "I have an apple and today is 今天, 3天之前, 5个月之前, 二年前, and 十天后 are all significant dates."

    # 使用 transform_string 进行字符串转换
    output = ques_enhancer.transform_string(input_str)
    print(output)


    