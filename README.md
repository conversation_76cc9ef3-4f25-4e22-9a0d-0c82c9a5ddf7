# 配置文档

## 如何启动snowflake

```shell
snowflake_start_server --worker=1

#服务器后台启动
nohup snowflake_start_server --address=127.0.0.1 --port=8910 --dc=1 --worker=1 --log_file_prefix=/tmp/pysnowflask.log>/dev/null &

#mac 启动
snowflake_start_server --worker=1
```

## 升级task_logger的日志显示

日志示例

```
[21488:8637706752] 2025-03-10 16:15:28,661 test_logger.py:77  - INFO - This should show test_function as caller
```

解释：

| 字段                                         | 含义                                       |
|--------------------------------------------|------------------------------------------|
| `21488`                                    | 进程ID（PID），表示当前日志记录的进程编号。                 |
| `8637706752`                               | 线程ID，表示当前日志记录的线程编号。                      |
| `2025-03-10 16:15:28,661`                  | 日志记录的时间戳，精确到毫秒。                          |
| `test_logger.py:77`                        | 日志记录的源代码位置，文件名为`test_logger.py`，行号为`77`。 |
| `INFO`                                     | 日志级别，表示这是一条普通信息日志。                       |
| `This should show test_function as caller` | 日志消息内容。                                  |
