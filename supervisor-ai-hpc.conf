[program:jusure-ai]
user=yons
directory = /home/<USER>/projects/zs/jusure_AI
command=/home/<USER>/shao.li/jusure_AI/venv/bin/uwsgi --ini /home/<USER>/projects/zs/jusure_AI/uwsgi_prod.ini
stderr_logfile = //home/<USER>/projects/zs/jusure_AI/logs/run.log
stopasgroup=true
killasgroup=true
environment=ENVIRONMENT=PROD

[program:jusure-ai-worker]
user=yons
directory=/home/<USER>/projects/zs/jusure_AI
command=/home/<USER>/shao.li/jusure_AI/venv/bin/celery --app=celery_app worker -l info
stdout_logfile=/home/<USER>/projects/zs/jusure_AI/logs/celery_worker.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile=/home/<USER>/projects/zs/jusure_AI/logs/celery_worker_err.log
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=10
autostart=true
autorestart=true
startsecs=10
stopwatisecs=60
priority=997
stopasgroup=true
killasgroup=true
environment=ENVIRONMENT=PROD

[program:jusure-ai-beat]
user=yons
directory=/home/<USER>/projects/zs/jusure_AI
command=/home/<USER>/shao.li/jusure_AI/venv/bin/celery --app=celery_app beat -l info
stdout_logfile=//home/<USER>/projects/zs/jusure_AI/logs/celery.beat.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile=//home/<USER>/projects/zs/jusure_AI/logs/celery_beat_err.log
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=10
autostart=true
autorestart=true
startsecs=10
stopwatisecs=60
priority=998
stopasgroup=true
killasgroup=true
environment=ENVIRONMENT=PROD


[group:hpc-ai]
user=yons
programs=jusure-ai,jusure-ai-worker,jusure-ai-beat
environment=ENVIRONMENT=PROD