from flask import request
from puppet.cache import redis_pool

# 角色对应ID编号
SuperManager = 'b5e2b5d8-03aa-11ee-9aa2-0242ac110003'
ContentManager = '6da1a175-fae8-11ed-9aa2-0242ac110003'
ContentUploader = 'c0deb565-ee32-11ed-9aa2-0242ac110003'
ContentReader = 'c67b29e0-ee32-11ed-9aa2-0242ac110003'
FissionPlotter = 'lbchz01'
FissionExecutor = 'lbzxz02'
ClueManager = 'xsglz01'
LibraryManager = 'zkgly01'

# 权限等级 逐级递减
"""
    0 超级管理员 权限 [全部权限]
    1 素材管理员 素材的增删改查；发布；上架
    2 素材上传者 素材的增查；自身文章的删改
    3 素材使用者 素材的查看
"""
content_grade_code_map = {
    '0': SuperManager,
    '1': ContentManager,
    '2': ContentUploader,
    "3": ContentReader
}


# 素材角色api权限校验
def permit(grade: int, check_author=False, key_name=None, _class=None):
    def inner(func):
        def inner_inner(*args, **kwargs):

            role_ids = request.user['role_ids']
            tp_user_id = request.user['tp_user_id']
            req_data = request.pmsd
            for i in range(grade + 1):
                role_id = content_grade_code_map.get(str(i))
                if role_id and role_id in role_ids:
                    if i == grade and check_author:
                        if key_name:
                            params_data = req_data.get(key_name)
                            # 存在class类，则特定方法进行数据检测
                            if _class is not None:
                                params_list = [x for x in params_data.split(',') if x]
                                if _class().check_is_auther(params_list, tp_user_id):
                                    return func(*args, **kwargs)
                                else:
                                    raise ValueError('权限限制')
                            # 检测是否为作者
                            else:
                                if tp_user_id == req_data.get(key_name):
                                    return func(*args, **kwargs)
                    else:
                        return func(*args, **kwargs)
            else:
                raise ValueError('权限限制')

        return inner_inner

    return inner


# 二进制数位对应素材操作权限
"""
    permits = [下架， 发布， 上线， 权限， 删除， 编辑], 是否作者检测，是否有功能键权限
    permits = [0, 0, 0, 0, 0, 0]
"""

content_role_grade_map_list = [
    (SuperManager, [1, 1, 1, 1, 1, 1], False, True),
    (ContentManager, [1, 1, 1, 1, 1, 1], False, True),
    (ContentUploader, [0, 0, 0, 0, 0, 0], True, True),
    (ContentReader, [0, 0, 0, 0, 0, 0], True, False)
]

Auther = [1, 0, 1, 1, 1, 1]
Default = [0, 0, 0, 0, 0, 0]


def permit_map_code(user_id):
    role_ids = request.user['role_ids']
    tp_user_id = request.user['tp_user_id']
    for one in content_role_grade_map_list:
        if one[0] in role_ids:
            # 判断操作者是否为作者
            if one[2] and user_id == tp_user_id:
                return int('0b' + ''.join(map(str, Auther)), 2)
            else:
                return int('0b' + ''.join(map(str, one[1])), 2)
    else:
        if user_id == tp_user_id:
            return int('0b' + ''.join(map(str, Auther)), 2)
        else:
            return int('0b' + ''.join(map(str, Default)), 2)


def check_create():
    role_ids = request.user['role_ids']
    for one in content_role_grade_map_list:
        if one[0] in role_ids:
            return one[3]
    else:
        return False


def check_phone_permit(phone, code):
    cache_data = redis_pool.use(redis_pool.token_pool).get(phone)
    if cache_data == code:
        redis_pool.use(redis_pool.token_pool).delete(phone)
        return True
    raise ValueError('验证码错误')


def check_client_permit(client_id, is_save=False):
    if is_save:
        redis_pool.use(redis_pool.token_pool).setex(client_id, 24 * 3600 * 2, '1')
        return True
    else:
        cache_data = redis_pool.use(redis_pool.token_pool).get(client_id)
        if cache_data == '1':
            return True
        return False


def check_phone_permit_v2(phone, code):
    cache_data = redis_pool.use(redis_pool.token_pool).get(phone)
    if cache_data == code:
        redis_pool.use(redis_pool.token_pool).delete(phone)
        return True
    else:
        return False
