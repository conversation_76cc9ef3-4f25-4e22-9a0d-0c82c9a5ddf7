# -*- coding: utf-8 -*-
import json
from flask import request, g
from functools import wraps
from authlib.jose import jwt
from utils.tools import sha256, md5
from settings import SECRET_KEY, SECRET_KEY_FOR_OS, GTApiKey, ENVIRONMENT
from puppet.cache import redis_pool
from lib_func.const_map import StatusMap
from horticulture.validate import json_response
from horticulture.permission import SuperManager
from controller.user_controller import RoleController, TPUserController

long_user = long_user = {'tp_user_id': 'edb15b16-2443-4314-bea1-06c5b7b944a5', 'dp_id': '1', 'dp_name': '中数睿智', 'ww_user_id': None,
                         'tp_user_name': 'demo-bi', 'alias_name': '', 'user_name': 'demo-bi',
                         'avatar': 'https://zhongshu-test.oss-cn-beijing.aliyuncs.com/adc900e4-5d19-49d3-bfcf-25fd10e863c6/WX20240521-113439.png',
                         'thumb_avatar': None, 'character_setting': '',
                         'company_name': {'org_id': 'wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ',
                                          'sale_version_id': '8c5038f7-ee2d-11ed-9aa2-0242ac110003',
                                          'sale_seat_num': 1000, 'org_name': '中数睿智', 'sale_version_name': '旗舰版',
                                          'logo_url': 'https://zhongshu-test.oss-cn-beijing.aliyuncs.com/adc900e4-5d19-49d3-bfcf-25fd10e863c6/zhongshu.png',
                                          'slogan': '创造价值，让世界更美好', 'address': '北京市海淀区量子银座10层',
                                          'email': '<EMAIL>',
                                          'phone': ''}, 'phone_number': '13555555555', 'email': None, 'position': '中数睿智',
                         'seat_flag': '1',
                         'qrcode': None, 'status': '1'}


def login_check(method):
    @wraps(method)
    def decorator(*args, **kwargs):
        token = request.headers.get("token")
        g.token = token
        redis_client = redis_pool.use(redis_pool.token_pool)
        dict_redis_client = redis_pool.use(redis_pool.dict_pool)
        system_variable_key = 'system_variable_' + g.corpid
        if token is None:
            raise ValueError(StatusMap['missing_token'])
        elif token == '836de7116db3d779fb52fe584c7f975a':
            user_info = long_user
        else:

            data = redis_client.get(token)
            # data = dict_redis_client.get(token)

            if data is None:
                raise ValueError(StatusMap['token_expired'])
            user_info = json.loads(data)

        if user_info:
        
            is_unauthorized_access = dict_redis_client.hget(system_variable_key, 'IS_UNAUTHORIZED_ACCESS')
            if is_unauthorized_access and is_unauthorized_access == '1':
                timestamp = request.headers.get('timestamp')
                tp_user_id = user_info['tp_user_id']
                
                _sign = md5(f'{timestamp}{tp_user_id}{SECRET_KEY_FOR_OS}')
                
                sign = request.headers.get('sign')
                if not sign:
                    raise ValueError(StatusMap['missing_sign'])
                elif not timestamp or not sign:
                    raise ValueError(StatusMap['missing_timestamp_sign'])
                elif _sign != sign:
                    raise ValueError(StatusMap['unauthorized_access'])
            
            # 使用用户Token缓存数据
            # user_info = json.loads(data)
            cache_user_info = TPUserController().user_info(user_info['tp_user_id'])
            if not cache_user_info:
                if cache_user_info == {}:
                    raise ValueError(StatusMap['sign_out'])
                else:
                    if user_info.get('seat_flag') != '1':
                        raise ValueError(StatusMap['seat_flag'])
            else:
                redis_client.setex(token, 3 * 24 * 3600, json.dumps(cache_user_info))
                if cache_user_info.get('seat_flag') != '1':
                    raise ValueError(StatusMap['seat_flag'])
            # 填充用户的系统角色
            role_ids = RoleController().get_all_user_role_ids(user_info['tp_user_id'])
            user_info['role_ids'] = role_ids or list()
            user_info['auth_ids'] = RoleController().get_user_auth_ids(user_info['tp_user_id']) or list()
            user_info['dp_id'] = user_info['dp_id']
            request.user = user_info
        else:
            raise ValueError(StatusMap['invalid_token'])
        return method(*args, **kwargs)

    return decorator


def admin_login(method):
    @wraps(method)
    def decorator(*args, **kwargs):
        token = request.headers.get("token")
        request.user = dict()
        if token:
            try:
                redis_client = redis_pool.use(redis_pool.token_pool)
                data = redis_client.get(token)
                if data:
                    cache_user_info = json.loads(data)
                    if cache_user_info:
                        user_info = TPUserController().user_info(cache_user_info['tp_user_id'])
                        if not user_info:
                            raise ValueError(StatusMap['sign_out'])
                        # 填充用户的系统角色
                        role_ids = RoleController().get_all_user_role_ids(user_info['tp_user_id'])
                        user_info['role_ids'] = role_ids
                        request.user = user_info
            except:
                pass
            finally:
                return method(*args, **kwargs)
        else:
            return method(*args, **kwargs)

    return decorator


def openapi_check(method):
    @wraps(method)
    def decorator(*args, **kwargs):
        token = request.headers.get("Authorization")
        if token is None:
            raise ValueError(StatusMap['missing_token'])
        if token[7:] != 'dataset-vxwdOFWybnZIoOLWzfaAnGRM' and token[7:] != 'dataset-9tFZvlxZSgkIizorpA03U2Uz':
            raise ValueError(StatusMap['invalid_token'])
        return method(*args, **kwargs)

    return decorator


def set_mini_token(func) -> object:
    @wraps(func)
    def decorator(*args, **kwargs):
        data_res = func(*args, **kwargs)
        token = request.headers.get("token")
        redis_client = redis_pool.use(redis_pool.token_pool)
        if token:
            redis_client.delete(token)
        if data_res == 3:
            return json_response(code=StatusMap['bind_failed']['code'], message=StatusMap['bind_failed']['message'])
        elif data_res:
            # token = generate_token(data_res)
            token = sha256(json.dumps(data_res))
            redis_client.setex(token, 3 * 24 * 3600, json.dumps(data_res, ensure_ascii=False))
            data_res['token'] = token
            return json_response(code='SUCCESS', data=data_res)
        else:
            return json_response(code=StatusMap['login_failed']['code'], message=StatusMap['login_failed']['message'])

    return decorator


def set_token(func) -> object:
    @wraps(func)
    def decorator(*args, **kwargs):
        data_res = func(*args, **kwargs)
        token = request.headers.get("token")
        redis_client = redis_pool.use(redis_pool.token_pool)
        if token:
            redis_client.delete(token)
        if data_res:
            # token = generate_token(data_res)
            token = sha256(json.dumps(data_res))
            redis_client.setex(token, 3 * 24 * 3600, json.dumps(data_res, ensure_ascii=False))
            data_res['token'] = token
            return json_response(code='SUCCESS', data=data_res)
        else:
            raise ValueError(StatusMap['login_failed'])

    return decorator


def del_token(func) -> object:
    @wraps(func)
    def decorator(*args, **kwargs):
        func(*args, **kwargs)
        token = request.headers.get("token")
        redis_client = redis_pool.use(redis_pool.token_pool)
        res = redis_client.delete(token)
        if res:
            return True
        else:
            return False

    return decorator


def generate_token(user_info):
    """生成用于邮箱验证的JWT（json web token）"""
    # 签名算法
    header = {'alg': 'HS256'}
    return jwt.encode(header=header, payload=user_info, key=SECRET_KEY).decode()


def validate_token(token):
    """用于验证用户注册和用户修改密码或邮箱的token, 并完成相应的确认操作"""
    try:
        data = jwt.decode(token, SECRET_KEY)
        return data
    except:
        return None


def sign_check(func):
    @wraps(func)
    def inner(*args, **kwargs):
        if all(x not in g.ip for x in ['127.0.0.1', '192.168', '0.0.0.0', '10.20.212','*************', '************']):
            raise ValueError('非法ip请求')
        params = request.pmsd
        sign = params.pop('sign')
        date_str = params.pop('date')
        others = sorted(params.items(), key=lambda x: x[0])
        data_str = '&'.join([f'{x[0]}={x[1]}' for x in others]) + f'&date={date_str}&key={GTApiKey}'
        if sign == md5(data_str) or ENVIRONMENT == 'local':
            return func(*args, **kwargs)
        else:
            raise ValueError('鉴权失败')
    return inner