import re
from flask import request, json, g
from lib_func.const_map import StatusMap
import json
import time
import uuid
from datetime import datetime, date
from decimal import Decimal
from bson import ObjectId
import numpy as np
from flask import make_response

url_white = ["/third_login", '/login', '/agent_login', '/content/check/notice', '/sync/client/info', '/sync/user/info',
             '/suite/receive', '/client/work_client', '/question/summary/train', '/question/rel/train',
             '/content/train/data', '/test/stream', '/v1/datasets']

url_white_map = {'/client/code': ['post', 'put'], '/client/register': ['post'],
                 '/landpage': ['get'], '/landpageid/list': ['get'], '/client/land/register': ['post'],
                 '/client/work_client': ['get'],
                 '/question/summary/train': ['get'], '/question/rel/train': ['get'], "/ai/app/sample/export": ['get']}

url_params_white = ['/config/mini/info']

url_notify_white = '/mini/course/notify'

url_stream_white = ['/test/stream', '/ai/conversation', '/ai/app/session', '/ai/app/prompt/optimize', '/ai/search/content',
                    '/ai/app/report', '/demo/app/report', '/ai/knowledge/doc/detail', '/ai/graphic/script', '/ai/app/chat2',
                    '/ai/flow/session']

# 在请求前加入参数过滤，防止sql注入
pattern = r"\b(and|like|exec|insert|select|drop|grant|alter|delete|update|count|master|truncate|char|delclare)"


def req_before_do():
    request_data()
    analyse_headers()
    _path = request.path
    _method = request.method.lower()
    # 获取客户端IP，优先使用X-Forwarded-For，如果没有则使用remote_addr
    client_ip = request.remote_addr
    if 'X-Forwarded-For' in request.headers:
        client_ip = request.headers['X-Forwarded-For'].split(',')[0]
    g.client_ip = client_ip
    
    if _path in url_white or _method in url_white_map.get(_path, []) or url_notify_white in _path or '/v1/datasets' in _path:
        pass
    else:
        if not g.corpid or g.corpid == 'null':
            raise ValueError(StatusMap['corp_miss'])


def request_data():
    request_params = dict()
    try:
        content_type = request.content_type
        if request.method == 'GET':
            req_data = request.args
        else:
            if 'json' in content_type and request.json:
                req_data = request.json
            else:
                req_data = request.form
        for key in req_data:

            # 参数白名单检测
            # if request.path not in url_params_white:
            #     try:
            #         v = str(req_data[key]).lower()
            #         if re.search(pattern, v):
            #             return ValueError(StatusMap['parameters_abnormal'])
            #     except:
            #         pass
            request_params[key] = req_data[key]

            if key in ['corp_id', 'corpid']:
                analyse_corp(req_data[key])
    except Exception as err:
        print(err)
        pass
    request.pmsd = request_params
    return request_params


def analyse_headers():
    headers = request.headers
    g.sale_id = headers.get("sale_id")
    g.org_id = headers.get("org_id")

    g.version_no = headers.get('version')
    g.ip = analyse_ip()

    corpid_and_suiteid = headers.get("corp-id")
    analyse_corp(corpid_and_suiteid)


def analyse_corp(corpid_and_suiteid):
    # 解析出不同应用的编号 和 组织ID
    if corpid_and_suiteid:
        data_list = [x for x in corpid_and_suiteid.split('$$') if x]
        corp_id = data_list[0]
        # 兼容自建应用旧数据 二维码链接
        if corp_id == 'content':
            # 自建应用的旧数据
            corp_id = 'wwf556ee1bcfa5d9d6'
        if len(data_list) > 1:
            suiteid = data_list[1]
        else:
            suiteid = None
    else:
        corp_id = suiteid = None
    # 组织编号信息 / 第三方代建应用的应用编号
    if hasattr(g, 'corpid'):
        g.corpid = g.corp_id = g.corpid or corp_id
    else:
        g.corpid = g.corp_id = corp_id
    if hasattr(g, 'suiteid'):
        g.suiteid = g.suiteid or suiteid
    else:
        g.suiteid = suiteid
    return corp_id, suiteid


def analyse_ip():
    try:
        ips = request.headers.get('X-Forwarded-For', '').replace('，', ',').replace(' ', '')
        ip_list = [ip for ip in ips.split(',') if ip]
        if ip_list:
            return ip_list[0]
        else:
            return request.remote_addr
    except:
        return ''


def json_response(data={}, code="SUCCESS", message='成功', status_code=200, err_msg=''):
    """
    将视图函数返回的数据转换成response对象
    :param code: 消息状态码
    :param status_code: http状态码
    :param message: 状态信息
    :param data: 数据
    :return:
    """
    if code == "SUCCESS":
        code = 0
    elif code == "FAIL":
        code = 1
    if code != 0 and message == '成功':
        message = "失败"
    if err_msg:
        message = err_msg
    result = {"code": code, "timestamp": int(time.time()), "message": message, "data": data, "req_id": str(uuid.uuid4().hex)}
    response = make_response(json.dumps(result, ensure_ascii=False, cls=CJsonEncoder), status_code)
    response.content_type = 'application/json; charset=utf-8'
    return response


class CJsonEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(obj, date):
            return obj.strftime("%Y-%m-%d")
        elif isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, np.int32) or isinstance(obj, np.int64):
            return int(obj)
        elif isinstance(obj, ObjectId):
            return str(obj)
        else:
            return json.JSONEncoder.default(self, obj)
