# -*- coding:utf-8 -*-
import time
import datetime
from json import JSONDecodeError

import requests

from lib_func.logger import logger
from lib_func.exception import ChatGptError, OceanApiError, RpaPromptError, ManagePromptError, BaiduApiError


def retry_request(tries=3, jitter=0, default_json={"code": 1, "message": "请求异常"}, throw_exception=True):
    """
    最外层装饰器，接收传递的参数
    :param tries: 重试次数
    :param jitter: 间隔
    :param default_json: 默认返回值
    :param throw_exception: 默认返回值
    :return:
    """
    def http_request(fn):
        """

        :param fn:
        :return:
        """
        def log(self, *args, **kwargs):
            error = None
            start_time = datetime.datetime.now()
            for i in range(1 + tries):
                try:
                    data = fn(self, *args, **kwargs)
                    end_time = datetime.datetime.now()
                    dl = (end_time - start_time)
                    ms = int(dl.seconds * 1000) + int((dl.microseconds / 1000))
                    logger.info('{}，{}，retry：{}'.format(fn.__name__, ms, i))
                    return data
                except requests.exceptions.Timeout as e:
                    error = e
                    e.message = '接口请求超时'
                    logger.exception(e)
                except requests.exceptions.ConnectionError as e:
                    error = e
                    e.message = '连接异常'
                    logger.exception(e)
                except JSONDecodeError as e:
                    error = e
                    e.message = 'json解析异常'
                    logger.exception(e)
                except RpaPromptError as e:
                    error = e
                    logger.exception(e)
                except OceanApiError as e:
                    error = e
                    logger.exception(e)
                except ChatGptError as e:
                    error = e
                    logger.exception(e)
                except BaiduApiError as e:
                    error = e
                    logger.exception(e)
                except ManagePromptError as e:
                    error = e
                    logger.exception(e)
                except Exception as e:
                    error = e
                    error.message = '未知异常'+str(e)
                    logger.exception(e)
                if jitter:
                    time.sleep(jitter)
            end_time = datetime.datetime.now()
            dl = (end_time - start_time)
            ms = int(dl.seconds * 1000) + int((dl.microseconds / 1000))
            logger.info('{}，{}，error：{}'.format(fn.__name__, ms, error))
            if throw_exception and error:
                raise error
            else:
                default_json['message'] = str(error)
                return default_json
        return log
    return http_request
