import functools
import json
import decimal
import numpy as np
from flask import g
from datetime import datetime, date
from utils.tools import md5
from puppet.cache import redis_pool


def cache_ret(cache_key, _index=None, expire_time=120, _key=None):
    def inner(func):
        @functools.wraps(func)
        def inner_inner(*args, **kwargs):
            # return func(*args, **kwargs)
            redis_client = redis_pool.use()
            index_data = ''
            if _index is not None:
                if type(_index) is int:
                    local_index = _index + 1
                    # 无参数作为cache_key的组成部分
                    if local_index >= 0:
                        if local_index > len(args) - 1:
                            raise ValueError('超出参数可选范围')
                        index_data = args[local_index]
                        if type(index_data) in [dict, list, bytes, str, int]:
                            pass
                        elif type(index_data) in [datetime, date]:
                            index_data = str(index_data)
                        else:
                            index_data = ''
                elif type(_index) is list:
                    index_data = [args[int(i) + 1] for i in _index]
                else:
                    raise ValueError('_index参数类型非法')
            elif _key is not None:
                if type(_key) is list:
                    index_data = [kwargs.get(i) for i in _key]
                elif type(_key) in [int, str]:
                    index_data = kwargs.get(_key)
                if not index_data:
                    raise ValueError('超出参数可选范围 {}'.format(_key))
            else:
                if len(args) or len(kwargs):
                    raise ValueError('缺少缓存数据唯一标识')
                else:
                    index_data = ''
            if type(index_data) in [dict, list]:
                index_data = md5(json.dumps(index_data, cls=CJsonEncoder))
            elif type(index_data) == bytes:
                index_data = index_data.decode()
            elif type(index_data) in [str, int]:
                index_data = str(index_data)
            elif index_data is None:
                index_data = ''
            else:
                raise ValueError('数据类型不允许序列化')
            
            if index_data:
                cache_key1 = index_data + "_" + cache_key + "_{}_{}".format(*analyse_corp_sale_id())
            else:
                cache_key1 = cache_key + "_{}_{}".format(*analyse_corp_sale_id())
            
            # 先判定是否存强制刷新标识 is_up
            is_up_check = kwargs.get('is_up')
            cache_data = None if is_up_check else redis_client.get(cache_key1)
            if not cache_data:
                ret = func(*args, **kwargs)
                # 刷新缓存
                if ret:
                    redis_client.setex(cache_key1, expire_time, json.dumps(ret, cls=CJsonEncoder))
                return ret
            else:
                return json.loads(cache_data)
        
        return inner_inner
    
    return inner


def reproduction_check(_index):
    def inner(func):
        @functools.wraps(func)
        def inner_inner(*args, **kwargs):
            redis_client = redis_pool.use()
            index_data = args[_index]
            if type(index_data) in [dict, list]:
                index_data = md5(json.dumps(index_data, cls=CJsonEncoder))
            elif type(index_data) == bytes:
                index_data = index_data.decode()
            elif type(index_data) == str:
                pass
            else:
                return func(*args, **kwargs)
            
            cache_key1 = index_data + "_{}_{}".format(*analyse_corp_sale_id())
            cache_data = redis_client.get(cache_key1)
            if not cache_data:
                ret = func(*args, **kwargs)
                if ret:
                    # 刷新缓存
                    redis_client.setex(cache_key1, 10, '1')
                return ret
            else:
                raise ValueError('请勿重复操作')
        
        return inner_inner
    
    return inner


def analyse_corp_sale_id():
    try:
        if hasattr(g, 'corpid'):
            corpid = g.corpid
        else:
            corpid = ''
        if hasattr(g, 'sale_id'):
            sale_id = g.sale_id
        else:
            sale_id = ''
    except:
        corpid = sale_id = ''
    return corpid, sale_id


def redis_is_lock(lock_key, expire=10):
    _redis = redis_pool.use()
    if not _redis.setnx(lock_key, '1'):
        return True
    else:
        _redis.expire(lock_key, expire)
        return False


class CJsonEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(obj, date):
            return obj.strftime('%Y-%m-%d')
        elif isinstance(obj, (np.integer, np.floating, np.bool_)):
            return obj.item()
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, decimal.Decimal):
            return float(obj)
        return json.JSONEncoder.default(self, obj)
