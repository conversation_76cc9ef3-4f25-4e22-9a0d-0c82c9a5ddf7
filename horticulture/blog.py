import json
from functools import wraps
from flask import request, g
from modeler.mysql.user_orm import UserLogM


def action_log(func) -> object:
    @wraps(func)
    def decorator(*args, **kwargs):
        response_data_obj = func(*args, **kwargs)
        response_data = response_data_obj.json
        try:
            view_code = request.headers.get('view_code') or 'content_manage'
            log_info, user_id = get_log_info(response_data['data'])
            UserLogM(g.corpid).add_user_do_log(view_code, log_info, user_id)
        except:
            pass
        return response_data

    return decorator


def get_log_info(_data):
    method = request.method
    url = request.path
    params = request.pmsd

    if url in ['/login', '/third_login', '/agent_login']:
        user_name = _data.get('tp_user_name')
        tp_user_id = _data.get('tp_user_id')
        url = '/login'
    else:
        if hasattr(request, 'user'):
            user_name = request.user['tp_user_name']
            tp_user_id = request.user['tp_user_id']
        else:
            tp_user_id = 0
            user_name = '游客'
    info = {
        '/login': {
            'POST': '登录账号',
        },
        '/logout': {
            'POST': '退出账号',
        },
        '/content/info': {
            'POST': '修改素材',
            'PUT': '创建素材',
            'DELETE': '删除素材',
            'Information': (lambda: '素材名称:{} '.format(_data.get('content_name')))
        },
        '/content/auth/scheme': {
            'PUT': '创建权限方案',
            'Information': (lambda: '权限方案名称:{}'.format(_data.get('auth_scheme_name')))
        },
        '/content/rel/tag': {
            'POST': '修改素材关联标签',
            'PUT': '创建素材关联标签',
            'DELETE': '删除素材关联标签',
            'Information': (lambda: '素材名称:{} 涉及标签数量/名称:{}'
                            .format(_data.get('content_name'), _data.get('tag_name') or len(_data.get('tag_ids', []))))
        },
        '/content/bulk/do': {
            'POST': '素材批量上架',
            'DELETE': '批量删除素材',
            'Information': (lambda: '操作数量:{}'.format(len(params.get('content_ids', '').split(','))))
        },
        '/content/bulk/publish': {
            'POST': '批量发布素材',
            'PUT': '批量审核素材',
            'Information': (
                lambda: '操作数量：{}'.format(len(params.get('content_id', params.get('content_ids', '')).split(','))))
        },
        '/content/scheme/handle': {
            'POST': '批量修改素材权限方案',
            'Information': (lambda: '权限名称:{} 涉及数量:{}'.format(_data, len(params.get('content_ids').split(','))))
        },
        '/config/publish': {
            'PUT': '新增/修改 素材发布位置',
            'DELETE': '删除素材发布位置',
            'Information': (lambda: '发布位置名称:{}'.format(_data.get('pos_name')))
        },
        '/config/content/type': {
            'PUT': '新增/修改 素材类型',
            'DELETE': '删除素材类型',
            'Information': (lambda: '素材类型名称:{}'.format(_data.get('type_name')))
        },
        '/config/tagsgroup': {
            'POST': '修改标签组',
            'PUT': '新增标签组',
            'DELETE': '删除标签组',
            'Information': (lambda: '素材组名称:{}'.format(_data.get('group_name')))
        },
        '/config/tags': {
            'POST': '修改标签',
            'PUT': '创建标签',
            'DELETE': '删除标签',
            'Information': (lambda: '标签名称:{}'.format(_data.get('tag_name')))
        },
        '/config/tpuser': {
            'PUT': '创建或变更用户角色',
            'DELETE': '删除用户角色',
            'Information': (lambda: '涉及角色数量:{} 用户名:{}'
                            .format(_data.get('role_count') or len(_data.get('role_name', '').split(',')),
                                    _data.get('user_name')))
        },
        '/config/role': {
            'PUT': '变更角色权限',
            'Information': (lambda: '角色名称:{} 操作权限数量:{}'
                            .format(params.get('role_name'), len(json.loads(params.get('auth_list', '')))))
        },
        '/config/role/manage': {
            'POST': '修改角色权限配置',
            'PUT': '创建角色权限配置',
            'DELETE': '删除角色权限配置',
            'Information': (lambda: '角色名称:{} 涉及人员数量:{} 涉及页面数量:{}'
                            .format(params.get('role_name', _data.get('role_name')),
                                    len(params.get('user_ids', '').split(',')) or _data.get('users', 0),
                                    len(params.get('view_ids', '').split(',')) or _data.get('views', 0)))
        },
        '/config/ctt_group/tag': {
            'POST': '修改素材标签组',
            'PUT': '创建素材标签组',
            'DELETE': '删除素材标签组',
            'Information': (lambda: '标签组名:{}'.format(params.get('group_name', _data)))
        },
        '/config/content/tag': {
            'POST': '修改素材标签',
            'PUT': '创建素材标签',
            'DELETE': '删除素材标签',
            'Information': (lambda: '标签名:{}'.format(params.get('tag_name', _data)))
        },
        '/config/authscheme': {
            'PUT': '创建/修改 素材权限方案',
            'DELETE': '删除素材权限方案',
            'Information': (lambda: '方案名称:{} 涉及人员数量:{} 角色数量:{} 部门数量:{}'
                            .format(params.get('scheme_name') or _data.get('scheme_name'),
                                    len(params.get('user_ids', '').split(',')) or _data.get('user_count', 0),
                                    len(params.get('role_ids', '').split(',')) or _data.get('role_count', 0),
                                    len(params.get('dpt_ids', '').split(',')) or _data.get('dpt_count', 0)))
        },
        '/content/sender': {
            'PUT': '发送素材',
            'Information': (
                lambda: '素材名称:{} 客户名称:{}'.format(_data.get('content_name'), _data.get('client_name')))
        },
        '/fission/plan': {
            'POST': '创建裂变策划计划',
            'PUT': '修改裂变策划计划',
            'DELETE': '删除裂变策划计划',
            'Information': (lambda: '策划计划名称:{}'.format(_data.get('fp_name')))
        },
        '/contentgroup/handle': {
            'POST': '修改素材组',
            'PUT': '创建素材组',
            'DELETE': '删除素材组',
            'Information': (lambda: '素材组名称:{}'.format(_data.get('group_name')))
        },
        '/channel/group': {
            'POST': '创建渠道组',
            'PUT': '修改渠道组',
            'DELETE': '删除渠道组',
            'Information': (lambda: '渠道组名称:{}'.format(_data.get('group_name')))
        },
        '/channel/handle': {
            'POST': '创建渠道',
            'PUT': '修改渠道',
            'DELETE': '删除渠道',
            'Information': (lambda: '渠道名称:{}'.format(_data.get('channel_name')))
        },
        '/content/offshelf': {
            'POST': '素材下架',
            'Information': (lambda: '素材名称:{}'.format(_data.get('content_name')))
        },
        '/content/label': {
            'POST': '变更关键字',
            'Information': (lambda: '素材名称:{} 关键字:{}'.format(_data, params.get('key_words', '')))
        },
        '/config/organization': {
            'PUT': '修改组织信息',
            'Information': (lambda: '组织名称:{}'.format(_data.get('org_name')))
        },
        '/clue/forward': {
            'POST': '转交了线索',
            'Information': (lambda: '将线索:[{}] 转交给:[{}]'.format(_data.get('clue_name'), _data.get('forward_user')))
        },
        '/fission/template': {
            'POST': '创建了裂变模板',
            'PUT': '修改了裂变模板',
            'DELETE': '删除了裂变模板',
            'Information': (lambda: '模板名称:{}'.format(_data.get('template_name')))
        },
        '/fission/template/startandstop': {
            'PUT': '修改了裂变模板',
            'Information': (lambda: '{}了模版  模板名称:{}'.format(_data.get('type'), _data.get('template_name')))
        },
        '/fission/mission/info': {
            'POST': '修改且执行策划任务',
            'Information': (lambda: '编号:{} 客户数量:{} 传播方式:{} 数量/名称:{}'
                            .format(_data['fm_code'], _data['clients'], '素材', _data['contents']
            if _data['flag'] == '0' else '雷达', _data['radar_title']))
        },
        '/fission/planning/info': {
            'PUT': '修改/新建策划',
            'Information': (lambda: '名称:{} 类型:{} 客户数量:{}'
                            .format(params['fp_name'], '素材 {}个'.format(len(params['content_id'].split(',')))
            if params['use_radar_flag'] == '0' else '雷达', len(params['client_ids'].split(','))))
        },
        '/clue/maturity': {
            'PUT': '调整线索成熟度',
            'Information': (lambda: '线索名称:{} 成熟度:{} 成熟阶段:{}'.format(_data['client_name'],
                                                                               params['clue_maturity'],
                                                                               _data['stage_name']))
        },
        '/newclue/relation': {
            'POST': '新建/修改线索关联',
            'Information': (
                lambda: '新线索名称:{} 被关联客户:{}'.format(_data['clue_client_name'], _data['rel_client_name']))
        },
        '/newclue/export': {
            'POST': '导出新线索',
            'Information': (lambda: '文件链接:{}'.format(_data['excel_oss_url']))
        },
        '/client/tag/rel': {
            'PUT': '关联了客户标签',
            'DELETE': '取消了客户标签关联',
            'Information': (lambda: '客户名称:{}, 标签名称:{}'.format(_data.get('client_name'), _data.get('tag_name')))
        },
        '/client/radar': {
            'POST': '修改/创建雷达',
            'PUT': '创建雷达',
            'DELETE': '删除雷达',
            'Information': (lambda: '名称:{}'.format(params.get('radar_title') or _data.get('radar_title')))
        },
        '/config/access': {
            'POST': '新建触达方式',
            'PUT': '修改触达方式',
            'DELETE': '删除触达方式',
            'Information': (lambda: '名称:{}'.format(_data.get('tm_type_name')))
        },
        '/config/recovery': {
            'PUT': '恢复了',
            'Information': (lambda: '{} 名称:{}'.format(_data.get('type'), _data.get('name')))
        },
        '/fission/start': {
            'PUT': '启用了策划',
            'Information': (lambda: '策划名称:{}'.format(_data.get('fp_name')))
        },
        '/fission/stop': {
            'PUT': '停用了策划',
            'Information': (lambda: '策划名称:{}'.format(_data.get('fp_name')))
        },
        '/buy/seat': {
            'POST': '购买了席位',
            'Information': (lambda: '购买数量:{}'.format(_data.get('seat_num')))
        },
        '/distribute/seat': {
            'PUT': '分配了席位',
            'Information': (lambda: '{}了{}的席位'.format(_data.get('seat_operate'), _data.get('tp_user_name')))
        },
        '/media/channel': {
            'POST': '新增了媒体渠道',
            'PUT': '修改了媒体渠道',
            'DELETE': '删除了媒体渠道',
            'Information': (lambda: '媒体渠道名称:{}'.format(_data.get('media_channel_name')))
        },
        '/landpage': {
            'POST': '新增了落地页',
            'PUT': '修改了落地页',
            'DELETE': '删除了落地页',
            'Information': (lambda: '落地页名称:{}'.format(_data.get('landpage_name')))
        },
        '/upload/admission/data': {
            'POST': '导入了任务投放数据',
            'Information': (lambda: '任务名称{}，导入数量:{}'.format(_data.get('ad_mission_name'), _data.get('count')))
        },
        '/admission/cost': {
            'POST': '修改了任务总体投放数据',
            'PUT': '修改了任务投放数据',
            'DELETE': '删除了任务投放数据',
            'Information': (lambda: '任务名称{}'.format(_data.get('ad_mission_name')))
        },
        '/admission/landpage/rel': {
            'PUT': '关联了落地页',
            'DELETE': '取消了落地页关联',
            'Information': (lambda: '任务名称:{} 涉及落地页数量:{}'
                            .format(_data.get('ad_mission_name'), _data.get('count')))
        },
        '/bonus/tpu': {
            'POST': '新建触点激励方式',
            'Information': (lambda: '名称:{}'.format(_data.get('bonus_tpu_name')))
        },
        '/bonus/client': {
            'POST': '新建客户激励方式',
            'Information': (lambda: '名称:{}'.format(_data.get('bonus_client_name')))
        },
        '/fission/template/type': {
            'POST': '新建裂变模版类型',
            'Information': (lambda: '名称:{}'.format(_data.get('tfp_type_name')))
        },
        '/targeting/type': {
            'POST': '新建客户定向类型',
            'Information': (lambda: '名称:{}'.format(_data.get('targeting_type_name')))
        },
        '/marketing': {
            'POST': '新建自动营销',
            'PUT': '编辑自动营销',
            'Information': (lambda: '名称:{},ID:{}'.format(_data.get('mkt_name'), _data.get('mkt_id')))
        }
    }
    action_info = info.get(url, {})
    InformationFunc = action_info.get('Information')
    method_info = action_info.get(method, '')
    format_msg = '{} {} {}'.format(user_name, method_info,
                                   InformationFunc() if (InformationFunc and method_info) else '')
    return format_msg, tp_user_id
