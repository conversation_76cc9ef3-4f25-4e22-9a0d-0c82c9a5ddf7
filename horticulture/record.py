import json
import functools
from flask import request, g
from horticulture.validate import request_data
from utils.tools import get_now_time, join_params
from lib_func import logger


"""    
a.当前装饰器作用
    1. 服务组件细节log记录 （包括info|warning|error）
    2. 服务组件异常捕获，并统一范围错误码 None

"""

Base_Ret = None
Log_Level_Info = 'info'
Log_Level_Warning = 'warning'
Log_Level_Error = 'error'


# 记录日志操作
def log_record(level, func_name, params_str, time_long, log_name, err_msg=''):
    try:
        msg = logger.format_sql_content(func_name, params_str, time_long, err_msg)
        if level == Log_Level_Info:
            logger.info(msg, log_name)
        elif level == Log_Level_Warning:
            logger.warning(msg, log_name)
        else:
            logger.error(msg, log_name)
    except Exception as err:
        print(err)
        pass


# sql语句行为记录装饰器
def sql_log(func):
    @functools.wraps(func)
    def inner_sl(*args, **kwargs):
        start = get_now_time(True)
        err_msg = ''
        level = Log_Level_Info
        func_name = func.__qualname__
        try:
            ret = func(*args, **kwargs)
        except Exception as err:
            level = Log_Level_Error
            if hasattr(err, 'args'):
                err_msg = json.dumps(err.args)
            elif hasattr(err, 'description'):
                err_msg = err.description
            else:
                err_msg = 'sql action error'
            ret = Base_Ret
        end = get_now_time(True)
        time_long = end - start
        if err_msg:
            log_name = 'sql_err'
        else:
            log_name = 'sql_log'
        if time_long >= 500 and level == Log_Level_Info:
            level = Log_Level_Warning
        params_str = join_params(*args, **kwargs)
        log_record(level, func_name, params_str, time_long, log_name, err_msg)
        return ret
    
    return inner_sl


# API行为记录
def api_log_record(resp):
    filter_pass_list = ['Path', 'Connection', 'Host', 'Cookie', 'Accept-Encoding', 'Accept', 'Sec-Fetch-Site',
                        'Sec-Fetch-Mode', 'Sec-Fetch-Dest', 'Content-Type', 'Light-Token']
    chose_key_list = ['X-Real-Ip', 'X-Real-Port', 'X-Forwarded-For', 'Timestamp', 'Nonce', 'Pname', 'Version',
                      'User-Agent', 'Content-Length', 'Version-Code']
    __param_keys = []
    __result_keys = []
    try:
        resp_data = resp.data
        path = request.path
        method = request.method
        header = request.headers
        if hasattr(request, 'user'):
            uid = request.user.get('tp_user_id', 0)
        else:
            uid = 0
        log_header = dict()
        for key, value in header.items():
            if key in chose_key_list:
                # continue
                log_header[key] = value
        header = json.dumps(log_header)
        ret = json.loads(resp_data)
        try:
            ret['message'] = ret.get('message')
            code = ret.get('code', 0)
        except:
            if not ret:
                code = 101
            else:
                code = 0
            ret = {
                'code': code,
                'data': ret,
                'msg': '',
                'message': ''
            }
        file_name = 'api_info'
        try:
            corpid = g.corpid
            if corpid:
                file_name += '_{}'.format(corpid)
        except:
            corpid = None

        time_long = get_now_time(True) - request.start
        if hasattr(request, 'schema_data'):
            req_data = request.schema_data
        else:
            req_data = request_data()
        msg = logger.format_log_content(req_data, ret, path, method, time_long, __param_keys, __result_keys, header,
                                        uid)

        # 响应返回值，单独记录
        logger.info({'req_id': msg.get('req_id'), 'resp': msg['ret'].pop('result')}, 'api_resp')

        if 0 == code:
            logger.info(msg, file_name)
        else:
            file_name = 'api_err'
            if corpid:
                file_name += '_{}'.format(corpid)
            logger.error(msg, file_name)
    except Exception as err:
        print(err)
        pass


# 第三方行为记录装饰器
def third_req_log(func):
    @functools.wraps(func)
    def inner_it_trl(*args, **kwargs):
        start = get_now_time(True)
        func_name = func.__qualname__
        level = Log_Level_Info
        err_msg = ''
        try:
            ret = func(*args, **kwargs)
        except Exception as err:
            if hasattr(err, 'args'):
                err_msg = json.dumps(err.args)
            elif hasattr(err, 'description'):
                err_msg = err.description
            else:
                err_msg = 'third action error'
            ret = Base_Ret
            level = Log_Level_Error
        end = get_now_time(True)
        time_long = end - start
        if err_msg:
            log_name = 'third_err'
        else:
            log_name = 'third_log'
        if time_long >= 5000 and level == Log_Level_Info:
            level = Log_Level_Warning
        params_str = join_params(*args, **kwargs)
        log_record(level, func_name, params_str, time_long, log_name, err_msg)
        return ret
    
    return inner_it_trl


# redis error 行为记录装饰器 (secret=True 代表零容忍异常，否则代表可容忍短暂异常)
def redis_error_log(secret=False):
    def inner_rel(func):
        @functools.wraps(func)
        def inner_ir_rel(*args, **kwargs):
            start = get_now_time(True)
            func_name = func.__qualname__
            level = Log_Level_Info
            err_msg = ''
            try:
                ret = func(*args, **kwargs)
            except Exception as err:
                if hasattr(err, 'args'):
                    err_msg = json.dumps(err.args)
                elif hasattr(err, 'description'):
                    err_msg = err.description
                else:
                    err_msg = 'redis action error'
                ret = Base_Ret
                if secret:
                    level = Log_Level_Error
                else:
                    level = Log_Level_Warning
            end = get_now_time(True)
            time_long = end - start
            if level == Log_Level_Error:
                log_name = 'redis_err'
            else:
                log_name = 'redis_log'
            if time_long >= 50 and level == Log_Level_Info:
                level = Log_Level_Warning
            params_str = join_params(*args, **kwargs)
            log_record(level, func_name, params_str, time_long, log_name, err_msg)
            return ret
        
        return inner_ir_rel
    
    return inner_rel
