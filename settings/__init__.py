# 通过环境变量DEPLOY判断读取的配置文件
# linux 下 在个人目录中的 .bash_profile设置环境变量
import os
import sys

project_root = os.path.dirname(os.path.abspath(__name__))
sys.path.append(project_root)

import platform

ENVIRONMENT = os.environ.get('ENVIRONMENT', 'LOCAL' if 'linux' not in platform.platform() else None)
# ENVIRONMENT = 'RELEASE'
SECRET_KEY_FOR_OS="5e2e8a586024f272b97e752e9d8f2885"


if ENVIRONMENT is None:
    import argparse

    parser = argparse.ArgumentParser(description='manual to this script')
    parser.add_argument('--ENVIRONMENT', type=str, default=None)
    args = parser.parse_args()
    ENVIRONMENT = args.ENVIRONMENT
if ENVIRONMENT is None:
    ENVIRONMENT = 'LOCAL'

#################### str_to_bool ####################
def str_to_bool(s):
    s = s.strip().lower()
    if s == 'true':
        return True
    elif s == 'false':
        return False
    else:
        # 可以根据需求选择抛出异常或者返回默认值
        raise ValueError(f"Invalid string value '{s}' for boolean conversion.")
    
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
OSS_DIR = '/mnt/oss/'

# 系统秘钥
SECRET_KEY = b'/\xf4-%\x96\x99X\x89\x84\xa5%\x0c\xa2\xa4\xbbG\xd5\xd7\xe7j\xe9\xab2\x97s\x82!\x9c4\xc5\x8bj'
# Operate
OPERATE_SECRET_KEY = b'\x7f\x93\xce\xbd\xa1\xe7\xab\t\xe8\x12\x95\xff\xd1MED\xe0j\x86\x1c\x8a\xd4\xb5z7"%)m\x0c\xf4\x92'

GTApiKey = 'zhongshuruizhi'

# 3DES 加密
DES_KEY = "f65120d1be85537ddaadc2ee68a67dc7ecaa6d5fa55f8b41"  # 24 字节密钥（48 字符十六进制）
DES_IV = "c33f68f50abeec07"  # 8 字节 IV（16 字符十六进制）

VERSION = 'v1.0.0'
print('version', VERSION)

if ENVIRONMENT == 'PROD':
    print('======prod======')
    print(f'======BASE_DIR: {BASE_DIR}======')
    from settings.prod import *
elif ENVIRONMENT == 'RELEASE':
    print('=======release=======')
    print(f'======BASE_DIR: {BASE_DIR}======')
    from settings.release import *
elif ENVIRONMENT == 'TEST':
    print('=======test=======')
    print(f'======BASE_DIR: {BASE_DIR}======')
    from settings.test import *
else:
    print('=======local=======')
    print(f'======BASE_DIR: {BASE_DIR}======')
    from settings.test import *



#################### 服务启动配置 ####################
API_HOST = os.getenv('API_HOST',"127.0.0.1")
API_PORT = os.getenv('API_PORT',7100)
API_DEBUG = str_to_bool(os.getenv('API_DEBUG','False'))  # 调试模式，即修改代码后立马响应，无需重启服务，但是不能用于生产环境
LOGGER_LEVEL = os.getenv('LOGGER_LEVEL', 'INFO')  # 日志级别
if API_DEBUG:
    from dotenv import load_dotenv
    load_dotenv()

DEFAULT_LOCAL_EMB_MODEL_PATH = os.getenv('DEFAULT_LOCAL_EMB_MODEL_PATH', 'bge-large-zh-v1.5')
DEFAULT_MODEL_PATH = os.getenv('DEFAULT_MODEL_PATH', 'text-embedding-v1')

#################### 删除字段 ####################
DELETE_FLAG_TRUE = 1  # 软删除
DELETE_FLAG_FALSE = 0  # 正常
DELETE_FLAG_WAIT_DEL_VECTOR = 2  # 等待删除向量,目前仅应用于文件管理
