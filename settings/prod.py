import os

# 日志目录
LOGO_PATH = os.path.realpath(
    os.path.join(os.path.split(os.path.split(os.path.realpath(__file__))[0])[0], '')) + "\\logs\\"
# 项目目录
PROJECT_PATH = os.path.realpath(os.path.join(os.path.split(os.path.split(os.path.realpath(__file__))[0])[0], ''))
# 训练模型导出tar目录
MODEL_EXPORT_TAR_DIR = os.getenv("MODEL_EXPORT_TAR_DIR", "/media/yons/data/files/")
# 模型文件目录
MODEL_EXPORT_DIR = os.getenv("MODEL_EXPORT_DIR", "/media/yons/data/models/")
# 模型下载
MODEL_DOWNLOAD_URL = os.getenv("MODEL_DOWNLOAD_URL", "http://**************:30080/data/")

# llama-factory env
LLAMA_FACTORY_ENV = os.getenv("LLAMA_FACTORY_ENV", "/home/<USER>/anaconda3/bin:")

# 重排序模型
RANK_MODEL_URL = os.getenv('RANK_MODEL_URL', 'http://**************:6300/api/v1/rerank/create')

# mysql
SQLConf = {
    'mysql': {
        "host": "**************",
        'port': 23308,
        'user': 'root',
        'password': 'zsrz2024wfn031641',
        'database': 'master',
        'charset': 'utf8mb4',
    }
}

# mq
RABBITMQ = {
    'user': 'root',
    'password': 'zsrz2024wfn031641',
    'host': '**************',
    'port': 5672,
    'v-host': '/dev',
    'exchange': {
        'client_portrait': 'mongodb',
        'client_care': 'mongodb',
        'mongo': 'mongodb',
        'aigc': 'aigc',
        'stream': 'stream',
    },
    'queue': {
        'mongo': 'mongodb',
        'aigc': 'aigc',
        'stream': 'stream',
        'client_portrait': 'client_portrait',
        'client_care': 'client_care'
    },
    'routing_key':
        {
            'mongo': 'mongodb_key',
            'aigc': 'aigc_key',
            'stream': 'stream_key',
            'client_portrait': 'portrait_key',
            'client_care': 'care_key'
        }
}

# mq
AIGC_RABBITMQ = {
    'user': 'root',
    'password': 'wfn031641',
    'host': '**************',
    'port': 5672,
    'v-host': '/prod',
    'exchange': {
        'aigc': 'aigc',
        'aigc_chatglm': 'aigc',
        'aigc_llama': 'aigc'
    },
    'queue': {
        'aigc': 'aigc',
        'aigc_chatglm': 'aigc_chatglm',
        'aigc_llama': 'aigc_llama'
    },
    'routing_key':
        {
            'aigc': 'aigc_key',
            'aigc_chatglm': 'aigc_chatglm',
            'aigc_llama': 'aigc_llama'
        }
}

# token存储
INS_TOKEN_REDIS = {
    'host': '**************',
    'port': 36381,
    'password': 'zsrz2024wfn031641',
    'db': 5
}
# 权限 相关
INS_ROLE_REDIS = {
    'host': '**************',
    'port': 36381,
    'password': 'zsrz2024wfn031641',
    'db': 6
}
# 字典 相关
INS_DICT_REDIS = {
    'host': '**************',
    'port': 36381,
    'password': 'zsrz2024wfn031641',
    'db': 7
}
# 客户信息缓存
INS_CLIENT_REDIS = {
    'host': '**************',
    'port': 36381,
    'password': 'zsrz2024wfn031641',
    'db': 8
}
# AIGC缓存
INS_AIGC_REDIS = {
    'host': '**************',
    'port': 6379,
    'password': 'wfn031641',
    'db': 12
}

JusureT3Api = 'http://**************:5000/api/prod'
JusureT3Url = 'http://**************:5000/api'
UrlEnv = '/prod'
JUSURE_EMBEDDINGS_API = os.getenv('JUSURE_EMBEDDINGS_API', 'http://**************:5205/')
JUSURE_MULTIMODAL_IMAGE_DESCRIPTION_V1= os.getenv('JUSURE_MULTIMODAL_IMAGE_DESCRIPTION_V1', 'http://**************:8082/image/description')

# 成功
SUCCESS = 'SUCCESS'
# 失败
FAILURE = 'FAILURE'
# 处理中
PENDING = 'PENDING'

SPLITDOC = 'SPLITDOC'
CLEARDOC = 'CLEARDOC'
CLEARCHUNK = 'CLEARCHUNK'
EMBEDDING = 'EMBEDDING'

######## skywalking配置 ########
SKYWALKING_CONFIG = {
    'enabled': eval(os.getenv('SKYWALKING_ENABLED', 'False')),  # 是否启用SkyWalking
    'collector_address': os.getenv('SKYWALKING_COLLECTOR_ADDRESS', '**************:11800'),  # SkyWalking Collector地址
    'service_name': os.getenv('SKYWALKING_SERVICE_NAME', 'jusure_ai_localhost'),  # 服务名称
    'log_level': os.getenv('SKYWALKING_LOG_LEVEL', 'INFO')  # 日志级别
} 