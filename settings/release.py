import os

# 日志目录
LOGO_PATH = os.path.realpath(
    os.path.join(os.path.split(os.path.split(os.path.realpath(__file__))[0])[0], '')) + "\\logs\\"
# 项目目录
PROJECT_PATH = os.path.realpath(os.path.join(os.path.split(os.path.split(os.path.realpath(__file__))[0])[0], ''))
# 训练模型导出tar目录
MODEL_EXPORT_TAR_DIR = os.getenv("MODEL_EXPORT_TAR_DIR", "/media/yons/data/files/")
# 模型文件目录
MODEL_EXPORT_DIR = os.getenv("MODEL_EXPORT_DIR", "/media/yons/data/models/")
# 模型下载
MODEL_DOWNLOAD_URL = os.getenv("MODEL_DOWNLOAD_URL", "http://**************:30080/data/")
# llama-factory env
LLAMA_FACTORY_ENV = os.getenv("LLAMA_FACTORY_ENV", "/home/<USER>/anaconda3/bin:")
# 重排序模型
RANK_MODEL_URL = os.getenv('RANK_MODEL_URL', 'http://**************:6300/api/v1/rerank/create')

#################### 数据库配置 ####################
MINIO_CONF = {
    # "endpoint": os.getenv('MINIO_ENDPOINT', '**************:9000'),
    "endpoint": os.getenv('MINIO_ENDPOINT', '**************:9000'),
    "access_key": os.getenv('MINIO_ACCESS_KEY', 'vlnO7uWSDGj92UEItkIX'),
    "secret_key": os.getenv('MINIO_SECRET_KEY', '94458uubN5x4CiuIxr6xn0Do0aym36lHSlqa0Hz9'),
    "replace_presigend_url": os.getenv('REPLACE_PRESIGNED_URL', '') # 海油将19presign地址转换为14
}


# mysql
SQLConf = {
    'mysql': {
        # "host": os.getenv('MASTER_MYSQL_HOST', '**************'),
        "host": os.getenv('MASTER_MYSQL_HOST', '**************'),
        'port': int(os.getenv('MASTER_MYSQL_PORT', '3306')),
        'user': os.getenv('MASTER_MYSQL_USER', 'root'),
        'password': os.getenv('MASTER_MYSQL_PWD', 'zsrz2024wfn031641'),
        'database': os.getenv('MASTER_MYSQL_DATABASE', 'master'),
        'charset': 'utf8mb4',
        'database_type': 'mysql',
        'schema': 'public',
    }
}

# mq
RABBITMQ = {
    'user': os.getenv('RABBITMQ_USER', 'root'),
    'password': os.getenv('RABBITMQ_PWD', 'zsrz2024wfn031641'),
    'host': os.getenv('RABBITMQ_HOST', '**************'),
    'port': int(os.getenv('RABBITMQ_PORT', '35675')),
    'v-host': '/dev',
    'exchange': {
        'client_portrait': 'mongodb',
        'client_care': 'mongodb',
        'mongo': 'mongodb',
        'aigc': 'aigc',
        'stream': 'stream',
    },
    'queue': {
        'mongo': 'mongodb',
        'aigc': 'aigc',
        'stream': 'stream',
        'client_portrait': 'client_portrait',
        'client_care': 'client_care'
    },
    'routing_key':
        {
            'mongo': 'mongodb_key',
            'aigc': 'aigc_key',
            'stream': 'stream_key',
            'client_portrait': 'portrait_key',
            'client_care': 'care_key'
        }
}

# mq
AIGC_RABBITMQ = {
    'user': 'root',
    'password': 'wfn031641',
    'host': '**************',
    'port': 5672,
    'v-host': '/prod',
    'exchange': {
        'aigc': 'aigc',
        'aigc_chatglm': 'aigc',
        'aigc_llama': 'aigc'
    },
    'queue': {
        'aigc': 'aigc',
        'aigc_chatglm': 'aigc_chatglm',
        'aigc_llama': 'aigc_llama'
    },
    'routing_key':
        {
            'aigc': 'aigc_key',
            'aigc_chatglm': 'aigc_chatglm',
            'aigc_llama': 'aigc_llama'
        }
}

# token存储
INS_TOKEN_REDIS = {
    'host': os.getenv('REDIS_HOST', '**************'),
    'port': int(os.getenv('REDIS_PORT', '6379')),
    'password': os.getenv('REDIS_PWD', 'zsrz2024wfn031641'),
    'db': 5
}
# 权限 相关
INS_ROLE_REDIS = {
    'host': os.getenv('REDIS_HOST', '**************'),
    'port': int(os.getenv('REDIS_PORT', '6379')),
    'password': os.getenv('REDIS_PWD', 'zsrz2024wfn031641'),
    'db': 6
}
# 字典 相关
INS_DICT_REDIS = {
    'host': os.getenv('REDIS_HOST', '**************'),
    'port': int(os.getenv('REDIS_PORT', '6379')),
    'password': os.getenv('REDIS_PWD', 'zsrz2024wfn031641'),
    'db': 7
}
# 客户信息缓存
INS_CLIENT_REDIS = {
    'host': os.getenv('REDIS_HOST', '**************'),
    'port': int(os.getenv('REDIS_PORT', '6379')),
    'password': os.getenv('REDIS_PWD', 'zsrz2024wfn031641'),
    'db': 8
}
# AIGC缓存
INS_AIGC_REDIS = {
    'host': os.getenv('REDIS_HOST', '**************'),
    'port': int(os.getenv('REDIS_PORT', '6379')),
    'password': os.getenv('REDIS_PWD', 'zsrz2024wfn031641'),
    'db': 12
}

# LLM缓存
INS_LLM_REDIS = {
    'host': os.getenv('REDIS_HOST', '**************'),
    'port': int(os.getenv('REDIS_PORT', '6379')),
    'password': os.getenv('REDIS_PWD', 'zsrz2024wfn031641'),
    'db': 6
}


# Graph连接
INS_GRAPH_CONF = {
    'host': os.getenv('NEBULA_HOSTS', '**************'),
    'port': int(os.getenv('NEBULA_PORT', 9669)),
    'username': os.getenv('NEBULA_USERNAME', 'root'),
    'password': os.getenv('NEBULA_PASSWORD', 'nebula'),
    'aigc_type_id': os.getenv('GRAPH_AIGC_TYPE_ID', 'tongyiqianwen'),
    'model_path': os.getenv('GRAPH_MODEL_PATH', 'qwen-max'),
}


# JusureT3Api = 'http://**************:5000/api/prod'
JusureT3Api = os.getenv('JUSURET3_API', 'http://**************:5000/api/prod')
JusureT3Url = os.getenv('JUSURET3_URL', 'https://knowledge.demo.zhongshuruizhi.com/api')
UrlEnv = '/prod'

#################### 向量相关配置 ####################

JUSURE_EMBEDDINGS_API = os.getenv('JUSURE_EMBEDDINGS_API', 'http://**************:6300/')
# is 910b embedding
IS_910B_EMBEDDINGS = int(os.getenv('IS_910B_EMBEDDINGS', 0))
JUSURE_910B_EMBEDDINGS_API = os.getenv('JUSURE_910B_EMBEDDINGS_API', 'http://**************:5205/')


#################### 图像识别功能  ####################
GRAPH_SERVER_URL = os.getenv('GRAPH_SERVER_URL', "http://**************:8082/image/description")


# 通用数科 demo
## test app id
ENABLE_TYSK_DEMO = int(os.getenv('ENABLE_TYSK_DEMO', 1))
TYSK_DEMO_APP_ID = int(os.getenv('TYSK_DEMO_APP_ID', '4926692760435560449'))

## 
CHUNK_ID_QUESTION_MAP = {
    # 1-5
    4942366274677215448: "原系统凭证号如何在通财云的凭证上体现",
    4942366274677215449: "新增资产卡片账务处理方式 新旧系统折旧差额的账务处理方式",
    4942366274677215450: "资产折旧答疑-模板参考并及时发布到所属的微信群，at相应产业对接的数科负责人、金蝶实施顾问",
    4942366274677215451: "折旧计提结果发现卡片默认的核算维度需着急调整，如，增加【成本中心】、【项目】，如何调整？",
    4942366274677215452: "实物卡片上选择什么【使用状态】可以不用计提折旧？",
    # 6-10
    4942366274677215453: "【资产反初始化】之后，资产卡片（实物卡片、财务卡片，资产融合卡片）都调整之后，如前期折旧计提之后，计提结果如何更新调整？",
    4942366274677215454: "【慎用】切了动态折旧，只有反初始化，再依据上述行的答疑指引操作，才能再切回来到【静态折旧】",
    4942366274677215455: "单张卡片是否需要计提折旧呢？",
    4942366274677215456: "哪些卡片选择了需要计提折旧（有绿色勾勾）的使用状态，本期折旧额，依旧是0，是为什么？",
    4942366274677215457: "【环球医疗专用】财务卡片变更折旧方法操作方式（注意：分两类财务卡片）",
    # 11-15
    4942366274677215458: "由已审核单据生成的凭证是否可以修改？",
    4942366274677215459: "【通财云】系统是否存在一个不影响现金流量项目的现金流量？",
    4942366274677215460: "如何在凭证列表中查看600-900号凭证",
    4942366274677215461: "”作废“状态凭证科目金额是否会写入科目余额表",
    4942366274677215462: "财务数据来自缓存，不是最新的数据，请清除缓存后再试",
    # 16-20
    4942366274677215463: "【出纳初始化】遇到银行账户界面出现重复的数据时，如何处理？",
    4942366274677215464: "【出纳初始化】时当银行账户在通财云系统中没有的时候",
    4942366274677215465: "【出纳收支处理】时，代发单的代发分录里，能填不同的业务场景和资金用途吗？",
    4942366274677215466: "【出纳收支处理】时，被动付款入账时的流水是谁提供的？拿什么与司库对账？",
    4942366274677215467: "【出纳查询、银企对账】通财云查询不到交易流水怎么处理？",
    # 21-25
    4942366274677215468: "是不是银行对账单同步司库的，司库有多少就同步多少，不限于整月",
    4942366274677215469: "【出纳银企对账】在【银企互联】-【账户查询】-【交易明细查询】界面有银行交易流水；在【出纳】-【查账对账】-【银行存款对账】左侧（银行对账单）没有数据/不全右侧（银行日记账）没有数据怎么解决？",
    4942366274677215470: "【出纳银企对账】手工对账一对多、多对一勾对的依据是什么？",
    4942366274677215471: "【出纳银企对账】对账标识码是如何生成的？是什么码？与司库的关系是什么？",
    4942366274677215472: "【出纳银企对账】余额调节表的作用是什么？",
    # 26-30
    4942366274677215473: "在进行银企对账时，银行对账单、银行日记账无数据？",
    4942366274677215474: "出纳银企对账，能不能实现银行日记账，凭证在没有过账的情况下，可以看到数据进行对账？",
    4942366274677215475: "因为流水数据较多，无法全部进行手工对账，如何实现自动对账",
    4942366274677215476: "【环球医疗】折旧凭证生成有部分会计科目没带出来。",
    4942366274677215477: "凭证模板生成失败：财务卡片没有设置成本中心，且部门属性为空",
    # 31-35
    4942366274677215478: "凭证模板生成失败：财务卡片设置成本中心后没有重新计提及分摊折旧。",
    4942366274677215479: "凭证模板生成失败：新增的资产类别没有配置在科目映射表中",
    4942366274677215480: "折旧表和折旧分摊表的折旧金额不一致。",
    4942366274677215481: "虚拟的现金流默认值999不区分流入和流出，通财云如何区分流入和流出?",
    4942366274677215482: "如何在科目余额表中联查到凭证",
    # 36-40
    4942366274677215483: "汇兑损益凭证如何出具？如果发现某个科目没有勾选期末调汇，本期如何手工做调汇凭证？",
    4942366274677215484: "科目余额反初始化后，修改往来科目的往来款项性质和客商信息后，科目余额无法结束初始化怎么处理。",
    4942366274677215485: "在凭证列表界面如何只看到自己做的凭证。",
    4942366274677215486: "银行对账单无数据或数量不全",
    4942366274677215487: "流水/对账单数据不对，需要在司库更新后重新在通财云获取的操作步骤",
    # 41-45
    4942366274677215488: "银行日记账无数据怎么处理？",
    4942366274677215489: "银行存款对账点击【自动对账】显示无自动对账结果的处理方法",
    4942366274677215490: "凭证上的到期日到底是什么意思？和业务日期的区别是什么？",
    4942366274677215491: "通财云二批上线及补充上线单位，会计账簿开账月份，选哪个月份",
    4942366274677215492: "资产变更单-本月做预计使用期间变动单，这个变动是当月生效还是下月生效",
    # 46-48
    4942366274677215493: "该怎么确认我有没有结账固定资产2022年10期呢？",
    4942366274677215494: "成本中心新增时候，引入结果报错：上级编码报错，如何解决",
    4942366274677215495: "日常资产新增是否可以使用资产卡片（即融合卡片）操作？如何操作？",
}


# MINIO
# http://**************:9000/knowledge-docs/皮卡丘.webp
# OSS_DOMAIN_NAME = os.getenv('OSS_DOMAIN_NAME', 'oss.ai.zhongshuruizhi.com')
OSS_DOMAIN_NAME = os.getenv('OSS_DOMAIN_NAME', '**************:9000')
KNOWLEDGE_OSS_BUCKET_NAME = os.getenv('KNOWLEDGE_OSS_BUCKET_NAME', 'guotou')
ENABLE_HTTPS = os.getenv('ENABLE_HTTPS','0') # 0 为http，1为https

# aliyun oss
IS_ALIYUN_OSS = int(os.getenv('IS_ALIYUN_OSS', '0'))

# pptx、pdf文件转图片并声称描述
JUSURE_MULTIMODAL_IMAGE_DESCRIPTION = os.getenv('JUSURE_MULTIMODAL_IMAGE_DESCRIPTION', 'http://**************:8082/extract')

#图片多模态
JUSURE_MULTIMODAL_IMAGE_DESCRIPTION_V1= os.getenv('JUSURE_MULTIMODAL_IMAGE_DESCRIPTION_V1', 'http://**************:8082/image/description')
# 文生图
TEXT_TO_IMG_MODEL_URL = os.getenv('TEXT_TO_IMG_MODEL_URL', 'http://**************:6300/api/v1/sd/create')
# 视频关键帧提取
VIDEO_KEY_FRAME_EXTRACT = os.getenv('VIDEO_KEY_FRAME_EXTRACT', 'http://**************:8082/video/keyframes')
# 语音转文字
AUDIO_TEXT_EXTRACT = os.getenv('AUDIO_TEXT_EXTRACT', 'http://**************:6300/voice2text')

# ocr服务调用
JUSURE_OCR_API = os.getenv('JUSURE_OCR_API', 'http://**************:8082/ocr')


######## 兼容 MyLocalModel 的 WebSocket 大模型调用类 ######

LOCAL_MODEL_PATH = os.getenv('LOCAL_MODEL_PATH', 'ws://spark-api.xf-yun.com/v4.0/chat')
LOCAL_MODEL_NAME = os.getenv('LOCAL_MODEL_NAME', 'jusure-llm')

# 本地或者公网
IS_SPARK_SAAS_MODEL = int(os.getenv('IS_SPARK_SAAS_MODEL', 0))

# dea6d294
SPARK_SAAS_MODEL_APPID = os.getenv('SPARK_SAAS_MODEL_APPID', 'dea6d294')

# 语音识别
#################### 语音识别 ####################

JUSURE_SPEECH_CLIENT_ID = os.getenv('JUSURE_SPEECH_CLIENT_ID', '3MScLBQhc9NCUBNmLEpebDee')
JUSURE_SPEECH_CLIENT_SECRET = os.getenv('JUSURE_SPEECH_CLIENT_ID', '64JmDZ507nW2Skwo45dtWhgtystsVBqk')


SPARK_MODEL_API_KEY = os.getenv('SPARK_MODEL_API_KEY', '903d64be651d22e42fe4d3f00bd3bf2f')
SPARK_MODEL_API_SECRET = os.getenv('SPARK_MODEL_API_SECRET', 'ZDY4Mzg4M2RjY2EzY2RiMWMzNjhkN2Jk')
SPARK_MODEL_TRACE_ID = os.getenv('SPARK_MODEL_TRACE_ID', 'spark-demo-test')

# 成功
SUCCESS = 'SUCCESS'
# 失败
FAILURE = 'FAILURE'
# 处理中
PENDING = 'PENDING'

SPLITDOC = 'SPLITDOC'
CLEARDOC = 'CLEARDOC'
CLEARCHUNK = 'CLEARCHUNK'
EMBEDDING = 'EMBEDDING'

######## skywalking配置 ########
SKYWALKING_CONFIG = {
    'enabled': eval(os.getenv('SKYWALKING_ENABLED', 'False')),  # 是否启用SkyWalking
    'collector_address': os.getenv('SKYWALKING_COLLECTOR_ADDRESS', '**************:11800'),  # SkyWalking Collector地址
    'service_name': os.getenv('SKYWALKING_SERVICE_NAME', 'jusure_ai_localhost'),  # 服务名称
    'log_level': os.getenv('SKYWALKING_LOG_LEVEL', 'INFO')  # 日志级别
} 

# 第三方接口
ZS_BASE_URL = os.getenv('ZS_BASE_URL',None)


## minio presigned url replace
IS_MINIO_PRESIGNED_URL_REPLACE = int(os.getenv('IS_MINIO_PRESIGNED_URL_REPLACE', '0'))
# PRESIGNED_URL_REPLACE_HOST = os.getenv('PRESIGNED_URL_REPLACE_HOST', '*************:80')
PRESIGNED_URL_REPLACE_HOST = os.getenv('PRESIGNED_URL_REPLACE_HOST', '*************:80')
LOCAL_MINIO_IP = os.getenv('LOCAL_MINIO_IP', '*************')