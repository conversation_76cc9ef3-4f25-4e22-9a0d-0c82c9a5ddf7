import os

# 日志目录
LOGO_PATH = os.path.realpath(
    os.path.join(os.path.split(os.path.split(os.path.realpath(__file__))[0])[0], '')) + "\\logs\\"
# 项目目录
PROJECT_PATH = os.path.realpath(os.path.join(os.path.split(os.path.split(os.path.realpath(__file__))[0])[0], ''))
# 训练模型导出tar目录
MODEL_EXPORT_TAR_DIR = os.getenv("MODEL_EXPORT_TAR_DIR", "/Users/<USER>/Documents/model/files/")
# 模型文件目录
MODEL_EXPORT_DIR = os.getenv("MODEL_EXPORT_DIR", "/Users/<USER>/Documents/model/")
# 模型下载
MODEL_DOWNLOAD_URL = os.getenv("MODEL_DOWNLOAD_URL", "http://**************:30080/data/")
# llama-factory env
LLAMA_FACTORY_ENV = os.getenv("LLAMA_FACTORY_ENV", "/home/<USER>/anaconda3/bin:")
# 重排序模型
RANK_MODEL_URL = os.getenv('RANK_MODEL_URL', 'http://**************:6300/api/v1/rerank/create')

# minio
MINIO_CONF = {
    # "endpoint": os.getenv('MINIO_ENDPOINT', '**************:9000'),
    "endpoint": os.getenv('MINIO_ENDPOINT', '**************:9000'),
    "access_key": os.getenv('MINIO_ACCESS_KEY', 'vlnO7uWSDGj92UEItkIX'),
    "secret_key": os.getenv('MINIO_SECRET_KEY', '94458uubN5x4CiuIxr6xn0Do0aym36lHSlqa0Hz9'),
}


# mysql
SQLConf = {
    'mysql': {
        "host": "**************",
        # 'port': 23308,
        'port': 3306,
        'user': 'root',
        'password': 'zsrz2024wfn031641',
        # 'database': 'master',
        'database': 'master_hpc',
        'database_type': 'mysql',
        'schema': 'public',
        'charset': 'utf8mb4',
    }
}

# mq
RABBITMQ = {
    'user': 'root',
    'password': 'zsrz2024wfn031641',
    'host': '**************',
    'port': 5672,
    'v-host': '/dev',
    'exchange': {
        'client_portrait': 'mongodb',
        'client_care': 'mongodb',
        'mongo': 'mongodb',
        'aigc': 'aigc',
        'stream': 'stream',
    },
    'queue': {
        'mongo': 'mongodb',
        'aigc': 'aigc',
        'stream': 'stream',
        'client_portrait': 'client_portrait',
        'client_care': 'client_care'
    },
    'routing_key':
        {
            'mongo': 'mongodb_key',
            'aigc': 'aigc_key',
            'stream': 'stream_key',
            'client_portrait': 'portrait_key',
            'client_care': 'care_key'
        }
}

# mq
AIGC_RABBITMQ = {
    'user': 'root',
    'password': 'wfn031641',
    'host': '**************',
    'port': 5672,
    'v-host': '/prod',
    'exchange': {
        'aigc': 'aigc',
        'aigc_chatglm': 'aigc',
        'aigc_llama': 'aigc'
    },
    'queue': {
        'aigc': 'aigc',
        'aigc_chatglm': 'aigc_chatglm',
        'aigc_llama': 'aigc_llama'
    },
    'routing_key':
        {
            'aigc': 'aigc_key',
            'aigc_chatglm': 'aigc_chatglm',
            'aigc_llama': 'aigc_llama'
        }
}

# token存储
INS_TOKEN_REDIS = {
    'host': '**************',
    'port': 6379,
    'password': 'zsrz2024wfn031641',
    'db': 1
}
# 权限 相关
INS_ROLE_REDIS = {
    'host': '**************',
    'port': 6379,
    'password': 'zsrz2024wfn031641',
    'db': 2
}
# 字典 相关
INS_DICT_REDIS = {
    'host': '**************',
    'port': 6379,
    'password': 'zsrz2024wfn031641',
    'db': 3
}
# 客户信息缓存
INS_CLIENT_REDIS = {
    'host': '**************',
    'port': 6379,
    'password': 'zsrz2024wfn031641',
    'db': 4
}
# AIGC缓存
INS_AIGC_REDIS = {
    'host': '**************',
    'port': 6379,
    'password': 'wfn031641',
    'db': 12
}


# JusureT3Api = 'http://**************:5202/api/prod'

JusureT3Api = 'http://**************:5000/api/prod'
# JUSURET3_API: 'http://**************:5202/api/prod'
JusureT3Url = 'http://***************:1011'
UrlEnv = ''
JUSURE_EMBEDDINGS_API = os.getenv('JUSURE_EMBEDDINGS_API', 'http://**************:5205/')

# 通用数科 demo
# # test app id
ENABLE_TYSK_DEMO = int(os.getenv('ENABLE_TYSK_DEMO', 0))
TYSK_DEMO_APP_ID = int(os.getenv('TYSK_DEMO_APP_ID', '4926692760435560449'))

# 图像识别
GRAPH_SERVER_URL = "http://**************:8082/"

# emb
IS_910B_EMBEDDINGS = int(os.getenv('IS_910B_EMBEDDINGS', 0))
JUSURE_910B_EMBEDDINGS_API = os.getenv('JUSURE_910B_EMBEDDINGS_API', 'http://**************:5205/')

# 语音识别
JUSURE_SPEECH_CLIENT_ID = os.getenv('JUSURE_SPEECH_CLIENT_ID', '3MScLBQhc9NCUBNmLEpebDee')
JUSURE_SPEECH_CLIENT_SECRET = os.getenv('JUSURE_SPEECH_CLIENT_ID', '64JmDZ507nW2Skwo45dtWhgtystsVBqk')

# MINIO
# http://**************:9000/knowledge-docs/皮卡丘.webp
# OSS_DOMAIN_NAME = os.getenv('OSS_DOMAIN_NAME', 'oss.ai.zhongshuruizhi.com')
OSS_DOMAIN_NAME = os.getenv('OSS_DOMAIN_NAME', '**************:9000')
KNOWLEDGE_OSS_BUCKET_NAME = os.getenv('KNOWLEDGE_OSS_BUCKET_NAME', 'guotou')

# aliyun oss
IS_ALIYUN_OSS = int(os.getenv('IS_ALIYUN_OSS', '0'))

# ppt 提取服务
JUSURE_MULTIMODAL_IMAGE_DESCRIPTION = 'http://**************:8082/extract'
JUSURE_MULTIMODAL_IMAGE_DESCRIPTION_V1= os.getenv('JUSURE_MULTIMODAL_IMAGE_DESCRIPTION_V1', 'http://**************:8082/image/description')

# 成功
SUCCESS = 'SUCCESS'
# 失败
FAILURE = 'FAILURE'
# 处理中
PENDING = 'PENDING'

SPLITDOC = 'SPLITDOC'
CLEARDOC = 'CLEARDOC'
CLEARCHUNK = 'CLEARCHUNK'
EMBEDDING = 'EMBEDDING'

######## skywalking配置 ########
SKYWALKING_CONFIG = {
    'enabled': eval(os.getenv('SKYWALKING_ENABLED', 'False')),  # 是否启用SkyWalking
    'collector_address': os.getenv('SKYWALKING_COLLECTOR_ADDRESS', '**************:11800'),  # SkyWalking Collector地址
    'service_name': os.getenv('SKYWALKING_SERVICE_NAME', 'jusure_ai_localhost'),  # 服务名称
    'log_level': os.getenv('SKYWALKING_LOG_LEVEL', 'INFO')  # 日志级别
} 


# 第三方接口
ZS_BASE_URL = os.getenv('ZS_BASE_URL',None)


## minio presigned url replace
IS_MINIO_PRESIGNED_URL_REPLACE = int(os.getenv('IS_MINIO_PRESIGNED_URL_REPLACE', '0'))
# PRESIGNED_URL_REPLACE_HOST = os.getenv('PRESIGNED_URL_REPLACE_HOST', '*************:80')
PRESIGNED_URL_REPLACE_HOST = os.getenv('PRESIGNED_URL_REPLACE_HOST', '*************:80')
LOCAL_MINIO_IP = os.getenv('LOCAL_MINIO_IP', '*************')