.PHONY: help prepare-dev test lint run doc

VENV_PATH?=venv
VENV_NAME?=venv
VENV_ACTIVATE=. $(VENV_NAME)/bin/activate
PYTHON=python3
ENCRPYT_KEY="rA4vzk1rh1p9uuT3XP9AaY10BcNL8LoeTpBD43J1MKc="
WORKERS=1

APPLICATION_NAME=reg.real-ai.cn/aip/realray_face_srv
VERSION="0.1"
APPLICATION_TAG=${VERSION}-Linux-x86_64_t4
SOPHON_APPLICATION_TAG=${VERSION}-Linux-aarch64-sophon_soc
SOPHON_PCIE_APPLICATION_TAG=${VERSION}-Linux-x86_64-sophon_pcie



.DEFAULT: help
help:
	@echo "make prepare-dev"
	@echo "       prepare development environment, use only once"
	@echo "make test"
	@echo "       run tests"
	@echo "make lint"
	@echo "       run pylint and mypy"
	@echo "make server"
	@echo "       run web server"
	@echo "make clean"
	@echo "       clean python cache files"
	@echo "make doc"
	@echo "       build sphinx documentation"
	@echo "make encrypt"
	@echo "       encrypt model"
	@echo "make build"
	@echo "       build docker image"
	@echo "make push"
	@echo "       push docker image"

# 安装python 和 搭建虚拟环境
prepare-dev:
	python -m venv venv

# 进入虚拟环境
venv: $(VENV_PATH)/bin/activate
	. $(VENV_PATH)/bin/activate

test:
	${PYTHON} -m pytest -s --basetemp=output ./tests/test_main.py

lint:
	${PYTHON} -m pylint
	${PYTHON} -m mypy

clean-pyc:
	@find . -name '*.pyc' -delete
	@find . -name '__pycache__' -type d | xargs rm -fr
	@find . -name '.pytest_cache' -type d | xargs rm -fr

clean:clean-pyc
	rm -rf 
	@echo "## Clean all data."

server:
	WEB_CONCURRENCY=${WORKERS} uvicorn app.main:app --host 0.0.0.0 --port 5000

doc:
	$(VENV_ACTIVATE) && cd docs; make html
.PHONY: doc

get_model:
	mkdir -p model
	wget http://***********:9000/algo-deploy-adapter/x86/Nvidia/yolov5x/model/2023-01-19/realcheck_cls37_1207_x5_state.engine -O ./model/realcheck_cls37_1207_x5_state.engine

.PHONY: get_model

encrypt:
	docker run -it --rm --name "model_encrypt" -v ${PWD}:/code -w /code reg.real-ai.cn/aip/encrypt:0.2-x86_64 python3 /app/crypto_tool.py --key ${ENCRPYT_KEY} -i /code/model/realcheck_cls37_1207_x5_state.engine -o /code/model/realcheck_cls37_1207_x5_state.engine_encrypt
.PHONY: encrypt

encrypt_soc:
	${PYTHON} ./app/utils/crypto_tool.py --key ${ENCRPYT_KEY} --input-engine-path ./model/crnn_int8_softmax.bmodel --output-engine-path ./model/crnn_int8_softmax.bmodel_encrypt
	${PYTHON} ./app/utils/crypto_tool.py --key ${ENCRPYT_KEY} --input-engine-path ./model/dbnet_int8.bmodel --output-engine-path ./model/dbnet_int8.bmodel_encrypt
	# ${PYTHON} ./app/utils/crypto_tool.py --key ${ENCRPYT_KEY} --input-engine-path ./model/realcheck_cls38_0130_x5.bmodel --output-engine-path ./model/realcheck_cls38_0130_x5.bmodel_encrypt
	${PYTHON} ./app/utils/crypto_tool.py --key ${ENCRPYT_KEY} --input-engine-path ./model/yolov5_v3_new.bmodel --output-engine-path ./model/yolov5_v3_new.bmodel_encrypt

.PHONY: encrypt_soc


pyarmor:
	docker run -it --rm --name "bl_server_encrypy" -v ${PWD}:/code -w /code reg.real-ai.cn/aip/encrypt:0.2-x86_64 pyarmor obfuscate ./deps/licclient/__init__.py --recursive -O dist/deps/licclient
	docker run -it --rm --name "bl_server_encrypy" -v ${PWD}:/code -w /code reg.real-ai.cn/aip/encrypt:0.2-x86_64 pyarmor obfuscate ./app/__init__.py  --recursive -O dist/app
	sudo cp ./app/utils/djmd5_0322.json ./dist/app/utils/djmd5_0322.json
	docker run -it --rm --name "bl_server_encrypy" -v ${PWD}:/code -w /code reg.real-ai.cn/aip/encrypt:0.2-x86_64 pyarmor obfuscate ./inference_system/__init__.py -O dist/inference_system
	docker run -it --rm --name "bl_server_encrypy" -v ${PWD}:/code -w /code reg.real-ai.cn/aip/encrypt:0.2-x86_64 pyarmor obfuscate ./inference_system/module/inference_t4 -O dist/inference_system/module/inference_t4 
	sudo cp -r ./inference_system/module/inference_t4/cn_clip/ ./dist/inference_system/module/inference_t4/
	sudo cp -r ./app/settings/*.json ./dist/app/settings/
.PHONY: pyarmor

pyarmor_sophon_soc:
	docker run -it --rm --name "bl_server_encrypy" -v ${PWD}:/code -w /code reg.real-ai.cn/aip/encrypt:0.2-aarch64 pyarmor obfuscate ./deps/licclient/__init__.py --recursive -O dist/deps/licclient
	docker run -it --rm --name "bl_server_encrypy" -v ${PWD}:/code -w /code reg.real-ai.cn/aip/encrypt:0.2-aarch64 pyarmor obfuscate ./app/__init__.py  --recursive -O dist/app
	sudo cp ./app/utils/djmd5_0322.json ./dist/app/utils/djmd5_0322.json
	docker run -it --rm --name "bl_server_encrypy" -v ${PWD}:/code -w /code reg.real-ai.cn/aip/encrypt:0.2-aarch64 pyarmor obfuscate  ./inference_system/__init__.py -O dist/inference_system
	docker run -it --rm --name "bl_server_encrypy" -v ${PWD}:/code -w /code reg.real-ai.cn/aip/encrypt:0.2-aarch64 pyarmor obfuscate ./inference_system/module/inference_sophon_soc -O dist/inference_system/module/inference_sophon_soc 
	docker run -it --rm --name "bl_server_encrypy" -v ${PWD}:/code -w /code reg.real-ai.cn/aip/encrypt:0.2-aarch64 pyarmor obfuscate ./inference_system/module/inference_sophon_soc/face_recognition -O dist/inference_system/module/inference_sophon_soc/face_recognition 
	sudo cp -r ./app/settings/*.json ./dist/app/settings/
.PHONY: pyarmor_sophon_soc

pyarmor_sophon_pcie:
	docker run -it --rm --name "bl_server_encrypy" -v ${PWD}:/code -w /code reg.real-ai.cn/aip/encrypt:0.1-x86_64 pyarmor obfuscate ./deps/licclient/__init__.py --recursive -O dist/deps/licclient
	docker run -it --rm --name "bl_server_encrypy" -v ${PWD}:/code -w /code reg.real-ai.cn/aip/encrypt:0.1-x86_64 pyarmor obfuscate ./app/__init__.py  --recursive -O dist/app
	sudo cp ./app/utils/djmd5_0322.json ./dist/app/utils/djmd5_0322.json
	docker run -it --rm --name "bl_server_encrypy" -v ${PWD}:/code -w /code reg.real-ai.cn/aip/encrypt:0.1-x86_64 pyarmor obfuscate ./inference_system/__init__.py -O dist/inference_system
	docker run -it --rm --name "bl_server_encrypy" -v ${PWD}:/code -w /code reg.real-ai.cn/aip/encrypt:0.1-x86_64 pyarmor obfuscate ./inference_system/module/inference_sophon_pcie -O dist/inference_system/module/inference_sophon_pcie 
	sudo cp -r ./app/settings/*.json ./dist/app/settings/
.PHONY: pyarmor_sophon_pcie

# build: pyarmor 
build:
	docker build -t ${APPLICATION_NAME}:${APPLICATION_TAG} .
.PHONY: build

build_soc: pyarmor_sophon_soc
	docker build -f ./docker/sophon-soc.Dockerfile -t ${APPLICATION_NAME}:${SOPHON_APPLICATION_TAG} .
.PHONY: build_soc

build_pcie: pyarmor_sophon_pcie
	docker build -f ./docker/sophon-pcie.Dockerfile -t ${APPLICATION_NAME}:${SOPHON_PCIE_APPLICATION_TAG} .
.PHONY: 

push: build
	docker push ${APPLICATION_NAME}:${APPLICATION_TAG}
.PHONY: push

work:
	docker run --gpus all --rm -it \
		--name "jusureAI_pwd_ls" \
		-v ${PWD}:/code \
		-w /code \
		-p 7070:7100 \
		-e MINIO_ENDPOINT=**************:9000 \
		-e MINIO_ACCESS_KEY=vlnO7uWSDGj92UEItkIX \
		-e MINIO_SECRET_KEY=94458uubN5x4CiuIxr6xn0Do0aym36lHSlqa0Hz9 \
		-e MASTER_MYSQL_HOST=************** \
		-e MASTER_MYSQL_PORT=3306 \
		-e MASTER_MYSQL_USER=root \
		-e MASTER_MYSQL_PWD=zsrz2024wfn031641 \
		-e MASTER_MYSQL_DATABASE=master \
		-e REDIS_HOST=************** \
		-e REDIS_PORT=6379 \
		-e REDIS_PWD=zsrz2024wfn031641 \
		-e CELERY_BROKER_URL='redis://:zsrz2024wfn031641@**************:6379/13' \
		-e CELERY_BACKEND_URL='redis://:zsrz2024wfn031641@**************:6379/13' \
		jusure_ai:0.1.7-Linux-x86_64 bash
	
	# docker run --gpus all --rm -it --name "jusureAI_pwd_ls" -v ${PWD}:/code -w /code -p 7070:7100 jusure_ai:0.1.7-Linux-x86_64 bash

	# docker run --rm -it --name "jusureAI_pwd_ls" -v ${PWD}:/code -w /code -p 7070:7100 jusure_ai_dxsj:0.1.1-Linux-x86_64 bash
	# docker run --cpus=12 --runtime=nvidia --rm -it --name "bl_server_dev_1024" -v ${PWD}:/code -w /code -e NVIDIA_VISIBLE_DEVICES=0 -e licser_nodes_master_hp="***********:5551" -p 6060:5000 ${APPLICATION_NAME}:${APPLICATION_TAG} bash
	# docker run --rm -it --name "realray_face_wrap_pwd" -v ${PWD}:/code -w /code -p 6005:5000 ${APPLICATION_NAME}:${APPLICATION_TAG} bash
.PHONY: work

work_soc:
	docker run --name work_soc --rm -it --privileged=true -p 5002:5000 -w /code -v ${PWD}:/code -v /system:/system -v /etc/localtime:/etc/localtime -v /etc/timezone:/etc/timezone -e LOCAL_USER_ID=`id -u` -e WEB_CONCURRENCY=1 -e licser_nodes_master_hp="***********:5551" ${APPLICATION_NAME}:${SOPHON_APPLICATION_TAG} bash
.PHONY: work_soc

work_pcie:
	docker run --name face_srv_pcie_pwd --rm -it --privileged=true -p 2500:5000 -w /code -v ${PWD}:/code \
	--device=/dev/bm-sophon0:/dev/bm-sophon0 \
	--device=/dev/bm-sophon1:/dev/bm-sophon1 \
	--device=/dev/bm-sophon2:/dev/bm-sophon2 \
	--device=/dev/bm-sophon3:/dev/bm-sophon3 \
	--device=/dev/bm-sophon4:/dev/bm-sophon4 \
	--device=/dev/bm-sophon5:/dev/bm-sophon5 \
	--device=/dev/bmdev-ctl:/dev/bmdev-ctl \
	-v /dev/shm --tmpfs /dev/shm:exec \
	-v /dev:/dev \
	-e WEB_CONCURRENCY=1 -e licser_nodes_master_hp="***********:5551" ${APPLICATION_NAME}:${SOPHON_PCIE_APPLICATION_TAG} bash
.PHONY: work_pcie

api_test:
	mkdir -p test_result
	vegeta attack -targets ./tests/vegeta/target.txt -timeout=10s -rate 0 -max-workers ${WORKERS} -duration=60s -output test_result/vegeta_${WORKERS}.bin
	vegeta report -output test_result/result_${WORKERS}.txt test_result/vegeta_${WORKERS}.bin
	vegeta report -type='hist[0,50ms,100ms,200ms,250ms,300ms,350ms]' -output test_result/result_hist_${WORKERS}.txt test_result/vegeta_${WORKERS}.bin
	vegeta plot -title "请求延迟" test_result/vegeta_${WORKERS}.bin > test_result/plot_${WORKERS}.html
.PHONY: api_test

start:
	docker run --runtime=nvidia -itd --name "bl_server_image_test" -e PRECISION_FLAG=0 -e WEB_CONCURRENCY=${WORKERS} -e NVIDIA_VISIBLE_DEVICES=0 -e licser_nodes_master_hp="***********:5551" -p 5001:5000 ${APPLICATION_NAME}:${APPLICATION_TAG}
.PHONY: start

stop:
	docker stop bl_server_image_test
	docker rm bl_server_image_test

.PHONY: stop

