[program:test-jusure-ai]
user=jusure
directory = /mnt/projects/service/jusure_AI
command=/mnt/projects/service/jusure-env/bin/uwsgi --ini /mnt/projects/service/jusure_AI/uwsgi_release.ini
stderr_logfile = /mnt/projects/service/jusure_AI/logs/run.log
stopasgroup=true
killasgroup=true
environment=ENVIRONMENT=RELEASE

[program:test-jusure-ai-worker]
user=jusure
directory=/mnt/projects/service/jusure_AI
command=/mnt/projects/service/jusure-env/bin/celery --app=celery_app worker -l info
stdout_logfile=/mnt/projects/service/jusure_AI/logs/celery.worker.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile=/mnt/projects/service/jusure_AI/logs/celery.worker.err.log
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=10
autostart=true
autorestart=true
startsecs=10
stopwatisecs=60
priority=997
stopasgroup=true
killasgroup=true
environment=ENVIRONMENT=RELEASE

[program:test-jusure-ai-beat]
user=jusure
directory=/mnt/projects/service/jusure_AI
command=/mnt/projects/service/jusure-env/bin/celery --app=celery_app beat -l info
stdout_logfile=/mnt/projects/service/jusure_AI/logs/celery.beat.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile=/mnt/projects/service/jusure_AI/logs/celery.beat.err.log
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=10
autostart=true
autorestart=true
startsecs=10
stopwatisecs=60
priority=998
stopasgroup=true
killasgroup=true
environment=ENVIRONMENT=RELEASE


[group:test-ai]
user=jusure
programs=test-jusure-ai,test-jusure-ai-worker,test-jusure-ai-beat
environment=ENVIRONMENT=RELEASE
