aiohttp==3.9.3
aiohttp-retry==2.8.3
aiosignal==1.3.1

aliyun-python-sdk-core==2.15.1
aliyun-python-sdk-kms==2.16.2
aliyun-python-sdk-sts==3.1.2
amqp==5.2.0
aniso8601==9.0.1
anthropic==0.17.0
antlr4-python3-runtime==4.9.3
anyio==4.3.0
appdirs==1.4.4
arxiv==2.1.0
async-timeout==4.0.3
attrs==23.2.0
Authlib==1.3.0
backoff==2.2.1
beautifulsoup4==4.12.2
billiard==3.6.4.0
blinker==1.7.0
blis==0.7.11
Brotli==1.1.0
bs4==0.0.2
cachetools==5.3.3
catalogue==2.0.10
celery==5.2.7
celery-once==3.0.1
certifi==2024.2.2
cffi==1.16.0
chardet==5.1.0
charset-normalizer==3.3.2
click==8.1.7
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
cloudpathlib==0.16.0
cohere==4.57
coloredlogs==15.0.1
confection==0.1.4
contourpy==1.2.0
coverage==7.2.7
crcmod==1.7
cryptography==42.0.5
cssselect==1.2.0
curl_cffi==0.6.3b1
cycler==0.12.1
cymem==2.0.8
dashscope==1.14.1
dataclasses==0.6
dataclasses-json==0.5.14
distro==1.9.0
dnspython==2.6.1
docx2txt==0.8
duckduckgo_search==4.4.3
effdet==0.4.1
elastic-transport==8.13.0
elasticsearch==8.13.0
emoji==2.11.0
environs==9.5.0
et-xmlfile==1.1.0
exceptiongroup==1.2.0
fabric==3.2.2
faiss-cpu==1.8.0
fastavro==1.9.4
feedfinder2==0.0.4
feedparser==6.0.10
ffmpeg-python==0.2.0
filelock==3.13.3
filetype==1.2.0
Flask==2.3.3
Flask-Compress==1.14
Flask-Cors==4.0.0
Flask-Login==0.6.3
Flask-Migrate==4.0.7
Flask-RESTful==0.3.10
Flask-SQLAlchemy==2.5.1
Flask-SSE==1.0.0
flatbuffers==24.3.25
fonttools==4.50.0
frozendict==2.4.0
frozenlist==1.4.1
fsspec==2024.3.1
func-timeout==4.3.5
future==1.0.0
gevent==23.9.1
gmpy2==2.1.5
google-ai-generativelanguage==0.4.0
google-api-core==2.18.0
google-api-python-client==2.90.0
google-auth==2.29.0
google-auth-httplib2==0.2.0
google-generativeai==0.3.2
google-search-results==2.4.2
googleapis-common-protos==1.63.0
greenlet==3.0.3
grpcio==1.56.0
grpcio-status==1.56.0
grpcio-tools==1.56.0
gunicorn==21.2.0
h11==0.14.0
h2==4.1.0
hpack==4.0.0
html5lib==1.1
httpcore==1.0.5
httplib2==0.22.0
httpx==0.27.2
huggingface-hub==0.16.4
humanfriendly==10.0
hyperframe==6.0.1
idna==3.6
imgkit==1.2.3
importlib-metadata==6.11.0
inflect==7.0.0
iniconfig==2.0.0
iopath==0.1.10
itsdangerous==2.1.2
jieba==0.42.1
jieba3k==0.35.1
Jinja2==3.1.3
jmespath==0.10.0
joblib==1.4.2
jsonpatch==1.33
jsonpointer==2.4
kiwisolver==1.4.5
kombu==5.3.5

langcodes==3.3.0
langdetect==1.0.9

layoutparser==0.3.4
lxml==5.1.0
mailchimp-transactional==1.0.56
Mako==1.3.2
Markdown==3.6
MarkupSafe==2.1.5
marshmallow==3.21.1
matplotlib==3.8.4
mpmath==1.3.0
msg-parser==1.2.0
multidict==6.0.5
multitasking==0.0.11
murmurhash==1.0.10
mypy-extensions==1.0.0
mysql-connector-python==8.3.0
Naked==0.1.32
nest-asyncio==1.6.0
networkx==3.2.1
newspaper3k==0.2.8
nltk==3.8.1
numexpr==2.9.0
numpy==1.26.4
olefile==0.47
omegaconf==2.3.0
onnx==1.16.0
onnxruntime==1.15.1

openapi-schema-pydantic==1.2.4
opencv-python==********
openpyxl==3.1.2

oss2==2.18.4
packaging==23.2
pandas==2.0.3
pdf2image==1.17.0
pdfminer.six==20231228
pdfplumber==0.11.0
peewee==3.17.1
pika==1.3.2
pillow==10.2.0
plotly==5.20.0
pluggy==1.4.0
portalocker==2.8.2
preshed==3.0.9
prompt-toolkit==3.0.43
proto-plus==1.23.0
protobuf==4.25.3
psycopg2-binary==2.9.9
pyasn1==0.6.0
pyasn1_modules==0.4.0
pyclipper==1.3.0.post5
pycocotools==2.0.7
pycparser==2.21
pycryptodome==3.20.0

pydub==0.25.1
PyJWT==2.8.0
pymilvus==2.3.0
pymongo==4.6.3
PyMuPDF==1.24.0
PyMuPDFb==1.24.0
PyMySQL==1.1.0
pyparsing==3.1.2
pypdfium2==4.30.0
pypng==0.20220715.0
pysnowflake==0.1.3
pytesseract==0.3.10
pytest==7.3.2
pytest-mock==3.11.1
python-dateutil==2.9.0.post0
python-docx==1.1.0
python-dotenv==1.0.0
python-iso639==2024.2.7
python-magic==0.4.27
python-multipart==0.0.9
python-pptx==0.6.23
pytz==2024.1
PyYAML==6.0.1
qdrant-client==1.7.3
qrcode==7.4.2
rapidfuzz==3.7.0
rapidocr-onnxruntime==1.3.16
readabilipy==0.2.0
redis==5.0.3
regex==2023.12.25
replicate==0.22.0
requests==2.31.0
requests-file==2.0.0
resend==0.7.2
rsa==4.9
safetensors==0.3.2
scikit-learn==1.4.2
scipy==1.12.0
sentry-sdk==1.39.2
sgmllib3k==1.0.0
shapely==2.0.3
shellescape==3.8.1
six==1.16.0
smart-open==6.4.0
sniffio==1.3.1
socksio==1.0.0
soupsieve==2.5
spacy==3.7.4
spacy-legacy==3.0.12
spacy-loggers==1.0.5
sqlacodegen==2.3.0.post1
SQLAlchemy==1.4.48
srsly==2.4.8
sseclient-py==1.8.0
sympy==1.12
tabulate==0.9.0
tenacity==8.2.3
thinc==8.2.3
threadpoolctl==3.4.0
tiktoken==0.8.0
timm==0.9.16
tinysegmenter==0.3
tldextract==5.1.2
tokenizers==0.13.3
tomli==2.0.1
torch==2.2.1
torchvision==0.17.1
tornado==6.4
tqdm==4.66.2
transformers==4.31.0
twilio==9.0.0
typer==0.9.4
typing-inspect==0.9.0

tzdata==2024.1
ujson==5.9.0
un==0.0.4
unstructured==0.10.30
unstructured-inference==0.7.25
unstructured.pytesseract==0.3.12
uritemplate==4.1.1
urllib3
validators==0.21.0
vine==5.1.0
wasabi==1.1.2
wcwidth==0.2.13
weasel==0.3.4
weaviate-client==3.21.0
webencodings==0.5.1
websocket-client==1.7.0
Werkzeug==3.0.2
wikipedia==1.4.0
WTForms==3.1.2
WTForms-JSON==0.3.5

XlsxWriter==3.2.0
xmltodict==0.13.0
yarl==1.9.4
yfinance==0.2.37
zhipuai==1.0.7
zipp==3.18.1
zope.event==5.0
zope.interface==6.2
weworkapi
requests_toolbelt
wechatpayv3
gpustat
wget
minio

accelerate==0.20.3
aioboto3==13.2.0
graspologic==3.4.1
hnswlib==0.8.0
nano-vectordb==*******
neo4j==5.26.0
ollama==0.3.3
oracledb==2.5.0
pyvis==0.3.2
xxhash==3.5.0

boto3==1.35.36
botocore==1.35.36
aiobotocore==2.15.2
s3transfer==0.10.3

nebula3-python==3.8.3
pytest-asyncio==0.23.8

langgraph==0.2.60
langchain-core==0.3.27
langchain==0.3.13
langchain-community==0.3.13
langchain-openai==0.2.14
langchain_wenxin==0.9.0
langchain_experimental==0.3.4

alembic==1.13.1
pydantic-settings==2.7.0
typing_extensions==4.12.2
pydantic==2.7.4
langsmith==0.2.6
openai==1.58.1
orjson==3.10.1
img2table==1.4.1
polars-lts-cpu==1.25.2
hdbscan==0.8.40
hf-xet==1.1.0 
sentence_transformers==4.1.0 
