      
import argparse
import re
import sys

from sqlalchemy import String
from sqlalchemy.dialects.postgresql.base import PGDialect
from sqlalchemy.sql.compiler import GenericTypeCompiler

print("Applying monkey patch for KingbaseES version detection...")

# 备份原始方法
original_get_server_version_info = PGDialect._get_server_version_info

def _patched_kingbase_version_parser(self, connection):
    version_str = connection.exec_driver_sql("SELECT version()").scalar()
    if version_str.startswith("KingbaseES"):
        # 使用kingbase伪装的pgsql版本号
        version = connection.exec_driver_sql("SHOW server_version").scalar()
        match = [int(x) for x in version.split(".")]
        print(f"[INFO] 伪装版本: PostgreSQL {version}")
        return tuple(match)
    else:
        return original_get_server_version_info(self, connection)


# 应用补丁
PGDialect._get_server_version_info = _patched_kingbase_version_parser


def _patched_render_string_type(self, type_, name):
    text = name
    if type_.length:
        if isinstance(type_.length, str):
            match = re.match(r"(\d+)", type_.length)
            if match:
                text += "(%d)" % int(match.group(1))
        else:
            text += "(%d)" % type_.length
    if type_.collation:
        text += ' COLLATE "%s"' % type_.collation
    return text


GenericTypeCompiler._render_string_type = _patched_render_string_type

# 原始 String 类的构造函数
_original_string_init = String.__init__


# 自定义构造函数：解析长度中的数字部分
def _patched_string_init(self,
                         length=None,
                         collation=None,
                         convert_unicode=False,
                         unicode_error=None,
                         _warn_on_bytestring=False,
                         _expect_unicode=False, ):
    # 如果长度是字符串（如 '255 char'），提取数字部分
    if isinstance(length, str):
        match = re.match(r"^(\d+)\s*.*$", str(length))
        if match:
            length = int(match.group(1))
    # 调用原始构造函数
    _original_string_init(self,
                          length,
                          collation,
                          convert_unicode,
                          unicode_error,
                          _warn_on_bytestring,
                          _expect_unicode)


# 应用补丁
String.__init__ = _patched_string_init

print("Monkey patch applied.")

# --- 现在运行 sqlacodegen ---
print("Running sqlacodegen...")
try:
    # 方法 A: 如果 sqlacodegen 安装在当前环境，可以尝试直接调用其入口点
    import sqlacodegen.main

    # 创建解析器
    parser = argparse.ArgumentParser(description="PostgreSQL&KingbaseES数据库models生成")
    parser.add_argument("-u", "--user", type=str, help="数据库用户名", default="root")
    parser.add_argument("-p", "--password", type=str, help="数据库密码", default="<PASSWORD>")
    parser.add_argument("-H", "--host", type=str, help="数据库地址", default="127.0.0.1")
    parser.add_argument("-P", "--port", type=int, help="数据库端口", default=5432)
    parser.add_argument("-d", "--database", type=str, help="数据库名称", default="master")
    parser.add_argument("-c", "--charset", type=str, help="数据库编码", default="utf8")
    parser.add_argument("-s", "--schema", type=str, help="数据库schema", default="public")
    # 解析参数
    args = parser.parse_args()
    # 修改 sys.argv 来传递参数给 sqlacodegen
    # 把 'kingbase_connection_string' 替换成你的实际连接字符串
    # 例如: 'postgresql+psycopg2://user:password@host:port/database'
    kingbase_connection_string = f'postgresql+psycopg2://{args.user}:{args.password}@{args.host}:{args.port}/{args.database}?options=-csearch_path={args.schema}&client_encoding={args.charset}'
    output_file = 'module/mysql/models.py'  # <--- 输出文件名
    sys.argv = ['sqlacodegen', kingbase_connection_string, '--outfile', output_file]
    print(f"Calling sqlacodegen with args: {sys.argv}")
    sqlacodegen.main.main()
    print(f"sqlacodegen finished. Output written to {output_file}")

except ImportError:
    # 方法 B: 如果无法导入 main，提示用户在应用补丁后手动运行
    print("Could not import sqlacodegen.cli.main.")
    print("Please run the sqlacodegen command manually in this same terminal session")
    print("where the patch has been applied.")
    print("Example: sqlacodegen postgresql+psycopg2://user:password@host:port/database --outfile models.py")

finally:
    # （可选）恢复原始方法，尽管脚本执行完通常就结束了
    # PGDialect._get_server_version_info = original_get_server_version_info
    print("Restored original version detection method (in case script continues).")

    