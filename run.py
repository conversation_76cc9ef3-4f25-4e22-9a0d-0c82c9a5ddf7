# -*- coding: utf-8 -*-
from catch import *
import wtforms_json
from flask import request

from horticulture.blog import action_log
from settings import API_HOST, API_PORT, API_DEBUG, SKYWALKING_CONFIG
from horticulture.record import api_log_record
from horticulture.validate import req_before_do, url_stream_white



@app.before_request
def handle_before_request():
    request.start = int(time.time() * 1000)
    req_before_do()


@app.after_request
def handle_after_request(resp):
    _path = request.path
    if _path in url_stream_white:
        pass
    else:
        stream_content_types = {'text/event-stream', 'application/octet-stream'}  # 添加其他流式响应的 Content-Type
        content_type = resp.headers.get('content-type', '') or resp.headers.get('Content-Type', '')
        if not any(content_type.startswith(ct) for ct in stream_content_types):
            api_log_record(resp)  # 只记录非流式响应
    return resp

try:
    if SKYWALKING_CONFIG.get('enabled'):
        # skywalking 初始化
        from uwsgidecorators import postfork
        @postfork
        def init_skywalking():
            from skywalking import agent, config
            agent_instance_name = f"{SKYWALKING_CONFIG.get('service_name', 'your-service')}"

            config.init(
                agent_collector_backend_services=SKYWALKING_CONFIG.get('collector_address'),
                agent_name=SKYWALKING_CONFIG.get('service_name'),
                agent_instance_name=agent_instance_name,
                agent_logging_level=SKYWALKING_CONFIG.get('log_level')
            )
            agent.start()
            print('skywalking 初始化成功--------------------------------')
    else:
        print('skywalking 未启用--------------------------------')
except Exception as e:
    print('skywalking 初始化失败--------------------------------',e)

wtforms_json.init()  # 解决json数据传入表单验证，取值问题，正常传入类型需要是，因此使用此插件处理

if __name__ == '__main__':
    app.run(host=API_HOST, port=API_PORT, debug=API_DEBUG)
