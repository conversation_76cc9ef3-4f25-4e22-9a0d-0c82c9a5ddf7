[program:jusure-ai]
user=yons
directory = /media/yons/data/projects/service/jusure_AI
command=/home/<USER>/anaconda3/envs/jusure-env/bin/uwsgi --ini /media/yons/data/projects/service/jusure_AI/uwsgi_prod.ini
stderr_logfile = /media/yons/data/projects/service/jusure_AI/logs/run.log
stopasgroup=true
killasgroup=true
environment=ENVIRONMENT=RELEASE

[program:jusure-ai-worker]
user=yons
directory=/media/yons/data/projects/service/jusure_AI
command=/home/<USER>/anaconda3/envs/jusure-env/bin/celery --app=celery_app worker -l info
stdout_logfile=/media/yons/data/projects/service/jusure_AI/logs/celery.worker.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile=/media/yons/data/projects/service/jusure_AI/logs/celery.worker.err.log
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=10
autostart=true
autorestart=true
startsecs=10
stopwatisecs=60
priority=997
stopasgroup=true
killasgroup=true
environment=ENVIRONMENT=RELEASE

[program:jusure-ai-beat]
user=yons
directory=/media/yons/data/projects/service/jusure_AI
command=/home/<USER>/anaconda3/envs/jusure-env/bin/celery --app=celery_app beat -l info
stdout_logfile=/media/yons/data/projects/service/jusure_AI/logs/celery.beat.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile=/media/yons/data/projects/service/jusure_AI/logs/celery.beat.err.log
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=10
autostart=true
autorestart=true
startsecs=10
stopwatisecs=60
priority=998
stopasgroup=true
killasgroup=true
environment=ENVIRONMENT=RELEASE


[group:prod-ai]
user=yons
programs=jusure-ai,jusure-ai-worker,jusure-ai-beat
environment=ENVIRONMENT=RELEASE