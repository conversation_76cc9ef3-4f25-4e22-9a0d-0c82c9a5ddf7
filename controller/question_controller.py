# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: QuestionController.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 4月 25, 2024
# ---
import json

import requests
import urllib
from urllib import parse

from lib_func.const_map import *
from puppet.es_sdk import EsSdkPool
from . import BaseController
from controller.knowledge_controller import KnowledgeController
from modeler.mongo.knowledge_mg import KnowledgeMg
from modeler.mysql.question_orm import QuestionOrm
from settings import DEFAULT_MODEL_PATH

class QuestionController(BaseController):

    def __init__(self, *args):
        super(Question<PERSON>ontroller, self).__init__(*args)
        self.model = QuestionOrm(self.corpid)
        self.es = EsSdkPool(self.corpid)
        self.mg = KnowledgeMg(self.corpid)
        self.default_model_path = DEFAULT_MODEL_PATH
        self.question_index_name = 'question_{}'.format(self.default_model_path)
        self.question_point_plan_index_name = 'question_point_plan_{}'.format(self.default_model_path)
        

    def get_question_train_data(self, next_id):
        return self.model.get_rel_question_tpu_train_data(next_id)

    def get_qpp_train_data(self, next_id):
        return self.model.get_question_point_plan_train_data(next_id)

    def search_es_question_last(self):

        body_dict = {
            "_source": {
                "includes": ["rel_question_tup_id", "add_time", "character_count", "description", "delete_flag"]
            },
            "query": {
                "match_all": {}
            },
            "sort": [
                {
                    "rel_question_tup_id": {
                        "order": "desc"
                    }
                }
            ],
            "size": 1
        }
        index_name = self.question_index_name
        try:
            res = self.es.search_data(index_name, body_dict)

            hits = res['hits']['hits']
            if len(hits) == 0:
                return 'all'
            else:
                return hits[0]['_source']['rel_question_tup_id']
        except Exception as e:
            return 'all'

    def search_es_question(self, input_text, size=10, mini_score=1.4):

        emb_data = KnowledgeController(self.corpid).create_embeddings(input_text, self.corpid, self.default_model_path)
        vector = emb_data['vector']
        body_dict = {
            "_source": {
                "includes": ["rel_question_tup_id", "question_name", "question_type_name", "description"]
            },
            "query":
                {"script_score": {
                    "query": {
                        "bool": {
                            "must": [
                                {
                                    "term": {
                                        "delete_flag": {
                                            "value": 0
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "min_score": mini_score,
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
                        "params": {
                            "query_vector": vector

                        }
                    }
                }
                },
            "size": size
        }
        index_name = self.question_index_name
        try:
            hits = self.es.search_data(index_name, body_dict)['hits']['hits']
            return hits
        except Exception as e:
            return []

    def search_es_qpp_last(self):

        body_dict = {
            "_source": {
                "includes": ["qpp_id", "add_time", "character_count", "description", "delete_flag"]
            },
            "query": {
                "match_all": {}
            },
            "sort": [
                {
                    "qpp_id": {
                        "order": "desc"
                    }
                }
            ],
            "size": 1
        }
        index_name = self.question_point_plan_index_name
        try:
            res = self.es.search_data(index_name, body_dict)

            hits = res['hits']['hits']
            if len(hits) == 0:
                return 'all'
            else:
                return hits[0]['_source']['qpp_id']
        except Exception as e:
            return 'all'

