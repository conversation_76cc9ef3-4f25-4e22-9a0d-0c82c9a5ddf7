ALTER TABLE `aigc_app`
  ADD COLUMN `app_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1-普通应用 2-上级应用',
  ADD COLUMN `classify_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '命中关键词',
  ADD COLUMN `classify_prompt` text COLLATE utf8mb4_general_ci COMMENT '分类提示词',
  ADD COLUMN `classify_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分类名称',
  ADD COLUMN `classify_priority` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0-手动，1-向量，2-大模型',
  ADD COLUMN `ques_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '问题提取开启',
  ADD COLUMN `ques_prompt` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '问题提取描述',
  ADD COLUMN `ques_keywords` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '问题提取关键词';


DROP TABLE IF EXISTS `rel_aigc_app`;
CREATE TABLE `rel_aigc_app` (
  `rel_id` bigint NOT NULL COMMENT '关联ID',
  `app_id_entry` bigint NOT NULL COMMENT '入口ID',
  `app_id` bigint NOT NULL COMMENT '应用ID',
  `tp_user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`rel_id`),
  KEY `rel_aigc_app_aigc_app_FK` (`app_id`),
  KEY `rel_aigc_app_aigc_app_FK_1` (`app_id_entry`),
  CONSTRAINT `rel_aigc_app_aigc_app_FK` FOREIGN KEY (`app_id`) REFERENCES `aigc_app` (`app_id`),
  CONSTRAINT `rel_aigc_app_aigc_app_FK_1` FOREIGN KEY (`app_id_entry`) REFERENCES `aigc_app` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Aigc 应用层级关系表';

UPDATE aigc_app SET classify_name = '公司-内网发文', classify_prompt = '回答公司的新闻，会议记录等信息查询问题。', classify_target = '主持,会议' WHERE app_id = '4942802632512114689';
UPDATE aigc_app SET classify_name = '公司-机构信息', classify_prompt = '回答公司相关信息查询问题。例如公司名称、公司总部地址,注册资本，组织架构，高管分工, 机构的人员统计等公司相关的信息' WHERE app_id = '4940616791962423297';
UPDATE aigc_app SET classify_name = '公司-国投志', classify_prompt = '回答公司发展历程，公司战略，战略演进，公司管理(安全发展，资本运营等各维度管理等)，公司业务，大事记等相关问题。', classify_target = '国投志' WHERE app_id = '4944203017780989953';
UPDATE aigc_app SET classify_name = '公司-同事信息', classify_prompt = '回答员工公司内信息查询问题。例如性别、年龄，出生地，工位、职级，职位，在职时间，邮箱，在职状态等信息。', classify_target = '工位,办公电话,办公室' WHERE app_id = '4940132339494686721';
UPDATE aigc_app SET classify_name = '公司-内部信息', classify_prompt = '回答国投云网内部信息查询。能够查询领导分工、公司制度、规章制度、公司文化等信息。' WHERE app_id = '4943362622809444353';
UPDATE aigc_app SET classify_name = '公司-员工履历', classify_prompt = '回答员工简历相关问题：例如毕业院校，曾任职履历等信息， 主要是曾经经历和贡献。' WHERE app_id = '4938415833111072769';
UPDATE aigc_app SET classify_name = '公司-科研助手', classify_prompt = '回答公司科研相关问题。例如：科技创新管理涉及人才培养、平台建设、激励措施、会计核算、专利保护等多方面，旨在提升科技实力与员工动力。' WHERE app_id = '4944543228427767809';
UPDATE aigc_app SET classify_name = '公司-领导行程', classify_prompt = '回答领导的行程问题。', classify_target = '行程' WHERE app_id = '4944589342132670465';
UPDATE aigc_app SET classify_name = '服务-差旅服务', classify_prompt = '回答差旅信息查询问题。能够查询差旅政策、差旅报销等信息。' WHERE app_id = '4940612712737542145';
UPDATE aigc_app SET classify_name = '服务-餐饮服务', classify_prompt = '回答餐厅信息查询问题。能够查询餐厅菜单、餐厅位置等信息。' WHERE app_id = '4942783044479094785';
UPDATE aigc_app SET classify_name = '服务-其他', classify_prompt = '回答公司服务，员工服务, 各种服务热线，帮助文档等问题。' WHERE app_id = '4942743862184513537';
UPDATE aigc_app SET classify_name = '官网-工作安排', classify_prompt = '回答官网的工作安排问题。' WHERE app_id = '4947440735864492033';
UPDATE aigc_app SET classify_name = '官网-资讯浏览', classify_prompt = '回答官网的新闻问题。' WHERE app_id = '4947439450599723009';
UPDATE aigc_app SET classify_name = '官网-专题学习', classify_prompt = '回答官网的专题学习问题。' WHERE app_id = '4947437536524898305';
UPDATE aigc_app SET classify_name = '官网-服务工具', classify_prompt = '回答官网的物业服务问题。', classify_target = '物业' WHERE app_id = '4947435546315067393';
UPDATE aigc_app SET classify_name = '官网-制度规范', classify_prompt = '回答官网的制度规范相关的问题。' WHERE app_id = '4947438596572319745';
UPDATE aigc_app SET classify_name = '业务协同', classify_prompt = '回答官网的业务协同问题。' WHERE app_id = '4947441039431438337';
UPDATE aigc_app SET classify_name = '党建与组织', classify_prompt = '回答官网的党建与组织问题。' WHERE app_id = '4947436598569472001';
UPDATE aigc_app SET classify_name = '国际交流', classify_prompt = '回答官网的国际交流问题。' WHERE app_id = '4947433933034754049';
UPDATE aigc_app SET classify_name = '廉洁自律监督', classify_prompt = '回答官网的廉洁自律监督问题。' WHERE app_id = '4947441498120523777';
UPDATE aigc_app SET classify_name = '资料获取', classify_prompt = '回答官网的资料获取问题。' WHERE app_id = '4947437022898819073';

INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477713764353', '4941912165801005057', '4940616791962423297', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477722152961', '4941912165801005057', '4940132339494686721', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477726347265', '4941912165801005057', '4940612712737542145', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477734735873', '4941912165801005057', '4942783044479094785', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477747318785', '4941912165801005057', '4942743862184513537', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477755707393', '4941912165801005057', '4944203017780989953', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477759901697', '4941912165801005057', '4944543228427767809', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477764096001', '4941912165801005057', '4947440735864492033', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477768290305', '4941912165801005057', '4947439450599723009', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477776678913', '4941912165801005057', '4947437536524898305', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477780873217', '4941912165801005057', '4947435546315067393', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477785067521', '4941912165801005057', '4947438596572319745', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477793456129', '4941912165801005057', '4947441039431438337', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477806039041', '4941912165801005057', '4947436598569472001', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477818621953', '4941912165801005057', '4947433933034754049', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477822816257', '4941912165801005057', '4947441498120523777', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477827010561', '4941912165801005057', '4947437022898819073', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477831204865', '4942821944895475713', '4940616791962423297', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477835399169', '4942821944895475713', '4940132339494686721', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477839593473', '4942821944895475713', '4943362622809444353', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477843787777', '4942821944895475713', '4940612712737542145', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477847982081', '4942821944895475713', '4942783044479094785', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477852176385', '4942821944895475713', '4942743862184513537', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477856370689', '4942821944895475713', '4944203017780989953', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477860564993', '4942821944895475713', '4944543228427767809', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477864759297', '4942821944895475713', '4944589342132670465', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477868953601', '4942821944895475713', '4947440735864492033', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477873147905', '4942821944895475713', '4947439450599723009', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477877342209', '4942821944895475713', '4947437536524898305', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477881536513', '4942821944895475713', '4947435546315067393', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477885730817', '4942821944895475713', '4947438596572319745', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477889925121', '4942821944895475713', '4947441039431438337', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477894119425', '4942821944895475713', '4947436598569472001', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477898313729', '4942821944895475713', '4947433933034754049', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477902508033', '4942821944895475713', '4947441498120523777', '6');
INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('4951080477906702337', '4942821944895475713', '4947437022898819073', '6');


ALTER TABLE `jusure_pro_2`.`aigc_app`
ADD COLUMN `ques_replace` json NULL COMMENT '问题替换描述' AFTER `ques_keywords`;

ALTER TABLE `jusure_pro_2`.`aigc_app`
ADD COLUMN `ques_enhance` varchar(100) NULL COMMENT '问题增强方案' AFTER `ques_replace`;


