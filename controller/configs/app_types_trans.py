import yaml

from utils.tools import get_snowflake_id

# 从文件读取 YAML 数据
yaml_file_path = './app_types.yaml'
with open(yaml_file_path, 'r', encoding='utf-8') as file:
    data = yaml.safe_load(file)

# 构建 app_ids 字典以便查找
app_id_lookup = {app['id']: app['app_id'] for app in data['app_ids']}

# 存放生成的 SQL 语句
sql_statements = []

# 遍历 app_ids 并生成 UPDATE SQL
for app in data['app_ids']:
    app_id = app['app_id']
    title = app['title']
    target = ','.join(app['target']) if app['target'] else ""
    llm_prompt = app['llm_prompt']

    # 构造 UPDATE SQL 语句
    sql = f"UPDATE aigc_app SET classify_name = '{title}', classify_prompt = '{llm_prompt}'"
    if target:
        sql += f", classify_target = '{target}'"
    sql += f" WHERE app_id = '{app_id}';"
    sql_statements.append(sql)

# 固定操作人 ID
user_id = "6"

# 遍历 entry_ids 并生成 INSERT SQL
for entry in data['entry_ids']:
    app_id_entry = entry['app_id']
    for detail_id in entry['detail_app_ids']:
        # 获取 detail_id 对应的 app_id
        app_id = app_id_lookup.get(detail_id)
        if app_id:  # 如果找到对应的 app_id
            rel_id = get_snowflake_id()

            # 构造 INSERT SQL 语句
            sql = f"INSERT INTO rel_aigc_app (rel_id, app_id_entry, app_id, tp_user_id) VALUES ('{rel_id}', '{app_id_entry}', '{app_id}', '{user_id}');"
            sql_statements.append(sql)

# 将生成的 SQL 语句写入文件
sql_file_path = './app_type.sql'
with open(sql_file_path, 'w', encoding='utf-8') as file:
    file.write('\n'.join(sql_statements))

print(f"SQL statements have been written to {sql_file_path}")