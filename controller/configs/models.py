# coding: utf-8
from sqlalchemy import BigInteger, Check<PERSON>onstraint, Column, DECIMAL, Date, DateTime, Float, ForeignKey, Index, Integer, JSON, SmallInteger, String, TIMESTAMP, Table, Text, text
from sqlalchemy.dialects.mysql import BIGINT, CHAR, ENUM, INTEGER, LONGTEXT, MEDIUMTEXT, TEXT, TINYINT, VARCHAR
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class AdApp(Base):
    __tablename__ = 'ad_apps'
    __table_args__ = {'comment': '投放产品表'}

    app_id = Column(BigInteger, primary_key=True, comment='产品ID')
    app_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='产品名称')
    status = Column(TINYINT, nullable=False, server_default=text("'1'"), comment='产品状态 1：启用 0:禁用')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')


class AgentAgentFavorite(Base):
    __tablename__ = 'agent_agent_favorite'
    __table_args__ = {'comment': '收藏数据'}

    id = Column(Integer, primary_key=True)
    agent_id = Column(Integer, nullable=False)
    user_id = Column(String(64, 'utf8mb4_general_ci'), nullable=False)
    created_time = Column(DateTime)
    updated_time = Column(DateTime)


class AgentAgentLike(Base):
    __tablename__ = 'agent_agent_like'

    id = Column(Integer, primary_key=True)
    user_id = Column(String(64, 'utf8mb4_general_ci'), nullable=False)
    agent_id = Column(Integer, nullable=False)
    created_time = Column(DateTime)
    updated_time = Column(DateTime)


class AgentScene(Base):
    __tablename__ = 'agent_scene'
    __table_args__ = {'comment': '智能体场景表'}

    scene_id = Column(Integer, primary_key=True, comment='场景ID')
    scene_name = Column(VARCHAR(255), comment='场景名称')
    default_question = Column(VARCHAR(255), comment='默认问题')
    created_time = Column(DateTime, nullable=False)
    updated_time = Column(DateTime, nullable=False)
    delete_flag = Column(TINYINT(1), comment='删除状态')
    user_id = Column(VARCHAR(64), nullable=False, comment='用户id')


class AgentTool(Base):
    __tablename__ = 'agent_tool'
    __table_args__ = {'comment': '智能体组件表'}

    tool_id = Column(Integer, primary_key=True, comment='组件ID')
    tool_name = Column(VARCHAR(255), comment='组件名称')
    tool_logo = Column(VARCHAR(255), comment='组件logo')
    tool_url = Column(VARCHAR(255), comment='组件URL')
    tool_desc = Column(TEXT, comment='组件描述')
    tool_debug = Column(Integer, comment='调试状态,0:未调试 1:通过 2:调试未通过')
    tool_used = Column(TINYINT(1), comment='是否启用 0:未启用 1:启用')
    tool_status = Column(TINYINT(1), comment='是否发布 0:未发布 1:发布')
    tool_type = Column(Integer, comment='工具组件：0 算法组件：1 分析算法：2')
    user_id = Column(VARCHAR(64), nullable=False, comment='用户id')
    tool_auth = Column(Integer, comment='授权方式 0:不需要授权 1：Service 2:oauth_standard')
    tool_star = Column(TINYINT(1), comment='收藏状态')
    delete_flag = Column(TINYINT(1), comment='删除状态')
    created_time = Column(DateTime, nullable=False)
    updated_time = Column(DateTime, nullable=False)


class AiQuesionNumber(Base):
    __tablename__ = 'ai_quesion_number'
    __table_args__ = {'comment': '智能问数表'}

    ai_question_number_id = Column(BigInteger, primary_key=True, comment='Ai问数ID')
    data_name = Column(VARCHAR(255), comment='数据名称')
    component_code = Column(VARCHAR(255), comment='组件名称')
    c = Column(VARCHAR(255), comment='数据使用类 class')
    m = Column(VARCHAR(255), comment='数据使用方法 method')
    w = Column(VARCHAR(255), comment='数据过滤条件 where')
    f = Column(VARCHAR(255), comment='Filter 过滤')
    uri = Column(VARCHAR(255), comment='接口地址')
    r = Column(VARCHAR(255), comment='请求方式')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='0:未删除 1：已删除')
    description = Column(VARCHAR(255), comment='描述')
    comment = Column(VARCHAR(255), comment='组件的详细介绍')
    analysis_prompts = Column(VARCHAR(500), comment='智能分析提示词')
    after_question = Column(VARCHAR(1500), server_default=text("''"), comment='追问数据')
    flag_after_question = Column(VARCHAR(1), server_default=text("'0'"), comment='是否显示追问按钮')
    drill_item = Column(VARCHAR(255), comment='显示下钻维度数据')


class AigcKnowledgeGraph(Base):
    __tablename__ = 'aigc_knowledge_graph'

    id = Column(BigInteger, primary_key=True)
    knowledge_id = Column(BigInteger, nullable=False)
    status = Column(Integer, nullable=False, server_default=text("'0'"))
    count_total = Column(Integer)
    count_done = Column(Integer)
    tp_user_id = Column(String(100, 'utf8mb4_general_ci'), comment='添加人')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    count_node = Column(Integer)
    count_edge = Column(Integer)
    labels = Column(String(1000, 'utf8mb4_general_ci'))
    build_context = Column(Text(collation='utf8mb4_general_ci'))
    plan_processed = Column(Integer, server_default=text("'0'"))
    already_processed = Column(Integer, server_default=text("'0'"))
    already_entities = Column(Integer, server_default=text("'0'"))
    already_relations = Column(Integer, server_default=text("'0'"))


class AigcQa(Base):
    __tablename__ = 'aigc_qa'
    __table_args__ = {'comment': '知识库表'}

    qa_id = Column(BigInteger, primary_key=True, comment='qaID')
    qa_name = Column(VARCHAR(255), comment='qa名称')
    qa_desc = Column(VARCHAR(255), comment='知识库描述')
    status = Column(TINYINT(1), server_default=text("'1'"), comment='1：启用 0：停用')
    tp_user_id = Column(VARCHAR(36), index=True, comment='添加人')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    icon_url = Column(VARCHAR(255), comment='Icon url')


class AigcReport(Base):
    __tablename__ = 'aigc_report'
    __table_args__ = {'comment': '智能报告配置表'}

    report_id = Column(Integer, primary_key=True, comment='报告菜单ID')
    app_id = Column(BigInteger, comment='应用ID')
    title = Column(VARCHAR(255), comment='标题')
    code = Column(VARCHAR(255), comment='编号')
    level = Column(Integer, comment='级别')
    content_prompt = Column(VARCHAR(1000), comment='文本内容提示词')
    content = Column(VARCHAR(1000), comment='文本内容')
    content_charts = Column(VARCHAR(1000), comment='图表内容')
    charts_type = Column(VARCHAR(255), comment='图片类型')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    is_first = Column(TINYINT(1), server_default=text("'0'"), comment='是否第一个 1：是 0：否')
    sort = Column(Integer, server_default=text("'0'"), comment='排序')
    data_type = Column(VARCHAR(100), comment='数据类型')
    chatbi_data = Column(VARCHAR(500), comment='关联chatbi data_name')
    is_charts = Column(TINYINT(1), server_default=text("'0'"), comment='是否有图表 1：有 0：没有')


class ApproveTemplate(Base):
    __tablename__ = 'approve_template'
    __table_args__ = {'comment': '素材审批模板表'}

    template_id = Column(VARCHAR(64), primary_key=True)
    approve_rule = Column(VARCHAR(1000), comment='json,流程规则')
    template_name = Column(VARCHAR(255), comment='审批模板名称')
    template_info = Column(VARCHAR(1000), comment='模板信息，json数据')
    departm_ids = Column(VARCHAR(255), comment='企业微信部门id，触发条件')
    tags_ids = Column(VARCHAR(1000), comment='对应触发的标签')
    common_flag = Column(VARCHAR(1), comment='1:通用触发流程；\\n0:非通用触发流程')


class ChannelGroup(Base):
    __tablename__ = 'channel_group'
    __table_args__ = {'comment': '渠道组表'}

    channel_group_id = Column(VARCHAR(64), primary_key=True)
    group_name = Column(VARCHAR(255), comment='渠道组名称')
    group_sn = Column(Integer, comment='渠道组排序')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='删除标志位：0:未删除；1:已删除')


class ChatbiDataGraphType(Base):
    __tablename__ = 'chatbi_data_graph_type'
    __table_args__ = {'comment': '视图分类表'}

    graph_type_id = Column(VARCHAR(255), primary_key=True)
    graph_type_name = Column(VARCHAR(63), comment='分类名称')
    tp_user_id = Column(VARCHAR(255), comment='创建者id')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    sort = Column(SmallInteger, server_default=text("'0'"), comment='排序')


class ChatbiDictAggStyle(Base):
    __tablename__ = 'chatbi_dict_agg_style'
    __table_args__ = {'comment': '数据聚合方式字典表'}

    agg_style_id = Column(Integer, primary_key=True, comment='聚合方式ID')
    agg_style_name = Column(VARCHAR(255), comment='聚合方式名称')
    agg_style_code = Column(VARCHAR(100), comment='聚合方式编码')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    sql_style_code = Column(VARCHAR(100), comment='Sql聚合函数')


class ChatbiDictDataFormat(Base):
    __tablename__ = 'chatbi_dict_data_format'
    __table_args__ = {'comment': '数据展示样式字典表'}

    data_format_id = Column(Integer, primary_key=True, comment='数据展示方式ID')
    data_format = Column(VARCHAR(50), comment='数据展示方式')
    data_format_name = Column(VARCHAR(255), comment='数据展示方式名称')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')


class ChatbiDictDbType(Base):
    __tablename__ = 'chatbi_dict_db_type'
    __table_args__ = {'comment': '数据库类型字典表'}

    db_type_id = Column(Integer, primary_key=True, comment='数据库类型ID')
    db_type_name = Column(VARCHAR(100), comment='数据类型名称')
    icon_url = Column(VARCHAR(255), comment='icon')
    sort = Column(Integer, comment='排序')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')


class ChatbiDictDrillDownType(Base):
    __tablename__ = 'chatbi_dict_drill_down_type'
    __table_args__ = {'comment': '下钻类型配置表'}

    drill_down_type_id = Column(Integer, primary_key=True, comment='下钻类型ID')
    drill_down_name = Column(VARCHAR(100), comment='下钻类型名称')
    drill_down_code = Column(VARCHAR(255), comment='下钻类型code')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')


class ChatbiDictFilterType(Base):
    __tablename__ = 'chatbi_dict_filter_type'
    __table_args__ = {'comment': 'Chatbi 图表过滤条件字典表'}

    filter_type_id = Column(Integer, primary_key=True, comment='过滤条件类型ID')
    filter_type_name = Column(VARCHAR(255), comment='过滤条件名称')
    filter_type_code = Column(VARCHAR(255), comment='过滤条件编码')
    sort = Column(Integer, comment='排序')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')


class ChatbiDictGraphEmpty(Base):
    __tablename__ = 'chatbi_dict_graph_empty'
    __table_args__ = {'comment': 'Chatbi 图表空数据展示字典表'}

    empty_id = Column(Integer, primary_key=True, comment='空样式ID')
    empty_name = Column(VARCHAR(255), comment='展示名称')
    empty_code = Column(VARCHAR(255), comment='展示code')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')


class ChatbiDictGraphSetting(Base):
    __tablename__ = 'chatbi_dict_graph_setting'
    __table_args__ = {'comment': '图表设置类型字典表'}

    setting_type_id = Column(Integer, primary_key=True, comment='图表设置类型ID')
    setting_type_name = Column(VARCHAR(100), comment='图表设置类型名称')
    setting_type_code = Column(VARCHAR(100), comment='图表设置类型code')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    field_number = Column(Integer, comment='允许字段数量')
    rel_dict = Column(VARCHAR(255), comment='关联字典')
    is_default = Column(TINYINT(1), server_default=text("'0'"), comment='是否默认显示 1：是 0：否')
    is_required = Column(TINYINT(1), server_default=text("'0'"), comment='是否必须 1：是 0：否')


class ChatbiDictGraphType(Base):
    __tablename__ = 'chatbi_dict_graph_type'
    __table_args__ = {'comment': '图表类型字典'}

    graph_type_id = Column(Integer, primary_key=True, comment='图表类型ID')
    graph_type_name = Column(VARCHAR(100), comment='图表类型名称')
    icon_url = Column(VARCHAR(255), comment='图表icon')
    sort = Column(Integer, comment='排序')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    graph_type_code = Column(VARCHAR(100), comment='图表类型编码')


class ChatbiSession(Base):
    __tablename__ = 'chatbi_session'
    __table_args__ = {'comment': 'chatbi会话记录表'}

    session_id = Column(BigInteger, primary_key=True, comment='会话ID')
    session_name = Column(VARCHAR(255), comment='会话名称')
    tp_user_id = Column(VARCHAR(36), comment='用户ID')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    is_top = Column(TINYINT(1), server_default=text("'0'"), comment='是否置顶 1：是 0：否')


class ClientGroupConfig(Base):
    __tablename__ = 'client_group_config'
    __table_args__ = {'comment': '客户群配置-便于企业微信生成'}

    client_group_config_id = Column(VARCHAR(64), primary_key=True)
    scene = Column(VARCHAR(1), comment='场景。1 - 群的小程序插件;2 - 群的二维码插件\\n')
    remark = Column(VARCHAR(255), comment='联系方式的备注信息，用于助记')
    state = Column(VARCHAR(255), comment='企业自定义的state参数，用于区分不同的入群渠道。不超过30个UTF-8字符')
    qr_code = Column(VARCHAR(255), comment='联系二维码的URL或小程序插件的URL')
    config_id = Column(VARCHAR(64), comment='配置id')
    add_user_id = Column(VARCHAR(64), comment='添加人')
    add_time = Column(DateTime, comment='添加时间')
    delete_flag = Column(VARCHAR(1), comment='删除标志位；0:未删除；1:已删除')


class ClientOpinion(Base):
    __tablename__ = 'client_opinion'
    __table_args__ = {'comment': '客户吐槽表（建议表）'}

    opinion_id = Column(BigInteger, primary_key=True, comment='建议ID')
    client_id = Column(VARCHAR(64), nullable=False, server_default=text("''"), comment='客户ID')
    star = Column(Integer, nullable=False, server_default=text("'5'"), comment='星')
    tags_ids = Column(VARCHAR(1000), nullable=False, server_default=text("''"), comment='标签ids')
    content = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='内容')
    img_url1 = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='图片1')
    img_url2 = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='图片2')
    img_url3 = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='图片3')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    reply = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='回复内容')
    reply_time = Column(DateTime, comment='回复时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0：未删除 1：已删除')
    work_id = Column(Integer, nullable=False, server_default=text("'0'"), comment='work平台id')


class ComProperty(Base):
    __tablename__ = 'com_property'

    com_property_id = Column(VARCHAR(64), primary_key=True)
    property_name = Column(VARCHAR(255), comment='熟悉名称')


class ComType(Base):
    __tablename__ = 'com_type'

    com_type_id = Column(VARCHAR(64), primary_key=True)
    com_type_name = Column(VARCHAR(255))
    com_level = Column(VARCHAR(1), comment='组件等级；1级无属性；2级有属性；')
    father_type_id = Column(VARCHAR(64), comment='组件父id')
    com_code = Column(VARCHAR(255), comment='组件的code')
    com_content = Column(TEXT, comment='具体组件的json数据')


class Company(Base):
    __tablename__ = 'company'
    __table_args__ = {'comment': '客户所属公司(个人)'}

    company_id = Column(VARCHAR(64), primary_key=True, comment='公司id')
    company_name = Column(VARCHAR(255), comment='公司名称')
    short_name = Column(VARCHAR(255), comment='公司短名称')
    tyc_status = Column(VARCHAR(1), server_default=text("'0'"), comment='天眼查状态 0：默认；1：更新成功； 2：未查询到信息')


class ConfAppId(Base):
    __tablename__ = 'conf_app_ids'

    id = Column(Integer, nullable=False)
    app_id = Column(String(100, 'utf8mb4_general_ci'), primary_key=True)
    title = Column(String(100, 'utf8mb4_general_ci'))
    target = Column(VARCHAR(1000))
    prompt = Column(Text(collation='utf8mb4_general_ci'))


class ConfEntryId(Base):
    __tablename__ = 'conf_entry_ids'

    name = Column(String(100, 'utf8mb4_general_ci'), nullable=False)
    app_id = Column(String(100, 'utf8mb4_general_ci'), primary_key=True)
    title = Column(String(100, 'utf8mb4_general_ci'), nullable=False)
    detail_app_ids = Column(String(200, 'utf8mb4_general_ci'))


class ConfigBonusClient(Base):
    __tablename__ = 'config_bonus_client'
    __table_args__ = {'comment': '客户激励配置'}

    bonus_client_id = Column(VARCHAR(64), primary_key=True)
    bonus_client_name = Column(VARCHAR(255), comment='客户激励名称')


class ConfigBonusTpu(Base):
    __tablename__ = 'config_bonus_tpu'
    __table_args__ = {'comment': '用户激励配置表'}

    bonus_tpu_id = Column(VARCHAR(64), primary_key=True, comment='触点用户激励配置id')
    bonus_tpu_name = Column(VARCHAR(255), comment='触点用户激励配置名称')


class ConfigMonitor(Base):
    __tablename__ = 'config_monitor'
    __table_args__ = {'comment': '监测配置表'}

    monitor_id = Column(VARCHAR(64), primary_key=True, comment='监控配置id')
    monitor_name = Column(VARCHAR(255), comment='监控配置名称')


class ContentAuthScheme(Base):
    __tablename__ = 'content_auth_scheme'
    __table_args__ = {'comment': '内容权限方案'}

    auth_scheme_id = Column(VARCHAR(64), primary_key=True, comment='权限方案id')
    scheme_name = Column(VARCHAR(255), comment='权限方案名称')
    create_time = Column(DateTime)
    modify_time = Column(DateTime, comment='修改时间')
    show_flag = Column(VARCHAR(1), server_default=text("'1'"), comment='是否展示在方案列表；0:不展示；1:展示')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='删除标志位；0:未删除；1:删除')


class ContentGroup(Base):
    __tablename__ = 'content_group'
    __table_args__ = {'comment': '素材组表'}

    group_id = Column(VARCHAR(64), primary_key=True)
    group_name = Column(VARCHAR(255), comment='素材组名称')
    group_code = Column(VARCHAR(64), comment='素材code')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='删除标志位；0:未删除；1:已删除')
    cover_pic_url = Column(VARCHAR(1000), comment='封面图片oss地址')


class ContentInfoOcean(Base):
    __tablename__ = 'content_info_ocean'
    __table_args__ = {'comment': '巨量广告素材相关表'}

    content_id = Column(VARCHAR(64), primary_key=True)
    advertiser_id = Column(BigInteger, comment='广告主ID')
    id = Column(VARCHAR(64), comment='视频or图片ID')
    material_id = Column(BigInteger, comment='素材ID')
    source = Column(VARCHAR(100), comment='素材来源')
    create_time = Column(DateTime, comment='素材创建时间')
    labels = Column(VARCHAR(255), comment='标签')
    aigc = Column(TINYINT, comment='是否aigc生成 1:是 0否')


class ContentLevel(Base):
    __tablename__ = 'content_level'
    __table_args__ = {'comment': '素材等级表'}

    level_id = Column(VARCHAR(64), primary_key=True)
    level_name = Column(VARCHAR(255), comment='素材等级名称')
    level_commet = Column(VARCHAR(255), comment='等级备注，描述')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='删除标志位')
    sn = Column(Integer, comment='排序序号')


class DictAigcSubject(Base):
    __tablename__ = 'dict_aigc_subject'
    __table_args__ = {'comment': 'Aigc主题类型'}

    aigc_subject_type_id = Column(VARCHAR(64), primary_key=True, comment='aigc的主题类型id')
    subject_name = Column(VARCHAR(255), comment='主题类型名称')


class DictAigcType(Base):
    __tablename__ = 'dict_aigc_type'
    __table_args__ = {'comment': 'Aigc类型字典'}

    aigc_type_id = Column(VARCHAR(64), primary_key=True, comment='aigc模型类型id')
    aigc_type_name = Column(VARCHAR(255), comment='aigc模型名称')
    api_key = Column(VARCHAR(255))
    secret_key = Column(VARCHAR(255))


class DictChannelType(Base):
    __tablename__ = 'dict_channel_type'

    channel_type_id = Column(VARCHAR(64), primary_key=True)
    channel_type_name = Column(VARCHAR(255), comment='渠道类型名称')
    state_show_flag = Column(VARCHAR(1), comment='是否需要输入，state_code和渠道补充内容')
    place_holder = Column(VARCHAR(255), comment='渠道补充内容的place_holder')


class DictClientIndustry(Base):
    __tablename__ = 'dict_client_industry'
    __table_args__ = {'comment': '客户行业表'}

    dict_client_industry_id = Column(VARCHAR(64), primary_key=True)
    industry_name = Column(VARCHAR(255), comment='行业名称')
    industry_desc = Column(VARCHAR(500), comment='行业名称描述')
    out_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='对外显示标志位')


class DictClientIntention(Base):
    __tablename__ = 'dict_client_intention'
    __table_args__ = {'comment': '意向合作方向字典表'}

    intention_id = Column(Integer, primary_key=True, comment='意向ID')
    intention_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='意向名称')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0：未删除 1：已删除')


class DictClientLevel(Base):
    __tablename__ = 'dict_client_level'
    __table_args__ = {'comment': '客户等级字典表'}

    level_id = Column(Integer, primary_key=True, comment='客户级别')
    level_name = Column(VARCHAR(255), nullable=False, comment='等级名称')
    level_content = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='等级说明')
    level_url = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='等级图片')
    min_value = Column(Integer, nullable=False, server_default=text("'0'"), comment='最小值')
    max_value = Column(Integer, nullable=False, server_default=text("'0'"), comment='最大值')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0 :未删除 1：已删除')
    level_color = Column(VARCHAR(10), nullable=False, server_default=text("''"), comment='颜色值')
    level_bg_url = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='背景图')
    level_type = Column(TINYINT, nullable=False, server_default=text("'1'"), comment='1:普通类型 2：合伙人类型')
    bg_url = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='bg_url')
    btn_url = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='btn_url')
    bar_color = Column(VARCHAR(10), nullable=False, server_default=text("''"), comment='bar_color')
    landpage_id = Column(VARCHAR(64), comment='关联落地页id，提交线索使用')


class DictClientMaxLearn(Base):
    __tablename__ = 'dict_client_max_learn'
    __table_args__ = {'comment': '最高学历字典表'}

    max_learn_id = Column(VARCHAR(32), primary_key=True, comment='最高学历ID')
    max_learn_name = Column(VARCHAR(255), comment='最高学历名称')
    sort = Column(Integer, server_default=text("'9999'"), comment='排序')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='0：未删除 1：已删除')


t_dict_client_node = Table(
    'dict_client_node', metadata,
    Column('node_id', Integer, nullable=False),
    Column('node_pid', Integer, comment='父id'),
    Column('node_name', VARCHAR(255), comment='客户节点名称'),
    comment='Work平台客户节点表'
)


class DictClientPolitical(Base):
    __tablename__ = 'dict_client_political'
    __table_args__ = {'comment': '政治面貌字典表'}

    political_id = Column(VARCHAR(32), primary_key=True, comment='政治面貌ID')
    political_name = Column(VARCHAR(255), comment='名称')
    sort = Column(Integer, server_default=text("'9999'"), comment='排序')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='0：未删除 1：已删除')


class DictClueMaturity(Base):
    __tablename__ = 'dict_clue_maturity'
    __table_args__ = {'comment': '线索成熟度字典表'}

    clue_maturity_id = Column(VARCHAR(64), primary_key=True, comment='线索成熟度id')
    maturity_level = Column(Integer, comment='成熟等级')
    level_name = Column(VARCHAR(255), comment='成熟等级名称')
    level_code = Column(VARCHAR(50), comment='成熟度等级英文')
    level_desc = Column(VARCHAR(255), comment='成熟度等级描述')


class DictClueSource(Base):
    __tablename__ = 'dict_clue_source'
    __table_args__ = {'comment': '线索来源字典表'}

    clue_source_id = Column(VARCHAR(64), primary_key=True, comment='线索来源id')
    source_name = Column(VARCHAR(255), comment='线索来源名称')
    sn = Column(Integer, comment='序号')


class DictClueStage(Base):
    __tablename__ = 'dict_clue_stage'
    __table_args__ = {'comment': '客户线索所在阶段'}

    clue_stage_id = Column(VARCHAR(64), primary_key=True, comment='线索所在的阶段id')
    stage_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='阶段名称')
    stage_desc = Column(VARCHAR(255), comment='阶段描述')
    stage_sn = Column(Integer, comment='阶段顺序')


class DictColorTheme(Base):
    __tablename__ = 'dict_color_theme'
    __table_args__ = {'comment': '配色方案字典表'}

    color_theme_id = Column(VARCHAR(64), primary_key=True)
    back_ground_color = Column(VARCHAR(255), comment='rgba表示的背景颜色')
    font_ground_color = Column(VARCHAR(255), comment='rgba表示的字体颜色')
    theme_name = Column(VARCHAR(255), comment='配色方案名称')


class DictComponentApi(Base):
    __tablename__ = 'dict_component_api'

    com_api_id = Column(VARCHAR(64), primary_key=True)
    com_api_name = Column(VARCHAR(255), comment='api名称')
    com_api_url = Column(VARCHAR(255), comment='api的url链接')
    com_api_desc = Column(VARCHAR(255), comment='api对应的描述')
    valid_flag = Column(VARCHAR(1), comment='生效标志位')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='新增时间')


class DictComponentType(Base):
    __tablename__ = 'dict_component_type'
    __table_args__ = {'comment': '组件类型字典表'}

    com_type_id = Column(VARCHAR(64), primary_key=True)
    type_name = Column(VARCHAR(255), comment='组件类型名称')
    type_code = Column(VARCHAR(255), comment='组件code')


class DictContentSource(Base):
    __tablename__ = 'dict_content_source'
    __table_args__ = {'comment': '素材来源字典'}

    content_source_id = Column(VARCHAR(64), primary_key=True)
    source_name = Column(VARCHAR(255), comment='素材类型名称')
    source_input_flag = Column(VARCHAR(1), comment='是否需要录入其他素材来源；content_source_input')
    place_holder = Column(VARCHAR(255), comment='素材来源place holder')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='删除标志位')


class DictContentUse(Base):
    __tablename__ = 'dict_content_use'
    __table_args__ = {'comment': '素材内容使用范围表'}

    dict_content_use_id = Column(VARCHAR(64), primary_key=True)
    content_use = Column(VARCHAR(255))


class DictFissionType(Base):
    __tablename__ = 'dict_fission_type'
    __table_args__ = {'comment': '裂变类型字典表'}

    dict_fission_type_id = Column(VARCHAR(64), primary_key=True)
    fission_type_name = Column(VARCHAR(255), comment='裂变类型')
    color_pattern = Column(VARCHAR(255), comment='颜色模式：元组；(rgba(255,255,255,0), rgba(0, 0, 0, 1))')


class DictFunctionView(Base):
    __tablename__ = 'dict_function_view'
    __table_args__ = {'comment': '页面类型字典表'}

    function_view_type_id = Column(VARCHAR(64), primary_key=True)
    view_type_name = Column(VARCHAR(255))


class DictLoginSetting(Base):
    __tablename__ = 'dict_login_setting'
    __table_args__ = {'comment': '合伙人任务登录频次设置表'}

    login_setting_id = Column(Integer, primary_key=True)
    login_setting_name = Column(VARCHAR(255), nullable=False, comment='登录频次名称')
    login_number = Column(Integer, nullable=False, comment='登录频次天数')
    sort = Column(Integer, nullable=False, server_default=text("'999'"), comment='排序')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')


class DictMediaType(Base):
    __tablename__ = 'dict_media_type'

    dict_media_type_id = Column(VARCHAR(64), primary_key=True)
    media_type_name = Column(VARCHAR(255), comment='媒体类型名称')
    delete_flag = Column(VARCHAR(1), comment='是否删除；0：未删除；1：已删除')
    sn = Column(Integer, comment='媒体排序，正序')


class DictMiniInfo(Base):
    __tablename__ = 'dict_mini_info'
    __table_args__ = {'comment': '小程序信息维护字典表'}

    mini_info_id = Column(Integer, primary_key=True)
    field_name = Column(VARCHAR(255), comment='字段名称')
    field_type = Column(VARCHAR(255), comment='字段类型')
    status = Column(TINYINT, server_default=text("'1'"), comment='1:开启 0：关闭')
    field_code = Column(VARCHAR(255), comment='字段编码')
    field_dict = Column(VARCHAR(1000), comment='字段字典')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='0：未删除 1：已删除')
    sort = Column(Integer, server_default=text("'9999'"), comment='排序')
    is_required = Column(TINYINT, server_default=text("'0'"), comment='是否必须 1：是 0:否')
    dict_name = Column(VARCHAR(255), comment='对应字典表名')


class DictMiniPage(Base):
    __tablename__ = 'dict_mini_page'

    page_id = Column(VARCHAR(64), primary_key=True)
    page_url = Column(VARCHAR(255), nullable=False, comment='page的url链接地址')
    page_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='对应的page名称')


class DictOrgType(Base):
    __tablename__ = 'dict_org_type'
    __table_args__ = {'comment': '组织类型字典'}

    dict_org_type_id = Column(VARCHAR(64), primary_key=True)
    org_type_name = Column(VARCHAR(255), comment='组织类型名称')
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='数据创建记录')


class DictPartnerContract(Base):
    __tablename__ = 'dict_partner_contract'
    __table_args__ = {'comment': '合伙人合同模版表'}

    tmp_contract_id = Column(VARCHAR(64), primary_key=True, comment='合伙人合同模版ID')
    tmp_contract_name = Column(VARCHAR(255), comment='合同模版名称')
    tmp_contract_pdf = Column(VARCHAR(255), comment='合同模板pdf')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='0：未删除 1：已删除')


class DictPicResolution(Base):
    __tablename__ = 'dict_pic_resolution'
    __table_args__ = {'comment': '照片分辨率字典'}

    pic_resolution_id = Column(VARCHAR(64), primary_key=True, comment='图片分辨率_id')
    pic_resolution_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='图片分辨率名称')
    width_value = Column(Integer, nullable=False, server_default=text("'0'"), comment='图片宽度-分辨率值')
    height_value = Column(Integer, nullable=False, server_default=text("'0'"), comment='图片高度-分辨率值')
    resolution_desc = Column(VARCHAR(255), server_default=text("''"), comment='图片分辨率描述')


class DictPicStyle(Base):
    __tablename__ = 'dict_pic_style'
    __table_args__ = {'comment': '图片风格字典表'}

    pic_style_id = Column(VARCHAR(64), primary_key=True, comment='图片风格id')
    pic_style_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='图片风格名称')


class DictPicType(Base):
    __tablename__ = 'dict_pic_type'
    __table_args__ = {'comment': '图片类型字典'}

    pic_type_id = Column(VARCHAR(64), primary_key=True, comment='图片类型id')
    pic_type_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='图片类型名称')
    type_value = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='图片类型的值')


class DictPlanningAuthority(Base):
    __tablename__ = 'dict_planning_authority'
    __table_args__ = {'comment': '策划权限等级表'}

    dict_pa_flag_id = Column(VARCHAR(1), primary_key=True, server_default=text("''"), comment='策划权限id等级不会超过60种')
    pa_flag_name = Column(VARCHAR(255), comment='策划权限名称')


class DictProductType(Base):
    __tablename__ = 'dict_product_type'

    product_type_id = Column(VARCHAR(64), primary_key=True, server_default=text("''"), comment='销售产品类型id')
    product_type_name = Column(VARCHAR(255), comment='销售产品类型名称')
    product_type_content = Column(VARCHAR(500), comment='销售产品的内容，用英文逗号隔开类型')
    product_type_value = Column(VARCHAR(500), comment='产品的内容的value值')
    multi_select_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='0：单选；1：多选')
    product_type_code = Column(VARCHAR(50), comment='产品类型code')
    type_id = Column(VARCHAR(1), comment='产品类型id:1:erp;2:百雁;3:喜鹊;4:mes墨工湖;5:蜂王台')


class DictQrPosType(Base):
    __tablename__ = 'dict_qr_pos_type'

    qr_pos_type_id = Column(VARCHAR(64), primary_key=True)
    pos_type_name = Column(VARCHAR(255), comment='位置信息')
    default_x = Column(Integer, comment='默认x坐标')
    default_y = Column(Integer, comment='默认y坐标')
    default_width = Column(Integer, comment='默认宽度')
    default_height = Column(Integer, comment='默认高度')


class DictQuestionType(Base):
    __tablename__ = 'dict_question_type'
    __table_args__ = {'comment': '问题分类表'}

    question_type_id = Column(Integer, primary_key=True, comment='类型ID')
    pid = Column(Integer, comment='父级ID')
    question_type_name = Column(VARCHAR(255), comment='分类名称')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除1：已删除')
    answer_number = Column(Integer, nullable=False, server_default=text("'0'"), comment='回答数量')
    form = Column(TINYINT, nullable=False, server_default=text("'1'"), comment='类型：1：个人分类 2：标准分类')
    tp_user_id = Column(VARCHAR(64), comment='员工ID')
    industry_id = Column(VARCHAR(36), comment='客户行业ID')
    sort = Column(Integer, nullable=False, server_default=text("'9999'"))


class DictRegion(Base):
    __tablename__ = 'dict_region'
    __table_args__ = {'comment': '区域字典表'}

    region_id = Column(Integer, primary_key=True, comment='地区id')
    pid = Column(Integer, comment='地区父id')
    name = Column(VARCHAR(255), comment='区域名称')
    region_data = Column(VARCHAR(255), comment='区域')
    region_name = Column(VARCHAR(255), comment='区域全名称')


class DictServiceRole(Base):
    __tablename__ = 'dict_service_role'
    __table_args__ = {'comment': '服务角色字典表'}

    service_role_id = Column(Integer, primary_key=True, comment='服务角色ID')
    service_role_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='服务角色名称')
    service_role_content = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='服务角色介绍')


class DictTagsType(Base):
    __tablename__ = 'dict_tags_type'
    __table_args__ = {'comment': '客户标签类型字典'}

    type_name = Column(VARCHAR(255), comment='标签类型内容')
    father_type_id = Column(VARCHAR(64), comment='标签父id')
    tags_type_id = Column(VARCHAR(64), primary_key=True, index=True, comment='标签类型id')


class DictTargetingType(Base):
    __tablename__ = 'dict_targeting_type'
    __table_args__ = {'comment': '客户定向类型'}

    targeting_type_id = Column(VARCHAR(64), primary_key=True, comment='客户定向类型id')
    targeting_type_name = Column(VARCHAR(255), comment='客户定向类型名称')
    father_id = Column(VARCHAR(64), comment='客户定向父id')
    default_value = Column(VARCHAR(1000), comment='默认值')
    value_type = Column(VARCHAR(1), server_default=text("'0'"), comment='0:list,列表值；可多选；\\n1:连续值，取范围；如年龄：[35,60],意为35-60')


class DictTaskType(Base):
    __tablename__ = 'dict_task_type'
    __table_args__ = {'comment': '任务类型字典'}

    task_type_id = Column(Integer, primary_key=True, nullable=False, comment='类型id')
    type_code = Column(VARCHAR(10), primary_key=True, nullable=False, index=True, comment='类型code')
    type_name = Column(VARCHAR(255), comment='类型名称')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    delete_flag = Column(VARCHAR(10), server_default=text("'0'"), comment='是否删除 0 否； 1删除')


class DictTemplateFpType(Base):
    __tablename__ = 'dict_template_fp_type'
    __table_args__ = {'comment': '裂变策划计划模板类型表'}

    tfp_type_id = Column(VARCHAR(64), primary_key=True, comment='模板类型id')
    tfp_type_name = Column(VARCHAR(255), comment='模板类型名称')


class DictTghSetting(Base):
    __tablename__ = 'dict_tgh_setting'
    __table_args__ = {'comment': '共创素材AB级的智邦币数量设置表'}

    tgh_setting_id = Column(Integer, primary_key=True, comment='共创设置ID')
    level_name = Column(VARCHAR(255), comment='级别名称')
    zb_num = Column(Integer, comment='Zb币数量')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='0:未删除 1：已删除')


class DictTouchMethodType(Base):
    __tablename__ = 'dict_touch_method_type'
    __table_args__ = {'comment': '触达方法类型字典表'}

    tm_type_id = Column(VARCHAR(64), primary_key=True, comment='触达方法类型id')
    tm_type_name = Column(VARCHAR(255), comment='触达方法类型名称')
    tm_type_code = Column(VARCHAR(30), comment='触达方式code')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='删除标志：0:未删除；1:已删除')


class DictTpUserRole(Base):
    __tablename__ = 'dict_tp_user_role'
    __table_args__ = {'comment': '触点用户类型表'}

    dict_tp_user_role_id = Column(VARCHAR(64), primary_key=True)
    tp_user_role_name = Column(VARCHAR(255), comment='触点人员类型名称')
    tmp_role_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='是否是临时角色；0:不是；1:是；')
    role_group_id = Column(Integer, comment='role组id')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='0:未删除；1:删除')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    tp_user_role_desc = Column(VARCHAR(255))


class DictTpuPosition(Base):
    __tablename__ = 'dict_tpu_position'

    tpu_pos_id = Column(VARCHAR(64), primary_key=True)
    pos_name = Column(VARCHAR(255), comment='职位名称')


class DictViewLevel(Base):
    __tablename__ = 'dict_view_level'
    __table_args__ = {'comment': '菜单视图等级表'}

    view_level_id = Column(VARCHAR(64), primary_key=True, comment='菜单页面等级')
    level_name = Column(VARCHAR(255), comment='页面名称')


class DictViewTrackingType(Base):
    __tablename__ = 'dict_view_tracking_type'
    __table_args__ = {'comment': '页面埋点类型'}

    tracking_type_id = Column(VARCHAR(64), primary_key=True, comment='埋点类型id')
    tracking_type_name = Column(VARCHAR(255), comment='埋点类型名称')
    tracking_father_id = Column(ForeignKey('dict_view_tracking_type.tracking_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='埋点类型父id')

    tracking_father = relationship('DictViewTrackingType', remote_side=[tracking_type_id])


class DictWechatChannel(Base):
    __tablename__ = 'dict_wechat_channels'
    __table_args__ = {'comment': '微信视频号字典'}

    channels_id = Column(VARCHAR(32), primary_key=True, comment='视频号ID')
    channels_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='视频号名称')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除\\n1:已删除')


class DictWechatMp(Base):
    __tablename__ = 'dict_wechat_mp'
    __table_args__ = {'comment': '公众号字典表'}

    app_id = Column(VARCHAR(32), primary_key=True, comment='公众号appID')
    app_name = Column(VARCHAR(100), nullable=False, server_default=text("''"), comment='公众号名称')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除\\n1:已删除')


class FBaseModel(Base):
    __tablename__ = 'f_base_model'
    __table_args__ = {'comment': '训练-基础模型表'}

    base_model_id = Column(VARCHAR(36), primary_key=True, comment='基础模型ID')
    base_model_name = Column(VARCHAR(255), comment='模型名称')
    params = Column(VARCHAR(10), comment='参数量')
    model_name_or_path = Column(VARCHAR(1000), comment='模型名称 or  path')
    template = Column(VARCHAR(255), comment='提示模版')
    status = Column(TINYINT(1), server_default=text("'1'"), comment='1:启用 0：禁用')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1:已删除 0：未删除')
    ms = Column(VARCHAR(255), comment='modelscope path')
    hf = Column(VARCHAR(255), comment='huggingface path')
    lora_target = Column(VARCHAR(50), server_default=text("'all'"), comment='LoRA 作用模块')
    space = Column(VARCHAR(10), comment='模型大小')
    base_model_type_id = Column(Integer, index=True, comment='模型类型 ID')
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"))
    update_time = Column(DateTime)


class FBaseModelBranch(Base):
    __tablename__ = 'f_base_model_branch'
    __table_args__ = {'comment': '基座模型训练子模型表'}

    base_model_branch_id = Column(VARCHAR(36), primary_key=True, comment='基座模型子模型ID')
    base_model_id = Column(VARCHAR(36), index=True, comment='基座模型ID')
    tp_user_id = Column(VARCHAR(36), index=True, comment='创建人ID')
    train_request_id = Column(VARCHAR(36), comment='模型训练ID')
    base_model_branch_name = Column(VARCHAR(128), comment='上传基座子模型用户自定义name')
    description = Column(VARCHAR(255), comment='上传描述')
    model_name_or_path = Column(VARCHAR(255), comment='魔搭name or 上传服务器文件路径')
    file_proxy_url = Column(VARCHAR(255), comment='用户上传文件网络代理地址')
    machine_model_path = Column(VARCHAR(255), comment='最终模型路径')
    model_source = Column(TINYINT(1), comment='来源 1-上传 2-微调')
    status = Column(TINYINT(1), comment='上传状态 1：已上传 0：上传失败  2：上传中 3: 空(非上传)')
    create_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, comment='更新时间')
    delete_flag = Column(TINYINT(1), nullable=False, server_default=text("'0'"), comment='删除标识 1-已删除 0-未删除')


class FBaseModelType(Base):
    __tablename__ = 'f_base_model_type'
    __table_args__ = {'comment': '基座模型类型表,eg: 通义大模型'}

    base_model_type_id = Column(Integer, primary_key=True, comment='递增主键')
    base_model_type_name = Column(VARCHAR(255), comment='基座模型类型名')
    icon_url = Column(VARCHAR(255), comment='模型类型图标')
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, comment='更新时间')
    delete_flag = Column(TINYINT(1), nullable=False, server_default=text("'0'"), comment='1: 已删除，0: 未删除')


class FCalcInstance(Base):
    __tablename__ = 'f_calc_instances'
    __table_args__ = (
        CheckConstraint('json_valid(`cpu_list`)'),
        CheckConstraint('json_valid(`gpu_list`)'),
        {'comment': '已保存、注册算力配置表'}
    )

    id = Column(VARCHAR(255), primary_key=True, comment='id标识符')
    resource_name = Column(VARCHAR(255), nullable=False, comment='资源名称')
    gpu_list = Column(LONGTEXT, nullable=False, comment='gpu型号')
    cpu_list = Column(LONGTEXT, nullable=False, comment='cpu核心数')
    memory = Column(Integer, comment='内存大小')
    disk = Column(VARCHAR(255), nullable=False, comment='磁盘容量')
    network = Column(VARCHAR(255), nullable=False, comment='网络带宽')
    timestamp = Column(DateTime, nullable=False, comment='注册时间')
    status = Column(TINYINT, nullable=False, comment='状态 1：保存 2：注册算力 3:停用 4:启用')
    corp_id = Column(VARCHAR(255), nullable=False, comment='用户识别符')


class FDictDataType(Base):
    __tablename__ = 'f_dict_data_type'
    __table_args__ = {'comment': '数据集类型字典表'}

    data_type_id = Column(Integer, primary_key=True, comment='数据集类型ID')
    data_type_name = Column(VARCHAR(255), comment='数据集类型名称')
    sort = Column(Integer, server_default=text("'9999'"), comment='排序')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1:已删除 0：未删除')


class FGpuSpec(Base):
    __tablename__ = 'f_gpu_specs'
    __table_args__ = {'comment': 'GPU算力信息参考表'}

    id = Column(Integer, primary_key=True, comment='pkey')
    name = Column(VARCHAR(255), nullable=False, comment='显卡型号')
    FP32 = Column(VARCHAR(255), comment='最大单精度浮点运算性能')
    TF32 = Column(VARCHAR(255), comment='最大TF32张量核心运算性能')
    TF32_ = Column('TF32*', VARCHAR(255), comment='最大带稀疏性的TF32张量核心运算性能')
    BF16 = Column(VARCHAR(255), comment='最大BFLOAT16张量核心运算性能')
    BF16_ = Column('BF16*', VARCHAR(255), comment='最大带稀疏性的BFLOAT16张量核心运算性能')
    FP16 = Column(VARCHAR(255), comment='最大半精度浮点张量核心运算性能')
    FP16_ = Column('FP16*', VARCHAR(255), comment='最大带稀疏性的半精度浮点张量核心运算性能')
    INT8 = Column(VARCHAR(255), comment='最大8位整数张量核心运算性能')
    INT8_ = Column('INT8*', VARCHAR(255), comment='最大带稀疏性的8位整数张量核心运算性能')
    INT4 = Column(VARCHAR(255), comment='最大4位整数张量核心运算性能')
    INT4_ = Column('INT4*', VARCHAR(255), comment='最大带稀疏性的4位整数张量核心运算性能')
    FP8 = Column(VARCHAR(255), comment='最大8位浮点运算性能')
    FP8_ = Column('FP8*', VARCHAR(255), comment='最大带稀疏性的8位浮点运算性能')
    GPU_memory = Column(VARCHAR(255), comment='GPU显存容量')
    memory_type = Column(VARCHAR(255), comment='显存类型')
    memory_bandwidth = Column(VARCHAR(255), comment='显存带宽')
    max_TDP = Column(VARCHAR(255), comment='最大功耗')
    FP64 = Column(VARCHAR(255), comment='最大双精度浮点数运算性能')
    FP64_tf = Column(VARCHAR(255), comment='最大加速双精度浮点数运算性能')


class FParamsConfig(Base):
    __tablename__ = 'f_params_config'
    __table_args__ = {'comment': '训练-参数配置保存'}

    param_config_id = Column(Integer, primary_key=True, comment='保存参数ID')
    param_config_name = Column(VARCHAR(255), comment='参数保存名称')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1:已删除 0：未删除')


class FParamsTypeStep(Base):
    __tablename__ = 'f_params_type_step'
    __table_args__ = {'comment': '训练-参数类型步骤表'}

    step_id = Column(Integer, primary_key=True, comment='步骤ID')
    step_name = Column(VARCHAR(255), comment='步骤名称')
    delete_flag = Column(TINYINT(1), comment='1:已删除 0：未删除')
    sort = Column(TINYINT, server_default=text("'99'"), comment='排序')


class FPromptTemplate(Base):
    __tablename__ = 'f_prompt_template'
    __table_args__ = {'comment': '训练-模型模板表'}

    template_id = Column(Integer, primary_key=True)
    template = Column(VARCHAR(255))
    delete_flag = Column(TINYINT(1), server_default=text("'0'"))


class KnowDirectory(Base):
    __tablename__ = 'know_directory'

    directory_id = Column(BigInteger, primary_key=True)
    parent_directory_id = Column(ForeignKey('know_directory.directory_id', ondelete='CASCADE'), index=True)
    directory_name = Column(String(255, 'utf8mb4_general_ci'), nullable=False)
    level = Column(Integer, server_default=text("'0'"))
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"))
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))
    delete_flag = Column(TINYINT)
    tp_user_id = Column(String(36, 'utf8mb4_general_ci'))
    directory_description = Column(String(255, 'utf8mb4_general_ci'))
    sort = Column(Integer)

    parent_directory = relationship('KnowDirectory', remote_side=[directory_id])


class Marketing(Base):
    __tablename__ = 'marketing'
    __table_args__ = {'comment': '自动营销流程表'}

    mkt_id = Column(BigInteger, primary_key=True, comment='自动营销流程ID')
    mkt_name = Column(VARCHAR(100), nullable=False, server_default=text("''"), comment='流程名称')
    status = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='是否启用 1：启用 0：停用')
    run_num = Column(Integer, nullable=False, server_default=text("'0'"), comment='执行次数')
    total_client_num = Column(Integer, nullable=False, server_default=text("'0'"), comment='覆盖总客户数')
    my_client_num = Column(Integer, nullable=False, server_default=text("'0'"), comment='覆盖我的客户\\n')
    mkt_type = Column(TINYINT, nullable=False, server_default=text("'1'"), comment='类型：1：公司级 2：个人级')
    tp_user_id = Column(VARCHAR(64), nullable=False, server_default=text("''"), comment='创建者')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')


class MiniArticle(Base):
    __tablename__ = 'mini_article'
    __table_args__ = {'comment': '图文组件内容表'}

    article_id = Column(BigInteger, primary_key=True, comment='图文ID')
    pid = Column(Integer, nullable=False, server_default=text("'0'"), comment='一级分类 0：图文分类')
    product_id = Column(Integer, nullable=False, server_default=text("'0'"), comment='二级分类')
    article_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='图文标题')
    cover_url = Column(VARCHAR(255), nullable=False, comment='图文封面url')
    jump_url = Column(VARCHAR(1000), nullable=False, server_default=text("''"), comment='跳转url')
    is_hot = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='是否 热门 1：是 0：否')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')
    content = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='介绍')
    tp_user_id = Column(VARCHAR(64), nullable=False, server_default=text("''"), comment='创建人')
    write_id = Column(BigInteger, nullable=False, server_default=text("'0'"), comment='白皮书ID')
    sort = Column(Integer, nullable=False, server_default=text("'999999'"), comment='排序')


class MiniSetting(Base):
    __tablename__ = 'mini_setting'

    set_id = Column(Integer, primary_key=True)
    appid = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='小程序ID')
    mini_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='小程序名称')
    mini_code = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='小程序码')
    trial_code = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='体验码')
    content = Column(LONGTEXT, comment='配置数据')
    path = Column(VARCHAR(255), comment='小程序路径')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0：未删除 1：已删除')


class MiniSolution(Base):
    __tablename__ = 'mini_solution'
    __table_args__ = {'comment': '小程序解决方案表'}

    solution_id = Column(Integer, primary_key=True, comment='解决方案ID')
    title = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='解决方案标题')
    icon = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='icon')
    bg_image = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='背景图')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除  1：已删除')


class MiniVersion(Base):
    __tablename__ = 'mini_version'
    __table_args__ = {'comment': '小程序版本记录表'}

    version_id = Column(Integer, primary_key=True, comment='版本号')
    app_id = Column(VARCHAR(64), nullable=False, server_default=text("''"), comment='小程序id')
    version_no = Column(VARCHAR(64), nullable=False, server_default=text("''"), comment='版本号')
    switch_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='开关状态 1：开启 0：关闭，开启后 针对小程序发布审核使用')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='发布时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')


class MiniWrite(Base):
    __tablename__ = 'mini_write'
    __table_args__ = {'comment': '小程序白皮书表'}

    write_id = Column(BigInteger, primary_key=True, comment='白皮书ID')
    write_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='白皮书名称')
    cover_url = Column(VARCHAR(1000), nullable=False, server_default=text("''"), comment='白皮书封面')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0：未删除 1：已删除')
    pdf_url = Column(VARCHAR(500), nullable=False, server_default=text("''"), comment='Pdf url')
    sort = Column(Integer, nullable=False, server_default=text("'9999'"), comment='排序')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    tp_user_id = Column(VARCHAR(64), nullable=False, server_default=text("''"), comment='创建人')


class OperateDailyMetric(Base):
    __tablename__ = 'operate_daily_metric'
    __table_args__ = {'comment': '每日指标统计表'}

    id = Column(BigInteger, primary_key=True, comment='主键id')
    cal_date = Column(Date, comment='计算日期')
    cal_type = Column(String(32, 'utf8mb4_general_ci'), comment='计算类型：整体趋势/留存率')
    app_type = Column(VARCHAR(32), comment='应用类型')
    brand = Column(String(32, 'utf8mb4_general_ci'), comment='渠道')
    version = Column(String(32, 'utf8mb4_general_ci'), comment='版本')
    package_name = Column(String(32, 'utf8mb4_general_ci'), comment='包名')
    metric_name = Column(VARCHAR(32), comment='每日指标名称')
    metric_num = Column(Integer, comment='每日指标数量')
    is_login = Column(TINYINT, server_default=text("'0'"), comment='是否登录（0:游客 1登录用户）')


class OperateDictAnalysisType(Base):
    __tablename__ = 'operate_dict_analysis_type'
    __table_args__ = {'comment': '智能运营数据分析类型表'}

    analysis_type_id = Column(Integer, primary_key=True, comment='分析类型ID')
    analysis_type_name = Column(String(255, 'utf8mb4_general_ci'), comment='分析类型名称')
    sort = Column(TINYINT, comment='排序')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='1:已删除 0：未删除')


class OperateEvent(Base):
    __tablename__ = 'operate_event'
    __table_args__ = {'comment': '智能运营事件表'}

    event_id = Column(BigInteger, primary_key=True, comment='事件id')
    event_code = Column(String(64, 'utf8mb4_general_ci'), comment='事件编码')
    event_name = Column(VARCHAR(255), comment='事件名称')
    status = Column(TINYINT(1), server_default=text("'1'"), comment='事件显示状态（1：启用， 0：禁用）')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, comment='更新时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='删除标志（0：未删除，1：删除）')
    app_id = Column(VARCHAR(255), comment='平台id')
    today_event_num = Column(Integer, comment='当日事件数')
    today_user_num = Column(Integer, comment='当日事件用户数')
    event_desc = Column(TEXT, comment='事件描述')
    track_sides = Column(String(255, 'utf8mb4_general_ci'), comment='埋点端')
    data_status = Column(TINYINT(1), server_default=text("'0'"), comment='数据状态（0:未上报 1:已上报）')


class OperateEventProperty(Base):
    __tablename__ = 'operate_event_property'
    __table_args__ = {'comment': '智能运营事件属性表'}

    property_id = Column(BigInteger, primary_key=True, comment='属性id')
    event_property_id = Column(VARCHAR(64), comment='事件属性id')
    event_property_name = Column(VARCHAR(255), comment='属性显示名称')
    event_property_valuetype = Column(VARCHAR(255), comment='属性值类型')
    status = Column(TINYINT(1), server_default=text("'1'"), comment='产品显示状态（1：启用， 0：禁用）')
    event_property_type = Column(TINYINT(1), comment='属性类型（1：预置， 2：自定义）')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, comment='更新时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='删除标志（0：未删除，1：删除）')
    event_id = Column(VARCHAR(255), index=True, comment='事件id')
    event_property_desc = Column(TEXT, comment='属性描述')
    track_sides = Column(String(255, 'utf8mb4_general_ci'), comment='埋点端')
    data_status = Column(TINYINT(1), server_default=text("'0'"), comment='数据状态（1：已上报， 0：未上报）')
    event_property_datatype = Column(VARCHAR(255), comment='属性技术类型')


class OperateProduct(Base):
    __tablename__ = 'operate_product'
    __table_args__ = {'comment': '智能运营产品表'}

    product_id = Column(VARCHAR(64), primary_key=True, comment='产品id')
    product_name = Column(String(255, 'utf8mb4_general_ci'), comment='产品名称')
    status = Column(TINYINT(1), comment='产品状态（1：启用， 0：禁用）')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='删除标志（0：未删除，1：删除）')
    product_desc = Column(String(255, 'utf8mb4_general_ci'), comment='产品描述')
    secret_key = Column(String(255, 'utf8mb4_general_ci'), comment='密钥')


class OperateProductEvent(Base):
    __tablename__ = 'operate_product_event'
    __table_args__ = {'comment': '智能运营产品事件统计表'}

    product_event_id = Column(BigInteger, primary_key=True, comment='产品事件统计id')
    event_id = Column(BigInteger, nullable=False, comment='事件id')
    product_id = Column(BigInteger, nullable=False, comment='产品id')
    today_event_num = Column(Integer, comment='当日事件数')
    today_user_num = Column(Integer, comment='当日事件用户数')


class OperateTagType(Base):
    __tablename__ = 'operate_tag_type'
    __table_args__ = {'comment': '标签分类表'}

    tag_type_id = Column(BigInteger, primary_key=True, comment='标签分类id')
    tag_type_name = Column(VARCHAR(255), comment='标签分类名称')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='删除标志（0：未删除，1：删除）')
    product_id = Column(BigInteger, comment='产品id')


class Product(Base):
    __tablename__ = 'product'
    __table_args__ = {'comment': '产品分类字典表'}

    product_id = Column(Integer, primary_key=True, comment='产品分类ID')
    product_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='产品名称')
    pid = Column(Integer, nullable=False, server_default=text("'0'"), comment='父级ID')
    prefix_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='分类前缀')
    content = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='内容介绍')
    detail_type = Column(VARCHAR(10), nullable=False, server_default=text("''"), comment='详情类型 image：图片 video ：视频')
    detail_url = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='详情链接')
    detail_content = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='详情介绍')
    qw_qrcode = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='企微二维码')
    icon = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='分类icon')
    bg_image = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='背景图')
    write_id = Column(BigInteger, nullable=False, server_default=text("'0'"), comment='白皮书ID')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    tp_user_id = Column(VARCHAR(64), nullable=False, server_default=text("''"), comment='添加人')
    sort = Column(Integer, nullable=False, server_default=text("'9999'"), comment='排序 9999')


class ProductInfo(Base):
    __tablename__ = 'product_info'

    product_id = Column(VARCHAR(64), primary_key=True, comment='uuid')
    product_type_id = Column(VARCHAR(64), comment='1:erp;2:百雁;3:喜鹊;4:mes;5:蜂王台')
    product_name = Column(VARCHAR(255), comment='产品名称')
    product_account = Column(Integer, comment='产品账户数')
    product_price = Column(DECIMAL(10, 2), comment='产品价格元')
    product_industry = Column(VARCHAR(500), comment='产品行业,逗号隔开')
    product_use = Column(VARCHAR(500), comment='产品用途,逗号隔开')
    valid_flag = Column(VARCHAR(1), server_default=text("'1'"), comment='是否有效；1有效；0无效')
    product_desc = Column(VARCHAR(500), comment='产品描述')
    key_words = Column(VARCHAR(1500), comment='产品相关关键词')
    display_words = Column(VARCHAR(255), comment='显示产品相关关键词')
    display_desc = Column(VARCHAR(255), comment='显示描述')
    write_id = Column(BigInteger, server_default=text("'1'"), comment='文章id')


class PublishPo(Base):
    __tablename__ = 'publish_pos'
    __table_args__ = {'comment': '发布位置-webhooks配置表'}

    publish_pos_id = Column(VARCHAR(64), primary_key=True)
    pos_name = Column(VARCHAR(255), comment='位置名称，一般是企业微信群或钉钉群')
    webhooks_url = Column(VARCHAR(255), comment='webhooks地址')
    pos_type = Column(VARCHAR(1), comment='地址类型；0:企业微信；1:钉钉')
    create_time = Column(DateTime)
    valid_flag = Column(VARCHAR(1), server_default=text("'1'"), comment='是否启动：0:不启用；1:启用中')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='删除标志位；0:未删除；1:已删除')
    pos_type_theme = Column(VARCHAR(255), comment='label的颜色模式')
    pos_code = Column(VARCHAR(255), comment='位置编码')


class RelMiniQrcode(Base):
    __tablename__ = 'rel_mini_qrcode'
    __table_args__ = {'comment': '关联小程序码参数表'}

    rel_id = Column(BigInteger, primary_key=True, comment='关联ID')
    rel_type = Column(TINYINT, nullable=False, comment='类型 1：名片 2：雷达')
    query_info = Column(TEXT, nullable=False, comment='参数')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')


class RelQppCase(Base):
    __tablename__ = 'rel_qpp_case'
    __table_args__ = {'comment': '痛点及方案关联案例表'}

    rel_case_id = Column(BigInteger, primary_key=True, comment='关联案例ID')
    qpp_id = Column(BigInteger, comment='痛点及方案ID')
    case_type = Column(TINYINT, comment='案例类型 1：客户雷达 2：表单录入')
    client_radar_id = Column(VARCHAR(36), comment='客户雷达ID')
    case_name = Column(VARCHAR(255), comment='案例名称')
    case_content = Column(VARCHAR(1000), comment='案例内容')
    case_links = Column(VARCHAR(1000), comment='案例链接 多个用，分割')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='1:已删除 0：未删除')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')


class ReportDictParam(Base):
    __tablename__ = 'report_dict_params'
    __table_args__ = {'comment': '模板参数表'}

    param_id = Column(BIGINT, primary_key=True, comment='参数ID')
    param_name = Column(VARCHAR(128), comment='参数名称')
    param_description = Column(VARCHAR(255), comment='参数描述')
    param_code = Column(VARCHAR(128), comment='参数编码')
    param_value = Column(VARCHAR(64), comment='参数默认值')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='1:已删除 0：未删除')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"))


class ReportTool(Base):
    __tablename__ = 'report_tools'
    __table_args__ = {'comment': '工具表'}

    tool_id = Column(BigInteger, primary_key=True)
    tool_name = Column(VARCHAR(36), comment='工具名称')
    tool_desc = Column(VARCHAR(256), comment='工具描述')
    tool_code = Column(VARCHAR(36), comment='工具编码')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"))
    update_time = Column(DateTime)
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='1:已删除 0：未删除')
    sort = Column(Integer, comment='排序')


class SaleVersion(Base):
    __tablename__ = 'sale_version'
    __table_args__ = {'comment': '售卖版本表'}

    sale_version_id = Column(VARCHAR(64), primary_key=True)
    sale_version_name = Column(VARCHAR(255), server_default=text("''"), comment='售卖版本的名称')


class WxMpUser(Base):
    __tablename__ = 'wx_mp_user'
    __table_args__ = (
        Index('idx_openid_appid', 'openid', 'appid', unique=True),
        {'comment': '公众号 用户列表'}
    )

    mp_user_id = Column(BigInteger, primary_key=True, comment='用户ID')
    openid = Column(VARCHAR(32), nullable=False, comment='openid')
    appid = Column(VARCHAR(32), nullable=False, server_default=text("''"), comment='appid')
    subscribe = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='用户是否订阅该公众号标识，值为0时，代表此用户没有关注该公众号，拉取不到其余信息。\\n')
    language = Column(VARCHAR(10), nullable=False, server_default=text("''"), comment='用户的语言，简体中文为zh_CN')
    subscribe_time = Column(Integer, nullable=False, server_default=text("'0'"), comment='用户关注时间，为时间戳。如果用户曾多次关注，则取最后关注时间\\n')
    unionid = Column(VARCHAR(32), nullable=False, comment='只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段。')
    remark = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='备注')
    groupid = Column(Integer, nullable=False, server_default=text("'0'"), comment='用户所在的分组ID（兼容旧的用户分组接口）')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='首次更新时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')
    subscribe_scene = Column(VARCHAR(255), comment='返回用户关注的渠道来源，ADD_SCENE_SEARCH 公众号搜索，ADD_SCENE_ACCOUNT_MIGRATION 公众号迁移，ADD_SCENE_PROFILE_CARD 名片分享，ADD_SCENE_QR_CODE 扫描二维码，ADD_SCENE_PROFILE_LINK 图文页内名称点击，ADD_SCENE_PROFILE_ITEM 图文页右上角菜单，ADD_SCENE_PAID 支付后关注，ADD_SCENE_WECHAT_ADVERTISEMENT 微信广告，ADD_SCENE_REPRINT 他人转载 ,ADD_SCENE_LIVESTREAM 视频号直播，ADD_SCENE_CHANNELS 视频号 , ADD_SCENE_OTHERS 其他')


class AdMediaAccount(Base):
    __tablename__ = 'ad_media_account'
    __table_args__ = {'comment': '投放媒体账户表'}

    media_account_id = Column(BigInteger, primary_key=True, comment='媒体账号ID')
    app_id = Column(ForeignKey('ad_apps.app_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='产品ID')
    media_id = Column(VARCHAR(50), comment='媒体ID')
    user_name = Column(VARCHAR(255), comment='账号')
    user_pass = Column(VARCHAR(255), comment='密码')
    advertiser_id = Column(VARCHAR(64), comment='广告主ID')
    advertiser_name = Column(VARCHAR(255), comment='广告主名称')
    form_account_id = Column(VARCHAR(64), comment='来自授权账号ID')
    form_account_name = Column(VARCHAR(255), comment='来自授权账号名称')
    account_role = Column(VARCHAR(30), comment='角色')
    advertiser_role = Column(Integer, comment='角色旧版')
    access_token = Column(VARCHAR(64))
    expires_in = Column(Integer)
    refresh_token = Column(VARCHAR(64))
    refresh_token_expires_in = Column(Integer)
    account_string_id = Column(VARCHAR(255), comment='账号id（字符串型）\\n当advertiser_role=10有效，即抖音号类型时，即为aweme_sec_uid，可用于Dou+接口调用')
    is_valid = Column(TINYINT, comment='授权是否有效 1：有效 0：无效')
    status = Column(TINYINT, nullable=False, server_default=text("'1'"), comment='1：启用 0：停用')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')
    refresh_time = Column(Integer, nullable=False, server_default=text("'0'"), comment='刷新时间')
    media_channel_id = Column(VARCHAR(64), comment='媒体渠道ID')
    user_acct_type = Column(TINYINT, comment='百度账号 账号类型\\n授权账户类型\\n1: 普通账户\\n2：超管账户（客户中心和账户管家）')
    openid = Column(VARCHAR(30), comment='百度账号openid')

    app = relationship('AdApp')


class AgentAgent(Base):
    __tablename__ = 'agent_agent'
    __table_args__ = {'comment': '智能体数据'}

    agent_id = Column(Integer, primary_key=True)
    agent_name = Column(VARCHAR(255), comment='智能体名称')
    scene_id = Column(ForeignKey('agent_scene.scene_id'), nullable=False, index=True, comment='场景id')
    scene_desc = Column(TEXT, comment='问题描述')
    format_id = Column(Integer, comment='输出格式')
    template_id = Column(Integer, comment='场景模版')
    buss_graph = Column(MEDIUMTEXT, comment='业务流节点')
    ft_content = Column(TEXT, comment='用户会话调整内容')
    buss_messages = Column(TEXT, comment='业务流分解信息')
    tech_graph = Column(MEDIUMTEXT, comment='技术流节点')
    tech_params = Column(TEXT, comment='技术流默认参数')
    user_id = Column(VARCHAR(64), nullable=False)
    is_collect = Column(TINYINT(1), comment='是否收藏')
    agent_logo = Column(VARCHAR(255), comment='智能体logo')
    status = Column(TINYINT(1), comment='发布状态')
    is_commit = Column(TINYINT(1), comment='提交状态')
    delete_flag = Column(TINYINT(1), comment='删除状态')
    created_time = Column(DateTime, nullable=False)
    updated_time = Column(DateTime, nullable=False)

    scene = relationship('AgentScene')


class AgentDataCollect(Base):
    __tablename__ = 'agent_data_collect'
    __table_args__ = {'comment': '智能体数据集表'}

    collect_id = Column(Integer, primary_key=True)
    collect_name = Column(VARCHAR(255), comment='数据集名称')
    scene_id = Column(ForeignKey('agent_scene.scene_id'), nullable=False, index=True, comment='场景id')
    collect_desc = Column(TEXT, comment='数据集描述')
    collect_type = Column(Integer, comment='数据集分类')
    collect_url = Column(TEXT, comment='数据集存储路径')
    collect_quote = Column(Integer, comment='流程引用数')
    user_id = Column(VARCHAR(64), nullable=False, comment='用户id')
    created_time = Column(DateTime, nullable=False)
    updated_time = Column(DateTime, nullable=False)
    delete_flag = Column(TINYINT(1), comment='删除状态')

    scene = relationship('AgentScene')


class AigcModel(Base):
    __tablename__ = 'aigc_model'
    __table_args__ = {'comment': 'aigc模型'}

    aigc_model_id = Column(VARCHAR(64), primary_key=True, comment='aigc模型id')
    aigc_type_id = Column(ForeignKey('dict_aigc_type.aigc_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='模型唯一编码')
    model_name = Column(VARCHAR(255), comment='aigc模型名称')
    model_url = Column(VARCHAR(255), comment='aigc的请求地址')
    model_icon = Column(VARCHAR(255), comment='模型icon')
    model_path = Column(VARCHAR(255), comment='模型路径')
    status = Column(TINYINT(1), server_default=text("'1'"), comment='1:显示 0：隐藏')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1:已删除 0：未删除')
    model_code = Column(VARCHAR(255), comment='模型编码')
    use_range = Column(TINYINT(1), server_default=text("'-1'"), comment='使用范围 -1：全部 1：素材生产 2：内容检索 3:生成向量')
    dims = Column(Integer, server_default=text("'0'"), comment='向量纬度')
    aol = Column(VARCHAR(8), nullable=False, server_default=text("'api'"), comment='api or local')

    aigc_type = relationship('DictAigcType')


class AigcSubjectTag(Base):
    __tablename__ = 'aigc_subject_tags'
    __table_args__ = {'comment': 'Aigc主题标签'}

    ast_id = Column(VARCHAR(64), primary_key=True, comment='subject tags id')
    tags_name = Column(VARCHAR(255), nullable=False, comment='主题标签名称')
    subject_id = Column(ForeignKey('dict_aigc_subject.aigc_subject_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='对应主题id')

    subject = relationship('DictAigcSubject')


class AppDirectory(Base):
    __tablename__ = 'app_directory'

    directory_id = Column(BigInteger, primary_key=True)
    parent_directory_id = Column(ForeignKey('know_directory.directory_id', ondelete='CASCADE'), index=True)
    directory_name = Column(VARCHAR(255), nullable=False)
    level = Column(Integer, server_default=text("'0'"))
    create_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"))
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))
    delete_flag = Column(TINYINT)
    tp_user_id = Column(VARCHAR(36))
    directory_description = Column(VARCHAR(255))

    parent_directory = relationship('KnowDirectory')


class ChatbiConversationPrologue(Base):
    __tablename__ = 'chatbi_conversation_prologue'
    __table_args__ = {'comment': '开场白表'}

    prologue_id = Column(BigInteger, primary_key=True, comment='开场白id')
    prologue_row_name = Column(VARCHAR(255), comment='开场白名称')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    tp_user_id = Column(VARCHAR(255), comment='用户id')
    sort = Column(SmallInteger, comment='排序')
    graph_type_id = Column(ForeignKey('chatbi_data_graph_type.graph_type_id'), nullable=False, index=True)
    status = Column(TINYINT, server_default=text("'1'"), comment='开关状态(0:关，1:开)')

    graph_type = relationship('ChatbiDataGraphType')


class ConfigSoftware(Base):
    __tablename__ = 'config_software'
    __table_args__ = {'comment': '系统软件配置-将来可以扩充'}

    config_software_id = Column(VARCHAR(64), primary_key=True)
    help_group_config_id = Column(ForeignKey('client_group_config.client_group_config_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='售后群config_id,获取二维码；')

    help_group_config = relationship('ClientGroupConfig')


class DefaultTpuRole(Base):
    __tablename__ = 'default_tpu_role'

    default_tpu_role_id = Column(VARCHAR(64), primary_key=True)
    role_id = Column(ForeignKey('dict_tp_user_role.dict_tp_user_role_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='对应的角色id')
    valid_flag = Column(VARCHAR(1), server_default=text("'1'"), comment='1:有效；0:无效')

    role = relationship('DictTpUserRole')


class DictAuth(Base):
    __tablename__ = 'dict_auth'
    __table_args__ = {'comment': '角色权限表'}

    dict_auth_id = Column(VARCHAR(64), primary_key=True)
    role_type_id = Column(ForeignKey('dict_tp_user_role.dict_tp_user_role_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='角色id')
    auth_name = Column(VARCHAR(255), comment='权限名称')

    role_type = relationship('DictTpUserRole')


class DictContentScope(Base):
    __tablename__ = 'dict_content_scope'
    __table_args__ = {'comment': '素材应用场景'}

    scope_id = Column(Integer, primary_key=True, comment='属性列别id')
    scope_name = Column(VARCHAR(255))
    pid = Column(ForeignKey('dict_content_scope.scope_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='父级id')
    color_theme = Column(ForeignKey('dict_color_theme.color_theme_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='配色主题')
    delete_flag = Column(VARCHAR(10), server_default=text("'0'"), comment='删除')

    dict_color_theme = relationship('DictColorTheme')
    parent = relationship('DictContentScope', remote_side=[scope_id])


class DictContentType(Base):
    __tablename__ = 'dict_content_type'
    __table_args__ = {'comment': '内容素材表'}

    content_type_id = Column(VARCHAR(64), primary_key=True, comment='内容素材类型id')
    content_type_name = Column(VARCHAR(255), comment='内容素材类型名称')
    father_id = Column(ForeignKey('dict_content_type.content_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='父类型id')
    color_theme_id = Column(ForeignKey('dict_color_theme.color_theme_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='配色主题')
    show_url_flag = Column(VARCHAR(1), comment='是否显示url地址栏；0:无外部链接；1:外部链接格式；2:markdown格式')
    default_pic_url = Column(VARCHAR(255), comment='默认封面url')
    suffix_keys = Column(VARCHAR(255), server_default=text("''"), comment='后缀名关键字')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='删除标志位；0:未删除；1已删除')

    color_theme = relationship('DictColorTheme')
    father = relationship('DictContentType', remote_side=[content_type_id])


class DictPartnerGrowthLevel(Base):
    __tablename__ = 'dict_partner_growth_level'
    __table_args__ = {'comment': '合伙人等级权益表'}

    growth_id = Column(Integer, primary_key=True)
    level_id = Column(ForeignKey('dict_client_level.level_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='合伙人类型ID')
    growth_level = Column(VARCHAR(255), comment='成长等级')
    growth_number = Column(Integer, comment='等级编号')
    min_value = Column(Integer, comment='所需最小值')
    max_value = Column(Integer, comment='所需最大值')
    content = Column(VARCHAR(255), comment='权益介绍')
    work_partner_type = Column(Integer, comment='work平台对应的ID')
    rebate_ratio = Column(Float(3), comment='返点比例')
    withdrawal_ratio = Column(Float(3), comment='智邦币提现比例')

    level = relationship('DictClientLevel')


class FData(Base):
    __tablename__ = 'f_datas'
    __table_args__ = {'comment': '训练-数据集表'}

    data_id = Column(VARCHAR(36), primary_key=True, comment='数据集ID')
    data_name = Column(VARCHAR(255), comment='数据集名称')
    data_description = Column(VARCHAR(255), comment='数据集描述')
    data_path = Column(VARCHAR(1000), index=True, comment='数据集存储路径')
    status = Column(TINYINT(1), server_default=text("'1'"), comment='状态 1：已上传 0：上传失败  2：上传中 4：训练中')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1:已删除 0：未删除')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    tp_user_id = Column(VARCHAR(36), comment='tp_user_id ')
    data_source = Column(TINYINT(1), server_default=text("'1'"), comment='数据集来源 1：内置 2：自定义')
    data_type_id = Column(ForeignKey('f_dict_data_type.data_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='数据集类型')
    register_dataset_name = Column(VARCHAR(255), comment='data_id+文件名')
    data_format = Column(TINYINT(1), comment='0-内置 1-Alpaca 2-Sharegpt')

    data_type = relationship('FDictDataType')


class FParamsConfigDetail(Base):
    __tablename__ = 'f_params_config_detail'
    __table_args__ = {'comment': '训练-训练请求参数表'}

    param_config_detail_id = Column(Integer, primary_key=True, comment='训练参数ID')
    param_config_id = Column(ForeignKey('f_params_config.param_config_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='参数保存ID')
    param_code = Column(VARCHAR(50), comment='参数编码')
    param_value = Column(VARCHAR(1000), comment='参数值')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1:已删除 0：未删除')

    param_config = relationship('FParamsConfig')


class FParamsType(Base):
    __tablename__ = 'f_params_type'
    __table_args__ = {'comment': '训练-参数类型表'}

    param_type_id = Column(Integer, primary_key=True, comment='参数类型ID')
    param_type_name = Column(VARCHAR(255), comment='参数类型名')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1:已删除 0：未删除')
    step_id = Column(ForeignKey('f_params_type_step.step_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='步骤ID')
    is_fold = Column(TINYINT(1), server_default=text("'0'"), comment='是否折叠 1：是 0：否')

    step = relationship('FParamsTypeStep')


class HelpInfo(Base):
    __tablename__ = 'help_info'
    __table_args__ = {'comment': '帮助信息表-跟版本相关'}

    help_info_id = Column(VARCHAR(64), primary_key=True)
    sale_version_id = Column(ForeignKey('sale_version.sale_version_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='版本号')
    info_name = Column(VARCHAR(255), comment='帮助名称')
    info_url = Column(VARCHAR(255), comment='帮助链接')
    order_sn = Column(Integer, comment='排序')

    sale_version = relationship('SaleVersion')


class LandpageComponent(Base):
    __tablename__ = 'landpage_component'

    landpage_com_id = Column(VARCHAR(64), primary_key=True)
    com_type_id = Column(ForeignKey('com_type.com_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True)
    com_name = Column(VARCHAR(255), comment='组件名称')

    com_type = relationship('ComType')


class MiniSolutionDetail(Base):
    __tablename__ = 'mini_solution_detail'
    __table_args__ = {'comment': '小程序解决方案内容表'}

    solution_detail_id = Column(BigInteger, primary_key=True, comment='解决方案内容ID')
    solution_id = Column(ForeignKey('mini_solution.solution_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='解决方案ID')
    title = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='标题')
    icon = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='icon')
    content = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='内容')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1:已删除')

    solution = relationship('MiniSolution')


class MiniVideo(Base):
    __tablename__ = 'mini_video'
    __table_args__ = {'comment': '视频组件内容表'}

    video_id = Column(BigInteger, primary_key=True, comment='视频ID')
    channels_id = Column(ForeignKey('dict_wechat_channels.channels_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, server_default=text("''"), comment='视频号ID')
    video_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='视频名称')
    cover_url = Column(VARCHAR(500), nullable=False, server_default=text("''"), comment='视频封面')
    jump_url = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='跳转地址')
    is_hot = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='是否是热门 1：是 0：否')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')
    pid = Column(Integer, nullable=False, server_default=text("'0'"), comment='一级分类 0：图文分类 -1:优秀案例 -2 资质 -3：证书')
    product_id = Column(Integer, nullable=False, server_default=text("'0'"), comment='二级分类')
    content = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='介绍')
    tp_user_id = Column(VARCHAR(64), nullable=False, server_default=text("''"), comment='创建人')
    write_id = Column(BigInteger, nullable=False, server_default=text("'0'"), comment='关联白皮书ID')
    sort = Column(Integer, nullable=False, server_default=text("'999999'"), comment='排序')

    channels = relationship('DictWechatChannel')


class MiniWriteDetail(Base):
    __tablename__ = 'mini_write_detail'
    __table_args__ = {'comment': '白皮书详情表'}

    write_detail_id = Column(BigInteger, primary_key=True, comment='白皮书详情ID')
    write_id = Column(ForeignKey('mini_write.write_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, server_default=text("'0'"), comment='白皮书ID')
    write_url = Column(VARCHAR(500), nullable=False, comment='白皮书url')
    sort = Column(Integer, nullable=False, server_default=text("'999999'"), comment='排序')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1:已删除')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')

    write = relationship('MiniWrite')


class OperateApp(Base):
    __tablename__ = 'operate_app'
    __table_args__ = {'comment': '智能运营应用表'}

    app_id = Column(VARCHAR(64), primary_key=True, comment='应用id')
    app_name = Column(VARCHAR(255), comment='应用名称')
    status = Column(TINYINT(1), server_default=text("'0'"), comment='应用状态（1：启用， 0：禁用）')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='删除标志（0：未删除，1：删除）')
    product_id = Column(ForeignKey('operate_product.product_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='产品id')
    app_desc = Column(VARCHAR(255), comment='应用描述')
    app_type = Column(String(255, 'utf8mb4_general_ci'), comment='应用类型')
    os = Column(String(255, 'utf8mb4_general_ci'), comment='系统')

    product = relationship('OperateProduct')


class OperateBrand(Base):
    __tablename__ = 'operate_brand'
    __table_args__ = {'comment': '品牌表'}

    brand_id = Column(Integer, primary_key=True, comment='品牌ID')
    product_id = Column(ForeignKey('operate_product.product_id'), index=True, comment='产品ID')
    brand_name = Column(String(255, 'utf8mb4_general_ci'), comment='品牌名称')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')

    product = relationship('OperateProduct')


class OperateChannel(Base):
    __tablename__ = 'operate_channel'
    __table_args__ = {'comment': '产品渠道表'}

    channel_id = Column(Integer, primary_key=True, comment='渠道ID')
    product_id = Column(ForeignKey('operate_product.product_id'), index=True, comment='产品ID')
    channel_name = Column(String(255, 'utf8mb4_general_ci'), comment='渠道名称')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')

    product = relationship('OperateProduct')


class OperateRelEventProperty(Base):
    __tablename__ = 'operate_rel_event_property'
    __table_args__ = {'comment': '事件和事件属性关联关系表'}

    event_proper_rel_id = Column(BigInteger, primary_key=True, comment='事件和事件属性关联id')
    event_id = Column(ForeignKey('operate_event.event_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='事件id')
    property_id = Column(ForeignKey('operate_event_property.property_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='事件属性id')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='删除标识 0:未删除 1:删除')
    app_id = Column(BigInteger, comment='平台id')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')

    event = relationship('OperateEvent')
    property = relationship('OperateEventProperty')


class Organization(Base):
    __tablename__ = 'organization'
    __table_args__ = {'comment': '组织表（企业、团体、个人）'}

    org_id = Column(VARCHAR(64), primary_key=True, comment='id')
    dict_org_type_id = Column(ForeignKey('dict_org_type.dict_org_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True)
    sale_version_id = Column(ForeignKey('sale_version.sale_version_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='售卖版本id')
    sale_seat_num = Column(Integer, server_default=text("'0'"), comment='售卖席位数目')
    org_name = Column(VARCHAR(255), comment='组织名称')
    sale_bgn_date = Column(Date, comment='开始时间;当前席位的开始时间；')
    sale_end_date = Column(Date, comment='结束时间；当前席位的结束时间；')
    logo_url = Column(VARCHAR(255), comment='组织logo')
    slogan = Column(VARCHAR(255), comment='组织slogan')
    address = Column(VARCHAR(255), comment='组织地址')
    web_address = Column(VARCHAR(255), comment='官网地址')
    email = Column(VARCHAR(255), comment='组织公开邮箱')
    phone = Column(VARCHAR(255), comment='组织公开电话')
    ww_qrcode_url = Column(VARCHAR(255), comment='组织企微通用二维码')
    father_org_id = Column(VARCHAR(64), comment='父组织id，适合于集团企业')
    son_depart_id = Column(VARCHAR(64), comment='子企业对应父整体企业的部门id')
    industry_oriented = Column(VARCHAR(1000), comment='面向行业')
    phone_400 = Column(VARCHAR(255), comment='400热线')
    service_phone = Column(VARCHAR(255), comment='服务监督')
    org_name_en = Column(VARCHAR(255), comment='组织英文名')
    phone_400_remark = Column(VARCHAR(255), comment='400热线备注')
    is_sp_client = Column(TINYINT(1), server_default=text("'0'"), comment='客户信息是否需要审核 1：需要 0：不需要')
    ww_corp_id = Column(VARCHAR(64), comment='企微组织id')

    dict_org_type = relationship('DictOrgType')
    sale_version = relationship('SaleVersion')


class Question(Base):
    __tablename__ = 'question'
    __table_args__ = {'comment': '问卷问题表'}

    question_id = Column(BigInteger, primary_key=True, comment='问卷id')
    question_type_id = Column(ForeignKey('dict_question_type.question_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='问题分类ID')
    question_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='问题名称')
    question_type = Column(VARCHAR(64), nullable=False, server_default=text("''"), comment='问题类型 single：单选 multiple：多选 text:填空 number:数字 ')
    placeholder = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='提示')
    max_num = Column(Integer, nullable=False, server_default=text("'-1'"), comment='多选题是 允许选择最大数量 -1 为不限')
    is_rel_option = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='是否关联答案显示 1需要 2 不需要')
    status = Column(TINYINT, nullable=False, server_default=text("'1'"), comment='状态 1：启用 2：禁用')
    sort = Column(Integer, nullable=False, server_default=text("'0'"), comment='排序')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0未删除 1已删除')
    file_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='上传附加标识 1：允许 0：不允许')
    tp_user_id = Column(VARCHAR(36), comment='员工ID')
    key_words = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='关键词')
    category_id = Column(TINYINT, nullable=False, server_default=text("'1'"), comment='分类ID:1:行业特征 2：痛点及方案')

    question_type1 = relationship('DictQuestionType')


class RelAuthSchemeRole(Base):
    __tablename__ = 'rel_auth_scheme_role'
    __table_args__ = {'comment': '素材权限方案角色关联表'}

    rel_as_role_id = Column(VARCHAR(64), primary_key=True)
    role_id = Column(ForeignKey('dict_tp_user_role.dict_tp_user_role_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='角色id')
    scheme_id = Column(ForeignKey('content_auth_scheme.auth_scheme_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='方案id')

    role = relationship('DictTpUserRole')
    scheme = relationship('ContentAuthScheme')


class TagGroup(Base):
    __tablename__ = 'tag_group'
    __table_args__ = {'comment': '素材组表'}

    tag_group_id = Column(VARCHAR(64), primary_key=True)
    group_name = Column(VARCHAR(255), comment='标签组名称')
    father_group_id = Column(VARCHAR(64), comment='父标签组id')
    tag_type_id = Column(ForeignKey('dict_tags_type.tags_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='标签类型id')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='删除标志位：0:未删除；1:已删除')
    sn = Column(Integer, comment='排列序号')

    tag_type = relationship('DictTagsType')


class TemplateFp(Base):
    __tablename__ = 'template_fp'
    __table_args__ = {'comment': '策划计划模板表'}

    template_fp_id = Column(VARCHAR(64), primary_key=True, comment='策划计划模板id')
    template_name = Column(VARCHAR(255), comment='策划计划模板名称')
    tfp_type_id = Column(ForeignKey('dict_template_fp_type.tfp_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='模板类型id')
    cover_pic_url = Column(VARCHAR(255), comment='模板封面id')
    base_fp_id = Column(VARCHAR(64), comment='策划模板基于的策划计划id')
    status = Column(VARCHAR(1), server_default=text("'1'"), comment='1:启用；0:停用')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='0:未删除；1:已删除')
    add_time = Column(DateTime, comment='添加时间')

    tfp_type = relationship('DictTemplateFpType')


class AgentAgentDataCollect(Base):
    __tablename__ = 'agent_agent_data_collect'
    __table_args__ = {'comment': '智能体关联数据集'}

    id = Column(Integer, primary_key=True)
    agent_id = Column(ForeignKey('agent_agent.agent_id'), nullable=False, index=True, comment='智能体id')
    collect_id = Column(ForeignKey('agent_data_collect.collect_id'), nullable=False, index=True, comment='数据集id')
    created_time = Column(DateTime)
    updated_time = Column(DateTime)
    delete_flag = Column(TINYINT(1), comment='删除状态')

    agent = relationship('AgentAgent')
    collect = relationship('AgentDataCollect')


class AigcModelBalance(Base):
    __tablename__ = 'aigc_model_balance'

    aigc_model_id = Column(ForeignKey('aigc_model.aigc_model_id'), nullable=False, index=True)
    model_url = Column(VARCHAR(255))
    url_id = Column(Integer, primary_key=True)
    status = Column(Integer)

    aigc_model = relationship('AigcModel')


class AigcModelBalanceCopy1(Base):
    __tablename__ = 'aigc_model_balance_copy1'

    aigc_model_id = Column(ForeignKey('aigc_model.aigc_model_id'), nullable=False, index=True)
    model_url = Column(VARCHAR(255))
    url_id = Column(Integer, primary_key=True)
    status = Column(Integer)

    aigc_model = relationship('AigcModel')


class AigcTagsDesc(Base):
    __tablename__ = 'aigc_tags_desc'
    __table_args__ = {'comment': '标签描述表 key words'}

    ast_desc_id = Column(VARCHAR(64), primary_key=True, comment='tags描述表id')
    ast_id = Column(ForeignKey('aigc_subject_tags.ast_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='tags id')
    sn = Column(Integer, nullable=False, server_default=text("'0'"), comment='tags desc的序列')
    desc = Column(VARCHAR(500), nullable=False, server_default=text("'无'"), comment='tags 描述内容')

    ast = relationship('AigcSubjectTag')


class Department(Base):
    __tablename__ = 'department'
    __table_args__ = {'comment': '组织部门表'}

    department_id = Column(VARCHAR(64), primary_key=True)
    parent_dp_id = Column(VARCHAR(64), index=True, comment='父级department id')
    org_id = Column(ForeignKey('organization.org_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='组织id')
    department_name = Column(VARCHAR(255), comment='部门名称')
    address = Column(VARCHAR(255), comment='部门地址')
    department_level = Column(VARCHAR(20), server_default=text("'2'"), comment='部门等级 1公司；2部门')
    master = Column(VARCHAR(64), comment='负责人')
    add_user_id = Column(VARCHAR(64), comment='添加人')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='用户信息同步/添加时间')
    telephone = Column(VARCHAR(100), comment='电话')
    email = Column(VARCHAR(100), comment='邮箱')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='删除标志位；0:未删除；1:已删除')

    org = relationship('Organization')


class FParam(Base):
    __tablename__ = 'f_params'
    __table_args__ = {'comment': '训练-参数表'}

    param_id = Column(Integer, primary_key=True)
    param_name = Column(VARCHAR(100), comment='参数名称')
    param_description = Column(VARCHAR(255), comment='参数描述')
    param_code = Column(VARCHAR(100), comment='参数编码')
    param_type_id = Column(ForeignKey('f_params_type.param_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='参数类型ID')
    default_value = Column(VARCHAR(50), comment='参数默认值')
    value_type = Column(VARCHAR(50), comment='参数值类型 int str')
    allowed_value = Column(VARCHAR(1000), comment='允许选择值')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1:已删除 0：未删除')
    sort = Column(Integer, server_default=text("'99'"), comment='排序')
    is_factory = Column(TINYINT(1), server_default=text("'1'"), comment='是否是llamafactory参数 1：是 0：否')
    api_url = Column(VARCHAR(255), comment='apiurl')
    is_required = Column(TINYINT(1), server_default=text("'0'"), comment='是否必填 1：是 0：否')
    is_rel_data = Column(TINYINT(1), server_default=text("'0'"), comment='是否有关联数据 1：是 0：否')
    placeholder = Column(VARCHAR(255), comment='placeholder')
    father_param_code = Column(VARCHAR(128), comment='依赖的param_code')
    subsidiary_param_code = Column(VARCHAR(128), comment='儿子的param_code')

    param_type = relationship('FParamsType')


class FunctionPoint(Base):
    __tablename__ = 'function_point'
    __table_args__ = {'comment': '软件功能点表'}

    func_point_id = Column(VARCHAR(64), primary_key=True, server_default=text("''"))
    sale_version_id = Column(ForeignKey('sale_version.sale_version_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='销售版本id')
    func_point_name = Column(VARCHAR(255), comment='功能点名称')
    father_point_id = Column(ForeignKey('function_point.func_point_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='父功能id')
    auth_id = Column(ForeignKey('dict_auth.dict_auth_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='权限id')

    auth = relationship('DictAuth')
    father_point = relationship('FunctionPoint', remote_side=[func_point_id])
    sale_version = relationship('SaleVersion')


class OrgSeatTime(Base):
    __tablename__ = 'org_seat_time'

    ost_id = Column(VARCHAR(64), primary_key=True)
    org_id = Column(ForeignKey('organization.org_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='组织id')
    seat_num = Column(Integer, comment='购买席位数')
    seat_bgn_date = Column(Date, comment='席位开始时间')
    seat_end_date = Column(Date, comment='席位结束时间')
    order_time = Column(DateTime, comment='订单时间')
    buy_price = Column(DECIMAL(10, 2), comment='实际付款金额：单位-元')
    valid_flag = Column(VARCHAR(1), server_default=text("'1'"), comment='0：失效；1：有效')

    org = relationship('Organization')


class QuestionOption(Base):
    __tablename__ = 'question_option'
    __table_args__ = {'comment': '问题选项表'}

    question_option_id = Column(BigInteger, primary_key=True, comment='问题答案id')
    question_id = Column(ForeignKey('question.question_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, server_default=text("'0'"), comment='问题id')
    question_option_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='答案名称')
    rel_question_ids = Column(VARCHAR(500), nullable=False, server_default=text("''"), comment='答案关联的问题id，非空')
    is_other = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='是否有其他自定义 1有 0没有')
    other_required = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='自定义选项是否必填 1是 0 否')
    status = Column(TINYINT, nullable=False, server_default=text("'1'"), comment='状态 1启用 2禁用')
    disable_option = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='禁用选项 ID')
    sort = Column(Integer, nullable=False, server_default=text("'0'"), comment='排序')
    max_num = Column(Integer, nullable=False, server_default=text("'-1'"), comment='最大值')
    min_num = Column(Integer, nullable=False, server_default=text("'-1'"), comment='最小值')
    placehoder = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='placehoder')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0: 未删除 1已删除')

    question = relationship('Question')


class QuestionSummary(Base):
    __tablename__ = 'question_summary'
    __table_args__ = {'comment': '问题总结表'}

    question_summary_id = Column(BigInteger, primary_key=True, comment='总结ID')
    summary_type = Column(TINYINT, comment='总结类型1：gpt 2：标准')
    question_id = Column(ForeignKey('question.question_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='问题ID')
    question_type_id = Column(ForeignKey('dict_question_type.question_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='分类ID')
    content = Column(VARCHAR(5000), comment='内容')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='1：已删除 0：未删除')

    question = relationship('Question')
    question_type = relationship('DictQuestionType')


class RelComProperty(Base):
    __tablename__ = 'rel_com_property'

    rel_ctp_id = Column(VARCHAR(64), primary_key=True)
    com_id = Column(ForeignKey('landpage_component.landpage_com_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='组件id')
    property_id = Column(ForeignKey('com_property.com_property_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='属性id')
    basic_com_code = Column(VARCHAR(255), comment='属性基础组件编码\\r\\ninput：输入框\\r\\nselect：选择框\\r\\ncolor_select：颜色选择器\\r\\nfile_update：文件上传框\\r\\nsize_select：尺寸选择器')
    default_value = Column(VARCHAR(255), comment='属性基础组件默认值')

    com = relationship('LandpageComponent')
    property = relationship('ComProperty')


class RelRoleAuth(Base):
    __tablename__ = 'rel_role_auth'
    __table_args__ = {'comment': '角色和权限关联表'}

    rel_role_auth_id = Column(VARCHAR(64), primary_key=True)
    auth_id = Column(ForeignKey('dict_auth.dict_auth_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='功能授权id')
    role_id = Column(ForeignKey('dict_tp_user_role.dict_tp_user_role_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='角色id')
    valid_flag = Column(VARCHAR(1), server_default=text("'1'"), comment='是否有效；1:有效；0:无效')
    create_time = Column(DateTime, comment='创建时间')

    auth = relationship('DictAuth')
    role = relationship('DictTpUserRole')


class Tag(Base):
    __tablename__ = 'tags'
    __table_args__ = {'comment': '标签表（素材内容标签、客户标签、策划标签）'}

    tag_name = Column(VARCHAR(255), comment='标签名称')
    tags_id = Column(VARCHAR(64), primary_key=True, index=True, comment='素材id')
    tag_type_id = Column(ForeignKey('dict_tags_type.tags_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='标签类型id')
    tag_group_id = Column(ForeignKey('tag_group.tag_group_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='标签组id')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='删除标志位；0:未删除；1:已删除')

    tag_group = relationship('TagGroup')
    tag_type = relationship('DictTagsType')


class FunctionView(Base):
    __tablename__ = 'function_view'
    __table_args__ = {'comment': '功能点页面'}

    function_view_id = Column(VARCHAR(64), primary_key=True, server_default=text("''"), comment='功能视图id')
    function_point_id = Column(ForeignKey('function_point.func_point_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='功能点id')
    father_view_id = Column(ForeignKey('function_view.function_view_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='父功能视图id')
    view_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='功能视图名称')
    view_url = Column(VARCHAR(255), comment='功能视图url')
    view_type_id = Column(ForeignKey('dict_function_view.function_view_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='视图类型id')
    view_icon = Column(VARCHAR(255), server_default=text("''"), comment='视图菜单icon')
    view_level_id = Column(ForeignKey('dict_view_level.view_level_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='视图等级id')
    redirect_path = Column(VARCHAR(255), comment='重定向地址')
    component_path = Column(VARCHAR(255), comment='组件地址')
    view_code = Column(VARCHAR(255), comment='页面代码')
    search_content = Column(VARCHAR(255), comment='如果是配置页面，search_content的placeholder')
    help_info = Column(VARCHAR(255), comment='页面帮助desc')
    status = Column(TINYINT(1), server_default=text("'1'"), comment='1：显示 0：隐藏')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1:已删除 0：未删除')

    father_view = relationship('FunctionView', remote_side=[function_view_id])
    function_point = relationship('FunctionPoint')
    view_level = relationship('DictViewLevel')
    view_type = relationship('DictFunctionView')


class RelAuthSchemeDepart(Base):
    __tablename__ = 'rel_auth_scheme_depart'
    __table_args__ = {'comment': '素材权限方案'}

    rel_as_depart_id = Column(VARCHAR(64), primary_key=True)
    scheme_id = Column(ForeignKey('content_auth_scheme.auth_scheme_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='方案id')
    depart_id = Column(ForeignKey('department.department_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='部门id')

    depart = relationship('Department')
    scheme = relationship('ContentAuthScheme')


class TouchpointUser(Base):
    __tablename__ = 'touchpoint_user'
    __table_args__ = {'comment': '触点之用户表'}

    tp_user_id = Column(VARCHAR(64), primary_key=True, comment='id')
    dp_id = Column(ForeignKey('department.department_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='department_id')
    pa_flag_id = Column(ForeignKey('dict_planning_authority.dict_pa_flag_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='Planning Authority 策划id等级')
    tp_user_name = Column(VARCHAR(255), comment='用户名')
    tp_user_alias = Column(VARCHAR(255), server_default=text("''"), comment='用户别称')
    phone_number = Column(VARCHAR(20), comment='电话号码')
    email = Column(VARCHAR(255), comment='email地址')
    position = Column(VARCHAR(255), comment='职位')
    character_setting = Column(VARCHAR(255), server_default=text("''"), comment='人设树立')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='删除标志位；0:未删除；1:已删除')
    ww_user_id = Column(VARCHAR(64), comment='企业微信user_id')
    avatar = Column(VARCHAR(200), comment='微信头像url')
    thumb_avatar = Column(VARCHAR(255), comment='微信头像缩略图url')
    depart_list = Column(VARCHAR(255), comment='企微部门list')
    personal_qrcode = Column(VARCHAR(255), comment='个人二维码')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='用户信息同步/添加时间')
    modify_time = Column(DateTime)
    seat_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='席位数；0：无效；1：有效')
    service_role_id = Column(ForeignKey('dict_service_role.service_role_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='服务角色ID')
    pos_id = Column(ForeignKey('dict_tpu_position.tpu_pos_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, server_default=text("'946f6788-7f14-11ee-900b-00163e06a675'"), comment='角色id')
    out_name = Column(VARCHAR(50), comment='对外姓名')
    out_email = Column(VARCHAR(100), comment='对外邮箱')
    status = Column(VARCHAR(10), server_default=text("'1'"), comment='状态 0待审；1通过；2驳回')
    pwd = Column(VARCHAR(50), comment='密码 MD5 MD5 加密')
    add_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人')

    add_user = relationship('TouchpointUser', remote_side=[tp_user_id])
    dp = relationship('Department')
    pa_flag = relationship('DictPlanningAuthority')
    pos = relationship('DictTpuPosition')
    service_role = relationship('DictServiceRole')


class AdAppsTpu(Base):
    __tablename__ = 'ad_apps_tpu'
    __table_args__ = {'comment': '员工负责投放产品表'}

    app_tup_id = Column(BigInteger, primary_key=True, comment='投放产品管理员工ID')
    app_id = Column(ForeignKey('ad_apps.app_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, server_default=text("'0'"), comment='产品ID')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, server_default=text("''"), comment='员工ID')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0：未删除 1：已删除')

    app = relationship('AdApp')
    tp_user = relationship('TouchpointUser')


class AffiliatedUnit(Base):
    __tablename__ = 'affiliated_unit'
    __table_args__ = {'comment': '单位表'}

    unit_id = Column(VARCHAR(64), primary_key=True)
    name = Column(VARCHAR(255), comment='单位名称')
    pid = Column(ForeignKey('affiliated_unit.unit_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='父级单位id')
    desc = Column(VARCHAR(255), comment='描述')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='创作者id')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='修改时间')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='0:未删除；1已删除')

    parent = relationship('AffiliatedUnit', remote_side=[unit_id])
    tp_user = relationship('TouchpointUser')


class AigcApp(Base):
    __tablename__ = 'aigc_app'
    __table_args__ = {'comment': 'aigc 应用表'}

    app_id = Column(BigInteger, primary_key=True, comment='应用ID')
    app_name = Column(VARCHAR(255), comment='应用名称')
    app_desc = Column(VARCHAR(500), comment='应用描述')
    app_type = Column(TINYINT(1), nullable=False, server_default=text("'1'"), comment='1-普通应用 2-上级应用')
    icon_url = Column(VARCHAR(255), comment='Icon url')
    auth_scheme_id = Column(ForeignKey('content_auth_scheme.auth_scheme_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='权限方案ID')
    aigc_model_id = Column(ForeignKey('aigc_model.aigc_model_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='对应模型ID')
    prompt = Column(TEXT, comment='提示词')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人')
    add_time = Column(DateTime, comment='创建时间')
    update_time = Column(DateTime, comment='更新时间')
    status = Column(TINYINT(1), server_default=text("'1'"), comment='发布状态 1：已发布 0：停用')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    size = Column(TINYINT, server_default=text("'5'"), comment='命中最大数量')
    mini_score = Column(Float(2), server_default=text("'1.4'"), comment='阈值 1-2之间')
    is_mixture = Column(TINYINT(1), server_default=text("'0'"), comment='是否混合检索 1：是 0：否')
    is_graph = Column(TINYINT(1), server_default=text("'1'"), comment='是否启用知识图谱 1：启用 0：未启用')
    welcome_content = Column(VARCHAR(300), comment='应用欢迎语')
    classify_target = Column(VARCHAR(500), comment='命中关键词')
    classify_prompt = Column(Text(collation='utf8mb4_general_ci'))
    classify_name = Column(String(100, 'utf8mb4_general_ci'))
    classify_priority = Column(TINYINT(1), nullable=False, server_default=text("'0'"), comment='0-手动，1-向量，2-大模型')
    ques_enabled = Column(TINYINT(1), nullable=False, server_default=text("'0'"), comment='问题提取开启')
    ques_prompt = Column(String(500, 'utf8mb4_general_ci'), comment='问题提取描述')
    ques_keywords = Column(VARCHAR(1000), comment='问题提取关键词')
    ques_replace = Column(JSON, comment='问题替换描述')
    ques_enhance = Column(String(100, 'utf8mb4_general_ci'), comment='问题增强方案')
    ques_enhance_prompt = Column(Text(collation='utf8mb4_general_ci'), comment='问题增强提示词')
    digest_size = Column(TINYINT(1), server_default=text("'0'"), comment='摘要-召回数量')
    digest_score = Column(Float, server_default=text("'1.9'"), comment='摘要-召回阈值')
    qa_score = Column(Float, server_default=text("'1.9'"), comment='QA-阈值')
    scope = Column(Integer, server_default=text("'0'"), comment='应用范围 0 公开 1 个人级')
    is_rerank = Column(TINYINT, server_default=text("'0'"), comment='是否开启重排')
    rerank_size = Column(Integer, server_default=text("'100'"), comment='重排片数')
    rarank_ignore_score = Column(Float, server_default=text("'0'"), comment='rerank 后丢弃阈值')
    global_percent = Column(Integer, server_default=text("'30'"), comment='全局检索比例')
    is_pic_rerank = Column(Integer, server_default=text("'0'"), comment='是否启用图片rerank')
    pic_rarank_ignore_score = Column(Float, server_default=text("'0'"), comment='图片丢弃阈值')
    parent_directory_id = Column(BigInteger)
    graph_mode = Column(TINYINT, nullable=False, server_default=text("'0'"))

    aigc_model = relationship('AigcModel')
    auth_scheme = relationship('ContentAuthScheme')
    tp_user = relationship('TouchpointUser')


class AigcContent(Base):
    __tablename__ = 'aigc_content'
    __table_args__ = {'comment': 'aigc内容生成表'}

    aigc_content_id = Column(VARCHAR(64), primary_key=True, comment='aigc产生的素材')
    aigc_model_id = Column(ForeignKey('aigc_model.aigc_model_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True)
    prompt_words = Column(VARCHAR(255), server_default=text("''"), comment='aigc的核心prompt')
    desc_words = Column(VARCHAR(500), server_default=text("''"), comment='aigc生成的desc')
    bgn_time = Column(DateTime, comment='模型开始生成时间')
    end_time = Column(DateTime, comment='模型结束返回时间')
    pic_url_array = Column(VARCHAR(1000), server_default=text("''"), comment='模型生成照片地址数组')
    ast_desc_id = Column(ForeignKey('aigc_tags_desc.ast_desc_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='tags描述id')
    batch_count = Column(Integer, nullable=False, server_default=text("'1'"), comment='训练生成几批图片')
    batch_num = Column(Integer, nullable=False, server_default=text("'1'"), comment='每批含有多少图片')
    pic_type_id = Column(ForeignKey('dict_pic_type.pic_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='图片类型id')
    pic_resolution_id = Column(ForeignKey('dict_pic_resolution.pic_resolution_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='图片分辨率id')
    pic_style_id = Column(ForeignKey('dict_pic_style.pic_style_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, server_default=text("''"), comment='图片风格id')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, server_default=text("''"), comment='图片制作者id')

    aigc_model = relationship('AigcModel')
    ast_desc = relationship('AigcTagsDesc')
    pic_resolution = relationship('DictPicResolution')
    pic_style = relationship('DictPicStyle')
    pic_type = relationship('DictPicType')
    tp_user = relationship('TouchpointUser')


class AigcKnowledge(Base):
    __tablename__ = 'aigc_knowledge'
    __table_args__ = {'comment': '知识库表'}

    knowledge_id = Column(BigInteger, primary_key=True, comment='知识库ID')
    knowledge_name = Column(VARCHAR(255), comment='知识库名称')
    knowledge_desc = Column(VARCHAR(255), comment='知识库描述')
    status = Column(TINYINT(1), server_default=text("'1'"), comment='1：启用 0：停用')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    icon_url = Column(VARCHAR(255), comment='Icon url')
    aigc_model_id = Column(ForeignKey('aigc_model.aigc_model_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='aigc_model_id')
    auth_scheme_id = Column(ForeignKey('content_auth_scheme.auth_scheme_id'), index=True, comment='权限ID')
    scene_id = Column(ForeignKey('agent_scene.scene_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True)
    open_life = Column(TINYINT, server_default=text("'0'"), comment='是否生命周期 0否；1是')
    scope = Column(Integer, server_default=text("'0'"), comment='知识库类型 0 是公开范围 1是个人知识库')
    parent_directory_id = Column(BigInteger)
    graph_status = Column(Integer, nullable=False, server_default=text("'0'"))
    graph_enable = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='是否支持图模式 0不支持1支持')

    aigc_model = relationship('AigcModel')
    auth_scheme = relationship('ContentAuthScheme')
    scene = relationship('AgentScene')
    tp_user = relationship('TouchpointUser')


class AigcMenuSupplement(Base):
    __tablename__ = 'aigc_menu_supplement'
    __table_args__ = {'comment': 'AI菜单补充字段'}

    ms_id = Column(VARCHAR(64), primary_key=True, comment='主键id')
    view_id = Column(ForeignKey('function_view.function_view_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='菜单id')
    sort = Column(Integer, nullable=False, server_default=text("'0'"), comment='排序字段')
    tag = Column(VARCHAR(255), comment='菜单标签')
    model_ids = Column(TEXT, comment='菜单支持的模型id，多个使用,分割')
    hello = Column(TEXT, comment='打开对话时的默认打招呼语句')
    prompt = Column(TEXT, comment='模型提示词')

    view = relationship('FunctionView')


class AigcQaLib(Base):
    __tablename__ = 'aigc_qa_lib'
    __table_args__ = {'comment': 'qa库'}

    qa_lib_id = Column(BigInteger, primary_key=True, comment='主键')
    qa_name = Column(String(255), nullable=False, comment='qa库名称')
    qa_desc = Column(String(255), comment='qa库描述')
    aigc_model_id = Column(ForeignKey('aigc_model.aigc_model_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='模型id')
    icon_url = Column(VARCHAR(526), comment='图标地址')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='创建人')
    status = Column(Integer, server_default=text("'1'"), comment='状态 1启用；0停用')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='删除状态 0；1')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='修改时间')

    aigc_model = relationship('AigcModel')
    tp_user = relationship('TouchpointUser')


class Banner(Base):
    __tablename__ = 'banner'
    __table_args__ = {'comment': 'Banner 表'}

    banner_id = Column(BigInteger, primary_key=True, comment='banner ID')
    banner_name = Column(VARCHAR(255), comment='Banner 名称')
    banner_url = Column(VARCHAR(255), comment='banner 图片')
    banner_location = Column(VARCHAR(255), comment='Banner 位置 home：首页  course：课程')
    jump_type = Column(VARCHAR(255), comment='banner 类型 no：不跳转 mp:公众号 channels：视频号 mini:小程序内')
    jump_rel_id = Column(VARCHAR(500), comment='跳转 关联公众号 or视频ID')
    jump_path = Column(VARCHAR(255), comment='小程序内跳转')
    status = Column(TINYINT, server_default=text("'1'"), comment='显示状态 1：显示 0：不显示')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='0:未删除 1：已删除')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人')
    sort = Column(Integer, server_default=text("'99999'"), comment='排序')
    channels_id = Column(ForeignKey('dict_wechat_channels.channels_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='视频号ID')

    channels = relationship('DictWechatChannel')
    tp_user = relationship('TouchpointUser')


class BucketInfo(Base):
    __tablename__ = 'bucket_info'
    __table_args__ = {'comment': '对象存储配置'}

    id = Column(Integer, primary_key=True, nullable=False, index=True, comment='id')
    bucket = Column(VARCHAR(255), primary_key=True, nullable=False, comment='容器名称')
    domain = Column(VARCHAR(255), nullable=False)
    endpoint = Column(VARCHAR(255), nullable=False)
    expire_time = Column(Integer, nullable=False)
    region = Column(VARCHAR(255), nullable=False)
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='修改时间')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='创作者id')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='0:未删除；1已删除')
    desc = Column(VARCHAR(255), comment='描述')
    access_key_id = Column(VARCHAR(255), comment='key_id')
    key_secret = Column(VARCHAR(255), comment='密钥')
    role_arn = Column(VARCHAR(255), comment='授权值')
    role = Column(VARCHAR(255), comment='角色名称')

    tp_user = relationship('TouchpointUser')


class ChatbiBoard(Base):
    __tablename__ = 'chatbi_board'
    __table_args__ = {'comment': 'chatbi 看板表'}

    board_id = Column(BigInteger, primary_key=True, comment='看板ID')
    board_name = Column(VARCHAR(255), comment='看板名称')
    create_tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='创建人')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='修改人')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='修改时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    graph_location_list = Column(TEXT, comment='关联的多个图表位置信息')

    create_tp_user = relationship('TouchpointUser', primaryjoin='ChatbiBoard.create_tp_user_id == TouchpointUser.tp_user_id')
    update_tp_user = relationship('TouchpointUser', primaryjoin='ChatbiBoard.update_tp_user_id == TouchpointUser.tp_user_id')


class ChatbiDataSource(Base):
    __tablename__ = 'chatbi_data_source'
    __table_args__ = {'comment': '数据源配置表'}

    data_source_id = Column(BigInteger, primary_key=True, comment='数据源id')
    data_source_type = Column(TINYINT(1), comment='数据源类型编号，1: 本地文件, 2: 数据库')
    data_source_name = Column(VARCHAR(255), comment='数据源名称')
    db_type = Column(ForeignKey('chatbi_dict_db_type.db_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='数据库类型：1: PostgreSQL 2:MySQL')
    db_host = Column(VARCHAR(255), comment='数据库主机地址')
    db_port = Column(VARCHAR(255), comment='数据库服务端口')
    db_name = Column(VARCHAR(255), comment='数据库名')
    db_username = Column(VARCHAR(255), comment='数据库用户名')
    db_password = Column(VARCHAR(255), comment='数据库密码')
    schema_name = Column(VARCHAR(255), server_default=text("'public'"))
    is_ssl = Column(TINYINT(1), comment='是否启用SSL连接,0:不启用,1:启用')
    is_ssh = Column(TINYINT(1), comment='是否通过SSH连接,0:不启用,1:启用')
    ssh_host = Column(VARCHAR(255), comment='数据库ssh主机地址')
    ssh_port = Column(VARCHAR(255), comment='ssh主机端口')
    ssh_username = Column(VARCHAR(255), comment='ssh主机用户名')
    ssh_password = Column(VARCHAR(255), comment='ssh主机密码')
    file_name = Column(VARCHAR(255), comment='文件名称')
    file_describe = Column(VARCHAR(255), comment='文件型数据源描述')
    file_path = Column(VARCHAR(500), comment='文件路径')
    file_type = Column(VARCHAR(30), comment='文件类型 excel csv')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    add_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='更新人')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    file_upload_status = Column(TINYINT(1), server_default=text("'1'"), comment='文件上传状态 0：失败 1:成功 2:上传中')
    table_count = Column(Integer, nullable=False, server_default=text("'0'"))

    add_user = relationship('TouchpointUser', primaryjoin='ChatbiDataSource.add_user_id == TouchpointUser.tp_user_id')
    chatbi_dict_db_type = relationship('ChatbiDictDbType')
    update_user = relationship('TouchpointUser', primaryjoin='ChatbiDataSource.update_user_id == TouchpointUser.tp_user_id')


class ClassInfo(Base):
    __tablename__ = 'class_info'
    __table_args__ = {'comment': '班级表'}

    class_id = Column(BigInteger, primary_key=True, comment='班级ID')
    class_name = Column(VARCHAR(255), nullable=False, comment='班级名称')
    start_date = Column(Date, comment='开始日期')
    end_date = Column(Date, comment='结束日期')
    person_number = Column(Integer, comment='培训人数')
    status = Column(TINYINT, server_default=text("'1'"), comment='状态1：开启 0：关闭')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='1:已删除 0：未删除')
    master_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='负责人')
    remarks = Column(VARCHAR(500), comment='班级备注')
    qr_code = Column(VARCHAR(255), comment='班级码')

    master_user = relationship('TouchpointUser', primaryjoin='ClassInfo.master_user_id == TouchpointUser.tp_user_id')
    tp_user = relationship('TouchpointUser', primaryjoin='ClassInfo.tp_user_id == TouchpointUser.tp_user_id')


class Client(Base):
    __tablename__ = 'client'
    __table_args__ = {'comment': '列表客户表'}

    client_id = Column(VARCHAR(64), primary_key=True, comment='客户id')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True)
    fission_user_id = Column(VARCHAR(64), comment='裂变传播员工id')
    industry_id = Column(ForeignKey('dict_client_industry.dict_client_industry_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='客户行业id')
    company_id = Column(ForeignKey('company.company_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='客户公司id')
    ww_id = Column(VARCHAR(64), comment='企业微信的client_id')
    delete_flag = Column(VARCHAR(64), server_default=text("'0'"), comment='删除标志位;0:未删除；1:已删除')
    new_clue_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='是否新线索；0:不是；1:是')
    rel_client_id = Column(VARCHAR(64), comment='关联客户id')
    mini_openid = Column(VARCHAR(64), comment='小程序openid')
    client_code = Column(BigInteger, server_default=text("'0'"), comment='用户编号')
    work_client_id = Column(VARCHAR(255), comment='work平台客户ID')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    unionid = Column(VARCHAR(64), comment='唯一ID')
    question_type_id = Column(ForeignKey('dict_question_type.question_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='行业智库中的角色分类')
    competitor_ids = Column(VARCHAR(255), comment='行业智库中竞品IDs')

    company = relationship('Company')
    industry = relationship('DictClientIndustry')
    question_type = relationship('DictQuestionType')
    tp_user = relationship('TouchpointUser')


class ClientInfo(Client):
    __tablename__ = 'client_info'
    __table_args__ = {'comment': '客户信息表'}

    client_id = Column(ForeignKey('client.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), primary_key=True, comment='客户id；跟client表是一对一关系')
    nick_name = Column(VARCHAR(255), comment='客户昵称')
    weixin_openid = Column(VARCHAR(32), comment='微信openid')
    real_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='真实姓名')
    phone_number = Column(VARCHAR(100), comment='电话号码')
    region_id = Column(ForeignKey('dict_region.region_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='区域id')
    age = Column(Integer, comment='客户年龄')
    gender = Column(VARCHAR(1), comment='0：未知 1：男性 2：女性')
    pic_url = Column(VARCHAR(255), comment='客户头像pic url')
    remark = Column(VARCHAR(255), comment='客户备注')
    company_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='公司名称')
    integral = Column(Integer, nullable=False, server_default=text("'0'"), comment='智邦币总数')
    address = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='地址')
    client_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='客户名')
    post_code = Column(VARCHAR(10), nullable=False, server_default=text("''"), comment='邮政编码')
    client_level = Column(Integer, nullable=False, server_default=text("'1'"), comment='等级')
    work_client_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='Work 平台客户名称')
    success_client = Column(VARCHAR(1), nullable=False, server_default=text("''"), comment='是否是work平台成功客户 1：是 0：否 空未知')
    activity_flag = Column(TINYINT, nullable=False, server_default=text("'1'"), comment='活动标识 默认是1')
    work_person_id = Column(VARCHAR(10), nullable=False, server_default=text("''"), comment='work平台persons ID')
    work_client_type = Column(VARCHAR(50), nullable=False, server_default=text("''"), comment='Work平台客户类型')
    source = Column(VARCHAR(50), nullable=False, server_default=text("''"), comment='客户来源')
    growth_value = Column(Integer, nullable=False, server_default=text("'0'"), comment='成长值')
    integral1 = Column(Integer, nullable=False, server_default=text("'0'"), comment='可提现智邦币')
    integral2 = Column(Integer, nullable=False, server_default=text("'0'"), comment='可转赠智邦币')
    growth_level = Column(VARCHAR(20), nullable=False, server_default=text("''"), comment='成长等级')
    now_role = Column(VARCHAR(10), nullable=False, server_default=text("'1'"), comment='当前角色 1：客户 2：合伙人')
    last_login = Column(DateTime, comment='上次登录时间')
    id_front_pic = Column(VARCHAR(255), comment='身份证正面')
    id_back_pic = Column(VARCHAR(255), comment='身份证背面')
    personal_pic = Column(VARCHAR(255), comment='个人正面照')
    graduation_pic = Column(VARCHAR(255), comment='毕业证照片')
    social_id_pic = Column(VARCHAR(255), comment='社保照片')
    student_flag = Column(VARCHAR(1), server_default=text("'1'"), comment='0:不是学生；1:是学生')
    id_address = Column(VARCHAR(255), comment='身份证地址')
    current_address = Column(VARCHAR(255), comment='当前地址')
    nation = Column(VARCHAR(255), comment='民族')
    political_id = Column(ForeignKey('dict_client_political.political_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='政治面貌；0:党员；1:团员；2:群众；3：民主党派')
    college_name = Column(VARCHAR(255), comment='毕业院校')
    max_learn = Column(ForeignKey('dict_client_max_learn.max_learn_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='最高学历：\u20281:初中及以下；\u20282:高中；\u20283:中专；\u20284:大专；\u20285:大学；\u20286:研究生；\u20287:博士及博士后；')
    educations_history = Column(VARCHAR(500), comment='参与何种培训')
    got_ca = Column(VARCHAR(255), comment='获得证书')
    company_master = Column(VARCHAR(255), comment='单位负责人姓名')
    company_phone = Column(VARCHAR(255), comment='单位电话')
    id_card = Column(VARCHAR(30), comment='身份证号')
    sp_status = Column(TINYINT, server_default=text("'0'"), comment='审核状态 0：默认状态 1：待审核 2：审核通过 3：驳回')

    dict_client_max_learn = relationship('DictClientMaxLearn')
    political = relationship('DictClientPolitical')
    region = relationship('DictRegion')


class ClientGroup(Base):
    __tablename__ = 'client_group'
    __table_args__ = {'comment': '客户群'}

    group_id = Column(VARCHAR(64), primary_key=True)
    chat_id = Column(VARCHAR(64), comment='微信客户群id')
    group_name = Column(VARCHAR(255), comment='微信群名称')
    owner = Column(VARCHAR(255), comment='群主')
    ower_user_id = Column(ForeignKey('touchpoint_user.tp_user_id'), index=True, comment='群主tp_user_id')
    client_list = Column(TEXT, comment='客户列表')
    valid_flag = Column(VARCHAR(1), server_default=text("'1'"), comment='1:有效；0:废弃，删除，无效')
    create_time = Column(DateTime, comment='群创建时间')

    ower_user = relationship('TouchpointUser')


class ComponentApiDatum(Base):
    __tablename__ = 'component_api_data'

    com_api_data_id = Column(VARCHAR(64), primary_key=True)
    com_data_type = Column(VARCHAR(1), comment='0:视频号；1:公众号；2:H5链接')
    com_api_id = Column(ForeignKey('dict_component_api.com_api_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='api的id')
    add_time = Column(DateTime)
    add_tpu_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True)
    data_name = Column(VARCHAR(255))
    data_title = Column(VARCHAR(255))
    data_cover_oss_url = Column(VARCHAR(255))
    data_url = Column(VARCHAR(255))

    add_tpu = relationship('TouchpointUser')
    com_api = relationship('DictComponentApi')


class ConfigChannel(Base):
    __tablename__ = 'config_channel'
    __table_args__ = {'comment': '渠道配置'}

    channel_id = Column(VARCHAR(64), primary_key=True, comment='渠道id')
    channel_name = Column(VARCHAR(255), comment='渠道名称')
    channel_pic_url = Column(VARCHAR(255), comment='渠道二维码地址')
    channel_group_id = Column(ForeignKey('channel_group.channel_group_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='渠道组id')
    channel_type_id = Column(ForeignKey('dict_channel_type.channel_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, server_default=text("'1'"), comment='channel对应的类型id')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='删除标志位；0:未删除；1:删除；')
    upload_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='自有二维码上传标志位：0：不上传已有二维码；1：上传已有二维码')
    client_group_id = Column(VARCHAR(64), comment='客户群id')
    add_time = Column(DateTime, comment='添加时间')
    modify_time = Column(DateTime)
    recovery_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='恢复者')
    recovery_time = Column(DateTime, comment='恢复时间')
    channel_code = Column(VARCHAR(30), comment='对应的渠道编码')
    activity_name = Column(VARCHAR(255), comment='活动名称')
    modify_user_id = Column(VARCHAR(64), comment='修改者id')
    add_user_id = Column(VARCHAR(64), comment='添加人id')
    bg_qr_code_url = Column(VARCHAR(255), comment='个人渠道背景码')

    channel_group = relationship('ChannelGroup')
    channel_type = relationship('DictChannelType')
    recovery_user = relationship('TouchpointUser')


class CourseCategory(Base):
    __tablename__ = 'course_category'
    __table_args__ = {'comment': '课程分类表'}

    course_category_id = Column(Integer, primary_key=True, comment='课程分类ID')
    course_category_name = Column(VARCHAR(255), comment='课程分类名称')
    sort = Column(Integer, server_default=text("'99999'"), comment='排序')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人')
    status = Column(TINYINT, comment='1:显示 0：隐藏')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='1:已删除 0：未删除')

    tp_user = relationship('TouchpointUser')


class CourseTeacher(Base):
    __tablename__ = 'course_teacher'
    __table_args__ = {'comment': '师资表'}

    course_teacher_id = Column(BigInteger, primary_key=True, comment='教师ID')
    teacher_name = Column(VARCHAR(50), comment='教师名称')
    phone_number = Column(VARCHAR(30), comment='手机号')
    company = Column(VARCHAR(255), comment='所在单位')
    introduce = Column(TEXT, comment='介绍')
    id_card = Column(VARCHAR(20), comment='身份证号')
    email = Column(VARCHAR(50), comment='邮箱')
    bank_name = Column(VARCHAR(100), comment='银行名称')
    open_bank = Column(VARCHAR(100), comment='开户行')
    bank_number = Column(VARCHAR(50), comment='银行卡号')
    img_url = Column(VARCHAR(255), comment='个人照片')
    status = Column(TINYINT, server_default=text("'1'"), comment='状态 1：有效 0：无效')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='员工id')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='1:已删除 0：未删除')
    is_index = Column(TINYINT, server_default=text("'0'"), comment='是否首页显示 0：否 1：是')
    title = Column(VARCHAR(255), comment='标题')

    tp_user = relationship('TouchpointUser')


class DictKeyWord(Base):
    __tablename__ = 'dict_key_words'
    __table_args__ = {'comment': '关键字字典'}

    words_id = Column(Integer, primary_key=True)
    words = Column(VARCHAR(255), comment='关键词')
    create_time = Column(DateTime, comment='创建者')
    creator_tpu_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='创建者id')

    creator_tpu = relationship('TouchpointUser')


class DocClearRule(Base):
    __tablename__ = 'doc_clear_rule'
    __table_args__ = (
        CheckConstraint('(`doc_type` in (0,1,2))'),
        CheckConstraint('(`rule_type` in (0,1,2))'),
        {'comment': '文档清洗规则配置'}
    )

    rule_id = Column(BigInteger, primary_key=True, comment='规则ID')
    rule_name = Column(VARCHAR(255), nullable=False, comment='规则名称')
    rule_content = Column(TEXT, comment='规则内容')
    rule_desc = Column(VARCHAR(255), comment='描述')
    is_deleted = Column(TINYINT(1), server_default=text("'0'"), comment='是否删除，布尔型（用TINYINT表示），默认值为0（未删除）')
    is_active = Column(TINYINT(1), server_default=text("'1'"), comment='是否激活，布尔型（用TINYINT表示），默认值为1（已激活）')
    created_at = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    rule_type = Column(TINYINT(1), comment='规则类型：0为提示词，1为脚本，2为预留')
    doc_type = Column(TINYINT(1), comment='文档类型：0为文档，1为表格，2为图片')
    is_builtin = Column(TINYINT(1), server_default=text("'0'"), comment='是否内置：0为内置，1为自定义')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id'), index=True, comment=' 关联用户ID')

    tp_user = relationship('TouchpointUser')


class ElectricCardComponet(Base):
    __tablename__ = 'electric_card_componet'

    ecc_id = Column(VARCHAR(64), primary_key=True)
    com_title = Column(VARCHAR(255), comment='电子组件标题')
    card_type_id = Column(VARCHAR(1), comment='0：企业；1：个人；2：产品；3：其他')
    thumbnail_oss_url = Column(VARCHAR(255), comment='缩略图oss地址')
    add_tpu_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人id')
    add_time = Column(DateTime, comment='添加时间')

    add_tpu = relationship('TouchpointUser')


class ElectricCardTemplate(Base):
    __tablename__ = 'electric_card_template'

    ect_id = Column(VARCHAR(64), primary_key=True)
    card_name = Column(VARCHAR(255), comment='电子名片名称')
    card_type_id = Column(VARCHAR(1), comment='0：企业；1：个人；2：产品；3：其他')
    card_content = Column(TEXT, comment='电子名片内容')
    cover_oss_url = Column(VARCHAR(255), comment='封面oss地址')
    add_tpu_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人id')
    add_time = Column(DateTime, comment='添加时间')
    client_send_count = Column(Integer, comment='给客户发送总次数')
    client_open_count = Column(Integer, comment='客户打开总次数')
    client_close_count = Column(Integer, comment='客户关闭总次数')
    client_avg_time = Column(Integer, comment='客户平均停留时间')
    client_trans_count = Column(Integer, comment='客户转发次数')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='删除标志位；0：未删除；1：已删除')
    modify_time = Column(DateTime)
    recovery_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='恢复者')
    recovery_time = Column(DateTime, comment='恢复时间')
    modify_user_id = Column(VARCHAR(64), comment='修改者id')
    qr_code = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='小程序二维码')
    publish_status = Column(ENUM('0', '1', '2', '3', '4', '5', '6'), nullable=False, server_default=text("'5'"), comment='0:已上架；1:审核中；2:审核拒绝；3:审核通过；4推送失败；5处理中；6已下架')
    scope = Column(ENUM('0', '1'), nullable=False, server_default=text("'1'"), comment='可使范围 0:公司；1:个人；')

    add_tpu = relationship('TouchpointUser', primaryjoin='ElectricCardTemplate.add_tpu_id == TouchpointUser.tp_user_id')
    recovery_user = relationship('TouchpointUser', primaryjoin='ElectricCardTemplate.recovery_user_id == TouchpointUser.tp_user_id')


class ExamPage(Base):
    __tablename__ = 'exam_page'
    __table_args__ = {'comment': '考试试卷'}

    exam_id = Column(Integer, primary_key=True, comment='试卷id')
    exam_name = Column(VARCHAR(255), comment='名字')
    status = Column(Integer, server_default=text("'0'"), comment='状态 0密封；1已启封；2已结束')
    start_time = Column(DateTime, comment='开始时间')
    end_time = Column(DateTime, comment='结束时间')
    time_long = Column(Integer, comment='考试时长/m')
    delete_flag = Column(Integer, server_default=text("'0'"), comment='1:已删除 0：未删除')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='修改时间')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人')
    assembly = Column(VARCHAR(20), server_default=text("'self'"), comment='装配方式 self；auto')
    score = Column(Integer, nullable=False, server_default=text("'100'"), comment='总分数')
    qb_type_range = Column(VARCHAR(255), server_default=text("''"), comment='题型范围')

    tp_user = relationship('TouchpointUser')


class FTrainRequest(Base):
    __tablename__ = 'f_train_request'
    __table_args__ = {'comment': '训练-训练请求表'}

    train_request_id = Column(VARCHAR(36), primary_key=True, comment='训练请求ID')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='申请人ID')
    status = Column(TINYINT, server_default=text("'1'"), comment='1：已保存 2：等待中 3：训练中 4：训练完成 5 训练失败 6:训练中断 -1:已取消')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='申请时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1:已删除 0：未删除')
    model_name = Column(VARCHAR(255), comment='模型名称')
    model_description = Column(VARCHAR(255), comment='模型描述')
    is_urgency = Column(TINYINT(1), comment='紧急程度 1：紧急 0：非紧急')
    model_icon = Column(VARCHAR(255), comment='模型图标')
    base_model_id = Column(ForeignKey('f_base_model.base_model_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='基础模型ID')
    is_publish = Column(TINYINT(1), server_default=text("'0'"), comment='是否发布 1：已发布 0：未发布')
    train_param_config_id = Column(Integer, comment='加载训练参数')
    elapsed_times = Column(Integer, server_default=text("'0'"), comment='已训练时长 单位秒')
    remaining_times = Column(Integer, server_default=text("'0'"), comment='剩余训练时长 单位秒')
    percentage = Column(Float(5), server_default=text("'0.00'"), comment='训练进度')
    request_time = Column(DateTime, comment='提交训练申请时间')
    success_time = Column(DateTime, comment='训练完成时间')
    process_id = Column(Integer, comment='进程ID')
    publish_model_path = Column(VARCHAR(500), comment='发布模型path')
    publish_download_url = Column(VARCHAR(500), comment='发布模型下载url')
    resource = Column(VARCHAR(255), comment='所需资源')

    base_model = relationship('FBaseModel')
    tp_user = relationship('TouchpointUser')


class FileClearRule(Base):
    __tablename__ = 'file_clear_rule'
    __table_args__ = {'comment': '清洗规则'}

    id = Column(Integer, primary_key=True, comment='自增主键')
    name = Column(String(255), comment='规则名字')
    desc = Column(String(255), comment='规则描述')
    content = Column(Text, comment='具体内容')
    type = Column(VARCHAR(100), nullable=False, index=True, comment='分类 call_word；code_logic；script')
    source = Column(String(100), index=True, server_default=text("'custom'"), comment='built_in内置；custom自定义')
    status = Column(TINYINT, server_default=text("'1'"), comment='1启用；0禁用')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='删除标识')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人')

    tp_user = relationship('TouchpointUser')


class LandpageConfJson(Base):
    __tablename__ = 'landpage_conf_json'

    lcs_id = Column(BigInteger, primary_key=True)
    content = Column(TEXT, comment='落地页数据')
    remark = Column(VARCHAR(255), server_default=text("''"), comment='备注')
    add_user = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='创建人')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(VARCHAR(2), nullable=False, server_default=text("'0'"), comment='是否删除；1 已删除；0启用')

    touchpoint_user = relationship('TouchpointUser')


class MediaChannel(Base):
    __tablename__ = 'media_channel'

    media_channel_id = Column(VARCHAR(64), primary_key=True)
    media_type_id = Column(ForeignKey('dict_media_type.dict_media_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='媒体类型id')
    media_channel_name = Column(VARCHAR(255), comment='媒体渠道名称')
    add_tpu_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加用户id')
    add_time = Column(DateTime, comment='添加时间')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='0:未删除；1：已删除')
    channel_code = Column(VARCHAR(255), comment='渠道码')
    master_tpu_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='负责人')
    modify_time = Column(DateTime)
    recovery_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='恢复人')
    recovery_time = Column(DateTime, comment='恢复时间')
    modify_user_id = Column(VARCHAR(64), comment='修改者id')

    add_tpu = relationship('TouchpointUser', primaryjoin='MediaChannel.add_tpu_id == TouchpointUser.tp_user_id')
    master_tpu = relationship('TouchpointUser', primaryjoin='MediaChannel.master_tpu_id == TouchpointUser.tp_user_id')
    media_type = relationship('DictMediaType')
    recovery_user = relationship('TouchpointUser', primaryjoin='MediaChannel.recovery_user_id == TouchpointUser.tp_user_id')


class MiniGift(Base):
    __tablename__ = 'mini_gift'
    __table_args__ = {'comment': '小程序礼品表'}

    gift_id = Column(BigInteger, primary_key=True, comment='礼品ID')
    gift_type = Column(Integer, nullable=False, server_default=text("'1'"), comment='礼品分类 1：默认分类 2：新人礼包 3心意好礼 4：办公优选 5：生活必须')
    gift_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='礼品名称')
    cover_url = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='礼品封面')
    content = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='礼品介绍')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='创建人')
    need_integral = Column(Integer, nullable=False, server_default=text("'1000'"), comment='需要积分')
    sort = Column(Integer, nullable=False, server_default=text("'999999'"), comment='排序')

    tp_user = relationship('TouchpointUser')


class OperateDashboard(Base):
    __tablename__ = 'operate_dashboard'
    __table_args__ = {'comment': '自定义看板表'}

    dashboard_id = Column(BigInteger, primary_key=True, comment='看板id')
    dashboard_name = Column(VARCHAR(255), server_default=text("''"), comment='看板名字')
    status = Column(TINYINT(1), server_default=text("'0'"), comment='状态 1:启用 0:停用')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    create_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人id')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='修改时间')
    product_id = Column(BigInteger, nullable=False, index=True, comment='产品id')

    create_user = relationship('TouchpointUser')


class OperateGragh(Base):
    __tablename__ = 'operate_gragh'
    __table_args__ = {'comment': '数据分析表'}

    graph_id = Column(BigInteger, primary_key=True, comment='图表D')
    analysis_type_id = Column(ForeignKey('operate_dict_analysis_type.analysis_type_id'), index=True, comment='数据分类型ID')
    graph_name = Column(String(255, 'utf8mb4_general_ci'), comment='图表名称')
    filter_type = Column(TINYINT(1), comment='筛选类型 1：区间 2：动态')
    start_date = Column(Date, comment='filter_type=1时 开始时间')
    end_date = Column(Date, comment='filter_type=1时 结束时间')
    past_day = Column(Integer, comment='filter_type=2时 过去xx天')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id'), index=True, comment='操作人')
    delete_flag = Column(TINYINT, comment='1:已删除 0：未删除')
    product_id = Column(BigInteger, nullable=False, comment='产品id')

    analysis_type = relationship('OperateDictAnalysisType')
    tp_user = relationship('TouchpointUser')


class OperateGroup(Base):
    __tablename__ = 'operate_groups'
    __table_args__ = {'comment': '用户群表'}

    group_id = Column(BigInteger, primary_key=True, comment='用户群id')
    group_name = Column(VARCHAR(255), comment='用户群名称')
    cal_state = Column(TINYINT, server_default=text("'0'"), comment='计算状态（0:待计算 1:计算中 2:计算成功 3:计算失败）')
    product_id = Column(BigInteger, comment='产品id')
    create_type = Column(TINYINT(1), server_default=text("'0'"), comment='创建方式（0:规则创建 1:用户群下钻/拆分 2:漏斗分析 3:路径分析 4:留存分析 5:事件分析 6:LTV分析）')
    group_desc = Column(TEXT, comment='用户群描述')
    update_type = Column(TINYINT(1), server_default=text("'0'"), comment='更新类型（0：手动更新，1:定期更新）')
    update_freq = Column(VARCHAR(10), server_default=text("'0'"), comment='更新频率（day：每天更新， month：每月更新，week：每周更新）')
    update_inter = Column(Integer, comment='更新间隔')
    update_day_time = Column(VARCHAR(10), comment='定期更新的时间，时：分')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    create_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人id')
    update_time = Column(DateTime, comment='更新时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='删除标志（0：未删除，1：删除）')
    last_update_time = Column(DateTime, comment='最近一次更新成功的时间')

    create_user = relationship('TouchpointUser')


class OperateLifeCycle(Base):
    __tablename__ = 'operate_life_cycle'
    __table_args__ = {'comment': '用户生命周期表'}

    cycle_id = Column(BigInteger, primary_key=True, comment='生命周期id')
    product_id = Column(BigInteger, comment='产品id')
    novice_user_num = Column(Integer, comment='新手期用户数')
    growth_user_num = Column(Integer, comment='成长期用户数')
    mature_user_num = Column(Integer, comment='成熟期用户数')
    silent_user_num = Column(Integer, comment='沉默期用户数')
    churn_user_num = Column(Integer, comment='流失期用户数')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    create_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人id')

    create_user = relationship('TouchpointUser')


class OperateTag(Base):
    __tablename__ = 'operate_tags'
    __table_args__ = {'comment': '标签表'}

    tag_id = Column(BigInteger, primary_key=True, comment='标签id')
    tag_name = Column(VARCHAR(255), comment='标签名称')
    tag_type_id = Column(ForeignKey('operate_tag_type.tag_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='标签分类id')
    create_type = Column(TINYINT(1), server_default=text("'0'"), comment='创建方式（0：规则创建）')
    tag_desc = Column(TEXT, comment='标签描述')
    update_type = Column(TINYINT(1), server_default=text("'0'"), comment='更新类型（0：手动更新，1:定期更新）')
    update_freq = Column(String(10, 'utf8mb4_general_ci'), server_default=text("'0'"), comment='更新频率（day：每天更新， month：每月更新，week：每周更新）')
    update_inter = Column(Integer, comment='更新间隔')
    update_day_time = Column(String(10, 'utf8mb4_general_ci'), comment='定期更新的时间，时：分')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    create_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人id')
    update_time = Column(DateTime, comment='更新时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='删除标志（0：未删除，1：删除）')
    cal_state = Column(TINYINT, server_default=text("'0'"), comment='计算状态（0:待计算 1:计算中 2:计算成功 3:计算失败）')
    last_update_time = Column(DateTime, comment='最近一次更新成功的时间')

    create_user = relationship('TouchpointUser')
    tag_type = relationship('OperateTagType')


class OperationLog(Base):
    __tablename__ = 'operation_logs'
    __table_args__ = {'comment': '操作日志表'}

    operation_logs_id = Column(VARCHAR(64), primary_key=True, comment='操作日志id')
    funciton_view_id = Column(ForeignKey('function_view.function_view_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, server_default=text("''"), comment='功能页面id')
    function_content = Column(VARCHAR(255), server_default=text("''"), comment='具体功能内容')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='触点用户id')
    operation_content = Column(TEXT, comment='操作内容')
    operation_time = Column(DateTime, comment='操作时间')

    funciton_view = relationship('FunctionView')
    tp_user = relationship('TouchpointUser')


class QuestionBank(Base):
    __tablename__ = 'question_bank'
    __table_args__ = {'comment': '试卷题库'}

    question_id = Column(Integer, primary_key=True, comment='题库id')
    name = Column(TEXT, comment='题目')
    type = Column(VARCHAR(255), comment='题型 radio,checkbox,textbox')
    criterion = Column(VARCHAR(255), server_default=text("'all'"), comment='多选 评分准则 all、single')
    tips = Column(VARCHAR(255), comment='提示')
    status = Column(Integer, server_default=text("'1'"), comment='启用状态 1启用；0禁用')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='修改时间')
    delete_flag = Column(Integer, server_default=text("'0'"), comment='1:已删除 0：未删除')
    key_words = Column(VARCHAR(255), comment='关键字')
    score = Column(Float, server_default=text("'0'"), comment='分数值')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人')

    tp_user = relationship('TouchpointUser')


class QuestionCategory(Base):
    __tablename__ = 'question_category'
    __table_args__ = {'comment': '试题科类'}

    qc_id = Column(Integer, primary_key=True, comment='题类id')
    name = Column(VARCHAR(255), comment='题类名称')
    delete_flag = Column(VARCHAR(10), server_default=text("'0'"), comment='删除状态')
    user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    up_time = Column(DateTime)

    user = relationship('TouchpointUser')


class QuestionPointPlan(Base):
    __tablename__ = 'question_point_plan'
    __table_args__ = {'comment': '痛点及方案表'}

    qpp_id = Column(BigInteger, primary_key=True, comment='痛点及方案ID')
    question_type_id = Column(ForeignKey('dict_question_type.question_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='一级分类ID')
    sore_point = Column(VARCHAR(2000), comment='痛点')
    describe = Column(VARCHAR(2000), comment='业务场景表述')
    function_point = Column(TEXT, comment='ERP解决功能点')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='员工ID')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='0:未删除 1：已删除')
    answer_type = Column(TINYINT, comment='回答类型 1：个人 2：标准')

    question_type = relationship('DictQuestionType')
    tp_user = relationship('TouchpointUser')


class RelAuthSchemeUser(Base):
    __tablename__ = 'rel_auth_scheme_user'
    __table_args__ = {'comment': '素材权限方案用户关联表'}

    rel_as_user_id = Column(VARCHAR(64), primary_key=True)
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='user id')
    scheme_id = Column(ForeignKey('content_auth_scheme.auth_scheme_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='方案id')

    scheme = relationship('ContentAuthScheme')
    tp_user = relationship('TouchpointUser')


class RelQuestionTpu(Base):
    __tablename__ = 'rel_question_tpu'
    __table_args__ = {'comment': '知识回答'}

    rel_question_tup_id = Column(BigInteger, primary_key=True, comment='关联ID')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='员工ID')
    question_id = Column(ForeignKey('question.question_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='问题ID')
    question_option_id = Column(ForeignKey('question_option.question_option_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='选项id')
    question_type_id = Column(ForeignKey('dict_question_type.question_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='分类ID')
    other_content = Column(VARCHAR(1000), nullable=False, server_default=text("''"), comment='其他内容')
    content = Column(VARCHAR(2000), nullable=False, server_default=text("''"), comment='问答题内容')
    orders = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='选择顺序')
    likes = Column(Integer, nullable=False, server_default=text("'0'"), comment='获得赞数')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')
    answer_type = Column(TINYINT, nullable=False, server_default=text("'1'"), comment='回答类型1：个人 2：标准')

    question = relationship('Question')
    question_option = relationship('QuestionOption')
    question_type = relationship('DictQuestionType')
    tp_user = relationship('TouchpointUser')


class RelRoleView(Base):
    __tablename__ = 'rel_role_view'
    __table_args__ = {'comment': '角色视图关系表'}

    rel_role_view_id = Column(VARCHAR(64), primary_key=True)
    role_id = Column(ForeignKey('dict_tp_user_role.dict_tp_user_role_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='角色id')
    view_id = Column(ForeignKey('function_view.function_view_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='视图id')
    sn = Column(Integer, comment='序号')

    role = relationship('DictTpUserRole')
    view = relationship('FunctionView')


class RelTpuRole(Base):
    __tablename__ = 'rel_tpu_role'
    __table_args__ = {'comment': '用户角色关联表'}

    rel_tpu_role_id = Column(VARCHAR(64), primary_key=True)
    tpu_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='触点用户id')
    role_id = Column(ForeignKey('dict_tp_user_role.dict_tp_user_role_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='角色id')
    valid_flag = Column(VARCHAR(1), server_default=text("'1'"), comment='1:生效；0:不生效')

    role = relationship('DictTpUserRole')
    tpu = relationship('TouchpointUser')


class ReportTemplate(Base):
    __tablename__ = 'report_template'
    __table_args__ = {'comment': '智能报告模板表'}

    template_id = Column(BigInteger, primary_key=True, comment='模板表主键')
    template_name = Column(VARCHAR(256), comment='模板名称')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='创建人id')
    use_counter = Column(Integer, server_default=text("'0'"), comment='使用次数')
    is_enable = Column(TINYINT, server_default=text("'0'"), comment='是否启用 1：启用 0：不启用')
    prompt = Column(TEXT, comment='模版提示词')
    auth_scheme_id = Column(ForeignKey('content_auth_scheme.auth_scheme_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='权限ID')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"))
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='1:已删除 0：未删除')
    template_content = Column(LONGTEXT, comment='模板内容')
    outline_level = Column(Integer, comment='大纲级别')
    outline_num = Column(Integer, comment='大纲个数')
    index_style = Column(String(255, 'utf8mb4_general_ci'), comment='编号样式')
    generate_mode = Column(TINYINT(1), comment='1:AI生成 2:自定义')
    template_describe = Column(String(255, 'utf8mb4_general_ci'), comment='模板说明')
    template_type = Column(TINYINT(1), comment='1:私有模板 2预置模板')
    template_subjects = Column(String(255, 'utf8mb4_general_ci'), comment='主题词（英文，分割）')
    template_key_words = Column(String(255, 'utf8mb4_general_ci'), comment='关键词（英文，分割）')

    auth_scheme = relationship('ContentAuthScheme')
    tp_user = relationship('TouchpointUser')


class TpuDatum(Base):
    __tablename__ = 'tpu_data'
    __table_args__ = {'comment': '触点用户批处理表'}

    tpu_data_id = Column(VARCHAR(64), primary_key=True, comment='触点用户数据id')
    tpu_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='触点用户id')
    latest_data_time = Column(DateTime, comment='最新更新日期')
    stat_date = Column(Date, comment='统计日期')
    convert_client_num = Column(Integer, comment='覆盖客户数')
    fission_point_num = Column(Integer, comment='裂变触点数')
    fission_index = Column(Integer, comment='统计值当日的裂变指数')
    pageview_count = Column(Integer, comment='阅读量')
    finish_read_count = Column(Integer, comment='完成量')
    like_count = Column(Integer, comment='点赞量')
    retweets_count = Column(Integer, comment='转发量')
    comment_count = Column(Integer, comment='评论量')
    total_read_time = Column(Integer, comment='总阅读时长')
    new_clue_num = Column(Integer, comment='新客户量')
    avg_rank = Column(Integer, comment='平均排名')
    rank_date = Column(DateTime, comment='排名周期时间')

    tpu = relationship('TouchpointUser')


class TpuSeatHistory(Base):
    __tablename__ = 'tpu_seat_history'

    tpu_seat_history_id = Column(VARCHAR(64), primary_key=True)
    tpu_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='用户id')
    seat_operate = Column(VARCHAR(2), server_default=text("'1'"), comment='+1:增加；-1:去除')
    operate_time = Column(DateTime, comment='操作时间')
    operate_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='用户操作人id')

    operate_user = relationship('TouchpointUser', primaryjoin='TpuSeatHistory.operate_user_id == TouchpointUser.tp_user_id')
    tpu = relationship('TouchpointUser', primaryjoin='TpuSeatHistory.tpu_id == TouchpointUser.tp_user_id')


class TpuTotalDatum(Base):
    __tablename__ = 'tpu_total_data'

    tpu_total_data_id = Column(VARCHAR(64), primary_key=True)
    tpu_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True)
    basic_read_num = Column(Integer, server_default=text("'0'"), comment='客户打开量')
    basic_collect_num = Column(Integer, server_default=text("'0'"), comment='客户点赞量')
    basic_trans_num = Column(Integer, server_default=text("'0'"), comment='客户转发量')
    basic_clue_num = Column(Integer, server_default=text("'0'"), comment='新线索量')
    basic_comment_num = Column(Integer, server_default=text("'0'"), comment='评论量')
    basic_interactive_num = Column(Integer, server_default=text("'0'"), comment='客户交互量')
    modify_time = Column(DateTime)
    current_rank = Column(Integer, comment='最新排名')
    current_fission_index = Column(DECIMAL(8, 2), server_default=text("'40.00'"), comment='裂变指数')
    total_fission_index = Column(DECIMAL(8, 2), server_default=text("'40.00'"), comment='总体裂变指数')

    tpu = relationship('TouchpointUser')


class ViewComponent(Base):
    __tablename__ = 'view_component'
    __table_args__ = {'comment': '页面组件库'}

    component_id = Column(VARCHAR(64), primary_key=True)
    component_name = Column(VARCHAR(255), comment='组件名称')
    function_view_id = Column(ForeignKey('function_view.function_view_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='页面id')
    valid_flag = Column(VARCHAR(1), server_default=text("'1'"), comment='是否有效')
    com_type_id = Column(ForeignKey('dict_component_type.com_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='组件类型id')
    default_value = Column(VARCHAR(255), comment='默认值')
    placeholder = Column(VARCHAR(255), comment='placeholder值')
    sn = Column(Integer, comment='序号')
    component_code = Column(VARCHAR(255), comment='组件code，用来区分同一页面的组件')
    help_info = Column(VARCHAR(255), comment='帮助组件信息')

    com_type = relationship('DictComponentType')
    function_view = relationship('FunctionView')


class ViewTip(Base):
    __tablename__ = 'view_tips'
    __table_args__ = {'comment': '页面提醒tips'}

    view_tips_id = Column(VARCHAR(64), primary_key=True, comment='页面提示信息id')
    function_view_id = Column(ForeignKey('function_view.function_view_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='功能页面id')
    tips_sn = Column(Integer, nullable=False, server_default=text("'0'"), comment='提示序号')
    tips_content = Column(VARCHAR(500), nullable=False, server_default=text("''"), comment='提示内容')

    function_view = relationship('FunctionView')


class AdMission(Base):
    __tablename__ = 'ad_mission'
    __table_args__ = {'comment': '投放任务表'}

    ad_mission_id = Column(VARCHAR(64), primary_key=True)
    media_channel_id = Column(ForeignKey('media_channel.media_channel_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='媒体渠道id')
    ad_mission_name = Column(VARCHAR(255), comment='投放任务名称')
    add_time = Column(DateTime, comment='添加时间')
    add_tpu_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加用户id')
    repeat_flag = Column(VARCHAR(1), comment='周期任务标志；0：非周期任务；1：周期任务')
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    budget = Column(DECIMAL(10, 2), comment='预算：元')
    repeat_type = Column(VARCHAR(1), comment='周期任务类型；0：季度；1：月；2：周')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='删除标志位;0:未删除；1:已删除')
    media_account_id = Column(BigInteger, comment='授权账号ID')

    add_tpu = relationship('TouchpointUser')
    media_channel = relationship('MediaChannel')


class AdmissionDatum(AdMission):
    __tablename__ = 'admission_data'
    __table_args__ = {'comment': '任务数据表'}

    admission_id = Column(ForeignKey('ad_mission.ad_mission_id', ondelete='RESTRICT', onupdate='RESTRICT'), primary_key=True, comment='投放任务id')
    show_num = Column(Integer, server_default=text("'0'"), comment='展示量')
    click_num = Column(Integer, server_default=text("'0'"), comment='点击数')
    open_num = Column(Integer, server_default=text("'0'"), comment='打开数')
    read_time = Column(Integer, server_default=text("'0'"), comment='阅读时长，毫秒')
    commit_num = Column(Integer, server_default=text("'0'"), comment='提交数')
    roi = Column(DECIMAL(5, 2), server_default=text("'0.00'"), comment='投入产出比；roi=total_return/total_cost')
    click_rate = Column(DECIMAL(5, 2), server_default=text("'0.00'"), comment='点击率')
    commit_rate = Column(DECIMAL(5, 2), server_default=text("'0.00'"), comment='提交率')
    ocpm = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='千次展示量费用')
    add_time = Column(DateTime, comment='添加时间')
    total_cost = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='总广告花费')
    total_return = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='总回款')
    total_sign = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='总签单')
    convert_cost = Column(DECIMAL(12, 2), comment='转化成本')
    convert_rate = Column(Float(6), comment='转化率')
    avg_click_cost = Column(DECIMAL(10, 2), comment='平均点击单价')
    valid_num = Column(Integer, server_default=text("'0'"), comment='转化有效数、表单有效数')


class AgentAgentKnowledge(Base):
    __tablename__ = 'agent_agent_knowledge'
    __table_args__ = {'comment': '智能体关联知识库'}

    id = Column(Integer, primary_key=True)
    agent_id = Column(ForeignKey('agent_agent.agent_id'), nullable=False, index=True, comment='智能体id')
    knowledge_id = Column(ForeignKey('aigc_knowledge.knowledge_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='知识库id')
    created_time = Column(DateTime)
    updated_time = Column(DateTime)
    delete_flag = Column(TINYINT(1), comment='删除状态')

    agent = relationship('AgentAgent')
    knowledge = relationship('AigcKnowledge')


class AigcAppPrompt(Base):
    __tablename__ = 'aigc_app_prompt'
    __table_args__ = {'comment': '应用提示词变量表'}

    prompt_id = Column(BigInteger, primary_key=True, comment='提示词变量ID')
    app_id = Column(ForeignKey('aigc_app.app_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='应用ID')
    prompt_type = Column(VARCHAR(255), comment='提示词变量类型 text:文本 paragraph：段落 drop_down:下拉\\n\\n')
    prompt_key = Column(VARCHAR(255), comment='变量关键字')
    field_name = Column(VARCHAR(255), comment='关键字名称')
    is_required = Column(TINYINT(1), server_default=text("'1'"), comment='是否必填 1：是 0：否')
    prompt_json = Column(TEXT, comment='Type 为下拉时必填')
    placeholeder = Column(VARCHAR(255), comment='placeholeder')
    sort = Column(Integer, server_default=text("'99'"), comment='排序')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0：未删除 1：已删除')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人')

    app = relationship('AigcApp')
    tp_user = relationship('TouchpointUser')


class AigcAppSession(Base):
    __tablename__ = 'aigc_app_session'
    __table_args__ = {'comment': 'ai应用会话表'}

    session_id = Column(BigInteger, primary_key=True, comment='会话ID')
    app_id = Column(ForeignKey('aigc_app.app_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='应用ID')
    session_name = Column(VARCHAR(255), comment='会话名称')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='创建人')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    prompt_list = Column(VARCHAR(2000), comment='提示词变量')
    is_top = Column(TINYINT(1), server_default=text("'0'"), comment='是否置顶 1：是 0：否')
    session_type = Column(VARCHAR(50), server_default=text("'search'"), comment='会话类型 search：定制检索  report：智能报表')
    report_date = Column(VARCHAR(50), comment='智能报告日期')

    app = relationship('AigcApp')
    tp_user = relationship('TouchpointUser')


class ChatbiDataView(Base):
    __tablename__ = 'chatbi_data_view'
    __table_args__ = {'comment': '数据视图配置表'}

    data_view_id = Column(BigInteger, primary_key=True, comment='数据视图id')
    date_view_name = Column(VARCHAR(255), comment='数据视图名称')
    data_source_id = Column(ForeignKey('chatbi_data_source.data_source_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='数据源id')
    data_source_table = Column(VARCHAR(255), comment='数据源表名')
    status = Column(TINYINT(1), server_default=text("'1'"), comment='1:启用 0：停用')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    add_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True)
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))
    data_count = Column(Integer, nullable=False, server_default=text("'0'"))

    add_user = relationship('TouchpointUser', primaryjoin='ChatbiDataView.add_user_id == TouchpointUser.tp_user_id')
    data_source = relationship('ChatbiDataSource')
    update_user = relationship('TouchpointUser', primaryjoin='ChatbiDataView.update_user_id == TouchpointUser.tp_user_id')


class ClientPortrait(Base):
    __tablename__ = 'client_portrait'
    __table_args__ = {'comment': '客户画像表'}

    client_id = Column(ForeignKey('client.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True)
    update_date = Column(DateTime, comment='客户画像更新时间')
    client_concern = Column(TEXT, comment='客户关注；用分号隔开')
    client_dimension = Column(VARCHAR(500), comment='客户维度：json数据表示')
    similar_client_list = Column(VARCHAR(2000), comment='近10个相似客户的列表')
    portrait_id = Column(VARCHAR(64), primary_key=True, comment='客户画像id')

    client = relationship('Client')


class ClientServiceEvaluation(Base):
    __tablename__ = 'client_service_evaluation'
    __table_args__ = {'comment': '服务评价'}

    evaluation_id = Column(BigInteger, primary_key=True, comment='服务评价ID')
    client_id = Column(ForeignKey('client.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, server_default=text("''"), comment='客户ID')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='服务者ID')
    tags_ids = Column(VARCHAR(1000), nullable=False, comment='标签IDs')
    star = Column(Integer, nullable=False, server_default=text("'5'"), comment='评价星级 5，4，3，2，1')
    content = Column(VARCHAR(255), nullable=False, comment='评价内容')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='编辑时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')
    tp_user_phone = Column(VARCHAR(64), nullable=False, server_default=text("''"), comment='员工手机号')
    work_id = Column(Integer, nullable=False, server_default=text("'0'"), comment='关联work平台id')

    client = relationship('Client')
    tp_user = relationship('TouchpointUser')


class ClientServiceRole(Base):
    __tablename__ = 'client_service_role'
    __table_args__ = {'comment': '客户关联服务角色'}

    service_role_id = Column(BigInteger, primary_key=True, comment='服务角色ID')
    client_id = Column(ForeignKey('client.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='客户ID')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='员工ID')
    role_name = Column(VARCHAR(50), comment='角色名称')
    phone_no = Column(VARCHAR(255), server_default=text("''"), comment='手机号')
    is_substitution = Column(TINYINT, server_default=text("'0'"), comment='是否申请换人 1：是 0：否')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='0：我删除 1：已删除')
    work_id = Column(Integer, server_default=text("'0'"), comment='Work平台申请换人id')

    client = relationship('Client')
    tp_user = relationship('TouchpointUser')


class ClientTpHistory(Base):
    __tablename__ = 'client_tp_history'
    __table_args__ = {'comment': '客户与员工触点历史表'}

    history_id = Column(VARCHAR(64), primary_key=True)
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True)
    client_id = Column(ForeignKey('client.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True)
    start_time = Column(DateTime, comment='服务开始时间')
    end_time = Column(DateTime, comment='服务结束时间')
    current_service_flag = Column(VARCHAR(1), server_default=text("'1'"), comment='当前是否服务；0:未服务；1:在服务')

    client = relationship('Client')
    tp_user = relationship('TouchpointUser')


class CodeFusion(Base):
    __tablename__ = 'code_fusion'
    __table_args__ = {'comment': '客户雷达配置存储'}

    fusion_id = Column(Integer, primary_key=True, comment='融合映射id')
    channel_id = Column(ForeignKey('config_channel.channel_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='对外展示渠道')
    user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='参数渠道')
    qr_code = Column(VARCHAR(255), comment='二维码')
    qr_config_code = Column(VARCHAR(50), comment='qr_code的config code信息')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')

    channel = relationship('ConfigChannel')
    user = relationship('TouchpointUser')


class ComponentValue(Base):
    __tablename__ = 'component_value'
    __table_args__ = {'comment': '页面组件值表'}

    com_val_id = Column(VARCHAR(64), primary_key=True)
    component_id = Column(ForeignKey('view_component.component_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='组件id')
    com_val = Column(VARCHAR(255), comment='组件值')
    com_val_url = Column(VARCHAR(255), comment='组件路由(可选)')

    component = relationship('ViewComponent')


class Course(Base):
    __tablename__ = 'course'
    __table_args__ = {'comment': '课程表'}

    course_id = Column(BigInteger, primary_key=True, comment='课程Id')
    course_name = Column(VARCHAR(255), comment='课程名')
    course_content = Column(TEXT, comment='课程介绍')
    cover_url = Column(VARCHAR(255), comment='课程封面')
    course_category_id = Column(ForeignKey('course_category.course_category_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='课程分类ID')
    price = Column(DECIMAL(10, 2), comment='价格')
    term = Column(VARCHAR(50), comment='学期数')
    sort = Column(Integer, server_default=text("'999999'"), comment='排序')
    status = Column(TINYINT, comment='1：上架  0：下架\\n')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人ID')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='0:未删除 1：已删除')
    teacher_ids = Column(VARCHAR(1000), comment='关联老师')
    is_index = Column(TINYINT(1), server_default=text("'0'"), comment='是否首页显示 1：是 0：否')

    course_category = relationship('CourseCategory')
    tp_user = relationship('TouchpointUser')


class CourseTeacherFile(Base):
    __tablename__ = 'course_teacher_file'
    __table_args__ = {'comment': '师资附件表'}

    teacher_file_id = Column(BigInteger, primary_key=True, comment='附件ID')
    teacher_id = Column(ForeignKey('course_teacher.course_teacher_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='老师ID')
    file_type = Column(TINYINT, comment='附件类型 1：毕业证 2：身份证 3：资格证')
    file_url = Column(VARCHAR(500), comment='Oss 路径')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='1:已删除 0：未删除')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')

    teacher = relationship('CourseTeacher')


class DirectoryLevel(Base):
    __tablename__ = 'directory_level'
    __table_args__ = (
        Index('name', 'name', 'pid', unique=True),
        {'comment': '目录层级'}
    )

    id = Column(Integer, primary_key=True, index=True)
    name = Column(VARCHAR(255), nullable=False, comment='目录名称')
    pid = Column(ForeignKey('directory_level.id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='父级id')
    level = Column(Integer, nullable=False, comment='菜单等级')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='修改时间')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='创作者id')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='0:未删除；1已删除')
    desc = Column(VARCHAR(255), comment='描述')
    bucket_id = Column(ForeignKey('bucket_info.id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='容器id')
    visibility = Column(TINYINT, server_default=text("'0'"), comment='可见范围 0全员；1自定义')
    dlr = Column(TINYINT, server_default=text("'0'"), comment='下载范围 0全员；1自定义')

    bucket = relationship('BucketInfo')
    parent = relationship('DirectoryLevel', remote_side=[id])
    tp_user = relationship('TouchpointUser')


class FModelEvaluate(Base):
    __tablename__ = 'f_model_evaluate'
    __table_args__ = {'comment': '训练-模型评估表'}

    evaluate_id = Column(VARCHAR(36), primary_key=True, comment='模型评估ID')
    train_request_id = Column(ForeignKey('f_train_request.train_request_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='模型训练ID')
    is_saves = Column(TINYINT(1), server_default=text("'1'"), comment='是否保存预测结果 1：是 0：否')
    output_dir = Column(VARCHAR(255), comment='输出目录')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='创建人')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    status = Column(TINYINT(1), server_default=text("'1'"), comment='评估状态1：已保存 2：等待中 3：评估中 4：评估完成 5 失败 6:中断 -1:已取消')
    register_datasets = Column(VARCHAR(512), comment='register_dataset_names name1,name2')
    cutoff_len = Column(Integer, comment='截断长度')
    max_samples = Column(Integer, comment='最大样本数')
    per_device_eval_batch_size = Column(Integer, comment='批处理大小')
    max_new_tokens = Column(Integer, comment='最大生成长度')
    top_p = Column(DECIMAL(5, 2), comment='top-p 采样值')
    temperature = Column(DECIMAL(5, 2), comment='温度系数')
    update_time = Column(DateTime, comment='更新detail info时间')
    evaluate_time = Column(DateTime, comment='开始评估时间')
    predict_results = Column(VARCHAR(512), comment='模型评估结果')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1:已删除 0：未删除')
    is_urgency = Column(TINYINT(1), server_default=text("'0'"), comment='紧急程度 1：紧急 0：非紧急')
    elapsed_times = Column(Integer, server_default=text("'0'"), comment='已流评估时长 单位秒')
    remaining_times = Column(Integer, server_default=text("'0'"), comment='剩余评估时长 单位秒')
    percentage = Column(DECIMAL(5, 2), server_default=text("'0.00'"), comment='评估进度')
    request_time = Column(DateTime, comment='提交评估申请时间')
    success_time = Column(DateTime, comment='评估完成时间')
    process_id = Column(Integer, comment='进程ID')
    resource = Column(VARCHAR(255), comment='所需资源')

    tp_user = relationship('TouchpointUser')
    train_request = relationship('FTrainRequest')


class FTrainRequestParam(Base):
    __tablename__ = 'f_train_request_params'
    __table_args__ = {'comment': '训练-训练请求参数表'}

    train_param_id = Column(VARCHAR(36), primary_key=True, comment='训练参数ID')
    train_request_id = Column(ForeignKey('f_train_request.train_request_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='训练请求ID')
    param_code = Column(VARCHAR(50), comment='参数编码')
    param_value = Column(VARCHAR(1000), comment='参数值')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1:已删除 0：未删除')

    train_request = relationship('FTrainRequest')


class FileLifeRule(Base):
    __tablename__ = 'file_life_rule'
    __table_args__ = {'comment': '生命周期规则'}

    life_rule_id = Column(Integer, primary_key=True)
    life_type = Column(VARCHAR(30), nullable=False, server_default=text("'days'"), comment='计算方式 date；days')
    life_days = Column(Integer, server_default=text("'0'"), comment='有效天数')
    expire_date = Column(DateTime, comment='过期时间')
    file_scope = Column(TINYINT, server_default=text("'0'"), comment='作用文件范围 1全部；0自定义')
    knowledge_id = Column(ForeignKey('aigc_knowledge.knowledge_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='知识库id')

    knowledge = relationship('AigcKnowledge')


class MiniCollect(Base):
    __tablename__ = 'mini_collect'
    __table_args__ = {'comment': '小程序我的收藏表'}

    collect_id = Column(BigInteger, primary_key=True, comment='收藏ID')
    client_id = Column(ForeignKey('client.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, server_default=text("''"), comment='客户ID')
    collect_type = Column(VARCHAR(50), nullable=False, server_default=text("''"), comment='收藏类型 article：文章 write: 白皮书')
    rel_id = Column(BigInteger, nullable=False, comment='关联ID')
    title = Column(VARCHAR(255), nullable=False, comment='收藏标题')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='收藏时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')

    client = relationship('Client')


class MiniFeedback(Base):
    __tablename__ = 'mini_feedback'
    __table_args__ = {'comment': '小程序功能反馈表'}

    feedback_id = Column(BigInteger, primary_key=True, comment='功能反馈ID')
    client_id = Column(ForeignKey('client.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, server_default=text("''"), comment='客户ID')
    content = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='意见和建议内容')
    img_url1 = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='图片1')
    img_url2 = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='图片2')
    img_url3 = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='图片3')
    phone_number = Column(VARCHAR(20), nullable=False, comment='联系方式')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')
    content_type = Column(TINYINT, nullable=False, server_default=text("'1'"), comment='类型 1：意见反馈 2：投诉')
    work_id = Column(Integer, nullable=False, server_default=text("'0'"), comment='关联work平台id')

    client = relationship('Client')


class MiniMessage(Base):
    __tablename__ = 'mini_message'
    __table_args__ = {'comment': '小程序 消息表'}

    message_id = Column(BigInteger, primary_key=True, comment='消息ID')
    client_id = Column(ForeignKey('client.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='客户ID')
    message = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='消息')
    is_read = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='是否已读 0：未读 1：已读')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='发送时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='是否删除 0：未删除 1：已删除')

    client = relationship('Client')


class MiniReservation(Base):
    __tablename__ = 'mini_reservation'
    __table_args__ = {'comment': '小程序预约表'}

    reservation_id = Column(BigInteger, primary_key=True, comment='产品预约ID')
    client_id = Column(ForeignKey('client.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, server_default=text("''"), comment='客户ID')
    name = Column(VARCHAR(50), nullable=False, comment='姓名')
    company = Column(VARCHAR(100), nullable=False, server_default=text("''"), comment='公司名称')
    phone_number = Column(VARCHAR(30), nullable=False, server_default=text("''"), comment='手机号')
    product_id = Column(ForeignKey('product.product_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='产品ID')
    reservation_time = Column(VARCHAR(30), nullable=False, server_default=text("''"), comment='预约时间')
    status = Column(TINYINT, nullable=False, server_default=text("'1'"), comment='1：待确认 2：已确认 3：已结束 4： 终止')
    presenter = Column(VARCHAR(64), nullable=False, server_default=text("''"), comment='演示人')
    meeting_number = Column(VARCHAR(100), nullable=False, server_default=text("''"), comment='会议号')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')
    back_status = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='后台状态 0：未分配 1：未联系 2：已分配 3：已联系')
    remarks = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='备注')
    tp_user_id = Column(VARCHAR(64), nullable=False, server_default=text("''"), comment='负责人ID')
    demand = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='需求')
    product_type = Column(VARCHAR(50), nullable=False, server_default=text("''"), comment='产品类型')
    work_id = Column(Integer, nullable=False, server_default=text("'0'"), comment='关联work平台id')
    demo_time = Column(VARCHAR(50), nullable=False, server_default=text("''"), comment='演示时间')
    work_pj_id = Column(Integer, nullable=False, server_default=text("'0'"), comment='work 平台评价ID')
    version = Column(VARCHAR(255), server_default=text("''"), comment='版本')
    remote = Column(VARCHAR(1), nullable=False, server_default=text("''"), comment='演示平台 腾讯会议=1;钉钉会议=2;QQ演示=3;向日葵=4;其他=5')

    client = relationship('Client')
    product = relationship('Product')


class OperateAnalysisRelation(Base):
    __tablename__ = 'operate_analysis_relation'
    __table_args__ = {'comment': '分析类型-图表关联表'}

    relation_id = Column(BigInteger, primary_key=True, comment='关联ID')
    dashboard_id = Column(ForeignKey('operate_dashboard.dashboard_id'), nullable=False, index=True, comment='关联的看板ID')
    analysis_type_id = Column(ForeignKey('operate_dict_analysis_type.analysis_type_id'), nullable=False, index=True, comment='分析类型ID')
    graph_id = Column(ForeignKey('operate_gragh.graph_id'), nullable=False, index=True, comment='关联的图表ID')
    sort = Column(Integer, comment='排序')
    status = Column(TINYINT(1), server_default=text("'1'"), comment='状态 1:启用 0:停用')
    create_user_id = Column(ForeignKey('touchpoint_user.tp_user_id'), index=True, comment='创建人')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1:已删除')

    analysis_type = relationship('OperateDictAnalysisType')
    create_user = relationship('TouchpointUser')
    dashboard = relationship('OperateDashboard')
    graph = relationship('OperateGragh')


class OperateCustomChartType(Base):
    __tablename__ = 'operate_custom_chart_type'
    __table_args__ = {'comment': '分析图表类型表'}

    type_id = Column(BigInteger, primary_key=True, comment='类型ID')
    type_code = Column(VARCHAR(50), nullable=False, unique=True, comment='图表类型编码')
    type_name = Column(VARCHAR(255), server_default=text("''"), comment='图表类型名称')
    icon = Column(VARCHAR(255), comment='图标')
    description = Column(TEXT, comment='描述')
    config_schema = Column(JSON, comment='配置schema')
    default_config = Column(JSON, comment='默认配置')
    status = Column(TINYINT(1), server_default=text("'0'"), comment='状态 1:启用 0:停用')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    create_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人id')
    product_id = Column(ForeignKey('operate_dashboard.product_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='产品id')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='修改时间')

    create_user = relationship('TouchpointUser')
    product = relationship('OperateDashboard')


class OperateCustomSqlTemplate(Base):
    __tablename__ = 'operate_custom_sql_template'
    __table_args__ = {'comment': '自定义分析SQL模板表'}

    template_id = Column(BigInteger, primary_key=True, comment='模板ID')
    template_name = Column(VARCHAR(255), server_default=text("''"), comment='模板名称')
    description = Column(TEXT, comment='模板描述')
    sql_content = Column(TEXT, nullable=False, comment='SQL语句内容')
    params_config = Column(JSON, comment='参数配置(待定)')
    status = Column(TINYINT(1), server_default=text("'0'"), comment='状态 1:启用 0:停用')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    create_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人id')
    product_id = Column(ForeignKey('operate_dashboard.product_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='产品id')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='修改时间')

    create_user = relationship('TouchpointUser')
    product = relationship('OperateDashboard')


class OperateDashboardContent(Base):
    __tablename__ = 'operate_dashboard_content'
    __table_args__ = {'comment': '自定义看板内容表'}

    content_id = Column(BigInteger, primary_key=True, comment='内容ID')
    dashboard_id = Column(ForeignKey('operate_dashboard.dashboard_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='关联的自定义看板ID')
    content_name = Column(VARCHAR(255), comment='内容名称')
    display_type = Column(TINYINT(1), server_default=text("'1'"), comment='展示类型 1:图表 2:表格')
    function_name = Column(VARCHAR(255), comment='函数名称或方法')
    status = Column(TINYINT(1), server_default=text("'1'"), comment='状态 1:启用 0:停用')
    create_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='创建人')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    update_user_id = Column(VARCHAR(36), index=True, comment='修改人')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='修改时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    graph_id = Column(ForeignKey('operate_gragh.graph_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='关联的图表分析ID')

    create_user = relationship('TouchpointUser')
    dashboard = relationship('OperateDashboard')
    graph = relationship('OperateGragh')


class OperateRelGraphTag(Base):
    __tablename__ = 'operate_rel_graph_tags'
    __table_args__ = {'comment': '智能运营图表关联标签表'}

    rel_tag_id = Column(BigInteger, primary_key=True, comment='图和标签关联ID')
    graph_id = Column(ForeignKey('operate_gragh.graph_id'), index=True, comment='分析图表ID')
    tags_id = Column(ForeignKey('tags.tags_id'), index=True, comment='标签ID')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='1:已删除 0：未删除')

    graph = relationship('OperateGragh')
    tags = relationship('Tag')


class OperateUserProfileTemplate(Base):
    __tablename__ = 'operate_user_profile_template'
    __table_args__ = {'comment': '用户画像模板表'}

    profile_template_id = Column(BigInteger, primary_key=True, comment='用户画像模板id')
    profile_template_name = Column(VARCHAR(255), comment='用户画像模板名称')
    profile_template_type = Column(TINYINT, server_default=text("'0'"), comment='用户画像模板分类（0:预置 1:自定义）')
    group_id = Column(ForeignKey('operate_groups.group_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='用户群id')
    profile_template_desc = Column(TEXT, comment='用户画像模板描述')
    use_tgi = Column(TINYINT(1), server_default=text("'0'"), comment='是否统计TGI（0:不统计，1:统计）')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    create_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人id')
    update_time = Column(DateTime, comment='更新时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='删除标志（0：未删除，1：删除）')
    product_id = Column(BigInteger, comment='产品id')
    insight_id = Column(String(255, 'utf8mb4_general_ci'), comment='用户画像洞察维度id, 使用英文逗号拼接')

    create_user = relationship('TouchpointUser')
    group = relationship('OperateGroup')


class QbAnswer(Base):
    __tablename__ = 'qb_answer'
    __table_args__ = {'comment': '题库对应答案和分值'}

    ans_id = Column(Integer, primary_key=True, comment='答案id')
    qb_id = Column(ForeignKey('question_bank.question_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='试题id')
    ans_text = Column(TEXT, comment='答案')
    score = Column(Float, server_default=text("'0'"), comment='分数值')
    sort = Column(Integer, server_default=text("'9999'"), comment='排序值')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人')
    delete_flag = Column(Integer, server_default=text("'0'"), comment='1:已删除 0：未删除')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='修改时间')

    qb = relationship('QuestionBank')
    tp_user = relationship('TouchpointUser')


class RelAigcApp(Base):
    __tablename__ = 'rel_aigc_app'
    __table_args__ = {'comment': 'Aigc 应用层级关系表'}

    rel_id = Column(BigInteger, primary_key=True, comment='关联ID')
    app_id_entry = Column(ForeignKey('aigc_app.app_id'), nullable=False, index=True, comment='入口ID')
    app_id = Column(ForeignKey('aigc_app.app_id'), nullable=False, index=True, comment='应用ID')
    tp_user_id = Column(VARCHAR(36), comment='操作人')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')

    app = relationship('AigcApp', primaryjoin='RelAigcApp.app_id == AigcApp.app_id')
    aigc_app = relationship('AigcApp', primaryjoin='RelAigcApp.app_id_entry == AigcApp.app_id')


class RelAppKnowledge(Base):
    __tablename__ = 'rel_app_knowledge'
    __table_args__ = {'comment': 'Aigc 应用管理模型表'}

    rel_id = Column(BigInteger, primary_key=True, comment='关联ID')
    app_id = Column(ForeignKey('aigc_app.app_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='应用ID')
    knowledge_id = Column(ForeignKey('aigc_knowledge.knowledge_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='知识库ID')
    delete_flag = Column(TINYINT(1), comment='0:未删除 1：已删除')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')

    app = relationship('AigcApp')
    knowledge = relationship('AigcKnowledge')
    tp_user = relationship('TouchpointUser')


class RelAppQa(Base):
    __tablename__ = 'rel_app_qa'
    __table_args__ = {'comment': 'Aigc 应用层级关系表'}

    rel_id = Column(BigInteger, primary_key=True, comment='关联ID')
    app_id = Column(ForeignKey('aigc_app.app_id'), nullable=False, index=True, comment='app ID')
    qa_id = Column(ForeignKey('aigc_qa.qa_id'), nullable=False, index=True, comment='qa ID')
    tp_user_id = Column(VARCHAR(36), comment='操作人')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')

    app = relationship('AigcApp')
    qa = relationship('AigcQa')


class RelAppQaLib(Base):
    __tablename__ = 'rel_app_qa_lib'
    __table_args__ = {'comment': 'Qa关联应用'}

    rel_id = Column(BigInteger, primary_key=True)
    app_id = Column(ForeignKey('aigc_app.app_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='应用id')
    qa_lib_id = Column(BigInteger, comment='qa库id')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='删除标识0；1')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='修改时间')

    app = relationship('AigcApp')
    tp_user = relationship('TouchpointUser')


class RelCardTpu(Base):
    __tablename__ = 'rel_card_tpu'

    rel_card_tpu_id = Column(VARCHAR(64), primary_key=True)
    ect_id = Column(ForeignKey('electric_card_template.ect_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='电子名片模板id')
    tpu_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='触点id')
    rel_time = Column(DateTime, comment='启用时间')
    client_send_count = Column(Integer, comment='发送给客户次数')
    client_open_count = Column(Integer, comment='客户打开次数')
    client_close_count = Column(Integer, comment='客户关闭次数')
    client_avg_time = Column(Integer, comment='客户平均停留时间')
    client_trans_count = Column(Integer, comment='客户转发次数')
    uv_open = Column(Integer, server_default=text("'0'"), comment='打开客户数')
    uv_send = Column(Integer, server_default=text("'0'"), comment='发送客户数')
    uv_close = Column(Integer, server_default=text("'0'"), comment='关闭客户数')
    uv_trans = Column(Integer, server_default=text("'0'"), comment='转发客户数')

    ect = relationship('ElectricCardTemplate')
    tpu = relationship('TouchpointUser')


class RelChannelTag(Base):
    __tablename__ = 'rel_channel_tags'
    __table_args__ = {'comment': '渠道标签关联表'}

    channel_tags_id = Column(VARCHAR(64), primary_key=True)
    channel_id = Column(ForeignKey('config_channel.channel_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='渠道id')
    tags_id = Column(ForeignKey('tags.tags_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='标签id')
    rel_time = Column(DateTime, comment='关联时间')

    channel = relationship('ConfigChannel')
    tags = relationship('Tag')


class RelChannelWorkTpu(Base):
    __tablename__ = 'rel_channel_work_tpu'
    __table_args__ = {'comment': '渠道参与人关联表'}

    rel_channel_work_id = Column(VARCHAR(64), primary_key=True)
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='参与人id')
    media_channel_id = Column(ForeignKey('media_channel.media_channel_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='media_channel id')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='删除标志位')

    media_channel = relationship('MediaChannel')
    tp_user = relationship('TouchpointUser')


class RelClassExam(Base):
    __tablename__ = 'rel_class_exam'
    __table_args__ = {'comment': '班级配置试卷'}

    rel_id = Column(Integer, primary_key=True, comment='关联id')
    class_id = Column(ForeignKey('class_info.class_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='班级id')
    exam_id = Column(ForeignKey('exam_page.exam_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='试卷id')
    status = Column(Integer, server_default=text("'0'"), comment='发卷状态 0未下发；1答题中；2已结束')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人')

    _class = relationship('ClassInfo')
    exam = relationship('ExamPage')
    tp_user = relationship('TouchpointUser')


class RelClassTag(Base):
    __tablename__ = 'rel_class_tags'
    __table_args__ = {'comment': '班级标签表'}

    rel_class_tag_id = Column(BigInteger, primary_key=True)
    class_id = Column(ForeignKey('class_info.class_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='合伙人ID')
    tags_id = Column(ForeignKey('tags.tags_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='标签ID')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')

    _class = relationship('ClassInfo')
    tags = relationship('Tag')


class RelClientTag(Base):
    __tablename__ = 'rel_client_tags'
    __table_args__ = {'comment': '用户标签关联表'}

    rel_ct_id = Column(VARCHAR(64), primary_key=True)
    client_id = Column(ForeignKey('client.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='客户id')
    ct_id = Column(ForeignKey('tags.tags_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='标签id')
    update_time = Column(DateTime, comment='客户标签生效时间')
    valid_flag = Column(VARCHAR(1), server_default=text("'1'"), comment='标签是否生效；1：生效；0：失效')
    tags_value = Column(VARCHAR(255), server_default=text("''"), comment='标签值')

    client = relationship('Client')
    ct = relationship('Tag')


class RelClientTargeting(Base):
    __tablename__ = 'rel_client_targeting'
    __table_args__ = {'comment': '客户定向关联表'}

    client_targeting_id = Column(VARCHAR(64), primary_key=True, comment='客户定向id')
    targeting_type_id = Column(ForeignKey('dict_targeting_type.targeting_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='定向类型id')
    client_id = Column(ForeignKey('client.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='客户id')
    target_value = Column(VARCHAR(255), comment='定向值')

    client = relationship('Client')
    targeting_type = relationship('DictTargetingType')


class RelClientTpu(Base):
    __tablename__ = 'rel_client_tpu'
    __table_args__ = {'comment': '客户关联员工表'}

    rel_client_tpu_id = Column(BigInteger, primary_key=True, comment='客户关联员工ID')
    client_id = Column(ForeignKey('client.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='客户ID')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='员工ID')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')

    client = relationship('Client')
    tp_user = relationship('TouchpointUser')


class RelExamQc(Base):
    __tablename__ = 'rel_exam_qc'
    __table_args__ = {'comment': '试卷组卷规则'}

    rel_id = Column(Integer, primary_key=True, comment='关联id')
    exam_id = Column(ForeignKey('exam_page.exam_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='试卷id')
    qc_id = Column(ForeignKey('question_category.qc_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='题类id')

    exam = relationship('ExamPage')
    qc = relationship('QuestionCategory')


class RelFileRule(Base):
    __tablename__ = 'rel_file_rule'
    __table_args__ = {'comment': '文件配置清洗规则'}

    rel_id = Column(Integer, primary_key=True)
    knowledge_id = Column(ForeignKey('aigc_knowledge.knowledge_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True)
    rule_id = Column(ForeignKey('file_clear_rule.id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True)

    knowledge = relationship('AigcKnowledge')
    rule = relationship('FileClearRule')


class RelGroupConfig(Base):
    __tablename__ = 'rel_group_config'
    __table_args__ = {'comment': '客户群关联表'}

    rel_group_config_id = Column(VARCHAR(64), primary_key=True)
    group_config_id = Column(ForeignKey('client_group_config.client_group_config_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='群配置id')
    group_id = Column(ForeignKey('client_group.group_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='客户群id')
    rel_time = Column(DateTime, comment='关联时间')

    group_config = relationship('ClientGroupConfig')
    group = relationship('ClientGroup')


class RelQbQc(Base):
    __tablename__ = 'rel_qb_qc'
    __table_args__ = {'comment': '试题分类'}

    rel_id = Column(Integer, primary_key=True)
    qc_id = Column(ForeignKey('question_category.qc_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='题类id')
    qb_id = Column(ForeignKey('question_bank.question_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='试题id')

    qb = relationship('QuestionBank')
    qc = relationship('QuestionCategory')


class RelQbTag(Base):
    __tablename__ = 'rel_qb_tag'

    rel_id = Column(Integer, primary_key=True, comment='关联id')
    qb_id = Column(ForeignKey('question_bank.question_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='试题id')
    tag_id = Column(ForeignKey('tags.tags_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='标签id')

    qb = relationship('QuestionBank')
    tag = relationship('Tag')


class RelQppFile(Base):
    __tablename__ = 'rel_qpp_file'
    __table_args__ = {'comment': '行业及痛点附件表'}

    rel_file_id = Column(BigInteger, primary_key=True, comment='答案关联附件')
    qpp_id = Column(ForeignKey('question_point_plan.qpp_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='关联痛点及方案iD')
    qpp_code = Column(VARCHAR(20), comment='关联类型 sore_point:痛点 describe：业务场景描述 function_point：解决功能点')
    file_url = Column(VARCHAR(500), comment='附件路径')
    file_name = Column(VARCHAR(255), comment='附件名称')
    file_content = Column(VARCHAR(1000), comment='文件描述')
    file_type = Column(VARCHAR(255), comment='附件类型 image ：图片 file:文件')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='新增时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')
    preview_list = Column(TEXT, comment='文档预览')

    qpp = relationship('QuestionPointPlan')


class RelQuestionTpuComment(Base):
    __tablename__ = 'rel_question_tpu_comment'
    __table_args__ = {'comment': '回答明细评论表'}

    rel_comment_id = Column(BigInteger, primary_key=True, comment='评论ID')
    rel_question_tpu_id = Column(ForeignKey('rel_question_tpu.rel_question_tup_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='回答ID')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='员工ID')
    content = Column(VARCHAR(255), comment='评论内容')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='评论时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0：已删除 1：未删除')

    rel_question_tpu = relationship('RelQuestionTpu')
    tp_user = relationship('TouchpointUser')


class RelQuestionTpuFile(Base):
    __tablename__ = 'rel_question_tpu_file'
    __table_args__ = {'comment': '答案关联附件表'}

    rel_file_id = Column(BigInteger, primary_key=True, comment='答案关联附件')
    rel_question_tup_id = Column(ForeignKey('rel_question_tpu.rel_question_tup_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='员工答案表')
    file_url = Column(VARCHAR(500), comment='附件路径')
    file_name = Column(VARCHAR(255), comment='附件名称')
    file_content = Column(VARCHAR(1000), comment='文件描述')
    file_type = Column(VARCHAR(255), comment='附件类型 image ：图片 file:文件')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='新增时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')

    rel_question_tup = relationship('RelQuestionTpu')


class RelQuestionTpuLike(Base):
    __tablename__ = 'rel_question_tpu_likes'
    __table_args__ = {'comment': '知识库回答点赞表'}

    rel_like_id = Column(BigInteger, primary_key=True, comment='点赞ID')
    rel_question_tup_id = Column(ForeignKey('rel_question_tpu.rel_question_tup_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='答案ID')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='员工ID')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='点赞时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除  1：已删除')

    rel_question_tup = relationship('RelQuestionTpu')
    tp_user = relationship('TouchpointUser')


class RelTeacherTag(Base):
    __tablename__ = 'rel_teacher_tags'
    __table_args__ = {'comment': '教师标签表'}

    rel_teacher_tag_id = Column(BigInteger, primary_key=True)
    teacher_id = Column(ForeignKey('course_teacher.course_teacher_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='合伙人ID')
    tags_id = Column(ForeignKey('tags.tags_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='标签ID')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')

    tags = relationship('Tag')
    teacher = relationship('CourseTeacher')


class RelTpuChannel(Base):
    __tablename__ = 'rel_tpu_channel'
    __table_args__ = {'comment': '用户渠道关联表'}

    tpu_channel_id = Column(VARCHAR(64), primary_key=True)
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='用户id')
    channel_id = Column(ForeignKey('config_channel.channel_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='渠道id')
    rel_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='关联时间')

    channel = relationship('ConfigChannel')
    tp_user = relationship('TouchpointUser')


class RelUnitUser(Base):
    __tablename__ = 'rel_unit_user'
    __table_args__ = {'comment': '用户归属单位'}

    rel_id = Column(Integer, primary_key=True)
    unit_id = Column(ForeignKey('affiliated_unit.unit_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True)
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True)

    tp_user = relationship('TouchpointUser')
    unit = relationship('AffiliatedUnit')


class ReportAi(Base):
    __tablename__ = 'report_ai'
    __table_args__ = {'comment': '智能报告表'}

    report_id = Column(BigInteger, primary_key=True, comment='智能报告ID')
    template_id = Column(ForeignKey('report_template.template_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='模版ID')
    report_name = Column(VARCHAR(255), comment='报告名称')
    report_date = Column(VARCHAR(50), comment='报告时间')
    report_prompt = Column(TEXT, comment='报告提示词')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='创建人')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    report_type = Column(TINYINT(1), comment='1:研究型报告，2:普通报告')
    report_model = Column(String(255, 'utf8mb4_general_ci'), comment='报告使用的大模型')
    report_process = Column(Float, comment='报告进度')
    report_abstract = Column(String(255, 'utf8mb4_general_ci'), comment='报告摘要')
    report_subjects = Column(String(255, 'utf8mb4_general_ci'), comment='主题词（英文，分割）')
    report_key_words = Column(String(255, 'utf8mb4_general_ci'), comment='关键词（英文，分割）')

    template = relationship('ReportTemplate')
    tp_user = relationship('TouchpointUser')


class ReportTemplateKnowledge(Base):
    __tablename__ = 'report_template_knowledge'
    __table_args__ = {'comment': '模板-知识库关系表'}

    template_knowledge_id = Column(Integer, primary_key=True)
    template_id = Column(ForeignKey('report_template.template_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='模板id')
    knowledge_id = Column(ForeignKey('aigc_knowledge.knowledge_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='知识库id')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1:已删除 0：未删除')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"))

    knowledge = relationship('AigcKnowledge')
    template = relationship('ReportTemplate')


class ReportTemplateParam(Base):
    __tablename__ = 'report_template_params'
    __table_args__ = {'comment': '模板-参数关系表'}

    template_param_id = Column(Integer, primary_key=True, comment='模板-参数关系表ID')
    param_id = Column(ForeignKey('report_dict_params.param_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='参数id')
    template_id = Column(ForeignKey('report_template.template_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='模板id')
    description = Column(VARCHAR(255))
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"))
    delete_flag = Column(TINYINT(1), comment='1:已删除 0：未删除')

    param = relationship('ReportDictParam')
    template = relationship('ReportTemplate')


class ReportTemplateTool(Base):
    __tablename__ = 'report_template_tools'
    __table_args__ = {'comment': '模板-工具关系表'}

    template_tool_id = Column(Integer, primary_key=True)
    template_id = Column(ForeignKey('report_template.template_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True)
    tool_id = Column(ForeignKey('report_tools.tool_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True)
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"))
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1:已删除 0：未删除')

    template = relationship('ReportTemplate')
    tool = relationship('ReportTool')


class TrackingDatum(Base):
    __tablename__ = 'tracking_data'
    __table_args__ = {'comment': '页面埋点统计数据'}

    tracking_data_id = Column(VARCHAR(64), primary_key=True, comment='跟踪数据id')
    function_view_id = Column(ForeignKey('function_view.function_view_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, server_default=text("''"), comment='功能视图id')
    click_count = Column(Integer, nullable=False, server_default=text("'0'"), comment='点击次数')
    stat_date = Column(Date, nullable=False, comment='统计日期')
    component_id = Column(ForeignKey('view_component.component_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, server_default=text("''"), comment='组件id')
    tracking_type_id = Column(ForeignKey('dict_view_tracking_type.tracking_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, server_default=text("''"), comment='埋点数据类型')

    component = relationship('ViewComponent')
    function_view = relationship('FunctionView')
    tracking_type = relationship('DictViewTrackingType')


class ChatbiDataGraph(Base):
    __tablename__ = 'chatbi_data_graph'
    __table_args__ = {'comment': '数据图表配置表'}

    data_graph_id = Column(BigInteger, primary_key=True, comment='数据图表id')
    data_graph_name = Column(VARCHAR(255), comment='数据图表名称')
    data_view_id = Column(ForeignKey('chatbi_data_view.data_view_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='数据视图id')
    graph_type_id = Column(ForeignKey('chatbi_data_graph_type.graph_type_id'), index=True, comment='关联分类id')
    data_graph_type = Column(ForeignKey('chatbi_dict_graph_type.graph_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='数据图表类型编号，1: 面积图, 2: 柱状图, 3: 折线图, 4: 扇形图, 5: 散点图, 6: 条形图')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    add_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='更新人')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    graph_url = Column(VARCHAR(255), comment='图表url')
    graph_description = Column(VARCHAR(255), comment='图表描述')
    analysis_prompt = Column(TEXT, comment='智能分析提示词')
    component_code = Column(VARCHAR(100), comment='前端组件')
    w = Column(VARCHAR(255), comment='条件')
    f = Column(VARCHAR(255))
    data_type = Column(TINYINT(1), server_default=text("'1'"), comment='图表类型 1：视图配置 2：兼容ai question')
    charts_show_type = Column(String(50, 'utf8mb4_general_ci'), server_default=text("'charts_bi'"), comment='chatbi展示样式 charts_bi：图表+智能分析 only_charts：仅图表 only_bi：仅智能分析')
    is_vector = Column(TINYINT(1), server_default=text("'0'"), comment='是否已经向量化 1：是 0：否')
    report_prompt = Column(VARCHAR(255), comment='报告提示词')
    graph_desc = Column(TEXT, comment='工具名称')

    add_user = relationship('TouchpointUser', primaryjoin='ChatbiDataGraph.add_user_id == TouchpointUser.tp_user_id')
    chatbi_dict_graph_type = relationship('ChatbiDictGraphType')
    data_view = relationship('ChatbiDataView')
    graph_type = relationship('ChatbiDataGraphType')
    update_user = relationship('TouchpointUser', primaryjoin='ChatbiDataGraph.update_user_id == TouchpointUser.tp_user_id')


class ChatbiDataViewColumn(Base):
    __tablename__ = 'chatbi_data_view_column'
    __table_args__ = {'comment': '视图自定义列表'}

    column_id = Column(BigInteger, primary_key=True, comment='自定义列id')
    data_view_id = Column(ForeignKey('chatbi_data_view.data_view_id'), index=True, comment='关联视图ID')
    column_name = Column(String(255, 'utf8mb4_general_ci'), comment='自定义列名称')
    formula_content = Column(String(1000, 'utf8mb4_general_ci'), comment='计算公式')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='0:未删除 1：已删除')

    data_view = relationship('ChatbiDataView')


class ClassClient(Base):
    __tablename__ = 'class_client'
    __table_args__ = {'comment': '班级学员表'}

    class_client_id = Column(BigInteger, primary_key=True, comment='班级学员ID')
    class_id = Column(ForeignKey('class_info.class_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='班级ID')
    client_id = Column(ForeignKey('client_info.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='学员ID')
    study_times = Column(Integer, server_default=text("'0'"), comment='已学时长')
    status = Column(TINYINT, server_default=text("'1'"), comment='学员状态 1：正常 0：停止')
    add_time = Column(DateTime, comment='入班时间')
    update_time = Column(DateTime, comment='更新时间')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='1:已删除 0：未删除')

    _class = relationship('ClassInfo')
    client = relationship('ClientInfo')
    tp_user = relationship('TouchpointUser')


class ClassCourse(Base):
    __tablename__ = 'class_course'
    __table_args__ = {'comment': '班级课程表'}

    class_course_id = Column(BigInteger, primary_key=True, comment='班级课程ID')
    class_id = Column(ForeignKey('class_info.class_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='班级ID')
    course_id = Column(ForeignKey('course.course_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='课程ID')
    status = Column(TINYINT, comment='课程状态 1：启用 0：关闭')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='0:未删除 1：已删除')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人')

    _class = relationship('ClassInfo')
    course = relationship('Course')
    tp_user = relationship('TouchpointUser')


class ClientInfoHistory(Base):
    __tablename__ = 'client_info_history'
    __table_args__ = {'comment': '客户信息历史表'}

    client_history_id = Column(VARCHAR(64), primary_key=True, comment='client_info历史信息')
    client_id = Column(ForeignKey('client_info.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True)

    client = relationship('ClientInfo')


class ClientInviteRecord(Base):
    __tablename__ = 'client_invite_record'
    __table_args__ = {'comment': '邀请记录表'}

    invite_id = Column(BigInteger, primary_key=True, comment='邀请记录ID')
    invite_client_id = Column(ForeignKey('client_info.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='邀请人ID')
    be_invite_client_id = Column(ForeignKey('client_info.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='被邀请人ID')
    status = Column(TINYINT, comment='1:待审核 2：审核通过 3：驳回')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='邀请时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='0：未删除 1：已删除')
    work_id = Column(Integer, comment='work平台ID')

    be_invite_client = relationship('ClientInfo', primaryjoin='ClientInviteRecord.be_invite_client_id == ClientInfo.client_id')
    invite_client = relationship('ClientInfo', primaryjoin='ClientInviteRecord.invite_client_id == ClientInfo.client_id')


class ClientPartner(Base):
    __tablename__ = 'client_partner'
    __table_args__ = {'comment': '合伙申请信息表'}

    partner_id = Column(BigInteger, primary_key=True, comment='合伙人ID')
    client_id = Column(ForeignKey('client_info.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='客户ID')
    activity_flag = Column(TINYINT, server_default=text("'2'"), comment='活动首页标志位，默认是2')
    client_name = Column(VARCHAR(50), comment='客户名称')
    phone_number = Column(VARCHAR(30), comment='手机号')
    wechat_number = Column(VARCHAR(50), comment='微信号')
    position = Column(VARCHAR(50), comment='职位')
    region = Column(VARCHAR(100), comment='地区')
    company_name = Column(VARCHAR(100), comment='公司名称')
    industry_id = Column(VARCHAR(36), comment='行业ID')
    client_type_id = Column(Integer, comment='合伙人类型ID')
    intention_ids = Column(VARCHAR(255), comment='合作意向 可多选1,2,3')
    channel = Column(VARCHAR(255), comment='推荐人/渠道：默认智邦管家')
    is_zb = Column(TINYINT, comment='是否购买智邦产品 1：是 0:否')
    is_erp = Column(TINYINT, comment='是否用过其他erp产品')
    erp_name = Column(VARCHAR(255), comment='Erp产品名称')
    link_time = Column(VARCHAR(100), comment='方便沟通时间 1：随时 2：上午 3：下午 4：晚上 5：周末 6：其他')
    status = Column(TINYINT, comment='状态：1：待审批 2：审批通过 3：驳回')
    invite_id = Column(BigInteger, index=True, comment='成功邀请人ID')
    share_qr_code = Column(VARCHAR(255), server_default=text("''"), comment='邀请二维码')
    remark = Column(VARCHAR(255), server_default=text("''"), comment='申请备注')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人')
    introduce_type = Column(VARCHAR(1), server_default=text("'1'"), comment='介绍者类型 1：合伙人邀请 2：员工邀请')
    invite_tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='员工邀请ID')
    sp_time = Column(DateTime, comment='审批时间')
    sp_no = Column(VARCHAR(32), comment='企微审批单号')
    integral = Column(Integer, nullable=False, server_default=text("'0'"), comment='智邦币总数')
    client_level = Column(Integer, nullable=False, server_default=text("'0'"), comment='合伙人类型')
    growth_value = Column(Integer, nullable=False, server_default=text("'0'"), comment='成长值')
    integral1 = Column(Integer, nullable=False, server_default=text("'0'"), comment='可提现智邦币')
    integral2 = Column(Integer, nullable=False, server_default=text("'0'"), comment='可转赠智邦币')
    growth_level = Column(VARCHAR(20), server_default=text("''"), comment='成长等级')
    work_company_id = Column(VARCHAR(10), comment='Work平台公司名称')
    work_company_name = Column(VARCHAR(255), comment='Work平台公司名称')
    partner_code = Column(VARCHAR(10), comment='合伙人编码')
    partner_type = Column(VARCHAR(1), comment='事业合伙人类型 A级 B级')
    cash_balance = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='现金余额')
    cash_history = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='现金提现历史')
    work_person_id = Column(VARCHAR(10), comment='Wok平台联系人ID')
    invite_client_id = Column(ForeignKey('client_info.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='合伙人邀请ID')
    source = Column(VARCHAR(10), server_default=text("'invite'"), comment='合伙人来源 invite: 二维码邀请 work：work平台申请 create:手动新增')

    client = relationship('ClientInfo', primaryjoin='ClientPartner.client_id == ClientInfo.client_id')
    invite_client = relationship('ClientInfo', primaryjoin='ClientPartner.invite_client_id == ClientInfo.client_id')
    invite_tp_user = relationship('TouchpointUser', primaryjoin='ClientPartner.invite_tp_user_id == TouchpointUser.tp_user_id')
    tp_user = relationship('TouchpointUser', primaryjoin='ClientPartner.tp_user_id == TouchpointUser.tp_user_id')


class ClientServiceRoleHistory(Base):
    __tablename__ = 'client_service_role_history'
    __table_args__ = {'comment': '客户关联服务角色历史记录表'}

    history_id = Column(BigInteger, primary_key=True, comment='历史记录ID')
    service_role_id = Column(ForeignKey('client_service_role.service_role_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='服务角色ID')
    client_id = Column(ForeignKey('client.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='客户ID')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='员工ID')
    role_name = Column(VARCHAR(50), nullable=False, comment='角色名称')
    phone_no = Column(VARCHAR(30), nullable=False, comment='手机号')
    is_substitution = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='是否申请换人 1：是 0：否')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0：我删除 1：已删除')
    bg_qr_code_url = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='添加企微弹出url')

    client = relationship('Client')
    service_role = relationship('ClientServiceRole')
    tp_user = relationship('TouchpointUser')


class ClientSpRecord(Base):
    __tablename__ = 'client_sp_record'
    __table_args__ = {'comment': '客户审核记录表'}

    record_id = Column(BigInteger, primary_key=True)
    client_id = Column(ForeignKey('client_info.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='客户ID')
    sp_status = Column(TINYINT(1), comment='审核状态')
    sp_result = Column(VARCHAR(255), comment='审核结果')
    remark = Column(VARCHAR(500), comment='审核备注')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人\\n')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='审核时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1:已删除 0：未删除')

    client = relationship('ClientInfo')
    tp_user = relationship('TouchpointUser')


class CourseOrder(Base):
    __tablename__ = 'course_orders'
    __table_args__ = {'comment': '课程订单表'}

    order_id = Column(BigInteger, primary_key=True, comment='订单ID')
    course_id = Column(ForeignKey('course.course_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='课程ID')
    client_id = Column(ForeignKey('client_info.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='学员ID')
    openid = Column(VARCHAR(64), comment='openid')
    amount = Column(DECIMAL(10, 2), comment='课程金额')
    prepay_id = Column(VARCHAR(255), comment='预支付id')
    trade_state = Column(VARCHAR(50), comment='交易状态，枚举值：SUCCESS：支付成功\\REFUND：转入退款 NOTPAY：未支付 CLOSED：已关闭 REVOKED：已撤销（付款码支付）USERPAYING：用户支付中（付款码支付）PAYERROR：支付失败(其他原因，如银行返回失败)')
    bank_type = Column(VARCHAR(100), comment='银行类型')
    pay_time = Column(DateTime, comment='支付时间')
    time_expire = Column(DateTime, comment='支付截止时间')
    is_callback = Column(TINYINT, server_default=text("'0'"), comment='0:未回调 1：已回调')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='0:未删除 1：已删除')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')

    client = relationship('ClientInfo')
    course = relationship('Course')


class CourseVideo(Base):
    __tablename__ = 'course_video'
    __table_args__ = {'comment': '课程视频表'}

    course_video_id = Column(BigInteger, primary_key=True, comment='课程视频ID')
    course_id = Column(ForeignKey('course.course_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='课程ID')
    video_name = Column(VARCHAR(255), comment='课程名称')
    video_url = Column(VARCHAR(1000), comment='课程视频url')
    sort = Column(Integer, comment='课程排序')
    size = Column(Integer, comment='大小')
    width = Column(Integer, comment='宽度')
    height = Column(Integer, comment='高度')
    format = Column(VARCHAR(30), comment='视频格式')
    signature = Column(CHAR(32), comment='视频或图片md5值')
    bit_rate = Column(Integer, comment='码率，单位bps\\n\\n')
    duration = Column(Float(6), comment='视频时长')
    status = Column(TINYINT, server_default=text("'1'"), comment='发布状态 1：发布 0：未发布')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='1:已删除 0：未删除')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人')
    is_free = Column(TINYINT, server_default=text("'0'"), comment='是否免费 1：是 0：否')

    course = relationship('Course')
    tp_user = relationship('TouchpointUser')


class DictClueManul(Base):
    __tablename__ = 'dict_clue_manul'

    clue_manul_id = Column(VARCHAR(64), primary_key=True)
    url_id = Column(VARCHAR(255), comment='对应落地页id')
    tab_name = Column(VARCHAR(255), comment='tab对应的名称')
    tab_sn = Column(Integer, comment='tab的序号')
    media_channel_id = Column(ForeignKey('media_channel.media_channel_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='媒体渠道id')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1:删除 0：未删除')
    status = Column(TINYINT(1), server_default=text("'1'"), comment='1:启用 0:停用')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人')
    ad_mission_id = Column(ForeignKey('ad_mission.ad_mission_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='任务ID')

    ad_mission = relationship('AdMission')
    media_channel = relationship('MediaChannel')
    tp_user = relationship('TouchpointUser')


class FQueue(Base):
    __tablename__ = 'f_queue'
    __table_args__ = {'comment': '训练-队列表'}

    queue_id = Column(Integer, primary_key=True)
    queue_type = Column(TINYINT, server_default=text("'1'"), comment='任务类型 1：模型训练 2模型评估')
    train_request_id = Column(ForeignKey('f_train_request.train_request_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='模型训练请求ID')
    evaluate_id = Column(ForeignKey('f_model_evaluate.evaluate_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='模型评估ID')
    location = Column(Integer, comment='队列位置')
    edit_tp_user_id = Column(VARCHAR(36), comment='调整队列人员')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新队列时间')
    is_edit = Column(TINYINT(1), server_default=text("'0'"), comment='是否调整过队列 1：已调整 0：未调整')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1:已删除 0:未删除')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间')

    evaluate = relationship('FModelEvaluate')
    train_request = relationship('FTrainRequest')


class FileScopeRule(Base):
    __tablename__ = 'file_scope_rule'
    __table_args__ = {'comment': '范围规则表'}

    scope_rule_id = Column(Integer, primary_key=True, comment='文件范围规则id')
    rule_key = Column(String(200), nullable=False, comment='规则关键字')
    rule_content = Column(String(255), nullable=False, comment='参数值')
    knowledge_id = Column(ForeignKey('aigc_knowledge.knowledge_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='知识库id')
    life_rule_id = Column(ForeignKey('file_life_rule.life_rule_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='生命周期规则id')
    target_field = Column(VARCHAR(255), server_default=text("'name'"), comment='规则比对字段')

    knowledge = relationship('AigcKnowledge')
    life_rule = relationship('FileLifeRule')


class Landpage(Base):
    __tablename__ = 'landpage'

    landpage_id = Column(VARCHAR(64), primary_key=True)
    admission_id = Column(ForeignKey('ad_mission.ad_mission_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='投放任务id')
    father_landpage_id = Column(VARCHAR(64), comment='模板landpageid')
    landpage_name = Column(VARCHAR(255), comment='落地页名称')
    landpage_title = Column(VARCHAR(255), comment='落地页标题')
    landpage_url = Column(VARCHAR(255), comment='落地页url')
    landpage_cover_url = Column(VARCHAR(255), comment='落地页封面url')
    add_time = Column(DateTime, comment='添加时间')
    add_tpu_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='创建者')
    landpage_content = Column(TEXT, comment='落地页内容')
    show_type = Column(VARCHAR(1), server_default=text("'0'"), comment='0:H5;1:web')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='删除标志位：0：未删除；1：删除；')
    remarks = Column(VARCHAR(255), comment='备注')

    add_tpu = relationship('TouchpointUser')
    admission = relationship('AdMission')


class LandpageDatum(Landpage):
    __tablename__ = 'landpage_data'

    landpage_id = Column(ForeignKey('landpage.landpage_id', ondelete='RESTRICT', onupdate='RESTRICT'), primary_key=True, comment='任务落地页id')
    show_num = Column(Integer, server_default=text("'0'"), comment='展示量')
    click_num = Column(Integer, server_default=text("'0'"), comment='点击数')
    open_num = Column(Integer, server_default=text("'0'"), comment='打开数')
    read_time = Column(Integer, server_default=text("'0'"), comment='阅读时长，毫秒')
    commit_num = Column(Integer, server_default=text("'0'"), comment='提交数')
    roi = Column(DECIMAL(5, 2), server_default=text("'0.00'"), comment='投入产出比')
    click_rate = Column(DECIMAL(5, 2), server_default=text("'0.00'"), comment='点击率')
    commit_rate = Column(DECIMAL(5, 2), server_default=text("'0.00'"), comment='提交率')
    ocpm = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='千次展示量费用')
    add_time = Column(DateTime, comment='添加时间')
    total_cost = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='花费')
    total_return = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='总回款')
    total_sign = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='总签单')


class OperateCustomView(Base):
    __tablename__ = 'operate_custom_view'
    __table_args__ = {'comment': '分析数据视图表'}

    view_id = Column(BigInteger, primary_key=True, comment='视图ID')
    view_name = Column(VARCHAR(255), server_default=text("''"), comment='视图名称')
    description = Column(TEXT, comment='视图描述')
    template_id = Column(ForeignKey('operate_custom_sql_template.template_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='SQL模板ID')
    custom_sql = Column(TEXT, comment='自定义SQL(不使用模板时的SQL)')
    fields_config = Column(JSON, comment='字段配置 {\\n        "fields": [{\\n            "name": "string",         -- 字段名称\\n            "type": "string",         -- 字段类型：string/number/date\\n            "displayName": "string",   -- 显示名称\\n            "description": "string"    -- 字段描述\\n        }]\\n    }')
    status = Column(TINYINT(1), server_default=text("'0'"), comment='状态 1:启用 0:停用')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    create_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人id')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='最后更新人ID')
    product_id = Column(ForeignKey('operate_dashboard.product_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='产品id')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='修改时间')

    create_user = relationship('TouchpointUser', primaryjoin='OperateCustomView.create_user_id == TouchpointUser.tp_user_id')
    product = relationship('OperateDashboard')
    template = relationship('OperateCustomSqlTemplate')
    tp_user = relationship('TouchpointUser', primaryjoin='OperateCustomView.tp_user_id == TouchpointUser.tp_user_id')


class RelClientExam(Base):
    __tablename__ = 'rel_client_exam'
    __table_args__ = {'comment': '考生答卷'}

    rel_exam_id = Column(Integer, primary_key=True, comment='答卷id')
    client_id = Column(ForeignKey('client_info.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='学员id')
    exam_id = Column(ForeignKey('exam_page.exam_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='试卷id')
    status = Column(Integer, server_default=text("'0'"), comment='答卷状态 0未开始；1答题中；2待阅卷；3已评分')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人')
    delete_flag = Column(Integer, server_default=text("'0'"), comment='1:已删除 0：未删除')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='修改时间')
    score = Column(Integer, server_default=text("'0'"), comment='总分')
    auto_check = Column(Integer, server_default=text("'0'"), comment='机器阅卷 0否；1是')

    client = relationship('ClientInfo')
    exam = relationship('ExamPage')
    tp_user = relationship('TouchpointUser')


class RelCourseTeacher(Base):
    __tablename__ = 'rel_course_teacher'
    __table_args__ = {'comment': '教师关联课程表'}

    rel_id = Column(BigInteger, primary_key=True, comment='关联ID')
    course_id = Column(ForeignKey('course.course_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='课程ID')
    teacher_id = Column(ForeignKey('course_teacher.course_teacher_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='教师ID')
    divide_ratio = Column(Float(5), server_default=text("'0.00'"), comment='分成比例')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='1:已删除 0：未删除')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人')

    course = relationship('Course')
    teacher = relationship('CourseTeacher')
    tp_user = relationship('TouchpointUser')


class RelDirectoryMaster(Base):
    __tablename__ = 'rel_directory_master'
    __table_args__ = {'comment': '目录配置管理员'}

    rel_id = Column(Integer, primary_key=True)
    dty_id = Column(ForeignKey('directory_level.id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='目录id')
    master_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='管理员id')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人id')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='上传时间')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='0:未删除；1已删除')

    dty = relationship('DirectoryLevel')
    master_user = relationship('TouchpointUser', primaryjoin='RelDirectoryMaster.master_user_id == TouchpointUser.tp_user_id')
    tp_user = relationship('TouchpointUser', primaryjoin='RelDirectoryMaster.tp_user_id == TouchpointUser.tp_user_id')


class RelExamQb(Base):
    __tablename__ = 'rel_exam_qb'
    __table_args__ = {'comment': '试卷关联试题'}

    rel_id = Column(Integer, primary_key=True, comment='关联id')
    exam_id = Column(ForeignKey('exam_page.exam_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='试卷id')
    qb_id = Column(ForeignKey('question_bank.question_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='试题id')
    sort = Column(Integer, server_default=text("'0'"), comment='顺序id')
    client_id = Column(ForeignKey('client_info.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='考生id')

    client = relationship('ClientInfo')
    exam = relationship('ExamPage')
    qb = relationship('QuestionBank')


class AdmissionCostDatum(Base):
    __tablename__ = 'admission_cost_data'
    __table_args__ = {'comment': '任务消耗表'}

    ad_cost_data_id = Column(VARCHAR(64), primary_key=True)
    landpage_id = Column(ForeignKey('landpage.landpage_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='landpage关联id')
    bgn_date = Column(DateTime, comment='统计开始时间')
    end_date = Column(DateTime, comment='统计结束时间')
    cost = Column(DECIMAL(12, 2), comment='广告花费，单位：元')
    show_num = Column(Integer, comment='展示量')
    click_num = Column(Integer, comment='点击量')
    commit_num = Column(Integer, comment='提交量')
    ocpm = Column(DECIMAL(10, 2), comment='千次展示成本；单位：元')
    add_time = Column(DateTime, comment='添加时间')
    adder = Column(VARCHAR(255), comment='添加者，将来是广告代理商id')
    convert_cost = Column(DECIMAL(12, 2), comment='转化成本')
    convert_rate = Column(Float(6), comment='转化率')
    media_account_id = Column(BigInteger, comment='授权媒体账户ID')
    avg_click_cost = Column(DECIMAL(10, 2), comment='平均点击单价')
    ctr = Column(Float(6), comment='点击率')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))

    landpage = relationship('Landpage')


class AdmissionDataTime(Base):
    __tablename__ = 'admission_data_time'
    __table_args__ = {'comment': '任务数据时间趋势表'}

    ad_data_time_id = Column(VARCHAR(64), primary_key=True)
    stat_date = Column(Date, comment='统计日期')
    admission_id = Column(ForeignKey('admission_data.admission_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='投放任务id')
    show_num = Column(Integer, server_default=text("'0'"), comment='展示量')
    click_num = Column(Integer, server_default=text("'0'"), comment='点击数')
    open_num = Column(Integer, server_default=text("'0'"), comment='打开数')
    read_time = Column(Integer, server_default=text("'0'"), comment='阅读时长，毫秒')
    commit_num = Column(Integer, server_default=text("'0'"), comment='提交数')
    roi = Column(DECIMAL(5, 2), server_default=text("'0.00'"), comment='投入产出比')
    click_rate = Column(DECIMAL(5, 2), server_default=text("'0.00'"), comment='点击率')
    commit_rate = Column(DECIMAL(5, 2), server_default=text("'0.00'"), comment='提交率')
    ocpm = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='千次展示量费用')
    add_time = Column(DateTime, comment='添加时间')

    admission = relationship('AdmissionDatum')


class ChatbiBoardDetail(Base):
    __tablename__ = 'chatbi_board_detail'
    __table_args__ = {'comment': 'Chatbi 看板关联图表表'}

    board_detail_id = Column(BigInteger, primary_key=True, comment='看板关联图表')
    board_id = Column(ForeignKey('chatbi_board.board_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='看板ID')
    data_graph_id = Column(ForeignKey('chatbi_data_graph.data_graph_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='图表ID')
    sort = Column(Integer, server_default=text("'9999'"), comment='排序')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0：未删除 1：已删除')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"))
    update_time = Column(DateTime)

    board = relationship('ChatbiBoard')
    data_graph = relationship('ChatbiDataGraph')


class ChatbiConversationPrologueTag(Base):
    __tablename__ = 'chatbi_conversation_prologue_tags'
    __table_args__ = {'comment': '开场白标签表'}

    rel_prologue_id = Column(String(255, 'utf8mb4_general_ci'), primary_key=True, comment='主键ID')
    prologue_id = Column(ForeignKey('chatbi_conversation_prologue.prologue_id'), nullable=False, index=True, comment='prologue_id')
    data_graph_id = Column(ForeignKey('chatbi_data_graph.data_graph_id'), index=True, comment='图表id')
    tags_id = Column(ForeignKey('tags.tags_id', ondelete='SET NULL', onupdate='CASCADE'), index=True, comment='标签ID')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(SmallInteger, nullable=False, server_default=text("'0'"), comment='删除标志位；0:未删除；1:已删除')
    tag_name = Column(String(255, 'utf8mb4_general_ci'), comment='标签名')
    sort = Column(SmallInteger, comment='排序')

    data_graph = relationship('ChatbiDataGraph')
    prologue = relationship('ChatbiConversationPrologue')
    tags = relationship('Tag')


class ChatbiDataGraphAsk(Base):
    __tablename__ = 'chatbi_data_graph_ask'
    __table_args__ = {'comment': '图表追问配置表'}

    graph_ask_id = Column(BigInteger, primary_key=True, comment='追问ID')
    data_graph_id = Column(ForeignKey('chatbi_data_graph.data_graph_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='图表ID')
    ask_name = Column(VARCHAR(255), comment='追问问题')
    ask_prompt = Column(TEXT, comment='追问提示词')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')

    data_graph = relationship('ChatbiDataGraph')


class ChatbiDataGraphSetting(Base):
    __tablename__ = 'chatbi_data_graph_setting'
    __table_args__ = {'comment': '图表设置表'}

    graph_setting_id = Column(BigInteger, primary_key=True, comment='图表设置ID')
    data_graph_id = Column(ForeignKey('chatbi_data_graph.data_graph_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='图表ID')
    setting_type_id = Column(ForeignKey('chatbi_dict_graph_setting.setting_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='设置类型ID')
    column_name = Column(VARCHAR(100), comment='列名')
    column_code = Column(VARCHAR(100), comment='列code')
    column_type = Column(VARCHAR(100), comment='列类型')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    empty_style = Column(VARCHAR(30), comment='空值样式')
    data_format_id = Column(ForeignKey('chatbi_dict_data_format.data_format_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='数据展示样式ID')
    agg_style_id = Column(ForeignKey('chatbi_dict_agg_style.agg_style_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='聚合方式ID')
    sort_type = Column(VARCHAR(30), comment='排序方式 asc 正序 desc 倒序 无排序')
    filter_type = Column(VARCHAR(30), comment='条件类型 or：或 and：与 single：单条件')
    drill_down_type_id = Column(ForeignKey('chatbi_dict_drill_down_type.drill_down_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='下钻类型ID')
    fmt_date = Column(VARCHAR(50), comment='下钻时间格式')
    drill_down_default = Column(String(255, 'utf8mb4_general_ci'), comment='下钻条件默认值')
    show_number = Column(String(10, 'utf8mb4_general_ci'), comment='展示条数')
    drill_down_prompt = Column(String(255, 'utf8mb4_general_ci'), comment='下钻校验提示词')

    agg_style = relationship('ChatbiDictAggStyle')
    data_format = relationship('ChatbiDictDataFormat')
    data_graph = relationship('ChatbiDataGraph')
    drill_down_type = relationship('ChatbiDictDrillDownType')
    setting_type = relationship('ChatbiDictGraphSetting')


class ChatbiIndex(Base):
    __tablename__ = 'chatbi_index'
    __table_args__ = {'comment': 'chatbi 指标表'}

    index_id = Column(BigInteger, primary_key=True, comment='指标ID')
    index_name = Column(VARCHAR(255), comment='指标名称')
    index_desc = Column(VARCHAR(500), comment='指标描述')
    data_graph_id = Column(ForeignKey('chatbi_data_graph.data_graph_id'), index=True, comment='图表ID')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='创建时间\\n')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    create_user_id = Column(ForeignKey('touchpoint_user.tp_user_id'), index=True, comment='创建人')
    update_user_id = Column(ForeignKey('touchpoint_user.tp_user_id'), index=True, comment='更新人\\n')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0：未删除 1：已删除')

    create_user = relationship('TouchpointUser', primaryjoin='ChatbiIndex.create_user_id == TouchpointUser.tp_user_id')
    data_graph = relationship('ChatbiDataGraph')
    update_user = relationship('TouchpointUser', primaryjoin='ChatbiIndex.update_user_id == TouchpointUser.tp_user_id')


class ChatbiRelGraphTag(Base):
    __tablename__ = 'chatbi_rel_graph_tags'
    __table_args__ = {'comment': 'Chatbi 图表关联标签'}

    rel_tag_id = Column(BigInteger, primary_key=True, comment='关联ID')
    data_graph_id = Column(ForeignKey('chatbi_data_graph.data_graph_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='图表ID')
    tags_id = Column(ForeignKey('tags.tags_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='标签ID')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除  1：已删除')

    data_graph = relationship('ChatbiDataGraph')
    tags = relationship('Tag')


class ClientPartnerContract(Base):
    __tablename__ = 'client_partner_contract'
    __table_args__ = {'comment': '合伙人合同表'}

    partner_contract_id = Column(BigInteger, primary_key=True, comment='合伙人合同ID')
    partner_id = Column(ForeignKey('client_partner.partner_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='合伙人ID')
    contract_number = Column(VARCHAR(255), comment='合同编号')
    contract_name = Column(VARCHAR(255), comment='合同名称')
    file_url = Column(VARCHAR(500), comment='最终合同存储url 一般是财务盖完章后的最终版本的合同')
    start_date = Column(VARCHAR(10), comment='合同开始日期')
    end_date = Column(VARCHAR(10), comment='合同结束日期')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='合同上传添加人')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='0:未删除 1：已删除')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    idcard_front = Column(VARCHAR(255), comment='身份证正面')
    idcard_back = Column(VARCHAR(255), comment='身份证反面')
    sign_url = Column(VARCHAR(255), comment='签名url')
    sign_status = Column(TINYINT, server_default=text("'1'"), comment='签名状态 1：未签名 2：已签名')
    tmp_contract_id = Column(ForeignKey('dict_partner_contract.tmp_contract_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='合同模版id')
    name = Column(VARCHAR(255), comment='身份证姓名')
    id = Column(VARCHAR(30), comment='身份证号')
    addr = Column(VARCHAR(400), comment='身份证地址')
    gender = Column(VARCHAR(10), comment='身份证性别')
    nationality = Column(VARCHAR(20), comment='身份证民族')
    valid_date = Column(VARCHAR(50), comment='身份证有效期')
    sign_file_url = Column(VARCHAR(255), comment='合伙人签完名后保存的url')
    file_img_list = Column(VARCHAR(1000), comment='合同pdf转图片列表\\n')
    sign_date = Column(VARCHAR(10), comment='签约日期')
    invite_tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='邀请人')

    invite_tp_user = relationship('TouchpointUser', primaryjoin='ClientPartnerContract.invite_tp_user_id == TouchpointUser.tp_user_id')
    partner = relationship('ClientPartner')
    tmp_contract = relationship('DictPartnerContract')
    tp_user = relationship('TouchpointUser', primaryjoin='ClientPartnerContract.tp_user_id == TouchpointUser.tp_user_id')


class MiniTask(Base):
    __tablename__ = 'mini_task'
    __table_args__ = {'comment': '活动任务表'}

    task_id = Column(BigInteger, primary_key=True, comment='任务ID')
    task_name = Column(VARCHAR(255), nullable=False, comment='任务名字')
    start_time = Column(DateTime, comment='任务开始时间')
    end_time = Column(DateTime, comment='任务结束时间')
    status = Column(VARCHAR(255), nullable=False, server_default=text("'0'"), comment='状态 0 默认；1 启动；2 停止； 3 终止')
    client_type = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='关联客户类型；多个 ‘,’分隔')
    content = Column(VARCHAR(255), server_default=text("''"), comment='任务内容')
    award = Column(Integer, nullable=False, server_default=text("'0'"), comment='奖励金额')
    souvenir = Column(VARCHAR(255), server_default=text("''"), comment="礼品奖励， ','多个礼品ID")
    priority = Column(Integer, server_default=text("'10'"), comment='优先级；数值越小，优先级越高')
    task_type = Column(ForeignKey('dict_task_type.type_code', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, server_default=text("'0'"), comment='类型；0 常规兑换；1 新客有礼；2合伙人')
    user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='创建人')
    get_type = Column(VARCHAR(10), server_default=text("'2'"), comment='任务下发方式 1被动领取； 2主动领取')
    landpage_id = Column(ForeignKey('landpage.landpage_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='关联落地页id')
    delete_flag = Column(VARCHAR(10), server_default=text("'0'"), comment='是否删除 0 否； 1删除')
    show_flag = Column(VARCHAR(11), server_default=text("'1'"), comment='是否主动弹出 0 否；1是')
    close_flag = Column(VARCHAR(11), server_default=text("'1'"), comment='是否可关闭 0 否；1是')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    modify_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='修改时间')
    index_task = Column(VARCHAR(10), server_default=text("'0'"), comment='是否首页任务')
    task_desc = Column(VARCHAR(255), server_default=text("''"), comment='任务描述')
    repeat = Column(Integer, server_default=text("'1'"), comment='可执行次数')
    ap_id = Column(ForeignKey('dict_login_setting.login_setting_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='登录频率id')
    type = Column(TINYINT, server_default=text("'2'"), comment='人员任务类型 1：客户任务 2：合伙人任务')

    ap = relationship('DictLoginSetting')
    landpage = relationship('Landpage')
    dict_task_type = relationship('DictTaskType')
    user = relationship('TouchpointUser')


class OperateCustomChart(Base):
    __tablename__ = 'operate_custom_chart'
    __table_args__ = {'comment': '分析图表表'}

    chart_id = Column(BigInteger, primary_key=True, comment='图表ID')
    chart_name = Column(VARCHAR(255), server_default=text("''"), comment='图表名称')
    description = Column(TEXT, comment='图表描述')
    view_id = Column(ForeignKey('operate_custom_view.view_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='关联的数据视图ID')
    type_id = Column(ForeignKey('operate_custom_chart_type.type_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='图表类型ID')
    dimensions_config = Column(JSON, comment='维度配置')
    metrics_config = Column(JSON, comment='指标配置')
    sort_config = Column(JSON, comment='排序配置 包含维度排序、指标排序、自定义排序')
    filter_config = Column(JSON, comment='筛选条件配置')
    time_config = Column(JSON, comment='时间配置 包含时间区间、时间粒度等')
    status = Column(TINYINT(1), server_default=text("'0'"), comment='状态 1:启用 0:停用')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    create_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人id')
    product_id = Column(ForeignKey('operate_dashboard.product_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='产品id')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='修改时间')

    create_user = relationship('TouchpointUser')
    product = relationship('OperateDashboard')
    type = relationship('OperateCustomChartType')
    view = relationship('OperateCustomView')


class RelClientResult(Base):
    __tablename__ = 'rel_client_result'
    __table_args__ = {'comment': '考生答案'}

    ret_id = Column(Integer, primary_key=True, comment='答题id')
    client_id = Column(ForeignKey('client_info.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='学员id')
    rel_exam_id = Column(ForeignKey('rel_client_exam.rel_exam_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='答卷id')
    qb_id = Column(ForeignKey('question_bank.question_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='考题id')
    ans_id = Column(ForeignKey('qb_answer.ans_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='答案id')
    content = Column(TEXT, comment='文本答案')
    score = Column(Float, server_default=text("'0'"), comment='分数')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')

    ans = relationship('QbAnswer')
    client = relationship('ClientInfo')
    qb = relationship('QuestionBank')
    rel_exam = relationship('RelClientExam')


class RelCourseOrderTeacher(Base):
    __tablename__ = 'rel_course_order_teacher'
    __table_args__ = {'comment': '课程订单关联教师分成记录表'}

    rel_id = Column(BigInteger, primary_key=True, comment='关联ID')
    order_id = Column(ForeignKey('course_orders.order_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='课程订单ID')
    teacher_id = Column(ForeignKey('course_teacher.course_teacher_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='老师ID')
    divide_ratio = Column(Float(5), server_default=text("'0.00'"), comment='分成比例')
    divide_amount = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='分成金额')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    status = Column(TINYINT(1), server_default=text("'0'"), comment='状态 1：已发放 0：未发送')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1:已删除 0：未删除')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人')

    order = relationship('CourseOrder')
    teacher = relationship('CourseTeacher')
    tp_user = relationship('TouchpointUser')


class RelLandpageCom(Base):
    __tablename__ = 'rel_landpage_com'

    rel_landpage_com_id = Column(VARCHAR(64), primary_key=True)
    landpage_id = Column(ForeignKey('landpage.landpage_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True)
    com_id = Column(ForeignKey('landpage_component.landpage_com_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True)

    com = relationship('LandpageComponent')
    landpage = relationship('Landpage')


class RelPartnerTag(Base):
    __tablename__ = 'rel_partner_tags'
    __table_args__ = {'comment': '合伙人关联标签表'}

    rel_partner_tag_id = Column(BigInteger, primary_key=True, comment='合伙人关联标签')
    partner_id = Column(ForeignKey('client_partner.partner_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='合伙人ID')
    tags_id = Column(ForeignKey('tags.tags_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='标签ID')
    update_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')

    partner = relationship('ClientPartner')
    tags = relationship('Tag')


class TogetherCreate(Base):
    __tablename__ = 'together_create'
    __table_args__ = {'comment': '合伙人共创表'}

    together_id = Column(BigInteger, primary_key=True, comment='共创ID')
    partner_id = Column(ForeignKey('client_partner.partner_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='合伙人ID')
    together_theme = Column(VARCHAR(255), comment='共创主题')
    together_content = Column(TEXT, comment='正文')
    status = Column(TINYINT, server_default=text("'1'"), comment='审核状态 1：待审核 2：审核通过 3：驳回')
    is_hot = Column(TINYINT, server_default=text("'0'"), comment='是否是热门共创 1：是 0：否')
    sort = Column(Integer, server_default=text("'999999'"), comment='排序')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='提交时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='更新时间')
    sp_time = Column(DateTime, comment='审批时间')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='审批人')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='1:已删除 0：未删除')
    level_name = Column(VARCHAR(10), comment='级别')
    zb_num = Column(Integer, comment='智邦b数量')

    partner = relationship('ClientPartner')
    tp_user = relationship('TouchpointUser')


class ChatbiRelGraphSettingFilter(Base):
    __tablename__ = 'chatbi_rel_graph_setting_filter'
    __table_args__ = {'comment': '图表设置过滤条件关联表'}

    rel_graph_setting_id = Column(BigInteger, primary_key=True, comment='过滤条件关联ID')
    graph_setting_id = Column(ForeignKey('chatbi_data_graph_setting.graph_setting_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='图标设置ID')
    filter_type_id = Column(ForeignKey('chatbi_dict_filter_type.filter_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='过滤条件类型ID')
    filter_value = Column(VARCHAR(255), comment='条件对应值')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')

    filter_type = relationship('ChatbiDictFilterType')
    graph_setting = relationship('ChatbiDataGraphSetting')


class ClientRadar(Base):
    __tablename__ = 'client_radar'
    __table_args__ = {'comment': '客户雷达配置存储'}

    client_radar_id = Column(VARCHAR(64), primary_key=True)
    radar_title = Column(VARCHAR(500), nullable=False, comment='客户雷达标题')
    radar_content = Column(TEXT, comment='客户雷达内容')
    add_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加用户id')
    add_time = Column(DateTime, comment='添加时间')
    modify_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='修改人id')
    modify_time = Column(DateTime)
    default_pic_url = Column(TEXT, comment='雷达默认图片地址')
    radar_client_id_list = Column(TEXT, comment='客户雷达应用的客户id列表')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='删除标志位；0:未删除；1:已删除')
    recovery_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='恢复者')
    recovery_time = Column(DateTime, comment='恢复时间')
    hidden_form = Column(ENUM('0', '1'), nullable=False, server_default=text("'0'"), comment='是否隐藏表单；0显示；1隐藏')
    qr_code = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='小程序二维码')
    radar_name = Column(VARCHAR(255), comment='雷达名称')
    publish_status = Column(ENUM('0', '1', '2', '3', '4', '5', '6'), nullable=False, server_default=text("'5'"), comment='0:已上架；1:审核中；2:审核拒绝；3:审核通过；4推送失败；5处理中；6已下架')
    scope = Column(ENUM('0', '1', '2'), nullable=False, server_default=text("'1'"), comment='可使范围 0:公司；1:个人；2合伙人')
    spread = Column(VARCHAR(10), nullable=False, server_default=text("'2'"), comment='传播渠道；1全部；2 裂变任务')
    share_code = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='小程序传播二维码')
    switch_channel = Column(VARCHAR(11), server_default=text("'0'"), comment='0 创建人渠道；1 配置渠道')
    channel_id = Column(ForeignKey('config_channel.channel_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='指定渠道id')
    sn = Column(Integer, server_default=text("'9999'"), comment='序号')
    publish_method = Column(VARCHAR(1), server_default=text("'1'"), comment='0:手动；1:立即发布(通过审核后);2:自定义时间')
    publish_time = Column(DateTime, comment='自定义发布时间')
    scheme_id = Column(ForeignKey('content_auth_scheme.auth_scheme_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='权限id')
    task_id = Column(ForeignKey('mini_task.task_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='合伙人任务ID')
    key_words = Column(VARCHAR(2000))

    add_user = relationship('TouchpointUser', primaryjoin='ClientRadar.add_user_id == TouchpointUser.tp_user_id')
    channel = relationship('ConfigChannel')
    modify_user = relationship('TouchpointUser', primaryjoin='ClientRadar.modify_user_id == TouchpointUser.tp_user_id')
    recovery_user = relationship('TouchpointUser', primaryjoin='ClientRadar.recovery_user_id == TouchpointUser.tp_user_id')
    scheme = relationship('ContentAuthScheme')
    task = relationship('MiniTask')


class LandpageDataTime(Base):
    __tablename__ = 'landpage_data_time'

    ad_land_data_id = Column(VARCHAR(64), primary_key=True)
    stat_date = Column(Date, comment='统计日期')
    landpage_id = Column(ForeignKey('landpage_data.landpage_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='任务落地页id')
    show_num = Column(Integer, server_default=text("'0'"), comment='展示量')
    click_num = Column(Integer, server_default=text("'0'"), comment='点击数')
    open_num = Column(Integer, server_default=text("'0'"), comment='打开数')
    read_time = Column(Integer, server_default=text("'0'"), comment='阅读时长，毫秒')
    commit_num = Column(Integer, server_default=text("'0'"), comment='提交数')
    roi = Column(DECIMAL(5, 2), server_default=text("'0.00'"), comment='投入产出比')
    click_rate = Column(DECIMAL(5, 2), server_default=text("'0.00'"), comment='点击率')
    commit_rate = Column(DECIMAL(5, 2), server_default=text("'0.00'"), comment='提交率')
    ocpm = Column(DECIMAL(10, 2), server_default=text("'0.00'"), comment='千次展示量费用')
    add_time = Column(DateTime, comment='添加时间')

    landpage = relationship('LandpageDatum')


class RelLandpageComValue(Base):
    __tablename__ = 'rel_landpage_com_value'

    rel_lcv_id = Column(VARCHAR(64), primary_key=True)
    landpage_com_id = Column(ForeignKey('rel_landpage_com.rel_landpage_com_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='落地页对应组件')
    com_type_property_id = Column(ForeignKey('rel_com_property.rel_ctp_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='组件属性')
    property_value = Column(VARCHAR(255), comment='属性值')

    com_type_property = relationship('RelComProperty')
    landpage_com = relationship('RelLandpageCom')


class TogetherApprovalRecord(Base):
    __tablename__ = 'together_approval_record'
    __table_args__ = {'comment': '共创审批记录表'}

    together_approval_id = Column(BigInteger, primary_key=True, comment='审批记录ID')
    together_id = Column(ForeignKey('together_create.together_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='共创ID')
    status = Column(TINYINT, comment='审批状态')
    reason = Column(VARCHAR(255), comment='原因')
    approval_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='审批时间')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='审批人')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='0：未删除 1：已删除')

    together = relationship('TogetherCreate')
    tp_user = relationship('TouchpointUser')


class TogetherCreateFile(Base):
    __tablename__ = 'together_create_file'
    __table_args__ = {'comment': '共创关联附件表'}

    together_file_id = Column(BigInteger, primary_key=True, comment='共创关联附件ID')
    together_id = Column(ForeignKey('together_create.together_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='共创ID')
    file_url = Column(VARCHAR(500), comment='附件路径')
    file_name = Column(VARCHAR(255), comment='附件名称')
    file_content = Column(VARCHAR(1000), comment='文件描述')
    file_type = Column(VARCHAR(255), comment='附件类型 image ：图片 file:文件')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='新增时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')

    together = relationship('TogetherCreate')


class FissionPlanning(Base):
    __tablename__ = 'fission_planning'
    __table_args__ = {'comment': '裂变策划计划表'}

    fission_planning_id = Column(VARCHAR(64), primary_key=True)
    fission_type_id = Column(ForeignKey('dict_fission_type.dict_fission_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='裂变类型id')
    fp_name = Column(VARCHAR(255), comment='裂变策划名称')
    cfg_monitor_id = Column(ForeignKey('config_monitor.monitor_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='监测配置id')
    cfg_bonus_tpu_id = Column(ForeignKey('config_bonus_tpu.bonus_tpu_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='触点用户激励id')
    cfg_bonus_client_id = Column(ForeignKey('config_bonus_client.bonus_client_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='客户激励id')
    template_fp_id = Column(ForeignKey('template_fp.template_fp_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='策划模板id')
    create_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='创建用户')
    create_date = Column(DateTime, comment='创建时间')
    subject = Column(VARCHAR(255), comment='策划主题')
    start_time = Column(DateTime, comment='策划开始时间')
    end_time = Column(DateTime, comment='策划结束时间')
    status = Column(VARCHAR(1), nullable=False, server_default=text("'0'"), comment='启动状态；0：未启动；1：进行中；2：已结束； 3：已停用')
    delete_flag = Column(VARCHAR(1), nullable=False, server_default=text("'0'"), comment='删除标志位；0：未删除；1：删除')
    content_type_id = Column(ForeignKey('dict_content_type.content_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='素材类型id')
    ad_title = Column(VARCHAR(255), comment='文案标题，限制50字')
    ad_content = Column(TEXT, comment='文案内容，限制200字')
    fp_org_flag = Column(VARCHAR(1), server_default=text("'1'"), comment='0:个人策划；1:组织策划；2:策划模板；')
    client_radar_id = Column(ForeignKey('client_radar.client_radar_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='客户雷达id')
    fp_mission_type = Column(VARCHAR(1), server_default=text("'1'"), comment='策划生成任务类型；0:直接任务；1:间接任务')
    use_radar_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='0:使用普通素材；1:使用雷达素材；')
    use_content_qr_pos = Column(VARCHAR(1), server_default=text("'1'"), comment='1:使用素材对应二维码位置；0:不使用素材对应二维码位置')
    all_touchpoint_flag = Column(VARCHAR(1), comment='1:使用全部触点；0:不使用全部触点')
    recovery_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='恢复人id')
    recovery_time = Column(DateTime, comment='恢复时间')
    modify_user_id = Column(VARCHAR(64), comment='修改者id')
    modify_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='修改时间')
    self_touchpoint_flag = Column(VARCHAR(1), nullable=False, server_default=text("'0'"), comment='是否开启个人渠道触点 0：未开启 1：已开启')

    cfg_bonus_client = relationship('ConfigBonusClient')
    cfg_bonus_tpu = relationship('ConfigBonusTpu')
    cfg_monitor = relationship('ConfigMonitor')
    client_radar = relationship('ClientRadar')
    content_type = relationship('DictContentType')
    create_user = relationship('TouchpointUser', primaryjoin='FissionPlanning.create_user_id == TouchpointUser.tp_user_id')
    fission_type = relationship('DictFissionType')
    recovery_user = relationship('TouchpointUser', primaryjoin='FissionPlanning.recovery_user_id == TouchpointUser.tp_user_id')
    template_fp = relationship('TemplateFp')


class FpDatum(FissionPlanning):
    __tablename__ = 'fp_data'
    __table_args__ = {'comment': '裂变策划计划数据表'}

    fp_id = Column(ForeignKey('fission_planning.fission_planning_id', ondelete='RESTRICT', onupdate='RESTRICT'), primary_key=True, comment='与fission_planning表是一对一关系')
    last_update_time = Column(DateTime, nullable=False, server_default=text("'0000-00-00 00:00:00'"))
    convert_client_num = Column(Integer, nullable=False, server_default=text("'0'"), comment='覆盖客户数')
    fission_point_num = Column(Integer, nullable=False, server_default=text("'0'"), comment='裂变触点数')
    content_num = Column(Integer, nullable=False, server_default=text("'0'"), comment='任务素材数量')
    fission_index = Column(Float(10), nullable=False, server_default=text("'0.00'"), comment='裂变指数：0-5000')
    interactivity_count = Column(Integer, nullable=False, server_default=text("'0'"), comment='互动量')
    like_count = Column(Integer, nullable=False, server_default=text("'0'"), comment='点赞量')
    retweets_count = Column(Integer, nullable=False, server_default=text("'0'"), comment='转发量')
    pageview_count = Column(Integer, nullable=False, server_default=text("'0'"), comment='阅读量')
    finish_read_count = Column(Integer, nullable=False, server_default=text("'0'"), comment='阅读结束人数')
    total_read_time = Column(Integer, nullable=False, server_default=text("'0'"), comment='总阅读时长')
    avg_read_time = Column(Integer, nullable=False, server_default=text("'0'"), comment='平均阅读时长')
    comment_count = Column(Integer, nullable=False, server_default=text("'0'"), comment='评论量')
    new_clue_num = Column(Integer, nullable=False, server_default=text("'0'"), comment='产生新线索量')
    active_clue_num = Column(Integer, nullable=False, server_default=text("'0'"), comment='激活线索量')
    primary_fission_num = Column(Float(50), nullable=False, server_default=text("'40.00'"), comment='一级裂变量')
    multiple_fission_num = Column(Float(50), nullable=False, server_default=text("'40.00'"), comment='多级裂变量')
    fission_comprehensive_score = Column(Integer, nullable=False, server_default=text("'0'"), comment='裂变综合评分')
    fission_communication_score = Column(Integer, nullable=False, server_default=text("'0'"), comment='裂变传播评分')
    fission_roi_score = Column(Integer, nullable=False, server_default=text("'0'"), comment='裂变roi评分')
    mission_num = Column(Integer, nullable=False, server_default=text("'0'"), comment='裂变任务数')
    tp_user_num = Column(Integer, nullable=False, server_default=text("'0'"), comment='裂变触点员工数')
    channel_num = Column(Integer, nullable=False, server_default=text("'0'"), comment='落地渠道数')


class RelRadarClientLevel(Base):
    __tablename__ = 'rel_radar_client_level'
    __table_args__ = {'comment': '雷达与客户等级关联关系'}

    rel_id = Column(Integer, primary_key=True, comment='关联id')
    radar_id = Column(ForeignKey('client_radar.client_radar_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='雷达ID')
    client_level_id = Column(ForeignKey('dict_client_level.level_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='客户等级ID')

    client_level = relationship('DictClientLevel')
    radar = relationship('ClientRadar')


class RelRadarTag(Base):
    __tablename__ = 'rel_radar_tags'
    __table_args__ = {'comment': '雷达标签'}

    radar_rel_tag_id = Column(VARCHAR(50), primary_key=True, comment='关联id')
    radar_id = Column(ForeignKey('client_radar.client_radar_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='雷达id')
    tag_id = Column(ForeignKey('tags.tags_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='标签id')

    radar = relationship('ClientRadar')
    tag = relationship('Tag')


class ConfigAbTest(Base):
    __tablename__ = 'config_ab_test'
    __table_args__ = {'comment': '渠道配置'}

    config_ab_test_id = Column(VARCHAR(64), primary_key=True, comment='abTestID')
    fp_id = Column(ForeignKey('fission_planning.fission_planning_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='策划计划id')
    create_time = Column(DateTime)
    ab_test_name = Column(VARCHAR(255), comment='abTest的名称')

    fp = relationship('FissionPlanning')


class FpEstimation(Base):
    __tablename__ = 'fp_estimation'
    __table_args__ = {'comment': '策划计划评估表'}

    fp_estimation_id = Column(VARCHAR(64), primary_key=True, comment='策划计划评估id')
    fp_id = Column(ForeignKey('fission_planning.fission_planning_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='策划计划id')
    convert_client_num = Column(Integer, server_default=text("'0'"), comment='覆盖客户数')
    convert_tpu_num = Column(Integer, comment='覆盖裂变员工触点；tpu：touchpoint_user')
    clue_num = Column(INTEGER, server_default=text("'0'"), comment='生成线索数')
    fission_index = Column(Float(8), server_default=text("'0.00'"), comment='裂变指数：0-1')

    fp = relationship('FissionPlanning')


class FpEstimationTime(Base):
    __tablename__ = 'fp_estimation_time'
    __table_args__ = {'comment': '策划计划评估时序数据（同步mongodb）'}

    fpe_time_id = Column(VARCHAR(64), primary_key=True, comment='策划计划评估时间数据')
    fp_id = Column(ForeignKey('fission_planning.fission_planning_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='策划评估id')
    fission_index = Column(Float(8), comment='裂变指数:0-5000')
    record_time = Column(Date, comment='评估时间')
    pageview_count = Column(Integer, comment='阅读量')
    finish_read_count = Column(Integer, comment='完成量')
    like_count = Column(Integer, comment='点赞量')
    retweets_count = Column(Integer, comment='转发量')
    comment_count = Column(Integer, comment='评论量')
    total_read_time = Column(Integer, comment='总阅读时间')
    new_clue_num = Column(Integer, comment='新线索量')

    fp = relationship('FissionPlanning')


class RelFissionTag(Base):
    __tablename__ = 'rel_fission_tags'
    __table_args__ = {'comment': '裂变策划计划与标签关联表'}

    rel_ft_id = Column(VARCHAR(64), primary_key=True, comment='关联裂变策划标签')
    fp_id = Column(ForeignKey('fission_planning.fission_planning_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='裂变策划id')
    fission_tag_id = Column(ForeignKey('tags.tags_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='标签id')
    rel_time = Column(DateTime, comment='关联时间')

    fission_tag = relationship('Tag')
    fp = relationship('FissionPlanning')


class RelFpChannel(Base):
    __tablename__ = 'rel_fp_channel'
    __table_args__ = {'comment': '策划计划渠道关联表'}

    rel_fp_channel_id = Column(VARCHAR(64), primary_key=True, comment='策划计划和渠道关联id')
    fp_id = Column(ForeignKey('fission_planning.fission_planning_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='策划计划id')
    channel_id = Column(ForeignKey('config_channel.channel_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='渠道id')

    channel = relationship('ConfigChannel')
    fp = relationship('FissionPlanning')


class RelFpContentGroup(Base):
    __tablename__ = 'rel_fp_content_group'
    __table_args__ = {'comment': '裂变策划计划与素材组关联表'}

    rel_fp_cg_id = Column(VARCHAR(64), primary_key=True)
    fp_id = Column(ForeignKey('fission_planning.fission_planning_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='裂变策划计划id')
    group_id = Column(ForeignKey('content_group.group_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='素材组id')
    rel_time = Column(DateTime, comment='关联添加时间')
    qr_pos_type_id = Column(ForeignKey('dict_qr_pos_type.qr_pos_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='0:左上；1:左下；2:右上；3:右下；4:自定义')
    qr_pos = Column(VARCHAR(20), comment='(x, y)；单位：px')
    qr_size = Column(VARCHAR(20), comment='(35, 35);width，height;单位：px')

    fp = relationship('FissionPlanning')
    group = relationship('ContentGroup')
    qr_pos_type = relationship('DictQrPosType')


class RelFpTag(Base):
    __tablename__ = 'rel_fp_tags'
    __table_args__ = {'comment': '策划计划标签关联表'}

    rel_fpt_id = Column(VARCHAR(64), primary_key=True, comment='策划计划标签关联表')
    fp_id = Column(ForeignKey('fission_planning.fission_planning_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='策划计划id')
    ct_id = Column(ForeignKey('tags.tags_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='标签id')
    update_time = Column(DateTime, comment='关联时间')
    valid_flag = Column(VARCHAR(1), comment='0：无效；1：有效；')
    tags_value = Column(VARCHAR(255), comment='标签值')

    ct = relationship('Tag')
    fp = relationship('FissionPlanning')


class RelFpTargeting(Base):
    __tablename__ = 'rel_fp_targeting'
    __table_args__ = {'comment': '裂变计划客户定向关联表'}

    rel_fpt_id = Column(VARCHAR(64), primary_key=True)
    targeting_type_id = Column(ForeignKey('dict_targeting_type.targeting_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='客户定向id')
    fp_id = Column(ForeignKey('fission_planning.fission_planning_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='策划id')
    targeting_value = Column(VARCHAR(255), comment='所选客户定向的值')

    fp = relationship('FissionPlanning')
    targeting_type = relationship('DictTargetingType')


class TouchMethod(Base):
    __tablename__ = 'touch_method'
    __table_args__ = {'comment': '触达方法'}

    touch_method_id = Column(VARCHAR(64), primary_key=True, comment='触达方法id')
    fp_id = Column(ForeignKey('fission_planning.fission_planning_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='策划计划id')
    tm_type_id = Column(ForeignKey('dict_touch_method_type.tm_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='触达方法类型id')

    fp = relationship('FissionPlanning')
    tm_type = relationship('DictTouchMethodType')


class ContentInfo(Base):
    __tablename__ = 'content_info'
    __table_args__ = {'comment': '素材内容表'}

    content_id = Column(VARCHAR(64), primary_key=True, comment='内容素材id')
    content_code = Column(VARCHAR(10), comment='素材编号字母数字组合，10字符')
    content_type_id = Column(ForeignKey('dict_content_type.content_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='内容素材类型id')
    ab_test_id = Column(ForeignKey('config_ab_test.config_ab_test_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='ab测试id')
    add_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='上传人id')
    aigc_content_id = Column(ForeignKey('aigc_content.aigc_content_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='aigc模型id')
    copywriting_pic_url = Column(VARCHAR(255), comment='文案内容markdown格式转图片的url地址')
    copywriting = Column(TEXT, comment='文案内容')
    pic_url = Column(VARCHAR(2000), comment='图片url')
    video_url = Column(VARCHAR(2000), comment='短视频url')
    docomnet_url = Column(VARCHAR(2000), comment='文档类oss链接地址')
    cover_pic_url = Column(VARCHAR(2000), comment='封面图片地址')
    link_url = Column(TEXT, comment='公众号或视频号的链接地址')
    content_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='素材名称')
    create_user_id = Column(VARCHAR(64), comment='创作者id')
    auth_scheme_id = Column(ForeignKey('content_auth_scheme.auth_scheme_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='权限方案id')
    thumbnail_url = Column(VARCHAR(2000), comment='缩略图url')
    create_time = Column(DateTime, comment='创作时间')
    comment = Column(VARCHAR(2000), comment='素材备注')
    info = Column(TEXT, comment='素材信息')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='0:未删除；1已删除')
    publish_status = Column(VARCHAR(1), comment='0:已上架；1:审核中；2:审核拒绝；3:审核通过')
    out_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='0:不对外公开；1:对外公开；')
    publish_department_id = Column(VARCHAR(64), comment='发布者部门id')
    publish_tpu_id = Column(VARCHAR(64), comment='发布者id')
    publish_writing = Column(VARCHAR(500), comment='发布文案')
    publish_method = Column(VARCHAR(1), comment='0:手动；1:立即发布(通过审核后);2:自定义时间')
    publish_time = Column(DateTime, comment='预设发布时间')
    qrcode_url = Column(VARCHAR(255), comment='二维码url地址')
    media_id = Column(TEXT, comment='企业微信media_id;临时素材id')
    media_upload_time = Column(DateTime, comment='临时素材上传时间，过期时间三天')
    media_pic_flag = Column(VARCHAR(100), comment='临时素材或素材组是否图片的标志位')
    moment_media_id = Column(TEXT, comment='朋友圈临时素材id')
    moment_media_upload_time = Column(DateTime, comment='朋友圈临时素材上传时间')
    content_source_id = Column(ForeignKey('dict_content_source.content_source_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='素材来源id')
    content_source_info = Column(VARCHAR(255), comment='其他素材来源')
    content_level = Column(VARCHAR(1), comment='素材等级：S、A、B、C')
    key_words = Column(TEXT, comment='素材关键词')
    content_use_id = Column(ForeignKey('dict_content_use.dict_content_use_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, server_default=text("'1'"), comment='素材使用范围')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='上传时间')
    modify_user_id = Column(VARCHAR(64), comment='修改者id')
    modify_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='修改时间')
    auto_shelf = Column(VARCHAR(1), server_default=text("'1'"), comment='审批后自动上架标志位；0:不自动上架；1:自动上架；')
    radar_oss = Column(TEXT, comment='存储h5，pdf等客户雷达转发素材的png对应oss对象地址')
    qr_pos = Column(VARCHAR(20), comment='二维码对应的位置；(x, y)')
    qr_size = Column(VARCHAR(20), comment='二维码对应的大小：(width, height)')
    recovery_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='回收入id')
    recovery_time = Column(DateTime, comment='回收时间')
    stat_exposure = Column(Integer, server_default=text("'0'"), comment='曝光量')
    stat_download = Column(Integer, server_default=text("'0'"), comment='下载量')
    stat_brower = Column(Integer, server_default=text("'0'"), comment='浏览量')
    stat_read = Column(Integer, server_default=text("'0'"), comment='客户阅读量；通过客户雷达来实现')
    knowledge_id = Column(ForeignKey('aigc_knowledge.knowledge_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='管理知识库ID')
    scope_id = Column(ForeignKey('dict_content_scope.scope_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='应用场景')
    visibility = Column(TINYINT, server_default=text("'0'"), comment='可见范围 0全员；1自定义')
    dty_id = Column(ForeignKey('directory_level.id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='目录id')
    meta_info = Column(VARCHAR(1000), comment='元数据')
    dlr = Column(TINYINT, server_default=text("'0'"), comment='下载范围 0全员；1自定义')
    pattern = Column(TINYINT, server_default=text("'2'"), comment='1简约；2复杂')
    author = Column(VARCHAR(100), comment='作者')
    remark = Column(VARCHAR(255), comment='备注信息')

    ab_test = relationship('ConfigAbTest')
    add_user = relationship('TouchpointUser', primaryjoin='ContentInfo.add_user_id == TouchpointUser.tp_user_id')
    aigc_content = relationship('AigcContent')
    auth_scheme = relationship('ContentAuthScheme')
    content_source = relationship('DictContentSource')
    content_type = relationship('DictContentType')
    content_use = relationship('DictContentUse')
    dty = relationship('DirectoryLevel')
    knowledge = relationship('AigcKnowledge')
    recovery_user = relationship('TouchpointUser', primaryjoin='ContentInfo.recovery_user_id == TouchpointUser.tp_user_id')
    scope = relationship('DictContentScope')


class ContentDatum(ContentInfo):
    __tablename__ = 'content_data'
    __table_args__ = {'comment': '素材数据表'}

    content_id = Column(ForeignKey('content_info.content_id', ondelete='RESTRICT', onupdate='RESTRICT'), primary_key=True, comment='内容素材数据id')
    spread_index = Column(Integer, comment='素材传播指数')
    spread_rank = Column(Integer, comment='素材传播排名')
    spread_rate = Column(VARCHAR(255), comment='素材传播增长率')
    basic_client_num = Column(Integer, server_default=text("'0'"), comment='基本统计-覆盖客户数')
    basic_clue_num = Column(Integer, server_default=text("'0'"), comment='基本统计-新线索数')
    basic_read_num = Column(Integer, server_default=text("'0'"), comment='基本统计-阅读量')
    basic_collect_num = Column(Integer, server_default=text("'0'"), comment='基本统计-点赞量')
    basic_trans_num = Column(Integer, server_default=text("'0'"), comment='基本统计-转发数')
    basic_interactive_num = Column(Integer, server_default=text("'0'"), comment='基本统计-交互数')
    last_update_time = Column(DateTime)


class ContentInfoExtend(ContentInfo):
    __tablename__ = 'content_info_extend'
    __table_args__ = {'comment': '内容扩展表'}

    content_id = Column(ForeignKey('content_info.content_id', ondelete='RESTRICT', onupdate='RESTRICT'), primary_key=True, comment='内容ID')
    app_id = Column(BigInteger, comment='智能投放产品ID')
    size = Column(Integer, comment='大小')
    width = Column(Integer, comment='宽度')
    height = Column(Integer, comment='高度')
    format = Column(VARCHAR(30), comment='视频格式')
    signature = Column(CHAR(32), comment='视频或图片md5值')
    bit_rate = Column(Integer, comment='码率，单位bps\\n\\n')
    duration = Column(Float(6), comment='视频时长')


class FissionMission(Base):
    __tablename__ = 'fission_mission'
    __table_args__ = {'comment': '裂变策划任务表\\r\\n跟触点发生关系'}

    fm_id = Column(VARCHAR(64), primary_key=True)
    fm_code = Column(VARCHAR(50), comment='裂变任务编号')
    fp_id = Column(ForeignKey('fission_planning.fission_planning_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='裂变策划id')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='触点人员id')
    ab_test_id = Column(ForeignKey('config_ab_test.config_ab_test_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='设置ab测试id')
    expert_bgn_time = Column(DateTime, nullable=False, comment='期望开始时间')
    expert_end_time = Column(DateTime, comment='期望结束时间')
    bgn_time = Column(DateTime, comment='实际开始时间')
    end_time = Column(DateTime, comment='实际结束时间')
    delete_flag = Column(VARCHAR(1), nullable=False, server_default=text("'0'"), comment='删除标志位：0：未删除；1：已删除；')
    status_flag = Column(VARCHAR(1), nullable=False, server_default=text("'0'"), comment='启动状态；0：未启动；1：进行中；2：已完成；3：已停用')
    channel_id = Column(ForeignKey('config_channel.channel_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='渠道id')
    edit_flag = Column(VARCHAR(1), server_default=text("'1'"), comment='模板是否可编辑标志位；0:不可编辑；1:可编辑')
    exec_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='任务是否执行；0：未执行；1：已执行')
    qr_code = Column(VARCHAR(255), comment='任务对应的二维码，添加了个人配置信息')
    qr_config_code = Column(VARCHAR(50), comment='添加至qr_code的config code信息')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')

    ab_test = relationship('ConfigAbTest')
    channel = relationship('ConfigChannel')
    fp = relationship('FissionPlanning')
    tp_user = relationship('TouchpointUser')


class FmDatum(FissionMission):
    __tablename__ = 'fm_data'
    __table_args__ = {'comment': '裂变任务数据表'}

    fm_id = Column(ForeignKey('fission_mission.fm_id', ondelete='RESTRICT', onupdate='RESTRICT'), primary_key=True, comment='与fission_mission表是一对一关系')
    last_update_time = Column(DateTime, nullable=False, server_default=text("'0000-00-00 00:00:00'"))
    convert_client_num = Column(Integer, nullable=False, server_default=text("'0'"), comment='覆盖客户数')
    interactivity_client_num = Column(Integer, nullable=False, server_default=text("'0'"), comment='互动客户数')
    fission_point_num = Column(Integer, nullable=False, server_default=text("'0'"), comment='裂变触点数')
    content_num = Column(Integer, nullable=False, server_default=text("'0'"), comment='任务素材数量')
    fission_index = Column(Float(10), nullable=False, server_default=text("'0.00'"), comment='裂变指数：0-5000')
    interactivity_count = Column(Integer, nullable=False, server_default=text("'0'"), comment='互动量')
    like_count = Column(Integer, nullable=False, server_default=text("'0'"), comment='点赞量')
    retweets_count = Column(Integer, nullable=False, server_default=text("'0'"), comment='转发量')
    pageview_count = Column(Integer, nullable=False, server_default=text("'0'"), comment='阅读量')
    finish_read_count = Column(Integer, nullable=False, server_default=text("'0'"), comment='阅读结束量')
    total_read_time = Column(Integer, nullable=False, server_default=text("'0'"), comment='总阅读时间(毫秒)')
    avg_read_time = Column(Integer, nullable=False, server_default=text("'0'"), comment='平均阅读时间(毫秒)')
    comment_count = Column(Integer, nullable=False, server_default=text("'0'"), comment='评论量')
    new_clue_num = Column(Integer, nullable=False, server_default=text("'0'"), comment='产生新线索量')
    active_clue_num = Column(Integer, nullable=False, server_default=text("'0'"), comment='激活线索量')
    primary_fission_num = Column(Float(50), nullable=False, server_default=text("'100.00'"), comment='一级裂变率')
    multiple_fission_num = Column(Float(50), nullable=False, server_default=text("'100.00'"), comment='多级裂变率')
    fission_comprehensive_score = Column(Integer, nullable=False, server_default=text("'0'"), comment='裂变综合评分')
    fission_communication_score = Column(Integer, nullable=False, server_default=text("'0'"), comment='裂变传播评分')
    fission_roi_score = Column(Integer, nullable=False, server_default=text("'0'"))
    active_client_ids = Column(TEXT, comment='激活客户ids')


class MissionMonitor(FissionMission):
    __tablename__ = 'mission_monitor'
    __table_args__ = {'comment': '策划任务监控'}

    fm_id = Column(ForeignKey('fission_mission.fm_id', ondelete='RESTRICT', onupdate='RESTRICT'), primary_key=True, comment='裂变任务id')
    monitor_url = Column(VARCHAR(255), comment='监控链接url')


class AigcKnowledgeDocument(Base):
    __tablename__ = 'aigc_knowledge_document'
    __table_args__ = {'comment': '知识库文档表\\n— 增加字段 doc_clear_url 文档清洗后地址'}

    document_id = Column(BigInteger, primary_key=True, comment='文档ID')
    doc_name = Column(VARCHAR(255), comment='文档名称')
    doc_type = Column(VARCHAR(255), comment='文档类型')
    chunk_size = Column(Integer, comment='切片数')
    knowledge_id = Column(ForeignKey('aigc_knowledge.knowledge_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='知识库ID')
    status = Column(VARCHAR(30), comment='TODO,RUNNING：未切片,FAILED：切片失败,FINISHED：切片完成')
    state = Column(Integer, comment='0:文档清洗成功  1:文档清洗失败 2:切片成功 3:切片失败 4:切片清洗成功 5:切片清洗失败 6:向量成功 7:向量失败')
    doc_url = Column(VARCHAR(1000), comment='文档地址')
    result = Column(VARCHAR(255), comment='TODO：切片完成，RUNNING：准备向量化，FINISHED：向量成功，FAILED：向量失败')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='0:未删除 1：已删除')
    doc_size = Column(Integer, comment='大小')
    content_id = Column(ForeignKey('content_info.content_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='关联素材ID')
    abstract = Column(TEXT, comment='摘要')
    description = Column(VARCHAR(2000), comment='文档描述')
    graph_content = Column(TEXT, comment='图谱内容')
    hit_count = Column(Integer, server_default=text("'0'"), comment='命中数')
    character_count = Column(Integer, server_default=text("'0'"), comment='字符数')
    content_time = Column(DateTime, comment='生产时间')
    tokens = Column(Integer, comment='tokens')
    open_life = Column(TINYINT, server_default=text("'0'"), comment='生命周期 0否；1是')
    life_type = Column(VARCHAR(30), comment='计算方式 date；days')
    expire_date = Column(DateTime, comment='过期时间')
    doc_clear_url = Column(VARCHAR(1000), comment='24.12.19添加 文档清洗后地址')

    content = relationship('ContentInfo')
    knowledge = relationship('AigcKnowledge')


class ApproveRecord(Base):
    __tablename__ = 'approve_record'
    __table_args__ = {'comment': '审批记录'}

    approved_id = Column(VARCHAR(64), primary_key=True)
    approved_time = Column(DateTime, comment='审批提交时间')
    approve_info = Column(VARCHAR(2000), server_default=text("'NULL'"), comment='json格式，包含审批信息')
    approve_template_id = Column(VARCHAR(255), comment='审批模板id')
    content_id = Column(ForeignKey('content_info.content_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='素材id')
    sp_no = Column(VARCHAR(32), comment='审批编号')
    ect_id = Column(ForeignKey('electric_card_template.ect_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='名片编号')
    cr_id = Column(ForeignKey('client_radar.client_radar_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='裂变雷达编号')
    pt_id = Column(ForeignKey('client_partner.partner_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='合伙人编号')
    type_key = Column(ENUM('content', 'card', 'radar', 'partner'), server_default=text("'content'"), comment='数据类型  content 素材；card 名片；radar 客户雷达')

    content = relationship('ContentInfo')
    cr = relationship('ClientRadar')
    ect = relationship('ElectricCardTemplate')
    pt = relationship('ClientPartner')


class Clue(Base):
    __tablename__ = 'clue'
    __table_args__ = {'comment': '线索表'}

    clue_id = Column(VARCHAR(64), primary_key=True, comment='线索id')
    client_id = Column(ForeignKey('client_info.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='客户id')
    last_estimate_date = Column(DateTime, comment='最新线索评估日期')
    latest_maturity_value = Column(Integer, server_default=text("'0'"), comment='线索成熟度值(0-100)')
    latest_clue_maturity_id = Column(ForeignKey('dict_clue_maturity.clue_maturity_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='线索成熟id')
    clue_source_id = Column(ForeignKey('dict_clue_source.clue_source_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='线索来源id')
    clue_stage_id = Column(ForeignKey('dict_clue_stage.clue_stage_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='线索阶段id')
    channel_source_id = Column(ForeignKey('config_channel.channel_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='来源渠道号id')
    fm_id = Column(ForeignKey('fission_mission.fm_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='裂变来源任务id')
    channel_fp_id = Column(ForeignKey('fission_planning.fission_planning_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='来源策划计划id')
    channel_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='通过渠道添加时间')
    landpage_id = Column(ForeignKey('landpage.landpage_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='如果公域线索，通过那个landpage提交表单')
    sign_money = Column(DECIMAL(10, 2), comment='签单：元')
    sign_date = Column(DateTime, comment='签单时间')
    return_money = Column(DECIMAL(10, 2), comment='回款金额：元')
    return_date = Column(DateTime, comment='回款时间')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='删除标志位')
    company_address = Column(VARCHAR(255), comment='工商注册地址')
    industry = Column(VARCHAR(50), comment='所属行业')
    partner_id = Column(ForeignKey('client_partner.partner_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='合伙人ID')
    is_settle_in = Column(VARCHAR(1), comment='是否入住园区1：是 0：否')
    settle_name = Column(VARCHAR(100), comment='入驻园区名称')
    remark = Column(VARCHAR(255), server_default=text("''"), comment='备注')
    approve_status = Column(VARCHAR(10), server_default=text("'0'"), comment='审批状态 1 默认；2通过；3驳回；0检测中')
    approve_time = Column(DateTime, comment='审批时间')
    approve_reason = Column(VARCHAR(255), comment='审批原因')
    sp_amount = Column(Integer, server_default=text("'0'"), comment='标准产品金额')
    nsp_amount = Column(Integer, server_default=text("'0'"), comment='非标准产品金额')
    task_id = Column(ForeignKey('mini_task.task_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='客户任务ID')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人ID')
    ip = Column(VARCHAR(255), server_default=text("''"), comment='请求ip')
    valid_flag = Column(VARCHAR(1), server_default=text("'2'"), comment='0：无效；1：有效；2：待判定')
    tip_type = Column(VARCHAR(1), comment='提点类型')
    work_result = Column(VARCHAR(1000), comment='处理结果')
    term_protection = Column(Integer, server_default=text("'180'"), comment='保护时长 / 天')
    supporter = Column(VARCHAR(10), server_default=text("'0'"), comment='特别支持 0否；1是')
    company_name = Column(VARCHAR(200), server_default=text("''"), comment='公司名字')
    media_account_id = Column(BigInteger, comment='媒体授权账户ID')
    mg_clue_id = Column(VARCHAR(20), comment='Mg库对应的线索ID')
    clue_name = Column(VARCHAR(100), server_default=text("''"), comment='线索名称')
    phone = Column(VARCHAR(20), server_default=text("''"), comment='手机号')
    is_verified = Column(VARCHAR(10), server_default=text("'0'"), comment='是否待核验 0否；1 是')

    channel_fp = relationship('FissionPlanning')
    channel_source = relationship('ConfigChannel')
    client = relationship('ClientInfo')
    clue_source = relationship('DictClueSource')
    clue_stage = relationship('DictClueStage')
    fm = relationship('FissionMission')
    landpage = relationship('Landpage')
    latest_clue_maturity = relationship('DictClueMaturity')
    partner = relationship('ClientPartner')
    task = relationship('MiniTask')
    tp_user = relationship('TouchpointUser')


class ContentDowloadHistory(Base):
    __tablename__ = 'content_dowload_history'
    __table_args__ = {'comment': '素材下载历史表'}

    dowload_history_id = Column(VARCHAR(64), primary_key=True)
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='触点用户id')
    content_id = Column(ForeignKey('content_info.content_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='素材id')
    dowload_time = Column(DateTime, comment='素材下载时间')
    down_date = Column(Date, comment='素材操作时间，精确到天')
    success_flag = Column(VARCHAR(1), server_default=text("'1'"), comment='下载成功标志位：1：成功；0：失败')
    operate_type = Column(VARCHAR(255), comment='操作类型;1:浏览；2:下载；3:曝光')
    client_ww_id = Column(VARCHAR(64), comment='客户企业微信id')

    content = relationship('ContentInfo')
    tp_user = relationship('TouchpointUser')


class ContentInfoVersion(Base):
    __tablename__ = 'content_info_version'

    id = Column(Integer, primary_key=True)
    content_id = Column(ForeignKey('content_info.content_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='素材id')
    content_index = Column(Integer, comment='文件位置')
    content_type_id = Column(ForeignKey('dict_content_type.content_type_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='文件类型')
    url_version = Column(VARCHAR(50), comment='版本号')
    url = Column(VARCHAR(2000), comment='文件链接')
    url_file_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='文件名称')
    delete_flag = Column(VARCHAR(1), server_default=text("'0'"), comment='是否删除')
    add_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='上传时间')
    pid = Column(ForeignKey('content_info_version.id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='本体id')

    add_user = relationship('TouchpointUser')
    content = relationship('ContentInfo')
    content_type = relationship('DictContentType')
    parent = relationship('ContentInfoVersion', remote_side=[id])


class FmClient(Base):
    __tablename__ = 'fm_client'
    __table_args__ = {'comment': '裂变任务客户表'}

    fm_client_id = Column(VARCHAR(64), primary_key=True)
    fm_id = Column(ForeignKey('fission_mission.fm_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='裂变任务id')
    client_id = Column(ForeignKey('client.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='客户id')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='任务生成时间')

    client = relationship('Client')
    fm = relationship('FissionMission')


class FmEstimation(Base):
    __tablename__ = 'fm_estimation'
    __table_args__ = {'comment': '裂变任务评估'}

    fm_estimation_id = Column(VARCHAR(64), primary_key=True, comment='裂变任务的评估id')
    fm_id = Column(ForeignKey('fission_mission.fm_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='裂变任务id')
    fission_index = Column(DECIMAL(8, 2), comment='裂变指数')

    fm = relationship('FissionMission')


class FmEstimationTime(Base):
    __tablename__ = 'fm_estimation_time'

    fme_time_id = Column(VARCHAR(64), primary_key=True)
    fm_id = Column(ForeignKey('fission_mission.fm_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='fm_id')
    pageview_count = Column(Integer, comment='客户查看数')
    finish_read_count = Column(Integer, comment='结束阅读数')
    like_count = Column(Integer, comment='点赞数')
    retweets_count = Column(Integer, comment='转发数')
    comment_count = Column(Integer, comment='评论数')
    total_read_time = Column(Integer, comment='总阅读时间：毫秒')
    new_clue_num = Column(Integer, comment='新客户数')
    fission_index = Column(DECIMAL(8, 2), comment='裂变指数')
    stat_date = Column(Date, comment='统计时间')

    fm = relationship('FissionMission')


class RelContentGroup(Base):
    __tablename__ = 'rel_content_group'
    __table_args__ = {'comment': '素材组和素材关联表'}

    rel_content_group_id = Column(VARCHAR(64), primary_key=True)
    group_id = Column(ForeignKey('content_group.group_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='素材组id')
    content_id = Column(ForeignKey('content_info.content_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='素材id')
    rel_time = Column(DateTime)

    content = relationship('ContentInfo')
    group = relationship('ContentGroup')


class RelContentKnowledge(Base):
    __tablename__ = 'rel_content_knowledge'
    __table_args__ = {'comment': '素材管理知识库表'}

    rel_id = Column(BigInteger, primary_key=True)
    content_id = Column(ForeignKey('content_info.content_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='素材ID')
    knowledge_id = Column(ForeignKey('aigc_knowledge.knowledge_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='知识库ID')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人ID')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1：已删除 0：未删除')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')

    content = relationship('ContentInfo')
    knowledge = relationship('AigcKnowledge')
    tp_user = relationship('TouchpointUser')


class RelContentPo(Base):
    __tablename__ = 'rel_content_pos'
    __table_args__ = {'comment': '素材位置关联表'}

    rel_content_pos_id = Column(VARCHAR(64), primary_key=True)
    content_id = Column(ForeignKey('content_info.content_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='素材id')
    publish_pos_id = Column(ForeignKey('publish_pos.publish_pos_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='webhooks发布位置id')

    content = relationship('ContentInfo')
    publish_pos = relationship('PublishPo')


class RelContentTag(Base):
    __tablename__ = 'rel_content_tags'
    __table_args__ = {'comment': '内容标签关联表'}

    rel_content_tags_id = Column(VARCHAR(64), primary_key=True, comment='素材标签关联id')
    content_id = Column(ForeignKey('content_info.content_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='素材id')
    tag_id = Column(ForeignKey('tags.tags_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='素材标签id')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='添加人id')

    content = relationship('ContentInfo')
    tag = relationship('Tag')
    tp_user = relationship('TouchpointUser')


class RelContentTpuCollect(Base):
    __tablename__ = 'rel_content_tpu_collect'
    __table_args__ = {'comment': '素材信息用户收藏关联表'}

    rel_content_tpu_collect_id = Column(VARCHAR(64), primary_key=True)
    content_id = Column(ForeignKey('content_info.content_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='素材id')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='用户id')
    collect_flag = Column(VARCHAR(1), comment='收藏标志位')
    create_time = Column(DateTime, comment='创建时间')
    collect_time = Column(DateTime, comment='收藏时间')

    content = relationship('ContentInfo')
    tp_user = relationship('TouchpointUser')


class RelDataPower(Base):
    __tablename__ = 'rel_data_power'
    __table_args__ = {'comment': '权限关联表'}

    rel_id = Column(Integer, primary_key=True, comment='关联id')
    directory_id = Column(ForeignKey('directory_level.id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='目录id')
    content_id = Column(ForeignKey('content_info.content_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='素材id')
    scheme_id = Column(ForeignKey('content_auth_scheme.auth_scheme_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='权限id')
    chose_type = Column(TINYINT, nullable=False, comment='1 正选；2反选')
    action_type = Column(TINYINT, nullable=False, comment='行为 1展示；2下载')

    content = relationship('ContentInfo')
    directory = relationship('DirectoryLevel')
    scheme = relationship('ContentAuthScheme')


class RelFmContent(Base):
    __tablename__ = 'rel_fm_content'
    __table_args__ = {'comment': '内容素材-策划任务关联表'}

    rel_fm_content_id = Column(VARCHAR(64), primary_key=True, comment='关联内容和任务id')
    fm_id = Column(ForeignKey('fission_mission.fm_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='策划任务id')
    content_id = Column(ForeignKey('content_info.content_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='内容素材id')

    content = relationship('ContentInfo')
    fm = relationship('FissionMission')


class RelQppContent(Base):
    __tablename__ = 'rel_qpp_content'
    __table_args__ = {'comment': '痛点及方案关联推荐素材表'}

    rel_recommend_id = Column(BigInteger, primary_key=True, comment='关联素材推荐ID')
    qpp_id = Column(ForeignKey('question_point_plan.qpp_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='痛点及方案ID')
    content_id = Column(ForeignKey('content_info.content_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='素材ID')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='1：已删除 0：未删除')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')

    content = relationship('ContentInfo')
    qpp = relationship('QuestionPointPlan')


class RelUnitContent(Base):
    __tablename__ = 'rel_unit_content'
    __table_args__ = {'comment': '素材分配单位'}

    rel_id = Column(Integer, primary_key=True)
    content_id = Column(ForeignKey('content_info.content_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='素材id')
    unit_id = Column(ForeignKey('affiliated_unit.unit_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='单位id')

    content = relationship('ContentInfo')
    unit = relationship('AffiliatedUnit')


class WeworkAddMsgTemplate(Base):
    __tablename__ = 'wework_add_msg_template'

    add_msg_template_id = Column(VARCHAR(64), primary_key=True)
    fm_id = Column(ForeignKey('fission_mission.fm_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='裂变任务id')
    external_client_id = Column(TEXT, comment='外部客户id')
    allow_select = Column(VARCHAR(1), server_default=text("'0'"), comment='是否允许成员在待发送客户列表中重新进行选择')
    text_content = Column(VARCHAR(4000), comment='消息文本内容')
    attachment_json = Column(VARCHAR(2000), comment='附件json数据')
    attchment_num = Column(Integer, comment='附件数量')
    image_num = Column(Integer, server_default=text("'0'"), comment='附件中图片数量')
    link_num = Column(Integer, comment='附件中链接数量')
    mimi_num = Column(Integer, comment='附件中小程序数量')
    video_num = Column(Integer, comment='附件中视频数量')
    file_num = Column(Integer, comment='附件中文件数量')

    fm = relationship('FissionMission')


class AigcKnowledgeDocumentChunk(Base):
    __tablename__ = 'aigc_knowledge_document_chunk'
    __table_args__ = {'comment': '知识库文档切片表'}

    chunk_id = Column(BigInteger, primary_key=True, comment='切片ID')
    doc_id = Column(ForeignKey('aigc_knowledge_document.document_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='文档ID')
    content = Column(TEXT, comment='内容')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT(1), server_default=text("'0'"), comment='1:已删除 0：未删除')
    character_count = Column(Integer, server_default=text("'0'"), comment='字符数')
    hit_count = Column(Integer, server_default=text("'0'"), comment='命中数')
    tokens = Column(Integer, server_default=text("'0'"), comment='Tokens')
    status = Column(TINYINT(1), server_default=text("'1'"), comment='1：启用 0：停用')
    result = Column(VARCHAR(255), comment='向量化结果 TODO：切片完成,RUNNING：准备向量化,FAILED：向量失败,FINISHED：向量完成')

    doc = relationship('AigcKnowledgeDocument')


class ClientIntegralExchange(Base):
    __tablename__ = 'client_integral_exchange'
    __table_args__ = {'comment': '积分兑换表'}

    exchange_id = Column(BigInteger, primary_key=True, comment='积分兑换ID')
    client_id = Column(ForeignKey('client.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='客户ID')
    total_integral = Column(Integer, comment='使用总积分数')
    receive_name = Column(VARCHAR(30), server_default=text("''"), comment='收件人姓名')
    receive_address = Column(VARCHAR(500), server_default=text("''"), comment='收货地址')
    receive_phone = Column(VARCHAR(15), server_default=text("''"), comment='联系电话')
    status = Column(TINYINT, nullable=False, server_default=text("'1'"), comment='订单状态 订单状态0：已取消 1：待发货 2：已发货 3已确认 4：已完成')
    remarks = Column(VARCHAR(255), server_default=text("''"), comment='备注')
    express_name = Column(VARCHAR(100), server_default=text("''"), comment='快递名称')
    express_num = Column(VARCHAR(255), server_default=text("''"), comment='快递单号')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='下单时时间')
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='更新时间')
    delete_flag = Column(TINYINT, server_default=text("'0'"), comment='是否删除 0：未删除 1：已删除')
    work_id = Column(Integer, nullable=False, server_default=text("'0'"), comment='对应work平台ID')
    exchange_type = Column(TINYINT, nullable=False, server_default=text("'1'"), comment='兑换类型 1：礼品兑换 2：智邦币提现 3：现金账户提现')
    type_code = Column(Integer, nullable=False, server_default=text("'1'"), comment='具体现金类型 1自荐客户；2合伙人抽成；3现金提现')
    clue_id = Column(ForeignKey('clue.clue_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='自荐客户线索id')
    partner_id = Column(ForeignKey('client_partner.partner_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='裂变合伙人id')

    client = relationship('Client')
    clue = relationship('Clue')
    partner = relationship('ClientPartner')


class ClueEstimation(Base):
    __tablename__ = 'clue_estimation'
    __table_args__ = {'comment': '线索评估表'}

    clue_estimation_id = Column(VARCHAR(64), primary_key=True, comment='线索评估id')
    estimation_date = Column(DateTime)
    clue_id = Column(ForeignKey('clue.clue_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, server_default=text("''"), comment='线索id')
    clue_maturity_id = Column(ForeignKey('dict_clue_maturity.clue_maturity_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='评估时线索成熟度')
    clue_stage_id = Column(VARCHAR(64), nullable=False, server_default=text("''"), comment='线索阶段id')
    work_wechart_flag = Column(VARCHAR(1), nullable=False, server_default=text("'0'"), comment='是否添加企业微信')
    approval_score = Column(Integer, nullable=False, server_default=text("'0'"), comment='线索认可值0-100')
    interaction_rate = Column(Float(5), nullable=False, server_default=text("'0.00'"), comment='互动率')
    fission_count = Column(Integer, nullable=False, server_default=text("'0'"), comment='涉及策划数')
    interaction_fission_count = Column(Integer, nullable=False, server_default=text("'0'"), comment='互动策划数')
    interaction_num = Column(Integer, nullable=False, server_default=text("'0'"), comment='互动数')
    long_interaction_num = Column(Integer, nullable=False, server_default=text("'0'"), comment='长互动数')
    good_interaction_rate = Column(Float(5), nullable=False, server_default=text("'0.00'"), comment='互动良率')

    clue = relationship('Clue')
    clue_maturity = relationship('DictClueMaturity')


class ClueEstimationTime(Base):
    __tablename__ = 'clue_estimation_time'
    __table_args__ = {'comment': '线索评估时间趋势表'}

    clue_estimation_time_id = Column(VARCHAR(64), primary_key=True)
    clue_id = Column(ForeignKey('clue.clue_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='线索id')
    interaction_num = Column(Integer, server_default=text("'0'"), comment='每天交互数')
    stat_date = Column(Date, comment='统计日期')

    clue = relationship('Clue')


class ClueFissionHistory(Base):
    __tablename__ = 'clue_fission_history'

    cfh_id = Column(VARCHAR(64), primary_key=True)
    clue_id = Column(ForeignKey('clue.clue_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='线索id')
    fm_id = Column(VARCHAR(64), comment='裂变任务id')
    sn = Column(Integer, comment='裂变顺序')
    from_client_id = Column(VARCHAR(64), comment='来源客户id')
    to_client_id = Column(VARCHAR(64), comment='去向客户id')
    fission_time = Column(DateTime, comment='裂变时间')

    clue = relationship('Clue')


class ClueForward(Base):
    __tablename__ = 'clue_forward'
    __table_args__ = {'comment': '线索转交表'}

    forward_id = Column(VARCHAR(64), primary_key=True)
    clue_id = Column(ForeignKey('clue.clue_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, comment='线索id')
    forward_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='转交员工id')
    forward_person_name = Column(VARCHAR(255), comment='转交员工姓名；如果id是空的情况下使用')
    forward_time = Column(DateTime, comment='转交时间')
    forward_status = Column(VARCHAR(1), nullable=False, server_default=text("'1'"), comment='转交状态；1：在服务；0：未服务（已转交他人）')

    clue = relationship('Clue')
    forward_user = relationship('TouchpointUser')


class ClueImproveAdvise(Base):
    __tablename__ = 'clue_improve_advise'
    __table_args__ = {'comment': '线索提升建议'}

    clue_improve_id = Column(VARCHAR(64), primary_key=True, comment='线索提升建议id')
    clue_id = Column(ForeignKey('clue.clue_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='对于线索id')
    improve_advise_text = Column(VARCHAR(500), comment='提升建议')
    advise_sn = Column(Integer, comment='提示建议序号')
    advise_reason = Column(VARCHAR(500), comment='提升建议的依据')

    clue = relationship('Clue')


class DocClearRelation(Base):
    __tablename__ = 'doc_clear_relation'
    __table_args__ = {'comment': '文档清洗关系表'}

    id = Column(BigInteger, primary_key=True)
    document_id = Column(ForeignKey('aigc_knowledge_document.document_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True)
    rule_id = Column(ForeignKey('doc_clear_rule.rule_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True)

    document = relationship('AigcKnowledgeDocument')
    rule = relationship('DocClearRule')


class RelDataPowerDetail(Base):
    __tablename__ = 'rel_data_power_detail'
    __table_args__ = {'comment': '权限配置详情'}

    rel_id = Column(Integer, primary_key=True)
    data_ids = Column(VARCHAR(1000), nullable=False, comment='数据id')
    data_type = Column(VARCHAR(50), nullable=False, comment='类型标识 dpt；user；role；scheme；')
    data_power_id = Column(ForeignKey('rel_data_power.rel_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='权限配置id')

    data_power = relationship('RelDataPower')


class AigcKnowledgeDocuChunkImage(Base):
    __tablename__ = 'aigc_knowledge_docu_chunk_images'
    __table_args__ = {'comment': '切片-图片表'}

    chunk_image_id = Column(BigInteger, primary_key=True, comment='主键id')
    chunk_id = Column(ForeignKey('aigc_knowledge_document_chunk.chunk_id'), index=True, comment='切片id')
    image_url = Column(String(512, 'utf8mb4_general_ci'), comment='图片url')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"))
    update_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"))

    chunk = relationship('AigcKnowledgeDocumentChunk')


class ClientIntegralExchangeDetail(Base):
    __tablename__ = 'client_integral_exchange_detail'
    __table_args__ = {'comment': '积分兑换礼品明细表'}

    exchange_detail_id = Column(BigInteger, primary_key=True, comment='积分兑换详情ID')
    exchange_id = Column(ForeignKey('client_integral_exchange.exchange_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='积分兑换ID')
    gift_id = Column(BigInteger, comment='礼品ID')
    gift_name = Column(VARCHAR(255), comment='礼品名称')
    exchange_number = Column(Integer, comment='兑换数量')
    use_integral = Column(Integer, comment='消耗积分数量')
    delete_flage = Column(TINYINT, server_default=text("'0'"), comment='0：未删除 1：已删除')

    exchange = relationship('ClientIntegralExchange')


class RelClientTask(Base):
    __tablename__ = 'rel_client_task'
    __table_args__ = {'comment': '客户任务详情表'}

    rel_task_id = Column(BigInteger, primary_key=True)
    client_id = Column(ForeignKey('client_info.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='客户id')
    task_id = Column(ForeignKey('mini_task.task_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='任务id')
    status = Column(VARCHAR(10), server_default=text("'0'"), comment='执行状态 0 默认；1完毕；2执行中')
    souvenir = Column(VARCHAR(255), server_default=text("''"), comment="礼品奖励， ','多个礼品ID")
    award = Column(Integer, server_default=text("'0'"), comment='奖励金额')
    exchange_id = Column(ForeignKey('client_integral_exchange.exchange_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='礼品兑换ID')
    add_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP"), comment='添加时间')
    modify_time = Column(DateTime, server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), comment='添加时间')
    delete_flag = Column(VARCHAR(10), server_default=text("'0'"), comment='是否删除 0 否； 1删除')
    is_update_value = Column(TINYINT, server_default=text("'0'"), comment='是否更新了成长值 1：是 0：否')
    update_value_time = Column(DateTime, comment='更新成长值时间')
    repeat = Column(Integer, server_default=text("'1'"), comment='可执行次数')

    client = relationship('ClientInfo')
    exchange = relationship('ClientIntegralExchange')
    task = relationship('MiniTask')


class ClientIntegral(Base):
    __tablename__ = 'client_integral'
    __table_args__ = {'comment': '权益明细表'}

    integral_id = Column(BigInteger, primary_key=True, comment='积分ID')
    client_id = Column(ForeignKey('client.client_id', ondelete='RESTRICT', onupdate='RESTRICT'), nullable=False, index=True, server_default=text("''"), comment='客户ID')
    integral_type = Column(TINYINT, nullable=False, comment='积分类型 1：获得积分 2：积分兑换')
    integral = Column(Integer, nullable=False, comment='积分')
    source_name = Column(VARCHAR(255), nullable=False, server_default=text("''"), comment='来源名称')
    add_time = Column(DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP"), comment='时间')
    delete_flag = Column(TINYINT, nullable=False, server_default=text("'0'"), comment='0:未删除 1：已删除')
    work_id = Column(Integer, nullable=False, server_default=text("'0'"), comment='关联work平台id')
    exchange_id = Column(ForeignKey('client_integral_exchange.exchange_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='积分兑换ID')
    rel_task_id = Column(ForeignKey('rel_client_task.rel_task_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='任务明细ID')
    detail_type = Column(VARCHAR(10), server_default=text("'client'"), comment='明细类型 client:普通客户  partner:合伙人')
    tp_user_id = Column(ForeignKey('touchpoint_user.tp_user_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='操作人ID')
    together_id = Column(ForeignKey('together_create.together_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='共创ID')
    clue_id = Column(ForeignKey('clue.clue_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='线索ID')
    be_partner_id = Column(ForeignKey('client_partner.partner_id', ondelete='RESTRICT', onupdate='RESTRICT'), index=True, comment='被邀请签约的合伙人ID')

    be_partner = relationship('ClientPartner')
    client = relationship('Client')
    clue = relationship('Clue')
    exchange = relationship('ClientIntegralExchange')
    rel_task = relationship('RelClientTask')
    together = relationship('TogetherCreate')
    tp_user = relationship('TouchpointUser')
