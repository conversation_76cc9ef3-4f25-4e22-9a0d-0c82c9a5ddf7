from flask import request, g


class BaseController:
    
    def __init__(self, corp_id=None, token=None, **kwargs):
        if not corp_id:
            try:
                if hasattr(g, 'corpid'):
                    self.corpid = g.corpid
                else:
                    self.corpid = corp_id
            except:
                self.corpid = corp_id
        else:
            self.corpid = corp_id

        try:
            if hasattr(g, 'suiteid'):
                self.suiteid = g.suiteid
            else:
                self.suiteid = None
        except:
            self.suiteid = None

        try:
            if hasattr(request, 'user'):
                self.__user_info = request.user
            else:
                self.__user_info = dict()
        except:
            self.__user_info = dict()

        if not token:
            try:
                if hasattr(g, 'token'):
                    self.token = g.token
                else:
                    self.token = token
            except:
                self.token = token
        else:
            self.token = token
    
    @property
    def user_info(self):
        return self.__user_info