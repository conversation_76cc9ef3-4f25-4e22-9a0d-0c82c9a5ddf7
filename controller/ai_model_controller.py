# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: ai_model_controller.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 4月 09, 2024
# ---
import json
import requests
from flask import request
from . import BaseController
from modeler.mysql.ai_model_orm import AiModelOrm


class AiModelController(BaseController):

    def __init__(self, *args):
        super(AiModelController, self).__init__(*args)
        self.model = AiModelOrm(self.corpid)

    def get_ai_model_list(self, args):

        return self.model.get_aigc_model_list(args)