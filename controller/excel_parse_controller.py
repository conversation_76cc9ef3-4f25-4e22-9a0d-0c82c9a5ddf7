import os
import io
import openpyxl
import urllib.parse

from PIL import Image as PilImage
from utils.oss_utils import OSSUtil
from lib_func.logger import logger
class ExcelParser:
    def __init__(self, path_or_bytesio):
        """
        初始化 ExcelParser 类。

        参数:
        file_path (str): 要加载的 Excel 文件路径
        """
        if isinstance(path_or_bytesio, str):
            self.workbook = openpyxl.load_workbook(path_or_bytesio)
        elif isinstance(path_or_bytesio, io.BytesIO):
            self.workbook = openpyxl.load_workbook(path_or_bytesio)
        else:
            raise TypeError("Unsupported file type. Expected str or io.BytesIO.")
            

    def parse(self, column_indices=None, ignore_image=False, sheet_name=None, start_row=2, end_row=None):
        sheet = self.workbook.active
        if sheet_name is not None:
            sheet = self.workbook[sheet_name]
        
        return self._parse_sheet(sheet, column_indices=column_indices, ignore_image=ignore_image, start_row=start_row, end_row=end_row)

    def _parse_sheet(self, sheet, column_indices=None, ignore_image=False, start_row=2, end_row=None):
        """
        解析给定的 Excel 工作表，提取每行的文本内容，并根据需要处理图像。

        参数:
        sheet (openpyxl.worksheet.worksheet.Worksheet): 要解析的 Excel 工作表对象。
        column_indices (list of int, optional): 列索引的列表，指定要提取文本的列, 列的序号从1开始。如果为 None, 则提取所有列的文本。默认值为 None。
        ignore_image (bool, optional): 是否忽略图像。如果为 True, 则不处理工作表中的图像。默认值为 False。
        start_row: 起始行， 默认为2, 跳过第1行的表头信息
        end_row: 结束行, 默认为None, 读取所有行

        返回:
        list of dict: 每一行的文本及相关图像的记录。每个字典包含以下键：
            - 'row' (int): 行号
            - 'texts' (list of str): 该行中提取的文本列表。
            - 'images' (list of openpyxl.drawing.image.Image, optional): 该行中与图像相关的列表（如果存在图像）。

        示例:
        >>> from openpyxl import load_workbook
        >>> parser = ExcelParser('example.xlsx')
        >>> records = parser.parse(sheet, column_indices=[1, 2], ignore_image=True)
        >>> logger.info(records)
        [{'row': 1, 'texts': ['A1', 'B1']}, {'row': 2, 'texts': ['A2', 'B2']}]
        """
        records = []

        images_of_row = {}
        if not ignore_image:
            for image in sheet._images:
                position = image.anchor
                row, col = position._from.row + 1, position._from.col
                if row not in images_of_row:
                    images_of_row[row] = []
                images_of_row[row].append(image._data())

        start_row_index = 1 if start_row is None else max(1, start_row)
        end_row_index = sheet.max_row if end_row is None else min(sheet.max_row, end_row)
        for row_index, row in enumerate(sheet.iter_rows(min_row=start_row_index, max_row=end_row_index), start=start_row_index):
            texts = []

            if column_indices is None:
                for col_idx in range(0, sheet.max_column):
                    cell = row[col_idx]
                    texts.append(str(cell.value) if cell.value is not None else '')
            else:
                for col_idx in column_indices:
                    cell = row[col_idx - 1]  # openpyxl的列索引从0开始，因此减1
                    texts.append(str(cell.value) if cell.value is not None else '')
            texts = list(filter(lambda str: len(str) > 0, texts))
            if(len(texts) > 0):
                record = {'row': row_index, 'texts': texts}
                if(row_index in images_of_row):
                    record['images'] = images_of_row[row_index]
                records.append(record)
        
        # logger.info(f"=== records: {records[:5]}")

        return records
    
    def process_excel_results(self, records):
        base_path = "./files"
        
        excel_image_list = []

        for record in records:
            excel_image_map = {
                "row": record['row'],
                "images": []
            }
            logger.info(f"{record['row']}, {record['texts']}")

            if 'images' not in record:
                continue

            images = record['images']
            folder_path = os.path.join(base_path, f"row_{record['row']}")  # 每行创建一个文件夹
            os.makedirs(folder_path, exist_ok=True)  # 创建文件夹

            for i, image_bytes in enumerate(images):
                image_name = f"image_{i+1}.png"
                image_path = os.path.join(folder_path, image_name)
                
                # 保存图片到本地
                with open(image_path, 'wb') as img_file:
                    img_file.write(image_bytes)
                logger.info(f"Saved image {i+1} to {image_path}")

                # 上传图片到阿里云 OSS
                oss_path = f"row_{record['row']}/{image_name}"  # OSS 中的路径
                res = OSSUtil().put_object_from_file(oss_path, image_path)
                oss_url = urllib.parse.unquote(res.resp.response.url)
                logger.info(f"Uploaded image {i+1} to OSS: {oss_url}")
                excel_image_map['images'].append(oss_url)
            excel_image_list.append(excel_image_map)
        return excel_image_list
    
    
if __name__ == "__main__":
    parser = ExcelParser('./通用数科问题.xlsx')
    records = parser.parse(column_indices=[3,4,5])
    for record in records:
        if('images' not in record):
            continue

        images = record['images']

        # 获取图片数据
        image_bytes = images[0]
        image = PilImage.open(io.BytesIO(image_bytes))
        
        image.show()

        # 询问用户是否继续
        # user_input = input("是否继续显示下一条记录？(Y/N): ").strip().lower()
        # if user_input == 'n':
        #     exit(0)