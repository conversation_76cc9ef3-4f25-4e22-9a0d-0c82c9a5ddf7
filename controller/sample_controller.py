import time

import pandas as pd
from controller import BaseController
from controller.knowledge_controller import KnowledgeController
from modeler.mysql.app_orm import AppOrm
from modeler.mysql.knowledge_orm import KnowledgeOrm
from puppet import es_sdk
from settings import DEFAULT_LOCAL_EMB_MODEL_PATH
from lib_func.logger import logger
class SampleController(BaseController):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.app_orm = AppOrm(self.corpid)
        self.es = es_sdk.EsSdkPool(self.corpid)

        self.index_name = f'knowledge_gtapp_demo_{DEFAULT_LOCAL_EMB_MODEL_PATH}'
        self.knowledge_orm = KnowledgeOrm(self.corpid)
        self.knowledge_controller = KnowledgeController(self.corpid)

    def get_sample_list(self, **kwargs):
        """
        查询样本列表
        :param kwargs:
        :return:
        """
        content = kwargs.get('content', None)
        start_size = kwargs.get('page_size', 1) * (kwargs.get('page_no', 1) - 1)

        if not content:
            query = {
                "query": {
                    "term": {
                        "app_id": {"value": kwargs.get('app_id', "")}
                    }
                },
                "from": start_size,
                "size": kwargs.get('page_size', 10)
            }
        else:
            query = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "term": {
                                    "app_id": {"value": kwargs.get('app_id', "")}
                                }
                            },
                            {
                                "match": {
                                    "question": content
                                }
                            }
                        ]
                    }
                },
                "from": start_size,
                "size": kwargs.get('page_size', 10)
            }

        result = self.es.search_data(self.index_name, query)
        return self.handle_es_to_table_list(result)

    def truncate_sample_list(self, app_id, action):
        """
        清空样本列表
        :param kwargs:
        :return: 成功或失败的状态码
        """

        if action != "truncate":
            return 0

        query = {
            "query": {
                "term": {
                    "app_id": {"value": app_id}
                }
            }
        }

        # 使用 Elasticsearch 的 delete_by_query API 进行截断
        self.es.delete_data(self.index_name, query)
        self.es.refresh_index(self.index_name)
        return 1

    # def delete_record_by_id(self, app_id, record_id):
    #     """
    #     根据 ES 中的 ID 删除一条记录，同时匹配指定的 app_id
    #     :param record_id: 记录的 ID
    #     :param app_id: 记录所属的应用 ID
    #     :return:
    #     """
    #     query = {
    #         "query": {
    #             "bool": {
    #                 "must": [
    #                     {"term": {"_id": record_id}},
    #                     {"term": {"app_id": app_id}}
    #                 ]
    #             }
    #         }
    #     }
    #     try:
    #         result = self.es.delete_data(self.index_name, query)
    #         return result['result'] == 'deleted'
    #     except Exception as e:
    #         logger.error(f"Error deleting record with ID {record_id} and app_id {app_id}: {e}")
    #         return False

    def create_samples(self, app_id, question_list):
        """
        批量导入问题列表到 ES
        :param app_id: 应用的 ID
        :param question_list: 要导入的问题列表，每个问题为一个字符串
        :return:
        """
        mapping = {
            "properties": {
                "question": {
                    "type": "keyword"
                },
                "answer": {
                    "type": "text"
                },
                "vector": {
                    "type": "dense_vector",
                    "dims": 1024,
                    "index": True
                }
            }
        }

        for question in question_list:
            # 跳过不匹配的 app_id
            if not question:
                continue

            # 创建嵌入向量
            emb_data = self.knowledge_controller.create_embeddings(
                question, self.corpid, self.knowledge_controller.default_local_emb_model_path
            )

            if emb_data['code'] == 0:
                data = {
                    "app_id": str(app_id),
                    "question": question,
                    "vector": emb_data['vector']
                }

                # 删除已存在的同一问题记录
                delete_query = {
                    "query": {
                        "bool": {
                            "must": [
                                {"term": {"app_id": str(app_id)}},
                                {"term": {"question": question}}
                            ]
                        }
                    }
                }
                self.es.delete_data(self.index_name, delete_query)

                # 插入新记录
                self.es.insert_data(self.index_name, data, mapping)

        self.es.refresh_index(self.index_name)
        return 1

    def update_sample_by_id(self, app_id, record_id, question):
        """
        根据 ES 中的 ID 修改记录的 question 字段，同时匹配指定的 app_id
        :param record_id: 记录的 ID
        :param app_id: 记录所属的应用 ID
        :param question: 更新后的 question 内容
        :return:
        """
        update_query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"_id": record_id}},
                        {"term": {"app_id": app_id}}
                    ]
                }
            },
            "script": {
                "source": "ctx._source.question = params.question",
                "params": {
                    "question": question
                }
            }
        }
        try:
            result = self.es.update_data(self.index_name, update_query)
            self.es.refresh_index(self.index_name)
            return result['updated'] > 0
        except Exception as e:
            logger.error(f"Error updating record with ID {record_id} and app_id {app_id} in index {self.index_name}: {e}")
            return False

    def delete_samples_by_ids(self, app_id, record_ids):
        """
        根据 ES 中的 ID 列表删除若干条记录，同时匹配指定的 app_id
        :param index_name: 索引名称
        :param record_ids: 记录的 ID 列表
        :param app_id: 记录所属的应用 ID
        :return:
        """
        query_dict = {
            "query": {
                "bool": {
                    "must": [
                        {"ids": {"values": record_ids}},
                        {"term": {"app_id": app_id}}
                    ]
                }
            }
        }
        try:
            result = self.es.delete_data(self.index_name, query_dict)
            self.es.refresh_index(self.index_name)
            return result['deleted'] == len(record_ids)
        except Exception as e:
            logger.error(f"Error deleting records with IDs {record_ids} and app_id {app_id} in index {self.index_name}: {e}")
            return False

    def handle_es_to_table_list(self, data=None):
        if not data:
            return {
                'total': 0,
                'data_list': []
            }
        total = data.get('hits', {}).get('total', {}).get("value", 0)
        data_list = data.get('hits', {}).get('hits', [])
        front_list = [{"id": item.get("_id", ""), "question": item.get("_source", {}).get("question", "")} for item in data_list]
        return {
            'total': total,
            'data_list': front_list
        }

    def import_sample(self, app_id, file):
        # 读取不包含表头的 Excel 文件，并且文件只包含一列 (即 'question' 列)
        df = pd.read_excel(file, header=None, dtype=str)

        # 生成 'app_id' 列，并赋值为传入的 app_id
        df['app_id'] = str(app_id)

        mapping = {
            "properties": {
                "question": {
                    "type": "keyword"
                },
                "answer": {
                    "type": "text"
                },
                "vector": {
                    "type": "dense_vector",
                    "dims": 1024,
                    "index": True
                }
            }
        }

        logger.info(f'****Import Samples****: start import samples for app: {app_id}, total: {len(df)}')
        start_time = time.time()
        for i in df.values:
            emb_data = self.knowledge_controller.create_embeddings(i[0], self.corpid,
                                                                   self.knowledge_controller.default_local_emb_model_path)
            if emb_data['code'] == 0:
                data = {
                    "app_id": str(app_id),
                    "question": i[0],
                    "vector": emb_data['vector']
                }

                delete_query = {
                    "query": {
                        "term": {
                            "question": i[0]
                        }
                    }
                }
                # del_res = es.delete_data(index_name, delete_query)
                self.es.insert_data(self.index_name, data, mapping)

        end_time = time.time()
        duration = end_time - start_time
        logger.info(f'****Import Samples****: completed import for app: {app_id} in {duration:.2f} seconds, total: {len(df)}')
        self.es.refresh_index(self.index_name)

    def export_sample(self, app_id, temp_file_path):
        query = {
            "_source": {
                "excludes": ["app_id", "vector"]
            },
            "size": 10000,
            "query": {
                "bool": {
                    "must": [
                        {
                            "term": {"app_id": app_id}
                        }
                    ]
                }
            }
        }
        response = self.es.search_data(self.index_name, query)
        question_list = [hit["_source"]["question"] for hit in response["hits"]["hits"]]

        data = {
            'app_id': [str(app_id)] * len(question_list),
            'question': question_list
        }
        df = pd.DataFrame(data)

        df = df.drop(columns=['app_id'])

        # 导出到 Excel，不保存表头和行索引
        df.to_excel(temp_file_path, index=False, header=False, engine='openpyxl')
        row_count = len(df)
        logger.info(f'Excel 文件已成功生成：{temp_file_path}, 共有 {row_count} 行')