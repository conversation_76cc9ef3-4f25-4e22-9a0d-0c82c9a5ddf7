import os
import json
import re
import time
import pandas as pd
import datetime
import settings
from settings.release import *
from lib_func.const_map import *
from lib_func.logger import logger

from controller import BaseController
from modeler.mysql.chat_free_orm import ChatFreeOrm
from modeler.mysql.app_orm import AppOrm
from modeler.mongo.chat_mongo import ChatMongo
from controller.app_controller import AppController


# class AiChatGpt:

#     def __init__(self, corpid):
#         self.corpid = corpid

#     def receive(self, messages, model_path, is_stream=True):
#         body = {
#             "messages": messages,
#             "model_path": model_path,
#             "is_stream": is_stream
#         }
#         gpt_url = JusureT3Api + '/jusurechat/ai/chat'
#         logger.info(f"Post ChatGPT API: {gpt_url} 模型: {model_path}")
#         try:
#             t1 = time.time()
#             resp = requests.post(gpt_url, json=body, headers={'Content-Type': 'application/json', 'corpid': self.corpid}, stream=is_stream)
#             logger.info(f"Post ChatGPT API succeeded!, codeStatus: {resp.status_code}, requestTime: {int(time.time()-t1)*1000} ms")
#             # logger.info(resp.text)
#         except Exception as e:
#             logger.info(f"Post ChatGPT API failured!, codeStatus: {resp.status_code}")
#         return resp


class ChatFreeController(BaseController):

    def __init__(self, *args):
        super(ChatFreeController, self).__init__(*args)
        self.model = ChatFreeOrm(self.corpid)

    def get_chat_free_files_list(self, page_no, page_size, date_view_name):
        return self.model.get_chat_free_files_list(page_no, page_size, date_view_name)

    def handle_chat_message_by_llm(self, data_view_id, message):

        db_excel_file_path = self.model.get_excel_file_path_by_view_id(data_view_id)
        # db_excel_file_path = '国家开发投资集团有限公司-通讯录_new.xlsx'
        
        # logger.info(f"=== db_excel_file_path: {db_excel_file_path}")

        if db_excel_file_path:
            relative_excel_file_path = db_excel_file_path.lstrip('/')
            hard_excel_file_path = os.path.join(settings.BASE_DIR, relative_excel_file_path)
            
            # logger.info(f"=== relative_excel_file_path: {relative_excel_file_path}, hard_excel_file_path: {hard_excel_file_path}")
            
            file_name = os.path.basename(hard_excel_file_path)
            # hard_excel_file_path = os.path.join("/code", relative_excel_file_path)
            s1 = time.time()
            logger.info(f"Start reading excel file: {hard_excel_file_path}...")

            file_extension = os.path.splitext(hard_excel_file_path)[-1].lower()
            if file_extension == '.xlsx' or file_extension == '.xls':
                df = pd.read_excel(hard_excel_file_path)
            elif file_extension == '.csv':
                df = pd.read_csv(hard_excel_file_path)
            else:
                raise ValueError(f"Unsupported file extension: {file_extension}")
            logger.info(f"Load file time: {time.time() - s1}s")

            preprocessed_message = preprocess_message(message)

            # logger.info(f"preprocessed_message: {preprocessed_message}")

            prompt = generate_prompt(df, preprocessed_message, file_name)

            pd_analysis_map = call_ai_model_v1(prompt, self.corpid)

            return pd_analysis_map, df
        else:
            raise ValueError("Excel file path not found")

    def chatbi_receive(self, pd_analysis_map, df, message):
        # call pd controller TODO
        # chatbi_data = group_and_agg_v1(df, pd_analysis_map)
        chatbi_data = group_and_agg_v2(df, pd_analysis_map, message)
        logger.info("*" * 25)
        logger.info(f"chatbi_data: {chatbi_data['聚合数据']}")
        # ai_data = {
        #     "analysis_prompt": "",
        #     "graph_description": pd_analysis_map['intention_understanding'],
        #     "data_type": 3,
        #     # "graph_type_code": pd_analysis_map['chart_type'],
        # }
        ai_data = {}
        res_data = {'data': chatbi_data, 'ai_data': ai_data, 'params': {}}
        return res_data
    
    def generate_prompt_by_msg_pandas_results(self, message, chatbi_data):
        
        prompt = (
            "你是一个专业的简历统计分析助手，能够结合简历分析数据和用户问题，进行概要分析总结。\n\n"
            
            "## 技能\n"
            "1. 精确理解用户提问，例如岗位人数、学历统计等。\n"
            "2. 根据问题和简历分析数据，给出最准确的回答。\n\n"
            
            "## 限制\n"
            "- 只回答与简历相关的问题。\n"
            "- 如果无法获取相关数据，忽略key值进行分析。\n"
            "- 如果搜索结果有姓名，请返回对应内容.\n"
            
            f"## 用户问题\n{message}\n\n"
            
            f"## 根据搜索结果\n{chatbi_data}\n\n进行回答")

        logger.info(f"Free Q&A Prompt: {prompt}")
    
        return prompt


def replace_with_date(message, keywords, replace_date):
    for keyword in keywords:
        message = message.replace(keyword, replace_date)
    return message


def replace_keywords_with_quotes(message, keywords):
    for word in keywords:
        if word in message:
            message = message.replace(word, f'[{word}]')
    return message


def replace_province_suffix(message):
    # 匹配所有以“省”结尾的省份名称，并去掉“省”字
    message = re.sub(r'(\w+)省', r'\1', message)
    return message


def preprocess_message(message):
    # now_date = '2024-04-01'
    now_date = datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d')
    now_year = datetime.datetime.strftime(datetime.datetime.now(), '%Y')
    today_list = ['今天', '今日', '当日']
    yesterday_list = ['昨天', '昨日']
    qian_day_list = ['前天']
    da_qian_day_list = ['大前天']

    # 定义产品关键字
    product_keywords = ["和对讲", "云视讯", "千里眼"]

    if any(word in message for word in today_list):
        message = replace_with_date(message, today_list, now_date)
    if any(word in message for word in yesterday_list):
        yesterday = datetime.datetime.strftime(datetime.datetime.now() - datetime.timedelta(days=1), '%Y-%m-%d')
        message = replace_with_date(message, yesterday_list, yesterday)
    if any(word in message for word in qian_day_list):
        qian_day = datetime.datetime.strftime(datetime.datetime.now() - datetime.timedelta(days=2), '%Y-%m-%d')
        message = replace_with_date(message, qian_day_list, qian_day)
    if any(word in message for word in da_qian_day_list):
        da_qian_day = datetime.datetime.strftime(datetime.datetime.now() - datetime.timedelta(days=3), '%Y-%m-%d')
        message = replace_with_date(message, da_qian_day_list, da_qian_day)

    # 处理产品相关的替换
    message = replace_keywords_with_quotes(message, product_keywords)

    # 去掉省份名称中的“省”字
    message = replace_province_suffix(message)

    return message


def process_pd_obj(df):
    pd_obj_map = {}
    object_columns = df.select_dtypes(include='object')
    for column in object_columns:
        unique_values = object_columns[column].unique()
        pd_obj_map[column] = unique_values.tolist()
    return pd_obj_map


def generate_prompt(df, message, file_name, history=''):
    # 表头-列
    # columns_list = df.columns.tolist()
    columns_obj_list = []

    # 遍历所有列，检查数据类型是否为 object
    for col in df.columns:
        # if df[col].dtype == 'object':
        if df[col].dtype == 'object' and col != '任职经历' and col != '联系邮箱':
            # 检查该列的唯一值并添加到 columns_list
            unique_values = df[col].unique().tolist()
            columns_obj_list.append({col: unique_values})
    columns_obj_list = columns_obj_list[0:3]

    # 表格前五行内容
    # json.dumps(df.head(5).to_json(orient='records'), indent=4, ensure_ascii=False)
    data_head_dict = df.head(1).to_dict()
    # logger.info(f"=== data_head_dict: {data_head_dict}")
    # 表格内容obj枚举: pd_obj_map

    rsp_example = {
        "filter": [
            {"column": "列名1", "value": "过滤值1_1,过滤值1_2", "operate": "=="},
            {"column": "列名2", "value": "过滤值2", "operate": ">="}
        ],
        "groupby": ["列名1", "列名2"],
        "group_calc": {
            "列名1": "sum",
            "列名2": "mean"
        },
        "intention_understanding": "主营业务收入增幅情况"
    }

    content = (f"# 角色\n"
               "你是一个知识渊博的智能助手，能够根据用户的提问和表格的信息迅速提取有用的信息。\n\n"
               "## 技能\n"
               "### 技能1 内容提取\n"
               # f"根据用户上传的表格表头以及第一行内容:{data_head_dict} 和表格内所有枚举信息: {columns_obj_list} 以及用户输入内容:【{message}】 提取下述内容：\n"
               f"根据用户上传的表格表头以及第一行内容:{data_head_dict}以及用户输入内容:【{message}】 提取下述内容：\n"
               "1. 根据输入内容提取数据分析过滤条件、分组方式、聚合方式。\n"
               "### 技能3 根据所给信息精准检索,把信息准确匹配到返回json的对应位置，如示例，这个json将用于后续的Pandas数据分析。\n"
               "## 限制:\n"
               "1. 响应json格式是固定的，filter, groupby, group_calc, intention_understanding不能为空按照下述要求提取。\n"
               "2. 通过表格信息和用户输入内容进行数据分析过滤条件提取，提取到信息然后写到响应json的filter里，filter的对象column是列名，value是对应column需要过滤的值,用operate表示如何过滤。其中：如果单个列column提取到多个值，就写到对应value用英文逗号隔开。\n"
               "3. 通过表格信息和用户输入内容进行数据分析提取分组方式，把你认为需要使用pandas groupby的数据列写到返回的json的groupby里，精准匹配，不能为空。\n"
               "4. 通过表格信息和用户输入内容进行数据分析提取聚合方式，把你认为需要使用pandas聚合函数的数据写到返回的json的group_calc里，其中：key是列名。value是pandas聚合函数。\n"
               "5. 通过表格信息和用户输入内容进行用户何种意图提取，简短总结，不能为空，然后写到响应json的intention_understanding里。\n"
               f"- 返回：严格的json格式，返回示例：{rsp_example}，不要有多月的文字输出。\n\n"
               f"- 自我监督检查：返回的json格式一定要严格遵循示例格式，如出现与示例格式不一致的情况请进行自我修复。\n\n")

    logger.info("+" * 30)
    logger.info(content)
    logger.info("+" * 30)

    return content


def call_ai_model_v1(prompt, corpid):
    # corpid = "wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ"
    messages = [{'role': 'system', 'content': prompt}]
    # AiChatGpt未修复无法使用，更换为MyChatGPT
    response = AppController(corpid).ai_app_chat(messages, [], CHATGPT_CODE, "gpt-4", is_stream=False)
    res_str = ''
    for result,_ in response:
        res_str = result

    if not isinstance(res_str, str):
        raise TypeError("Response data is not a string")

    json_str = res_str.replace('```json', '').replace('```', '')

    # Try to extract the JSON object enclosed by triple backticks
    # pattern = r'```json\n(.*?)\n```'
    # match = re.search(pattern, res_str, re.DOTALL)
    #
    # if match:
    #     json_str = match.group(1)
    # else:
    #     # Extract JSON fragments
    #     pattern = r'\{.*?\}'
    #     matches = re.findall(pattern, res_str, re.DOTALL)
    #     # logger.info(f"matches: {matches}, {type(matches)}")
    #
    #     if not matches:
    #         raise ValueError("No JSON objects found in the response")
    #
    #     # Combine the JSON fragments properly
    #     json_str = '{'
    #     for i, match in enumerate(matches):
    #         if i > 0 and match.strip().startswith('{'):
    #             json_str += ','  # Add comma between fragments
    #         json_str += match
    #     json_str += '}'
    #
    # # Replace single quotes with double quotes to ensure valid JSON format
    json_str = json_str.replace("'", '"')
    logger.info(json_str)
    # # json_str = _repair_json(json_str)

    try:
        # Load the combined JSON string
        parsed_response = json.loads(json_str)
    except json.JSONDecodeError as e:
        logger.error(f"JSONDecodeError: {e}")
        logger.error(f"json_str: {json_str}")
        raise
    return parsed_response


def _repair_json(new_match):
    # 检查json括号闭合情况
    flag = True
    while flag and new_match:
        try:
            result = json.loads(new_match)
            flag = False
        except json.JSONDecodeError:
            logger.error(new_match)

            # 检查并补全缺失的中括号或大括号
            bracket_stack = []
            for i, char in enumerate(new_match):
                if char == '[':
                    bracket_stack.append(('[', i))
                elif char == ']':
                    if not bracket_stack or bracket_stack[-1][0] != '[':
                        # 如果栈为空或者栈顶不是'['，则在当前位置插入'['
                        new_match = new_match[:i] + ']' + new_match[i:]
                    else:
                        bracket_stack.pop()
                elif char == '{':
                    bracket_stack.append(('{', i))
                elif char == '}':
                    if not bracket_stack or bracket_stack[-1][0] != '{':
                        # 如果栈为空或者栈顶不是'{'，则在当前位置插入'}'
                        new_match = new_match[:i] + '}' + new_match[i:]
                    else:
                        bracket_stack.pop()

            # 补全剩余的缺失括号
            for bracket_type, pos in reversed(bracket_stack):
                if bracket_type == '[':
                    new_match += ']'
                elif bracket_type == '{':
                    new_match += '}'
    return new_match


def cal_func(data):
    return ','.join(data.values.tolist())


def group_and_agg_v2(data, mapping, message):
    """
    对表格数据进行分组聚合
    """
    filters = mapping['filter']
    group_by = mapping.get('groupby')
    calc = mapping['group_calc']
    if not group_by and not calc and not filters:
        return {'聚合数据': {}}
    date = None
    for filter in filters:
        values = filter.get('value').split(',')
        value = []
        for val in values:
            try:
                tmp = int(val)
            except ValueError:
                tmp = val
            value.append(tmp)

        # 需要判断字段值是否是字符串, 如果是列表形式则不需要进行字符串和int类型判断
        column = filter.get('column')
        if column in data.columns:
            operate = filter.get('operate')
            if filter.get("is_date"):
                date = column
                new_column = column + 'copy'
                try:
                    data[new_column] = pd.to_datetime(data[column], format="%Y%m")
                except ValueError:
                    data[new_column] = pd.to_datetime(data[column])
                operate = ">="
                query_str = f"{new_column} {operate} '{value[0]}'"
                data.query(query_str, inplace=True)
                operate = "<="
                query_str = f"{new_column} {operate} '{value[1]}'"
            else:
                if '：' in column or '（' in column or '）' in column:
                    new_column = 'temp_column'
                    data[new_column] = data[column]
                    column = new_column

                # 修复 str.contains 查询时传递列表的问题
                if operate == '>' or operate == '<' or operate == '>=' or operate == '<=' or operate == '!=':
                    try:
                        value_1 = value[0]
                        query_str = f"{column} {operate} @value_1"
                    except:
                        query_str = f"{column} {operate} {value}"
                else:
                    # if operate == '==' or operate == 'contains':
                    # 检查value是否为列表，并生成对应的查询字符串
                    if isinstance(value, list) and len(value) > 1:
                        # 将列表中的值用'|'连接成正则表达式
                        if data[column].dtype == 'int64' or data[column].dtype == 'int32':
                            query_str = f"{column} {operate} {value}"
                        else:
                            value_str = '|'.join(map(str, value))
                            query_str = f'{column}.str.contains("{value_str}")'
                    elif isinstance(value, list) and len(value) == 1:
                        # 如果是单个值，直接使用str.contains
                        value_0 = value[0]
                        query_str = f'{column}.str.contains("{value_0}")'
                    else:
                        filter_val = filter["value"]
                        query_str = f'{column}.str.contains("{filter_val}")'

            # 进行过滤查询
            try:
                data.query(query_str, inplace=True)
            except Exception as e:
                logger.error(str(e))

    # 分组和聚合
    try:
        if not group_by:
            group_by = [filter['column'] for filter in filters if filter.get("column")]
        if not group_by:
            # 没有groupby的时候，主要以公司作为分组
            if '所属公司' in data.columns:
                group_by = ['所属公司']
            elif '当前单位' in data.columns:
                group_by = ['当前单位']
        if not calc:
            if len(data) <= 10:
                return {'聚合数据': data.to_dict(orient='records')}
            else:
                calc = {'姓名': 'count'}
        key_list = [key for key in calc.keys() if key not in group_by]
        group_data = data.groupby(group_by)
        tmp_calc = {}
        for key, value in calc.items():
            tmp_calc[key] = key + value
        new_data = group_data.agg(calc).rename(columns=calc)
        # 通讯录太长，人员信息太多超限
        # new_cal = {}
        # try:
        #     key_list.remove('人数')
        # except Exception:
        #     pass
        # if key_list and group_by != ['姓名'] and 'min' not in calc.values() and 'max' not in calc.values():
        #     # 姓名groupby相当于没有groupby，太多了，不进行详情展示，min和max也是数据太多不展示到详情
        #     if '姓名' not in key_list and '姓名' not in group_by and '姓名' in data.columns:
        #         # 加入姓名到详情，可以查看具体的人是谁
        #         key_list.append('姓名')
        #     try:
        #         key_list.remove('入职时间')
        #     except Exception:
        #         pass
        #     for key in key_list:
        #         new_cal[key] = key + '详情'
        #     tmp = group_data[key_list].agg(cal_func).rename(columns=new_cal)
        #     new_data = new_data.join(tmp, on=new_data.index)
        group_result = new_data.reset_index().to_dict(orient='records')
    except Exception as e:
        # 如果分组聚合失败，则返回未处理的数据
        logger.error(f"分组聚合时发生错误: {e}")
        group_result = {}
    return {'聚合数据': group_result}


if __name__ == '__main__':
    # chat_free = ChatFreeController('wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ')
    message = '兼职岗位为董事的有多少人'
    # pd_analysis_map, df = chat_free.handle_chat_message_by_llm(4942752873491468289, message)
    df = pd.read_excel(r'/Users/<USER>/PycharmProjects/jusure_AI/upload/4942752728037199873/国家开发投资集团有限公司-通讯录_new.xlsx')
    pd_analysis_map = {'filter': [{'column': '部门', 'value': '秘书处', 'operate': '=='}], 'groupby': ['姓名', '工号'], 'group_calc': {}, 'intention_understanding': '查询秘书处的人员信息'}
    # df1 = pd.read_excel(r'/Users/<USER>/Desktop/zsrz/国家开发投资集团有限公司-通讯录-导出.xlsx', header=2)
    # df = pd.read_excel(r'/Users/<USER>/PycharmProjects/jusure_AI/国家开发投资集团有限公司-通讯录.xlsx')
    # df.drop_duplicates(inplace=True)
    # now_date = pd.Timestamp.today()
    # from dateutil.relativedelta import relativedelta
    # df['入职日期'].fillna(now_date.strftime('%Y-%m-%d'), inplace=True)
    # df['入职日期_date'] = pd.to_datetime(df['入职日期'], format='%Y-%m-%d')
    # df['年月差'] = df['入职日期_date'].apply(lambda x: relativedelta(now_date, x))
    # df['入职时间'] = df['年月差'].apply(lambda rd: rd.years + round(rd.months / 12, 1))
    # df['入职时间'] = df['入职时间'].astype(str)
    # df.fillna('OA系统暂未填写', inplace=True)
    # df.drop(['入职日期_date'], axis=1, inplace=True)
    # df.drop(['年月差'], axis=1, inplace=True)
    # df.drop(['职务名称'], axis=1, inplace=True)
    # df.drop(['联系邮箱'], axis=1, inplace=True)
    # def name_map(data):
    #     return data.split('|')[0]
    # def df_map(data):
    #     new_data = data.split(',')
    #     res = []
    #     for new in new_data:
    #         res.append(new.split('/')[-1])
    #     return ','.join(res)
    # df['部门'] = df['部门'].apply(df_map)
    # df['姓名'] = df['姓名'].apply(name_map)
    # df1['姓名'] = df1['姓名'].apply(name_map)
    # df = df.merge(df1[['工号', '联系手机', '联系邮箱']], on='工号', how='inner')
    # # df = df.merge(df1[['姓名', '联系邮箱']], on='姓名', how='outer', suffixes=('_1', '_2'))
    # # df['联系邮箱'] = df['联系邮箱_1'].combine_first(df['联系邮箱_2'])
    # # df.drop(['联系邮箱_1', '联系邮箱_2'], axis=1, inplace=True)
    # df['联系手机'] = df['联系手机'].astype(str)
    # df.to_excel(r'/Users/<USER>/PycharmProjects/jusure_AI/upload/4942752728037199873/国家开发投资集团有限公司-通讯录_new.xlsx', index=False)
    group_and_agg_v2(df, pd_analysis_map, message)
