# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: demo_controller.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 7月 09, 2024
# ---

from controller import BaseController


question_type = "研究方向、非财务风险、项目发展目标、创新型、加大投入"

question_list = {
    '研究方向': [
        {
            'name': '1、海油发展在2024年第一季度的研发费用为1.657亿元,同比有所增长,显示公司持续加大了科研投入力度',
            'quote': [
                {
                    'index': 1,
                    'chunk': 'asfasdfsdfs',
                    'doc_name': '测试文档名称',
                    'doc_download': '下载地址'
                },
                {
                    'index': 1,
                    'chunk': 'asfasdfsdfs',
                    'doc_name': '测试文档名称',
                    'doc_download': '下载地址'
                }
            ],
            'charts': {
                'title': '营业收入',
                'charts_type': 'line',
                'unit': '百万元',
                'data_list': [
                    {
                        'name': '2021年',
                        'value': 29203,
                    },
                    {
                        'name': '2022年',
                        'value': 35658.9,
                    },
                    {
                        'name': '2023年',
                        'value': 44108.6,
                    }
                ]
            }

        },
        {
            'name': '2、公司重点发展了FPSO生产技术服务、油田化学服务、多功能生活支持平台、油田装备运维、数据信息、监督监理等业务',
            'quote': [
                {
                    'index': 2,
                    'chunk': 'asfasdfsdfs',
                    'doc_name': '测试文档名称',
                    'doc_download': '下载地址'
                },
                {
                    'index': 3,
                    'chunk': 'asfasdfsdfs',
                    'doc_name': '测试文档名称',
                    'doc_download': '下载地址'
                }
            ],
            'charts': {
                'title': '总资产',
                'charts_type': 'line',
                'unit': '百万元',
                'data_list': [
                    {
                        'name': '2021年',
                        'value': 73311.7,
                    },
                    {
                        'name': '2022年',
                        'value': 77160.7,
                    },
                    {
                        'name': '2023年',
                        'value': 83245.8,
                    }
                ]
            }

        },
        {
            'name': '3、根据中国海油发布的2024年度战略展望,2024年公司的资本开支计划为1250亿元,其中将重点投资油气生产储卸油装置项目、LNG运输船项目和基地建设(生产)项目等',
            'quote': [
                {
                    'index': 4,
                    'chunk': 'asfasdfsdfs',
                    'doc_name': '测试文档名称',
                    'doc_download': '下载地址'
                }
            ],
            'charts': {
                'title': '油田技术服务业务实现营业',
                'charts_type': 'line',
                'unit': '百万元',
                'data_list': [
                    {
                        'name': '2021年',
                        'value': 15084.6,
                    },
                    {
                        'name': '2022年',
                        'value': 19599.7,
                    },
                    {
                        'name': '2023年',
                        'value': 25756.9,
                    }
                ]
            }

        },
        {
            'name': '4、海油发展接待了35家机构调研,包括中金公司、国信证券、南方基金等,表明公司在技术创新和产业链拓展方面受到资本市场的广泛关注',
            'quote': [
                {
                    'index': 5,
                    'chunk': 'asfasdfsdfs',
                    'doc_name': '测试文档名称',
                    'doc_download': '下载地址'
                }
            ],
            'charts': {
                'title': '研发费用',
                'charts_type': 'line',
                'unit': '百万元',
                'data_list': [
                    {
                        'name': '2021年',
                        'value': 960.4,
                    },
                    {
                        'name': '2022年',
                        'value': 978.1,
                    },
                    {
                        'name': '2023年',
                        'value': 1253.9,
                    }
                ]
            }

        },
        {
            'name': '5、企业营收、技术服务、研发各项指标持续健康增长',
            'quote': [
                {
                    'index': 6,
                    'chunk': 'asfasdfsdfs',
                    'doc_name': '测试文档名称',
                    'doc_download': '下载地址'
                }
            ],
            'charts': {}

        }
    ],
    '非财务风险': [
        {
            'name': '油价波动的风险 ：若油价发生大幅度波动,会影响中海油等油气公司的资本开支和产量,从而对海油发展的业绩造成影响\nHSSE(健康、安全、安保和环境)风险：由于海洋作业的复杂性,公司日常作业各方面存在潜在的HSSE风险,如发生重大事故可能会导致人员伤亡、环境损害、业务中断\n地缘政治不稳定的风险：若地缘局势发生波动,影响海上油气开采进度',
            'quote': [
                {
                    'index': 1,
                    'chunk': 'asfasdfsdfs',
                    'doc_name': '测试文档名称',
                    'doc_download': '下载地址'
                },
                {
                    'index': 1,
                    'chunk': 'asfasdfsdfs',
                    'doc_name': '测试文档名称',
                    'doc_download': '下载地址'
                }
            ],
            'charts': {
                'title': '雷达图',
                'charts_type': 'radar',
                'unit': '',
                'data_list': [
                    {
                        'name': '油价波动的风险',
                        'value': 0.6,
                    },
                    {
                        'name': 'HSSE风险',
                        'value': 0.8,
                    },
                    {
                        'name': '地缘政治不稳定的风险',
                        'value': 0.4,
                    }
                ]
            }

        }
    ]
}


class DemoController(BaseController):

    def __init__(self, *args):
        super(DemoController, self).__init__(*args)
        self.question_list = question_list

    def get_question_list(self, question_type):
        q_list = self.question_list[question_type]
        return q_list
