# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: classify_controller
# @Author: <PERSON><PERSON><PERSON>
# @E-mail: <PERSON><PERSON>@zhongshuruizhi.com
# @Time: 2024/10/16
# ---
import os

import pandas as pd

from modeler.mysql.app_orm import AppOrm
from modeler.mysql.knowledge_orm import KnowledgeOrm
from controller import BaseController
from controller.knowledge_controller import KnowledgeController
from settings import DEFAULT_LOCAL_EMB_MODEL_PATH
from lib_func.logger import logger

class ClassifyController(BaseController):
    def __init__(self, corpid):
        super().__init__(corpid)
        self.knowledge_orm = KnowledgeOrm(corpid)
        self.knowledge_controller = KnowledgeController(corpid)
        self.es = self.knowledge_controller.es
        self.model = AppOrm(self.corpid)
        self.default_local_emb_model_path = DEFAULT_LOCAL_EMB_MODEL_PATH  # 本地部署向量化模型
        self.not_hit_target_app_id = "4943372548806021121"

    # def classify_and_process_answers(self, question_str, app_info):
    #     return self.classify_and_process_answers_inner(question_str, app_info, False)[0]

    def classify_and_process_answers_llm(self, question_str, app_info, controller):
        result = self.classify_and_process_answers_inner(question_str, app_info, controller)
        result_ids = result.get('app_list', [])
        llm_answer = result.get('llm_answer', [])
        keyword_list = result.get('keyword_list', [])
        vector_list = result.get('scene_list', [])
        app_list = []
        children = app_info['children']

        for result_id in result_ids:
            result_app = ClassifyController.get_app_by_id(children, result_id)
            if result_app:
                app_list.append({
                    # use str for fe
                    "app_id": str(result_app['app_id']),
                    "app_name": result_app['app_name'],
                    "app_content": result_app['classify_prompt'],
                    "app_type": result_app['app_type'],
                })
        match_ids = []
        if not app_list:
            match_ids = [{'app_id': self.not_hit_target_app_id}]
        else:
            match_ids = app_list

        return {
            "app_list": match_ids,
            "llm_answer": llm_answer,
            "keyword_list": keyword_list,
            "scene_list": vector_list,
            "scene_type": result.get('scene_type', 0)
        }


    def classify_and_process_answers_inner(self, question_str, app_info, controller=None, args=None) -> list:
        # 0. configs
        max_score = 2.9  # score必定匹配
        min_score = 2.4  # score最低阈值
        size = 3  # 使用前n名

        classify_priority = app_info['classify_priority']
        children = app_info['children']

        keyword_list = list()
        vector_list = list()
        llm_answer=list()
        app_list=list()

        def wrapper_result(sel_ids, scene_type=0):
            new_answer = [{**item, 'app_id': str(item.get('app_id', ''))} for item in llm_answer]
            new_vector_list = [{**item, 'app_id': str(item.get('app_id', ''))} for item in vector_list]
            return {'app_list': sel_ids, 'llm_answer': new_answer, 'keyword_list': keyword_list, 'scene_list': new_vector_list, 'scene_type' : scene_type}

        # TODO
        # 1. target hit
        for app in children:
            for target in app["classify_target"]:
                if target in question_str:
                    logger.info(
                        f"*** 命中分类关键字: {target}, use {app['classify_name']}, app_id: {app['app_id']}, question: {question_str}")
                    app_list = app_list if app_list else [app["app_id"]]
                    keyword_list.append(target)
        if app_list:
            return wrapper_result(app_list, scene_type=4)

        app_ids_list = [app["app_id"] for app in children]
        hits = self.search_es_gt_classify_mode_top(question_str, app_ids_list, size, min_score)

        app_id_scores = {}  # app_id -> (count, max_score)
        top_results = []
        results = {app_id: 0 for app_id in app_ids_list}
        max_app_id = self.not_hit_target_app_id # 低于阈值，最终将返回默认id

        if not hits:
            logger.info(f"没有任何相似向量，将转为使用大模型。向量最低分配置：{min_score}，问题：{question_str}")
            classify_priority = 2
        else:
            for hit in hits:
                app_id = int(hit['_source']['app_id'])
                score = hit['_score']
                results[app_id] = results.get(app_id, 0) + 1
                top_results.append({
                    "app_id": app_id,
                    "question": hit['_source']['question'],
                    "score": score
                })

                # 统计 count 和 max_score
                if app_id not in app_id_scores:
                    app_id_scores[app_id] = [0, score]  # 初始化计数和最高分
                app_id_scores[app_id][0] += 1  # 增加计数
                app_id_scores[app_id][1] = max(app_id_scores[app_id][1], score)  # 更新最高分

            # 输出统计信息
            for app_id, count in results.items():
                if count > 0:
                    app_name = ClassifyController.get_app_name_by_id(children, app_id)
                    app_type = ClassifyController.get_app_type_by_id(children, app_id)
                    vector_list.append({
                        "app_id": app_id,
                        "app_name": app_name,
                        "app_type": app_type,
                        "count": count,
                        "max_score": app_id_scores[app_id][1]
                    })
                    logger.info(f"{app_name}（app_id: {app_id}）hits is: {count}")

            # 向量不匹配使用大模型
            if not top_results:
                classify_priority = 2

            # 输出最高分命中
            highest_score_result = max(top_results, key=lambda x: x['score'])
            logger.info(f"最高分命中：{ClassifyController.get_app_name_by_id(children, highest_score_result['app_id'])}, {highest_score_result}")

            # 选择计数最多的 app_id
            max_count = max(count for count, _ in app_id_scores.values())
            max_candidates = [app_id for app_id, (count, _) in app_id_scores.items() if count == max_count]

            # 如果有多个相同计数的 app_id，选择分数最高的
            if len(max_candidates) > 1:
                max_app_id = max(max_candidates, key=lambda app_id: app_id_scores[app_id][1])
                logger.info(f"经过选择使用：{ClassifyController.get_app_name_by_id(children,max_app_id)}")
            else:
                max_app_id = max_candidates[0]

        # 使用大模型
        if classify_priority == 2:
            # may return -1 if llm cannot handle it
            response_app_id = self.invoke(question_str, app_info, controller)
            app_by_llm = ClassifyController.get_app_by_id(children, response_app_id)
            if app_by_llm is not None:
                llm_answer = [app_by_llm]
                app_id_by_llm = app_by_llm.get('app_id')
                logger.info(f"*** classify by llm, use app_id: {app_id_by_llm} ***")
                return wrapper_result([app_id_by_llm], scene_type=2)
            else:
                # 大模型返回出错
                return wrapper_result([max_app_id], scene_type=2)
        # 使用向量
        elif classify_priority == 1:
            return wrapper_result([max_app_id], scene_type=1)
        # 使用手动选择
        elif classify_priority == 0:
            if highest_score_result['score'] < max_score:
                response_app_id = self.invoke(question_str, app_info, controller)
                app_by_llm = ClassifyController.get_app_by_id(children, response_app_id)
                if app_by_llm is not None:
                    app_id_by_llm = app_by_llm.get('app_id')
                    logger.info(f"*** classify by llm, use app_id: {app_id_by_llm} ***")
                    if max_app_id != app_id_by_llm:
                        return wrapper_result([max_app_id, app_id_by_llm])
                    else:
                        return wrapper_result([max_app_id])
                else:
                    return wrapper_result([max_app_id])
            else:
                return wrapper_result([max_app_id])
        else:
            logger.error("Error: 未设置classify_priority")
            return wrapper_result([max_app_id])



    def search_es_gt_classify_mode_top(self, input_text, app_ids, size=10, min_score=2.0, is_search=True):
        try:
            model_path = self.default_local_emb_model_path
            emb_data = self.knowledge_controller.create_embeddings(input_text, self.corpid, model_path, is_search=is_search)
            vector = emb_data['vector']

            body_dict = {
                "_source": {
                    "includes": ["question", "app_id", "_score"]
                },
                "query": {
                    "bool": {
                        "must": [
                            {
                                "terms": {
                                    "app_id": app_ids
                                }
                            },
                            {
                                "script_score": {
                                    "query": {
                                        "match_all": {}
                                    },
                                    "script": {
                                        "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
                                        "params": {
                                            "query_vector": vector
                                        }
                                    }
                                }
                            }
                        ],
                    }
                },
                "size": size,
                "min_score": min_score,
                "sort": [
                    {"_score": {"order": "desc"}}
                ]
            }

            index_name = 'knowledge_gtapp_demo_' + self.default_local_emb_model_path
            hits_ = self.es.search_data(index_name, body_dict)
            hits = hits_.body['hits']['hits']

            logger.info("*" * 35)
            logger.info(f"Search es embeddings demo app index: {index_name}, hits size: {len(hits)}")
            return hits

        except Exception as e:
            logger.error(e)
            return []

    def search_es_gt_classify_mode(self, input_text, app_id, size=1000, min_score=1.8, is_search=True):
        try:
            model_path = self.default_local_emb_model_path
            emb_data = self.knowledge_controller.create_embeddings(input_text, self.corpid, model_path, is_search=is_search)
            vector = emb_data['vector']

            body_dict = {
                "_source": {
                    "includes": ["question"]
                },
                "query": {
                    "bool": {
                        "must": [
                            {
                                "term": {"app_id": app_id}
                            },
                            {
                                "script_score": {
                                    "query": {
                                        "match_all": {}
                                    },
                                    "min_score": min_score,
                                    "script": {
                                        "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
                                        "params": {
                                            "query_vector": vector
                                        }
                                    }
                                }
                            }
                        ]
                    }
                },
                "size": size
            }

            index_name = 'knowledge_gtapp_demo_' + self.default_local_emb_model_path
            hits_ = self.es.search_data(index_name, body_dict)
            hits = hits_['hits']['hits']

            logger.info("*" * 35)
            logger.info(f"Search es embeddings demo app index: {index_name}, min_score: {min_score}, \n hits size: ",
                  len(hits))
            # return [hit['_source']['answer'] for hit in hits][0]
            return len(hits)
        except Exception as e:
            logger.error(e)
            return []

    def export_answer_question(self, qa_id, name, export_path):
        index_name = 'knowledge_gt_demo_qa_bge-large-zh-v1.5'

        query = {
            "_source": {
                "excludes": ["vector"]
            },
            "size": 10000,
            # "query": {
            #     "bool": {
            #         "must": [
            #             {
            #                 "term": {"qa_id": qa_id}
            #             }
            #         ]
            #     }
            # }
        }

        # 查询 Elasticsearch
        response = self.es.search_data(index_name, query)

        # 提取 question 和 answer 字段
        question_list = [hit["_source"].get("question", "") for hit in response["hits"]["hits"]]
        answer_list = [hit["_source"].get("answer", "") for hit in response["hits"]["hits"]]

        # 将数据转换为 DataFrame
        data = {
            'qa_id': [qa_id] * len(question_list),
            'question': question_list,
            'answer': answer_list
        }
        df = pd.DataFrame(data)

        # 检查并创建导出路径
        if not os.path.exists(export_path):
            os.makedirs(export_path)

        # 导出到 Excel 文件
        file_path = os.path.join(export_path, f'{name}.xlsx')
        df.to_excel(file_path, index=False)

        # 打印生成信息
        row_count = len(df)
        logger.info(f'Excel 文件 {name} 已成功生成, 共有 {row_count} 行')

    def export_classify(self, app_id, name, export_path):
        index_name = 'knowledge_gtapp_demo_%s' % self.knowledge_controller.default_local_emb_model_path
        query = {
            "_source": {
                "excludes": ["app_id", "vector"]
            },
            "size": 10000,
            "query": {
                "bool": {
                    "must": [
                        {
                            "term": {"app_id": app_id}
                        }
                    ]
                }
            }
        }
        response = self.es.search_data(index_name, query)
        question_list = [hit["_source"]["question"] for hit in response["hits"]["hits"]]

        data = {
            'app_id': [app_id] * len(question_list),
            'question': question_list
        }

        df = pd.DataFrame(data)

        if not os.path.exists(export_path):
            os.makedirs(export_path)

        file_path = os.path.join(export_path, f'{name}.xlsx')
        df.to_excel(file_path, index=False)
        row_count = len(df)
        logger.info(f'Excel 文件 {name} 已成功生成：{file_path}, 共有 {row_count} 行')

    # 导入分类器
    def import_classify(self, file_path):
        df = pd.read_excel(file_path, dtype=str)
        # logger.info(df)
        mapping = {
            "properties": {
                "question": {
                    "type": "keyword"
                },
                "answer": {
                    "type": "text"
                },
                "vector": {
                    "type": "dense_vector",
                    "dims": 1024,
                    "index": True
                }
            }
        }
        index_name = 'knowledge_gtapp_demo_%s' % self.knowledge_controller.default_local_emb_model_path
        for i in df.values:
            logger.info(f"{str(i[0])}, {i[1]}")
            emb_data = self.knowledge_controller.create_embeddings(i[1], self.corpid,
                                                              self.knowledge_controller.default_local_emb_model_path)
            if emb_data['code'] == 0:
                data = {
                    "app_id": str(i[0]),
                    "question": i[1],
                    "vector": emb_data['vector']
                }

                delete_query = {
                    "query": {
                        "term": {
                            "question": i[1]
                        }
                    }
                }
                del_res = self.es.delete_data(index_name, delete_query)
                self.es.insert_data(index_name, data, mapping)

    # def test_result(self, entry_id, file_path):
    #     df = pd.read_excel(file_path)
    #
    #     df['vector'] = ''  # Column 7: "vector"
    #     df['diff'] = ''  # Column 8: "diff"
    #
    #     # Iterate through the dataframe
    #     for index, row in df.iterrows():
    #         content_value = row['content']  # Assuming 4th column is named 'content'
    #
    #         # Apply the test function to the content
    #         app_id = self.classify_and_process_answers(content_value, entry_id)
    #         app_title = AppIdManager.get_title(app_id)
    #
    #         # Write the result in the "vector" column (7th column)
    #         df.at[index, 'vector'] = app_title
    #
    #         # Compare the "人工标签" (6th column) with "vector" (7th column)
    #         if row['人工标签'] != app_title:
    #         # if row['llm_result'] != app_title:
    #             df.at[index, 'diff'] = 'diff'  # Mark as "diff" if they are different
    #     # 保存更改后的 DataFrame 到文件
    #     df.to_excel(file_path, index=False)  # 保存回原文件路径
    #     logger.info(f"File updated and saved to {file_path}")

    @staticmethod
    def get_app_name_by_id(app_list, app_id):
        for app in app_list:
            if str(app.get("app_id")) == str(app_id):
                return app.get("app_name")
        return None

    @staticmethod
    def get_app_by_id(app_list, app_id):
        for app in app_list:
            if str(app.get("app_id")) == str(app_id):
                return app
        return None

    @staticmethod
    def get_app_type_by_id(app_list, app_id):
        for app in app_list:
            if str(app.get("app_id")) == str(app_id):
                return app.get("app_type")
        return None

    @staticmethod
    def gen_template(app_info):
        app_ids = app_info['children']
        tool_list = ""
        for app in app_ids:
            tool_list += f"  {app['app_id']}. {app['app_name']} {app['classify_prompt']}\n"

        template = f"""
            从以下工具中选择一个能解决用户问题的最合适工具。可用工具的列表及其功能描述：
            {tool_list}
            针对用户问题：{{user_query}}  
            请输入工具编号（例如，输入 `4942821944895475713` 选择 DashScopeRerank）
            请只输出工具编号（例如，输出 `4942821944895475713`），以选择工具。
            """
        return template

    # def get_response(self, messages):
    #     client = OpenAI(
    #         # 如果您没有配置环境变量，请在此处用您的API Key进行替换
    #         api_key=self.app_key,
    #         # 填写DashScope服务的base_url
    #         base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    #     )
    #     completion = client.chat.completions.create(
    #         model="qwen-plus",
    #         # model="111",
    #         messages=messages
    #     )
    #     return completion

    def invoke(self, message: str, app_info, controller) -> int:
        # response = self.get_response([{
        #     'role': 'user', 'content': self.template.format(user_query=message)
        # }])
        # return response.choices[0].message.content
        # model = MyQianWen(self.app_key, model='qwen-turbo', prompt=self.template.format(user_query=message))
        # model.receive(message)

        template = self.gen_template(app_info)
        result = controller.ai_app_chat(template.format(user_query=message),
                                        [], app_info['aigc_type_id'], app_info['model_path'], is_stream=False, model_id=app_info['aigc_model_id'])
        sel_model = -1
        for item, _ in result:
            if item:
                try:
                    sel_model = int(item)
                except Exception as e:
                    logger.exception(e)
                break
        return sel_model

# class AppIdManager:
#     not_hit_target_app_id = "4943372548806021121"
#     entry_ids = []
#     app_ids = []
#
#     @classmethod
#     def load_config(cls):
#         config_path = os.path.join(os.path.dirname(__file__), "configs/app_types.yaml")
#         with open(config_path, 'r', encoding='utf-8') as f:
#             config = yaml.safe_load(f)
#         cls.entry_ids = config['entry_ids']
#         cls.app_ids = config['app_ids']
#
#     @staticmethod
#     def get_app_ids(entry_app_id):
#         AppIdManager.load_config()
#         entry = next((e for e in AppIdManager.entry_ids if e["app_id"] == entry_app_id), None)
#         if entry is None:
#             return []
#
#         detail_app_ids = entry.get("detail_app_ids", [])
#         detail_apps = [
#             {
#                 "app_id": app["app_id"],
#                 "title": app["title"],
#             }
#             for app in AppIdManager.app_ids if app["id"] in detail_app_ids
#         ]
#
#         return detail_apps
#
#     @staticmethod
#     def get_app_by_id(id_):
#         AppIdManager.load_config()
#         return {str(app["id"]): app for app in AppIdManager.app_ids}.get(str(id_), None)
#
#     @staticmethod
#     def get_app_by_app_id(app_id):
#         AppIdManager.load_config()
#         return {app["app_id"]: app for app in AppIdManager.app_ids}.get(app_id, None)

if __name__ == "__main__":
    # input_text_list = ["你好","hello"]
    # corp_id = "wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ"
    # knowledge_controller = KnowledgeController(corp_id)
    # res = asyncio.run(knowledge_controller.create_BAAI_bge_embeddings_async(input_text_list))
    #
    # # 输出结果
    # logger.info(res)

    # import
    # c = ClassifyController('wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ')
    # directory_path = "/Users/<USER>/Documents/work/新分类增加/Book3.xlsx"
    # c.import_classify(directory_path)

    # # export
    # c = ClassifyController('wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ')
    # app_ids = AppIdManager.get_app_ids("4942821944895475713")
    # path = "/Users/<USER>/Documents/work/样本库汇总2.0"
    # for content in app_ids:
    #     c.export_classify(content['app_id'], content['title'], path)

    # export_answer_question
    # c = ClassifyController('wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ')
    # qa_id = "4942821944895475713"
    # path = "/Users/<USER>/Documents/work/qa"
    # c.export_answer_question(qa_id, 'qa', path)

    # exam
    # c = ClassifyController('wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ')
    # c.test_result("4942821944895475713", "/Users/<USER>/Documents/work/分类pending/untitled folder/result_with_llm72b_diff.xlsx")

    # llm
    # c = ClassifyController('wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ')
    #
    # app_info = {
    #     "aigc_type_id": "tongyiqianwen",
    #     "model_path": "qwen1.5-7b-chat",
    # }
    # response = c.invoke("国投员工的基本信息？", app_info)
    # logger.info(response)

    # test
    # entry_id = '4942821944895475713';
    # c = ClassifyController('wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ')
    # response = c.classify_and_process_answers_llm("中长期激励管理办法如何影响员工的工作动力？", entry_id)
    # logger.info(response)
    #
    # static
    # res1 = AppIdManager.get_app_by_id("1")
    # res2 = AppIdManager.get_app_by_id("111")
    # res3 = AppIdManager.get_app_by_app_id("4942802632512114689")
    # res4 = AppIdManager.get_app_by_app_id("111")


    # corp_id = "wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ"
    # knowledge_controller = KnowledgeController(corp_id)
    # knowledge_id = 4968818164507873281
    # doc_list = knowledge_controller.get_knowledge_doc_list(knowledge_id, {'is_all': 'all'})
    # doc_id = doc_list['data_list'][0]['document_id']
    #
    # args = {
    #     'page_no': 1,
    #     'page_size': 5,
    #     'document_id': doc_id,
    #     'status': 1,
    #     'key_word': ''
    # }
    # data = knowledge_controller.get_knowledge_doc_chunk_list(doc_id, args)
    # # print(data)
    # chunks = data['data_list']


    # corp_id = "wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ"
    # knowledge_controller = KnowledgeController(corp_id)
    # knowledge_id = 4968818164507873281
    # doc_list = knowledge_controller.get_knowledge_doc_list(knowledge_id, {'is_all': 'all'})
    # doc_id = doc_list['data_list'][0]['document_id']
    #
    # args = {
    #     'page_no': 1,
    #     'page_size': 1,
    #     'document_id': doc_id,
    #     'status': 1,
    #     'key_word': ''
    # }
    # data = knowledge_controller.get_knowledge_doc_chunk_list(doc_id, args)
    # chunks = data['data_list']

    # corp_id = "wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ"
    # knowledge_controller = KnowledgeController(corp_id)
    # # knowledge_id = 4966273701889708033
    # knowledge_id = 4968818164507873281
    # CHUNKS_LIMIT = 200  # 定义常量 CHUNKS_LIMIT
    # CHUNK_CHAR_MAX = 20  # 定义最大字符数限制
    #
    # doc_list = knowledge_controller.get_knowledge_doc_list(knowledge_id, {'is_all': 'all'})
    #
    # total_count = 0
    # collected_chunks = {}
    # all_chunks = []  # 用于收集所有 chunks 的列表
    #
    # for doc in doc_list['data_list']:
    #     doc_id = doc['document_id']  # 提取 doc_id
    #     doc_name = doc['doc_name']  # 提取 doc_name
    #
    #     args = {
    #         'page_no': 1,
    #         'page_size': CHUNKS_LIMIT + 1,  # 使用足够大的 page_size
    #         'document_id': doc_id,
    #         'status': 1,
    #         'key_word': ''
    #     }
    #
    #     data = knowledge_controller.get_knowledge_doc_chunk_list(doc_id, args)
    #
    #     chunks = data['data_list']
    #     current_count = data['total']
    #
    #     for chunk in chunks:
    #         if chunk['character_count'] > CHUNK_CHAR_MAX:
    #             raise GraphRagException(
    #                 message="A chunk exceeds the maximum allowed character count.",
    #                 error_code=400,
    #                 details={"knowledge_id": knowledge_id}
    #             )
    #
    #     total_count += current_count
    #
    #     if total_count >= CHUNKS_LIMIT:
    #         print(f"Reached CHUNKS_LIMIT: {CHUNKS_LIMIT}. Stopping.")
    #         break
    #
    #     print(f"Use doc_id: {doc_id}, current_count: {current_count}, total_count: {total_count}")
    #     collected_chunks[doc_id] = {
    #         'doc_name': doc_name,
    #         'count': current_count
    #     }
    #
    #     all_chunks.extend(chunks)
    #
    # collected_chunks_json = json.dumps(collected_chunks, ensure_ascii=False)
    # print("Collected chunks for DB storage:")
    # print(collected_chunks_json)
    #
    # print("Collected all chunks:")
    # print(all_chunks)

    print("***")