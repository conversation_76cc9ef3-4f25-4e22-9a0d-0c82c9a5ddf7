import os
from settings import BASE_DIR
from controller import BaseController
from modeler.mysql.knowledge_orm import KnowledgeOrm
from utils.tools import now_date_str, upload_file
from puppet.es_sdk import EsSdkPool
from controller.knowledge_controller import KnowledgeController
from lib_func.logger import logger

class SBFController(BaseController):
    """
    存储和批处理文件--->知识文档
    """
    BasePath = '/upload/kbcf'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.knowledge_orm = KnowledgeOrm(self.corpid)
        self.es = EsSdkPool(self.corpid)
        self.kl_controller = KnowledgeController(self.corpid)

    # 文件存储
    def save_web_crawler_file(self, knowledge_id, files):
        now_date = now_date_str()
        save_path = f'{self.BasePath}/{now_date}/{self.corpid or "zsrz"}/{knowledge_id}'
        res = dict(succ=[], err=[])
        for file in files:
            try:
                res['succ'].append(upload_file(file, save_path)['filename'])
            except:
                res['err'].append(file.filename)
        return res

    # 文件检索
    def read_files(self, date_str):
        data_map = dict()
        for root_path, _, files in os.walk(f'{BASE_DIR}{self.BasePath}/{date_str}/{self.corpid}'):
            root_path += '' if root_path[-1] == '/' else '/'
            if len(files):
                dirt_list = [x for x in root_path.split('/') if x]
                if dirt_list:
                    kl_id = dirt_list[-1]
                else:
                    continue
                for file_name in files:
                    data_map.setdefault(kl_id, []).append(root_path + file_name)
        return data_map

    # 知识库文档切片
    def cut_doc_index(self, knowledge_id, docs):
        knowledge_info = self.knowledge_orm.get_knowledge_detail(knowledge_id)
        model_path = knowledge_info['model_path'] if knowledge_info and knowledge_info['model_path'] else 'embedding-v1'
        dims = knowledge_info['dims'] if knowledge_info and knowledge_info['dims'] else 1024
        emb_mapping = {
            "properties": {
                "chunk_id": {
                    "type": "keyword"
                },
                "doc_id": {
                    "type": "keyword"
                },
                "knowledge_id": {
                    "type": "keyword"
                },
                "content_id": {
                    "type": "keyword"
                },
                "description": {
                    "type": "text"
                },
                "character_count": {
                    "type": "integer"
                },
                "hit_count": {
                    "type": "integer"
                },
                "tokens": {
                    "type": "integer"
                },
                "status": {
                    "type": "boolean"
                },
                "vector": {
                    "type": "dense_vector",
                    "dims": dims,
                    "index": True
                },
                "delete_flag": {
                    "type": "boolean"
                }
            }
        }
        index = 1
        err_list = list()
        for doc in docs:
            try:
                res = self.kl_controller.document_splitter_v2_batch(doc['doc_url'], 500, 100)
                if res['code'] == 0 and len(res['data']) > 0:
                    tokens = 0
                    for i, chunk in enumerate(res['data']):
                        chunk_id = self.kl_controller.add_knowledge_doc_chunk(doc['doc_id'], chunk['content'],
                                                                              chunk['character_count'], 'TODO')
                        emb_data = self.kl_controller.create_embeddings(chunk['content'], str(doc['doc_id']), model_path)
                        if emb_data['code'] == 0:
                            data = {
                                "chunk_id": chunk_id,
                                "doc_id": doc['doc_id'],
                                "knowledge_id": knowledge_id,
                                "content_id": '',
                                "description": chunk['content'],
                                "character_count": len(chunk['content']),
                                "hit_count": 0,
                                "tokens": emb_data['total_tokens'],
                                "status": True,
                                "vector": emb_data['vector'],
                                "delete_flag": False
                            }
                            index_name = 'knowledge_%s' % model_path

                            self.es.insert_data(index_name, data, emb_mapping)
                            self.kl_controller.update_knowledge_doc_chunk(chunk_id, {'tokens': emb_data['total_tokens'],
                                                                                     'result': 'FINISHED'})
                            tokens += emb_data['total_tokens']
                    self.kl_controller.update_knowledge_doc(doc['doc_id'],
                                                            {'status': 'FINISHED', 'chunk_size': len(res['data']),
                                                             'result': 'FINISHED', 'tokens': tokens})

                index += 1
            except Exception as err:
                err_list.append(doc['doc_id'])
                continue
        return err_list

    # 文档摘要
    def doc_abstract_batch(self, k_id, docs):
        mapping = {
            "properties": {
                "doc_id": {
                    "type": "keyword"
                },
                "knowledge_id": {
                    "type": "keyword"
                },
                "description": {
                    "type": "text"
                },
                "character_count": {
                    "type": "integer"
                },
                "hit_count": {
                    "type": "integer"
                },
                "tokens": {
                    "type": "integer"
                },
                "vector": {
                    "type": "dense_vector",
                    "dims": 1024,
                    "index": True
                },
                "delete_flag": {
                    "type": "boolean"
                }
            }
        }
        index = 1
        for doc in docs:
            try:
                # 文档摘要
                abstract = doc['doc_name'].split('.')[0]
                emb_data = self.kl_controller.create_embeddings(abstract, str(doc['doc_id']),
                                                                self.kl_controller.default_local_emb_model_path)
                if emb_data['code'] == 0:
                    data = {
                        "doc_id": doc['doc_id'],
                        "knowledge_id": k_id,
                        "description": abstract,
                        "character_count": len(abstract),
                        "hit_count": 0,
                        "tokens": emb_data['total_tokens'],
                        "vector": emb_data['vector'],
                        "delete_flag": False
                    }
                    index_name = 'knowledge_doc_abstract_%s' % self.kl_controller.default_local_emb_model_path
                    self.kl_controller.es.insert_data(index_name, data, mapping)
                    self.kl_controller.update_knowledge_doc(doc['doc_id'], {'abstract': abstract})
                    logger.info('====摘要完成====')
            except Exception as e:
                logger.error(e)
            index += 1
