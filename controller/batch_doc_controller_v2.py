# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: batch_doc_controller.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 10月 05, 2024
# ---
import re

import pandas as pd
import json
import os
import urllib.parse
import uuid

from controller.app_controller import AppController
from lib_func.const_map import QIANWEN_CODE
from lib_func.logger import logger
from utils.oss_utils import OSSUtil
from controller import BaseController
from modeler.mysql.knowledge_orm import KnowledgeOrm
from controller.knowledge_controller import KnowledgeController
from apps.ai import task


class BatchDocController(BaseController):
    """
    人员信息批处理
    """

    def __init__(self, corpid):
        super().__init__(corpid)
        self.knowledge_orm = KnowledgeOrm(corpid)

    @staticmethod
    def list_files_os(directory):
        try:
            # 获取指定目录中的文件和子目录列表
            files_and_dirs = os.listdir(directory)

            # 过滤出仅文件（排除子目录）
            files = [f for f in files_and_dirs if os.path.isfile(os.path.join(directory, f))]

            return files
        except FileNotFoundError:
            logger.error(f"目录 {directory} 不存在")
            return []
        except PermissionError:
            logger.error(f"没有权限访问目录 {directory}")
            return []

    def upload_batch_doc(self, doc_list, directory_path, knowledge_id):

        # 上传图片到阿里云 OSS
        oss_util = OSSUtil(self.corpid)

        # knowledge_id = '4940113662934257665'

        index = 1
        for doc_name in doc_list:
            logger.info(f'当前处理第 {index}，总共 {len(doc_list)} 条数据, doc_name: {doc_name}')
            if self.knowledge_orm.check_knowledge_doc(knowledge_id, doc_name):
                logger.info('=已存在=')
            else:
                # oss_path = f"knowledge/{knowledge_id}/{uuid.uuid4().hex}.txt"
                # res = oss_util.put_object_from_file(oss_path, os.path.join(directory_path, doc_name))
                # oss_url = urllib.parse.unquote(res.resp.response.url)
                # logger.info(oss_url)
                doc_type_dict = {
                    'txt': 'text/plain',
                    'pdf': 'application/pdf',
                    'doc': 'application/msword',
                    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                }
                doc_type = doc_type_dict[doc_name.split('.')[-1]]
                oss_url = None
                self.knowledge_orm.add_knowledge_doc(knowledge_id, doc_name, doc_type, oss_url, 0, "TODO")
                # break
            index += 1

    def get_doc_list(self, knowledge_id, args, d_path):
        docs = self.knowledge_orm.get_knowledge_doc_list(knowledge_id, args)

        total = len(docs['data_list'])
        knowledge_controller = KnowledgeController(self.corpid)
        es = knowledge_controller.es
        knowledge_info = knowledge_controller.get_knowledge_detail(knowledge_id)
        model_path = knowledge_info['model_path'] if knowledge_info and knowledge_info['model_path'] else 'embedding-v1'
        dims = knowledge_info['dims'] if knowledge_info and knowledge_info['dims'] else 1024
        emb_mapping = {
            "properties": {
                "chunk_id": {
                    "type": "keyword"
                },
                "doc_id": {
                    "type": "keyword"
                },
                "knowledge_id": {
                    "type": "keyword"
                },
                "content_id": {
                    "type": "keyword"
                },
                "description": {
                    "type": "text"
                },
                "character_count": {
                    "type": "integer"
                },
                "hit_count": {
                    "type": "integer"
                },
                "tokens": {
                    "type": "integer"
                },
                "status": {
                    "type": "boolean"
                },
                "vector": {
                    "type": "dense_vector",
                    "dims": dims,
                    "index": True
                },
                "delete_flag": {
                    "type": "boolean"
                }
            }
        }
        index = 1
        for doc in docs['data_list']:
            # logger.info(doc)
            logger.info(f'当前处理第 {index}，总共 {total} 条数据, doc_id: {doc["doc_id"]}')
            path = d_path + '/' + doc['doc_name']
            res = knowledge_controller.document_splitter_v2_batch(path, 1000, 200)

            if res['code'] == 0 and len(res['data']) > 0:
                for i, chunk in enumerate(res['data']):
                    chunk_id = knowledge_controller.add_knowledge_doc_chunk(
                        doc['doc_id'], chunk['content'], chunk['character_count'], 'TODO'
                    )

                    emb_data = knowledge_controller.create_embeddings(chunk['content'], str(doc['doc_id']), model_path)
                    if emb_data['code'] == 0:
                        data = {
                            "chunk_id": chunk_id,
                            "doc_id": doc['doc_id'],
                            "knowledge_id": knowledge_id,
                            "content_id": '',
                            "description": chunk['content'],
                            "character_count": len(chunk['content']),
                            "hit_count": 0,
                            "tokens": emb_data['total_tokens'],
                            "status": True,
                            "vector": emb_data['vector'],
                            "delete_flag": False
                        }
                        index_name = 'knowledge_%s' % model_path
                        delete_query = {
                            "query": {
                                "term": {
                                    "doc_id": doc['doc_id']
                                }
                            }
                        }
                        # logger.info(index_name)
                        # es.delete_data(index_name, delete_query)
                        es.insert_data(index_name, data, emb_mapping)
                        knowledge_controller.update_knowledge_doc_chunk(chunk_id,
                                                                        {'tokens': emb_data['total_tokens'], 'result': 'FINISHED'})

                knowledge_controller.update_knowledge_doc(doc['doc_id'],
                                                          {'status': 'FINISHED', 'chunk_size': len(res['data']), 'result': 'FINISHED'})

            index += 1

    def doc_abstract_batch(self, k_id, args):
        knowledge_controller = KnowledgeController(self.corpid)
        docs = knowledge_controller.get_knowledge_doc_list(k_id, args)
        # logger.info(docs['data_list'])
        mapping = {
            "properties": {
                "doc_id": {
                    "type": "keyword"
                },
                "knowledge_id": {
                    "type": "keyword"
                },
                "description": {
                    "type": "text"
                },
                "character_count": {
                    "type": "integer"
                },
                "hit_count": {
                    "type": "integer"
                },
                "tokens": {
                    "type": "integer"
                },
                "vector": {
                    "type": "dense_vector",
                    "dims": 1024,
                    "index": True
                },
                "delete_flag": {
                    "type": "boolean"
                }
            }
        }
        index = 1
        total = len(docs['data_list'])
        logger.info(total)
        new_list = []
        for doc in docs['data_list']:
            # logger.info(f'当前处理第 {index}，总共 {total} 条数据, doc_id: {doc["doc_id"]}')
            # question = doc['doc_name'].split('--')
            # logger.info(question[-1:][0])
            # question = self.remove_prefix_before_date(doc['doc_name'])
            # new_list.append(question)
            try:
                # 文档摘要
                abstract = doc['doc_name'].split('.')[0]

                emb_data = knowledge_controller.create_embeddings(abstract, str(doc['doc_id']), knowledge_controller.default_local_emb_model_path)
                if emb_data['code'] == 0:
                    data = {
                        "doc_id": doc['doc_id'],
                        "knowledge_id": k_id,
                        "description": abstract,
                        "character_count": len(abstract),
                        "hit_count": 0,
                        "tokens": emb_data['total_tokens'],
                        "vector": emb_data['vector'],
                        "delete_flag": False
                    }
                    index_name = 'knowledge_doc_abstract_%s' % knowledge_controller.default_local_emb_model_path
                    knowledge_controller.es.insert_data(index_name, data, mapping)
                    knowledge_controller.update_knowledge_doc(doc['doc_id'],{'abstract': abstract})
                    logger.info('====摘要完成====')
            except Exception as e:
                logger.error(e)
            index += 1

    @staticmethod
    def remove_prefix_before_date(filename):
        # 匹配日期格式 YYYY-MM-DD
        pattern = r'\d{4}-\d{2}-\d{2}'

        # 使用正则表达式查找日期
        match = re.search(pattern, filename)

        if match:
            # 提取日期后的部分
            return filename[match.end() - 10:]  # +2 是为了跳过两个破折号
        else:
            return filename


if __name__ == "__main__":
    # 示例使用
    c = BatchDocController('wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ')
    directory_path = "/Users/<USER>/Downloads/知识库材料1012整理v2/内部历史发文/讲话稿"
    files = c.list_files_os(directory_path)
    # k_id = '4942745754495094785'  # 内部历史发文知识库
    # k_id = '4944172413131689985'  # 国投志
    k_id = '4944535612356038657'  # 科研助手
    # print(len(files))
    # c.upload_batch_doc(files, directory_path, k_id)
    # 批量切片 & 向量化 'bgn_id': 4940312149969866753, 'end_id': 4940351609780572161
    p = {'status': 'TODO', 'is_all': 'all', 'doc_name': '付刚峰'}
    # doc_list = c.get_doc_list(k_id, p, directory_path)
    # print(doc_list)
    #
    p = {'is_all': 'all', 'is_abstract': 1}
    c.doc_abstract_batch(k_id, p)
