from controller import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Annotated, Literal, List
import datetime
from puppet.cache import redis_pool
from puppet.es_sdk import EsSdkPool
from dataclasses import dataclass
from controller.knowledge_controller import KnowledgeController
from lib_func.logger import logger

app_desc_mapping = {
    "properties":{
        "description": {
            "type": "text",
            "analyzer": "ik_max",
        },
        "name": {
            "type": "text",
            "analyzer": "ik_max",
        },
        "classify_prompt": {
            "type": "text",
            "analyzer": "ik_max"
        },
        "app_id": {
            "type": "keyword",  
        },

        "add_time": {
            "type": "date",
            "format": "yyyy-MM-dd HH:mm:ss"
        },
        "update_time": {
            "type": "date",
            "format": "yyyy-MM-dd HH:mm:ss"
        },
        "scope": {
            "type": "integer"
        },
        "tokens": {
            "type": "integer"
        },
        "vector": {
            "type": "dense_vector",
            "dims": 1024,
            "index": True,
            "similarity": "cosine",
        },
    }
}

knowledge_desc_mapping = {
    "properties":{
        "description": {
            "type": "text",
            "analyzer": "ik_max",
        },
        "name": {
            "type": "text",
            "analyzer": "ik_max",
        },
        "knowledge_id": {
            "type": "keyword",  
        },
        "add_time": {
            "type": "date",
            "format": "yyyy-MM-dd HH:mm:ss"
        },
        "update_time": {
            "type": "date",
            "format": "yyyy-MM-dd HH:mm:ss"
        },
        "scope": {
            "type": "integer"
        },
        "tokens": {
            "type": "integer"
        },
        "vector": {
            "type": "dense_vector",
            "dims": 1024,
            "index": True,
            "similarity": "cosine",
        },
    }
}


@dataclass
class SaveAppParams:
    name: str
    description: str
    classify_prompt: str
    app_id: Annotated[str, "应用id"]
    scope: int


@dataclass
class SaveKnowledgeParams:
    name: str
    description: str
    knowledge_id: Annotated[str, "知识库id"]
    scope: int


@dataclass
class VectorResult:
    vector: List[float]
    total_tokens: int


class EsOptController(BaseController):
    def __init__(self, *args):
        super(EsOptController, self).__init__(*args)
        self.es = EsSdkPool(self.corpid)
        self.app_index_name = "app_desc"
        self.knowledge_index_name = "knowledge_desc"
        self.model_path = "bge-large-zh-v1.5"
        self.kn_controller = KnowledgeController(self.corpid)
        # 阈值
        self.top_k = self._get_params('KN_TOP_K')
        self.min_score = self._get_params('KN_MIN_SCORE')

    def _get_params(self, var_code):
        dict_redis_client = redis_pool.use(redis_pool.dict_pool)
        system_variable_key = 'system_variable_' + self.corpid
        var_value = dict_redis_client.hget(system_variable_key, var_code)
        return var_value

    def save_description(self, info: SaveAppParams | SaveKnowledgeParams):
        if isinstance(info, SaveAppParams):
            self.save_app_description(info)
        elif isinstance(info, SaveKnowledgeParams):
            self.save_knowledge_description(info)

    def create_description_vector(self, description: str, type: Annotated[str, Literal["app", "knowledge"]], id: str, model_path:str)-> VectorResult:
        res = KnowledgeController(self.corpid).create_embeddings(input_text=description, user_id=id,model_path=model_path)
        name = "app" if type == "app" else "knowledge"
        logger.info(f"save_description_{name}: {res}")
        if res['code'] != 0:
            raise Exception(f"{name} 详情向量化失败：{res['error_msg']}")
        return VectorResult(vector=res.get('vector'), total_tokens=res.get('total_tokens'))
    
    def save_app_description(self, info: SaveAppParams):
        model_path = self.model_path
        description = f"{info.name} {info.description} {info.classify_prompt}"
        res = self.create_description_vector(description, type="app", id=info.app_id, model_path=model_path)

        data = {
            "app_id": info.app_id,
            "name": info.name,
            "description": info.description,
            "classify_prompt": info.classify_prompt,
            "scope": info.scope,
            "vector": res.vector,
            "token": res.total_tokens,
            "update_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "add_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        }
        index_name = f"{self.app_index_name}_{model_path}"
        self.es.insert_data(index_name, data)
        self.es.refresh_index(index_name)

    def save_knowledge_description(self, info: SaveKnowledgeParams):
        
        description = f"{info.name} {info.description}"
        model_path = self.model_path
        res = self.create_description_vector(description, type="knowledge", id=info.knowledge_id, model_path=model_path)

        data = {
            "knowledge_id": info.knowledge_id,
            "name": info.name,
            "description": info.description,
            "scope": info.scope,
            "vector": res.vector,
            "token": res.total_tokens,
            "update_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "add_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        }
        index_name = f"{self.knowledge_index_name}_{model_path}"
        self.es.insert_data(index_name, data)
        self.es.refresh_index(index_name=index_name)

    def update_description(self, update: SaveAppParams | SaveKnowledgeParams):
        if isinstance(update, SaveAppParams):
            self.update_app_description(update)
        elif isinstance(update, SaveKnowledgeParams):
            self.update_knowledge_description(update)

    def update_app_description(self, update: SaveAppParams):
        description = f"{update.name} {update.description} {update.classify_prompt}"
        model_path = self.model_path
        res = self.create_description_vector(description=description, type="app", model_path=model_path, id=update.app_id)

        update_query = {
            "query": {
                "term": {
                    "app_id": update.app_id
                }
            },
            "script": {
                "source": """
                    ctx._source.name = params.name;
                    ctx._source.description = params.description;
                    ctx._source.tokens = params.tokens;
                    ctx._source.classify_prompt = params.classify_prompt;
                    ctx._source.vector = params.vector;
                    ctx._source.scope = params.scope;
                    ctx._source.update_time = params.update_time;
                """,
                "params": {
                    "name": update.name,
                    "description": update.description,
                    "classify_prompt": update.classify_prompt,
                    "vector": res.vector,
                    "token": res.total_tokens,
                    "scope": update.scope,
                    "update_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            }
        }
        index_name = f"{self.app_index_name}_{model_path}"
        self.es.update_data(index_name,update_query)
        self.es.refresh_index(index_name)

    def update_knowledge_description(self, update: SaveAppParams):
        description = f"{update.name} {update.description}"
        model_path = self.model_path
        res = self.create_description_vector(description, type="knowledge", id=update.knowledge_id, model_path=model_path)
        update_query = {
            "query": {
                "term": {
                    "knowledge_id": update.knowledge_id
                }
            },
            "script": {
                "source": """
                    ctx._source.name = params.name;
                    ctx._source.description = params.description;
                    ctx._source.tokens = params.tokens;
                    ctx._source.vector = params.vector;
                    ctx._source.scope = params.scope;
                    ctx._source.update_time = params.update_time;
                """,
                "params": {
                    "name": update.name,
                    "description": update.description,
                    "vector": res.vector,
                    "token": res.total_tokens,
                    "scope": update.scope,
                    "update_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            }
        }
        index_name = f"{self.knowledge_index_name}_{model_path}"
        self.es.update_data(f"{self.knowledge_index_name}_{model_path}", update_query)
        self.es.refresh_index(index_name)

    def delete_knowledge_description(self, knowledge_id: str)-> bool:
        delete_query = {
            "query": {
                "term": {
                    "knowledge_id": knowledge_id
                }
            }
        }
        return self.es.delete_data(f"{self.knowledge_index_name}_{self.model_path}", delete_query)

    def delete_app_description(self, app_id: str)-> bool:
        delete_query = {
            "query": {
                "term": {
                    "app_id": app_id
                }
            }
        }
        return self.es.delete_data(f"{self.app_index_name}_{self.model_path}", delete_query)

    def recommend_app_list(self, input_text, scope=0, knowledge_ids=None):
        return self._query_similar_opt(self.app_index_name, input_text, scope, knowledge_ids)

    def recommend_knowledge_list(self, input_text, scope=0, knowledge_ids=None):
        return self._query_similar_opt(self.knowledge_index_name, input_text, scope, knowledge_ids)

    def _query_similar_opt(self, index_name, input_text, scope, knowledge_ids, is_search=True):
        model_path = self.model_path
        emb_data = self.kn_controller.create_embeddings(input_text, self.corpid, model_path, is_search=is_search)
        if emb_data.get('code') == '-1':
            return []
        vector = emb_data['vector']
        if index_name == 'app_desc':
            includes = ["app_id", "description", "name", "classify_prompt"]
        else:
            includes = ["knowledge_id", "description", "name"]
        bool_must = [{"term": {"scope": {"value": scope}}}]
        if knowledge_ids:
            bool_must.append({"terms": {"knowledge_id": knowledge_ids}})
        body_dict = {
            "_source": {
                "includes": includes
            },
            "query":
                {"script_score": {
                    "query": {
                        "bool": {
                            "must": bool_must
                        }
                    },
                    "min_score": self.min_score,
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
                        "params": {
                            "query_vector": vector
                        }
                    }
                }
                },
            "size": self.top_k
        }
        try:
            index_name = index_name + '_' + self.model_path
            hits = self.es.search_data(index_name, body_dict)['hits']['hits']
            if hits:
                result = [hit['_source'] for hit in hits if hit.get('_source')]
                return result
            return []
        except Exception as e:
            return []


if __name__ == "__main__":
    es_opt_controller = EsOptController("wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ")
    # app_info = SaveAppParams(app_id="5004997108978159617", description="", classify_prompt="", scope=0, name="jky-要是要闻-应用-修改")
    # es_opt_controller.update_description(app_info)
    # es_opt_controller.save_description(app_info)
    # es_opt_controller.delete_app_description(app_id="5004997108978159617")

    knowledge_info = SaveKnowledgeParams(knowledge_id="5004995159234973697", description="测试知识库123", scope=0,
                                         name="测试知识库123")
    # es_opt_controller.save_description(knowledge_info)
    # es_opt_controller.update_description(knowledge_info)
    # es_opt_controller.delete_knowledge_description(knowledge_id="5004995159234973697")