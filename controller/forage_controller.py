import json
import random
import re
import os
import time
import ast
from datetime import datetime
from io import BytesIO
import tempfile

from langchain_experimental.graph_transformers.llm import system_prompt

from . import BaseController
from modeler.mysql.qa_orm import ForageOrm
from lib_func.const_map import StatusMap
import pandas as pd
from module import minio_util
from settings import KNOWLEDGE_OSS_BUCKET_NAME, DELETE_FLAG_TRUE
from utils.tools import now_datetime_str,get_name_from_url, get_bytes_from_url, now_datetime_str_format
from puppet.mongo_sdk import MongodbPool
from apps.ai.app import AppController
from urllib.parse import urlparse
from controller.knowledge_controller import KnowledgeController
from lib_func.logger import task_logger, logger
from utils.tools import get_snowflake_id

class ForageController(BaseController):

    def __init__(self, *args):
        super().__init__(*args)
        self.model = ForageOrm(self.corpid)
        self.uid = self.user_info.get('tp_user_id')

        self.forage_chunk = 'forage_chunk'
        self.mongo = MongodbPool(self.corpid)
        self.structured_db_name = 'structured_data'

    def get_forage_clean_result(self, args):
        return self.model.get_forage_clean_result(args)

    def get_forage_clean_result_detail(self, args):
        return self.model.get_forage_clean_result_detail(args)

    def get_forage_evaluation_record(self, args, re_evaluate=1):
        return self.model.get_forage_evaluation_record(args, re_evaluate)
    
    def update_forage_evaluation_record(self, query, update):
        return self.model.update_forage_evaluation_record(query, update)
    
    def add_forage_evaluation_record(self, data):
        return self.model.add_forage_evaluation_record(data)

    def add_forage_combine_record(self, data):
        return self.model.add_forage_combine_record(data)

    def update_forage_chunk(self, query, update):
        return self.mongo.db[self.forage_chunk].update_one(query, {"$set": update}, upsert=True)

    def add_forage_chunk(self, data):
        return self.mongo.db[self.forage_chunk].insert_one(data)

    def bulk_forage_chunk(self, datas):
        return self.mongo.db[self.forage_chunk].insert_many(datas)

    def del_forage_chunk(self, query):
        self.mongo.db[self.forage_chunk].delete_many(query)
        return True

    def get_forage_chunk(self, query):
        datas = self.mongo.db[self.forage_chunk].find(query)
        return datas

    def add_or_up_forage(self, params):
        forage_id = params.pop('forage_id')
        params['tp_user_id'] = self.uid
        if forage_id:
            self.model.up_forage({'forage_id': forage_id}, params)
        else:
            forage_id = self.model.add_forage(params)
        return forage_id

    def del_forage(self, forage_id):
        return self.model.up_forage({'forage_id': forage_id}, {'delete_flag': DELETE_FLAG_TRUE})

    def page_forage(self, *args):
        res = self.model.page_forage(*args)
        for item in res['data_list']:
            if item['is_structured'] == 1:
                item['qa_count'] = self.get_mongo_structured_len_data(item['forage_id'])
        return res

    def forage_info(self, forage_id):
        return self.model.page_forage(forage_id)['data_list'][0]
    
    def add_files(self, forage_id, files,upload_type=0):
        ids = list()

        for one in files:
            file_name = get_name_from_url(one) or '文件'
            ids.append(self.model.add_file({'name': file_name, 'url': one, 'stage': 'add', 'status': 2, 'tp_user_id': self.uid,'upload_type':upload_type}))

        if ids:
            self.model.bilk_add_forage_rel_file(forage_id, ids)
        return ids

    def up_file_data(self, qp, up):
        return self.model.up_file_data(qp, up)

    def page_forage_file(self, forage_id=None, file_ids=None, name=None, status=None, stage=None, page_size=10, page_no=1,upload_type=0):
        if forage_id:
            file_ids = self.model.get_forage_rel_file_ids(forage_id, file_ids)
            if not file_ids:
                return {'data_list': [], 'page': page_no, 'total': 0}
        return self.model.page_forage_file(file_ids, name, status, stage, page_size, page_no,upload_type)

    def forage_file_detail(self, file_id):
        datas = self.model.page_forage_file([file_id])['data_list']
        if datas:
            return datas[0]
        else:
            raise ValueError(StatusMap['search_failed'])

    def del_file(self, file_ids, sync_del_qa):
        for file_id in file_ids:
            self.model.up_file_data({'file_id': file_id}, {'delete_flag': DELETE_FLAG_TRUE})
            self.model.del_forage_rel_file(file_id=file_id)
            if sync_del_qa:
                self.model.del_forage_qa(file_id=file_id)
        return True

    def add_forage_qa(self, forage_id, file_id, kvs, file_url, args=None):
        datas = list()
        if args is None:
            args = {}
        qa_type = args.get('qa_type', 0)
        print(f"qa_type: {qa_type}")
        if file_id:
            source = 'file'
        elif kvs:
            source = 'add'
        else:
            source = 'load'
        if kvs:
            for one in kvs:
                datas.append({'question': one.get('question', ''), 'answer': one.get('answer', ''), 'source': source, 'file_id': file_id, 'forage_id': forage_id,'qa_type':qa_type})
        elif file_url:
            try:
                df = pd.read_excel(get_bytes_from_url(file_url)).fillna('')
                for i in df.values:
                    if str(i[0]) and str(i[1]):
                        datas.append({
                            'question': i[0],
                            'answer': str(i[1]),
                            'source': source,
                            'file_id': file_id,
                            'forage_id': forage_id,
                            'qa_type':qa_type
                        })
            except Exception as err:
                print(err)
                raise ValueError(StatusMap['do_action_failed'])
        if datas:
            return self.model.bulk_add_forage_qa(datas)
        return True
    def add_forage_qas_bulk(self, qa_list):
        return self.model.bulk_add_forage_qa(qa_list)

    def add_forage_cleaning_record(self, params):
        return self.model.add_forage_cleaning_record(params)

    def del_forage_qa(self, ids):
        return self.model.bulk_up_forage_qa(ids, {'delete_flag': DELETE_FLAG_TRUE})

    def up_forage_qa(self, qp, up):
        return self.model.up_forage_qa(qp, up)

    def page_forage_qa(self, **kwargs):
        return self.model.page_forage_qa(**kwargs)

    def get_random_qa(self,forage_id,random_percent=1,qa_ids=[]):
        return self.model.get_random_qa(forage_id,random_percent,qa_ids)

    def build_down_qa_file(self, forage_id, file_id, file_type):
        datas = self.model.all_forage_qa(forage_id, file_id)
        if not datas:
            return ""
        else:
            forage_name = datas[0]['forage_name']
            now_time = now_datetime_str()
            forage_qa_file = f'/model_forage/{forage_name}_{forage_id}/{now_time}_{file_type}.json'

            file_type = file_type.lower()
            ret = list()
            if file_type == 'ShareGPT'.lower():
                for one in datas:
                    ret.append({'from': 'human', 'value': one['question']})
                    ret.append({'from': 'gpt', 'value': one['answer']})

            elif file_type == 'Alpace'.lower():
                for one in datas:
                    ret.append({'instruction': one['question'], 'output': one['answer']})
            if not ret:
                return ""
            else:
                file_bytes = json.dumps(ret).encode()
                url = minio_util.upload_to_minio(KNOWLEDGE_OSS_BUCKET_NAME, forage_qa_file, file_bytes)
                return url

    def get_forage_evaluation(self, forage_id):
        return self.model.get_forage_evaluation(forage_id)
    
    def get_model_forage_info(self, forage_id):
        return self.model.get_model_forage_info(forage_id)
    
    def update_model_forage_info(self, forage_id, update):
        return self.model.update_model_forage_info(forage_id, update)
    
    def export_excel_file(self, forage_id):
        # 获取数据
        forage_info = self.forage_info(forage_id)
        forage_name = forage_info.get('name') or '未命名'
        obj = self.get_mongo_structured_all(forage_id)
        if not obj or not obj.get('data'):
            raise ValueError(StatusMap['no_export_data'])
        excel_file = f'/structured_data/{forage_name}_{forage_id}/{forage_name}_{get_snowflake_id()}.xlsx'

        # 获取表头和数据
        headers = obj.get('header', [])
        data = obj.get('data', [])
        
        if not headers or not data:
            raise ValueError(StatusMap['no_export_data'])
            
        # 创建DataFrame
        # 将header转换为列名映射
        try:
            column_mapping = {h['key']: h['title'] for h in headers}
            
            # 创建DataFrame并重命名列
            df = pd.DataFrame(data)
            df = df.rename(columns=column_mapping)
            
            # 使用tempfile创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
                # 将DataFrame写入临时文件
                df.to_excel(tmp_file.name, index=False, engine='openpyxl')
                
                # 上传到MinIO
                with open(tmp_file.name, 'rb') as file_data:
                    excel_url = minio_util.upload_to_minio(KNOWLEDGE_OSS_BUCKET_NAME, excel_file, file_data.read())
            
            return excel_url
            
        except Exception as e:
            task_logger.error(f"导出Excel失败: {str(e)}")
            raise ValueError(StatusMap['export_excel_failed'])
    
    def add_excel_file(self, forage_id, file_list, tp_user_id, corpid, tp_user_name):
        # 读取文件
        # 校验 # 将数据插入到mongodb中
        file_type = ['xlsx', 'xls','csv']
        
        for url in file_list:
            parsed_url = urlparse(url)
            path = parsed_url.path
            file_name = os.path.basename(path)
            file_extension = os.path.splitext(file_name)[1].lstrip('.')
            task_logger.info(f"forage_id: {forage_id}  开始上传文件 {file_name}")

            task_logger.info(f"forage_id: {forage_id}  验证文件格式是否为csv xls xlsx")
            if file_extension not in file_type:
                raise ValueError(StatusMap['do_action_failed'])
            # 读取csv文件
            try:
                file_path = KnowledgeController(self.corpid).handle_file_download(url)
            except Exception as e:
                task_logger.error(f"forage_id: {forage_id}  文件下载失败: {str(e)}")
                return {'code': 400, 'message': f'文件下载失败: {str(e)}'}
            task_logger.info("+" * 25)
            
            if file_extension in ['xlsx', 'xls']:
                # 检查Excel文件是否包含多个sheet
                excel_file = pd.ExcelFile(file_path)
                if len(excel_file.sheet_names) > 1:
                    task_logger.warning(f"forage_id: {forage_id}  文件包含多个sheet，默认只读取第一个sheet")
                    raise ValueError(StatusMap['excel_sheet_err'])
                df = excel_file.parse(excel_file.sheet_names[0])
                # 数据清洗，去除NaN内容
                df = df.fillna('')
                if df.isnull().any().any():
                    task_logger.error(f"forage_id: {forage_id}  文件可能包含合并单元格，不符合格式要求")
                    raise ValueError(StatusMap['excel_merged_cell_err'])
                # if df.duplicated().any().any():
                #     task_logger.error(f"forage_id: {forage_id}  文件可能包含数据透视表，不符合格式要求")
                #     raise ValueError(StatusMap['excel_pivot_table_err'])
            else:
                # CSV文件没有sheet的概念，直接读取
                df = pd.read_csv(file_path)
                # 数据清洗，去除NaN内容
                df = df.fillna('')
            
            # Check for headers and data
            if df.empty:
                raise ValueError(StatusMap['excel_data_err'])
            
            if all(pd.isna(df.columns)):
                raise ValueError(StatusMap['excel_header_err'])
            
            header, value_list = self.get_header_value_from_df(df)
            if not header:
                task_logger.error(f"forage_id: {forage_id} 提取 df 文件没有表头")
                continue

            # Insert data into MongoDB
            self.check_and_update_structured_by_forage_id(forage_id, header, value_list, tp_user_id, tp_user_name)
            

        return  {"forage_id": forage_id, 'header': header}
    
    def get_mongo_structured_forage(self, structured_data_id=None, forage_id=None, page_size=10, page_no=1):
        query = {}
        conditions = []
        if structured_data_id:
            conditions.append({'structured_data_id': structured_data_id})
        if forage_id:
            conditions.append({'forage_id': forage_id})
        if conditions:
            query = {'$or': conditions}
        document = self.mongo.db[self.structured_db_name].find_one(query)
        if not document or 'data' not in document:
            return {
                'header': [],
                'data': [],
                'total': 0,
                'page_no': page_no,
                'page_size': page_size
            }

        # Calculate the start and end index for pagination
        start_index = (page_no - 1) * page_size
        end_index = start_index + page_size

        # Slice the data array based on the calculated indices
        paginated_data = document['data'][start_index:end_index]

        return {
            'header': document.get('header', []),
            'data': paginated_data,
            'total': len(document['data']),
            'page_no': page_no,
            'page_size': page_size
        }

    def get_mongo_structured_all(self, forage_id):
        query = {'forage_id': forage_id}
        document = self.mongo.db[self.structured_db_name].find_one(query)

        header = document.get('header', []) if document else []
        data = []
        if document and 'data' in document:
            for item in document['data']:
                # Filter out the structured_data_id field
                filtered_item = {key: value for key, value in item.items() if key != 'structured_data_id' and key != 'add_time'}
                data.append(filtered_item)

        return {'header': header, 'data': data}
    
    def get_mongo_structured_len_data(self, forage_id):
        query = {'forage_id': str(forage_id)}
        document = self.mongo.db[self.structured_db_name].find_one(query)
        if not document or 'data' not in document:
            return 0
        return len(document['data'])

    def del_mongo_structured_data(self, structured_data_ids, forage_id):
        query = {'forage_id': forage_id}
        update = {'$pull': {'data': {'structured_data_id': {'$in': structured_data_ids}}}}
        return self.mongo.db[self.structured_db_name].update_one(query, update)


    def add_to_mongo_data_field(self, forage_id, new_data):
        """
        Add new data to the 'data' field of a document identified by forage_id.

        :param forage_id: The ID of the forage associated with the document.
        :param new_data: A list of dictionaries representing the new data to be added.
        :return: The result of the update operation.
        """

        query = {'forage_id': forage_id}
        update = {'$push': {'data': {'$each': new_data}}}
        return self.mongo.db[self.structured_db_name].update_one(query, update, upsert=True)
    
    def get_header_value_from_df(self, df=None, json_data=None):
        # 转换为DataFrame
        # task_logger.info(f"json_data: {json_data}")

        if json_data:
            df = pd.DataFrame(json_data)
        if df.empty:
            return [],[]
        new_header_keys = [f'column{i + 1}' for i in range(len(df.columns))]
        header = [{'title': col, 'key': key} for col, key in zip(df.columns.tolist(), new_header_keys)]
        header.append({'title': '数据生成时间', 'key': 'add_time'})

        df.columns = new_header_keys
        data = [{'structured_data_id': get_snowflake_id(), 'add_time': now_datetime_str_format(), **row} for idx, row in
                enumerate(df.to_dict(orient='records'))]
        return header,data

    def delete_structured_by_forage_id(self, forage_id):
        """
        根据forage_id删除结构化数据
        
        Args:
            forage_id: 要删除的数据ID
        Returns:
            bool: 删除是否成功
        """
        try:
            query = {'forage_id': forage_id}
            result = self.mongo.db[self.structured_db_name].delete_one(query)
            
            # 检查是否成功删除
            if result.deleted_count > 0:
                task_logger.info(f"成功删除forage_id: {forage_id}的数据")
                return True
            else:
                task_logger.info(f"未找到forage_id: {forage_id}的数据")
                return False
                
        except Exception as e:
            task_logger.error(f"删除结构化数据失败: {str(e)}")
            raise ValueError(f"删除结构化数据失败: {str(e)}")
        
    def insert_structured_to_mongo(self,forage_id,header,data,tp_user_id,tp_user_name):
        mongo_data = {
            'forage_id': forage_id,
            'header': header,
            'data': data,
            'add_time': now_datetime_str_format(),
            'tp_user_id': tp_user_id,
            'corpid': self.corpid,
            'tp_user_name': tp_user_name
        }
        self.delete_structured_by_forage_id(forage_id)
        # Insert data into MongoDB
        self.mongo.db[self.structured_db_name].insert_one(mongo_data)

    def check_and_update_structured_by_forage_id(self, forage_id, new_header, value_list, tp_user_id, tp_user_name):
        """
        Check if there's data for a given forage_id.

        :param forage_id: The ID of the forage to check.
        :return: True if there's data, False otherwise.
        """
        query = {'forage_id': forage_id}
        result = self.mongo.db[self.structured_db_name].find_one(query)
        header = result.get('header', []) if result else []
        is_insert = False  # is_insert 为True时，表示插入数据， False时，表示更新数据
        task_logger.info(f'new_header: {new_header}')
        task_logger.info(f'header: {header}')

        # 若header和new_header不一致,且result['data]不为空，无法插入数据
        if result and not self.compare_headers(header, new_header) and result.get('data'):
            task_logger.error(f'forage_id: {forage_id}  表头不一致，无法插入数据')
            raise ValueError(StatusMap['excel_header_not_match'])

        if result is None or 'data' not in result or not result['data']:
            is_insert = True
        if is_insert:
            self.insert_structured_to_mongo(forage_id, new_header, value_list, tp_user_id, tp_user_name)
        else:
            self.add_to_mongo_data_field(forage_id, value_list)

    def compare_headers(self, header1, header2):
        """
        比对两个header列表是否一致
        
        Args:
            header1: 第一个header列表
            header2: 第二个header列表
        Returns:
            bool: 如果header一致返回True，否则返回False
        """
        if len(header1) != len(header2):
            return False
        title1_list = [h['title'] for h in header1]
        title2_list = [h['title'] for h in header2]
        if set(title1_list) != set(title2_list):
            return False
        return True

    def preprocess_structured_response(self, response_text: str) -> str:
        """
        预处理模型返回的文本，确保格式正确
        """
        # 移除可能的markdown代码块标记
        response_text = re.sub(r'```python|```', '', response_text)
        
        # 替换中文标点为英文标点
        punctuation_map = {
            '，': ',',
            '：': ':',
            '"': '"',
            '"': '"',
            ''': "'",
            ''': "'",
            '（': '(',
            '）': ')',
            '【': '[',
            '】': ']',
            '｛': '{',
            '｝': '}'
        }
        
        for cn, en in punctuation_map.items():
            response_text = response_text.replace(cn, en)
        
        # 移除多余的空格和换行
        response_text = response_text.strip()
        
        return response_text

    def parse_generated_data(self, response_text: str) -> dict:
        """
        解析生成的数据，使用更安全的方式
        """
        try:
            # 预处理响应文本
            cleaned_text = self.preprocess_structured_response(response_text)
            
            # 检查 JSON 是否完整
            if not cleaned_text.strip().startswith('[') or not cleaned_text.strip().endswith(']'):
                task_logger.error(f"JSON 格式不完整: {cleaned_text}")
                raise ValueError(StatusMap['json_format_err'])

            # 尝试使用ast.literal_eval进行安全解析
            try:
                return ast.literal_eval(cleaned_text)
            except Exception as e:
                task_logger.error(f"ast.literal_eval 解析失败: {str(e)}")
                # 如果ast.literal_eval失败，尝试使用eval
                try:
                    return eval(cleaned_text)
                except Exception as e:
                    task_logger.error(f"eval 解析失败: {str(e)}")
                    return []
        except Exception as e:
            task_logger.error(f"数据解析失败，原始文本: {response_text}")
            task_logger.error(f"清理后的文本: {cleaned_text}")
            raise ValueError(StatusMap['json_parse_err'])
    
    def get_template_data(self, forage_id, sample_int):
        structured_data = self.get_mongo_structured_all(forage_id=forage_id)
        # sample_ratio = max(round(float(sample_ratio), 2), 1)  # 确保至少为1
        sample_from_data = structured_data['data'][:1000] # 限制样本数量前1000条
        # 使用浮点数计算，避免大整数运算
        num_records = max(int(sample_int), 1)
        
        # 确保样本数量不超过总数据量，且为非负数
        num_records = min(num_records, len(sample_from_data))
        num_records = max(num_records, 0)
        
        task_logger.info(f"forage_id: {forage_id}  样本数量: {num_records}")
        if num_records > 0:
            sample_data = random.sample(sample_from_data, num_records)
        else:
            sample_data = []

        # 通过 header 重新拼接 sample_data
        header = structured_data.get('header', [])
        if header:
            # 创建 key 到 title 的映射
            key_to_title = {h['key']: h['title'] for h in header}
            # 重新拼接 sample_data
            formatted_data = []
            for item in sample_data:
                formatted_item = {key_to_title.get(k, k): v for k, v in item.items()}
                formatted_data.append(formatted_item)
            return formatted_data
        else:
            return sample_data
        
    
    def reinsert_structured_after_dup(self, forage_id, primary_key,is_distinct):
        structured_data = self.get_mongo_structured_all(forage_id=forage_id)
        len_structured_data = 0
        if structured_data and 'data' in structured_data:
            # Convert the 'data' field in the MongoDB document to a DataFrame
            if is_distinct and primary_key:
                df = pd.DataFrame(structured_data['data'])
                # Remove duplicate rows based on specific columns
                df = df.drop_duplicates(subset=primary_key)
            
                unique_data = df.to_dict(orient='records')
                mongo_update = {
                    'data': [{'structured_data_id': get_snowflake_id(), 'add_time': now_datetime_str_format(), **item} for item in unique_data]
                }
                self.mongo.db[self.structured_db_name].update_one(
                    {'forage_id': forage_id},
                    {'$set': mongo_update},
                    upsert=False
                )
            
            len_structured_data = self.get_mongo_structured_len_data(forage_id=forage_id)
        return len_structured_data
    
    def ai_generate_structured_data(self, stats, chat, num_records, current_batch_size, 
                                  batch_index, template_data, forage_id, tp_user_id, tp_user_name, primary_key):
        primary_key = primary_key if primary_key else '无'
        prompt = f"""请根据以下模板生成{current_batch_size}条数据：，当前为第{batch_index}批：
                        模板数据：{template_data}
                        模板数据的唯一键： {primary_key}
                        要求：
                        1. 返回格式必须是Python列表格式，列表中包含{current_batch_size}个字典
                        """
        task_logger.info(f'forage_id: {forage_id} 应生成数量：{num_records}  当前批次： {batch_index}  当前批次生成数量 {current_batch_size}')

        stats.api_calls += 1
        start_time = time.time()
        try:
            for item in chat.chat_model.receive(prompt, False):
                json_item = json.loads(item.decode())
                result = json_item.get('result')

                # 解析生成的数据
                generated_data = self.parse_generated_data(result)
                if not generated_data:
                    continue

                # 验证数据数量
                if not isinstance(generated_data, list):
                    raise ValueError("生成的数据不是列表格式")
                # Update statistics
                stats.successful_records += len(generated_data)
                stats.failed_records += current_batch_size - len(generated_data)

                header, data = self.get_header_value_from_df(json_data=generated_data)
                if not header:
                    continue
                self.check_and_update_structured_by_forage_id(forage_id, header, data, tp_user_id, tp_user_name)
                duration = time.time() - start_time
                
                task_logger.info(
                    f"当前批次： {batch_index} 成功生成: {len(generated_data)}，总耗时: {round(duration, 2)}秒")
                
            return {'code': 0, 'message': 'success', 'num': len(generated_data)}
        except Exception as e:
            task_logger.error(f"生成数据失败: {str(e)}")
            return {'code': 1, 'message': str(e)}



class DataGenerationStats:
    def __init__(self):
        self.start_time = time.time()
        self.total_records = 0
        self.successful_records = 0
        self.failed_records = 0
        self.total_tokens = 0
        self.api_calls = 0
        
    def to_dict(self):
        end_time = time.time()
        duration = end_time - self.start_time
        return {
            "开始时间": datetime.fromtimestamp(self.start_time).strftime('%Y-%m-%d %H:%M:%S'),
            "结束时间": datetime.fromtimestamp(end_time).strftime('%Y-%m-%d %H:%M:%S'),
            "总耗时(秒)": round(duration, 2),
            "平均每条耗时(秒)": round(duration / self.successful_records, 2) if self.successful_records > 0 else 0,
            "总记录数": self.total_records,
            "成功记录数": self.successful_records,
            "失败记录数": self.failed_records,
            "成功率": f"{(self.successful_records / self.total_records * 100):.2f}%" if self.total_records > 0 else "0%",
            "API调用次数": self.api_calls,
            "总消耗token数": self.total_tokens
        }