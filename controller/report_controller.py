# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: report_controller.py
# @Author: <PERSON>LiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 7月 09, 2024
# ---

from . import BaseController

from modeler.mysql.ai_report_orm import AiReportOrm
from modeler.mysql.knowledge_orm import KnowledgeOrm
from modeler.mongo.chat_mongo import ChatMongo
from modeler.mysql.question_orm import QuestionOrm


class ReportController(BaseController):

    def __init__(self, *args):
        super(ReportController, self).__init__(*args)
        self.model = AiReportOrm(self.corpid)
        self.knowledge_model = KnowledgeOrm(self.corpid)
        self.chat_mongo = ChatMongo(self.corpid)
        self.question_model = QuestionOrm(self.corpid)

    def ai_report_menu(self, app_id, report_id):
        return self.model.get_ai_report_by_name(app_id, report_id)

    def ai_report_info(self, report_id):
        return self.model.get_ai_report_detail(report_id)

    def quote_list(self, doc_ids, chunk_ids):
        doc_list = self.knowledge_model.get_doc_list_by_doc_ids(doc_ids)
        for doc in doc_list:
            doc['chunk_list'] = self.knowledge_model.get_doc_chunk_list_by_chunk_ids(doc['document_id'], chunk_ids)
        return doc_list

    def update_session_record(self, session_id, data):
        return self.chat_mongo.update_session_report_record(session_id, data)

    def get_session_record(self, session_id):
        return self.chat_mongo.get_session_report_record(session_id)

    def chatbi_data_list(self, data_names):
        data_names = data_names.split(',')
        return self.question_model.get_chatbi_datas(data_names)