# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: batch_doc_controller.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 10月 05, 2024
# ---
import json
import os
import urllib.parse
import uuid
import pandas as pd

from controller.classify_controller import AppIdManager
from lib_func.const_map import QIANWEN_CODE
from lib_func.logger import logger
from utils.oss_utils import OSSUtil
from controller import BaseController
from modeler.mysql.knowledge_orm import KnowledgeOrm
from controller.knowledge_controller import KnowledgeController
from apps.ai import task


class BatchDocController(BaseController):
    """
    新鲜事批处理
    """

    def __init__(self, corpid):
        super().__init__(corpid)
        self.knowledge_orm = KnowledgeOrm(corpid)

    @staticmethod
    def list_files_os(directory):
        try:
            # 获取指定目录中的文件和子目录列表
            files_and_dirs = os.listdir(directory)

            # 过滤出仅文件（排除子目录）
            files = [f for f in files_and_dirs if os.path.isfile(os.path.join(directory, f))]

            return files
        except FileNotFoundError:
            logger.error(f"目录 {directory} 不存在")
            return []
        except PermissionError:
            logger.error(f"没有权限访问目录 {directory}")
            return []

    def upload_batch_doc(self, doc_list, directory_path):

        # 上传图片到阿里云 OSS
        oss_util = OSSUtil(self.corpid)
        knowledge_id = '4940113662934257665'
        for doc_name in doc_list:
            oss_path = f"knowledge/{knowledge_id}/{uuid.uuid4().hex}.txt"
            res = oss_util.put_object_from_file(oss_path, os.path.join(directory_path, doc_name))
            oss_url = urllib.parse.unquote(res.resp.response.url)
            logger.info(oss_url)
            # self.knowledge_orm.add_knowledge_doc(knowledge_id, doc_name, 'text/plain', oss_url, 0, "TODO")
            # break

    def get_doc_list(self, knowledge_id, args):
        docs = self.knowledge_orm.get_knowledge_doc_list(knowledge_id, args)
        total = len(docs['data_list'])
        knowledge_controller = KnowledgeController(self.corpid)
        es = knowledge_controller.es
        # model_path = knowledge_controller.default_local_emb_model_path
        knowledge_info = knowledge_controller.get_knowledge_detail(knowledge_id)
        model_path = knowledge_info['model_path'] if knowledge_info and knowledge_info['model_path'] else 'embedding-v1'
        dims = knowledge_info['dims'] if knowledge_info and knowledge_info['dims'] else 1024
        mapping = {
            "properties": {
                "doc_id": {
                    "type": "keyword"
                },
                "knowledge_id": {
                    "type": "keyword"
                },
                "description": {
                    "type": "text"
                },
                "character_count": {
                    "type": "integer"
                },
                "hit_count": {
                    "type": "integer"
                },
                "tokens": {
                    "type": "integer"
                },
                "vector": {
                    "type": "dense_vector",
                    "dims": 1024,
                    "index": True
                },
                "delete_flag": {
                    "type": "boolean"
                }
            }
        }
        emb_mapping = {
            "properties": {
                "chunk_id": {
                    "type": "keyword"
                },
                "doc_id": {
                    "type": "keyword"
                },
                "knowledge_id": {
                    "type": "keyword"
                },
                "content_id": {
                    "type": "keyword"
                },
                "description": {
                    "type": "text"
                },
                "character_count": {
                    "type": "integer"
                },
                "hit_count": {
                    "type": "integer"
                },
                "tokens": {
                    "type": "integer"
                },
                "status": {
                    "type": "boolean"
                },
                "vector": {
                    "type": "dense_vector",
                    "dims": dims,
                    "index": True
                },
                "delete_flag": {
                    "type": "boolean"
                }
            }
        }
        index = 1
        for doc in docs['data_list']:
            # logger.info(doc)
            logger.info(f'当前处理第 {index}，总共 {total} 条数据, doc_id: {doc["doc_id"]}')
            res = knowledge_controller.document_splitter_v1(doc['doc_url'], 1000, 200)
            # logger.info(res)

            if res['code'] == 0 and len(res['data']) > 0:
                description_str = ''
                for i, chunk in enumerate(res['data']):
                    chunk_id = knowledge_controller.add_knowledge_doc_chunk(
                        doc['doc_id'], chunk['content'], chunk['character_count'], 'TODO'
                    )

                    emb_data = knowledge_controller.create_embeddings(chunk['content'], str(doc['doc_id']), model_path)
                    if emb_data['code'] == 0:
                        data = {
                            "chunk_id": chunk_id,
                            "doc_id": doc['doc_id'],
                            "knowledge_id": knowledge_id,
                            "content_id": '',
                            "description": chunk['content'],
                            "character_count": len(chunk['content']),
                            "hit_count": 0,
                            "tokens": emb_data['total_tokens'],
                            "status": True,
                            "vector": emb_data['vector'],
                            "delete_flag": False
                        }
                        index_name = 'knowledge_%s' % model_path

                        es.insert_data(index_name, data, emb_mapping)
                        knowledge_controller.update_knowledge_doc_chunk(chunk_id,
                                                                        {'tokens': emb_data['total_tokens'], 'result': 'FINISHED'})

                        #  取前三段，拼接为描述
                        # if i < 3:
                        #     description_str += chunk['content']
                abstract = None
                # try:
                #     # 文档摘要
                #     abstract = None
                #     if description_str:
                #         # description_str = description_str[:1024]
                #         message = "请根据以下内容进行摘要总结，%s。 要求：仅返回摘要内容，不返回其他内容，内容格式为文本格式，不要markdown格式。" % description_str
                #         response = AppController(self.corpid).chat_qianwen(message, [], QIANWEN_CODE, 'qwen-turbo',
                #                                                            is_stream=False)
                #
                #         for item in response:
                #             logger.info(item)
                #             if item and item.decode():
                #                 abstract = json.loads(item.decode())['result']
                #         emb_data = knowledge_controller.create_embeddings(abstract, str(doc['doc_id']), knowledge_controller.default_local_emb_model_path)
                #         if emb_data['code'] == 0:
                #             data = {
                #                 "doc_id": doc['doc_id'],
                #                 "knowledge_id": knowledge_id,
                #                 "description": abstract,
                #                 "character_count": len(abstract),
                #                 "hit_count": 0,
                #                 "tokens": emb_data['total_tokens'],
                #                 "vector": emb_data['vector'],
                #                 "delete_flag": False
                #             }
                #             index_name = 'knowledge_doc_abstract_%s' % knowledge_controller.default_local_emb_model_path
                #             es.insert_data(index_name, data, mapping)
                #             logger.info('====摘要完成====')
                # except Exception as e:
                #     logger.info(e)

                knowledge_controller.update_knowledge_doc(doc['doc_id'],
                                                          {'status': 'FINISHED', 'chunk_size': len(res['data']), 'result': 'FINISHED',
                                                           'abstract': abstract})

            index += 1


            # break

    def re_doc_abstract(self, knowledge_id, doc_id, description_str):
        """
        重新生成摘要
        """

        # description_str = description_str[:1024]
        knowledge_controller = KnowledgeController(self.corpid)
        es = knowledge_controller.es
        # message = "请根据以下内容进行摘要总结，%s。 要求：仅返回摘要内容，不返回其他内容，内容格式为文本格式，不要markdown格式。" % description_str
        # response = AppController(self.corpid).chat_qianwen(message, [], QIANWEN_CODE, 'qwen-turbo',
        #                                                    is_stream=False)
        mapping = {
            "properties": {
                "doc_id": {
                    "type": "keyword"
                },
                "knowledge_id": {
                    "type": "keyword"
                },
                "description": {
                    "type": "text"
                },
                "character_count": {
                    "type": "integer"
                },
                "hit_count": {
                    "type": "integer"
                },
                "tokens": {
                    "type": "integer"
                },
                "vector": {
                    "type": "dense_vector",
                    "dims": 1024,
                    "index": True
                },
                "delete_flag": {
                    "type": "boolean"
                }
            }
        }
        abstract = description_str
        # for item in response:
        #     if item and item.decode():
        #         abstract = json.loads(item.decode())['result']
        # logger.info(abstract)
        emb_data = knowledge_controller.create_embeddings(abstract, str(doc_id),
                                                          knowledge_controller.default_local_emb_model_path)

        mapping = {
            "properties": {
                "doc_id": {
                    "type": "keyword"
                },
                "knowledge_id": {
                    "type": "keyword"
                },
                "description": {
                    "type": "text"
                },
                "character_count": {
                    "type": "integer"
                },
                "hit_count": {
                    "type": "integer"
                },
                "tokens": {
                    "type": "integer"
                },
                "vector": {
                    "type": "dense_vector",
                    "dims": 1024,
                    "index": True
                },
                "delete_flag": {
                    "type": "boolean"
                }
            }
        }

        if emb_data['code'] == 0:
            data = {
                "doc_id": doc_id,
                "knowledge_id": knowledge_id,
                "description": abstract,
                "character_count": len(abstract),
                "hit_count": 0,
                "tokens": emb_data['total_tokens'],
                "vector": emb_data['vector'],
                "delete_flag": False
            }
            index_name = 'knowledge_doc_abstract_%s' % knowledge_controller.default_local_emb_model_path

            delete_query = {
                "query": {
                    "term": {
                        "doc_id": doc_id
                    }
                }
            }
            del_res = es.delete_data(index_name, delete_query)
            es.insert_data(index_name, data, mapping)
            knowledge_controller.update_knowledge_doc(doc_id, {'abstract': abstract})

    # 导入qa
    def demo_excel(self, file_path):
        df = pd.read_excel(file_path)
        knowledge_controller = KnowledgeController(self.corpid)
        # logger.info(df)
        mapping = {
            "properties": {
                "question": {
                    "type": "keyword"
                },
                "answer": {
                    "type": "text"
                },
                "vector": {
                    "type": "dense_vector",
                    "dims": 1024,
                    "index": True
                }
            }
        }
        index_name = 'knowledge_gt_demo_qa_%s' % knowledge_controller.default_local_emb_model_path
        for i in df.values:
            logger.info(f"{i[0]}, {i[1]}")
            emb_data = knowledge_controller.create_embeddings(i[1], self.corpid, knowledge_controller.default_local_emb_model_path)
            if emb_data['code'] == 0:
                data = {
                    "question": str(i[1]),
                    "answer": i[2],
                    "vector": emb_data['vector']
                }

                delete_query = {
                    "query": {
                        "term": {
                            "question": i[1]
                        }
                    }
                }
                del_res = knowledge_controller.es.delete_data(index_name, delete_query)
                knowledge_controller.es.insert_data(index_name, data, mapping)




if __name__ == "__main__":
    # 示例使用
    c = BatchDocController('wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ')
    directory_path = "/Users/<USER>/Downloads/知识库材料/知识库1002batch1/国投集团nw"
    # files = c.list_files_os(directory_path)
    # logger.info(files)
    # c.upload_batch_doc(files, directory_path)
    #
    # doc_list = c.get_doc_list('4940113662934257665', {'status': 'TODO', 'is_all': 'all'})
    # logger.info(doc_list)
    # desc = "美亚中敏科技有限公司(简称：美亚中敏)的出差管理办法旨在规范出差流程，控制差旅费用，提高成本意识。适用于公司及其子公司，员工因派驻、轮岗除外。主要原则是合理安排、低价优先，出差需提前计划并提交申请，避免无效出差，涉及境外还需遵循国投智能股份的员工出国管理规定。交通标准上，飞机、火车、轮船等均需考虑成本，优先选择经济舱或硬席，特殊情况下可选择软席。住宿标准根据职别和出差地不同，伙食费按出差天数定额报销。出差期间，接待单位提供交通或餐饮时，相应费用将从定额中扣除。特殊情况下的超标准开支需提供说明并经审批后报销。"
    # desc = "美亚中敏，厦门美亚中敏科技有限公司，美亚中敏的员工出差报销时每接待一餐按多少元的标准扣减伙食费?美亚中敏的员工出差报销时每接待一餐按多少元的标准扣减伙食费?"
    # c.re_doc_abstract('4940238895364182017', '4940239082790850561', desc)

    # c.demo_excel('/Users/<USER>/Downloads/qa新增.xlsx')
    c.demo_excel('/Users/<USER>/Downloads/qa4.xlsx')
