#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/11/1 18:00
# <AUTHOR> wow
# @File    : content_info_controller.py
# @Comment :
import datetime
import json
import re
import time

from lib_func.const_map import *
from utils.tools import datetime_to_dt_str
# from . import BaseController
from controller import BaseController
from modeler.mysql.content_info_orm import ContentInfoOrm
from collections import defaultdict, Counter

class ContentInfoController(BaseController):
    def __init__(self, *args):
        super(ContentInfoController, self).__init__(*args)
        self.model = ContentInfoOrm(self.corpid)
        
    def auto_update(self):
        self.model.add_content_info_by_chunk_image_id()

    def check_content_ids_auth(self, content_ids):
        return self.model.check_content_ids_auth(content_ids)
    
    def get_content_ids_by_delete_flag(self):
        return self.model.get_content_ids_by_delete_flag()
    
    def update_content_info_delete_flag(self, content_ids):
        self.model.update_content_info_delete_flag(content_ids)