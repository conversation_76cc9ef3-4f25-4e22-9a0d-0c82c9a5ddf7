from . import BaseController
from modeler.mysql.mini_orm import MiniOrm


class MiniController(BaseController):

    def __init__(self, *args):
        super(MiniController, self).__init__(*args)
        self.model = MiniOrm(self.corpid)


    def get_client_info_detail(self, client_id, unionid=None, phone_number=None):
        """
        获取客户信息详情
        """
        return self.model.get_client_info_detail(client_id, unionid, phone_number)

