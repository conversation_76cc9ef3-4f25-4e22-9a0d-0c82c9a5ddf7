# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: knowledge_controller.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 3月 28, 2024
# ---
import asyncio
import os
import json
import time
from enum import Enum

import fitz
import mimetypes
from numpy import object_
import requests
import urllib

from sympy import pde_separate_add
from apps.ai import knowledge
import settings
from urllib import parse
from typing import List, <PERSON><PERSON>
from datetime import datetime, timedelta

from sentry_sdk.integrations import aiohttp

from rapidocr_onnxruntime import RapidOCR

from apps.ai.analyzer.cluster.viewer import DataCollector
from apps.ai.analyzer.config import SAMPLE_SIZE
from apps.ai.analyzer.meta_info import MetaInfoProcessor
from apps.ai.analyzer.rank_job import RankJob
from apps.ai.analyzer.sampling_strategy import SamplingFunc, SamplingStrategy
from apps.ai.analyzer.semantic_vectorization import SemanticVectorization
from apps.graphrag.configs import VDB_ENTITIES, DEFAULT_LOCAL_EMB_MODEL_PATH_GRAPH, VDB_RELATIONSHIPS
from utils import minio_utils
from utils.oss_utils import OSSUtil
from lib_func.const_map import *
from puppet.es_sdk import EsSdkPool
from . import BaseController
from horticulture.request_retry import retry_request
from lib_func.exception import ALiApiError, ChatGptError
from puppet.qianfan_sdk import QianFanModel
from puppet.qianwen_sdk import QianWenModel
from modeler.mongo.knowledge_mg import KnowledgeMg,DocumentSplitMg
from modeler.mysql.app_orm import AppOrm
from modeler.mysql.ai_model_orm import AiModelBalanceOrm

from modeler.mysql.knowledge_orm import KnowledgeOrm, LifeCycleRuleOrm, FileClearRuleORM, KnowledgeDocumentORM, FileClearDataORM, FileClearRelationORM, FileKnowledgeClearRelationORM
from modeler.mysql.qa_orm import QaOrm
from modeler.mysql.content_info_orm import ContentInfoOrm
from langchain_community.document_loaders import TextLoader, UnstructuredFileLoader, PyMuPDFLoader, Docx2txtLoader, \
     UnstructuredWordDocumentLoader, UnstructuredPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter,CharacterTextSplitter
from langchain.schema import Document
from controller.excel_parse_controller import ExcelParser
from lib_func.logger import task_logger, logger
from utils.tools import extract_images_from_docx, get_snowflake_id, return_ocr_result, str_to_date,parse_date, word_tables_to_csv, process_mime_doc
from module import minio_util
from controller.user_controller import TPUserController, SchemeController
import aiohttp
import subprocess
import uuid 
from utils.tools import xls_to_xlsx,download_and_verify_scripts, convert_otherfile_to_markdown,convert_docx_by_MarkItDown,convert_xlsx_to_markdown,convert_xls_to_markdown, emf_to_png
import re
from lib_func.type_map import SeparatorMap
from openai import OpenAI
from langchain_experimental.text_splitter import SemanticChunker
from langchain_core.embeddings import Embeddings
from controller.chat_view_controller import  ChatViewController
class CustomPDFLoader(UnstructuredPDFLoader):
    """Custom PDF Loader that filters headers and footers and returns Document objects."""

    def __init__(self, file_path: str, header_threshold: int = 100, footer_threshold: int = 100):
        super().__init__(file_path)
        self.header_threshold = header_threshold
        self.footer_threshold = footer_threshold

    def _filter_header_footer(self, page: fitz.Page) -> str:
        """Filter header and footer based on position."""
        rect = page.rect
        top_margin = rect.y0 + self.header_threshold
        bottom_margin = rect.y1 - self.footer_threshold

        filtered_blocks = []
        for block in page.get_text("dict")["blocks"]:
            if block["type"] == 0:  # Text blocks
                for line in block["lines"]:
                    for span in line["spans"]:
                        y0 = span["bbox"][1]
                        y1 = span["bbox"][3]
                        if top_margin <= y0 <= bottom_margin and top_margin <= y1 <= bottom_margin:
                            filtered_blocks.append(span["text"])

        return ' '.join(filtered_blocks)

    def load(self) -> List[Document]:
        document = fitz.open(self.file_path)
        documents = []
        for page_num in range(len(document)):
            page = document.load_page(page_num)
            filtered_content = self._filter_header_footer(page)
            doc = Document(page_content=filtered_content, metadata={"page": page_num + 1})
            documents.append(doc)
        return documents

class GraphStatus(Enum):
    DEFAULT = 0
    RUNNING = 1
    BUILDUP = 2
    FINISHED = 3
    ERROR = 4

class GalaxyStatus(Enum):
    DEFAULT = 0
    RUNNING = 1
    BUILDUP = 2
    FINISHED = 3
    ERROR = 4

class KnowledgeController(BaseController):

    def __init__(self, *args):
        super(KnowledgeController, self).__init__(*args)
        self.model = KnowledgeOrm(self.corpid)
        self.es = EsSdkPool(self.corpid)
        self.mg = KnowledgeMg(self.corpid)
        self.index_name = 'knowledge_'  # 知识库索引
        self.content_index_name = 'content_text-embedding-v1'  # 内容索引
        self.vector_all_index_name = 'vector_all_'  # 向量化索引
        self.default_model_path = settings.DEFAULT_MODEL_PATH
        self.default_local_emb_model_path = settings.DEFAULT_LOCAL_EMB_MODEL_PATH  # 本地部署向量化模型
        self.content_model = ContentInfoOrm(self.corpid)
        self.abstract_index_name = 'knowledge_doc_abstract_'  # 知识库文档摘要索引
        self.content_graph_index_name = 'content_graph_'  # 内容管理图谱索引
        self.knowledge_graph_index_name = 'knowledge_graph_'  # 知识库内容图谱索引
        self.chunk_model = KnowledgeDocumentORM(self.corpid)
        self.file_rule_model = FileClearDataORM(self.corpid)
        self.file_rule_relation_model = FileClearRelationORM(self.corpid)
        self.knowledge_rule_relation_model = FileKnowledgeClearRelationORM(self.corpid)
        self.document_split_mg = DocumentSplitMg(self.corpid)

    def to_dict(self):
        return {
            "corp_id": self.corp_id,
        }

    def insert_many_document_split_log(self, log_list):
        return self.mongo.insert_data_many(self.record_table, log_list)

    def generate_tag_abstract_graph(self, message, document_id: str, data_type: str, aigc_type_id: str, model_path: str,
                                    app_controller):
        result = []
        for item in app_controller.ai_app_chat(message, [], aigc_type_id, model_path):
            if item is not None:  # 添加这个判断
                result.append(item[0])

        # 然后将result拼接成字符串
        full_result = ''.join(result)
        doc_info = self.get_knowledge_document_detail(document_id)
        update_data = {}

        update_key = data_type

        data_type_dict = {
            'tag': 'description',
            'abstract': 'abstract',
            'graph': 'graph_content',
            'main_points': 'main_points',
            'key_words': 'key_words',
            'mind_map': 'mind_map' 
        }

        update_key = data_type_dict.get(data_type, 'graph_content')
        if doc_info:
            if doc_info.get(update_key) != full_result:
                update_data[update_key] = full_result
            if update_data:
                self.edit_doc_info(doc_info['knowledge_id'], document_id, update_data)
        return full_result or ""

    def get_knowledge_document_count(self, knowledge_id):
        return self.model.get_knowledge_document_count(knowledge_id)


    def get_file_state(self,args):
        return self.model.get_file_state(args)

    def save_chunk_clear_rule_id(self,doc_id,chunk_clear_rule_id):
        return self.model.save_chunk_clear_rule_id(doc_id,chunk_clear_rule_id)

    def get_knowledge_document_upload_history(self,args):
        return self.model.get_knowledge_document_upload_history(args)

    def add_file_rule(self, args):
        # rule_type: 1: script, 0: prompt
        if args.get("rule_type") == 1:
            download_path = "./upload/doc_clear_script"
            content = args.get("rule_content")
            
            if not os.path.exists(download_path):
                os.makedirs(download_path)
            # download script to local
            try:
                oss_url = urllib.parse.unquote(content)
                logger.info(f"{type(oss_url)}, {oss_url}")
                
                file_path = os.path.join(download_path, os.path.basename(oss_url))
                minio_util.download_file_use_requests(oss_url, file_path)
            except:
                return 0, "下载脚本失败，存储规则失败"
        args['rule_content'] = oss_url
        id = str(self.file_rule_model.add_file_rule(args))
        return 1, id
    
    def get_all_file_rule(self):
        return self.file_rule_model.get_all_file_rule()
    
    def query_file_rule(self, rule_id):
        return self.file_rule_model.query_file_rule(rule_id)
    
    def put_file_rule(self, rule_id, rule_content, rule_name, doc_type):
        return self.file_rule_model.put_file_rule(rule_id, rule_content, rule_name, doc_type)
    
    def get_file_rule_table(self, page, page_size):
        return self.file_rule_model.get_file_rule_table(page, page_size)
    
    def delete_file_rule(self, rule_id):
        return self.file_rule_model.delete_file_rule(rule_id)
    
    def get_file_relation_rule(self, knowledge_id):
        return self.file_rule_relation_model.get_file_relation_rule(knowledge_id)
    
    def add_knowledge_relation_rule(self, knowledge_id, params):
        return self.knowledge_rule_relation_model.add_knowledge_relation_rule(knowledge_id, params)
    
    def put_knowledge_relation_rule(self, knowledge_id, old_params, params):
        return self.knowledge_rule_relation_model.put_knowledge_relation_rule(knowledge_id, old_params, params)
    
    def get_knowledge_relation_rule(self, knowledge_id):
        return self.knowledge_rule_relation_model.get_knowledge_relation_rule(knowledge_id)
    
    def delete_knowledge_relation_rule(self, ids):
        return self.knowledge_rule_relation_model.delete_knowledge_relation_rule(ids)

    def add_file_relation_rule(self, doc_list, excel_list):
        doc_doc_ids = doc_list.get("doc_ids")
        doc_rule_ids = doc_list.get("rule_ids")
        excel_doc_ids = excel_list.get("doc_ids")
        excel_rule_ids = excel_list.get("rule_ids")
        if not (doc_doc_ids or doc_rule_ids or excel_doc_ids or excel_rule_ids):
            return False
        if doc_doc_ids and doc_rule_ids:
            for doc_id in doc_doc_ids:
                self.file_rule_relation_model.add_file_relation_rule(doc_id, doc_rule_ids)
        if excel_doc_ids and excel_rule_ids:
            for doc_id in excel_doc_ids:
                self.file_rule_relation_model.add_file_relation_rule(doc_id, excel_rule_ids)
        return True

    def add_file_relation_rule_v1(self, doc_id, rule_ids):
        return self.file_rule_relation_model.add_file_relation_rule(doc_id, rule_ids)



    def download_file_v1(self,object_name,bucket_name):
        return minio_util.download_file_v1(bucket_name,object_name)


    def get_knowledge_tree(self, args):
        user_info = self.user_info
        args['auth_scheme_ids'] = SchemeController().check_role_permit(user_info.get('user_id'), user_info.get('role_ids'))
        return self.model.get_knowledge_tree(args)

    def add_knowledge_tree(self, args):
        return self.model.add_knowledge_tree(args)
    
    def delete_knowledge_tree(self, args):
        logger.info(args)
        knowledge_ids = self.model.get_knowledge_ids_by_directory(args)
        if knowledge_ids:
            for knowledge_id in knowledge_ids:
                self.del_es_knowledge({"knowledge_id":knowledge_id})
        self.model.delete_knowledge_tree(args)
        return knowledge_ids
    
    def add_knowledge_tag(self, args):
        return self.model.add_knowledge_tag(args)
    
    def get_knowledge_tag(self, args):
        return self.model.get_knowledge_tag(args)
        
    def get_knowledge_documents_by_date(self,knowledge_id, args):
        args['start_time'] = parse_date(args.get('start_time')) if args.get('start_time') else None
        args['end_time'] = parse_date(args.get('end_time')) if args.get('end_time') else None
        return self.model.get_knowledge_documents_by_date(knowledge_id,args)

    def delete_knowledge_documents_by_date(self,knowledge_id,args):
        args['start_time'] = parse_date(args.get('start_time'))
        args['end_time'] = parse_date(args.get('end_time'))
        objs = self.model.delete_knowledge_documents_by_date(knowledge_id,args)
        document_ids = [x.document_id for x in objs]
        self.delete_knowledge_documents_by_doc_ids(knowledge_id, document_ids)
        return document_ids

    def  delete_knowledge_documents_by_doc_ids(self,knowledge_id,doc_ids):
        cut_ids = doc_ids[:20]
        for_no = 0
        while cut_ids:
            if self.bulk_del_es_knowledge(knowledge_id, {'doc_id': cut_ids}):
                self.model.delete_knowledge_documents_by_doc_ids(cut_ids)
            for_no += 1
            cut_ids = doc_ids[for_no * 20:(for_no + 1) * 20]
        return doc_ids
    def get_all_knowledge_list(self):
        return self.model.get_all_knowledge_list()

    def get_knowledge_list(self, args):
        # return self.model.get_knowledge_list(args)
        user_info = self.user_info
        args['auth_scheme_ids'] = SchemeController().check_role_permit(user_info.get('tp_user_id'), user_info.get('role_ids'))
        return self.model.get_knowledge_list_v2(args)

    def get_all_scope(self):
        return self.model.get_all_scope()

    def get_system_list(self):
        return self.model.get_system_list()
    
    def create_system(self, args):
        return self.model.create_system(args)

    def get_system_config(self, args):
        return self.model.get_system_config(args)

    def update_system_config(self, args):
        return self.model.update_system_config(args)

    def create_system_config(self, args):
        return self.model.create_system_config(args)

    def delete_system_config(self, args):
        return self.model.delete_system_config(args)
    
    def get_knowledge_list_by_directory_id(self, args):
        user_info = self.user_info
        args['auth_scheme_ids'] = SchemeController().check_role_permit(user_info.get('user_id'), user_info.get('role_ids'))
        return self.model.get_knowledge_list_by_directory_id(args)

    def openapi_knowledge_list(self, args):
        return self.model.get_knowledge_list_v1(args)

    def openapi_gt_knowledge_list(self, args):
        user_id = TPUserController(self.corpid).user_info_by_code(args.get('gtid')).get('tp_user_id')
        return self.model.get_knowledge_list_gt(args, user_id)

    def get_knowledge_detail_v0(self, knowledge_id):
        return self.model.get_knowledge_detail(knowledge_id)

    def get_knowledge_detail(self, knowledge_id):
        # return self.model.get_knowledge_detail(knowledge_id)
        return self.model.get_knowledge_detail_v2(knowledge_id)

    def add_knowledge(self, args, tp_user_id,scene_id):
        return self.model.add_knowledge(args['knowledge_name'], args['knowledge_desc'], args['icon_url'], args.get('aigc_model_id'),
                                        tp_user_id, args['auth_scheme_id'],scene_id, args.get("scope", 0),
                                        args.get('parent_directory_id',None),
                                        args.get("graph_enable", 0),args.get('tag',None),args.get('scope_id',None))

    def update_knowledge(self, knowledge_id, args):
        return self.model.update_knowledge(knowledge_id, {**args, 'scope': args.get('scope', 0), 'scope_id': args.get('scope_id', None)})

    def get_knowledge_doc_list(self, knowledge_id, args):
        return self.model.get_knowledge_doc_list(knowledge_id, args)

    def openapi_knowledge_doc_list(self, knowledge_id, args):
        data = self.model.openapi_knowledge_doc_list(knowledge_id, args)
        return data

    def del_knowledge_doc(self, doc_id, ):
        return self.model.del_knowledge_doc(doc_id)

    def del_knowledge_doc_by_qp(self, qp):
        return self.model.del_knowledge_doc_by_qp(qp)
    
    def add_chunk_images(self, chunk_id, images):
        return self.model.add_chunk_images(chunk_id, images)

    def add_chunk_image(self, chunk_id, image_url):
        return self.model.add_chunk_image(chunk_id, image_url)

    def add_chunk_video(self, chunk_id: str, frame: str, video_time: str, audio_start: int, audio_end: int) -> str:
        return self.model.add_chunk_video(chunk_id, frame, video_time, audio_start, audio_end)

    def del_chunk_image_by_chunk_id(self, chunk_id):
        return self.model.del_chunk_image_by_chunk_id(chunk_id)
    
    def get_chunk_hits_images(self, chunk_ids):
        ret_lst = self.model.get_chunk_hits_images(chunk_ids)
        final_images_list = []
        for i in ret_lst:
            final_images_list.extend(i.get('image_list', []))
        unique_images_list = list(set(final_images_list))
        # logger.info(f"ret_lst: {ret_lst}, \nfinal_images_list: {final_images_list}")
        return unique_images_list

    def get_chunk_hits_images_v2(self, chunk_ids, controller=None, message = "", app_info=None):

        trim_ids = chunk_ids

        if (controller is not None) and (app_info is not None) and app_info.get('is_pic_rerank'):
    
            chunk_list = self.chunk_model.get_chunk_content_by_chunk_ids(chunk_ids)

            chunk_id_map = { x['content']: x['chunk_id'] for x in chunk_list}
        
            text_list = [x.get('content', '') for x in chunk_list]
            rerank_list = controller.re_rank_text(message, text_list)

            trim_list = [item[0] for item in list(filter(lambda x: x[1] > app_info.get('pic_rarank_ignore_score', 0), rerank_list))]

            trim_ids = [chunk_id_map[x] for x in trim_list]

            trim_ids = list(filter(lambda x: x in trim_ids, chunk_ids))

        ret_lst = self.model.get_chunk_hits_images(trim_ids)

        unique = set()
        final_images_list = []
        for i in ret_lst:
            image_list = i.get('image_list', [])
            not_has = False
            for img in image_list:
                img_name = img.split('_', 1)[-1]
                if img_name not in unique:
                    unique.add(img_name)
                    not_has = True
            if not_has:
                final_images_list.append(i)        
        return final_images_list

    def document_splitter(self, oss_url, chunk_size=256, chunk_overlap=20):
        """
        :param oss_url: https://zb-zcxx.oss-cn-beijing.aliyuncs.com/ed4061e6-f60d-4e46-ad28-ede96ec10c1f/2018-12-05-12-39-52.jpg
        :param chunk_size
        :param chunk_overlap
        :return: []
        """
        try:

            oss_url = urllib.parse.unquote(oss_url)
            bucket_name = oss_url.split('/')[2].split('.')[0]
            oss_util = OSSUtil(corp_id=self.corpid)
            save_path = settings.OSS_DIR + bucket_name + urllib.parse.urlparse(oss_url).path
            file_path = oss_util.download_file(oss_url, save_dir=save_path)
            # file_path = OSS_DIR + bucket_name + oss_url.split('aliyuncs.com')[1]
            # 'pptx', 'ppt', 'jpg', 'png', 'jpeg', 'gif'
            # # file_path = '/Users/<USER>/Downloads/智邦T3产品线V1.0规划.pptx'
            # file_path = '/mnt/oss/test-oss-bz/exampledir/智邦T3产品线V1.0规划.pptx'
            # file_path = '/Users/<USER>/Downloads/aaaaaa.png'
            document_dict = {'txt', 'pdf', 'docx', 'doc', 'xlsx', 'xls', 'md', 'jpg', 'png', 'jpeg'}
            file_name = urllib.parse.unquote(urllib.parse.urlparse(oss_url).path.split("/")[-1])
            suffix = file_name.split('.')[-1]
            split_documents = True
            if suffix in document_dict:
                match suffix:
                    case 'txt':
                        loader = TextLoader(file_path, 'gbk')
                        try:
                            docs = loader.load()
                        except Exception as e:
                            loader = TextLoader(file_path, 'utf-8')
                    case 'pdf':
                        loader = PyMuPDFLoader(file_path)
                    case 'docx':
                        loader = Docx2txtLoader(file_path)
                    case 'doc':
                        loader = UnstructuredWordDocumentLoader(file_path)
                    case 'jpg' | 'png' | 'jpeg' | 'gif':
                        headers = {
                            "Content-Type": "application/json",
                        }
                        payload = json.dumps({
                            'url': oss_url
                        })

                        return_dict = return_ocr_result(headers, payload)
                        if return_dict.get('code') == 400:
                            return return_dict
                        docs = return_dict.get('data')

                        # TODO: 上传图片功能抽离
                        # response = requests.request('GET', JUSURE_OCR_API, headers=headers, data=payload)
                        # if response.status_code == 200:
                        #     docs = response.json()['text']
                        # else:
                        #     return {'code': 400, 'message': 'ocr识别失败'}         
                        
                        # ocr = RapidOCR()
                        # result, _ = ocr(file_path)
                        # docs = '\n'.join([line[1] for line in result])
                        split_documents = False
                        
                    # case 'ppt':
                    #     loader = UnstructuredPowerPointLoader(file_path)
                    # case 'pptx':
                    #     loader = UnstructuredPowerPointLoader(file_path)
                    case _:
                        loader = UnstructuredFileLoader(file_path)

                text_splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
                if split_documents:
                    documents = loader.load()
                else:
                    documents = text_splitter.create_documents([docs])
                texts = text_splitter.split_documents(documents)
                data_list = list()
                for text in texts:
                    data_list.append(
                        {'content': text.page_content, 'character_count': len(text.page_content)})
                return {'code': 0, 'data': data_list}
            else:
                return {'code': 400, 'message': '文件格式错误，错误格式【%s】支持格式 %s' % (suffix, document_dict)}
        except Exception as e:
            return {'code': 400, 'message': str(e)}

    def document_clear(self, oss_url):
        try:
            # aliyun oss
            if settings.IS_ALIYUN_OSS:
                oss_url = urllib.parse.unquote(oss_url)
                bucket_name = oss_url.split('/')[2].split('.')[0]
                oss_util = OSSUtil(corp_id=self.corpid)
                save_path = settings.OSS_DIR + bucket_name + urllib.parse.urlparse(oss_url).path
                file_path = oss_util.download_file(oss_url, save_dir=save_path)
            # minio
            else:
                oss_url = urllib.parse.unquote(oss_url)
                # relative_file_path = oss_url.split("oss.ai.zhongshuruizhi.com")[-1]
                relative_file_path = oss_url.split(settings.OSS_DOMAIN_NAME)[-1]
                download_path = settings.BASE_DIR + "/upload/minio" + relative_file_path
                minio_util.download_file_use_requests(oss_url, download_path)
                file_path = download_path
        except Exception as e:
            return {'code': 400, 'message': str(e)}

        task_logger.info("+" * 25)
        task_logger.info(f"Start clear oss_url: {oss_url}\nfile_path: {file_path}")
        task_logger.info("+" * 25)

        file_name = urllib.parse.unquote(urllib.parse.urlparse(oss_url).path.split("/")[-1])
        suffix = file_name.split('.')[-1]

        is_clear_doc = {'txt', 'pdf', 'docx', 'doc', 'md', 'pptx', 'ppt'}
        if suffix not in is_clear_doc:
            return {'code': 400, 'message': '文件格式错误，错误格式【%s】支持格式 %s' % (suffix, is_clear_doc)}
        # todo
        if suffix == "txt": pass
        elif suffix == "pdf": pass
        elif suffix in {"pptx", 'ppt'}: pass
        elif suffix == {"docx", 'doc'}: pass
        elif suffix == "md": pass
        else: pass
        return

    def document_splitter_v1(self, oss_url, chunk_size=256, chunk_overlap=20, rows_per_chunk=1, pdf_model=0, slice_model=0, doc_image_list=[],model_path="bge-large-zh-v1.5", tp_user_id=None,separator=""):
        """
        :param oss_url: OSS 文件的 URL
        :param chunk_size: 非 Excel 文件的切片大小
        :param chunk_overlap: 非 Excel 文件的切片重叠大小
        :param rows_per_chunk: Excel 文件每个 chunk 对应的行数
        :return: 包含文本和图片 URL 的字典
        """
        # aliyun oss
        try:
            file_path = self.handle_file_download(oss_url)
        except Exception as e:
            return {'code': 400, 'message': f'文件下载失败: {str(e)}'}

        task_logger.info("+" * 25)
        task_logger.info(f"Start split oss_url: {oss_url}\nfile_path: {file_path}")
        task_logger.info("+" * 25)

        # ocr = RapidOCR()
        document_dict = {'txt', 'pdf', 'docx', 'doc', 'xlsx', 'xls', 'md', 'jpg', 'png', 'jpeg', 'gif', 'pptx', 'ppt'}
        suffix = oss_url.lower().split('.')[-1]
        if suffix not in document_dict:
            return {'code': 400, 'message': '文件格式错误，错误格式【%s】支持格式 %s' % (suffix, document_dict)}
        if separator == "":
            separator = SeparatorMap.get(slice_model, "")
        data_list = []
        images_list = []
        split_documents = True
        doc_img_list = []
        oss_util = OSSUtil(corp_id=self.corpid)
        csv_path_list = []
        output_folder_path = os.path.join(settings.BASE_DIR, f"upload/knowledge_doc_image_files/{get_snowflake_id()}")
        current_time = datetime.now().strftime('%Y%m%d%H%M%S')
        task_logger.info(f"slice_model: {slice_model}")

        try:
            if slice_model == 1:
                doc_images_list = []
                if doc_image_list:
                    task_logger.info(f"doc_image_list: {doc_image_list}")
                    doc_images_list = [self.handle_file_download(image) for image in doc_image_list]
                # task_logger.warning(f"doc_image_list: {doc_image_list}")
                if  suffix == 'md':
                    try:
                        return self.process_chapter_md_file(file_path, oss_util, chunk_size, doc_images_list)
                    except Exception as e:
                        return {'code': 400, 'message': f'章节切片处理失败: {str(e)}'}
                elif suffix == 'docx':
                    doc_name = file_path.split("/")[-1]
                    with open(file_path, 'rb') as file:
                        temp_content = file.read()
                    pe_doc_name = doc_name.split(".")[0]
                    content = convert_docx_by_MarkItDown(temp_content, 'docx', pe_doc_name)
                    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                    task_logger.warning(f'parent_dir: {parent_dir}')

                    save_file_path = os.path.join(parent_dir, "upload/minio/guotou/knowledge", doc_name)
                    task_logger.warning(f'save_file_path: {save_file_path}')

                    # save_file_path = "../upload/minio/guotou/knowledge/" + doc_name  # 替换为你想要保存文件的路径和文件名
                    with open(save_file_path, 'wb') as file:
                        file.write(content)
                    return self.process_chapter_md_file(save_file_path, oss_util, chunk_size, doc_images_list)
                else:
                    return {'code': 400, 'message': '文件格式错误，错误格式【%s】支持格式 %s' % (suffix, ['docx', 'doc', 'md'])}
            if suffix in {'xlsx', 'xls'}:
                try:
                    return self.process_xlsx_file(file_path,current_time,oss_util,rows_per_chunk)
                except Exception as e:
                    return {'code': 400, 'message': f'xlsx文件处理失败: {str(e)}'}

            elif suffix in ['pptx', 'ppt'] or (suffix in ['pdf'] and pdf_model == 1):
                try:
                    return self.process_pptx_file(oss_url)
                except Exception as e:
                    return {'code': 400, 'message': f'pptx文件处理失败: {str(e)}'}



            elif suffix == 'md':
                try:
                    return self.process_md_file(file_path,chunk_size,chunk_overlap)
                except Exception as e:
                    return {'code': 400, 'message': f'md文件处理失败: {str(e)}'}
            else:
                if suffix == 'txt':
                    loader = TextLoader(file_path, 'gbk')
                    try:
                        docs = loader.load()
                    except Exception as e:
                        loader = TextLoader(file_path, 'utf-8')
                elif suffix == 'pdf' and pdf_model == 0:
                    # loader = PyMuPDFLoader(file_path)
                    loader = PyMuPDFLoader(file_path, extract_images=False)
                    # 去掉pdf 页眉 页脚
                    # loader = CustomPDFLoader(file_path, header_threshold=100, footer_threshold=100)
                elif suffix == 'docx':
                    loader = Docx2txtLoader(file_path)
                    try:
                        doc_img_list, position_list = extract_images_from_docx(file_path, output_folder_path)
                        task_logger.info(f"doc_img_list: {doc_img_list}, position_list: {position_list}")
                    except Exception as e:
                        return {'code': 400, 'message': f'docx文件处理失败: {str(e)}'}
                elif suffix in ['jpg', 'png', 'jpeg', 'gif']:
                    try:
                        return self.process_image_file(oss_url)
                    except Exception as e:
                        return {'code': 400, 'message': f'image文件处理失败: {str(e)}'}

                else:
                    loader = UnstructuredFileLoader(file_path)

                texts = ""
                if separator != "":
                    text_splitter = CharacterTextSplitter(separator=separator,chunk_size=chunk_size,chunk_overlap=chunk_overlap)
                else:
                    text_splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size,chunk_overlap=chunk_overlap)
                if split_documents:
                    documents = loader.load()
                else:
                    documents = text_splitter.create_documents([docs])

                try:
                    #  增加 slice_model=2 时使用语义切片
                    if slice_model == 2:
                        embedding_model_path = model_path
                        task_logger.info(f"embedding_model_path: {embedding_model_path}")
                        long_text = documents[0].page_content if isinstance(documents, list) else docs
                        try:
                            chunks = self.semantic_split(long_text, model_path=embedding_model_path, tp_user_id=tp_user_id)
                            task_logger.info(f"chunks: {chunks}")
                            for chunk in chunks:
                                if len(chunk) >0:
                                    data_list.append({
                                        'content': chunk,
                                        'character_count': len(chunk)
                                    })
                        except Exception as e:
                            return {'code': 400, 'message': f'语义切片失败: {str(e)}'}
                    else:
                        texts = text_splitter.split_documents(documents)

                    for text in texts:
                        data_list.append({
                            'content': text.page_content,
                            'character_count': len(text.page_content)
                        })
                except Exception as e:
                    return {'code': 400, 'message': f'文本分片失败: {str(e)}'}

                # 处理文档中的图片
                if doc_img_list:
                    try:
                        return self.process_docx_image_file(doc_img_list,position_list,oss_util,text_splitter,data_list,images_list,csv_path_list)
                    except Exception as e:
                        return {'code': 400, 'message': f'docx带图片文件处理失败: {str(e)}'}
                        
        except Exception as e:
            task_logger.error(f'文档处理过程中发生错误: {str(e)}')
            return {'code': 400, 'message': f'文档处理过程中发生错误: {str(e)}'}
        finally:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as e:
                task_logger.warning(f'删除文件失败: {str(e)}')

        return {'code': 0, 'data': data_list, 'images': images_list,'csv_path_list': csv_path_list}


    def semantic_split(self, long_text, model_path, tp_user_id):

        embeddings = EmbeddingWrapper(model_path=model_path, corpid=self.corpid, tp_user_id=tp_user_id)

        chunker = SemanticChunker(
            embeddings=embeddings,
            buffer_size=1,
            sentence_split_regex=r"(?<=[。！？!?])\s*",
            breakpoint_threshold_type="gradient",
            breakpoint_threshold_amount=80
        )

        result = chunker.split_text(long_text)

        chunk_stats = []
        for idx, chunk in enumerate(result):
            _, token_count = embeddings.embed_fn(chunk)
            chunk_stats.append((idx, token_count, chunk[:50].replace('\n', ' ') + "..."))  # 预览前 50 个字

        for idx, token_count, preview in chunk_stats:
            task_logger.info(f"Chunk {idx}: {token_count} tokens | Preview: {preview}")
        return result



    def extract_table_images_from_docx(self,doc_url, output_folder_path):
        download_path = self.handle_file_download(doc_url)
        doc_img_list, position_list = extract_images_from_docx(download_path, output_folder_path)
        bucket_name = settings.KNOWLEDGE_OSS_BUCKET_NAME
        csv_path_list = []
        logger.info(f"doc_img_list: {doc_img_list}")
        for img_url in doc_img_list:
            with open(img_url, 'rb') as imagefile:
                image_data = imagefile.read()
            csv_url = minio_util.upload_to_minio(bucket_name, img_url, image_data)
            csv_path_list.append(csv_url)
        logger.info(f"csv_path_list: {csv_path_list}")
        return  csv_path_list

    def handle_file_download(self,oss_url):
        if settings.IS_ALIYUN_OSS:
            oss_url = urllib.parse.unquote(oss_url)
            bucket_name = oss_url.split('/')[2].split('.')[0]
            oss_util = OSSUtil(corp_id=self.corpid)
            save_path = settings.OSS_DIR + bucket_name + urllib.parse.urlparse(oss_url).path
            file_path = oss_util.download_file(oss_url, save_dir=save_path)
            return file_path
        # minio
        else:
            if "#" in oss_url or "%" in oss_url:
                oss_url = oss_url.replace("%", "%25").replace("#", "%23")
            else:
                oss_url = urllib.parse.unquote(oss_url)
            relative_file_path = oss_url.split(settings.OSS_DOMAIN_NAME)[-1]
            download_path = settings.BASE_DIR+ "/upload/minio" + relative_file_path
            minio_util.download_file_use_requests(oss_url, download_path)
            file_path = download_path
            suffix = oss_url.lower().split('.')[-1]
            new_file_path = file_path.replace(file_path.split('/')[-1], f"{get_snowflake_id()}.{suffix}")
            task_logger.info(f"new_file_path: {new_file_path}")
            os.rename(file_path, new_file_path) 
            file_path = new_file_path
            return file_path

    def del_split_chunk(self,doc_id):
        self.model.del_split_chunk(doc_id)

    def process_xlsx_file(self, file_path,current_time,oss_util,rows_per_chunk=1):
        """
        处理xlsx文件
        """
        # 处理 Excel 文件，按行进行分块
        data_list = []
        images_list = []
        excel_parser = ExcelParser(file_path)
        records = excel_parser.parse()

        chunked_records = [records[i:i + rows_per_chunk] for i in range(0, len(records), rows_per_chunk)]

        for chunk in chunked_records:
            chunk_texts = []
            chunk_images = []

            for record in chunk:
                chunk_texts.extend(record['texts'])
                if 'images' in record:
                    # 处理并上传图片到 OSS，并保存图片的 URL
                    folder_path = os.path.join(settings.BASE_DIR, f"upload/image_files/{current_time}", f"row_{record['row']}")
                    os.makedirs(folder_path, exist_ok=True)
                    for i, image_bytes in enumerate(record['images']):
                        image_name = f"image_{i+1}.png"
                        image_path = os.path.join(folder_path, image_name)

                        # 保存图片到本地
                        with open(image_path, 'wb') as img_file:
                            img_file.write(image_bytes)
                        task_logger.info(f"Saved image {i+1} to {image_path}")

                        # 上传图片到阿里云 OSS
                        if settings.IS_ALIYUN_OSS:
                            oss_path = f"{current_time}/row_{record['row']}/{image_name}"
                            res = oss_util.put_object_from_file(oss_path, image_path)
                            oss_url = urllib.parse.unquote(res.resp.response.url)
                            task_logger.info(f"Uploaded image {i+1} to OSS: {oss_url}")
                            chunk_images.append(oss_url)
                        # upload -> minio
                        else:
                            bucket_name = settings.KNOWLEDGE_OSS_BUCKET_NAME
                            object_name = f"{current_time}/row_{record['row']}/{image_name}"
                            oss_url = minio_util.upload_file_v2(bucket_name, object_name, image_path)
                            task_logger.info(f"Uploaded image {i+1} to MINIO OSS: {oss_url}")
                            chunk_images.append(oss_url)

            chunk_data = {
                'content': " ".join(chunk_texts),
                'character_count': sum(len(text) for text in chunk_texts)
            }
            data_list.append(chunk_data)
            images_list.append(chunk_images if chunk_images else [])
        return {'code': 0, 'data': data_list, 'images': images_list}

    def process_pptx_file(self,oss_url):
        ip = settings.JUSURE_MULTIMODAL_IMAGE_DESCRIPTION
        headers = {
            "Content-Type": "application/json",
        }

        payload = json.dumps({
            "url": oss_url
        })

        logger.info(f'request params : {payload}')
        response = requests.request('POST', ip, headers=headers , data=payload)
        logger.info(f'response result: {response.status_code} , \n {response.text}')
        if response.status_code == 200:
            images_list = []
            data_list = []

            result = response.json()
            for i in range(len(result[0])):
                images_list.append(result[0][i])
                data_list.append({
                    'content': result[1][i]['content'],
                    'character_count': result[1][i]['character_count']
                })
            return {'code': 0, 'data': data_list, 'images': images_list}
        else:
            return {'code': 400, 'message': 'pptx文件处理失败'}

    def process_docx_image_file(self, doc_img_list,position_list,oss_util,text_splitter,data_list,images_list,csv_path_list):
                    # dir_name = os.path.dirname(doc_img_list[0])
        is_success, res_or_error_list = emf_to_png(doc_img_list)
        if not is_success:
            logger.info(f'error_list: {res_or_error_list}')
            task_logger.error(f'document_splitter_v1: error_list{res_or_error_list}')
        else:
            doc_img_list = res_or_error_list
        # logger.info(f"=== 处理文档中的图片, doc_img_list: {doc_img_list}")
        # logger.info(doc_img_list)
        for img_url ,para_index in zip(doc_img_list, position_list):
            # 上传图片到阿里云 OSS
            if settings.IS_ALIYUN_OSS:
                oss_path = f"knowledge_doc_image_files/{get_snowflake_id()}/{img_url}"
                res = oss_util.put_object_from_file(oss_path, img_url)
                doc_img_oss_url = urllib.parse.unquote(res.resp.response.url)
            # upload -> minio
            else:
                bucket_name = settings.KNOWLEDGE_OSS_BUCKET_NAME
                new_img_url = img_url
                # new_img_url = new_img_url.lstrip('/')
                new_img_url = new_img_url.replace('\\\\', '/').replace('\\', '/').split('/')[-1]
                object_name = f"knowledge_doc_image_files/{get_snowflake_id()}/{new_img_url}"
                oss_url = minio_util.upload_file_v2(bucket_name, object_name, img_url)
                # task_logger.info(f"Uploaded image {i+1} to MINIO OSS: {oss_url}")
                doc_img_oss_url = urllib.parse.unquote(oss_url)
            
            # logger.info("L" * 50)

            # logger.info(f"=== doc_img_oss_url: {doc_img_oss_url}")
            headers = {
                "Content-Type": "application/json",
            }
            payload = json.dumps({
                'url': doc_img_oss_url
            })

            try:
                return_dict = return_ocr_result(headers, payload)
                docs = return_dict.get('data')
            except Exception as e:
                task_logger.error(f"ocr识别失败: {e}")
                docs = "未识别到文字"

            if docs:
                documents = text_splitter.create_documents([docs])

                texts = text_splitter.split_documents(documents)

                for text in texts:
                    data_list.append({
                        'content': text.page_content,
                        'character_count': len(text.page_content),
                        'doc_img_url': doc_img_oss_url,
                        'position': para_index
                    })
            else:
                mutil_modal_result= self.process_image_file(doc_img_oss_url)
                task_logger.info(f"mutil_modal_result: {mutil_modal_result['data']}")
                data_list.append({
                    'content': mutil_modal_result['data'][0].get('content'),
                    'character_count': len(mutil_modal_result['data'][0].get('content')),
                    'doc_img_url': doc_img_oss_url,
                    'position': para_index
                })
            os.remove(img_url)
        task_logger.info(f"csv_path_list_knowledge_controller: {csv_path_list}")
        return {'code': 0, 'data': data_list, 'images': images_list, 'csv_path_list': csv_path_list}


    def process_paragraph_image_file(self, doc_img_list,oss_util):
        res_list = []
        is_success, res_or_error_list = emf_to_png(doc_img_list)
        if not is_success:
            logger.info(f'error_list: {res_or_error_list}')
            task_logger.error(f'document_splitter_v1: error_list{res_or_error_list}')
        else:
            doc_img_list = res_or_error_list
        # logger.info(f"=== 处理文档中的图片, doc_img_list: {doc_img_list}")
        # logger.info(doc_img_list)
        for img_url in doc_img_list:
            # 上传图片到阿里云 OSS
            if settings.IS_ALIYUN_OSS:
                oss_path = f"knowledge_doc_image_files/{get_snowflake_id()}/{img_url}"
                res = oss_util.put_object_from_file(oss_path, img_url)
                doc_img_oss_url = urllib.parse.unquote(res.resp.response.url)
            # upload -> minio
            else:
                bucket_name = settings.KNOWLEDGE_OSS_BUCKET_NAME
                new_img_url = img_url
                # new_img_url = new_img_url.lstrip('/')
                new_img_url = new_img_url.replace('\\\\', '/').replace('\\', '/').split('/')[-1]
                object_name = f"knowledge_doc_image_files/{get_snowflake_id()}/{new_img_url}"
                oss_url = minio_util.upload_file_v2(bucket_name, object_name, img_url)
                # task_logger.info(f"Uploaded image {i+1} to MINIO OSS: {oss_url}")
                doc_img_oss_url = urllib.parse.unquote(oss_url)

            # logger.info("L" * 50)

            # logger.info(f"=== doc_img_oss_url: {doc_img_oss_url}")
            headers = {
                "Content-Type": "application/json",
            }
            payload = json.dumps({
                'url': doc_img_oss_url
            })

            return_dict = return_ocr_result(headers, payload)
            if return_dict.get('code') == 400:
                return return_dict
            docs = return_dict.get('data')


            res_list.append({
                'content': docs,
                'character_count': len(docs),
                'doc_img_url': doc_img_oss_url
            })

            os.remove(img_url)
        return res_list

    def process_md_file(self, file_path,chunk_size,chunk_overlap):
        data_list = []
        images_list = []
        image_url = ""
        image_pattern = re.compile(r'!\[([^\]]*)\]\(([^)]+)\)')

        with open(file_path, 'r', encoding='utf-8') as f:
            md_content = f.read()

        # 处理图片描述
        for match in image_pattern.finditer(md_content):
            image_url = match.group(2)
            images_list.append([image_url])

            # 获取图片描述
            id = str(uuid.uuid4())
            ip = settings.JUSURE_MULTIMODAL_IMAGE_DESCRIPTION_V1

            headers = {
                "Content-Type": "application/json",
            }

            params = {
                'id': id,
                'url': image_url  # oss_url 可能应该是 image_url
            }

            response = requests.get(ip, headers=headers, params=params)

            if response.status_code == 200:
                result = response.json()
                content = result['text'].replace('|||||||||||:', '\r\n')
                # 将图片描述添加到 data_list
                data_list.append({
                    'content': content,
                    'character_count': len(content)
                })
        md_content = re.sub(image_pattern, '', md_content)
        # 按段落切割
        paragraphs = md_content.split('\n\n')

        for paragraph in paragraphs:
            # 先筛选空段落
            if not paragraph.strip():
                continue  # 跳过空段落

            start_index = 0
            end_index = chunk_size
            if end_index >= len(paragraph):
                data_list.append({
                    'content': paragraph,
                    'character_count': len(paragraph)
                })
                continue
            while end_index < len(paragraph):
                chunk = paragraph[start_index:end_index]
                data_list.append({
                    'content': chunk,
                    'character_count': len(chunk)
                })
                start_index = end_index - chunk_overlap
                end_index += chunk_size

        return {'code': 0, 'data': data_list, 'images': images_list}

    def process_chapter_md_file(self, file_path, oss_util, chunk_size=5000, img_list=[]):
        data_list = []
        images_list = []

        import re

        def split_by_first_level_title(chunks, buffer):
            pattern = re.compile(r'^# (.*)$', re.MULTILINE)
            result = []
            combined_text = ''.join(chunks) + buffer
            matches = list(pattern.finditer(combined_text))
            start_index = 0
            for match in matches:
                if match.start() > start_index:
                    chunk = combined_text[start_index:match.start()]
                    if chunk.strip():
                        result.append(chunk)
                start_index = match.start()
            last_chunk = combined_text[start_index:]
            if last_chunk.strip():
                result.append(last_chunk)
            return result

        def split_by_length(chunk, max_length=chunk_size):
            result = []
            while len(chunk) > max_length:
                result.append(chunk[:max_length])
                chunk = chunk[max_length:]
            if chunk.strip():
                result.append(chunk)
            return result

        def merge_small_chunks(chunks, min_length=50):
            new_chunks = []
            i = 0
            while i < len(chunks):
                chunk = chunks[i]
                if len(chunk) < min_length:
                    if new_chunks:
                        new_chunks[-1] += chunk
                    elif i + 1 < len(chunks):
                        chunks[i + 1] = chunk + chunks[i + 1]
                else:
                    new_chunks.append(chunk)
                i += 1
            return new_chunks

        def process_chunks(file_url, chunk_size):
            all_chunks = []
            buffer = ""
            with open(file_url, 'r', encoding='utf-8') as file:
                while True:
                    data = file.read(1024 * 1024)
                    if not data:
                        break
                    current_chunks = split_by_first_level_title(all_chunks, buffer + data)
                    all_chunks = []
                    for chunk in current_chunks:
                        if len(chunk) > chunk_size:
                            sub_chunks = split_by_length(chunk, chunk_size)
                            all_chunks.extend(sub_chunks)
                        else:
                            all_chunks.append(chunk)
                    buffer = ""
            final_chunks = merge_small_chunks(all_chunks)

            return final_chunks

        adjusted_chunks = process_chunks(file_path, chunk_size)

        for chunk in adjusted_chunks:
            data_list.append({
                'content': chunk,
                'character_count': len(chunk)
            })
        if img_list:
            try:
                img_res_list =  self.process_paragraph_image_file(img_list,oss_util)
                data_list.extend(img_res_list)
            except Exception as e:
                return {'code': 400, 'message': f'章节切片: docx带图片文件处理失败: {str(e)}'}

        return {'code': 0, 'data': data_list, 'images': images_list}

    def process_image_file(self, oss_url):
        id = str(uuid.uuid4())
        ip = settings.JUSURE_MULTIMODAL_IMAGE_DESCRIPTION_V1
        headers = {
            "Content-Type": "application/json",
        }

        params ={
            'id' : id,
            'url' :oss_url
        }
        response = requests.request('GET', ip, headers=headers, params=params)
        if response.status_code == 200:
            result = response.json()
            content = result['text'].replace('|||||||||||:','\r\n')
            data_list = [{
                'content': content,
                'character_count': len(content)
            }]

            return {'code': 0, 'data': data_list, 'images': [[oss_url]]}
        else:
            return { 'message': '图像处理失败'}


    def get_model_path(self, knowledge_id):
        return self.model.get_model_path(knowledge_id)

    def add_knowledge_document_images_batch(self, doc_id, image_url_list):
        return self.model.add_knowledge_document_images_batch(doc_id, image_url_list)

    def get_knowledge_document_images_and_tables(self, args):
        return self.model.get_knowledge_document_images_and_tables(args)

    def add_knowledge_doc_chunk(self, doc_id, content, character_count, result):
        """
        :param doc_id:
        :param content:
        :param character_count:
        :param result:
        :return:
        """
        return self.model.add_knowledge_doc_chunk(doc_id, content, character_count, result)

    def add_knowledge_doc_chunk_csv_in_batch(self, doc_id, csv_list):
        return self.model.add_knowledge_doc_chunk_csv_in_batch(doc_id, csv_list)
    
    def transfer_doc_copy(self, args):
        return self.model.transfer_doc_copy(args)
    
    def transfer_doc(self, args):
        return self.model.transfer_doc(args)

    def update_doc_sharing(self, args):
        doc_ids = self.model.update_doc_sharing(args)
        try:
            self.delete_knowledge_documents_by_doc_ids(None,doc_ids)
        except Exception as e:
            logger.error(f"删除文档es失败: {str(e)}")
            return {'message': '删除文档失败'}
        return doc_ids

    def add_knowledge_doc(self, knowledge_id, doc_name, doc_type, doc_url, doc_size, status=None, current_node=None, state=None,content_id=None,tp_user_id=None,split_chunk_size=None,chunk_overlap=None,slice_model=None,pdf_model=None,chunk_clear_call_words=None,upload_type=0):
        """
        :param knowledge_id:
        :param doc_name:
        :param doc_type:
        :param doc_url:
        :param doc_size:
        :param status:
        :param current_node:
        :param state:
        :param content_id:
        :return:
        """
        return self.model.add_knowledge_doc(knowledge_id, doc_name, doc_type, doc_url, doc_size, status,current_node, state, content_id,None,tp_user_id,split_chunk_size,chunk_overlap,slice_model,pdf_model,chunk_clear_call_words,upload_type)

    def video_upload(self, knowledge_id, doc_name, doc_type, doc_url, doc_size,tp_user_id, key_frame, video_duration, frame_rate, upload_type):
        return self.model.video_upload(knowledge_id, doc_name, doc_type, doc_url, doc_size,tp_user_id, key_frame, video_duration, frame_rate, upload_type)

    def update_knowledge_doc(self, doc_id, args):   
        doc_id = self.model.update_knowledge_doc(doc_id, args)
        return doc_id
    
    def get_doc_clear_url(self, doc_id):
        doc_cleat_url = self.model.get_doc_clear_url(doc_id)    
        return doc_cleat_url

    def get_doc_image_urls(self, doc_id):
        image_url_list = self.model.get_doc_image_urls(doc_id)
        return image_url_list

    def edit_doc_info(self, k_id, doc_id, args):
        self.model.update_knowledge_doc(doc_id, args)
        if args.get('abstract'):
            self.generate_doc_abstract(k_id, doc_id, args['abstract'])
        return doc_id

    def generate_doc_abstract(self, k_id, doc_id, abstract):
        """
        生成文档摘要
        """

        mapping = {
            "properties": {
                "doc_id": {
                    "type": "keyword"
                },
                "knowledge_id": {
                    "type": "keyword"
                },
                "description": {
                    "type": "text"
                },
                "character_count": {
                    "type": "integer"
                },
                "hit_count": {
                    "type": "integer"
                },
                "tokens": {
                    "type": "integer"
                },
                "vector": {
                    "type": "dense_vector",
                    "dims": 1024,
                    "index": True
                },
                "delete_flag": {
                    "type": "boolean"
                }
            }
        }
        try:
            emb_data = self.create_embeddings(abstract, str(doc_id), self.default_local_emb_model_path)
            if emb_data['code'] == 0:
                data = {
                    "doc_id": doc_id,
                    "knowledge_id": k_id,
                    "description": abstract,
                    "character_count": len(abstract),
                    "hit_count": 0,
                    "tokens": emb_data['total_tokens'],
                    "vector": emb_data['vector'],
                    "delete_flag": False
                }
                index_name = 'knowledge_doc_abstract_%s' % self.default_local_emb_model_path
                delete_query = {
                    "query": {
                        "term": {
                            "doc_id": doc_id
                        }
                    }
                }
                # logger.info(index_name)
                self.es.delete_data(index_name, delete_query)
                self.es.insert_data(index_name, data, mapping)
                self.update_knowledge_doc(doc_id, {'abstract': abstract})
                return True
        except Exception as e:
            logger.error(e)
            return False

    def get_knowledge_doc_conversation(self, doc_id, aigc_model_id, prompt, size=10, min_score=1.4):
        """
        根据doc_id获取知识库文档内容，并使用模型生成回答
        :param doc_id: 文档ID
        :param aigc_model_id: 模型ID
        :param prompt: 用户输入的问题
        :return: 模型生成的回答
        """
        # 构造ES查询体
        hits = self.search_es_knowledge(None, prompt, [], size, min_score, is_search=True, doc_ids=doc_id)
        doc_content = ''.join(item['_source']['description'] for item in hits)


        chat = ChatViewController(aigc_model_id, corp_id=self.corpid, old_flag=False)

        # 使用模型生成回答
        for item in chat.chat_model.receive(doc_content):
            json_item = json.loads(item.decode())
            result = json_item.get('result')
            data = json.dumps({"status": "generating", "content": result or '',
                          "contentType": "text"}, ensure_ascii=False)
            yield data
        yield json.dumps({"status": "finished", "content": "",
                          "contentType": "text"}, ensure_ascii=False)

    def generate_doc_chunk(self, content, knowledge_id, doc_id, chunk_id):

        knowledge_info = self.get_knowledge_detail(knowledge_id)
        model_path = knowledge_info['model_path'] if knowledge_info and knowledge_info['model_path'] else 'embedding-v1'
        task_logger.info(f"词向量模型 model_path: {model_path}")
        dims = knowledge_info['dims'] if knowledge_info and knowledge_info['dims'] else 1024
        mapping = {
            "properties": {
                "chunk_id": {
                    "type": "keyword"
                },
                "doc_id": {
                    "type": "keyword"
                },
                "knowledge_id": {
                    "type": "keyword"
                },
                "content_id": {
                    "type": "keyword"
                },
                "description": {
                    "type": "text"
                },
                "character_count": {
                    "type": "integer"
                },
                "hit_count": {
                    "type": "integer"
                },
                "tokens": {
                    "type": "integer"
                },
                "status": {
                    "type": "boolean"
                },
                "vector": {
                    "type": "dense_vector",
                    "dims": dims,
                    "index": True
                },
                "delete_flag": {
                    "type": "boolean"
                }
            }
        }

        emb_data = self.create_embeddings(content, self.corpid, model_path)
        if emb_data['code'] == 0:
            data = {
                "chunk_id": chunk_id,
                "doc_id": doc_id,
                "knowledge_id": knowledge_id,
                "content_id": "",
                "description": content,
                "character_count": len(content),
                "hit_count": 0,
                "tokens": emb_data['total_tokens'],
                "status": True,
                "vector": emb_data['vector'],
                "delete_flag": False
            }
            index_name = 'knowledge_%s' % model_path
            self.es.insert_data(index_name, data, mapping)

    def get_knowledge_document_detail(self, doc_id, upload_type='0'):
        return self.model.get_knowledge_document_detail(doc_id, upload_type)

    def get_knowledge_document_detail_by_doc_source_id(self, doc_source_id):
        return self.model.get_knowledge_document_detail_by_doc_source_id(doc_source_id)

    def get_document_chunk_id_by_chunk_source_id(self, source_chunk_id):
        return self.model.get_document_chunk_id_by_chunk_source_id(source_chunk_id)

    def get_document_chunk_id_by_chunk_source_ids(self, source_chunk_ids):
        return self.model.get_document_chunk_id_by_chunk_source_ids(source_chunk_ids)
    
    def get_knowledge_doc_chunk_detail(self, chunk_id):
        return self.model.get_knowledge_doc_chunk_detail(chunk_id)

    def get_knowledge_doc_chunk_video_detail(self, chunk_id):
        return self.model.get_knowledge_doc_chunk_video_detail(chunk_id)

    def get_chunk_images(self, chunk_id):
        return self.model.get_chunk_image_list(chunk_id)

    def get_knowledge_doc_chunk_list(self, doc_id, args):
        key_word = args.get('key_word')
        if key_word:
            datas = self.search_es_work_like_doc_ids(key_word, doc_id=doc_id, is_light=True)
            if not datas:
                return {'total': 0, 'po': 1, 'data_list': []}
            else:
                chunk_map_light = {x['_source']['chunk_id']: x.get('highlight', {}).get('description') for x in datas}
                args['chunk_map_light'] = chunk_map_light
        return self.model.get_knowledge_doc_chunk_list(doc_id, args)

    def openapi_knowledge_doc_chunk_detail(self, document_id):
        return self.model.openapi_knowledge_doc_detail(document_id)

    def create_embeddings(self, input_text, user_id, model_path='text-embedding-v1', is_search=False):
        # logger.info(f"Create embeddings model_path: {model_path}")
        # task_logger.info(f"Using embeddings model_path: {model_path}")
        data = {
            'code': 0
        }
        if is_search:
            es_emb_data = self.search_es_vector_all(input_text, model_path)
            if es_emb_data:
                data.update(es_emb_data)
                return data
        if model_path == 'text-embedding-ada-002':
            try:
                res = self.create_chat_gpt_embeddings(input_text, user_id)
                data.update({
                    'vector': res['data']['data'][0]['embedding'],
                    'total_tokens': res['data']['usage']['total_tokens']
                })
            except Exception as e:
                data.update({
                    'code': -1,
                    'error_msg': str(e)
                })
        elif model_path in ['text-embedding-v1', 'text-embedding-v2']:
            app_key, app_secret = AppOrm(self.corpid).get_key_secret(QIANWEN_CODE)
            res = QianWenModel(app_key).create_embeddings([input_text], model_path)
            if res.get('status_code') != 200:
                data.update({
                    'code': -1,
                    'error_msg': res['message']
                })
            else:
                data.update({
                    'vector': res['output']['embeddings'][0]['embedding'],
                    'total_tokens': res['usage']['total_tokens']
                })

        # BAAI/bge-large-zh-v1.5
        # elif model_path == "bge-large-zh-v1.5":
        elif model_path in ["bge-large-zh-v1.5", "bge-m3"]:
            try:
                res = self.create_BAAI_bge_embeddings(input_text, model_path)
                if isinstance(input_text, str):
                    data.update({
                        'vector': res['vector'][0],
                        'total_tokens': res['total_tokens'][0]
                    })
                else:
                    data.update({
                        'vector': res['vector'],
                        'total_tokens': sum(res['total_tokens'])
                    })
            except Exception as e:
                data.update({
                    'code': -1,
                    'error_msg': str(e)
                })
                
        # BAAI/bge-m3
        # elif model_path == "bge-m3":
        #     try:
        #         res = self.create_BAAI_bge_embeddings(input_text)
        #         data.update({
        #             'vector': res['vector'][0],
        #             'total_tokens': res['total_tokens'][0]
        #         })
        #     except Exception as e:
        #         data.update({
        #             'code': -1,
        #             'error_msg': str(e)
        #         })

        else:
            app_key, app_secret = AppOrm(self.corpid).get_key_secret(WENXIN_CODE)

            res = QianFanModel({'Api_Key': app_key, 'Secret_Key': app_secret}).create_embeddings([input_text], user_id, model_path)
            if res.get('error_code'):
                data.update({
                    'code': -1,
                    'error_msg': res['error_msg']
                })
            else:
                data.update({
                    'vector': res['data'][0]['embedding'],
                    'total_tokens': res['usage']['total_tokens'],

                })
        if is_search:
            self.save_es_embeddings(input_text, data, model_path)
        return data
    
    def _get_embedding_handler(self, model_path):
        mapping = {
            'text-embedding-ada-002': self._handle_chatgpt,
            'text-embedding-v1': self._handle_qianwen,
            'text-embedding-v2': self._handle_qianwen,
            'bge-large-zh-v1.5': self._handle_bge,
            'bge-m3': self._handle_bge,
        }
        return mapping.get(model_path, self._handle_wenxin)



    async def async_create_embeddings(self, input_text, user_id, model_path='text-embedding-v1', is_search=False):
        data = {'code': 0}
        default_error_msg = "async_create_embedding func inside: "
        if is_search:
            es_data = self.search_es_vector_all(input_text, model_path)
            if es_data:
                data.update(es_data)
                return data
        try:
            handler = self._get_embedding_handler(model_path)
            vector, tokens = await handler(input_text, user_id, model_path)
            data.update({'vector': vector, 'total_tokens': tokens})
        except Exception as e:
            data.update({'code': -1, 'error_msg': f"{default_error_msg}{e}"})

        if is_search:
            self.save_es_embeddings(input_text, data, model_path)

        return data

    async def send_request(self, model_path: str, headers: dict, data: dict, use_data: bool = True) -> dict:
        url = self.model.get_model_url(model_path)
        try:
            async with aiohttp.ClientSession() as session:
                if use_data:
                    async with session.post(url=url, headers=headers, data=data) as resp:
                        resp.raise_for_status()
                        return await resp.json()
                else:
                    async with session.post(url=url, headers=headers, json=data) as resp:
                        resp.raise_for_status()
                        return await resp.json()
        except Exception as e:
            raise Exception(f"Request failed: {str(e)}")

    async def _handle_chatgpt(self, input_text, user_id, model_path):
        headers = {
            'corp-id': self.corpid,
            'Content-Type': 'application/json',
        }
        data = {
            "input_text": input_text,
            "user": user_id
        }
        res = await self.send_request(model_path, headers, data)
        return res['data']['data'][0]['embedding'], res['data']['usage']['total_tokens']

    async def _handle_qianwen(self, input_text, user_id, model_path):
        app_key, _ = AppOrm(self.corpid).get_key_secret(QIANWEN_CODE)
        api_key = str(app_key)
        url = self.model.get_model_url(model_path)
        client = OpenAI(
            api_key=api_key, # 如果您没有配置环境变量，请在此处用您的API Key进行替换
            base_url=url,  # 百炼服务的base_url
        )
        res = client.embeddings.create(
            model=model_path,
            input=[input_text],
            encoding_format="float"
            )
        task_logger.info(f"Qianwen embedding res: {res}")
        embedding = res.data[0].embedding
        total_tokens = res.usage.total_tokens
        return embedding, total_tokens

    async def _handle_bge(self, input_text, user_id, model_path):
        try:
            if not settings.IS_910B_EMBEDDINGS:
                headers = {'Content-Type': 'application/json'}

                payload = json.dumps({
                    "input_text_list": [input_text],
                })

                res = await self.send_request(model_path, headers, payload)
                return res['vector'][0], res['total_tokens'][0]
            else:
                return await self._handle_910b_embedding(input_text, headers)
        except Exception as e:
            raise ChatGptError(e)

    async def _handle_wenxin(self, input_text, user_id, model_path):
        key, secret = AppOrm(self.corpid).get_key_secret(WENXIN_CODE)
        res = await QianFanModel({'Api_Key': key, 'Secret_Key': secret}) \
            .async_create_embeddings([input_text], user_id, model_path)
        if res.get('error_code'):
            raise Exception(res['error_msg'])
        return res['data'][0]['embedding'], res['usage']['total_tokens']

    async def _handle_910b_embedding(self, input_text, headers):
        model = AiModelBalanceOrm(self.corpid)
        payload = json.dumps({"inputs": input_text})
        res = {"vector": [], "total_tokens": []}

        for attempt in range(20):
            random_desc = model.get_random_model_url()
            url = random_desc.get('model_url') + "embed"

            logger.info(f"[910B] Attempt {attempt + 1}: POST {url}, text_len={len(input_text)}")
            task_logger.info(f"[910B] Attempt {attempt + 1}: POST {url}, text_len={len(input_text)}")

            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(url=url, headers=headers, data=payload) as resp:
                        if resp.status == 200:
                            logger.info(f"[910B] Success with status 200")
                            rsp_data = await resp.json()
                            res["vector"] = rsp_data
                            res["total_tokens"].append(int(0.92 * len(input_text)))
                            return res
                        elif len(input_text) <= 560:
                            model.set_model_url_status(random_desc.get("url_id"), 1)
            except Exception as e:
                logger.warning(f"[910B] Exception during attempt {attempt + 1}: {e}")

        raise ChatGptError("910B embedding API failed after retries.")


    async def async_create_qianwen_embeddings(self, input_text: str, api_key: str, model_path: str) -> dict:
        url = 'https://dashscope.aliyuncs.com/compatible-mode/v1/embeddings'
        # 请求头
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        # 请求体
        data = {
            "model": model_path,
            "input": input_text,
            "encoding_format": "float"
        }

        try:
            response = requests.post(url, headers=headers, json=data)
            response.raise_for_status()
            res = response.json()
            return res
            # return {
            #     'vector': res['data'][0]['embedding'],
            #     'total_tokens': res['usage']['total_tokens']
            # }

        except requests.exceptions.HTTPError as http_err:
            raise ALiApiError(f'HTTP 错误发生: {http_err}')
        except requests.exceptions.RequestException as req_err:
            raise ALiApiError(f'请求错误发生: {req_err}')

    async def async_create_chat_gpt_embeddings(self, input_text, user_id):
        try:
            payload = json.dumps({
                "input_text": input_text,
                "user": user_id
            })
            # token = request.headers.get("token")
            headers = {
                # 'token': token,
                'corp-id': self.corpid,
                'Content-Type': 'application/json',
            }
            url = settings.JusureT3Api + "/chat/embedding"
            async with aiohttp.ClientSession() as session:
                async with session.post(url=url, headers=headers, data=payload) as resp:
                    response = await resp.json()
            # response = requests.request("POST", url, headers=headers, data=payload)
            return response
        except Exception as e:
            raise ChatGptError(e)
        
    async def async_create_BAAI_bge_embeddings(self, input_text, model_path):
        try:
            headers = {
                # 'token': token,
                # 'corp-id': self.corpid,
                'Content-Type': 'application/json',
            }
            if not settings.IS_910B_EMBEDDINGS:
            # if False:
                payload = json.dumps({
                    "input_text_list": [input_text],
                })
                # token = request.headers.get("token")
                
                url = settings.JUSURE_EMBEDDINGS_API + "api/v1/embeding/create"
                if model_path == "bge-m3":
                    url = settings.JUSURE_EMBEDDINGS_API + "api/v1/embeding/m3"

                task_logger.info(f"Start post jusure embedding srv: {url}, length: {len(input_text)}")
                t1 = time.time()
                async with aiohttp.ClientSession() as session:
                    async with session.post(url=url, headers=headers, data=payload) as resp:
                        response = await resp.json()
                # task_logger.info(f"Post Jusure Embedding API succeeded!, codeStatus: {response.status_code}, requestTime: {round((time.time()-t1)*1000, 2)} ms")
                
                return response
            else:
                res = {
                    "vector": [],
                    "total_tokens": []
                }
                payload = json.dumps({
                    "inputs": input_text,
                })
                # token = request.headers.get("token")
                model = AiModelBalanceOrm(self.corpid)
                random_desc = model.get_random_model_url()
                # url = settings.JUSURE_910B_EMBEDDINGS_API + "embed"
                url = random_desc.get('model_url') + "embed"
                logger.info(f"Start post jusure 910B embedding srv: {url}, length: {len(input_text)}")
                task_logger.info(f"Start post jusure 910B embedding srv: {url}, length: {len(input_text)}")
                t1 = time.time()
                # try: 
                async with aiohttp.ClientSession() as session:
                    async with session.post(url=url, headers=headers, data=payload) as resp:
                        # response = await resp.json()

                    # response = requests.request("POST", url, headers=headers, data=payload)
                # except Exception as e:
                #     logger.info(f"Post jusure 910B embedding srv failed: {e}")
                        count = 0
                        while resp.status != 200 and count < 20:
                            count += 1
                            if len(input_text) > 560:
                                pass
                            else:
                                model.set_model_url_status(random_desc.get('url_id'), 1)
                            random_desc = model.get_random_model_url()
                            url = random_desc.get('model_url') + "embed"
                            logger.info(f"Retry post jusure 910B embedding srv: {url}, length: {len(input_text)}, {input_text}")
                            task_logger.info(f"Retry post jusure 910B embedding srv: {url}, length: {len(input_text)}, {input_text}")
                            try:
                                async with aiohttp.ClientSession() as session:
                                    async with session.post(url=url, headers=headers, data=payload) as resp:
                                        response = resp
                            except Exception as e:
                                logger.info(f"Post jusure 910B embedding srv failed: {e}")

                        logger.info(f"Post Jusure 910B Embedding API succeeded!, codeStatus: {response.status_code}, requestTime: {round((time.time()-t1)*1000, 2)} ms")
                        task_logger.info(f"Post Jusure 910B Embedding API succeeded!, codeStatus: {response.status_code}, requestTime: {round((time.time()-t1)*1000, 2)} ms")
                        embeddings_910b_rsp = await response.json()
                        res['vector'] =  embeddings_910b_rsp

                        mock_910b_tokes = int(0.92 * len(input_text))
                        res['total_tokens'].append(mock_910b_tokes)
                        return res
        except Exception as e:
            raise ChatGptError(e)

    @retry_request(tries=3, jitter=2, throw_exception=True)
    async def create_BAAI_bge_embeddings_async(self, input_text_list: list[str]):
        try:
            headers = {
                # 'token': token,
                # 'corp-id': self.corpid,
                'Content-Type': 'application/json',
            }

            if not settings.IS_910B_EMBEDDINGS:
                payload = json.dumps({
                    "input_text_list": input_text_list,
                })
                url = settings.JUSURE_EMBEDDINGS_API + "api/v1/embeding/create"
                logger.info(f"Start post jusure embedding srv: {url}, list length: {len(input_text_list)}")
                task_logger.info(f"Start post jusure embedding srv: {url}, list length: {len(input_text_list)}")
                t1 = time.time()

                # 使用 aiohttp 发起异步请求
                async with aiohttp.ClientSession() as session:
                    async with session.post(url, headers=headers, data=payload) as response:
                        logger.info(
                            f"Post Jusure Embedding API succeeded!, codeStatus: {response.status}, requestTime: {round((time.time() - t1) * 1000, 2)} ms")
                        task_logger.info(
                            f"Post Jusure Embedding API succeeded!, codeStatus: {response.status}, requestTime: {round((time.time() - t1) * 1000, 2)} ms")
                        response_json = await response.json()
                        return response_json
            # TODO: stop using it for lack of batch support
            # else:
            #     res = {
            #         "vector": [],
            #         "total_tokens": []
            #     }
            #     payload = json.dumps({
            #         "inputs": input_text,
            #     })
            #     url = settings.JUSURE_910B_EMBEDDINGS_API + "embed"
            #     logger.info(f"Start post jusure 910B embedding srv: {url}, length: {len(input_text)}")
            #     task_logger.info(f"Start post jusure 910B embedding srv: {url}, length: {len(input_text)}")
            #     t1 = time.time()
            #
            #     # 使用 aiohttp 发起异步请求
            #     async with aiohttp.ClientSession() as session:
            #         async with session.post(url, headers=headers, data=payload) as response:
            #             logger.info(
            #                 f"Post Jusure 910B Embedding API succeeded!, codeStatus: {response.status}, requestTime: {round((time.time() - t1) * 1000, 2)} ms")
            #             task_logger.info(
            #                 f"Post Jusure 910B Embedding API succeeded!, codeStatus: {response.status}, requestTime: {round((time.time() - t1) * 1000, 2)} ms")
            #             embeddings_910b_rsp = await response.json()
            #             res['vector'] = embeddings_910b_rsp
            #
            #     mock_910b_tokes = int(0.92 * len(input_text))
            #     res['total_tokens'].append(mock_910b_tokes)
            #     return res
        except Exception as e:
            raise ChatGptError(e)

    @retry_request(tries=3, jitter=2, throw_exception=True)
    def create_chat_gpt_embeddings(self, input_text, user_id):
        try:
            payload = json.dumps({
                "input_text": input_text,
                "user": user_id
            })
            # token = request.headers.get("token")
            headers = {
                # 'token': token,
                'corp-id': self.corpid,
                'Content-Type': 'application/json',
            }
            url = settings.JusureT3Api + "/chat/embedding"
            response = requests.request("POST", url, headers=headers, data=payload)
            return response.json()
        except Exception as e:
            raise ChatGptError(e)

    def del_one_es_content(self, content_id, add_time):
        body_dict = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"content_id": content_id}},
                        {"term": {"add_time": add_time}}
                    ]
                }
            }
        }
        index_name = self.content_index_name
        self.es.delete_data(index_name, body_dict)

    def create_BAAI_bge_embeddings(self, input_text, model_path):
        try:
            headers = {
                # 'token': token,
                # 'corp-id': self.corpid,
                'Content-Type': 'application/json',
            }
            if not settings.IS_910B_EMBEDDINGS:
            # if False:
                real_params = [input_text] if isinstance(input_text, str) else input_text

                payload = json.dumps({
                    "input_text_list": real_params,
                })
            
                # token = request.headers.get("token")
                url = settings.JUSURE_EMBEDDINGS_API + "api/v1/embeding/create"
                # if model_path == "bge-large-zh-v1.5":
                #     url = settings.JUSURE_EMBEDDINGS_API + "api/v1/embeding/create"
                if model_path == "bge-m3":
                    url = settings.JUSURE_EMBEDDINGS_API + "api/v1/embeding/m3"
                    
                # url = settings.JUSURE_EMBEDDINGS_API + "api/v1/embeding/create"
                # logger.info(f"Start post jusure embedding srv: {url}, length: {len(input_text)}")
                task_logger.info(f"Start post jusure embedding srv: {url}, length: {len(input_text)}")
                t1 = time.time()
                response = requests.request("POST", url, headers=headers, data=payload)
                response.raise_for_status()
                # logger.info(f"Post Jusure Embedding API succeeded!, codeStatus: {response.status_code}, requestTime: {round((time.time()-t1)*1000, 2)} ms")
                task_logger.info(f"Post Jusure Embedding API succeeded!, codeStatus: {response.status_code}, requestTime: {round((time.time()-t1)*1000, 2)} ms")
                return response.json()
            else:
                res = {
                    "vector": [],
                    "total_tokens": []
                }
                payload = json.dumps({
                    "inputs": input_text,
                })
                # token = request.headers.get("token")
                model = AiModelBalanceOrm(self.corpid)
                random_desc = model.get_random_model_url()
                # url = settings.JUSURE_910B_EMBEDDINGS_API + "embed"
                url = random_desc.get('model_url') + "embed"
                logger.info(f"Start post jusure 910B embedding srv: {url}, length: {len(input_text)}")
                task_logger.info(f"Start post jusure 910B embedding srv: {url}, length: {len(input_text)}")
                t1 = time.time()
                response = None
                try: 
                    response = requests.request("POST", url, headers=headers, data=payload)
                except Exception as e:
                    logger.error(f"Post jusure 910B embedding srv failed: {e}")
                count = 0
                while (not response or response.status_code != 200) and count < 20:
                    count += 1
                    try:
                        logger.info(f"response status code: {response.status_code}")
                    except Exception as e:
                        logger.info(f"response status code: {e}")
                    if len(input_text) > 500:
                        break
                    else:
                        model.set_model_url_status(random_desc.get('url_id'), 1)
                    random_desc = model.get_random_model_url()
                    url = random_desc.get('model_url') + "embed"
                    logger.info(f"Retry post jusure 910B embedding srv: {url}, length: {len(input_text)}, {input_text}")
                    task_logger.info(f"Retry post jusure 910B embedding srv: {url}, length: {len(input_text)}, {input_text}")
                    try:
                        response = requests.request("POST", url, headers=headers, data=payload)
                    except Exception as e:
                        logger.error(f"Post jusure 910B embedding srv failed: {e}")

                logger.info(f"Post Jusure 910B Embedding API succeeded!, codeStatus: {response.status_code}, requestTime: {round((time.time()-t1)*1000, 2)} ms")
                task_logger.info(f"Post Jusure 910B Embedding API succeeded!, codeStatus: {response.status_code}, requestTime: {round((time.time()-t1)*1000, 2)} ms")
                embeddings_910b_rsp = response.json()
                res['vector'] =  embeddings_910b_rsp

                mock_910b_tokes = int(0.92 * len(input_text))
                res['total_tokens'].append(mock_910b_tokes)
                return res
        except Exception as e:
            raise ChatGptError(e)
    
    def refresh_model_status(self):
        model = AiModelBalanceOrm(self.corpid)
        model_list = model.get_all_model_url()
        logger.info('aaa---aaaa')
        for model_desc in model_list:
            headers = {
                'Content-Type': 'application/json',
            }

            vector = [[0.011506242, -0.023022633, -0.037687965, 0.04793764, 0.010620988, -0.015002217, -0.064666264, 0.033577595, 0.0041009616, -0.0028397187, 0.01978461, -0.026203888, -0.011757575, 0.009062102, -0.024858642, -0.011815015, 0.048351843, 0.015326683, 0.031902034, -0.04988864, 0.0213309, -0.0018658874, -0.06951932, -0.0070962356, -0.0044731363, 0.018441303, 0.0019579178, 0.036508113, 0.0379754, -0.04603931, 0.011173897, -0.025577536, -0.008013337, 0.009273034, 0.0033446758, 0.029357372, -0.03378548, 0.00029058038, 0.005848667, -0.02666556, 0.014065019, -0.016795687, -0.0034506796, -0.013659313, -0.04779971, 0.008360484, -0.034529675, 0.0053356406, 0.031110132, 0.04785685, -0.021756548, 0.02145374, -0.021664169, 0.033655502, 0.017339665, 0.05640092, 0.035992596, -0.02043682, 0.025353016, -0.09186136, 0.04240503, 0.06906611, 0.020371217, 0.0028137856, 0.034103647, -0.010363466, 0.0016646422, -0.042530924, -0.0034788498, -0.033056214, -0.024704782, 0.011609834, 0.03777804, 0.009508843, 0.0038439068, 0.026272764, -0.011577454, -0.0046540727, 0.0077852313, 0.04232528, -0.02001694, -0.00892641, -0.017142091, 0.012901987, 0.015539076, -0.018526347, 0.019144163, 0.33630627, 0.05160098, -0.023347907, -0.016291583, -0.012650501, -0.017457642, -0.014232507, 0.03293748, 1.0596132e-05, 0.013412828, 0.025371956, 0.022658585, 0.014732751, 0.009773865, 0.016962603, -0.003923074, -0.029357577, -0.0002991277, -0.01534502, 0.011038261, 0.0012958699, -0.010330844, -0.022964135, 0.03665711, 0.010612997, 0.017665952, -0.027259942, -0.04390127, 0.0005870272, -0.01157423, 0.019323196, 0.031398952, -0.005920446, -0.009364509, 0.0026104385, -0.01441698, 0.014913117, 0.040330242, 0.035434976, 0.021608358, 0.00972469, -0.010677304, -0.04209629, 0.060547, -0.02623105, 0.019434607, 0.033108212, 0.025365463, -0.0039449288, -0.033544306, -0.0019440957, -0.00040868562, -0.03293538, 0.025999496, -0.00010943468, -0.002372814, 0.0037377349, 0.0032716796, -0.007584042, -0.049215227, 0.023541238, -0.03986174, -0.010547485, 0.0058789593, -0.025643138, -0.025244918, -0.014223822, -0.018262692, -0.022125985, -0.014713514, -0.007008832, -0.0024030139, 0.011384594, -0.019507103, -0.008091253, 0.03869947, 0.04378431, -0.015371083, 0.013882021, -0.039769977, 0.022795651, 0.039910015, 0.0057091746, -0.037923295, -0.07155626, -0.018025644, 0.020305183, -0.0110236155, -0.05813128, 0.011356572, 0.03926708, 0.017908549, 0.00534789, -0.022349272, 0.0006899312, -0.009061726, -0.020297201, -0.052880857, -0.0111137675, 0.029902982, 0.020224862, 0.014301317, -0.032744788, -0.009133698, 0.019517086, -0.027690846, 0.00871584, 0.018513013, 0.040831763, -0.005662716, 0.0034701657, 0.052794874, -0.05040526, -0.005066025, 0.0044130986, -0.008900805, -0.01605804, -0.013401198, -0.05743761, 0.06725318, 0.017509036, 0.036410518, -0.0002483978, -0.053277776, -0.028287724, 0.0053325403, -0.019048387, 0.011500723, 0.023333805, 0.0016938193, 0.06001215, -0.0383456, -0.02548303, 0.017192284, -0.026393916, 0.024167158, -0.030949602, 0.011653111, 0.00036352815, 0.024482764, -0.008474225, -0.037319135, 0.040179167, -0.016044468, -0.016244018, 0.050809395, 0.031477816, 0.000511345, 0.031575628, 0.017120399, -0.006038201, -0.056547288, -0.0349539, -0.05344752, 0.027395226, 0.013721586, -0.01246643, 0.035695765, 0.012723846, 0.04675276, 0.014863599, 0.019108, 0.021911535, 0.037317257, 0.026971042, -0.033459146, -0.033338655, -0.027675794, -0.004017609, 0.037219387, 0.021285607, -0.018253759, -0.023557022, -0.015016234, -0.02270317, -0.031530086, -0.009467475, 0.023716401, 0.04800052, 0.006615592, 0.010606877, 0.025856365, 0.028589422, 0.042918086, -0.00010679806, -0.0061899372, -0.020664241, 0.00045187442, -0.015902122, -0.049301956, 0.015287984, 0.005793716, -0.0029887354, 0.003624505, 0.008227362, 0.013338235, -0.016215296, 0.041870296, 0.0079920115, -0.033570822, 0.0074834656, 0.0044940226, -0.018409798, -0.042530954, 0.021940876, -0.030819397, -0.0098029, 0.032945633, 0.06505049, 0.01657223, 0.0012046196, 0.0024723394, -0.011390198, 0.019605983, 0.020250406, 0.07044092, 0.011641082, 0.04546836, -0.026824791, 0.0025953006, -0.014388177, -0.014439754, 0.006113577, -0.027679836, -0.097331926, -0.0033448446, -0.06798581, 0.07494444, 0.002411953, -0.0014677995, -0.0076141655, 0.013275285, 0.071392804, -0.0050815744, 0.010936175, -0.044086486, -0.046940133, 0.01373766, 0.035291303, 0.010710937, -0.022760076, -0.016385013, 0.026079366, 0.021138117, 0.026864998, 0.0125085255, -0.043547545, 0.02892031, 0.045364253, 0.035573117, -0.0022664715, -0.044261396, 0.03396529, 0.04053767, -0.07276225, -0.0071224812, -0.05055689, -0.023762222, 0.027541645, -0.013691847, -0.0446367, -0.020720841, -0.00035677376, 0.02835911, -0.015938096, 0.0032184485, -0.041886155, 0.018141324, -0.0068331105, 0.030340044, -0.013378863, 0.030098867, 0.08462977, -0.034177706, 0.008600112, 0.031346917, -0.05263848, 0.012904547, -0.019099982, -0.043045036, 0.0046113655, -0.046094406, 0.0058497847, 0.035030995, 0.051178034, -0.019826835, -0.0041101724, -0.00459163, 0.023090811, 0.0041346783, -0.03188165, 0.0028901093, 0.032586843, 0.009715504, 0.022764424, -0.03384044, -0.0013176772, 0.005001278, -0.0102097085, 0.015513402, -0.0138761, -0.06328149, -0.012131996, -0.024608906, 0.006417583, -0.072885774, -0.03337882, -0.04732645, 0.023679473, -0.0237948, -0.01649627, -0.034776147, -0.008998146, -0.0058409325, -0.0023610299, -0.008585749, -0.020041537, 0.025243763, -0.021336056, 0.007094679, 0.038189467, 0.009115771, -0.028531883, -0.01118012, -0.0150808785, -0.008990959, 0.0028856676, 0.013140072, 0.07433155, 0.01498359, -0.044657025, -0.019463625, -0.013192364, 0.016259156, 0.024922594, 0.010532302, 0.021850396, 0.0067193527, -0.031028422, -0.03425795, -0.031080822, -0.023233332, -0.04872048, 0.01146273, 0.034857795, -0.0061566383, -0.004744438, 0.047021165, -0.009865302, -0.00963071, 0.0054843854, -0.025671957, 0.06530917, -0.030049954, -0.00526369, 0.0251581, -0.0058196676, -0.01544749, 0.044553574, 0.003817897, -0.092893735, -0.0045656613, 0.014260447, 0.0015121847, 0.04712852, -0.016443046, -0.015013465, 0.024349263, 0.0065745628, 0.01414529, -0.022444984, -0.033548903, 0.035067927, 0.048228726, -0.008606164, 0.0016350036, -0.012954456, -0.0008843655, -0.011089055, -0.057768594, -0.016408812, 0.04228218, 0.013483896, -0.010610461, -0.023932535, -0.010047167, 0.0041991263, -0.02716761, 0.017001253, -0.024803141, -0.0023878484, -0.033974327, -0.01662001, -0.015224595, 0.023288172, -0.021706073, -0.015008568, 0.03807732, 0.015799709, -0.003706445, 0.030019248, 0.03498292, -0.02423002, 0.019124102, -0.025119528, -0.05184074, 0.00074366695, 0.04220048, -0.008655418, -0.021515425, -0.020439245, -0.06909173, -0.008265908, 0.006684768, 0.04319081, 0.051668365, 0.015402524, 0.004133826, -0.061286725, -0.06489642, 0.019285483, -0.009102801, 0.015460431, 0.023454247, -0.031734206, -0.015708085, 0.022005016, -0.0035953878, -0.009284246, 0.048054807, -0.032565, 0.03017624, -0.00021308019, -0.0025098394, 0.0024651543, 0.035498854, -0.021955943, -0.037166756, 0.000858326, -0.020853948, 0.022329545, -0.026342576, -0.043443926, -0.049019784, -0.032359958, 0.011647578, 0.030802669, -0.0063727256, -0.00110855, -0.012550515, 0.02016038, -0.02275866, -0.0188764, 0.0074583255, -0.0033147775, -0.00531654, 0.0070993463, 0.025133137, 0.04617477, -0.018647106, 0.0050101154, 0.058978416, 0.02071567, -0.018639792, 0.012174188, 0.003282293, -0.03716445, -0.0002232775, 0.05135266, -0.0012703512, 0.027292654, -0.013611781, 0.0076758354, -0.023236489, 0.016850878, 0.027228458, -0.060673904, 0.034218602, 0.009651135, 0.014424706, 0.038881075, 0.009277158, 0.019702636, -0.02782218, 0.022251477, -0.03265695, -0.0055131144, 0.009000655, 0.006270351, -0.0024394772, -0.019691268, -0.10643121, 0.05667178, 0.044534918, -0.023275685, 0.03230594, -0.04028159, -0.016255181, 0.0015827367, 0.03594097, 0.02146599, -0.04089329, -0.044164736, -0.022482555, -0.0031500626, 0.007355758, -0.017676396, 0.015960664, 0.00063747884, -0.0046058455, -0.006028347, -0.012752871, -0.03648091, -0.023651036, 0.023256864, -0.06481656, -0.022991426, -0.02761675, -0.019860135, -0.0055925297, 0.029536713, -0.008314251, -0.0052371672, 0.013527123, 0.0026030447, 0.0048694503, 0.021445522, -0.0047783637, -0.03618533, 0.026048707, -0.0013829311, 0.0047580805, 0.00985981, 0.029717589, 0.0026051076, 0.0024199316, -0.016595365, -0.0024456384, -0.027688665, -0.011886226, 0.053363677, -0.066605866, -0.008108212, -0.008320767, 0.010897159, 0.048125643, 0.019299993, -0.02890581, 0.0139669785, -0.05839138, 0.014722979, -0.044089936, -0.045655813, 0.014956838, -0.005704012, -0.000990121, -0.023555659, 0.022123484, -0.024347547, -0.020249953, 0.00016516581, -0.02023982, -0.009187346, -0.021102173, 0.0068229362, -0.023718791, 0.028258277, -0.028221494, 0.02092967, -0.040933535, 0.00036598436, 0.029765882, -0.012518605, 0.022626571, 0.04840385, 0.035032794, 0.059419785, -0.04306555, -0.02037991, 0.010891851, -0.002318729, -0.019338995, 0.0023655407, 0.030446978, 0.032861307, 0.013865174, 0.038826615, 0.03757983, 0.023413397, -0.0045919577, 0.006699098, -0.032033667, 0.025697086, -0.045952007, -0.051792316, 0.017044162, 0.01734594, 0.07462268, -0.04348862, -0.026691826, 0.013053354, 0.06829086, -0.01951063, 0.0050650733, 0.030364286, -0.002125825, -0.00030275696, 0.0027416274, 0.005503671, 0.031763297, -0.026317433, -0.00892521, -0.017336925, -0.067691386, 0.016943593, -0.026615974, -0.023585448, 0.0042219884, 0.06705943, 0.05380069, 0.031178279, 0.011851673, 0.019398872, -0.014452128, -0.054510623, -0.014845283, 0.035083342, -0.031022329, 0.0560773, 0.008396328, 0.024386639, -0.011680756, 0.032398704, 0.012267943, -0.007208076, -0.041443076, 0.020240884, 0.021215906, -0.08109861, 0.01496274, -0.032489907, 0.04340505, -0.046361014, 0.021298364, -0.057268098, -0.045972977, -0.026400216, 0.009725131, -0.040670283, -0.0004520272, -0.027938036, 0.0075936574, -0.012690933, -0.037932873, 0.023523252, -0.0146015305, 0.04473997, -0.017559214, -0.014736612, -0.009050258, -0.023664445, -0.05522644, 0.018770456, 0.02302989, -0.009812411, -0.017200174, -0.037934393, 0.01382199, -0.0030113498, 0.005959331, -0.008103168, 0.042261586, -0.000933281, -0.008957037, 0.00794364, 0.014451685, -0.069135785, 0.008131003, -0.012621582, -0.011729826, -0.0014071108, -0.04081787, 0.055448532, 0.0067438195, 0.029791657, -0.024163706, 0.03382359, -0.03628944, -0.031871106, 0.07281594, 0.00039928913, 0.023524418, 0.019489406, -0.0040924917, -0.028246643, -0.0017947516, -0.043128025, -0.0531073, -0.001552141, -0.0142146265, 0.025897468, 0.006509048, -0.0407001, -0.043351464, -0.013445114, 0.023094675, -0.060402244, -0.020948311, 0.028726198, -0.0415415, 0.043682896, 0.008741621, -0.012288206, 0.0064153103, 0.02611381, -0.08592555, -0.015931705, -0.021701345, 0.004569582, -0.022915352, 0.00043420118, -0.076071285, -0.015515203, -0.046075173, -0.013865881, 0.029175589, -0.006406069, 0.012824609, 0.022684773, 0.024795465, -0.006369484, 0.027192997, 0.010272235, 0.005479056, 0.01405409, -0.0071662026, -0.039610844, 0.016150277, 0.03878072, 0.016035056, -0.046739876, -0.044324107, -0.0042133466, -0.030851994, 0.015799263, -0.057855703, -0.011563403, -0.058675632, -0.0120997215, -0.026219953, -0.021164898, 0.0414574, -0.018538965, -0.036643613, 0.011853294, 0.030360727, -0.09124054, 0.013614488, 0.0060219513, 0.04462234, 0.03632134, 0.060622953, 0.025945604, -0.0069370572, -0.05514721, 0.0070323115, -0.03875773, -0.028417919, -0.027156752, 0.060414728, 0.027307935, 0.01376224, -0.041082617, -0.014609547, -0.0012032547, 0.03717842, -0.043940023, 0.018416923, -0.054401413, -0.0072043776, -0.023449033, -0.019789867, 0.0058640586, 0.020944063, -0.008866614, 0.012882554, 0.03324583, 0.022490125, 0.02907578, -0.0508204, -0.00953372, 0.0026942766, 0.016770275, 0.03989873, 0.027047798, 0.0016227593, 0.03039375, 0.0037644585, -0.023087358, -0.016665976, 0.0033433554, 0.027180258, 0.01331379, 0.04333795, -0.029962253, -0.03768121, 0.0053158766, -0.02270431, -0.011536961, 0.03303719, -0.0073439484, 0.01207734, -0.011644986, -0.057158973, 0.016609617, 0.014629768, -0.004348898, -0.036153477, 0.05605779, -0.0009631684, -0.06325418, -0.025600683, 0.0031186298, 0.051486604, 0.021628443, 0.0409395, -0.019906243, -0.021860983, -0.0131346965, -0.0067929644, -0.009676774, 0.031283434, -0.020532843, -0.034516305, 0.03767352, 0.015848365, 0.050217435, 0.029001428, -0.004444, 0.02200374, 0.013749906, -0.031738013, -0.0030108825, -0.03542409, 0.023409616, 0.016966455, -0.010601377, 0.03776819, 0.035651106, -0.005590877, -0.029753163, 0.009424819, 0.03354485, 0.048704427, 0.014131312, -0.007543736, -0.043952316, 0.047262523, -0.033299606, -0.011939485, -0.009119922, 0.046017062, -0.0059930724, 0.004233765, -0.037631575, -0.0012912902, -0.018091507, 0.032928612, 0.02881071, -0.032087963, -0.061210833, -0.010728339, 0.0036066407, 0.018146217, 0.0027341323, 0.01894163, 0.029317534, -0.026072992, -0.06053648, 0.0060672113, -0.019533208, -0.023847817, -0.007557975, 0.0088719595, 0.035701808, 0.04794816, -0.0051421043, -0.03298897, 0.044457987, -0.006431306, 0.0025287198, -0.003507796, 0.032562487, -0.034666188, -0.03758733, -0.0037665416, 0.03871838, 0.019063745, 0.0033632186, 0.06698999, -0.043474752, 0.0041355635, 0.03403281, 0.014045894, 0.023527622, -0.019790858, 0.012186912, 0.00414813, -0.037256543, 0.0005918026, 0.012266535, 0.04182252, 0.02347361, -0.022119908, 0.040273786, 0.0017803371, -0.030819142, -0.02357365, 0.009921421, 0.0003760013, -0.0110489195, -0.038555622, -0.029975455, -0.012991932, 0.025079908, -0.0005886532, -0.0048469333, 0.008340206, 0.031728815, -0.070781805, 0.0047662193, -0.026498992, -0.022693617, -0.0028680384, -0.026475282, -0.050944716, 0.0062730527]]

            # TODO: 要配到库里面
            vector_lian_tong = [[0.01150623,-0.02302261,-0.037687935,0.047937706,0.010620993,-0.015002192,-0.064666264,0.033577614,0.00410088,-0.0028396887,0.019784624,-0.026203895,-0.011757609,0.009062141,-0.02485868,-0.011814984,0.048351884,0.015326719,0.031902082,-0.049888592,0.021330902,-0.0018658742,-0.069519304,-0.007096254,-0.0044731908,0.018441271,0.0019579064,0.03650822,0.03797538,-0.04603925,0.011173847,-0.025577545,-0.008013301,0.009273001,0.0033446995,0.029357294,-0.033785485,0.0002904872,0.005848682,-0.026665587,0.014065008,-0.016795712,-0.00345065,-0.013659328,-0.04779976,0.008360463,-0.034529693,0.0053356593,0.03111012,0.0478569,-0.021756604,0.02145374,-0.021664083,0.033655547,0.017339658,0.056400917,0.035992585,-0.0204368,0.025353042,-0.0918614,0.042405,0.06906616,0.02037123,0.0028137804,0.03410365,-0.010363484,0.0016645923,-0.04253094,-0.0034788058,-0.033056203,-0.02470484,0.01160984,0.03777808,0.009508823,0.003843984,0.026272811,-0.011577425,-0.0046541262,0.007785207,0.042325236,-0.020016909,-0.008926344,-0.017142106,0.01290197,0.015539038,-0.01852643,0.019144187,0.33630633,0.051600985,-0.023347892,-0.016291581,-0.012650503,-0.017457627,-0.0142324,0.03293754,0.000010561761,0.013412875,0.025371978,0.022658542,0.0147327725,0.009773881,0.016962606,-0.0039231568,-0.029357526,-0.00029909302,-0.015344987,0.011038301,0.0012958136,-0.010330848,-0.022964107,0.036657076,0.010612985,0.01766597,-0.027259976,-0.0439013,0.0005870657,-0.011574238,0.019323207,0.031398907,-0.0059203957,-0.009364503,0.0026104369,-0.014416915,0.014913153,0.04033028,0.03543499,0.021608334,0.009724705,-0.010677263,-0.042096328,0.060547013,-0.026231049,0.019434616,0.033108164,0.025365507,-0.003944958,-0.03354434,-0.0019441188,-0.00040868754,-0.032935422,0.025999524,-0.0001094587,-0.0023728302,0.0037377,0.0032717453,-0.0075839916,-0.04921517,0.023541242,-0.039861668,-0.010547483,0.0058788983,-0.025643155,-0.025245,-0.014223887,-0.018262658,-0.02212606,-0.014713535,-0.007008831,-0.0024029843,0.011384641,-0.01950712,-0.008091218,0.038699515,0.043784257,-0.015371105,0.013881976,-0.039770056,0.022795655,0.039910056,0.0057091857,-0.037923362,-0.071556225,-0.018025642,0.020305198,-0.011023606,-0.05813126,0.011356586,0.03926702,0.017908523,0.0053479183,-0.022349272,0.0006899378,-0.009061718,-0.020297136,-0.05288088,-0.011113765,0.029903002,0.020224825,0.014301285,-0.032744836,-0.009133669,0.019517064,-0.027690794,0.008715779,0.018513039,0.040831752,-0.0056626606,0.0034701573,0.05279489,-0.05040529,-0.005066076,0.0044130743,-0.008900842,-0.0160581,-0.013401248,-0.057437606,0.06725316,0.017509032,0.036410496,-0.00024843815,-0.053277865,-0.028287798,0.005332539,-0.019048337,0.011500759,0.023333779,0.0016938249,0.060012072,-0.03834563,-0.025483048,0.017192246,-0.026393939,0.02416718,-0.030949641,0.011653098,0.00036351418,0.024482694,-0.008474224,-0.037319053,0.040179245,-0.016044367,-0.016244065,0.05080939,0.03147784,0.00051138806,0.031575605,0.017120346,-0.0060381936,-0.05654727,-0.03495392,-0.05344756,0.027395178,0.013721531,-0.012466503,0.03569571,0.01272383,0.046752803,0.014863618,0.019108044,0.021911537,0.037317213,0.026971081,-0.03345913,-0.033338625,-0.027675794,-0.0040176488,0.03721942,0.021285554,-0.018253699,-0.023556983,-0.015016299,-0.022703197,-0.03153004,-0.009467463,0.023716418,0.048000388,0.0066156145,0.01060696,0.025856324,0.028589413,0.0429181,-0.000106737876,-0.0061899666,-0.020664312,0.00045190574,-0.015902082,-0.04930199,0.015288039,0.0057936907,-0.0029886956,0.0036245356,0.008227386,0.013338124,-0.016215254,0.041870303,0.00799194,-0.033570837,0.0074834265,0.004494026,-0.018409807,-0.042530954,0.021940947,-0.030819414,-0.009802898,0.032945655,0.06505045,0.016572192,0.0012045706,0.0024723457,-0.011390218,0.019606002,0.020250367,0.07044089,0.011641029,0.04546839,-0.026824716,0.0025952947,-0.014388117,-0.014439757,0.0061135916,-0.027679816,-0.09733192,-0.003344852,-0.06798579,0.07494445,0.0024119546,-0.0014677483,-0.007614225,0.01327524,0.07139278,-0.005081568,0.010936121,-0.044086475,-0.046940114,0.013737684,0.035291184,0.010710963,-0.022760117,-0.016384995,0.02607935,0.021138143,0.026864938,0.012508541,-0.043547515,0.028920311,0.045364313,0.035573173,-0.0022664147,-0.044261336,0.033965286,0.040537763,-0.072762236,-0.00712253,-0.050556865,-0.023762172,0.027541619,-0.0136918565,-0.044636708,-0.020720862,-0.00035682006,0.028359063,-0.01593809,0.0032184634,-0.041886125,0.01814132,-0.006833134,0.030340038,-0.013378852,0.030098893,0.084629774,-0.034177687,0.008600087,0.03134691,-0.052638404,0.01290454,-0.019099982,-0.043045025,0.0046113795,-0.04609442,0.0058497735,0.035030965,0.051178057,-0.01982686,-0.004110178,-0.0045915996,0.023090761,0.0041346233,-0.031881638,0.002890092,0.0325868,0.009715476,0.02276444,-0.03384049,-0.0013176776,0.0050012646,-0.010209694,0.015513381,-0.013876096,-0.06328145,-0.012132039,-0.024608873,0.0064175706,-0.07288581,-0.033378802,-0.047326427,0.023679424,-0.023794847,-0.016496226,-0.034776147,-0.008998101,-0.0058408948,-0.002361062,-0.008585764,-0.020041516,0.025243727,-0.021336095,0.0070946575,0.03818944,0.009115791,-0.028531965,-0.011180096,-0.01508084,-0.008990946,0.002885606,0.013140075,0.074331544,0.014983647,-0.04465699,-0.019463632,-0.013192309,0.016259117,0.024922596,0.010532237,0.021850392,0.006719371,-0.03102838,-0.034257963,-0.031080835,-0.023233363,-0.048720516,0.011462777,0.03485775,-0.0061566154,-0.0047444412,0.0470211,-0.009865246,-0.00963067,0.0054844515,-0.025671974,0.0653092,-0.030049877,-0.005263704,0.025158139,-0.0058195745,-0.015447485,0.04455354,0.003817828,-0.09289377,-0.004565721,0.014260439,0.0015121802,0.047128502,-0.016443027,-0.015013517,0.024349306,0.006574497,0.014145291,-0.022445004,-0.033548903,0.035067987,0.048228677,-0.0086061945,0.0016349666,-0.012954377,-0.00088433654,-0.011089135,-0.057768565,-0.016408835,0.042282175,0.0134838745,-0.010610416,-0.023932543,-0.010047146,0.0041991025,-0.027167508,0.017001245,-0.02480312,-0.0023879376,-0.03397442,-0.016620014,-0.015224644,0.023288174,-0.021706004,-0.015008535,0.038077354,0.015799647,-0.0037064434,0.030019235,0.03498294,-0.024229994,0.019124074,-0.025119541,-0.05184074,0.0007436745,0.042200483,-0.008655414,-0.021515436,-0.02043916,-0.06909169,-0.008265866,0.0066847554,0.0431908,0.051668372,0.015402543,0.004133796,-0.061286718,-0.064896435,0.019285541,-0.009102872,0.0154604595,0.023454262,-0.031734146,-0.01570806,0.022004997,-0.0035954325,-0.00928422,0.048054777,-0.032564975,0.03017622,-0.00021305642,-0.0025098454,0.0024651417,0.035498768,-0.021955969,-0.03716672,0.00085836404,-0.020853933,0.02232964,-0.026342569,-0.04344399,-0.04901982,-0.032359913,0.011647696,0.030802693,-0.006372726,-0.0011085368,-0.012550529,0.020160358,-0.022758642,-0.018876437,0.007458328,-0.0033148096,-0.005316534,0.007099392,0.025133096,0.046174794,-0.018647142,0.005010124,0.058978405,0.020715682,-0.01863979,0.012174203,0.0032822504,-0.03716447,-0.00022320413,0.05135261,-0.0012703137,0.027292687,-0.013611759,0.0076758224,-0.023236545,0.016850868,0.02722846,-0.06067392,0.034218565,0.009651117,0.014424739,0.038881112,0.009277172,0.019702667,-0.02782214,0.022251455,-0.03265693,-0.0055131023,0.009000621,0.0062703616,-0.0024394367,-0.019691184,-0.10643125,0.056671817,0.044534925,-0.023275698,0.032305896,-0.040281564,-0.016255138,0.0015827194,0.035941,0.021466011,-0.040893313,-0.04416472,-0.02248252,-0.0031500824,0.007355768,-0.017676383,0.01596072,0.00063746545,-0.004605859,-0.0060283276,-0.012752957,-0.036480926,-0.023651097,0.023256885,-0.06481655,-0.02299142,-0.027616723,-0.019860055,-0.005592537,0.029536678,-0.008314269,-0.0052371537,0.013527156,0.0026030866,0.0048694867,0.021445472,-0.0047783693,-0.03618536,0.026048698,-0.0013829668,0.004758105,0.0098598255,0.029717581,0.002605161,0.0024198596,-0.016595338,-0.002445667,-0.027688662,-0.011886242,0.05336369,-0.06660592,-0.008108246,-0.008320745,0.010897184,0.048125695,0.019299986,-0.028905833,0.013966926,-0.058391362,0.01472291,-0.044089932,-0.04565581,0.014956861,-0.0057039983,-0.0009900556,-0.02355566,0.022123424,-0.024347527,-0.020249862,0.00016513417,-0.020239824,-0.009187258,-0.021102143,0.006822908,-0.023718774,0.028258285,-0.028221484,0.020929625,-0.040933535,0.0003660444,0.029765842,-0.01251863,0.022626517,0.048403896,0.03503283,0.05941984,-0.0430655,-0.020379867,0.010891855,-0.0023187196,-0.01933901,0.0023655305,0.03044702,0.032861304,0.013865151,0.03882667,0.037579793,0.023413425,-0.004591976,0.006699096,-0.032033615,0.02569704,-0.04595199,-0.051792305,0.01704419,0.017345976,0.07462267,-0.043488547,-0.026691867,0.013053384,0.068290815,-0.019510638,0.0050650956,0.030364256,-0.0021257903,-0.0003027643,0.0027415853,0.0055037164,0.031763207,-0.0263174,-0.008925221,-0.017336855,-0.067691445,0.01694363,-0.026615987,-0.023585428,0.004222028,0.06705943,0.053800732,0.031178312,0.011851708,0.019398909,-0.014452097,-0.054510627,-0.014845346,0.03508335,-0.031022351,0.056077242,0.008396285,0.024386615,-0.0116807865,0.032398727,0.012267926,-0.007208101,-0.041443016,0.020240989,0.021215871,-0.081098564,0.014962747,-0.032489907,0.043405052,-0.046361048,0.021298314,-0.057268072,-0.04597301,-0.026400264,0.009725182,-0.040670276,-0.00045202105,-0.02793809,0.0075936522,-0.012690978,-0.03793289,0.023523213,-0.014601495,0.044739965,-0.017559221,-0.0147366095,-0.009050204,-0.023664407,-0.055226438,0.01877049,0.023029841,-0.009812418,-0.017200148,-0.037934348,0.013821995,-0.0030113389,0.0059593613,-0.008103123,0.04226157,-0.0009332276,-0.0089570675,0.007943706,0.014451687,-0.0691358,0.0081310235,-0.012621587,-0.011729794,-0.0014071184,-0.040817928,0.055448543,0.0067438213,0.029791705,-0.024163727,0.033823583,-0.03628947,-0.031871118,0.072815925,0.0003992429,0.023524389,0.019489365,-0.004092487,-0.028246673,-0.0017947712,-0.043128047,-0.053107277,-0.0015521541,-0.014214619,0.025897501,0.006509122,-0.0407001,-0.043351498,-0.013445098,0.023094697,-0.060402226,-0.020948235,0.028726162,-0.04154145,0.043682884,0.008741609,-0.012288175,0.006415307,0.026113847,-0.08592557,-0.015931679,-0.02170134,0.0045695994,-0.022915302,0.0004341853,-0.07607125,-0.015515239,-0.046075165,-0.013865888,0.029175567,-0.00640611,0.012824566,0.022684662,0.024795473,-0.0063694823,0.027193025,0.010272228,0.005479124,0.014054096,-0.0071662394,-0.039610866,0.016150255,0.038780715,0.016035063,-0.046739873,-0.044324003,-0.0042133722,-0.03085196,0.015799148,-0.0578558,-0.011563414,-0.058675624,-0.012099796,-0.026219957,-0.021165,0.04145738,-0.018538967,-0.036643606,0.01185333,0.030360729,-0.09124043,0.013614531,0.006021981,0.044622313,0.03632135,0.060622975,0.025945656,-0.006937045,-0.055147216,0.0070322845,-0.038757674,-0.028417962,-0.027156733,0.060414694,0.02730794,0.013762256,-0.041082602,-0.014609533,-0.0012032054,0.037178382,-0.04394004,0.018416904,-0.05440136,-0.0072044428,-0.023449013,-0.019789904,0.005864074,0.020943994,-0.008866558,0.012882584,0.033245847,0.022490075,0.029075798,-0.050820384,-0.00953376,0.0026942634,0.016770305,0.03989868,0.027047783,0.0016227192,0.030393692,0.0037644235,-0.023087386,-0.016665963,0.0033433433,0.02718024,0.013313808,0.043338004,-0.029962197,-0.03768118,0.0053158305,-0.022704313,-0.011536963,0.033037115,-0.007343963,0.01207734,-0.011645034,-0.057158995,0.01660962,0.014629758,-0.004348963,-0.036153488,0.056057815,-0.0009632222,-0.063254215,-0.025600696,0.0031186223,0.051486574,0.021628436,0.040939488,-0.019906197,-0.021861041,-0.013134581,-0.006792921,-0.009676729,0.03128348,-0.020532815,-0.03451633,0.03767357,0.015848316,0.05021745,0.029001422,-0.004444003,0.022003707,0.013749823,-0.031738088,-0.0030108995,-0.035424132,0.0234096,0.016966514,-0.010601323,0.03776821,0.03565113,-0.005590802,-0.029753165,0.009424868,0.03354489,0.048704356,0.014131305,-0.0075436505,-0.043952294,0.047262475,-0.03329961,-0.011939431,-0.009119936,0.046017036,-0.005993096,0.004233755,-0.037631556,-0.0012913338,-0.018091539,0.032928623,0.028810753,-0.032088015,-0.061210815,-0.010728425,0.0036066454,0.018146208,0.0027341295,0.0189417,0.029317524,-0.026072985,-0.060536463,0.006067193,-0.019533249,-0.023847809,-0.007557973,0.008871953,0.03570165,0.047948245,-0.0051421826,-0.03298898,0.044457894,-0.0064313384,0.0025286837,-0.0035077794,0.03256255,-0.034666155,-0.03758732,-0.0037665134,0.038718358,0.019063786,0.003363215,0.06699001,-0.04347474,0.0041355602,0.034032863,0.014045898,0.023527663,-0.01979088,0.012186861,0.004148116,-0.03725654,0.00059178995,0.012266474,0.041822493,0.0234736,-0.022119854,0.040273745,0.0017803183,-0.030819176,-0.023573676,0.009921368,0.00037599966,-0.011048913,-0.03855555,-0.029975504,-0.01299193,0.02507992,-0.0005886295,-0.0048469775,0.008340237,0.031728823,-0.070781805,0.0047662472,-0.026499009,-0.022693593,-0.0028680244,-0.026475267,-0.050944746,0.006273083]]

          

            payload = json.dumps({
                "inputs": '你好',
            })
            # token = request.headers.get("token")
            # url = settings.JUSURE_910B_EMBEDDINGS_API + "embed"
            url = model_desc.get('model_url') + "embed"

            if '223' in url:
                vector = vector_lian_tong
            task_logger.info(f"url:{url}")
            try: 
                response = requests.request("POST", url, headers=headers, data=payload)
                if response.status_code == 200:
                    data = response.json()
                   
                    if json.dumps(data) == json.dumps(vector):
                        model.set_model_url_status(model_desc.get('url_id'), 0)
                    else:
                        task_logger.info(f"Post jusure 910B embedding srv failed: 向量结果不一致")
                        model.set_model_url_status(model_desc.get('url_id'), 1)
            except Exception as e:
                task_logger.info(f"Post jusure 910B embedding srv failed: {e}")

    def del_knowledge_doc_chunk(self, chunk_id):
        return self.model.update_knowledge_doc_chunk(chunk_id, {'delete_flag': settings.DELETE_FLAG_TRUE})

    def update_knowledge_doc_chunk(self, chunk_id, args):
        return self.model.update_knowledge_doc_chunk(chunk_id, args)

    def update_knowledge_doc_chunk_batch(self, chunk_ids, args):
        return self.model.update_knowledge_doc_chunk_batch(chunk_ids, args)

    def update_knowledge_doc_chunk_batch_by_tokenlist(self, chunk_ids, tokens, results):
        return self.model.update_knowledge_doc_chunk_batch_by_tokenlist(chunk_ids, tokens, results)

    def get_knowledge_doc_chunk_list_by_knowledge(self, knowledge_id, args):
        return self.model.get_knowledge_doc_chunk_list_by_knowledge(knowledge_id, args)

    def get_index_name(self, args):
        model_path = ''
        if args.get('knowledge_id'):
            model_path = self.get_knowledge_detail(args['knowledge_id'])['model_path']
        if args.get('doc_id'):
            model_path = self.get_knowledge_document_detail(args['doc_id'])['model_path']
        if args.get('chunk_id'):
            model_path = self.get_knowledge_doc_chunk_detail(args['chunk_id'])['model_path']
        if model_path:

            return self.index_name + model_path
        else:
            raise ValueError(StatusMap['es_index_name_err'])

    def del_es_knowledge(self, args):
        delete_query = {
            "query": {
                "term": args
            }
        }
        index_name = self.get_index_name(args)
        try:
            res = self.es.delete_data(index_name, delete_query)

            return res
        except Exception as e:
            logger.info(e)
            return True
    
    def bulk_del_knowledge_chunk(self, doc_id, chunk_ids):
        del_list = []
        del_list.append((doc_id, chunk_ids))

        new_doc_id = self.get_knowledge_document_detail_by_doc_source_id(doc_id)
        new_chunk_ids = KnowledgeController().get_document_chunk_id_by_chunk_source_ids(chunk_ids)
        if new_doc_id and new_chunk_ids:
            del_list.append((new_doc_id, new_chunk_ids))
        for doc_id_one,chunk_id_one in del_list:
            new_res = KnowledgeController().bulk_del_es_knowledge_chunk(doc_id_one, {'chunk_id':chunk_id_one})
            if new_res:
                new_chunk_ids = KnowledgeController().update_knowledge_doc_chunk_batch(chunk_id_one, {'delete_flag': settings.DELETE_FLAG_TRUE})
            else:
                logger.error(f"Failed to del doc_id {doc_id_one}  chunk_ids {chunk_id_one}")
                return False
        return True
    
    def bulk_del_es_knowledge_chunk(self, did, args):
        
        delete_query = {
            "query": {
                "terms": args
            }
        }
        try:
            index_name = self.get_index_name({'doc_id': did})
        except:
             return False
        return self.es.delete_data(index_name, delete_query)

    def bulk_del_es_knowledge(self, kid, args):
        delete_query = {
            "query": {
                "terms": args
            }
        }
        try:
            index_name = self.get_index_name({'knowledge_id': kid})
        except:
            try:
                index_name = self.get_index_name({'doc_id': args.get('doc_id', [0])[0]})
            except:
                return
        return self.es.delete_data(index_name, delete_query)

    def update_es_knowledge(self, query, status):
        update_query = {
            "query": {
                "term": query
            },
            "script": {
                "source": "ctx._source.status = params.status",
                "lang": "painless",
                "params": {
                    "status": status
                }

            }
        }
        index_name = self.get_index_name(query)
        return self.es.update_data(index_name, update_query)

    def update_es_knowledge_by_chunk(self, query, description, character_count):
        index_name = self.get_index_name(query)
        model_path = index_name.replace(self.index_name, '')
        emb_data = self.create_embeddings(description, self.corpid, model_path or self.default_local_emb_model_path, is_search=True)
        vector = emb_data['vector']
        update_query = {
            "query": {
                "term": query
            },
            "script": {
                "source": """
                ctx._source.description = params.description;
                ctx._source.character_count = params.character_count;
                ctx._source.tokens = params.tokens;
                ctx._source.vector = params.vector;
                """,
                "lang": "painless",
                "params": {
                    "description": description,
                    "character_count": character_count,
                    "tokens": emb_data['total_tokens'],
                    "vector": vector
                }

            }
        }

        self.es.update_data(index_name, update_query)
        return emb_data['total_tokens']

    def group_es_knowledge_search(self, message, knowledge_ids, size, min_score):
        knowledge_group = self.get_knowledge_ids_by_model(knowledge_ids)
        ret = list()
        content = ""
        for knowledge_ids in knowledge_group:
            ret.extend(self.search_es_knowledge(None, message, knowledge_ids, size, min_score, is_search=True))
        for one in sorted(ret, key=lambda x: x['_score'], reverse=True)[:size]:
            content += f'### {one["_source"]["description"]}\n'
        return content

    def search_es_knowledge(self, task, input_text, knowledge_ids, size=5, min_score=1.4, is_search=False, doc_ids=[]):
        # logger.info(f"=== search_es_knowledge === knowledge_ids: {knowledge_ids}")
        try:
            if doc_ids:
                index_name = self.get_index_name({'doc_id': doc_ids[0]})
            elif knowledge_ids:
                index_name = self.get_index_name({'knowledge_id': knowledge_ids[0]})
            else:
                return []
            model_path = index_name.replace(self.index_name, '')
            emb_data = self.create_embeddings(input_text, self.corpid, model_path, is_search=is_search)
            vector = emb_data['vector']
            body_dict = {
                "_source": {
                    "includes": ["chunk_id", "doc_id", "knowledge_id", "content_id", "description", "character_count", "tokens", '_score']
                },
                "query":
                    {"script_score": {
                        "query": {
                            "bool": {
                                "must": [
                                    {"term": {
                                        "status": {
                                            "value": True
                                        }
                                    }
                                    },
                                    {
                                        "term": {
                                            "delete_flag": {
                                                "value": False
                                            }
                                        }
                                    }
                                ]
                            }
                        },
                        "min_score": min_score,
                        "script": {
                            "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
                            "params": {
                                "query_vector": vector

                            }
                        }
                    }
                    },
                "size": size
            }
            if knowledge_ids:
                body_dict['query']['script_score']['query']['bool']['must'].append({"terms": {"knowledge_id": knowledge_ids}})
            if doc_ids:
                body_dict['query']['script_score']['query']['bool']['must'].append({"terms": {"doc_id": doc_ids}})

            # logger.info(f"index_name {index_name}=== body_dict: ", json.dumps(body_dict))
            hits = self.es.search_data(index_name, body_dict)['hits']['hits']
            # 记录命中
            if task is not None:
                task.async_chunk_hits.delay(self.corpid, hits)
            
            logger.info("*" * 35)
            logger.info(f"Search es embeddings knowledge index: {index_name}, min_score: {min_score}, hits: {len(hits)}")
            return hits
        except Exception as e:
            logger.error(e)
            return []

    def search_es_knowledge_v1(self, task, input_text, knowledge_ids, size=5, min_score=1.4, is_search=False):
        try:
            model_path = self.default_local_emb_model_path
            # logger.info(f"Embeding Model: {model_path}")
            emb_data = self.create_embeddings(input_text, self.corpid, model_path, is_search=is_search)
            vector = emb_data['vector']
            body_dict = {
                "_source": {
                    "includes": ["chunk_id", "doc_id", "knowledge_id", "content_id", "description"]
                },
                "query":
                    {"script_score": {
                        "query": {
                            "bool": {
                                "must": [
                                    {
                                        "terms": {"knowledge_id": knowledge_ids}
                                    },
                                    {"term": {
                                        "status": {
                                            "value": True
                                        }
                                    }
                                    },
                                    {
                                        "term": {
                                            "delete_flag": {
                                                "value": False
                                            }
                                        }
                                    }
                                ]
                            }
                        },
                        "min_score": min_score,
                        "script": {
                            "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
                            "params": {
                                "query_vector": vector

                            }
                        }
                    }
                    },
                "size": size
            }

            index_name = self.get_index_name({'knowledge_id': knowledge_ids[0]})
            index_exists = self.es.index_exists(index_name)
            if not index_exists:
                return []
            hits = self.es.search_data(index_name, body_dict)['hits']['hits']
            # task.async_chunk_hits.delay(self.corpid, hits)

            logger.info("*" * 35)
            logger.info(f"Search es embeddings knowledge index: {index_name}, min_score: {min_score}, \nhits: {hits}")
            return hits
        except Exception as e:
            logger.error(e)
            return []

    def get_es_query(self, knowledge_ids, input_vector, size=5, min_score=1.4):
        body_dict = {
            "_source": {
                "includes": ["doc_id", "knowledge_id", "description", "character_count", "tokens"]
            },
            "query":
                {"script_score": {
                    "query": {
                        "bool": {
                            "must": [
                                {
                                    "terms": {"knowledge_id": knowledge_ids}
                                }
                            ]
                        }
                    },
                    "min_score": min_score,
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
                        "params": {
                            "query_vector": input_vector

                        }
                    }
                }
                },
            "size": size
        }

        return body_dict
    def search_es_doc_batch(self, search_list, is_search=False):
        # input_vector, knowledge_ids, size=5, min_score=1.4
        try:
            body_dict_list = [self.get_es_query(**item) for item in search_list]
            # # TODO： 获取index_name 移动到外面
            index_name = self.abstract_index_name + self.default_local_emb_model_path
            return self.es.batch_search_data(index_name, body_dict_list)
        except Exception as e:
            print(e, '==============')
            return []
        
    def build_es_query(self, knowledge_ids, input_vector, size=10, min_score=1.4, doc_ids=[]):
        filter_list =  [
            {"terms": {"knowledge_id": knowledge_ids}},
            {"term": {"status": True}},
            {"term": {"delete_flag": False}}
        ]
        if doc_ids:
            filter_list.append({"terms": {"doc_id": doc_ids}})
        return {
            "_source": ["chunk_id", "doc_id", "knowledge_id", "content_id", "description", "character_count", "tokens"],
            "query": {
                "script_score": {
                    "query": {
                        "bool": {
                            "filter": filter_list
                        }
                    },
                    "min_score": min_score,
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
                        "params": {
                            "query_vector": input_vector
                        }
                    }
                }
            },
            "size": size,
            "track_total_hits": True
        }
        
    def search_es_knowledge_batch(self, search_list):
        batch_search_query_list = [self.build_es_query(**item) for item in search_list]
        # # TODO： 获取index_name 移动到外面
        index_name = self.index_name + self.default_local_emb_model_path
        return self.es.batch_search_data(index_name, batch_search_query_list)

    def search_es_doc_abstract(self, input_text, knowledge_ids, size=5, min_score=1.4, is_search=False):
        # logger.info(f"=== search_es_knowledge === knowledge_ids: {knowledge_ids}")
        ret = dict()
        try:
            model_path = self.default_local_emb_model_path
            emb_data = self.create_embeddings(input_text, self.corpid, model_path, is_search=is_search)
            vector = emb_data['vector']
            body_dict = self.get_es_query(knowledge_ids, vector, size, min_score)
            index_name = self.abstract_index_name + self.default_local_emb_model_path
            # logger.info(f"index_name {index_name}=== body_dict: ", json.dumps(body_dict))
            hits = self.es.search_data(index_name, body_dict)['hits']['hits']

            logger.info("*" * 35)
            logger.info(f"Search es embeddings abstract index: {index_name}, min_score: {min_score}, hits: {hits}")
            for hit in hits:
                ret[hit['_source']['doc_id']] = (hit['_source']['description'], hit['_score'])
            return ret
        except Exception as e:
            logger.error(e)
            return ret


    async def handle_analysis(self, corpid, knowledge_id, ai_func):
        results = {}
        DEFAULT_SAMPLING: SamplingFunc = SamplingStrategy.EVEN_SAMPLING

        async def query_rdb():
            document_id = None
            args = {'page_no': 1, 'page_size': 100}
            if document_id:
                data = KnowledgeController(corpid).get_knowledge_document_detail(document_id)
            else:
                data = KnowledgeController(corpid).get_knowledge_doc_list(knowledge_id, args)
            data_list = data.get('data_list', [])
            results['data_list'] = data_list
            filenames = [data['doc_name'] for data in data_list]

            all_chunks = []
            for data in data_list:
                doc_id = data['document_id']
                chunk_list = self.get_knowledge_doc_chunk_list(doc_id, {'is_all': 'all'})
                all_chunks.extend(chunk_list.get('data_list', []))

            indices = DEFAULT_SAMPLING(len(all_chunks), SAMPLE_SIZE)
            accumulated_data_list = [all_chunks[i] for i in indices]

            texts = [data['content'] for data in accumulated_data_list]

            results['texts'] = texts
            results['filenames'] = filenames

        async def query_vdb():
            count_query = {
                "query": {
                    "term": {
                        "knowledge_id": knowledge_id
                    }
                },
                "size": 0
            }
            index_name = 'knowledge_' + self.default_local_emb_model_path
            count_res = self.es.search_data(index_name, count_query)
            total_docs = count_res['hits']['total']['value']

            indices = DEFAULT_SAMPLING(total_docs, SAMPLE_SIZE)

            embeddings = []

            for idx in indices:
                query_dict = {
                    "query": {
                        "term": {
                            "knowledge_id": knowledge_id
                        }
                    },
                    "size": 1,
                    "from": idx
                }
                res = self.es.search_data(index_name, query_dict)

                if not res or 'hits' not in res or 'hits' not in res['hits']:
                    logger.info(f"Invalid response format for index {idx}: {res}")
                    continue
                hits = res['hits']['hits']
                if hits and len(hits) > 0:
                    if '_source' in hits[0] and 'vector' in hits[0]['_source']:
                        embeddings.append(hits[0]['_source']['vector'])

            results['embeddings'] = embeddings

        await asyncio.gather(query_rdb(), query_vdb())

        knowledge_data = {
            knowledge_id: {
                "texts": results.get('texts', []),
                "filenames": results.get('filenames', []),
                "embeddings": results.get('embeddings', [])
            }
        }

        def process_ranks(knowledge_data):
            return RankJob().process(knowledge_data)

        def handle_meta(data_list, ai_func):
            return MetaInfoProcessor().handle_meta(data_list, ai_func)

        def handle_scores(knowledge_id):
            return SemanticVectorization(self.get_ebd, self.es).handle_analysis(knowledge_id)

        async def main(knowledge_data, data_list, ai_func, knowledge_id):
            ranks, meta_val, scores = await asyncio.gather(
                asyncio.to_thread(process_ranks, knowledge_data),
                asyncio.to_thread(handle_meta, data_list, ai_func),
                asyncio.to_thread(handle_scores, knowledge_id)
            )
            return ranks, meta_val, scores

        # ranks, meta_val, scores = asyncio.run(main(knowledge_data, results.get('data_list', []), ai_func, knowledge_id))
        # try:
        #     loop = asyncio.get_event_loop()
        # except RuntimeError:
        #     loop = asyncio.new_event_loop()
        #     asyncio.set_event_loop(loop)
        #
        # ranks, meta_val, scores = loop.run_until_complete(
        #     main(knowledge_data, results.get('data_list', []), ai_func, knowledge_id))
        ranks, meta_val, scores = await main(knowledge_data, results.get('data_list', []), ai_func, knowledge_id)

        # val = self.get_knowledge_individual(knowledge_id)
        self.model.delete_knowledge_individual(knowledge_id)

        # 提取 dict 中的字段
        ranks = ranks.get(knowledge_id, None)
        knowledge_name = "knowledge_name"
        chunk_size = ranks.get('recommended_chunk_size', None)
        recall_size = ranks.get('recommended_top_k', None)
        recall_threshold = ranks.get('recommended_threshold', None)
        strategy = ranks.get('strategy', None)

        features = ranks.get('features', None)
        total_chars = features.get('total_chars') if features else None
        total_documents = features.get('total_documents') if features else None
        avg_doc_length = features.get('avg_doc_length') if features else None
        avg_sentence_length = features.get('avg_sentence_length') if features else None
        vocabulary_size = features.get('vocabulary_size') if features else None
        keyword_density = features.get('keyword_density') if features else None
        noun_ratio = features.get('noun_ratio') if features else None
        avg_cosine_similarity = features.get('avg_cosine_similarity') if features else None
        diversity_score = features.get('diversity_score') if features else None
        text_complexity_score = features.get('text_complexity_score') if features else None
        avg_paragraphs_length = features.get('avg_paragraphs_length') if features else None
        avg_paragraphs = features.get('avg_paragraphs') if features else None
        estimate_density = features.get('estimate_density') if features else None
        classify_scores = scores
        cluster_info = meta_val

        self.model.add_knowledge_individual(
            knowledge_id=knowledge_id,
            knowledge_name=knowledge_name,
            chunk_size=chunk_size,
            recall_size=recall_size,
            strategy=strategy,
            total_chars=total_chars,
            total_documents=total_documents,
            avg_doc_length=avg_doc_length,
            avg_sentence_length=avg_sentence_length,
            vocabulary_size=vocabulary_size,
            keyword_density=keyword_density,
            noun_ratio=noun_ratio,
            avg_cosine_similarity=avg_cosine_similarity,
            diversity_score=diversity_score,
            text_complexity_score=text_complexity_score,
            classify_scores=classify_scores,
            cluster_info=cluster_info,
            recall_threshold=recall_threshold,
            avg_paragraphs_length=avg_paragraphs_length,
            avg_paragraphs=avg_paragraphs,
            estimate_density=estimate_density
        )


    def get_knowledge_individual(self, knowledge_id):
        knowledge_individual = self.model.get_knowledge_individual(knowledge_id)
        return knowledge_individual


    def get_ebd(self, input_text):
        model_path = "bge-large-zh-v1.5"
        emb_data = self.create_embeddings(input_text, self.corpid, model_path, is_search=False)
        vector = emb_data['vector']
        return vector

    def search_es_gt_demo_knowledge(self, input_text, size=1, min_score=1.2, is_search=False, app_id=None, qa_list=None, need_all=False):
        # logger.info(f"=== search_es_knowledge === knowledge_ids: {knowledge_ids}")

        def inner(model_path, qa_list=None):
            emb_data = self.create_embeddings(input_text, self.corpid, model_path, is_search=is_search)
            vector = emb_data['vector']
            body_dict = {
                "_source": {
                    "includes": ["question", "answer", 'qa_modal']
                },
                "query":
                    {"script_score": {
                        "query": {
                            "match_all": {}
                        },
                        "min_score": min_score,
                        "script": {
                            "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
                            "params": {
                                "query_vector": vector

                            }
                        }
                    }
                    },
                "size": size
            }

            if qa_list:
                body_dict['query']['script_score']['query'] = {
                    "bool": {
                        "must": [
                            {
                                "terms": {"qa_lib_id": qa_list}
                            }
                        ]
                    }
                }
            index_name = 'knowledge_gt_demo_qa_' + model_path
            hits = self.es.search_data(index_name, body_dict)['hits']['hits']
            return hits

        try:
            all_hits = list()
            model_path = self.default_local_emb_model_path
            qa_list = qa_list or []
            if app_id:
                app = AppOrm(self.corpid)
                app_info = app.get_app_by_id(app_id)
                min_score = app_info.qa_score if app_info.qa_score else min_score
                qa_list = app.get_app_qa_lib_rels(app_id)
                qa_list.extend(app.get_app_qa_lib_rels(app_id))

            if qa_list:
                qa_model = QaOrm(self.corpid)
                cache_data = []
                for one in qa_list:
                    model_path = qa_model.get_lib_detail({"qa_lib_id": one}).get('aigc_model', {}).get('model_path', model_path)
                    if model_path not in cache_data:
                        all_hits += inner(model_path, qa_list)
                        cache_data.append(model_path)
            else:
                all_hits += inner(model_path)
            sort_hits = sorted([dict(score=hit['_score'], **hit['_source']) for hit in all_hits], key=lambda x: x['score'], reverse=True)
            if sort_hits:
                if need_all:
                    return sort_hits
                return sort_hits[0]
            else:
                return {}
        except Exception as e:
            logger.error(e)
            return {}

    def search_es_work_like_doc_ids(self, input_text, size=1000, knowledge_id=None, doc_id=None, is_light=False):
        try:
            body_dict = {
                "_source": {
                    "includes": ["chunk_id", 'doc_id']
                },
                "query":
                    {"bool": {
                        "must": [
                            {
                                "match_phrase": {
                                    "description": {
                                        "query": input_text,
                                        "slop": len(input_text) * 5
                                    }
                                }
                            }
                        ]
                    }
                },
                "size": size
            }
            if knowledge_id:
                body_dict['query']['bool']['must'].append({'term': {'knowledge_id': knowledge_id}})
                index_name = self.get_index_name({'knowledge_id': knowledge_id})
            elif doc_id:
                body_dict['query']['bool']['must'].append({'term': {'doc_id': doc_id}})
                index_name = self.get_index_name({'doc_id': doc_id})
            else:
                return []
            if is_light:
                body_dict['highlight'] = {
                    "fields": {"description": {"fragment_size": len(input_text) * 2}},  # 需要高亮显示的字段
                    "pre_tags": ["<font color='red'>"],  # 高亮前缀
                    "post_tags": ["</font>"],  # 高亮后缀
                    "fragment_size": 12
                }
                body_dict['_source']['includes'].append('description')
            hits = self.es.search_data(index_name, body_dict)['hits']['hits']
            return hits
        except Exception as e:
            logger.error(e)
            return []

    def search_es_doc_abstract_full_text(self, input_text, size=5, min_score=0.35):
        # logger.info(f"=== search_es_knowledge === knowledge_ids: {knowledge_ids}")
        try:
            body_dict = {
                "_source": {
                    "includes": ["doc_id", "knowledge_id", "description", "character_count", "tokens"]
                },
                "query": {
                    "bool": {
                        "must": [
                            {
                                "match": {
                                    "description": {
                                        "query": input_text,
                                        "operator": "and"
                                    }
                                }
                            }
                        ]
                    }
                },
                "size": size,
                "min_score": min_score
            }

            index_name = self.abstract_index_name + self.default_local_emb_model_path
            logger.info(f"index_name {index_name}=== body_dict: ", json.dumps(body_dict))
            hits = self.es.search_data(index_name, body_dict)['hits']['hits']

            logger.info("*" * 35)
            logger.info(f"Search es embeddings abstract index: {index_name}, min_score: {min_score}, \nhits: ", len(hits))
            return [hit['_source']['doc_id'] for hit in hits]
        except Exception as e:
            logger.error(e)
            return []

    def search_es_knowledge_full_text(self, task, input_text, knowledge_ids, size=5, min_score=0.2):
        # logger.info(f"=== search_es_knowledge_full_text === knowledge_ids: {knowledge_ids}")
        self.get_index_name({'knowledge_id': knowledge_ids[0]}).replace(self.index_name, '')
        # logger.info(f"=== search_es_knowledge_full_text end === knowledge_ids: {knowledge_ids}")
        try:
            body_dict = {
                "_source": {
                    "includes": ["chunk_id", "doc_id", "knowledge_id", "content_id", "description"]
                },
                "query": {
                    "bool": {
                        "must": [
                            {
                                "terms": {"knowledge_id": knowledge_ids}
                            },
                            {
                                "match": {
                                    "description": {
                                        "query": input_text,
                                        "operator": "and"
                                    }
                                }
                            },
                            {
                                "term": {
                                    "status": {
                                        "value": True
                                    }
                                }
                            },
                            {
                                "term": {
                                    "delete_flag": {
                                        "value": False
                                    }
                                }
                            }
                        ]
                    }
                },
                "size": size,
                "min_score": min_score,
            }
            index_name = self.get_index_name({'knowledge_id': knowledge_ids[0]})
            hits = self.es.search_data(index_name, body_dict)['hits']['hits']
            task.async_chunk_hits.delay(self.corpid, hits)

            logger.info("*" * 35)
            logger.info(f"Search es full text knowledge index: {index_name}, min_score: {min_score}, \nhits: {hits}")
            return hits
        except Exception as e:
            logger.error(e)
            return []
    
    def search_es_knowledge_full_text_v1(self, task, input_text, knowledge_ids, size=5, min_score=0.2):
        try:
            body_dict = {
                "_source": {
                    "includes": ["chunk_id", "doc_id", "knowledge_id", "content_id", "description", "character_count", "tokens"]
                },
                "query": {
                    "bool": {
                        "must": [
                            {
                                "terms": {"knowledge_id": knowledge_ids}
                            },
                            {
                                "match": {
                                    "description": {
                                        "query": input_text,
                                        "operator": "or",
                                        "analyzer": "ik_max"
                                    }
                                }
                            },
                            {
                                "term": {
                                    "status": {
                                        "value": True
                                    }
                                }
                            },
                            {
                                "term": {
                                    "delete_flag": {
                                        "value": False
                                    }
                                }
                            }
                        ]
                    }
                },
                "size": size,
                "min_score": min_score,
                "highlight": {
                    "fields": {
                        "description": {}  # 需要高亮显示的字段
                    },
                    "pre_tags": ["<font color='red'>"],  # 高亮前缀
                    "post_tags": ["</font>"],  # 高亮后缀
                    "fragment_size": 12
                }
            }

            index_name = self.get_index_name({'knowledge_id': knowledge_ids[0]})
            search_result = self.es.search_data(index_name, body_dict)
            hits = search_result['hits']['hits']

            logger.info("*" * 35)
            logger.info(f"Search es full text highlight knowledge index: {index_name}, min_score: {min_score}, \nhits: {hits}")
            return hits
        except Exception as e:
            logger.info(e)
            return []
        
        
    def calculate_match_score(self, score, max_score=12.0):
        """
        计算命中百分比
        :param score: 当前文档的得分
        :param max_score: 假设的最大得分
        :return: 百分比字符串，例如 '95.37%'
        """
        if score > max_score:
            return "100%"
        percentage = (score / max_score) * 100
        return f"{percentage:.2f}%"

    def calculate_match_score_v2(self, score, max_score=12.0):
        """
        计算命中分值
        :param score: 当前文档的得分
        :param max_score: 假设的最大得分
        :return: 百分比字符串，例如 '95.37%'
        """
        if score > max_score:
            return 100
        percentage = (score / max_score)
        return percentage

    def update_knowledge_run_record(self, query, update):
        return self.mg.update_knowledge_run_record(query, update)

    def add_knowledge_doc_chunk_in_batch(self, doc_chunks, batch_size=100):
        """
        :param doc_chunks:
        :param batch_size:
        :return:
        """
        return self.model.add_knowledge_doc_chunk_in_batch(doc_chunks, batch_size)

    def add_chunk_images_in_batch(self, chunk_image_list, batch_size=100):
        return self.model.add_chunk_images_in_batch(chunk_image_list, batch_size)

    def add_chunk_image_in_batch(self, chunk_image_list, batch_size=100):
        return self.model.add_chunk_image_in_batch(chunk_image_list, batch_size)

    def get_knowledge_run_record(self, query):
        return self.mg.get_knowledge_run_record(query)

    def get_content_info(self, content_id):

        data = ContentInfoOrm(self.corpid).get_content_train_data(content_id)

        content = "名称：%s" % data['content_name']
        if data['key_words']:
            content += "\n关键字：%s" % data['key_words']
        if data['comment']:
            content += "\n备注：%s" % data['comment']
        if data['publish_writing'] or data['copywriting']:
            content += "\n文案：%s" % data['publish_writing'] + data['copywriting']
        if data['content_tags']:
            content += "\n标签：%s" % data['content_tags']

        return data['content_name'], content, data['info']

    def update_rel_content_knowledge(self, content_id, knowledge_id, tp_user_id):
        return ContentInfoOrm(self.corpid).update_rel_content_knowledge(content_id, knowledge_id, tp_user_id)

    def check_rel_content_knowledge(self, content_id, knowledge_id):
        return ContentInfoOrm(self.corpid).check_rel_content_knowledge(content_id, knowledge_id)

    def del_rel_content_knowledge(self, content_id, knowledge_id):
        return ContentInfoOrm(self.corpid).del_rel_content_knowledge(content_id, knowledge_id)

    def add_chunk_hits(self, chunk_id, hit_count):
        return self.model.add_chunk_hit_count(chunk_id, hit_count)

    def get_knowledge_ids(self, args):
        return self.model.get_knowledge_ids(args)

    def get_knowledge_ids_by_model(self, knowledge_ids=None, doc_ids=None):
        if knowledge_ids is None:
            knowledge_ids = []
        user_info = self.user_info
        auth_scheme_ids = SchemeController().check_role_permit(user_info.get('user_id'), user_info.get('role_ids'))
        return self.model.get_knowledge_ids_by_model(auth_scheme_ids, knowledge_ids, doc_ids)

    def get_content_train_data_list(self, next_time):
        content_orm = ContentInfoOrm(self.corpid)
        content_list = content_orm.get_content_train_data_list(next_time)
        data_list = []
        for data in content_list:

            content = "名称：%s" % data['content_name']
            if data['key_words']:
                content += "\n关键字：%s" % data['key_words']
            if data['comment']:
                content += "\n备注：%s" % data['comment']
            if data['publish_writing'] or data['copywriting']:
                content += "\n文案：%s" % data['publish_writing'] + data['copywriting']
            if data['content_tags']:
                content += "\n标签：%s" % data['content_tags']

            data_list.append({
                'content_name': data['content_name'],
                'content': content,
                'doc_list': data['info'] or json.dumps([{'url': x['url']} for x in content_orm.get_content_detail_list(data['content_id'])]),
                'content_id': data['content_id'],
                'publish_status': data['publish_status'],
                'add_time': data['add_time'],
                'content_type_id': data['content_type_id']
            })
        return data_list

    def search_es_content_last(self):

        body_dict = {
            "_source": {
                "includes": ["content_id", "add_time", "character_count", "publish_status", "description", "delete_flag"]
            },
            "query": {
                "match_all": {}
            },
            "sort": [
                {
                    "add_time": {
                        "order": "desc"
                    }
                }
            ],
            "size": 1
        }
        index_name = self.content_index_name
        try:
            res = self.es.search_data(index_name, body_dict)

            hits = res['hits']['hits']
            if len(hits) == 0:
                return 'all'
            else:
                return hits[0]['_source']['add_time']
        except Exception as e:
            return 'all'

    def search_es_content_graph_last(self):

        body_dict = {
            "_source": {
                "includes": ["content_id", "content_chunk_id", "add_time", "description" ]
            },
            "query": {
                "match_all": {}
            },
            "sort": [
                {
                    "add_time": {
                        "order": "desc"
                    }
                }
            ],
            "size": 1
        }
        logger.info(json.dumps(body_dict))
        index_name = self.content_graph_index_name + self.default_local_emb_model_path
        try:
            res = self.es.search_data(index_name, body_dict)

            hits = res['hits']['hits']
            if len(hits) == 0:
                return 'all'
            else:
                return hits[0]['_source']['add_time']
        except Exception as e:
            return 'all'

    def search_es_knowledge_doc_graph_last(self):

        body_dict = {
            "_source": {
                "includes": ["doc_id", "chunk_id", "add_time", "description" ]
            },
            "query": {
                "match_all": {}
            },
            "sort": [
                {
                    "add_time": {
                        "order": "desc"
                    }
                }
            ],
            "size": 1
        }
        logger.info(json.dumps(body_dict))
        index_name = self.knowledge_graph_index_name + self.default_local_emb_model_path
        try:
            res = self.es.search_data(index_name, body_dict)

            hits = res['hits']['hits']
            if len(hits) == 0:
                return 'all'
            else:
                return hits[0]['_source']['add_time']
        except Exception as e:
            return 'all'

    def search_es_content(self, input_text, size=10, min_score=1.4, is_search=False):

        model_path = self.default_model_path
        emb_data = self.create_embeddings(input_text, self.corpid, model_path, is_search=is_search)
        vector = emb_data['vector']
        body_dict = {
            "_source": {
                "includes": ["content_id", "description"]
            },
            "query":
                {"script_score": {
                    "query": {
                        "bool": {
                            "must": [
                                # {"term": {
                                #     "publish_status": {
                                #         "value": 0
                                #     }
                                # }
                                # },
                                {
                                    "term": {
                                        "delete_flag": {
                                            "value": 0
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "min_score": min_score,
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
                        "params": {
                            "query_vector": vector

                        }
                    }
                }
                },
            "size": size
        }
        index_name = self.content_index_name
        try:
            hits = self.es.search_data(index_name, body_dict)['hits']['hits']
            logger.info("*" * 35)
            logger.info(f"Search es embedding content index: {index_name}, min_score: {min_score}, \nhits: {hits}")
            return hits
        except Exception as e:
            return []

    def search_es_content_full_text(self, input_text, size=10, min_score=0.35):
        body_dict = {
            "_source": {
                "includes": ["content_id", "description"]
            },
            "query": {
                "bool": {
                    "must": [
                        {
                            "match": {
                                "description": {
                                    "query": input_text,
                                    "operator": "or",
                                    "analyzer": "ik_max"
                                }
                            }
                        },
                        {
                            "term": {
                                "delete_flag": {
                                    "value": 0
                                }
                            }
                        }
                    ]
                }
            },
            "size": size,
            "min_score": min_score
        }
        
        index_name = self.content_index_name
        try:
            hits = self.es.search_data(index_name, body_dict)['hits']['hits']
            logger.info("*" * 35)
            logger.info(f"Search es full text content index: {index_name}, min_score: {min_score}, \nhits: {hits}")
            return hits
        except Exception as e:
            logger.error(f"Error during Elasticsearch search: {e}, Time: {datetime.now()}")
            return []

    def save_es_embeddings(self, description, emb_data, model_path):

        model_info = self.model.get_ai_model_by_path(model_path)
        if model_info and emb_data['code'] == 0:
            index_name = self.vector_all_index_name + model_path
            mapping = {
                "properties": {

                    "description": {
                        "type": "text"
                    },
                    "total_tokens": {
                        "type": "integer"
                    },

                    "vector": {
                        "type": "dense_vector",
                        "dims": model_info['dims'],
                        "index": True
                    }
                }
            }
            data = {
                "description": description,
                "total_tokens": emb_data['total_tokens'],
                "vector": emb_data['vector']
            }
            self.es.insert_data(index_name, data, mapping)

    def search_es_vector_all(self, description, model_path):

        body_dict = {
            "_source": {
                "includes": ["total_tokens", "vector"]
            },
            "query": {
                "match_phrase": {
                    "description": description
                }
            },
            "size": 1
        }
        index_name = self.vector_all_index_name + model_path
        try:
            res = self.es.search_data(index_name, body_dict)

            hits = res['hits']['hits']
            if len(hits) == 0:
                return None
            else:
                return hits[0]['_source']
        except Exception as e:
            return

    def search_es_content_by_content_ids(self, content_ids):

        content_ids = ContentInfoOrm(self.corpid).get_content_ids_by_ids(content_ids)
        if not content_ids:
            return []
        body_dict = {
            "_source": {
                "includes": ["content_id", "description", "content_type_id"]
            },
            "query": {
                "bool": {
                    "must": [
                        {
                            "terms": {
                                "content_id": content_ids
                            }
                        },
                        # {
                        #     "term": {
                        #         "publish_status": {
                        #             "value": 0
                        #         }
                        #     }
                        # },
                        {
                            "term": {
                                "delete_flag": {
                                    "value": 0
                                }
                            }
                        }
                    ]
                }
            }
        }
        index_name = self.content_index_name
        try:
            res = self.es.search_data(index_name, body_dict)

            hits = res['hits']['hits']
            if len(hits) == 0:
                return []
            else:
                return hits
        except Exception as e:
            return []

    def del_es_content(self, content_ids):

        body_dict = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "terms": {
                                "content_id": content_ids
                            }
                        }
                    ]
                }
            }
        }
        index_name = self.content_index_name
        self.es.delete_data(index_name, body_dict)

    def get_similar_material(self, content):
        emb_data = self.create_embeddings(content, self.corpid)
        if emb_data['code'] != 0:
            return []
        vector = emb_data['vector']
        start = 100
        body_dict = {
            "_source": {
                "includes": ["content_id"]},
            "query": {
                "script_score": {
                    # 查询固定条件 delete_flag = False
                    "query": {"term": {"delete_flag": {"value": 0}}},
                    # 无固定查询条件
                    # "query": "match_all": {},
                    # 分数要求, 1.4-精确匹配, 1.1-宽松匹配
                    "min_score": 1.1,
                    "script": {
                        # 向量取余弦计算分数
                        "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",  # +1 规避计算分数结果为负数
                        "params": {"query_vector": vector}}}},
            # "aggs": {
            #     "group_content_id": {
            #         "terms": {
            #             "field": "content_id"
            #         }
            #     }
            # },
            # 指定查询的条数
            "size": start}
        # logger.info(json.dumps(body_dict))
        search_res = self.es.search_data(self.content_index_name, body_dict)
        # content_ids = [item['key'] for item in search_res['aggregations']['group_content_id']['buckets']]
        content_ids = [item['_source']['content_id'] for item in search_res['hits']['hits']]
        content_ids = list(set(content_ids))
        return content_ids
    
    def get_doc_info(self, doc_id):
        return self.model.get_doc_info(doc_id)

    def search_es_content_description(self, content_id):
        body_dict = {
            "_source": {
                "includes": ["content_id", "description", "character_count", "tokens"]
            },
            "query": {
                "bool": {
                    "must": [
                        {
                            "term": {
                                "content_id": {
                                    "value": content_id
                                }
                            }
                        }
                    ]
                }
            }
        }

        index_name = self.content_index_name
        try:
            hits = self.es.search_data(index_name, body_dict)['hits']['hits']
            return [item['_source'] for item in hits]
        except Exception as e:
            return []

    def del_es_content_info(self, args):
        delete_query = {
            "query": {
                "term": args
            }
        }
        index_name = self.content_index_name
        res = EsSdkPool(self.corpid).delete_data(index_name, delete_query)
        return res

    def document_splitter_v1_batch(self, oss_url, chunk_size=256, chunk_overlap=20, rows_per_chunk=1):
        """
        :param oss_url: OSS 文件的 URL
        :param chunk_size: 非 Excel 文件的切片大小
        :param chunk_overlap: 非 Excel 文件的切片重叠大小
        :param rows_per_chunk: Excel 文件每个 chunk 对应的行数
        :return: 包含文本和图片 URL 的字典
        """
        try:
            oss_url = urllib.parse.unquote(oss_url)
            bucket_name = oss_url.split('/')[2].split('.')[0]
            oss_util = OSSUtil(corp_id=self.corpid)
            save_path = settings.OSS_DIR + bucket_name + urllib.parse.urlparse(oss_url).path
            file_path = oss_util.download_file(oss_url, save_dir=save_path)
            # ocr = RapidOCR()

            document_dict = {'txt', 'pdf', 'docx', 'doc', 'xlsx', 'xls', 'md', 'jpg', 'png', 'jpeg', 'gif'}
            file_name = urllib.parse.unquote(urllib.parse.urlparse(oss_url).path.split("/")[-1])
            suffix = file_name.split('.')[-1]

            if suffix not in document_dict:
                return {'code': 400, 'message': '文件格式错误，错误格式【%s】支持格式 %s' % (suffix, document_dict)}

            data_list = []
            images_list = []
            split_documents = True
            doc_img_list = []
            current_time = datetime.now().strftime('%Y%m%d%H%M%S')

            if suffix in {'xlsx', 'xls'}:
                # 处理 Excel 文件，按行进行分块
                excel_parser = ExcelParser(file_path)
                records = excel_parser.parse(column_indices=[3, 4, 5])

                chunked_records = [records[i:i + rows_per_chunk] for i in range(0, len(records), rows_per_chunk)]

                for chunk in chunked_records:
                    chunk_texts = []
                    chunk_images = []

                    for record in chunk:
                        chunk_texts.extend(record['texts'])
                        if 'images' in record:
                            # 处理并上传图片到 OSS，并保存图片的 URL
                            folder_path = os.path.join(settings.BASE_DIR, f"upload/image_files/{current_time}", f"row_{record['row']}")
                            os.makedirs(folder_path, exist_ok=True)
                            for i, image_bytes in enumerate(record['images']):
                                image_name = f"image_{i + 1}.png"
                                image_path = os.path.join(folder_path, image_name)

                                # 保存图片到本地
                                with open(image_path, 'wb') as img_file:
                                    img_file.write(image_bytes)
                                task_logger.info(f"Saved image {i + 1} to {image_path}")

                                # 上传图片到阿里云 OSS
                                oss_path = f"{current_time}/row_{record['row']}/{image_name}"
                                res = oss_util.put_object_from_file(oss_path, image_path)
                                oss_url = urllib.parse.unquote(res.resp.response.url)
                                task_logger.info(f"Uploaded image {i + 1} to OSS: {oss_url}")
                                chunk_images.append(oss_url)

                    chunk_data = {
                        'content': " ".join(chunk_texts),
                        'character_count': sum(len(text) for text in chunk_texts)
                    }
                    data_list.append(chunk_data)
                    images_list.append(chunk_images if chunk_images else [])

            else:
                # 处理非 Excel 文件，按字符数进行分块
                if suffix == 'txt':
                    loader = TextLoader(file_path, 'gbk')
                    try:
                        docs = loader.load()
                    except Exception as e:
                        loader = TextLoader(file_path, 'utf-8')
                elif suffix == 'pdf':
                    loader = PyMuPDFLoader(file_path)
                elif suffix == 'docx':
                    loader = Docx2txtLoader(file_path)
                    output_folder_path = os.path.join(settings.BASE_DIR, f"upload/knowledge_doc_image_files/{get_snowflake_id()}")
                    doc_img_list = extract_images_from_docx(file_path, output_folder_path)
                elif suffix == 'doc':
                    loader = UnstructuredWordDocumentLoader(file_path)
                elif suffix in ['jpg', 'png', 'jpeg', 'gif']:
                    headers = {
                            "Content-Type": "application/json",
                        }
                    payload = json.dumps({
                        'url': oss_url
                    })
                    return_dict = return_ocr_result(headers, payload)
                    if return_dict.get('code') == 400:
                        return return_dict
                    docs = return_dict.get('data')
                    # response = requests.request('GET', JUSURE_OCR_API, headers=headers, data=payload)
                    # if response.status_code == 200:
                    #     docs = response.json()['text']
                    # else:
                    #     return {'code': 400, 'message': 'ocr识别失败'}
                    # result, _ = ocr(file_path)
                    # docs = '\n'.join([line[1] for line in result])
                    split_documents = False
                else:
                    loader = UnstructuredFileLoader(file_path)

                text_splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
                if split_documents:
                    documents = loader.load()
                else:
                    documents = text_splitter.create_documents([docs])

                texts = text_splitter.split_documents(documents)
                for text in texts:
                    data_list.append({
                        'content': text.page_content,
                        'character_count': len(text.page_content)
                    })
                # 处理文档中的图片
                if doc_img_list:

                    # logger.info(doc_img_list)
                    for img_url in doc_img_list:
                        # 上传图片到阿里云 OSS
                        oss_path = f"knowledge_doc_image_files/{get_snowflake_id()}/{img_url}"
                        res = oss_util.put_object_from_file(oss_path, img_url)
                        doc_img_oss_url = urllib.parse.unquote(res.resp.response.url)
                        headers = {
                            "Content-Type": "application/json",
                        }
                        payload = json.dumps({
                            'url': oss_url
                        })

                        return_dict = return_ocr_result(headers, payload)
                        if return_dict.get('code') == 400:
                            return return_dict
                        docs = return_dict.get('data')

                        # response = requests.request('GET', JUSURE_OCR_API, headers=headers, data=payload)
                        # if response.status_code == 200:
                        #     docs = response.json()['text']
                        # else:
                        #     return {'code': 400, 'message': 'ocr识别失败'}
                        # result, _ = ocr(img_url)
                        # docs = '\n'.join([line[1] for line in result])

                        documents = text_splitter.create_documents([docs])

                        texts = text_splitter.split_documents(documents)
                        for text in texts:
                            data_list.append({
                                'content': text.page_content,
                                'character_count': len(text.page_content),
                                'doc_img_url': doc_img_oss_url
                            })
                        os.remove(img_url)
                os.remove(file_path)
            return {'code': 0, 'data': data_list, 'images': images_list}
        except Exception as e:
            return {'code': 400, 'message': str(e)}

    def document_splitter_v2_batch(self, path, chunk_size=256, chunk_overlap=20, rows_per_chunk=1):
        """
        :param path: OSS 文件的 URL
        :param chunk_size: 非 Excel 文件的切片大小
        :param chunk_overlap: 非 Excel 文件的切片重叠大小
        :param rows_per_chunk: Excel 文件每个 chunk 对应的行数
        :return: 包含文本和图片 URL 的字典
        """
        # try:
        oss_util = OSSUtil(corp_id=self.corpid)
        file_path = path
        # ocr = RapidOCR()

        document_dict = {'txt', 'pdf', 'docx', 'doc', 'xlsx', 'xls', 'md', 'jpg', 'png', 'jpeg', 'gif'}
        suffix = file_path.split('.')[-1]

        if suffix not in document_dict:
            return {'code': 400, 'message': '文件格式错误，错误格式【%s】支持格式 %s' % (suffix, document_dict)}

        data_list = []
        images_list = []
        split_documents = True
        doc_img_list = []
        current_time = datetime.now().strftime('%Y%m%d%H%M%S')

        if suffix in {'xlsx', 'xls'}:
            # 处理 Excel 文件，按行进行分块
            excel_parser = ExcelParser(file_path)
            records = excel_parser.parse(column_indices=[3, 4, 5])

            chunked_records = [records[i:i + rows_per_chunk] for i in range(0, len(records), rows_per_chunk)]

            for chunk in chunked_records:
                chunk_texts = []
                chunk_images = []

                for record in chunk:
                    chunk_texts.extend(record['texts'])
                    if 'images' in record:
                        # 处理并上传图片到 OSS，并保存图片的 URL
                        folder_path = os.path.join(settings.BASE_DIR, f"upload/image_files/{current_time}", f"row_{record['row']}")
                        os.makedirs(folder_path, exist_ok=True)
                        for i, image_bytes in enumerate(record['images']):
                            image_name = f"image_{i + 1}.png"
                            image_path = os.path.join(folder_path, image_name)

                            # 保存图片到本地
                            with open(image_path, 'wb') as img_file:
                                img_file.write(image_bytes)
                            task_logger.info(f"Saved image {i + 1} to {image_path}")

                            # 上传图片到阿里云 OSS
                            oss_path = f"{current_time}/row_{record['row']}/{image_name}"
                            res = oss_util.put_object_from_file(oss_path, image_path)
                            oss_url = urllib.parse.unquote(res.resp.response.url)
                            task_logger.info(f"Uploaded image {i + 1} to OSS: {oss_url}")
                            chunk_images.append(oss_url)

                chunk_data = {
                    'content': " ".join(chunk_texts),
                    'character_count': sum(len(text) for text in chunk_texts)
                }
                data_list.append(chunk_data)
                images_list.append(chunk_images if chunk_images else [])

        else:
            # 处理非 Excel 文件，按字符数进行分块
            if suffix == 'txt':

                loader = TextLoader(file_path, 'gbk')
                try:
                    docs = loader.load()
                except Exception as e:
                    loader = TextLoader(file_path, 'utf-8')
            elif suffix == 'pdf':
                loader = PyMuPDFLoader(file_path)
            elif suffix == 'docx':
                loader = Docx2txtLoader(file_path)
                # output_folder_path = os.path.join(settings.BASE_DIR, f"upload/knowledge_doc_image_files/{get_snowflake_id()}")
                # doc_img_list = extract_images_from_docx(file_path, output_folder_path)
            elif suffix == 'doc':
                loader = UnstructuredWordDocumentLoader(file_path)
            elif suffix in ['jpg', 'png', 'jpeg', 'gif']:
                headers = {
                    "Content-Type": "application/json",
                }
                payload = json.dumps({
                    'url': oss_url
                })

                return_dict = return_ocr_result(headers, payload)
                if return_dict.get('code') == 400:
                    return return_dict
                docs = return_dict.get('data')
                # response = requests.request('GET', JUSURE_OCR_API, headers=headers, data=payload)
                # if response.status_code == 200:
                #     docs = response.json()['text']
                # else:
                #     return {'code': 400, 'message': 'ocr识别失败'}
                # result, _ = ocr(file_path)
                # docs = '\n'.join([line[1] for line in result])
                split_documents = False
            else:
                loader = UnstructuredFileLoader(file_path)

            text_splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
            if split_documents:
                documents = loader.load()
            else:
                documents = text_splitter.create_documents([docs])

            texts = text_splitter.split_documents(documents)
            for text in texts:
                data_list.append({
                    'content': text.page_content,
                    'character_count': len(text.page_content)
                })
            # 处理文档中的图片
            # if doc_img_list:
            #
            #     # logger.info(doc_img_list)
            #     for img_url in doc_img_list:
            #         # 上传图片到阿里云 OSS
            #         oss_path = f"knowledge_doc_image_files/{get_snowflake_id()}/{img_url}"
            #         res = oss_util.put_object_from_file(oss_path, img_url)
            #         doc_img_oss_url = urllib.parse.unquote(res.resp.response.url)
            #
            #         result, _ = ocr(img_url)
            #         docs = '\n'.join([line[1] for line in result])
            #
            #         documents = text_splitter.create_documents([docs])
            #
            #         texts = text_splitter.split_documents(documents)
            #         for text in texts:
            #             data_list.append({
            #                 'content': text.page_content,
            #                 'character_count': len(text.page_content),
            #                 'doc_img_url': doc_img_oss_url
            #             })
            #         os.remove(img_url)
            # os.remove(file_path)
        return {'code': 0, 'data': data_list, 'images': images_list}
        # except Exception as e:
        #     return {'code': 400, 'message': str(e)}

    def search_es_content_graph(self, input_text, size=12, min_score=1.4, is_search=False):

        model_path = self.default_local_emb_model_path
        emb_data = self.create_embeddings(input_text, self.corpid, model_path, is_search=is_search)
        vector = emb_data['vector']
        body_dict = {
            "_source": {
                "includes": ["content_id", "content_chunk_id", "description"]
            },
            "query":
                {"script_score": {

                    "query": {
                        "match_all": {}
                    },
                    "min_score": min_score,
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
                        "params": {
                            "query_vector": vector

                        }
                    }
                }
                },
            "size": size
        }
        index_name = self.content_graph_index_name + self.default_local_emb_model_path
        try:
            hits = self.es.search_data(index_name, body_dict)['hits']['hits']
            # logger.info("*" * 35)
            return hits
        except Exception as e:
            return []

    def search_es_knowledge_chunk_graph(self, input_text, size=12, min_score=1.4, is_search=False):

        model_path = self.default_local_emb_model_path
        emb_data = self.create_embeddings(input_text, self.corpid, model_path, is_search=is_search)
        vector = emb_data['vector']
        body_dict = {
            "_source": {
                "includes": ["doc_id", "chunk_id", "description"]
            },
            "query":
                {"script_score": {

                    "query": {
                        "match_all": {}
                    },
                    "min_score": min_score,
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
                        "params": {
                            "query_vector": vector

                        }
                    }
                }
                },
            "size": size
        }
        index_name = self.knowledge_graph_index_name + self.default_local_emb_model_path
        try:
            hits = self.es.search_data(index_name, body_dict)['hits']['hits']
            # logger.info("*" * 35)
            return hits
        except Exception as e:
            return []

    def search_es_content_full_text_graph(self, input_text, size=10, min_score=0.35):
        body_dict = {
            "_source": {
                "includes": ["content_id", "content_chunk_id" , "description"]
            },
            "query": {
                "bool": {
                    "must": [
                        {
                            "match": {
                                "description": {
                                    "query": input_text,
                                    "operator": "and"
                                }
                            }
                        }
                    ]
                }
            },
            "size": size,
            "min_score": min_score
        }
        # logger.info(json.dumps(body_dict))
        index_name = self.content_graph_index_name + self.default_local_emb_model_path
        try:
            hits = self.es.search_data(index_name, body_dict)['hits']['hits']
            # logger.info("*" * 35)
            # logger.info(f"Search es full text content index: {index_name}, min_score: {min_score}, \nhits: {hits}")
            return hits
        except Exception as e:
            logger.error(f"Error during Elasticsearch search: {e}, Time: {datetime.now()}")
            return []

    def get_content_info_by_graph(self, content_id, content_chunk_id):

        content_obj = self.content_model.get_content_info_by_id(content_id)
        if not content_obj:
            return {}
        img_list = content_obj.pic_url.split(',')
        data = {
            'img_url': img_list[int(content_chunk_id)],
            'content_name': content_obj.content_name,
            'tp_user_name': content_obj.add_user.tp_user_name,
            'avatar': content_obj.add_user.avatar,
            'add_time': datetime.strftime(content_obj.add_time, format='%Y/%m/%d')
        }

        return data

    def get_knowledge_chunk_image_by_graph(self, chunk_id):
        chunk_img_info = self.model.get_chunk_image_by_id(chunk_id)
        return chunk_img_info


    def get_content_graph_data_list(self, next_time, content_type_id='2001'):
        content_list = ContentInfoOrm(self.corpid).get_content_train_data_list(next_time, content_type_id)
        return content_list
    
    def get_knowledge_doc_chunk_image_list(self, next_time):
        chunk_image_list = self.model.get_chunk_image_by_add_time(next_time)
        return chunk_image_list

    def get_minio_urls(self, relative_path, oss_names_array):
        url_list = []
        for oss_name in oss_names_array:
            # file_name = oss_name.split('_')[-1]
            file_name = oss_name.split('_', 1)[-1]
            bucket_name = settings.KNOWLEDGE_OSS_BUCKET_NAME
            object_name = f"{relative_path}/{oss_name}"
            # http://oss.ai.zhongshuruizhi.com/knowledge-docs/681eb0ba-e93b-48fb-b570-9cbcb1dc9e50/WechatIMG71.png
            presigned_url, oss_url = minio_util.generate_presigned_url(bucket_name, object_name)
            # replace TODO
            # presigned_url = presigned_url.replace('172.22.167.196', '123.56.116.227')
            
            # logger.info("*" * 35)
            # logger.info(f"replace presigned_url: {presigned_url}")
            
            url_list.append({
                "presigned_url": presigned_url,
                "oss_url": oss_url,
                "file_name": file_name,
                "oss_name": oss_name,
                "relative_path": relative_path,
            })
        return url_list
    
    def upload_files_to_minio(self, relative_path, files_hard_path_list):
        doc_list = []
        for file_path in files_hard_path_list:
            file_name = os.path.basename(file_path)
            object_name = f"{relative_path}/{file_name}"
            
            # byte
            file_size = os.path.getsize(file_path)
            
            mime_type, _ = mimetypes.guess_type(file_path)
            if not mime_type:
                mime_type = 'application/octet-stream'
            
            oss_url = minio_util.upload_file_v2(settings.KNOWLEDGE_OSS_BUCKET_NAME, object_name, file_path)
            
            doc_info = {
                'doc_name': file_name,
                'doc_size': file_size,
                'doc_type': mime_type,
                'doc_url': oss_url
            }
            doc_list.append(doc_info)
        
        return doc_list

    def update_knowledge_graph_status(self, knowledge_id:int, graph_status: GraphStatus):
        logger.info(f"update knowledge id: {knowledge_id} , graph_status: {graph_status.name}, value: {graph_status.value}")
        self.update_knowledge(knowledge_id, {'graph_status': graph_status.value})
        return

    def get_knowledge_graph_detail(self, knowledge_id:int):
        # logger.info(f"update knowledge id: {knowledge_id} , graph_status: {graph_status.value}")
        graph_detail = self.model.get_knowledge_graph_detail(knowledge_id)
        return graph_detail

    def add_knowledge_graph(self, knowledge_graph_data):
        new_record_id = self.model.add_knowledge_graph(knowledge_graph_data)
        return new_record_id

    def delete_knowledge_graph(self, knowledge_id:int):
        success = self.model.delete_knowledge_graph(knowledge_id)
        return success

    def update_knowledge_graph(self, graph_id:int, args):
        updated_record = self.model.update_knowledge_graph(graph_id, args)
        return updated_record

    def truncate_knowledge_graph_vdb(self, knowledge_id:int):
        query ={
                "query": {
                    "term": {
                        "knowledge_id": knowledge_id
                    }
                }
            }
        vertex_index = '%s_%s' % (VDB_ENTITIES, DEFAULT_LOCAL_EMB_MODEL_PATH_GRAPH)
        self.es.delete_data(vertex_index, query)
        relation_index = '%s_%s' % (VDB_RELATIONSHIPS, DEFAULT_LOCAL_EMB_MODEL_PATH_GRAPH)
        self.es.delete_data(relation_index, query)
        return
    
    def get_knowledge_galaxy_detail(self, knowledge_id:int):
        # logger.info(f"update knowledge id: {knowledge_id} , galaxy_status: {galaxy_status.value}")
        galaxy_detail = self.model.get_knowledge_galaxy_detail(knowledge_id)
        return galaxy_detail

    def get_knowledge_galaxy_history(self):
        # logger.info(f"update knowledge id: {knowledge_id} , galaxy_status: {galaxy_status.value}")
        galaxy_detail = self.model.get_knowledge_galaxy_history()
        return galaxy_detail

    def add_knowledge_galaxy(self, knowledge_galaxy_data):
        new_record_id = self.model.add_knowledge_galaxy(knowledge_galaxy_data)
        return new_record_id

    def delete_knowledge_galaxy(self, knowledge_id:int):
        success = self.model.delete_knowledge_galaxy(knowledge_id)
        return success

    def update_knowledge_galaxy(self, galaxy_id:int, args):
        updated_record = self.model.update_knowledge_galaxy(galaxy_id, args)
        return updated_record


    def get_knowledge_galaxy_record_detail(self, record_id: int):
        # logger.info(f"get knowledge galaxy record detail, record_id: {record_id}")
        record_detail = self.model.get_knowledge_galaxy_record_detail(record_id)
        return record_detail

    def get_knowledge_galaxy_record_history(self):
        # logger.info(f"get knowledge galaxy record history")
        record_history = self.model.get_knowledge_galaxy_record_history()
        return record_history

    def get_knowledge_galaxy_record_history_filter(self, tp_user_id, size):
        # logger.info(f"get knowledge galaxy record history")
        record_history = self.model.get_knowledge_galaxy_record_history_filter(tp_user_id, size)
        return record_history

    def add_knowledge_galaxy_record(self, knowledge_galaxy_record_data):
        new_record_id = self.model.add_knowledge_galaxy_record(knowledge_galaxy_record_data)
        return new_record_id

    def delete_knowledge_galaxy_record(self, record_id: int):
        success = self.model.delete_knowledge_galaxy_record(record_id)
        return success

    def update_knowledge_galaxy_record(self, record_id: int, args):
        updated_record = self.model.update_knowledge_galaxy_record(record_id, args)
        return updated_record
    

    def get_minio_urls_v1(self, relative_path, oss_names_array):
        url_list = []
        bucket_name = settings.KNOWLEDGE_OSS_BUCKET_NAME
        minio_end_point = settings.MINIO_CONF['endpoint']

        for oss_name in oss_names_array:
            try:
                # 检查是否为 .doc 文件
                if not oss_name.lower().endswith(".doc"):
                    logger.info(f"Skipping non-.doc file: {oss_name}")
                    continue

                # 初始化变量
                file_name = oss_name.split('_', 1)[-1]
                object_name = f"{relative_path}/{oss_name}"
                doc_url = f"{minio_end_point}/{bucket_name}/{object_name}"

                # 下载 .doc 文件并转换为 .docx
                try:
                    doc_file_data = minio_util.download_file_v1(bucket_name, object_name)
                    # 处理 doc的content_type为：text/html 解析获取其中文件
                    doc_file_data = process_mime_doc(doc_file_data)
                    docx_file_data = self.doc_to_docx(doc_file_data,oss_name)

                    # 更新文件名和路径为 .docx
                    oss_name = os.path.splitext(oss_name)[0] + ".docx"
                    object_name = f"{relative_path}/{oss_name}"
                    logger.info(f"Converted .doc to .docx: {doc_url} -> {oss_name}")
                    size = len(docx_file_data)
                    logger.info('11111'*20)
                    logger.info(size)
                    # 上传 .docx 文件到 MinIO
                    up_url = minio_util.upload_to_minio(bucket_name, object_name, docx_file_data)
                except Exception as e:
                    logger.error(f"Error converting .doc to .docx for {oss_name}: {e}")
                    continue

                # 添加到结果列表
                url_list.append({
                    "size": size,
                    "oss_url": up_url,
                    "file_name": file_name,
                    "oss_name": oss_name,
                    "relative_path": relative_path,
                    "original_ursl": doc_url
                })
            except Exception as e:
                # 捕获并记录异常
                logger.error(f"Error processing file {oss_name}: {e}")

        return url_list


    def doc_to_docx(self, doc_file_data, oss_name):
        # 获取当前目录并设置临时文件存储路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        temp_dir = os.path.join(current_dir, 'temp_files')
        logger.info(f"temp_dir: {temp_dir}")

        # 获取当前时间戳
        timestamp = str(int(time.time()))
        # 生成UUID并去掉连字符
        unique_id = str(uuid.uuid4()).replace('-', '')
        # 组合UUID和时间戳
        unique_name = f"{unique_id}_{timestamp}"
        temp_file_path = os.path.join(temp_dir, f"temp_file_{unique_name}.doc")
        logger.info(f"temp_file_path: {temp_file_path}")
        temp_file_path_docx = os.path.join(temp_dir, f"temp_file_{unique_name}.docx")
        logger.info(f"temp_file_path_docx: {temp_file_path_docx}")

        # 创建临时目录（如果不存在）
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)

        # 将传入的 .doc 文件数据写入临时文件
        with open(temp_file_path, "wb") as temp_file:
            temp_file.write(doc_file_data)

        try:
            # 使用 LibreOffice 命令行进行转换
            command = f"soffice --headless --convert-to docx  --outdir {temp_dir} {temp_file_path}"
            logger.info(f"执行命令: {command}")
            
            # 运行命令并检查返回状态
            result = subprocess.run(command, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            if result.returncode != 0:
                logger.info(f"命令执行失败：{result.stderr.decode()}")
                raise Exception(f"Failed to convert {temp_file_path} to docx")

            # 读取转换后的 .docx 文件数据
            with open(temp_file_path_docx, "rb") as docx_file:
                docx_file_data = docx_file.read()

        except Exception as e:
            logger.error(f"Error during conversion: {e}")
            docx_file_data = None

        finally:
            # 删除临时文件
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
            if os.path.exists(temp_file_path_docx):
                os.remove(temp_file_path_docx)

        return docx_file_data

    def modify_md(self, object_name, md_content, doc_id):
        return self.model.modify_md(object_name, md_content, doc_id)
    

    def file2md(self, oss_names_array):
        md_list = []
        for oss_name in oss_names_array:
            bucket_name = settings.KNOWLEDGE_OSS_BUCKET_NAME
            doc_name = oss_name.split("/")[-1]
            object_name = f"knowledge/{doc_name}"   
            content = minio_util.download_file_v1(bucket_name, object_name)
            md_oss_name = os.path.splitext(doc_name)[0] + ".md"
            md_object_name = f"knowledge/{md_oss_name}"

            file_ext = doc_name.split('.')[-1].lower()

            try:
                if file_ext == 'docx':
                    content = convert_docx_by_MarkItDown(content,file_ext,doc_name)
                elif file_ext == 'pptx':
                    content = convert_otherfile_to_markdown(content,file_ext,doc_name)
                elif file_ext == 'pdf':
                    content = convert_otherfile_to_markdown(content,file_ext,doc_name)
                elif file_ext == 'txt':
                    content = convert_otherfile_to_markdown(content,file_ext,doc_name)
                elif file_ext == 'xlsx':
                    content = convert_xlsx_to_markdown(content,doc_name)
                elif file_ext == 'xls':
                    content = convert_xls_to_markdown(content,doc_name)
                else:
                    task_logger.warning(f"不支持的文件格式: {file_ext}")
                md_list.append(minio_util.upload_to_minio(bucket_name, md_object_name, content))
            except Exception as e:
                task_logger.error(f"转换失败: {e}")
                raise e

        return md_list

    def get_minio_urls_xls2xlsx(self, relative_path, oss_names_array):
        url_list = []
        bucket_name = settings.KNOWLEDGE_OSS_BUCKET_NAME
        minio_end_point = settings.MINIO_CONF['endpoint']
        for oss_name in oss_names_array:
            try:
                # 检查是否为 .doc 文件
                if not oss_name.lower().endswith(".xls"):
                    logger.info(f"Skipping non-.xls file: {oss_name}")
                    continue
                # 初始化变量
                file_name = oss_name.split('_', 1)[-1]
                object_name = f"{relative_path}/{oss_name}"
                doc_url = f"{minio_end_point}/{bucket_name}/{object_name}"
                # 下载 .doc 文件并转换为 .docx
                try:
                    doc_file_data = minio_util.download_file_v1(bucket_name, object_name)
                    docx_file_data = xls_to_xlsx(doc_file_data,oss_name)
                    # 更新文件名和路径为 .docx
                    oss_name = os.path.splitext(oss_name)[0] + ".xlsx"
                    object_name = f"{relative_path}/{oss_name}"
                    logger.info(f"Converted .xls to .xlsx: {doc_url} -> {oss_name}")
                    size = len(docx_file_data)
                    # 上传 .docx 文件到 MinIO
                    up_url = minio_util.upload_to_minio(bucket_name, object_name, docx_file_data)
                except Exception as e:
                    logger.error(f"Error converting .xls to .xlsx for {oss_name}: {e}")
                    continue
                # 添加到结果列表
                url_list.append({
                    "size": size,
                    "oss_url": up_url,
                    "file_name": file_name,
                    "oss_name": oss_name,
                    "relative_path": relative_path,
                    "original_ursl": doc_url
                })
            except Exception as e:
                # 捕获并记录异常
                logger.error(f"Error processing file {oss_name}: {e}")
        return url_list

    def get_knowledge_statistic(self, args):
        return self.model.get_knowledge_statistic(args)
    
    def get_all_knowledge_statistic(self, args):
        return self.model.get_all_knowledge_statistic(args)
    
    def increment_search_hit_count(self, knowledge_id):
        return self.model.increment_search_hit_count(knowledge_id)

    def get_cluster_all(self, size):
        collector = DataCollector(self.corpid)
        result = collector.collect_and_get_results(size)
        return result

    def get_cluster_detail(self, cluster_id, size):
        collector = DataCollector(self.corpid)
        data = collector.fetch_cluster_data(cluster_id, True, size)
        id_list = [item['id'] for item in data['documents']]
        result = self.model.get_docs_name_list(id_list)

        id_to_name = {item['doc_id']: item['doc_name'] for item in result}

        for doc in data['documents']:
            doc_id = doc['id']
            doc['doc_name'] = id_to_name.get(doc_id, None)  # 如果找不到可以设为None或其他默认值

        return data

    def fetch_data_similar_outer(self, doc_id, size=50, min_score=0.4):
        collector = DataCollector(self.corpid)
        data = collector.fetch_data_similar(doc_id, size=size, min_score=min_score)

        if not data:
            return []

        id_list = [item['_id'] for item in data]

        result = self.model.get_docs_name_list(id_list)
        id_to_name = {item['doc_id']: item['doc_name'] for item in result}

        formatted_data = []
        for doc in data:
            doc_id = doc['_id']
            source = doc.get('_source', {})

            formatted_doc = {
                'id': doc_id,
                'doc_name': id_to_name.get(doc_id, None),
                'score': doc.get('_score', 0),
                'cluster_id': source.get('cluster_id'),
                # 'reduced_vectors_3d': source.get('reduced_vectors_3d')
            }

            formatted_data.append(formatted_doc)

        return formatted_data

    def add_search_record(self, doc_name, tp_user_id):
        knowledge_galaxy_record_data = {
            'record_id': get_snowflake_id(),
            'state': doc_name,
            'tp_user_id': tp_user_id,
        }
        record_id = self.add_knowledge_galaxy_record(knowledge_galaxy_record_data)
        return record_id

    def fetch_doc_view(self, doc_name, doc_id_list, size=100):
        sql_size = 10000
        if not doc_id_list:
            doc_ids = self.model.get_docs_id_list(doc_name, sql_size)
        else:
            doc_ids = doc_id_list
        collector = DataCollector(self.corpid)
        data = collector.fetch_doc_view(doc_ids, size)
        if not data:
            return []

        id_list = [doc['id'] for doc in data]

        result = self.model.get_docs_name_list(id_list)
        id_to_name = {item['doc_id']: item['doc_name'] for item in result}

        formatted_data = []
        for doc in data:
            doc_id = doc['id']

            formatted_doc = {
                'doc_id': doc_id,
                'doc_name': id_to_name.get(doc_id, None),
                'v': doc['v']
            }

            formatted_data.append(formatted_doc)

        return formatted_data



    def get_keywords_by_knowledge_ids(self, knowledge_ids):
        all_keywords = []
        descriptions_list = self.model.get_descriptions_list_by_knowledge_ids(knowledge_ids)

        key_word_set = set()
        for descriptions in descriptions_list:
            key_word_set.update(descriptions.split(','))
        all_keywords = list(key_word_set)
        return all_keywords



class EmbeddingWrapper(Embeddings):
    def __init__(self, model_path, corpid, tp_user_id):
        """
        :param model_path: 模型路径
        :param embedding_fn_provider: 一个带有 create_BAAI_bge_embeddings 的对象（比如 self）
        """
        self.model_path = model_path
        self.corpid = corpid
        self.embedding_fn_provider = KnowledgeController(corpid)
        self.tp_user_id = tp_user_id

    def embed_fn(self, text):
        if self.model_path in ["bge-large-zh-v1.5", "bge-m3"]:
            res = self.embedding_fn_provider.create_BAAI_bge_embeddings(text, self.model_path)
            return res['vector'][0], res['total_tokens'][0]

        elif self.model_path in ['text-embedding-v1', 'text-embedding-v2']:
            app_key, app_secret = AppOrm(self.corpid).get_key_secret(QIANWEN_CODE)
            res = QianWenModel(app_key).create_embeddings([text], self.model_path)
            return res['output']['embeddings'][0]['embedding'], res['usage']['total_tokens']

        elif self.model_path == 'text-embedding-ada-002':
            res = self.embedding_fn_provider.create_chat_gpt_embeddings(text, self.tp_user_id)
            return res['data']['data'][0]['embedding'], res['data']['usage']['total_tokens']
        else:
            app_key, app_secret = AppOrm(self.corpid).get_key_secret(WENXIN_CODE)
            res = QianFanModel({'Api_Key': app_key, 'Secret_Key': app_secret}).create_embeddings([text], self.tp_user_id, self.model_path)
            return res['data'][0]['embedding'], res['usage']['total_tokens']

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        return [self.embed_fn(text)[0] for text in texts]

    def embed_query(self, text: str) -> List[float]:
        return self.embed_fn(text)[0]
