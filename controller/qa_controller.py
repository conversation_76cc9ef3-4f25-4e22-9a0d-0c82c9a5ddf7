from . import BaseController
from modeler.mysql.qa_orm import QaOrm, QaExtractOrm
from modeler.mysql.app_orm import AppOrm
from controller.knowledge_controller import KnowledgeController
from puppet import es_sdk
from celery_app import celery
import datetime
from utils.tools import get_snowflake_id
class Qa<PERSON><PERSON>roller(BaseController):
    

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.model = QaOrm(self.corpid)
        self.es = es_sdk.EsSdkPool(self.corpid)
    def add_lib(self, data):
        app_orm = AppOrm(self.corpid)
        model = app_orm.get_model(model_id=data.get('aigc_model_id', ''))
        # TODO: mapping 合并
        mapping = {
            "properties": {
                "qa_lib_id": {
                    "type": "keyword"
                },
                "question": {
                    "type": "keyword",
                },
                "answer": {
                    "type": "text",
                },
                "status": {
                    "type": "boolean"
                },
                "file": {
                    "type": "keyword"
                },
                "creator": {
                    "type": "keyword"
                },
                "tokens": {
                    "type": "integer"
                },
                "add_time": {
                    "type": "date",
                    "format": "yyyy-MM-dd HH:mm:ss"
                },
                "update_time": {
                    "type": "date",
                    "format": "yyyy-MM-dd HH:mm:ss"
                },
                "vector": {
                    "type": "dense_vector",
                    "dims": model.dims,
                    "index": True
                }
            }
        }
        # return self.model.add_lib(data)
        self.es.crate_index("knowledge_gt_demo_qa_" + model.model_path, mapping)
        return self.model.add_lib(data)

    def get_lib_list(self, data):
        def query_qa_count(query, qa_path):
            try:
                index_name = "knowledge_gt_demo_qa_" + qa_path
                return self.es.search_data(index_name, query)
            except Exception as e:
                print(f"Error searching index {index_name}: {e}")
                return {'aggregations': {'id_counts': {'buckets': []}}}
        return self.model.get_lib_list(data, query_qa_count)
    
    def modify_lib(self, data):
        return self.model.modify_lib(data)
    def update_status(self, data):
        return self.model.update_status(data)
    def delete_lib(self, qa_lib_id):   
        qa_item_controller = QaItemsController(self.corpid)
        index_name = qa_item_controller.get_index_name(qa_lib_id)
        delete_query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"qa_lib_id": qa_lib_id}}
                    ]
                }
            }
        }
        self.es.delete_data(index_name, delete_query)
        self.es.refresh_index(index_name)
     
        return self.model.delete_lib(qa_lib_id)
    
    def get_lib_detail(self, data):
        return self.model.get_lib_detail(data)
    def insert_qa(self, data):
        print(f"qa_lib_id: {data}")
        detail = self.get_lib_detail({'qa_lib_id': data.get('qa_lib_id')})
        dims = detail.get('aigc_model', {}).get('dims', 0)
        model_path = detail.get('aigc_model', {}).get('model_path', '')
        mapping = {
            "properties": {
                "qa_lib_id": {
                    "type": "keyword"
                },
                "question": {
                    "type": "keyword",
                },
                "answer": {
                    "type": "text",
                },
                "status": {
                    "type": "boolean"
                },
                "file": {
                    "type": "keyword"
                },
                "creator": {
                    "type": "keyword"
                },
                "tokens": {
                    "type": "integer"
                },
                "add_time": {
                    "type": "date",
                    "format": "yyyy-MM-dd HH:mm:ss"
                },
                "update_time": {
                    "type": "date",
                    "format": "yyyy-MM-dd HH:mm:ss"
                },
                "vector": {
                    "type": "dense_vector",
                    "dims": dims,
                    "index": True
                }
            }
        }
        # index_name = "knowledge_gt_demo_qa_" + model_path + '_copy'
        index_name = "knowledge_gt_demo_qa_" + model_path
        self.es.insert_data(index_name, data, mapping)
    def create_embeddings(self, input_text, qa_lib_id):
        controller = KnowledgeController(self.corpid)
        detail = self.get_lib_detail({'qa_lib_id': qa_lib_id})
        model_path = detail.get('aigc_model', {}).get('model_path', '')
        print(f"qa_lib model_path: {model_path}")
        return controller.create_embeddings(input_text, qa_lib_id, model_path)
    
class QaUploadController(BaseController):
    def upload(self, data, task):
        # print(f"qa_lib upload data: {data}")
        result = task.async_qa_embedding.delay(**{**data, "corpid": self.corpid})
        return {'task_id': result.task_id}
    def get_progress(self, data):
        result = celery.AsyncResult(data.get('task_id'))
        total = 0
        current = 0
        success = 0
        status = 'PENDING'
        if result.info:
            try:
                total = result.info.get('total')
                current = result.info.get('current')
                success = result.info.get('success')
            except Exception as e:
                print(f'qa_lib get_progress error: {e}')
        if result.state == 'SUCCESS':
            return {
                'total': result.info.get('total'),
                'current': result.info.get('current'),
                'success':  result.info.get('success'),
                'status': result.state
            }
        return {
            'total': total,
            'current': current,
            'success': success,
            'status': result.state
        }
    

class QaItemsController(BaseController):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.model = QaOrm(self.corpid)
        self.es = es_sdk.EsSdkPool(self.corpid)
       
    def get_index_name(self, qa_lib_id):
        detail = self.model.get_lib_detail({'qa_lib_id': qa_lib_id})
        # index_name = "knowledge_gt_demo_qa_" + detail.get('aigc_model', {}).get('model_path', '') + '_copy'
        index_name = "knowledge_gt_demo_qa_" + detail.get('aigc_model', {}).get('model_path', '')

        mapping = {
            "properties": {
                "qa_lib_id": {
                    "type": "keyword"
                },
                "question": {
                    "type": "keyword",
                },
                "answer": {
                    "type": "text",
                },
                "status": {
                    "type": "boolean"
                },
                "file": {
                    "type": "keyword"
                },
                "creator": {
                    "type": "keyword"
                },
                "tokens": {
                    "type": "integer"
                },
                "add_time": {
                    "type": "date",
                    "format": "yyyy-MM-dd HH:mm:ss"
                },
                "update_time": {
                    "type": "date",
                    "format": "yyyy-MM-dd HH:mm:ss"
                },
                "vector": {
                    "type": "dense_vector",
                    "dims": detail.get('aigc_model', {}).get('dims', ''),
                    "index": True
                }
            }
        }

        self.es.crate_index(index_name, mapping)
        return index_name
    def get_qa_list(self, kwargs):
        content = kwargs.get('content', None)
        start_size = kwargs.get('page_size', 1) * (kwargs.get('page_no', 1) - 1)
        index_name = self.get_index_name(kwargs.get('qa_lib_id'))
        if not content:
            query = {
                "query": {
                    "term": {
                        "qa_lib_id": {"value": kwargs.get('qa_lib_id', "")}
                    }
                },
                "from": start_size,
                "size": kwargs.get('page_size', 10),
                "_source": ["question", "answer", "status", "file", "creator", "tokens", "add_time", "update_time","qa_lib_id","qa_modal"],
                "sort": [
                    {
                        "add_time": {
                            "order": "desc"
                        }
                    }
                ]
            }
        else:
            query = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "term": {
                                    "qa_lib_id": {"value": kwargs.get('qa_lib_id', "")}
                                }
                            },
                            {
                                "regexp": {
                                    "question": f".*{content}.*"
                                }
                            }
                        ]
                    }
                },
                "from": start_size,
                "size": kwargs.get('page_size', 10),
                "_source": ["question", "answer", "status", "file", "creator", "tokens", "add_time", "update_time","qa_lib_id","qa_modal"],
                 "sort": [
                    {
                        "add_time": {
                            "order": "desc"
                        }
                    }
                ]
            }

        result = self.es.search_data(index_name, query)
        return self.handle_es_to_table_list(result)
    def handle_es_to_table_list(self, data=None):
        if not data:
            return {
                'total': 0,
                'data_list': []
            }
        total = data.get('hits', {}).get('total', {}).get("value", 0)
        data_list = data.get('hits', {}).get('hits', [])
        front_list = [{"id": item.get("_id", ""), **item.get("_source", {})} for item in data_list]
        return {
            'total': total,
            'data_list': front_list
        }
    def update_qa_by_id(self, data):
        record_id = data.get('id')
        qa_lib_id = data.get('qa_lib_id')
        question = data.get('question')
        answer = data.get('answer')
        qa_modal = data.get('qa_modal')
        index_name = self.get_index_name(qa_lib_id)
        qa_controller = QaController(self.corpid)
        emb_data = qa_controller.create_embeddings(question, qa_lib_id)
        if emb_data['code'] != 0:
            raise ValueError("生成向量失败")
        update_query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"_id": record_id}},
                        {"term": {"qa_lib_id": qa_lib_id}}
                    ]
                }
            },
            "script": {
                "source": """
                    ctx._source.question = params.question;
                    ctx._source.answer = params.answer;
                    ctx._source.vector = params.vector;
                    ctx._source.update_time = params.update_time;
                    ctx._source.qa_modal = params.qa_modal;
                """,
                "params": {
                    "question": question,
                    "answer": answer,
                    "vector": emb_data['vector'],
                    "qa_modal": qa_modal,
                    "update_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            }
        }
        self.es.update_data(index_name, update_query)
        self.es.refresh_index(index_name)
        return {"status": True}
    def add_qa(self, data):
        qa_lib_id = data.get('qa_lib_id')
        question = data.get('question')
        answer = data.get('answer')  
        creator = data.get('creator') 
        qa_modal = data.get('qa_modal')     
        index_name = self.get_index_name(qa_lib_id)
        qa_controller = QaController(self.corpid)
        emb_data = qa_controller.create_embeddings(question, qa_lib_id)
        if emb_data['code'] != 0:
            raise ValueError("生成向量失败")
 
        data = {
            "qa_lib_id": qa_lib_id,
            "question": question,
            "answer": answer,
            "status": True,
            "file": "",
            "creator": creator,
            "tokens": emb_data['total_tokens'],
            "qa_modal": qa_modal,
            "add_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "update_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "vector": emb_data['vector']
        }
        controller = QaController(self.corpid)
        controller.insert_qa(data)
        self.es.refresh_index(index_name)
        return {"status": True}        
    def get_qa_detail(self, data):
        index_name = self.get_index_name(data.get('qa_lib_id'))
        result = self.es.get_data(index_name, data.get('id'))
        return result.get('_source', {})
    
    def delete_qa_by_id(self, data):
        record_id = data.get('id')
        qa_lib_id = data.get('qa_lib_id')
        index_name = self.get_index_name(qa_lib_id)
        delete_query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"_id": record_id}},
                        {"term": {"qa_lib_id": qa_lib_id}}
                    ]
                }
            }
        }
        self.es.delete_data(index_name, delete_query)
        self.es.refresh_index(index_name)
        return {"status": True}


class QaExtractController(BaseController):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.model = QaExtractOrm(self.corpid)

    def pre_handle_document_list(self, document_list, query):
        has_extracted_list = self.model.get_extracted_list(query)
        if not has_extracted_list:
            return document_list
        extracted_dict = dict()

        for item in has_extracted_list:
            unique_key = str(item.document_id)
            extracted_dict[unique_key] = item
        for doc in document_list:
            doc['extracted'] = False
            extracted = extracted_dict.get(doc.get("document_id"), {})
            if extracted:
                doc['extracted'] = True
                # TODO: 优化,用一个字段
                doc["qa_content"] = extracted.qa_content
                doc['extract_id'] = str(extracted.extract_id)
        return document_list        

    def batch_add_extract(self, qa_list):
        
        handle_list = [
            {   
                "extract_id":get_snowflake_id(), 
                'document_id': item.get('document_id', ''), 
                'knowledge_id': item.get('knowledge_id', ''),
                'qa_content':item.get('qa_content', ''),
                'key_words': item.get('key_words', ''),  
            } for item in qa_list
        ]
        
        self.model.batch_add_extract(handle_list)

        new_extract_dict = dict() 
        for item in handle_list:
            new_extract_dict[item.get('document_id', '')] = str(item.get('extract_id', ''))
        return new_extract_dict
    
    def update_qa_list(self, extract_id, new_qa_content):
        self.model.update_qa_list(extract_id, new_qa_content)