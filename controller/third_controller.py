import json
from utils.tools import get_now_time, md5
from settings import SECRET_KEY_FOR_OS
import requests
from horticulture.record import third_req_log
from controller import BaseController


class ThirdServerController(BaseController):
    def __init__(self, *args):
        super().__init__(*args)
        timestamp = str(get_now_time())
        tp_user_id = self.user_info.get('tp_user_id', 'edb15b16-2443-4314-bea1-06c5b7b944a5')
        sign = md5(f'{timestamp}{tp_user_id}{SECRET_KEY_FOR_OS}')
        self.Header = {'corp-id': 'wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ', "sign": sign,
                       'token': self.token or '836de7116db3d779fb52fe584c7f975a', 'timestamp': timestamp}

    def request(self, method, params, url, stream=False):
        base_params = {'page_size': 100, 'page_no': 1}
        method = method or 'post'
        if method.lower() == 'post':
            resp = requests.post(url, json=params, headers=self.Header, stream=stream)
        else:
            if not params:
                params = base_params
            resp = requests.get(url, params, headers=self.Header, stream=stream)
        return resp

    # 工作流执行结果
    @third_req_log
    def work_flow_ret(self, params, stream=True):
        url = 'http://demo.ai.zhongshuruizhi.com/flow/prod/flow/app/action'
        # url = 'http://127.0.0.1:2020/flow/app/action'
        resp = self.request('post', params, url, stream)

        def stream_run():
            # 逐行处理流式响应数据
            for line in resp.iter_lines():
                if line:
                    str_data = line.decode('utf-8').replace('data: ', '')
                    # 对每一行数据进行解码
                    node_data = json.loads(str_data)
                    if not node_data:
                        continue
                    else:
                        yield node_data

        if resp.status_code == 200:
            if stream:
                return stream_run()
            else:
                ret = resp.json()['data']
                return ret
        else:
            raise ValueError('网络请求异常')

