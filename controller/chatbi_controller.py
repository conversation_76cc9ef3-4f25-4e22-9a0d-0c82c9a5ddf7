# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File: chatbi_controller.py
# @Author: SunLiFei
# @E-mail: <EMAIL>
# @Site: 
# @Time: 2月 17, 2025
# ---
from . import BaseController
from modeler.mongo.chat_mongo import ChatMongo


class ChatbiController(BaseController):

    def __init__(self, *args):
        super().__init__(*args)
        self.chat_mg = ChatMongo(self.corpid)

    def update_chatbi_session_record(self, session_id, record_info):
        return self.chat_mg.update_chatbi_session(session_id, record_info)

    def get_chatbi_session_record(self, session_id):
        return self.chat_mg.get_chatbi_session_record(session_id)
