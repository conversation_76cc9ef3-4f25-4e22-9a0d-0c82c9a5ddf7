


CREATE SPACE IF NOT EXISTS space02(partition_num=10, replica_factor=1, vid_type=FIXED_STRING(45))


-- 创建 organization 类型的点
CREATE TAG organization (
    entity_type STRING,
    description STRING,
    source_id STRING,
    name STRING
);

-- 创建 person 类型的点
CREATE TAG person (
    entity_type STRING,
    description STRING,
    source_id STRING,
    name STRING
);

-- 创建 geo 类型的点
CREATE TAG geo (
    entity_type STRING,
    description STRING,
    source_id STRING,
    name STRING
);

-- 创建 event 类型的点
CREATE TAG event (
    entity_type STRING,
    description STRING,
    source_id STRING,
    name STRING
);

-- 创建 role 类型的点
CREATE TAG role (
    entity_type STRING,
    description STRING,
    source_id STRING,
    name STRING
);

-- 创建 technology 类型的点
CREATE TAG technology (
    entity_type STRING,
    description STRING,
    source_id STRING,
    name STRING
);

-- 创建 concept 类型的点
CREATE TAG concept (
    entity_type STRING,
    description STRING,
    source_id STRING,
    name STRING
);

-- 创建 location 类型的点
CREATE TAG location (
    entity_type STRING,
    description STRING,
    source_id STRING,
    name STRING
);

-- 创建 mission 类型的点
CREATE TAG mission (
    entity_type STRING,
    description STRING,
    source_id STRING,
    name STRING
);



CREATE EDGE DIRECTED
(
    weight FLOAT,
    description STRING,
    keywords STRING,
    source_id STRING
)
COMMENT = 'Edge type description';