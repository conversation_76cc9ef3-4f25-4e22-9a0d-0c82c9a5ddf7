from . import BaseController
from horticulture.cache_decoration import cache_ret
from modeler.mysql.master_orm import CustomerM


class MasterAction(BaseController):

    def __init__(self, *args):
        super(MasterAction, self).__init__(*args)
        self.model = CustomerM(None)

    @cache_ret('get_all_live_organize', -1, 300)
    def get_all_live_organize(self):
        objs = self.model.get_all_customer(status='1')
        ret = list()
        for obj in objs:
            if obj.corpid:
                ret.append({
                    'corpid': obj.corpid,
                    'name': obj.organize.name,
                    'location': obj.organize.location,
                    'corp_sub_industry': obj.organize.corp_sub_industry,
                    'corp_industry': obj.organize.corp_industry,
                    'corp_scale': obj.organize.corp_scale,
                    'corp_wxqrcode': obj.organize.corp_wxqrcode,
                    'subject_type': obj.organize.subject_type,
                    'corp_square_logo_url': obj.organize.corp_square_logo_url,
                    'corp_type': obj.organize.corp_type,
                    'regist_flag': obj.organize.regist_flag,
                    'tenant_id': obj.organize.tenant_id,
                    'work_erp_sn': obj.organize.work_erp_sn,
                    'client_status': obj.organize.client_status,
                    'service_date_bgn': obj.organize.service_date_bgn,
                    'service_date_end': obj.organize.service_date_end
                })
        return ret

    @cache_ret('get_all_corp', -1, 300)
    def get_all_corp(self):
        objs = self.model.get_all_organize()
        ret = list()
        for obj in objs:
            ret.append({
                'corpid': obj.corpid,
                'name': obj.name,
                'location': obj.location,
                'corp_sub_industry': obj.corp_sub_industry,
                'corp_industry': obj.corp_industry,
                'corp_scale': obj.corp_scale,
                'corp_wxqrcode': obj.corp_wxqrcode,
                'subject_type': obj.subject_type,
                'corp_square_logo_url': obj.corp_square_logo_url,
                'corp_type': obj.corp_type,
                'regist_flag': obj.regist_flag,
                'tenant_id': obj.tenant_id,
                'work_erp_sn': obj.work_erp_sn,
                'client_status': obj.client_status,
                'service_date_bgn': obj.service_date_bgn,
                'service_date_end': obj.service_date_end
            })
        return ret

    @cache_ret('get_all_organize', -1, 300)
    def get_all_organize(self):
        objs = self.model.get_all_organize()
        ret = dict()
        for obj in objs:
            if obj.corpid:
                corpid = obj.corpid
                ret[corpid] = {
                    'corpid': obj.corpid,
                    'name': obj.name,
                    'location': obj.location,
                    'corp_sub_industry': obj.corp_sub_industry,
                    'corp_industry': obj.corp_industry,
                    'corp_scale': obj.corp_scale,
                    'corp_wxqrcode': obj.corp_wxqrcode,
                    'subject_type': obj.subject_type,
                    'corp_square_logo_url': obj.corp_square_logo_url,
                    'corp_type': obj.corp_type,
                    'regist_flag': obj.regist_flag,
                    'tenant_id': obj.tenant_id,
                    'work_erp_sn': obj.work_erp_sn,
                    'client_status': obj.client_status,
                    'service_date_bgn': obj.service_date_bgn,
                    'service_date_end': obj.service_date_end
                }
        return ret

    def get_mysql_conf_info(self):

        @cache_ret('get_mysql_conf_info', expire_time=300)
        def inner():
            objs = self.model.get_all_mysql_conf()
            ret = dict()
            for obj in objs:
                corpid = obj.corpid
                ret[corpid] = {
                    'corpid': obj.corpid,
                    'ip': obj.ip,
                    'port': obj.port,
                    'user': obj.user,
                    'password': obj.password,
                    'database': obj.database,
                    'charset': obj.charset,
                    'permission_type': obj.permission_type
                }
            return ret

        return inner().get(self.corpid)

    def get_live_mysql_confs(self, is_json=True):
        objs = self.model.get_all_mysql_conf()
        if is_json:
            ret = dict()
            for obj in objs:
                corpid = obj.corpid
                ret[corpid] = {
                    'corpid': obj.corpid,
                    'ip': obj.ip,
                    'port': obj.port,
                    'user': obj.user,
                    'password': obj.password,
                    'database': obj.database,
                    'charset': obj.charset,
                    'permission_type': obj.permission_type
                }
            return ret
        else:
            return objs

    def get_oss_conf(self):
        @cache_ret('get_oss_conf', expire_time=300)
        def inner():
            # 初始化所有租户公共参数
            middle_dict = dict()

            # 初始化多租户oss加载
            objs = self.model.get_all_oss_conf()
            for obj in objs:
                params = {
                    'access_key_id': obj.access_key_id,
                    'key_secret': obj.key_secret,
                    'domain': obj.domain,
                    'bucket': obj.bucket,
                    'role_arn': obj.role_arn,
                    'endpoint': obj.endpoint,
                    'role': obj.role,
                    'expire_time': obj.expire_time,
                    'region': obj.region
                }
                middle_dict[obj.corpid] = params
            return middle_dict

        all_conf = inner()
        return all_conf.get(self.corpid, {})

    def get_sms_conf(self, use_type):
        @cache_ret('get_sms_conf', expire_time=300)
        def inner():
            # 初始化所有租户公共参数
            middle_dict = dict()

            # 初始化多租户oss加载
            objs = self.model.get_all_sms_conf()
            for obj in objs:
                use_type = obj.use_type
                corp_id = obj.corpid
                params = {
                    "code_login": obj.code_login,
                    "sign_name": obj.sign_name,
                    "sign_chinese_name": obj.sign_chinese_name,
                    "tp_key": obj.tp_key,
                    "url": obj.url,
                    'access_key_id': obj.access_key_id,
                    'key_secret': obj.key_secret,
                }
                if corp_id not in middle_dict:
                    middle_dict[corp_id] = {use_type: params}
                else:
                    middle_dict[corp_id][use_type] = params
            return middle_dict

        all_conf = inner()
        return all_conf.get(self.corpid, {}).get(use_type)

    def get_mongo_conf(self):
        @cache_ret('get_mongo_conf', expire_time=300)
        def inner():
            middle_dict = dict()
            # 初始化多租户mongo加载
            objs = self.model.get_all_mongo_conf()
            for obj in objs:
                corp_id = obj.corpid
                params = {
                    "host": obj.ip,
                    'port': obj.port,
                    'user': obj.user,
                    'password': obj.password,
                    'db': obj.db,
                    'uri': obj.uri,
                    'auth': True if obj.auth else False
                }
                middle_dict[corp_id] = params
            return middle_dict

        all_conf = inner()
        return all_conf.get(self.corpid, {})

    def get_es_conf(self):
        @cache_ret('get_es_conf', expire_time=300)
        def inner():
            middle_dict = dict()
            # 初始化多租户mongo加载
            objs = self.model.get_all_es_conf()
            for obj in objs:
                corp_id = obj.corpid
                params = {
                    "host": obj.ip,
                    'port': obj.port,
                    'user': obj.user,
                    'password': obj.password,
                    'db': obj.db,
                    'db_prefix': obj.db_prefix,
                    'uri': obj.uri,
                    'auth': True if obj.auth else False
                }
                middle_dict[corp_id] = params
            return middle_dict

        all_conf = inner()
        return all_conf.get(self.corpid, {})

    def get_model_conf(self):
        @cache_ret('get_model_conf', expire_time=300)
        def inner():
            middle_dict = dict()
            objs = self.model.get_all_model_conf()
            for obj in objs:
                corp_id = obj.corpid
                for obj2 in objs:
                    middle_dict[corp_id] = {
                        obj2.model_type: {
                            "api_key": obj2.api_key,
                            'secret_key': obj2.secret_key
                        }
                    }
            return middle_dict
        return inner()

