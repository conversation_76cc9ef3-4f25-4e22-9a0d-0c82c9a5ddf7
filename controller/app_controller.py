#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/4/8 11:07
# <AUTHOR> zhangda
# @File    : app_controller.py
# @Comment :
import copy
import datetime
import json
import re
from cv2 import sort
from regex import F, T
import requests
from flask import g
from sympy import im
from puppet.rerank import reranker

from apps.graphrag.exceptions.graph_rag_exception import GraphRagException
from apps.graphrag.configs import SPACE_PREFIX, DEFAULT_AIGC_TYPE_ID, DEFAULT_MODEL_PATH
from settings import IS_910B_EMBEDDINGS, DEFAULT_LOCAL_EMB_MODEL_PATH,RANK_MODEL_URL
from controller.classify_controller import ClassifyController
from controller.chat_view_controller import ChatViewController
from lib_func.const_map import *
from lib_func.logger import logger
from puppet.chat_sdk import AiChatGpt, MyQianWen, My<PERSON>ing, MyQianWenAsync, XFAiModel, AllInOneModel
from utils.tools import datetime_to_dt_str, timestamp_to_dt_str, timestampToDatetime, md5
from controller import BaseController
from modeler.mysql.app_orm import AppOrm, ServiceOrm
from modeler.mysql.content_info_orm import ContentInfoOrm
from modeler.mysql.knowledge_orm import KnowledgeOrm
from modeler.mysql.content_info_orm import ContentInfoOrm
from modeler.mongo.chat_mongo import ChatMongo
from collections import defaultdict
from modeler.mongo.ai_app_mg import AiAppMg
from controller.knowledge_controller import KnowledgeController
from lib_func.type_map import SessionMsgMap, SessionJsonMap, session_msg_table_map
from apps.utils.ques_enhance import QuesEnhancement
from utils.tools import get_formatted_content
from utils.fn_tool import group_by
from controller.third_controller import ThirdServerController
from prompt_template.knowledge import qa_summary_prompt,knowledge_prompt

class AppController(BaseController):

    def __init__(self, *args):
        super(AppController, self).__init__(*args)
        self.model = AppOrm(self.corpid)
        self.mg = AiAppMg(self.corpid)
        self.knowledge = KnowledgeOrm(self.corpid)
        self.model_id = None
        self.content_model = ContentInfoOrm(self.corpid)

    def up_app_rel_flow_app(self, app_id, flow_version_ids):
        self.model.del_app_rel_flow(app_id)
        self.model.add_app_rel_flow(app_id, flow_version_ids)
        return True

    def get_app_rel_flow_app(self, app_id):
        return self.model.get_app_rel_flow(app_id)

    def get_flow_app_detail(self, app_id):
        return self.model.get_app_sample_detail(app_id)

    def get_ai_app(self, app_name, app_desc, app_id=''):
        apps = self.model.get_apps(app_name, app_desc, app_id=app_id)

        app_ids = [str(item.app_id) for item in apps]
        app_prompts = self.model.get_prompt_by_app_ids(app_ids)
        app_prompt_dict = defaultdict(list)
        for p in app_prompts:
            app_prompt_dict[str(p.app_id)].append({'prompt_id': str(p.prompt_id), 'prompt_type': p.prompt_type,
                                                   'prompt_key': p.prompt_key, 'field_name': p.field_name,
                                                   'is_required': p.is_required or 0,
                                                   'prompt_json': json.loads(p.prompt_json) if p.prompt_json else []})
        app_knowledge_ids = self.model.get_knowledge_ids_by_app_ids(app_ids)
        app_knowledge_id_dict = defaultdict(list)
        for k in app_knowledge_ids:
            app_knowledge_id_dict[str(k.app_id)].append({'knowledge_id':str(k.knowledge_id), 'knowledge_name': k.knowledge.knowledge_name, 'icon_url': k.knowledge.icon_url, "model_name": k.knowledge.aigc_model.model_name,'delete_flag': k.knowledge.delete_flag})
        res = []
        for item in apps:
            scheme_info = {}
            if item.auth_scheme_id:
                scheme_info = self.model.get_scheme_info(item.auth_scheme_id)
            app_rel = []
            library_ids = []
            # for detail api
            if app_id != '':
                app_rel = self.model.get_app_rels(app_id)
            #当app_type为3时，获取图表库ids
            if item.app_type == 3:
                library_rels = self.model.get_library_ids_by_app_ids([item.app_id])
                library_ids = [str(rel.library_id) for rel in library_rels]

            qa_rel = self.model.get_app_qa_lib_rels(app_id)    
            try:
                ques_replace = [] if not (ques_replace := getattr(item, 'ques_replace', None)) else json.loads(ques_replace)
            except (json.JSONDecodeError, AttributeError) as e:
                ques_replace = []
            res.append({
                'app_name': item.app_name or '', 'app_id': str(item.app_id), 'app_desc': item.app_desc,
                'icon_url': item.icon_url or '', 'prompt': item.prompt or '', 'status': item.status or 0,
                'auth_scheme_id': item.auth_scheme_id or '', 'aigc_model_id': item.aigc_model_id,
                'add_time': item.add_time.strftime('%Y-%m-%d %H:%M:%S') if item.add_time else '',
                'prompt_info': app_prompt_dict.get(str(item.app_id), []),
                'sensitive_words': item.sensitive_words,
                'knowledge_ids': app_knowledge_id_dict.get(str(item.app_id), []),
                'auth_scheme_info': scheme_info, 'model_name': item.aigc_model.model_name,
                'model_icon': item.aigc_model.model_icon,
                'welcome_content': item.welcome_content or '您好，欢迎来到AI助手！',
                'aigc_type_name': item.aigc_model.aigc_type.aigc_type_name,
                'size': item.size, 'mini_score': item.mini_score, 'is_mixture': item.is_mixture, 'is_graph': item.is_graph,
                'app_type': item.app_type,
                'classify_name': item.classify_name,
                'classify_prompt': item.classify_prompt,
                'classify_priority': item.classify_priority,
                'classify_target': [] if not item.classify_target else item.classify_target.split(","),
                'children': [{'app_id': str(item['app_id']), 'app_name': item['app_name']} for item in app_rel],
                'ques_enabled': item.ques_enabled,
                'ques_prompt': item.ques_prompt,
                'ques_keywords': [] if not item.ques_keywords else item.ques_keywords.split(","),
                'ques_enhance': [] if not item.ques_enhance else item.ques_enhance.split(","),
                'ques_replace': ques_replace, 'digest_size': item.digest_size, 'digest_score': item.digest_score,
                'qa_score': item.qa_score,
                "qa_lib_ids": qa_rel,
                'scope': item.scope,
                'dataset_enhance': item.dataset_enhance,
                'is_rerank': item.is_rerank,
                'rerank_size': item.rerank_size,
                'rarank_ignore_score': item.rarank_ignore_score,
                'global_percent': item.global_percent,
                'is_pic_rerank': item.is_pic_rerank,
                'pic_rarank_ignore_score': item.pic_rarank_ignore_score,
                'parent_directory_id': str(item.parent_directory_id),
                'graph_mode': item.graph_mode,
                'library_ids': library_ids,
                'is_global': item.is_global,
                'global_rerank_num': item.global_rerank_num,
                'need_suggestion': item.need_suggestion,
                'scope_id': item.scope_id,
                'run_mode': item.run_mode,
                'is_multi_qa':item.is_multi_qa,
                'multi_qa_size': item.multi_qa_size,
            })
        return res
    def get_ai_app_v1(self, app_name = '', app_desc= '', knowledge_id= '', page_size='', page_no='', scope=1, scope_id=''):
        return self.model.get_apps_list(app_name, app_desc, knowledge_id, page_size, page_no, scope, scope_id)

    def get_app_list_by_directory_id(self, args):
        return self.model.get_app_list_by_directory_id(args)
    def get_ai_app_v2(self, app_name, app_desc, app_id=''):
        apps = self.model.get_apps(app_name, app_desc, app_id=app_id)
        app_ids = [str(item.app_id) for item in apps]
        app_prompts = self.model.get_prompt_by_app_ids(app_ids)
        app_prompt_dict = defaultdict(list)
        for p in app_prompts:
            app_prompt_dict[str(p.app_id)].append({'prompt_id': str(p.prompt_id), 'prompt_type': p.prompt_type,
                                                   'prompt_key': p.prompt_key, 'field_name': p.field_name,
                                                   'is_required': p.is_required or 0,
                                                   'prompt_json': json.loads(p.prompt_json) if p.prompt_json else []})
        app_knowledge_ids = self.model.get_knowledge_ids_by_app_ids(app_ids)
        app_knowledge_id_dict = defaultdict(list)

        for k in app_knowledge_ids:
            app_knowledge_id_dict[str(k.app_id)].append(str(k.knowledge_id))
        res = []
        for item in apps:
            scheme_info = {}
            if item.auth_scheme_id:
                scheme_info = self.model.get_scheme_info(item.auth_scheme_id)
            res.append({'app_name': item.app_name or '', 'app_id': str(item.app_id), 'app_desc': item.app_desc,
                        'app_type': item.app_type or '',
                        'icon_url': item.icon_url or '', 'status': item.status or 0,
                        'aigc_model_id': item.aigc_model_id,
                        'welcome_content': item.welcome_content or '您好，欢迎来到AI助手！',
                        'add_time': item.add_time.strftime('%Y-%m-%d %H:%M:%S') if item.add_time else '',
                        'knowledge_ids': app_knowledge_id_dict.get(str(item.app_id), []),
                        'size': item.size, 'mini_score': item.mini_score, 'is_mixture': item.is_mixture, 'is_graph': item.is_graph,
                        'digest_size': item.digest_size, 'digest_score': item.digest_score, 'run_mode': item.run_mode
                        })
        return res

    def check_app_name(self, app_name):
        return self.model.get_app_by_name(app_name)

    def check_prompt_drop_down(self, prompt_info):
        for p in prompt_info:
            if p.get('prompt_type') == 'pull_down' and not self.check_prompt_drop_down_item(p.get('prompt_json')):
                return True
        return False

    @classmethod
    def check_prompt_drop_down_item(cls, pjson):
        return len([i for i in pjson if i]) > 0
    def get_app_list_by_auth(self, args):
            return self.model.get_app_list_by_auth(args)
    def create_app(self, **kwargs):
        graph_mode = kwargs.get('graph_mode')
        tp_user_id = kwargs.get('tp_user_id')

        qa_lib_ids = kwargs.pop('qa_lib_ids')
        prompt_info = kwargs.pop('prompt_info')
        library_ids = kwargs.pop('library_ids')
        knowledge_ids = kwargs.pop('knowledge_ids')
        children = kwargs.pop('children')

        self.validate_graph_mode(graph_mode, knowledge_ids)
        app_id = self.model.create_app(**kwargs)
        self.model.create_app_rels(app_id, children, tp_user_id)
        self.model.create_app_qa_lib_rels(app_id, qa_lib_ids, tp_user_id)
        if prompt_info:
            self.model.create_app_prompt(prompt_info, app_id, tp_user_id)
        if knowledge_ids:
            self.model.app_bind_knowledge(knowledge_ids, app_id, tp_user_id)
        if library_ids:
            self.model.app_bind_library(library_ids, app_id, tp_user_id)
        return app_id

    def validate_graph_mode(self, graph_mode, knowledge_ids):
        if graph_mode == 1:
            if not knowledge_ids:
                raise GraphRagException("knowledge is required")
            if len(knowledge_ids) != 1:
                raise GraphRagException("knowledge for graph mode must be one")
            knowledge_id = int(knowledge_ids[0])
            knowledge_controller = KnowledgeController(self.corpid)
            knowledge = knowledge_controller.get_knowledge_detail(knowledge_id)
            if not knowledge:
                raise GraphRagException("knowledge not found")
            if knowledge['graph_enable'] != 1:
                raise GraphRagException("knowledge graph mode not enabled")

    def individual_app_work(self, corpid, scope, knowledge_ids):
        if scope != 1:
            logger.info(f"not individual dataset. scope: {scope}")
            return
        if not knowledge_ids or knowledge_ids[0] is None:
            return
        knowledge_id = knowledge_ids[0]
        from apps.ai import task
        logger.info(f"start individual_app_work task for knowledge_id: {knowledge_id}")
        task.handle_individual_knowledge.delay(corpid, knowledge_id)
        return

    def add_app_tree(self, args):
        return self.model.add_app_tree(args)
    
    def delete_app_tree(self, args):
        app_ids = self.model.get_app_ids_by_directory(args)
        if app_ids:
            for app_id in app_ids:
                self.delete_app(app_id)
        self.model.delete_app_tree(args)
        return app_ids
    
    def get_app_tree(self, args):
        return self.model.get_app_tree(args)

    def check_app_id(self, app_id):
        return self.model.get_app_by_id(app_id)

    def update_app(self, **kwargs):
        tp_user_id = kwargs.get('tp_user_id')
        app_id = kwargs.get('app_id')
        children = kwargs.pop('children')
        qa_lib_ids = kwargs.pop('qa_lib_ids')
        prompt_info = kwargs.pop('prompt_info')
        library_ids = kwargs.pop('library_ids')
        knowledge_ids = kwargs.pop('knowledge_ids')
        
        self.model.update_app(**kwargs)
        self.model.update_app_rels(tp_user_id, app_id, children)
        self.model.update_app_qa_lib_rels(app_id, qa_lib_ids, tp_user_id)
        self.model.create_app_prompt(prompt_info, app_id, tp_user_id)
        if knowledge_ids:
            self.model.app_bind_knowledge(knowledge_ids, app_id, tp_user_id)
        if library_ids:
            self.model.app_bind_library(library_ids, app_id, tp_user_id)

    def delete_app(self, app_id):
        self.model.delete_app(app_id)
        self.model.delete_app_rels(app_id)
        self.model.delete_app_qa_lib_rels(app_id)
        self.model.delete_app_prompt(app_id)
        self.model.delete_app_knowledge_rel(app_id)
        self.model.delete_app_library_rel(app_id)

    def get_prompt_by_app_ids(self, app_ids):

        return self.model.get_prompt_by_app_ids(app_ids)

    def get_ai_app_info(self, app_id, auth_ids=None):
        if type(auth_ids) is list and not auth_ids:
            knowledge_ids = list()
        else:
            rel_knowledge = self.model.get_knowledge_ids_by_app_ids([app_id], auth_ids)
            # TODO: 添加个人知识库且创建者是自己的情况，因为个人类型的空权限查不出来
            knowledge_ids = [str(item.knowledge_id) for item in rel_knowledge]
            # get my knowledge 
            # 取并集
            # knowledge_ids = set(my_knowledge_ids | knowledge_ids)
        app_info = self.model.get_ai_app_detail(app_id)
        app_info.update({'knowledge_ids': knowledge_ids})
        return app_info
    
    def get_ai_app_info_v2(self, app_id, args):
        '''
        获取应用信息，并填充对应有权限的关联知识库id
        '''
        rel_knowledge = self.model.get_knowledge_ids_by_app_ids_v2([app_id], args)
        knowledge_ids = [str(item.knowledge_id) for item in rel_knowledge]
        logger.info(knowledge_ids)
        app_info = self.model.get_ai_app_detail(app_id)
        app_info.update({'knowledge_ids': knowledge_ids})
        return app_info

    def get_app_knowledge_list(self, app_id, auth_ids=None):
        if type(auth_ids) is list and not auth_ids:
            knowledge_list = list()
        else:
            objs = self.model.get_knowledge_ids_by_app_ids([app_id], auth_ids)
            knowledge_list = [{"knowledge_id": str(item.knowledge_id), "knowledge_name": item.knowledge.knowledge_name} for item in objs]
        return knowledge_list

    def get_doc_list_by_session(self, knowledge_list):
        knowledge_ids = [i['knowledge_id'] for i in knowledge_list]
        # logger.info(knowledge_ids)
        data_list = KnowledgeOrm(self.corpid).get_doc_list_by_knowledge_ids(knowledge_ids)
        return data_list

    def get_model_info(self, model_id):
        obj = self.model.get_model(model_id)
        data = {
            'aigc_model_id': obj.aigc_model_id,
            'model_path': obj.model_path,
            'aigc_type_id': obj.aigc_type_id

        }
        return data
    
    def get_app_prompt(self, app_info, prompt_list):
        prompt = app_info['prompt'] or ''

        for item in prompt_list:
            old_str = f'{{{item["prompt_key"]}}}'
            new_str = item['prompt_value']
            prompt += prompt.replace(old_str, new_str)
        return prompt
    def concat_prompt(self, app_prompt = '', question = "", knowledge_content="", graph_content="", abstract_content=""):
        return knowledge_prompt.format(knowledge_content=knowledge_content, question=question, graph_content=graph_content, abstract_content=abstract_content, app_prompt=app_prompt.strip())
    
    def set_qa_prompt(self, prompt_list, message, app_info, qa_list: list[str]):
        """
        设置qa总结提示语
        """
        prompt = self.get_app_prompt(app_info, prompt_list)

        knowledge_prompt = ""
        for idx,item in enumerate(qa_list):
            knowledge_prompt += f"### 答案{idx + 1}: {item}\n"
      
        return qa_summary_prompt.format(answer_content=knowledge_prompt, question=message, app_prompt=prompt.strip())

    def set_first_message(self, task, prompt_list, message, app_info, search_all=False):
        """
        search_all默认为False。如果为True，则在knowledge——ids接口参数为空时进行全文检索。仅适用于智能报告服务。
        """

        chunk_ids_content_map = {}
        chunk_score_map = {}
        chunk_contents_list = []

        
        knowledge_content = ""
        graph_prompt = ""
        abstract_prompt = ""

        prompt = self.get_app_prompt(app_info, prompt_list)
   
        doc_ids = list()
        chunk_ids = list()
        doc_map_abstract = dict()

        if app_info['knowledge_ids'] or search_all:
            mixture_doc_ids = []
            if app_info.get('doc_ids'):
                mixture_doc_ids = app_info['doc_ids']
                logger.info('===========mixture_doc_ids：%s' % mixture_doc_ids)
            elif app_info['is_mixture']:
                digest_size = app_info.get('digest_size') or app_info['size']
                digest_score = app_info.get('digest_score') or 1.9
                doc_map_abstract = KnowledgeController(self.corpid).search_es_doc_abstract(message, app_info['knowledge_ids'], size=digest_size, min_score=digest_score)
                # mixture_doc_ids = KnowledgeController(self.corpid).search_es_doc_abstract_full_text(message, size=app_info['size'], min_score=0.35)
                mixture_doc_ids = list(doc_map_abstract.keys())
                logger.info('===========mixture：%s' % mixture_doc_ids)
                logger.info(mixture_doc_ids)
            
            cache_data = dict()
            index = 1
            retriever_list = []
            if app_info.get('is_rerank', False):
                retriever_list = self.search_and_rerank(task, message, app_info, mixture_doc_ids)
            else:
                retriever_list = self.search_es_knowledge(task, message, app_info['knowledge_ids'], size=app_info['size'],
                                                          min_score=app_info['mini_score'], doc_ids=mixture_doc_ids)
            for item in retriever_list:
                doc_id = item['_source']['doc_id']
                chunk_id = item['_source']['chunk_id']
                _description = item['_source']['description']
                _score = item.get('_score', 0)
                doc_name = cache_data.get(doc_id)
                if not doc_name:
                    try:
                        doc_name = KnowledgeOrm(self.corpid).get_doc_list_by_doc_ids([doc_id])[0]['doc_name']
                    except:
                        doc_name = doc_id
                    finally:
                        cache_data[doc_id] = doc_name
                doc_images_list = KnowledgeController(self.corpid).get_chunk_hits_images([chunk_id])
                knowledge_content += f'### 来源 <<{doc_name}>> 片段[{index}]:{_description} 附件图片: <<{doc_images_list}>> \n'
                doc_ids.append(doc_id)
                chunk_ids.append(chunk_id)
                chunk_contents_list.append(_description)
                index += 1
                if chunk_id not in chunk_ids_content_map:
                    chunk_ids_content_map[chunk_id] = _description
                chunk_score_map[chunk_id] = (_description, _score)
        else:
            knowledge_content += '\n'

        graph_list = []
        
        if app_info.get('is_graph'):
            graph_list = self.knowledge.get_graph_list_by_doc_ids(doc_ids)
            if graph_list:
                logger.info(f"graph_list: {graph_list}")
                graph_prompt += "\n\n## 图谱:\n"
                for item in graph_list:
                    if cache_data:
                        item.update({'doc_name': cache_data.get(item['doc_id'], '')})
                    graph_prompt += f"# {item['graph_content']}\n"
      
        abstract_list = self.knowledge.get_abstract_list(doc_ids)
       
        if abstract_list:
            print(f"abstract_list: {abstract_list}")
            for item in abstract_list:
                abstract_prompt += f"# {item['abstract']}\n"
            abstract_prompt += abstract_prompt
        info = {
            'content': self.concat_prompt(app_prompt=prompt, question=message, knowledge_content=knowledge_content,graph_content=graph_prompt, abstract_content=abstract_prompt),
            'doc_ids': doc_ids,
            'chunk_ids': chunk_ids,
            'chunk_contents_list': chunk_contents_list,
            'chunk_ids_content_map': chunk_ids_content_map,
            'graph_list': graph_list,
            'abstract_list': abstract_list,
            'doc_map_abstract': doc_map_abstract,
            'chunk_score_map': chunk_score_map
        }
        return info

    def set_flow_session_message(self, task, message, knowledge_ids, mini_score, size, other_params):
        knowledge_content = ''
        doc_ids = list()
        chunk_ids = list()
        doc_map_abstract = dict()
        mixture_doc_ids = []
        chunk_ids_content_map = {}
        chunk_score_map = {}
        chunk_contents_list = []
        know_action = KnowledgeController(self.corpid)
        if other_params.get('is_mixture'):
            doc_map_abstract = know_action.search_es_doc_abstract(message, knowledge_ids, size=other_params['digest_size'], min_score=other_params['digest_score'])
            mixture_doc_ids = list(doc_map_abstract.keys())
        cache_data = dict()
        index = 1
        if not IS_910B_EMBEDDINGS and other_params.get('is_rerank'):
            retriever_list, _ = self.search_and_rerank_lib(task, message, knowledge_ids, mini_score, size,
                                                           other_params['global_percent'], other_params['rerank_size'],
                                                           other_params['ignore_score'], mixture_doc_ids)
        else:
            retriever_list = self.search_es_knowledge(task, message, knowledge_ids, size, mini_score, mixture_doc_ids)
        for item in retriever_list:
            doc_id = item['_source']['doc_id']
            chunk_id = item['_source']['chunk_id']
            _description = item['_source']['description']
            _score = item.get('_score', 0)
            doc_name = cache_data.get(doc_id)
            if not doc_name:
                try:
                    doc_name = know_action.model.get_doc_list_by_doc_ids([doc_id])[0]['doc_name']
                except:
                    doc_name = doc_id
                finally:
                    cache_data[doc_id] = doc_name
            doc_images_list = know_action.get_chunk_hits_images([chunk_id])
            knowledge_content += f'### 来源 <<{doc_name}>> 片段[{index}]:{_description} 附件图片: <<{doc_images_list}>> \n'
            doc_ids.append(doc_id)
            chunk_ids.append(chunk_id)
            chunk_contents_list.append(_description)
            index += 1
            if chunk_id not in chunk_ids_content_map:
                chunk_ids_content_map[chunk_id] = _description
            chunk_score_map[chunk_id] = (_description, _score)
        content = knowledge_content
        graph_list = []
        doc_ids = list(set(doc_ids))
        if other_params.get('is_graph'):
            graph_list = self.knowledge.get_graph_list_by_doc_ids(doc_ids)
            graph_prompt = "\n\n ## 知识图谱:\n"
            for item in graph_list:
                item.update({'doc_name': cache_data.get(item['doc_id'], '')})
                graph_prompt += f"# {item['graph_content']}\n"
            content += graph_prompt
        abstract_list = self.knowledge.get_abstract_list(doc_ids)
        if abstract_list:
            abstract_prompt = "\n\n## 摘要:\n"
            for item in abstract_list:
                abstract_prompt += f"# {item['abstract']}\n"
            content += abstract_prompt
        if content:
            content = '\n ## 知识库内容\n' + content
        info = {
            'content': content,
            'doc_ids': doc_ids,
            'chunk_ids': chunk_ids,
            'chunk_contents_list': chunk_contents_list,
            'chunk_ids_content_map': chunk_ids_content_map,
            'graph_list': graph_list,
            'abstract_list': abstract_list,
            'doc_map_abstract': doc_map_abstract,
            'chunk_score_map': chunk_score_map
        }
        return info
    
    def search_and_rerank(self, task, message, app_info, mixture_doc_ids):
        percent = app_info.get('global_percent', 50) / 100
        total_size = app_info.get('rerank_size', 100)
        es_count = int(total_size * percent)
        vector_count = total_size - es_count

        if vector_count:
            vector_list = self.search_es_knowledge(task, message, app_info['knowledge_ids'], size=vector_count,
                                                   min_score=app_info['mini_score'], doc_ids=mixture_doc_ids)
        else:
            vector_list = []
        if es_count:
            global_retriever_list = self.get_es_global_retriever(task, message, es_count, app_info['knowledge_ids'])
        else:
            global_retriever_list = []
        retriever_list = vector_list + global_retriever_list
        text_list = self.get_hits_text_list(retriever_list)
        rerank_text_list = self.re_rank_text(message, text_list)
        retriever_list = self.get_re_rank_hits(retriever_list, rerank_text_list, app_info.get('size', 5), app_info.get('rarank_ignore_score', 0))
        return retriever_list or []

    def search_and_rerank_lib(self, task, message, knowledge_ids, mini_score, call_size, global_percent=50, rerank_size=100, ignore_score=0, mixture_doc_ids=[]):
        text_count = int(rerank_size * (global_percent / 100))
        vector_count = rerank_size - text_count

        # 向量检索数据
        vector_list = self.search_es_knowledge(task, message, knowledge_ids, size=vector_count, min_score=mini_score, doc_ids=mixture_doc_ids)

        # 文本检索数据
        text_list = self.get_es_global_retriever(task, message, text_count, knowledge_ids)

        # 综合信息
        all_data_map = dict()
        filer_map = dict()
        msg_list = list()
        for item in vector_list + text_list:
            chunk_id = item['_source']['chunk_id']
            description = item['_source']['description']
            _key = md5(description)
            # 数据去重 和 构建映射关系
            if chunk_id not in filer_map.get(_key, []):
                filer_map.setdefault(_key, []).append(chunk_id)
                all_data_map.setdefault(_key, []).append(item)
                msg_list.append(description)

        if not msg_list:
            return msg_list, msg_list

        # 重排结果数据
        rerank_text_list = self.re_rank_text(message, msg_list)

        # ReRank数据召回和去重
        unique_text = dict()
        for item in rerank_text_list:
            content, score = item[0], item[1]
            # 满足召回数量
            if len(unique_text) >= call_size:
                break
            else:
                _key = md5(content)
                # 去重 和 阈值过滤
                if (score >= ignore_score) and (_key not in unique_text):
                    unique_text[_key] = content

        retriever_hit_list = list()
        for _key, value in unique_text.items():
            retriever_hit_list += all_data_map.get(_key, [])
        return retriever_hit_list, list(unique_text.values())

    def get_hits_text_list(self, hits):
        return [item['_source']['description'] for item in hits]
    
    def re_rank_text(self, message, text_list):
        body_data = {
            "question": message,
            "answers_list": text_list
        }
        response = requests.post(RANK_MODEL_URL, json=body_data, headers={"Content-Type": "application/json"})
        # logger.info(text_list)
        logger.info("============================")
        try:
            if response.status_code != 200:
                logger.error(f"重排序服务返回错误状态码: {response.status_code}")
                return []
            
            if not response.text:
                logger.error("重排序服务返回空响应")
                return []
                
            result = response.json()
            answer_list = result.get("ranked_answers", [])
            return answer_list
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}, 响应内容: {response.text[:100]}")
            return []
        except Exception as e:
            logger.error(f"处理重排序响应时发生错误: {e}")
            return []
    
    def get_aigc_model_id_by_has_reason(self, has_reason):
        return self.model.get_aigc_model_id_by_has_reason(has_reason)

    def get_re_rank_hits(self, hits, rerank_text_list, size, ignore_score):
        # 对rerank_text_list进行去重，根据——source——description
        unique = set()
        unique_list = list()
        for item in rerank_text_list:
            if item[0] not in unique:
                unique_list.append(item)
            unique.add(item[0])

        top_list = unique_list[:size]
        filter_list = filter(lambda x: x[1] > ignore_score, top_list)
        filter_text_list = [item[0] for item in filter_list]
        rerank_hits = []
        unique_list_hist = set()
        for item in hits:
            description = item['_source']['description']
            if description in filter_text_list:
                if description not in unique_list_hist:
                    rerank_hits.append(item)
                unique_list_hist.add(description)
                
        return rerank_hits

    # deprecated
    def set_message(self, task, message, app_info):
        chunk_ids_content_map = {}
        chunk_contents_list = []
        knowledge_content = '## 知识库内容\n'
        doc_ids = list()
        chunk_ids = list()
        if app_info['knowledge_ids']:
            # mixture_doc_ids = KnowledgeController(self.corpid).search_es_doc_abstract(message,
            #                                                                           app_info['knowledge_ids'],
            #                                                                           size=app_info['size'],
            #                                                                           min_score=1.5)
            mixture_doc_ids = KnowledgeController(self.corpid).search_es_doc_abstract_full_text(message, size=app_info['size'],
                                                                                                min_score=0.35)

            logger.info('===========mixture：%s' % mixture_doc_ids)
            logger.info(mixture_doc_ids)
            retriever_list = self.search_es_knowledge(task, message, app_info['knowledge_ids'], size=app_info['size'],
                                                      min_score=app_info['mini_score'], doc_ids=mixture_doc_ids)

            # logger.info(f"search_es_knowledge: {retriever_list}\nTime: {datetime.datetime.now()}")

            for item in retriever_list:
                knowledge_content += f'### {item["_source"]["description"]}\n'
                doc_ids.append(item['_source']['doc_id'])
                chunk_ids.append(item['_source']['chunk_id'])
                chunk_contents_list.append(item['_source']['description'])
                if item['_source']['chunk_id'] not in chunk_ids_content_map:
                    chunk_ids_content_map[item['_source']['chunk_id']] = item['_source']['description']
        else:
            knowledge_content += '\n'
        # message_content = f'\n\n## 输入内容\n### {message}\n\n'
        message_content = f"{knowledge_content}\n 输入内容\n### {message} \n\n"
        info = {
            'content': message_content,
            'doc_ids': doc_ids,
            'chunk_ids': chunk_ids,
            'chunk_contents_list': chunk_contents_list,
            'chunk_ids_content_map': chunk_ids_content_map
        }
        return info

    # def app_id_classify(self, question_str, app_info):
    #     return ClassifyController(self.corpid).classify_and_process_answers(question_str, app_info)
    
    def app_id_classify_inner(self, question_str, app_info, controller):
        ret = ClassifyController(self.corpid).classify_and_process_answers_llm(question_str, app_info, controller)
        try:
            self.mg.log_app_judge({'question_str': question_str, 'ret': ret.get('app_list', []), 'app_info': app_info})
        except:
            pass
        return ret

    def search_es_gt_demo_qa(self, message, size=1, min_score=1.9, is_json=False, app_id=None):

        ret = KnowledgeController(self.corpid).search_es_gt_demo_knowledge(message, size=size, min_score=min_score, is_search=True, app_id=app_id)
        if is_json:
            return ret
        else:
            return ret.get('answer')

    def search_es_qa_list(self, message, size=1, min_score=1.9, is_json=False, app_id=None):
        qa_list =  KnowledgeController(self.corpid).search_es_gt_demo_knowledge(message, size=size, min_score=min_score, is_search=True, app_id=app_id, need_all=True)
        logger.info(f"qa_list: {qa_list}")
        return qa_list
        
    def get_es_global_retriever(self, task, message, es_count, knowledge_ids):
        controller = KnowledgeController()
        knowledge_group = controller.get_knowledge_ids_by_model(knowledge_ids)
        index_list = []
        for ids in knowledge_group:
            index_name = controller.get_index_name({'knowledge_id': ids[0]})
            index_list.append(index_name)
        query = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "match": {
                                "description": message
                            },
                        },
                        {
                            "terms": {"knowledge_id": knowledge_ids}
                        },
                    ]
                }
            },
            "size": es_count,
        }
        try:
            return controller.es.search_data(index_name=index_list, query_dict=query)['hits']['hits']
        except Exception as e:
            logger.error(f"get_es_global_retriever error: {e}")
            return []

    @staticmethod
    def search_es_knowledge(task, message, knowledge_ids, size=5, min_score=1.4, doc_ids=[]):
        knowledge_group = KnowledgeController().get_knowledge_ids_by_model(knowledge_ids, doc_ids)
        hits = []
       
        for knowledge_ids in knowledge_group:
            hits.extend(
                KnowledgeController().search_es_knowledge(task, message, knowledge_ids=knowledge_ids, size=size, min_score=min_score,
                                                          is_search=True, doc_ids=doc_ids))
        return hits

    @staticmethod
    def set_suggestion_message(message):

        content = ("# 角色\n你是一个知识渊博的智能助手，能够根据用户的需求迅速回忆起相关知识，并以通俗易懂的方式进行解答。\n"
                   "## 要求\n"
                   "1. 提取并整理用户输入的信息，并给出相应的建议。\n"
                   "2. 确保每条建议内容清晰、并且都是疑问句。\n"
                   "3. 返回的建议数量不超过3条。\n"
                   "4. 每条建议不要超过15个字。\n"
                   "5. 每条建议不要有序号、角标等。\n"
                   "6. “以下是、建议”等这样的文字都不要返回。\n"
                   "7. 不要出现和建议无关的内容。\n"
                   "8. 建议与建议之间只要一个换行符。\n"
                   "##内容原文\n")
        return content + message

    @staticmethod
    def set_suggestion_message_by_hit_chunks(input_llm_chunk_contents_list):

        input_prompt = ("# 角色\n"
                        "你是一个知识渊博的智能助手，能够根据用户的需求迅速回忆起相关知识，并以通俗易懂的方式进行解答。\n"
                        "## 要求\n"
                        f"1. 根据以下列表内的文档切片内容生成3个推荐的问题：\n{input_llm_chunk_contents_list}\n"
                        "2. 每个推荐的问题应基于文档切片中的内容，体现用户可能想进一步了解的主题。\n"
                        "3. 确保每条建议内容清晰，并且都是疑问句。\n"
                        "4. 返回的建议数量不超过3条且最好是3条。\n"
                        "5. 每条建议不要超过15个字。\n"
                        "6. 每条建议不要有序号、角标等。\n"
                        "7. 不要使用“以下是、建议”等提示性文字。\n"
                        "8. 不要出现和建议无关的内容。\n"
                        "9. 建议之间仅用一个换行符分隔。\n"
                        "10. 请严格遵守以上所有要求。\n")
        return input_prompt

    def create_app_session(self, app_id, tp_user_id, session_name, prompt_list, session_type='search', report_date=None):

        return self.model.creat_app_session(app_id, tp_user_id, session_name, prompt_list, session_type, report_date)

    def get_app_session_info(self, session_id):
        return self.model.get_session_detail(session_id)

    def get_app_session_detail(self, session_id, app_id):
        return self.model.get_app_session_detail(session_id, app_id)

    def update_app_session(self, session_id, args):
        return self.model.update_app_session(session_id, args)

    def create_app_session_record(self, session_id, flow_id, app_id, is_first, content, content_info, original_app_id,  session_type='search'):
        data = {
            'app_id': app_id,
            'session_id': session_id,
            'is_first': is_first,
            'content': content,
            'final_content': "",
            'doc_ids': "",
            'chunk_ids': "",
            'original_app_id': original_app_id,
            'session_type': session_type,
            'flow_id': flow_id,
            'create_time': datetime_to_dt_str(datetime.datetime.now())
        }
        if content_info:
            data.update({'final_content': content_info['content'], 'doc_ids': ",".join(content_info['doc_ids']),
                         'chunk_ids': ",".join(content_info['chunk_ids'])})
        return self.mg.insert_app_session(data)

    def create_app_session_flow_record(self, _index, flow_id, type_key, data_json, content, reasoning_content):
        data = {
            'flow_id': flow_id,
            '_index': _index,
            'content': content,
            'data_json': data_json,
            'type_key': type_key,
            'create_time': datetime_to_dt_str(datetime.datetime.now())
        }
        return self.mg.insert_app_session_flow(data)

    def get_session_flow_data(self, flow_id):
        ret = list()
        if not flow_id:
            return ret
        log_list = self.mg.get_app_session_flow_record(flow_id)

        sorted_list =  sorted(log_list, key=lambda x: x['_index'])

        for one in sorted_list:
            base_map = {'title': None, 'create_time': one['create_time'], 'data_list': []}
            type_key = one['type_key']
            title = SessionMsgMap.get(type_key)
            if title:
                base_map['title'] = title
            mg_content = one.get('content')
            mg_data_json = one.get('data_json', {})
            table_map = session_msg_table_map.get(type_key)
            if type_key == 'qa_check' and isinstance(mg_content, list):
                IS_SUMMARY = '1'
                is_qa_summary = False
                if mg_content:
                    is_qa_summary = len(mg_content) > 1 or mg_content[0].get('qa_modal') == IS_SUMMARY
                front_show_value = '是' if is_qa_summary else '否'
                base_map['data_list'].append({'data':
                    f"是否总结: {front_show_value}", 'data_type': 'str'})

                base_map['data_list'].append({
                    'map': {
                        'keys': ['question','answer', 'score'],
                        'values': ['问题', '答案', '评分'],
                    },
                    'data': mg_content,
                    'data_type': 'table'
                })
            elif not table_map:
                data_map = SessionJsonMap.get(type_key)
                if data_map:
                    _type = data_map.get('_type', 'key_map')
                    if mg_data_json:
                        build_data = list()
                        for _key, msg_data in data_map.items():
                            if _key not in mg_data_json:
                                continue
                            _value = mg_data_json.get(_key)

                        # for _key, _value in mg_data_json.items():
                            _key = _key if _type == 'key_map' else ('1' if _value else '0')
                            msg_data = data_map.get(_key)
                            if not msg_data:
                                continue
                            s_data_map = SessionJsonMap.get(_key)
                            if s_data_map:
                                s_type = s_data_map.get('_type', 'key_map')
                                s_key = _key if s_type == 'key_map' else ('1' if _value else '0')
                                s_msg_data = s_data_map.get(s_key)
                                if s_data_map:
                                    build_data.append(f'{msg_data}: {s_msg_data}')
                            else:
                                build_data.append(f'{msg_data}: {_value}')
                        base_map['data_list'].append({'data': build_data, 'data_type': 'list'})
                    else:
                        _key = type_key if _type == 'key_map' else ('1' if mg_content else '0')
                        msg_data = data_map.get(_key, mg_content)
                        base_map['data_list'].append({'data': msg_data, 'data_type': 'str'})

                else:
                    if mg_data_json:
                        base_map['data_list'].append({'data': mg_data_json, 'data_type': 'json'})
                    else:
                        base_map['data_list'].append({'data': mg_content, 'data_type': 'str'})
            else:
                if type_key == 'tag':
                    doc_ids = mg_data_json["doc_ids"]
                    if not doc_ids:
                        continue
                    base_map['data_list'].append({'data': [x['description'] for x in mg_data_json['tag_map']], 'data_type': 'list'})
                    base_map['data_list'].append({'data': f'{table_map["msg"]}: {len(mg_data_json["doc_ids"])}', 'data_type': 'str'})
                    data_info = self.knowledge.get_doc_map_infos_by_ids(doc_ids)
                    for i in data_info:
                        i.setdefault('word', [])
                        for j in mg_data_json['tag_map']:
                            if i['document_id'] == j['doc_id']:
                                i['word'].append(j['description'])
                                break

                    if data_info:
                        base_map['data_list'].append({'data': data_info, 'data_type': 'table', 'map': table_map['map']})

                elif type_key == 'search_ret':
                    for s_type_key in table_map:
                        title = SessionMsgMap.get(s_type_key)
                        base_map_copy = copy.deepcopy(base_map)
                        if title:
                            base_map_copy['title'] = title
                        son_table_map = session_msg_table_map[s_type_key]
                        msg = son_table_map.get('msg')
                        log_data = mg_data_json.get(s_type_key)
                        if msg and log_data:
                            if type(log_data) is dict:
                                count_no = len(list(log_data.keys()))
                            elif type(log_data) is list:
                                count_no = len(log_data)
                            else:
                                count_no = 0
                            if count_no:
                                base_map_copy['data_list'].append({'data': f'{msg}: {count_no}', 'data_type': 'str'})
                        if log_data:
                            if 'abstract' in s_type_key:
                                data_info = self.knowledge.get_doc_map_infos_by_ids(list(log_data.keys()))
                                for i in data_info:
                                    i['word'], i['score'] = log_data.get(i['document_id'], ['', 0])
                                base_map_copy['data_list'].append({'data': data_info, 'data_type': 'table', 'map': son_table_map['map']})
                            elif 'chunk' in s_type_key:
                                data_info = self.knowledge.get_chunk_map_info_by_ids(list(log_data.keys()))
                                for i in data_info:
                                    i['word'], i['score'] = log_data.get(i['chunk_id'],  ['', 0])
                                base_map_copy['data_list'].append({'data': data_info, 'data_type': 'table', 'map': son_table_map['map']})
                            elif 'graph' in s_type_key:
                                data_map = {x['doc_id']: x['graph_content'] for x in log_data}
                                data_info = self.knowledge.get_doc_map_infos_by_ids(list(data_map.keys()))
                                for i in data_info:
                                    i['word'] = data_map.get(i['document_id'])
                                base_map_copy['data_list'].append({'data': data_info, 'data_type': 'table', 'map': son_table_map['map']})
                        if base_map_copy['title'] and base_map_copy['data_list']:
                            ret.append(base_map_copy)
                    else:
                        continue
            if base_map['title'] and base_map['data_list']:
                ret.append(base_map)
        return ret

    def get_app_session_record(self, session_id, is_final):
        # return self.mg.get_app_session_record(session_id, is_final)
        return self.mg.get_app_session_record_v1(session_id, is_final)

    async def chat_qianwen_async(self, message, old_content, aigc_type_id, model_path, is_stream=False):
        app_key, app_secret = self.model.get_key_secret(aigc_type_id)
        chat_model = MyQianWenAsync(app_key=app_key, app_secret=app_secret, model=model_path, old_content=old_content)
        return chat_model.receive(message, is_stream)

    def chat_xunfei_model(self, message, old_content, aigc_type_id=None, model_path=None, is_stream=True):
        chat_model = XFAiModel(old_content=old_content)
        return chat_model.receive(message)
    
    def ai_app_chat(self, message, old_content:list, aigc_type_id:str, model_path:str, is_stream:bool=True,model_id:str=None):
        if aigc_type_id == XunFei_Code:
            resp = self.chat_xunfei_model(message, old_content, is_stream=is_stream)
            for item in resp:
                try:
                    if item.decode():
                        yield json.loads(item.decode())['result'],''
                except AttributeError:
                    logger.info(f"item: {item}")
                    if json.loads(item):
                        yield json.loads(item)['result'],''
        else:
            app_model = AppOrm(self.corpid)
            mongo = ChatMongo(self.corpid)
            if not model_id:
                aigc_model = app_model.get_model_by_typ_id_path(aigc_type_id, model_path)
                model_id = aigc_model.aigc_model_id
            view_obj = ChatViewController(model_id, app_model=app_model, mongo=mongo, old_content=old_content)
            chat_model = view_obj.chat_model
            for item in chat_model.receive(message, is_stream):
                json_item = json.loads(item.decode())
                result,reasoing_content = json_item.get('result'), json_item.get('reasoning_content')
                if result or reasoing_content:
                    yield result,reasoing_content

    async def ai_app_chat_async(self, message, old_content, aigc_type_id, model_path, is_stream=True):
        if aigc_type_id == QIANWEN_CODE:
            resp = await self.chat_qianwen_async(message, old_content, aigc_type_id, model_path, False)
            async for item in resp:
                try:
                    if isinstance(item, bytes):
                        decoded_item = item.decode()
                    else:
                        decoded_item = item

                    if decoded_item:
                        yield json.loads(decoded_item)['result']
                except (AttributeError, KeyError, json.JSONDecodeError) as e:
                    logger.error(f"Error processing item: {item}, error: {e}")
                    try:
                        yield json.loads(item)['result']
                    except Exception as inner_e:
                        logger.error(f"Failed to process item: {item}, inner error: {inner_e}")

    async def ai_app_chat_inner(self, message, old_content, aigc_type_id, model_path, is_stream=True):
        app_model = AppOrm(self.corpid)
        mongo = ChatMongo(self.corpid)

        aigc_model = app_model.get_model_by_typ_id_path(aigc_type_id, model_path)
        model_id = aigc_model.aigc_model_id

        view_obj = ChatViewController(model_id, app_model=app_model, mongo=mongo, old_content=old_content)
        app_key = view_obj.app_key
        app_secret = view_obj.app_secret
        model = view_obj.chat_model.model
        model_url = view_obj.chat_model.model_url

        chat_model = AllInOneModel(app_key=app_key, app_secret=app_secret, model=model,
                                   model_url=model_url, prompt=message)
        resp = chat_model.receive_async(message, is_stream=is_stream)

        async for item in resp:
            try:
                if isinstance(item, bytes):
                    decoded_item = item.decode()
                else:
                    decoded_item = item

                if decoded_item:
                    if isinstance(decoded_item, dict):
                        yield decoded_item.get('result', decoded_item)
                    else:
                        parsed_data = json.loads(decoded_item)
                        yield parsed_data.get('result', parsed_data)

            except (AttributeError, KeyError, json.JSONDecodeError) as e:
                logger.error(f"Error processing item: {item}, error: {e}")
                try:
                    if isinstance(item, str):
                        parsed_data = json.loads(item)
                        yield parsed_data.get('result', parsed_data)
                    else:
                        yield item
                except Exception as inner_e:
                    logger.error(f"Failed to process item: {item}, inner error: {inner_e}")
                    yield item

    async def ai_app_chat_graph(self, message, old_content, aigc_type_id, model_path, is_stream=True):
        async for item in self.ai_app_chat_inner(message, old_content, aigc_type_id, model_path, is_stream):
            yield item

    def get_app_session_list(self, args):
        ret = self.model.get_app_session_list(args)
        for one in ret['data_list']:
            one['qa_no'] = self.mg.get_app_session_record_count(one['session_id'])
        return ret

    def update_mg_app_session_record(self, session_id, update):
        return self.mg.update_app_session_record(session_id, update)

    def insert_mg_search_record(self, data):
        return self.mg.insert_search_record(data)

    def insert_mg_search_content_record(self, data):
        return self.mg.insert_search_content_record(data)

    def search_recommend_content(self):
        data = {
            'content_ids': self.mg.get_recommend_content(),
            'search_key': self.mg.get_recommend_search_key()

        }
        return data

    def search_recommend_key_list(self):

        search_key_list = self.mg.get_recommend_search_key_list()
        return search_key_list

    def search_recommend_content_list(self, limit=10):
        content_ids = self.mg.get_recommend_content(limit)
        
        if not content_ids:  # 检查 content_ids 是否为空
            return []  # 或者返回一个默认的响应
        
        data_list = self.content_model.get_content_info_by_ids(content_ids)
        return data_list

    def search_report_knowledge(self, task, message, app_info, size=5):

        knowledge_content = '\n\n## 知识库内容\n'
        doc_ids = list()
        chunk_ids = list()

        if app_info['knowledge_ids']:
            retriever_list = self.search_es_knowledge(task, message, app_info['knowledge_ids'], size=size)
            index = 1
            for item in retriever_list:
                knowledge_content += f'### 片段[{index}]:{item["_source"]["description"]}\n'
                doc_ids.append(item['_source']['doc_id'])
                chunk_ids.append(item['_source']['chunk_id'])
                index += 1
        else:
            knowledge_content += '\n'
        content = message + knowledge_content
        info = {
            'content': content,
            'doc_ids': doc_ids,
            'chunk_ids': chunk_ids
        }
        return info

    def knowledge_classifier(self, input_text, knowledge_list):

        prompt = f"请根据输入内容返回应该使用哪个知识库进行检索，知识库:{knowledge_list},\n返回格式为：knowledge_id，多个以英文逗号分割， 例如：1,2,3;\n输入内容：{input_text}"
        response = self.ai_app_chat(prompt, [], QIANWEN_CODE, "qwen-turbo", is_stream=False)
        knowledge_ids = ''
        for item in response:
            if item.decode():
                knowledge_ids = json.loads(item.decode())['result']

        return knowledge_ids

    def knowledge_doc_classifier(self, input_text, ques_prompt, doc_list, aigc_type_id, model_path, model_id=None):
        # prompt = f"请根据输入内容先提取公司名称，然后用提取的信息匹配文档库中的description，应该使用文档库中的哪个文档进行检索，文档库:{doc_list},\n返回格式为：doc_id，多个以英文逗号分割， 例如：1,2,3;\n输入内容：{input_text}"
        # prompt = f"请根据输入内容先提取公司简称，例如：’江苏税软软件科技有限公司‘提取‘江苏税软’\n 要求：-返回的公司必须是公司简称。\n -自我监督：返回内容事要自我监督检查，必须返回公司简称\n -仅返回提取的公司简称。\n返回格式为：多个以英文逗号分割， 例如：xxx,xxx,xxx;\n输入内容：{input_text}"
        prompt = f"{ques_prompt} {input_text}"
        if IS_910B_EMBEDDINGS:
            # TODO: 临时使用tongyiqianwen
            response = self.ai_app_chat(prompt, [], aigc_type_id, model_path,
                                        is_stream=False, model_id=model_id)
        else:
            response = self.ai_app_chat(prompt, [], "tongyiqianwen", "qwen-turbo",
                                        is_stream=False, model_id=model_id)
        doc_ids = ''
        for item,reasoning_content in response:
            doc_ids += item

        return doc_ids

    # prompt = f"请根据输入内容先提取公司简称，例如：’江苏税软软件科技有限公司‘提取‘江苏税软’\n 要求：-返回的公司必须是公司简称。\n -自我监督：返回内容事要自我监督检查，必须返回公司简称\n -仅返回提取的公司简称。\n返回格式为：多个以英文逗号分割， 例如：xxx,xxx,xxx;\n输入内容：{input_text}"
    
    def message_statistic_classifier(self, input_text):
        
        input_llm_prompt = self.generate_prompt_message_statistic_classifier(input_text)
        
        # logger.info(f"input_llm_prompt: {input_llm_prompt}")
        
        is_statistic_map = self.call_ai_model_v2(input_llm_prompt, self.corpid)
        logger.info(f"is_statistic_map: {is_statistic_map}")
        
        
        # prompt = f"你是个精通wen用户输入问题：{input_text}"
        # response = self.chat_qianwen(prompt, [], QIANWEN_CODE, 'qwen-turbo',
        #                              is_stream=False)

        return is_statistic_map

    def get_doc_list_by_description(self, knowledge_ids, descriptions):
        return KnowledgeOrm(self.corpid).get_doc_list_by_description(knowledge_ids, descriptions)

    def get_doc_list_by_doc_ids(self, doc_ids, chunk_ids):
        doc_list = KnowledgeOrm(self.corpid).get_doc_list_by_doc_ids(doc_ids)
        for doc in doc_list:
            doc['chunk_ids'] = chunk_ids
        return doc_list

    def get_doc_chunk_list_by_doc_ids(self, doc_id, chunk_ids):
        return KnowledgeOrm(self.corpid).get_doc_chunk_list_by_chunk_ids(doc_id, chunk_ids)
    
    @staticmethod
    def generate_prompt_message_statistic_classifier(message):
    
        # logger.info(f"Input message: {message}\nData_graph_list: {data_graph_list}")

        rsp_example = {
            "is_statistic": 1
        }

        content = (
            f"# 角色\n"
            "你是一个能够根据用户输入内容判断问题是否包含统计信息的智能助手，统计信息指的是需要Pandas处理数据。\n\n"
            "## 判断规则\n"
            f"根据用户输入内容:[{message}]，判断其是否需要Pandas进行数据操作。\n"
            "1. 如果用户输入内容涉及数据过滤、聚合、分组、选择等Pandas相关操作(例如：都有谁/有多少人/有几个……)，则判定为包含统计信息，返回is_statistic为整型1。\n"
            "2. 否则，判定为不包含统计信息，返回is_statistic为整型0。\n"
            f"- 返回：严格的JSON Markdown格式，仅有 is_statistic 字段。返回示例：{rsp_example}\n\n"
        )

        return content
    @staticmethod
    def call_ai_model_v2(prompt, corpid):
        messages = [{'role': 'system', 'content': prompt}]
        response = AiChatGpt(corpid).receive(messages, 'gpt-4o', is_stream=False)

        res_str = response.json().get('data')

        logger.info(f"Call llm res str: {res_str}, type: {type(res_str)}")


        if not isinstance(res_str, str):
            # raise TypeError("Response data is not a string")
            pass

        # Try to extract the JSON object enclosed by triple backticks
        json_str = None

        # 1. Check if res_str is already in JSON format
        try:
            json_str = res_str.replace("'", '"')

            parsed_response = json.loads(json_str)
            if isinstance(parsed_response, dict):
                return parsed_response
        except json.JSONDecodeError:
            # logger.info(f"res_str is not in JSON format")
            pass  # It's not a plain JSON, so continue to other checks
            # raise ValueError("Response data is not a string")

        # 2. Look for the ```json ... ``` pattern
        pattern = r'```json\n(.*?)\n```'
        match = re.search(pattern, res_str, re.DOTALL)

        if match:
            json_str = match.group(1)
        else:
            # 3. Try to find JSON fragments in res_str
            pattern = r'\{.*?\}'
            matches = re.findall(pattern, res_str, re.DOTALL)
            logger.info(f"matches: {matches}, {type(matches)}")

            if not matches:
                # raise ValueError("No JSON objects found in the response")
                pass

            # Combine the JSON fragments properly
            json_str = '{'
            for i, match in enumerate(matches):
                if i > 0 and match.strip().startswith('{'):
                    json_str += ','  # Add comma between fragments
                json_str += match
            json_str += '}'

        if json_str:
            # Replace single quotes with double quotes to ensure valid JSON format
            json_str = json_str.replace("'", '"')

            try:
                # Load the combined JSON string
                parsed_response = json.loads(json_str)
                return parsed_response
            except json.JSONDecodeError as e:
                logger.error(f"JSONDecodeError: {e}")
                logger.error(f"json_str: {json_str}")
                # raise

        # If all attempts fail, raise an exception
        # raise ValueError("Unable to parse JSON from response")

    def chat_bing(self, content):
        qwen_api_key, qwen_app_secret = self.model.get_key_secret(QIANWEN_CODE)
        try:
            self.chat_model = MyBing(url="https://api.bing.microsoft.com/v7.0/search", api_key='********************************',
                                     corp_id=self.corpid, chat_model=MyQianWen(qwen_api_key))

            resp = self.chat_model.receive(content, is_stream=True)
            for item in resp:
                if item.decode():
                    yield json.loads(item.decode())['result']
        except Exception as e:
            logger.error(f"Error: {e}")
            yield '抱歉未查询到相关信息！'

    def gt_chat_bing(self, api_key, content, is_chat):
        _api_key, _app_secret = self.model.get_key_secret(ASCEND_910B_CODE)
        try:
            chat_model = MyBing(url="https://api.bing.microsoft.com/v7.0/search", api_key=api_key, corp_id=self.corpid)
            return chat_model.receive_v2(content)
        except Exception as e:
            raise ValueError('抱歉未查询到相关信息！')

    def get_app_list(self, args):
        self.model.get_apps_gt(args)


    def get_graph_rag(self, query, app_id):
        """
        Retrieves a GraphRag instance and executes a query.

        :param query: The query string to be executed.
        :param app_id: The application ID used to retrieve knowledge IDs.
        :return: The result of the query execution.
        :raises GraphRagException: If any step fails during the process.
        """
        # Retrieve knowledge IDs associated with the app_id
        app_knowledge_ids = self.model.get_knowledge_ids_by_app_ids([app_id])
        if not app_knowledge_ids:
            raise GraphRagException(
                message="No knowledge IDs found for the given app_id",
                error_code=400,
                details={"app_id": app_id}
            )

        # FIXME
        knowledge_list = [{"knowledge_id": str(item.knowledge_id), "knowledge_name": item.knowledge.knowledge_name} for
                          item in app_knowledge_ids]
        if not knowledge_list:
            raise GraphRagException(
                message="Failed to extract knowledge list from knowledge IDs",
                error_code=400,
                details={"app_id": app_id}
            )

        knowledge_id = knowledge_list[0]['knowledge_id']
        if not knowledge_id:
            raise GraphRagException(
                message="Invalid knowledge ID retrieved for the given app_id",
                error_code=400,
                details={"app_id": app_id, "knowledge_ids": app_knowledge_ids}
            )

        storage_namespace = SPACE_PREFIX + str(knowledge_id)
        try:
            from apps.graphrag.graphrag import GraphRag
            rag = GraphRag(corp_id=g.corpid, namespace=storage_namespace).get_instance()
            if rag is None:
                raise GraphRagException(
                    message="GraphRag instance is None",
                    error_code=500,
                    details={"namespace": storage_namespace}
                )
        except Exception as e:
            logger.error(f"Failed to create GraphRag instance: {e}")
            raise GraphRagException(
                message="Failed to create GraphRag instance",
                error_code=500,
                details={"namespace": storage_namespace, "original_error": str(e)}
            )

        try:
            app_info = {
                "aigc_type_id": DEFAULT_AIGC_TYPE_ID,
                "model_path": DEFAULT_MODEL_PATH,
                "corp_id": g.corpid,
            }
            result = rag.query_wrapper(query, int(knowledge_id), app_info)
            if result is None:
                raise ValueError("GraphRag query_wrapper returned None")
            return result
        except Exception as e:
            logger.error(f"Error executing query_wrapper: {e}")
            raise GraphRagException(
                message="Error executing aquery_wrapper",
                error_code=500,
                details={"query": query, "knowledge_id": knowledge_id, "original_error": str(e)}
            )
    
    def get_is_new_session(self, user_id, session_id, start_time):
        query = {'tp_user_id':user_id, 'session_id':session_id}
        sort_criteria = [('session_start_time', -1)]
        the_latest_one = self.mg.get_latest_statistics_session(query, sort_criteria)
        if not the_latest_one:
            return True
        mongo_start_time = the_latest_one.get("session_start_time")
        mongo_start_time_stamp = mongo_start_time.timestamp()
        if start_time - mongo_start_time_stamp < 30 * 60:
            return False
        return True
    
    def add_statistics_session(self, is_success_session, session_start_time, anwer_time, session_end_time, message, app_id, tp_user_id, session_id, is_new_session, the_token, old_app_id, is_aggregate, app_name, old_app_name, is_create_index=0):
        if is_create_index:
            self.mg.create_statistics_index([('app_id', 1)])
            self.mg.create_statistics_index([('tp_user_id', 1), ('session_id', 1)])
            self.mg.create_statistics_index([('session_start_time', -1)])
        session_info = {
            "is_success_session": is_success_session,
            "session_start_time": timestampToDatetime(session_start_time),
            "anwer_time": str(round(anwer_time, 1)),
            "session_end_time": str(round(session_end_time, 1)),
            "message": message,
            "app_id": old_app_id,
            "app_name": old_app_name,
            "hit_app_id": app_id,
            "hit_app_name": app_name,
            "tp_user_id": tp_user_id,
            "session_id": session_id,
            "is_new_session": is_new_session,
            'create_time': datetime.datetime.now(),
            'token': the_token,
            "is_aggregate": is_aggregate,
        }
        res = self.mg.insert_statistics_session(session_info)
        return res
    
    def add_aggregate_statistics_session(self, app_id, old_app_id, app_name, old_app_name, scene_type, is_create_index=0):
        if is_create_index:
            self.mg.create_aggregate_statistics_index([('app_id', 1)])
            self.mg.create_aggregate_statistics_index([('create_time', -1)])
        session_info = {
            "app_id": old_app_id,
            "app_name": old_app_name,
            "hit_app_id": app_id,
            "hit_app_name": app_name,
            'create_time': datetime.datetime.now(),
            "scene_type": scene_type, 
        }
        res = self.mg.insert_aggregate_statistics_session(session_info)
        return res

    def get_app_session_list_v2(self, args):
        """
        获取会话列表 v2 版本,按时间分组
        """
        ret = self.model.get_app_session_list(args)

        # 获取当前时间
        now = datetime.datetime.now()
        today = now.replace(hour=0, minute=0, second=0, microsecond=0)
        yesterday = today - datetime.timedelta(days=1)
        last_7_days = today - datetime.timedelta(days=7)
        last_30_days = today - datetime.timedelta(days=30)
        last_year = today - datetime.timedelta(days=365)

        # 初始化分组
        groups = {
            'today': [],
            'yesterday': [],
            'last_7_days': [],
            'last_30_days': [],
            'last_year': [],
            'earlier': []
        }

        # 遍历会话记录进行分组
        for session in ret['data_list']:
            session_time = session['add_time']
            if isinstance(session_time, str):
                session_time = datetime.strptime(session_time, '%Y-%m-%d %H:%M:%S')

            if session_time >= today:
                groups['today'].append(session)
            elif session_time >= yesterday:
                groups['yesterday'].append(session)
            elif session_time >= last_7_days:
                groups['last_7_days'].append(session)
            elif session_time >= last_30_days:
                groups['last_30_days'].append(session)
            elif session_time >= last_year:
                groups['last_year'].append(session)
            else:
                groups['earlier'].append(session)

        # 构建返回数据
        grouped_data = []
        group_names = {
            'today': '今天',
            'yesterday': '昨天',
            'last_7_days': '最近7天',
            'last_30_days': '最近30天',
            'last_year': '最近一年',
            'earlier': '更早'
        }

        for key, name in group_names.items():
            if groups[key]:  # 只返回有数据的分组
                grouped_data.append({
                    'group_name': name,
                    'sessions': groups[key]
                })

        return {
            'total': ret['total'],
            'page_no': ret['po'],
            'grouped_data': grouped_data
        }

    def create_app_session_with_id(self, session_id, app_id, tp_user_id, session_name, prompt_list,session_type='search', report_date=None):
        """
        使用指定的session_id创建会话
        """
        return self.model.create_app_session_with_id(session_id, app_id, tp_user_id, session_name, prompt_list,session_type, report_date)
    def global_search(self, simple_app_info, args, knowledge_controller, question_str, tp_user_id, handle_ques, task):
        handle_app_list = self.batch_app(simple_app_info, args, knowledge_controller, handle_ques)
        all_retrieve_list = self.get_retrieve_list(handle_app_list)
        rerank_list = self.rerank_all_scene(all_retrieve_list, question_str, rerank_number=simple_app_info.get('global_rerank_num'))

        task.async_chunk_hits(self.corpid, rerank_list)

        global_search_prompt = ''
        if rerank_list:
            global_search_prompt = rerank_list[0].get('app_info', {}).get('prompt', '')

        content_info = self.batch_set_first_message(rerank_list, question_str, tp_user_id, simple_app_info.get('memory_size'), is_private=args.get("is_private", 0))

        print(f"*** batch app content info: {content_info} ***")

        return {'content_info': content_info, 'global_search_prompt': global_search_prompt}
    
    def batch_fill_knowledge_ids(self, app_list, args):
        app_ids = [i['app_id'] for i in app_list]
        knowledge_app_rels = self.get_apps_knowledge_ids(app_ids, args)
        if not knowledge_app_rels:
            return {
                'knowledge_ids': []
            }
        app_dict = dict()
        app_embed_dict = dict()
        for rel in knowledge_app_rels:
            if rel['app_id'] not in app_dict:
                app_dict[rel['app_id']] = list()
            app_dict[rel['app_id']].append(rel['knowledge_id'])
            if app_embed_dict.get(rel['app_id']) is None:
                app_embed_dict[rel['app_id']] = rel['knowledge_model_path']
            elif app_embed_dict.get(rel['app_id']) != rel['knowledge_model_path']:
                raise ValueError(f"app_id: {rel['app_id']} 全局查询情景下，同一应用不能使用不同向量的知识库, {app_embed_dict.get(rel['app_id'])} != {rel['knowledge_model_path']}")
        for app in app_list:
            app['knowledge_ids'] = app_dict.get(app['app_id'], [])
            app['knowledge_model_path'] = app_embed_dict.get(app['app_id'], '')
        return {
            'knowledge_ids': knowledge_app_rels
        }

    def batch_app(self, app_info, args, knowledge_controller, handle_ques):
        message = args.get('message', '')
        date_prompt = get_formatted_content()

        children_app_list = app_info.get('children', [])

        for app in children_app_list:
            question_str = ""
            try:
                date_enabled_flag = "1" in app.get('ques_enhance')
        
                ques_enhancer = QuesEnhancement(app.get('ques_replace'), list_replace_flag=True,
                                                date_replace_flag=False,
                                                date_enabled_flag=date_enabled_flag)
        
                question_str = ques_enhancer.transform_string(message)
            
                print('***enhance question:***: %s' % question_str)
            except Exception as e:
                logger.error(f"Error occurred: {e}")
                question_str = ""
                pass
            app.update({
                'prompt': (app.get('prompt', '') or '') + "\n ## 时间信息 \n " + date_prompt + "\n",
                'question_str': question_str
            })

            # 差旅助手 提取 公司 找对应文档 领导问答
            if app.get('ques_enabled'):
                handle_ques(app.get('app_id'), app, args, self, question_str)
        return self.batch_retrieve(children_app_list)
    def batch_retrieve(self, app_list):
        self.batch_update_question_to_vector(app_list)
        self.batch_add_abstract_doc_ids(app_list)
        return self.batch_retrieve_es_message(app_list)
    
    def batch_retrieve_es_message(self, app_list):
        # 不支持多个向量知识库全局搜索 
        batch_search_params_list = list()
        for app in app_list:
            batch_search_params_list.append({'input_vector': app.get('vector'), 'size': app.get('size', 5), 'min_score': app.get('min_score', 1.4),
                                     'knowledge_ids': app.get('knowledge_ids'), 'doc_ids': app.get('doc_ids')})
            
        
        res_list = KnowledgeController().search_es_knowledge_batch(batch_search_params_list)


        fix_list = list()
        for idx,res_item in enumerate(res_list):
            hit_list = res_item.get('hits', {}).get('hits', [])
            cur_app = app_list[idx]
            if hit_list:
                if cur_app.get('doc_ids') and len(hit_list) < cur_app.get('size', 0):
                    fix_list.append(cur_app)
                app_list[idx]['retriever_list'] = res_item['hits']['hits']
            else:
                app_list[idx]['retriever_list'] = []
        fix_search_params_list = list()
        for app in fix_list:
            fix_search_params_list.append({'input_vector': app.get('vector'), 'size': app.get('size', 5), 'min_score': app.get('min_score', 1.4),
                                     'knowledge_ids': app.get('knowledge_ids'), 'doc_ids': []})
            
        if fix_search_params_list:
            res_list = KnowledgeController().search_es_knowledge_batch(fix_search_params_list)

            for idx,res_item in enumerate(res_list):
                hit_list = res_item.get('hits', {}).get('hits', [])
                cur_app = app_list[idx]
                if hit_list:
                    diff_size = cur_app.get('size', 0) - len(cur_app.get('retriever_list', []))
                    app_list[idx]['retriever_list'] += res_item['hits']['hits'][:diff_size]

        logger_app_list = [
            {
                'app_id': app.get('app_id'),
                'retriever_list': list(map(lambda x: {'doc_id': x.get('_source', {}).get('doc_id'), 'chunk_id': x.get('_source', {}).get('chunk_id'), 'knowledge_id': x.get('_source', {}).get('knowledge_id')} ,app.get('retriever_list', []))),
                'retriever_size': len(app.get('retriever_list', [])),
                'question_str': app.get('question_str', ''),
                'hit_abstract_doc_ids': app.get('hit_abstract_doc_ids', []),
                'hit_tag_doc_ids': app.get('doc_ids', [])
            } for app in app_list
        ]

        logger.info(f"batch_retrieve_es_message app_list: {logger_app_list}")

        return app_list
    
    def batch_update_question_to_vector(self, app_list):
        model_path_group = group_by(app_list, lambda x: x.get('knowledge_model_path', ''))
        print(f"*** batch_update_question_to_vector model_path_group: {model_path_group.keys()} ***")
        for model_path in model_path_group:
            cur_app_list = model_path_group[model_path]
            question_list = [app.get('question_str', '') for app in cur_app_list]
            embed_data = KnowledgeController(self.corpid).create_embeddings(question_list, self.corpid, model_path)
            if embed_data['code'] != 0:
                raise Exception('batch_update_question_to_vector error:{0}'.format(embed_data.get('error_msg', '未知错误')))
            if 'vector' not in embed_data:
                raise Exception('batch_update_question_to_vector error:{0}'.format("vector not in embed_data"))
            code = embed_data['code']
            err_message = embed_data.get('error_msg', '')
            logger.info(f"batch_update_question_to_vector embed_data: code: {code} err_message: {err_message}")
            vector_list = embed_data.get('vector', [])

            for i, app in enumerate(cur_app_list):
                app['vector'] = vector_list[i]

    def batch_add_abstract_doc_ids(self, app_list):
        abstract_list = list()
        abstract_app_list = list()
        for idx, app in enumerate(app_list):
            if app.get('doc_ids'):
                continue
            if not app.get('knowledge_ids'):
                continue
            if not app.get('is_mixture'):
                continue
            abstract_app_list.append(app.get('app_id'))    
            abstract_list.append({'input_vector':app.get('vector'), 'size': app.get('size', 5),'min_score': app.get('min_score', 1.4), 'knowledge_ids': app.get('knowledge_ids')})


        knowledge_controller = KnowledgeController(self.corpid)
        if abstract_list:
            abstract_doc_list = knowledge_controller.search_es_doc_batch(abstract_list)
        else:
            abstract_doc_list = list()

        if len(abstract_doc_list) != len(abstract_app_list):
            raise Exception('batch_add_abstract_doc_ids error:{0}'.format("batch_add_abstract_doc_ids error, 长度不统一"))

        update_dict = dict()
        for idx,result in enumerate(abstract_doc_list):
            hits = result.get('hits', []).get('hits', [])
            update_dict[abstract_app_list[idx]] = [hit['_source']['doc_id'] for hit in hits]

        for app in app_list:
            if app.get('app_id') in update_dict:
                app['hit_abstract_doc_ids'] = update_dict[app.get('app_id')]
                app['doc_ids'] = update_dict[app.get('app_id')]
            else:
                app['doc_ids'] = list()
        return app_list
    
    def get_retrieve_list(self, app_list):
        retrieve_list = list()
        for app in app_list:
            handle_list = app.get('retriever_list', [])

            if handle_list:
                for handle in handle_list:
                    handle['app_info'] = {
                        'app_id': app.get('app_id'),
                        'app_name': app.get('app_name'),
                        'question_str': app.get('question_str'),
                        'is_graph': app.get('is_graph'),
                        'prompt': app.get('prompt'),
                        "memory_size": app.get('memory_size'),
                        "tp_user_id": app.get('tp_user_id'),
                    }

            retrieve_list+= handle_list
        return retrieve_list

    def rerank_all_scene(self, retrieve_list, question_str, rerank_number):
        rerank_list = list()
        if not retrieve_list:
            rerank_list = []
        else:
            rerank_list = reranker.rerank(question_str, [item.get('_source', {}).get('description', "") for item in retrieve_list])
        rerank_chunk = list()
        for idx, item in enumerate(rerank_list):
            rerank_chunk.append({**retrieve_list[item['index']], 'score': item['score']})
        return rerank_chunk[0:rerank_number]

    def get_apps_knowledge_ids(self, app_ids, args):
        rel_list = self.model.get_knowledge_ids_by_app_ids_v2(app_ids, args)
        return [{"app_id": item.app_id, "knowledge_id": item.knowledge_id, 'knowledge_model_path': item.knowledge.aigc_model.model_path} for item in rel_list]
    
    def batch_set_first_message(self, retriever_list, question, tp_user_id, memory_size, is_private=1):
        chunk_ids_content_map = {}
        chunk_contents_list = []
        memory_result = ''
   
        prompt = ''

        if retriever_list:
            prompt = retriever_list[0]['app_info']['prompt']
     
        graph_placeholder = "<%%%%graph%%%%>"
        prompt += graph_placeholder
      
        limit = ("\n\n## 限制:\n"
                 "- 仅提供知识相关内容，不回答其他无关问题。\n"
                 "- 输出内容需严格按照给定格式组织。\n"
                 "- 尽量使用易懂的语言，避免专业复杂术语，方便用户理解。\n"
                 "- 每次直接回复内容 其他无关信息不要返回。\n"
                 "- 每次将知识进行通俗易懂的总结。\n"
                 "- 有以下知识库<%%%%replace_agent%%%%>请用里面最合适的**一个**知识库内容回答\n"
                 "- 如果知识库内容为空，则直接输出‘抱歉暂未获取到相关信息’然后结束。\n"
                 "- 如果输入的内容和上下无关不要进行多余的输出，直接输出‘抱歉暂未获取到相关信息’然后结束。")

        app_name_desc = ""
        for item in retriever_list:
            app_name_desc += item.get('app_info', {}).get('app_name')
            app_name_desc += ','
        limit = limit.replace("<%%%%replace_agent%%%%>", app_name_desc)
        knowledge_content = '\n\n## 知识库内容\n'
        doc_ids = list()
        chunk_ids = list()
       
        #TODO: 添加记录
        cache_data = dict()
        index = 1
        for item in retriever_list:
            doc_id = item['_source']['doc_id']
            app_name = item.get('app_info', {}).get('app_name')
            doc_name = cache_data.get(doc_id)
            if not doc_name:
                try:
                    doc_name = KnowledgeOrm(self.corpid).get_doc_list_by_doc_ids([doc_id])[0]['doc_name']
                except:
                    doc_name = doc_id
                finally:
                    cache_data[doc_id] = doc_name
            knowledge_content += f'### 来源 <<{doc_name}>> 所属知识库：<<{app_name}>> 片段[{index}]: \n {item["_source"]["description"]}\n'
            doc_ids.append(doc_id)
            chunk_ids.append(item['_source']['chunk_id'])
            chunk_contents_list.append(item['_source']['description'])
            index += 1
            if item['_source']['chunk_id'] not in chunk_ids_content_map:
                chunk_ids_content_map[item['_source']['chunk_id']] = item['_source']['description']
      
        
        message_content = f'## 输入内容\n### {question}\n\n'
        
        abstract_prompt = ""
        abstract_list = self.knowledge.get_abstract_list(doc_ids)
        if abstract_list:
            print(f"abstract_list: {abstract_list}")
            abstract_prompt = "\n\n## 摘要:\n"
            for item in abstract_list:
                abstract_prompt += f"# {item['abstract']}\n"

        content = prompt + limit + memory_result + knowledge_content + abstract_prompt + message_content

        graph_list = []
        graph_prompt = self.get_graph_from_list(retriever_list)
        content = content.replace(graph_placeholder, graph_prompt)
        info = {
            'content': content,
            'doc_ids': doc_ids,
            'chunk_ids': chunk_ids,
            'chunk_contents_list': chunk_contents_list,
            'chunk_ids_content_map': chunk_ids_content_map,
            'graph_list': graph_list,
            'abstract_list': abstract_list,
            'memory': memory_result
        }
        return info
    def get_graph_from_list(self, retriever_list):
        doc_ids = list()
        graph_prompt = ''
        for item in retriever_list:
            if item.get('app_info', {}).get('is_graph'):
                doc_ids.append(item['_source']['doc_id'])

        if not doc_ids:
            return graph_prompt
        graph_list = self.knowledge.get_graph_list_by_doc_ids(doc_ids)
        if graph_list:
            graph_prompt += "\n\n## 图谱:\n"
            for item in graph_list:
                graph_prompt += f"# {item['graph_content']}\n"
     
        return graph_prompt 
    def get_ai_app_info_simple(self, app_id, args = None):
        app_detail = self.model.get_ai_app_detail(app_id)
        knowledge_ids = self.batch_fill_knowledge_ids(app_detail.get('children', []), args)

        if not knowledge_ids:
            logger.info(f"当前用户对聚合应用所有知识库都没权限")
            app_detail['no_auth_knowledge_ids'] = True
        app_detail['no_auth_knowledge_ids'] = False
        return app_detail

class TestAppController(BaseController):
    @staticmethod
    def get():
        try:
            message = "hello"
            old_content = ""
            aigc_type_id = "tongyiqianwen_72b"
            model_path = "jusure-llm"
            model_id = 'fc0c8560-42d1-4b3b-b658-7a99df8f7564'
            app_controller = AppController("wpyUJNCQAAO_dAnLQRrCGlq4RlKfmtcQ")
            logger.info("***")
            app_controller.ai_app_chat(message, old_content, aigc_type_id, model_path, True, model_id=model_id)
        except Exception as e:
            logger.error(f"An error occurred: {e}")

class FlowAppController(BaseController):
    def __init__(self, *args):
        super().__init__(*args)
        self.model = AppOrm(self.corpid)
        self.tp_user_id = self.user_info.get('tp_user_id')

    def work_flow_run(self, task, app_id, flow_id, question, session_id):
        corpid = self.corpid
        controller = AppController()
        knowledge_controller = KnowledgeController()

        app_info = self.model.get_app_sample_detail(app_id)
        flow_app_info = app_info.get('flow_app')
        session_log = app_info.get('session_log')
        session_no = int(app_info.get('session_no', 0))

        if not session_id:
            session_id = AppController().create_app_session(app_id, self.tp_user_id, question[0:20], [])
            is_first = True
        else:
            is_first = False

        message_id = controller.create_app_session_record(session_id, flow_id, app_id, is_first, question, None, app_id)

        if flow_app_info:
            flow_app_id = flow_app_info[0]['app_id']
            version_id = flow_app_info[0]['version_id']

            task.async_add_session_flow_record.delay(corpid, -1, flow_id, 'question', content=question)

            input = [{'type': "text", 'value': question}]
            params = {'id': flow_app_id, 'version_id': version_id, 'stream': True, 'input': input, 'flow_id': flow_id}

            # 会话追踪，收集上下文数据
            if session_log:
                others = {'session_id': session_id, 'session_no': session_no}
                _data = ChatMongo(corpid).get_chatbi_session_record(session_id)
                record_info = _data.get('record_info', []) if _data else []
                role_map = {'user': 'user', 'know_answer': 'assistant'}
                if session_no:
                    record_info = record_info[-session_no * 2:]
                others['old_content'] = [{'role': role_map.get(x['role']), 'content': x['content']}
                                         for x in record_info if role_map.get(x['role'])]
                params['others'] = others

            flow_ret = ThirdServerController().work_flow_ret(params)

            task.async_add_session_flow_record.delay(corpid, 0, flow_id, 'app_info', data_json=app_info)

            def run():
                final_images_list = []
                doc_images_list = []
                hit_doc_list = []
                chunk_ids = []
                doc_ids = []
                answer = ''
                had_yield = False
                nonlocal flow_ret

                if flow_ret is not None:
                    for _data in flow_ret:
                        position = _data['position']
                        if position == 'stop':
                            break
                        node_type = _data['node_type']
                        node_data = _data['output'].get('data') or {}

                        if node_type == 'qa-data' and position == 'end':
                            if node_data:
                                answer = node_data.get('answer')
                            task.async_add_session_flow_record.delay(corpid, 1, flow_id, 'qa_check', content=answer)
                            if answer:
                                yield answer, final_images_list, hit_doc_list, doc_images_list, 'doing', node_type
                                had_yield = True
                                break

                        elif node_type in ['rerank', 'rag'] and position == 'end':
                            chunk_ids = node_data.get('chunk_ids', [])
                            doc_ids = node_data.get('doc_ids', [])
                            flow_log_json = {'chunk_score_map': node_data.get('chunk_score_map', {}),
                                             'graph_list': node_data.get('graph_list',[]),
                                             'doc_map_abstract': node_data.get('doc_map_abstract', {})}
                            task.async_add_session_flow_record.delay(corpid, 2, flow_id, 'search_ret', data_json=flow_log_json)

                        elif node_type == 'r-model':
                            if type(node_data) is str:
                                answer = node_data
                            else:
                                answer = node_data.get('answer', '')
                            if position == 'doing':
                                yield answer, final_images_list, hit_doc_list, doc_images_list, position, node_type
                                had_yield = True

                            elif position == 'end':
                                if chunk_ids and doc_ids:
                                    final_images_list = knowledge_controller.get_chunk_hits_images(chunk_ids)
                                    hit_doc_list = controller.get_doc_list_by_doc_ids(doc_ids, chunk_ids)
                                    doc_images_list = knowledge_controller.get_chunk_hits_images_v2(chunk_ids, controller, question, app_info)
                                task.async_add_session_flow_record.delay(corpid, 5, flow_id, 'answer', content=answer)
                                yield answer, final_images_list, hit_doc_list, doc_images_list, position, node_type
                                had_yield = True
                        elif node_type == 'build_report':
                            if type(node_data) is str:
                                report = node_data
                            else:
                                report = node_data.get('answer', '')
                            if position == 'doing':
                                yield report, final_images_list, hit_doc_list, doc_images_list, position, node_type
                                had_yield = True
                            elif position == 'end':
                                task.async_add_session_flow_record.delay(corpid, 5, flow_id, 'answer', content=report)
                                had_yield = True

                        elif node_type == 'agent-MCP':
                            if type(node_data) is str:
                                answer = node_data
                            else:
                                answer = node_data.get('answer', '')
                            if position == 'doing':
                                yield answer, final_images_list, hit_doc_list, doc_images_list, position, node_type
                                had_yield = True

                            elif position == 'end':
                                task.async_add_session_flow_record.delay(corpid, 5, flow_id, 'answer', content=answer)
                                had_yield = True

                # 默认返回值
                if not had_yield:
                    yield answer, final_images_list, hit_doc_list, doc_images_list, 'doing', None
            return run, message_id, session_id
        return None


class ServiceController(BaseController):
    def __init__(self, *args):
        super().__init__(*args)
        self.model = ServiceOrm(self.corpid)
        self.tp_user_id = self.user_info.get('tp_user_id')

    def service_list(self, key_word):
        ret = [{'title': "工作流服务", 'childList': self.model.flow_app_list(key_word)}]
        return ret
