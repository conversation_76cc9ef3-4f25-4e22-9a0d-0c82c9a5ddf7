from json import tool
import re
from tracemalloc import start

from numpy import full
from regex import P
from modeler.mongo.ai_app_mg import AiAppMg
from utils import tools
from . import BaseController
import datetime
from dateutil import rrule



class StatisticsAppController(BaseController):

    def __init__(self, *args):
        super(StatisticsAppController, self).__init__(*args)
        self.mg = AiAppMg(self.corpid)

    @property
    def statistics_basic_dict(self):
        return {
            "app_ring": "智能体环比",
            "app_count": "智能体数量",
            "user_count": "用户数量",
            "user_ring": "用户环比",
            "new_app_count": "新增智能体数量",
            "new_user_count": "新增用户数量",
            "new_app_ring": "新增智能体环比",
            "new_user_ring": "新增用户环比",
            "session_rounds_count": "对话轮数",
            "session_count": "对话数量",
            "session_rounds_ring": "对话轮数环比",
            "session_ring": "对话数量环比",
            "session_duration": "人均对话时长",
            "session_duration_ring": "人均对话时长环比",
            "session_deep": "人均对话深度",
            "session_deep_ring": "人均对话深度环比",
        }
    
    @property
    def session_statistics_by_date_dict(self):
        return {
            "date": "日期",
            "user_count": "用户数量",
            "session_rounds_count": "对话轮数",
            "average_duration": "人均对话时长",
            "average_depth": "对话平均深度"
        }

    @property
    def sesstion_order_dict(self):
        return {
            "order_session_list": "对话轮数排名",
            "order_user_list": "用户数量排名",
            "order_question_list": "问题排名",
        }
        
        
    def return_statistics_basic_result(self, today_time, yesterday_time, today_start_time, yesterday_start_time):
        res_dict = dict()
        temp_app_count, temp_ring_count = self.get_statistics_app_by_app_ring_day(today_time, yesterday_time)
        res_dict.update({"app_ring": round(temp_ring_count, 2)})
        res_dict.update({"app_count": temp_app_count})
        
        temp_user_dict, temp_user_ring_dict = self.get_statistics_app_by_user_ring_day(today_time, yesterday_time)
        res_dict.update({"user_count": temp_user_dict})
        res_dict.update({"user_ring": round(temp_user_ring_dict, 2)})
        
        temp_new_app_count, temp_new_app_ring = self.get_statistics_app_by_app_time_quantum_ring_day(today_time, yesterday_time, today_start_time, yesterday_start_time)
        temp_new_user_count, temp_new_user_ring = self.get_statistics_app_by_user_time_quantum_ring_day(today_time, yesterday_time, today_start_time, yesterday_start_time)
        res_dict.update({"new_app_count": temp_new_app_count})
        res_dict.update({"new_user_count": temp_new_user_count})
        res_dict.update({"new_app_ring": round(temp_new_app_ring, 2)})
        res_dict.update({"new_user_ring": round(temp_new_user_ring, 2)})

        temp_session_rounds_count, temp_session_rounds_ring = self.get_session_rounds_rings_count(today_time, yesterday_time)
        temp_session_count, temp_session_ring = self.get_session_rings_count(today_time, yesterday_time)
        res_dict.update({"session_rounds_count": temp_session_rounds_count})
        res_dict.update({"session_count": temp_session_count}) 
        res_dict.update({"session_rounds_ring": round(temp_session_rounds_ring, 2)})
        res_dict.update({"session_ring": round(temp_session_ring, 2)})

        temp_sesstion_duration, temp_session_duration_ring = self.get_session_duration_rings_count(today_time, yesterday_time)
        
        res_dict.update({"session_duration": round(temp_sesstion_duration, 2)})
        res_dict.update({"session_duration_ring": round(temp_session_duration_ring, 2)})

        temp_session_deep, temp_session_deep_ring = self.get_session_deep_rings_count(today_time, yesterday_time)
        res_dict.update({"session_deep": round(temp_session_deep, 2)})
        res_dict.update({"session_deep_ring": round(temp_session_deep_ring, 2)})

        return  res_dict
    
    def return_session_statistics_by_date_result(self, start_time, end_time):
        start_time = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
        end_time = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
        date_range = list(rrule.rrule(rrule.DAILY, dtstart=start_time, until=end_time))
        result = self.mg.get_statistics_session_by_date(start_time, end_time)
        full_result = []
        for date in date_range:
            date_str = date.strftime("%Y-%m-%d")
            found = False
            for doc in result:
                if doc['date'] == date_str:
                    full_result.append(doc)
                    found = True
                    break
            if not found:
                full_result.append({
                    "date": date_str,
                    "user_count": 0,
                    "session_rounds_count": 0,
                    "average_duration": 0,
                    "average_depth": 0
                })
        return full_result
    
    def return_session_statistics_order_result(self):
        order_session_list = self.mg.get_statistics_order_session()
        order_user_list = self.mg.get_statistics_order_user()
        order_question_list = self.mg.get_statistics_order_question()
        return {"order_session_list": order_session_list, "order_user_list": order_user_list, "order_question_list": order_question_list}


    def get_compound_app_statistics(self, args):
        return self.mg.get_compound_app_statistics(args)

    def get_statistics_app_by_app_ring_day(self, today_time, yesterday_time):
        
        today_count = self.get_statistics_app_by_app_count(today_time)
        yesterday_count = self.get_statistics_app_by_app_count(yesterday_time)
        
        if yesterday_count == 0:
            return today_count, -9999
        return today_count, ((today_count-yesterday_count) / yesterday_count)  * 100

    def get_statistics_app_by_app_count(self, the_time):
        return self.mg.get_statistics_app_by_field_count(the_time, 'app_id')
    

    def get_statistics_app_by_user_ring_day(self, today_time, yesterday_count):
        
        today_count = self.get_statistics_app_by_user_count(today_time)
        yesterday_count = self.get_statistics_app_by_user_count(yesterday_count)
        
        if yesterday_count == 0:
            return today_count, -9999
        return today_count, ((today_count-yesterday_count) / yesterday_count)  * 100

    def get_statistics_app_by_user_count(self, the_time):
        return self.mg.get_statistics_app_by_field_count(the_time, 'tp_user_id')
    
    def get_statistics_app_by_app_time_quantum_count(self, start_time, end_time):
        return self.mg.get_statistics_app_by_field_time_quantum_count(start_time, end_time, 'app_id')

    def get_statistics_app_by_user_time_quantum_count(self, start_time, end_time):
        return self.mg.get_statistics_app_by_field_time_quantum_count(start_time, end_time, 'tp_user_id')
        
    def get_statistics_app_by_user_time_quantum_ring_day(self, today_time, yesterday_time, today_start_time, yesterday_start_time):
        
        today_count = self.get_statistics_app_by_user_time_quantum_count(today_start_time, today_time)
        yesterday_count = self.get_statistics_app_by_user_time_quantum_count(yesterday_start_time, yesterday_time)
        
        if yesterday_count == 0:
            return today_count, -9999
        return today_count, ((today_count-yesterday_count) / yesterday_count)  * 100

    def get_statistics_app_by_app_time_quantum_ring_day(self, today_time, yesterday_time, today_start_time, yesterday_start_time):
        
        today_count = self.get_statistics_app_by_app_time_quantum_count(today_start_time, today_time)
        yesterday_count = self.get_statistics_app_by_app_time_quantum_count(yesterday_start_time, yesterday_time)
        
        if yesterday_count == 0:
            return today_count, -9999
        return today_count, ((today_count-yesterday_count) / yesterday_count)  * 100

    def get_session_rounds_rings_count(self, today_time, yesterday_time):
        
        today_count = self.mg.get_statistics_app_count_by_query_dict({"session_start_time": {"$lte": today_time}})
        yesterday_count = self.mg.get_statistics_app_count_by_query_dict({"session_start_time": {"$lte": yesterday_time}})
        if yesterday_count == 0:
            return today_count, -9999
        return today_count, ((today_count-yesterday_count) / yesterday_count)  * 100

    def get_session_rings_count(self, today_time, yesterday_time):
        
        today_count = self.mg.get_statistics_app_count_by_query_dict({"is_new_session": True, "session_start_time": {"$lte": today_time}})
        yesterday_count = self.mg.get_statistics_app_count_by_query_dict({"is_new_session": True, "session_start_time": {"$lte": yesterday_time}})
        if yesterday_count == 0:
            return today_count, -9999
        return today_count, ((today_count-yesterday_count) / yesterday_count)  * 100
    
    def get_session_duration_rings_count(self, today_time, yesterday_time):
        today_durantion = self.mg.get_statistics_session_duration(today_time)
        yesterday_durantion = self.mg.get_statistics_session_duration(yesterday_time)
        if yesterday_durantion == 0:
            return today_durantion, -9999
        return today_durantion, ((today_durantion-yesterday_durantion) / yesterday_durantion)  * 100
    
    def get_session_deep_rings_count(self, today_time, yesterday_time):
        today_deep = self.mg.get_statistics_session_deep(today_time)
        yesterday_deep = self.mg.get_statistics_session_deep(yesterday_time)
        if yesterday_deep == 0:
            return today_deep, -9999
        return today_deep, ((today_deep-yesterday_deep) / yesterday_deep)  * 100
    

class StatisticsAppDetailController(BaseController):
    
    def __init__(self, *args):
        super(StatisticsAppDetailController, self).__init__(*args)
        self.mg = AiAppMg(self.corpid)

    @property
    def detail_dict(self):
        return {
            "date": "时间",
            "user_count": "用户数",
            "success_turns": "成功对话轮数",
            "failure_turns": "未命中对话轮数",
            "average_turns": "人均对话轮数",
            "total_duration": "总对话时长",
            "average_duration": "人均对话时长",
            "new_sessions": "对话数量",
            "average_depth": "对话平均深度",
            "average_response_time": "平均响应时长",
            "average_session_per_user": "人均对话数量",
            "average_session_duration": "对话平均时长",
            "total_token_consumption": "token消耗",
            "average_token_consumption_per_user": "人均token消耗",
            "average_token_consumption_per_session": "对话平均token消耗",
            }
    
    @property
    def field_dict(self):
        return {
            "user": "用户数量",
            "session": "会话数量",
            "turns": "会话轮数",
            "duration": "人均对话时长",
            "depth": "对话平均深度"
        }
    
    @property
    def mode_dict(self):
        return {
            'daily': "天模式", 
            'weekly': "周模式", 
            'monthly': "月模式"
        }
    
    
    def get_statistics_count_ring_by_field(self, app_id, current_time, field_name, mode):
        try:
            date_obj = tools.str_to_datetime(current_time)
        except Exception as e:
            return str(e), str(e)
        last_period_start, last_period_end, this_period_start, this_period_end = tools.calculate_chain_ratio_time_ranges(date_obj, mode)
        period_count, last_period_count = 0, 0
        match field_name:
            case "user":
                period_count = self.mg.get_count_by_user(app_id, this_period_start, this_period_end)
                last_period_count = self.mg.get_count_by_user(app_id, last_period_start, last_period_end)
            case "session":
                period_count = self.mg.get_count_by_session(app_id, this_period_start, this_period_end)
                last_period_count = self.mg.get_count_by_session(app_id, last_period_start, last_period_end)
            case "turns":
                period_count = self.mg.get_count_by_session_turns(app_id, this_period_start, this_period_end)
                last_period_count = self.mg.get_count_by_session_turns(app_id, last_period_start, last_period_end)
            case "duration":
                period_count = self.mg.get_average_session_per_user(app_id, this_period_start, this_period_end)
                last_period_count = self.mg.get_average_session_per_user(app_id, last_period_start, last_period_end)
            case "depth":
                period_count = self.mg.get_average_depth(app_id, this_period_start, this_period_end)
                last_period_count = self.mg.get_average_depth(app_id, last_period_start, last_period_end)
            case _:
                return 'get_statistics_count_ring_by_field: no this field' * 2
        if last_period_count == 0:
            return period_count, -9999
        return period_count, ((period_count-last_period_count) / last_period_count)  * 100
        

    def get_statistics_detail_by_date_result(self, start_time, end_time, app_id):
        start_time = datetime.datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
        end_time = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
        date_range = list(rrule.rrule(rrule.DAILY, dtstart=start_time, until=end_time))
        result_list = self.mg.get_statistics_detail_by_date(start_time, end_time, app_id)
        full_result = []
        for date in date_range:
            date_str = date.strftime("%Y-%m-%d")
            found = False
            for doc in result_list:
                if doc['date'] == date_str:
                    full_result.append(doc)
                    found = True
                    break
            if not found:
                full_result.append({
                    "date": date_str,
                    "user_count": 0,
                    "success_turns": 0,
                    "failure_turns": 0,
                    "average_turns": 0,
                    "total_duration": 0,
                    "average_duration": 0,
                    "new_sessions": 0,
                    "average_depth": 0,
                    "average_response_time": 0,
                    "average_session_per_user": 0,
                    "average_session_duration": 0,
                    "total_token_consumption": 0,
                    "average_token_consumption_per_user": 0,
                    "average_token_consumption_per_session": 0,
                })
        return full_result

    def get_question_by_app_id(self, app_id):
        order_question_list = self.mg.get_statistics_order_question(app_id, limit=100)
        return order_question_list