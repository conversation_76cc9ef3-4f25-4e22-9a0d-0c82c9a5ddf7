#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/4/9 15:04
# <AUTHOR> z<PERSON><PERSON>
# @File    : chat_view_controller.py
# @Comment :
import json, copy

from modeler.mongo.chat_mongo import ChatMongo
from modeler.mysql.app_orm import AppOrm
from puppet.chat_sdk import *
from . import BaseController
from lib_func.const_map import *
from lib_func.logger import logger


class ChatViewController(BaseController):
    """
    根据view_id、model_id 确定要调用的方法
    """
    def __init__(self, model_id, app_model: AppOrm = None, mongo: ChatMongo = None, view_id=FREE_VIEW_ID, prompt ='',old_content=None, old_flag=None, *args, **kwargs):
        super(ChatViewController, self).__init__(*args, **kwargs)
        self.view_id = view_id
        self.model_id = model_id
        self.app_model = app_model or AppOrm(self.corpid)
        self.mongo = mongo
        self.chat_model = None
        self.prompt = prompt
        self.kwargs = kwargs
        self.model_code = ''
        self.app_key,self.app_secret = None, None
        self.old_content = old_content
        self.old_flag = old_flag
        self._initialize()
    
    def _initialize(self):
        # self._check_view_model()
        self._get_view_supplement()
        if not self.prompt:
            self.prompt = self.view_supplement.prompt or ''
        self._get_model_by_view_id()
    
    def _get_model_conf(self):
        self.model_conf = self.app_model.get_model(model_id=self.model_id)
        self.model_code = self.model_conf.aigc_type_id
        self.app_key, self.app_secret = self.app_model.get_key_secret(self.model_code)
        if not self.model_conf:
            raise f'模型ID不存在！'
        if not self.model_code:
            raise f'模型编码不存在！'
        # if not self.app_key or not self.app_secret:
        #     raise f'{self.model_conf.model_name}未配置！'

    def _get_view_supplement(self):
        self.view_supplement = self.app_model.get_menu_supplement(self.view_id)

    def _check_view_model(self):
        """
        检查菜单与模型是否匹配
        匹配则什么都不做
        不匹配则修改model_id为菜单支持的model_id
        """
        if not self.view_supplement:
            return
        view_model_ids = self.view_supplement.model_ids or ''
        view_model_ids = view_model_ids.split(',')
        if self.model_id in view_model_ids:
            return
        if view_model_ids and view_model_ids[0]:
            self.model_id = view_model_ids[0]

    def handle_free_dialogue(self):
        # 自由对话/文案续写 TODO：图像解析只有一个模型，需要提取配置
        self.chose_model()

    def handle_code_write(self):
        # 代码生成
        self.chose_model()
    
    def handle_text_to_img(self):
        # 文生图
        # if self.model_id == CHATGPT_MODEL_ID:
        #     app_key, app_secret = self.app_model.get_key_secret(CHATGPT_CODE)
        #     if not app_key and not app_secret:
        #         raise f'{self.model_conf.model_name}未配置！'
        #     self.get_chat_gpt_model(data_type='text2img', dalle_flag=True, api_key=app_key)
        #     return
        # self.model_id = CHATGPT_MODEL_ID
        # self._get_model_conf()
        # self.get_chat_gpt_model(data_type='text2img')
        self.chat_model = True
    
    def handle_img_to_img(self):
        # 多模态-图生图
        # self.model_id = CHATGPT_MODEL_ID
        # self._get_model_conf()
        # self.get_chat_gpt_model(data_type='img2img')
        self.chat_model = True
    
    def handle_img_to_record(self):
        # 截图生成跟进记录
        self.chose_model()
        chat = copy.deepcopy(self.chat_model)
        config_builder = ModelConfigBuilder()
        config_builder.set_param({
            "chat": chat,
            "prompt": self.prompt,
        })
        self.chat_model = ModelFactory.get_chat_sdk_by_view_id(self.view_id, config_builder)

    
    def handle_material_view(self):
        # 素材推荐
        self.model_id = AIGC_CONTENT_MODEL_ID  # 固定模型id保证能查到历史记录
        self._get_model_conf()
        config_builder = ModelConfigBuilder()
        config_builder.set_param({
            "corp_id": self.corpid,
            "batch": self.kwargs.get('batch'),
            "app_model": self.app_model,
            "model_path": self.model_conf.model_path,
            "dims": self.model_conf.dims,
            "app_key": self.app_key,
            "app_secret": self.app_secret,
        })
        self.chat_model = ModelFactory.get_chat_sdk_by_view_id(self.view_id, config_builder)

    def handle_case_view(self):
        # 雷达推荐
        # todo 向量生成固定使用文心一言, 接入其他模型
        self.model_id = AIGC_MODEL_ID
        self._get_model_conf()
        config_builder = ModelConfigBuilder()
        config_builder.set_param({
            "model_path": self.model_conf.model_path,
            "dims": self.model_conf.dims,
            "corp_id": self.corpid,
            "app_key": self.app_key,
            "app_secret": self.app_secret,
            "batch": self.kwargs.get('batch'),
            "model_code": self.model_code,
        })
        self.chat_model = ModelFactory.get_chat_sdk_by_view_id(self.view_id, config_builder)

    def handle_ai_number(self):
        config_builder = ModelConfigBuilder()
        config_builder.set_param({
            "app_model": self.app_model,
            "corp_id": self.corpid,
        })
        self.chat_model = ModelFactory.get_chat_sdk_by_view_id(self.view_id, config_builder)
    
    def handle_bing_view(self):
        # Bing搜索模型
        self.chose_model(old_flag=False)
        # use_model: 为使用bing搜索模型时，选择的大模型，如：kimi,deepseek-r1
        use_model = self.chat_model
        self.model_id = BING_MODEL_ID
        self._get_model_conf()
        config_builder = ModelConfigBuilder()
        config_builder.set_param({
            "url": self.model_conf.model_url,
            "api_key": self.app_key,
            "corp_id": self.corpid,
            "prompt": self.prompt,
            "chat_model": use_model
        })
        self.chat_model = ModelFactory.get_chat_sdk_by_view_id(self.view_id, config_builder)
    
    def _get_model_by_view_id(self):
        # 文生图、图生图，采用call_generate 的配置
        
        view_handlers = {
            FREE_VIEW_ID: self.handle_free_dialogue,
            DOC_WRITE_ID: self.handle_free_dialogue,
            DATA_SYNTHESIS_ID: self.handle_free_dialogue,
            IMG_TO_TEXT: self.handle_free_dialogue,
            TEXT_TO_IMG_ID: self.handle_text_to_img,
            IMG_TO_IMG_ID_1: self.handle_img_to_img,
            IMG_TO_RECORD_ID: self.handle_img_to_record,
            CODE_WRITE_ID: self.handle_code_write,
            MATERIAL_VIEW_ID: self.handle_material_view,
            CASE_VIEW_ID: self.handle_case_view,
            AI_NUMBER_VIEW_ID: self.handle_ai_number,
            BING_VIEW_ID: self.handle_bing_view
        }
        self._get_model_conf()
        handler = view_handlers.get(self.view_id)
        if handler:
            handler()
        else:
            raise ValueError(f'未找到视图 ID {self.view_id} 的处理方法')
    
    
    def chose_model(self, end=False, old_flag=True):
        """
        _get_model 会调用

        """
        try:
            if self.model_code == CHATGPT_CODE:
                self.get_chat_gpt_model(old_flag=old_flag)
            elif self.model_code == CHATGLM_CODE:
                self.get_chat_glm_model(old_flag=old_flag)
            elif self.model_code == LLAMA_CODE:
                self.get_chat_glm_model(cfg='aigc_chatglm', old_flag=old_flag)
            else:
                # 适配  WENXIN_CODE, QIANWEN_CODE, QWEN_72B_CODE
                self.get_chat_model(old_flag)
            
        except Exception as e:
            logger.error(f"Error choosing model {self.model_code}: {e}")
            if end:
                return
            self._check_view_model()
            self._get_model_conf()
            self.chose_model(end=True)

    def check_old_content(self, old_flag):
        old_content = []

        # 添加主控字段
        if self.old_flag is None or self.old_flag:
            if old_flag:
                session_id = self.kwargs.get('session_id')
                old_content = self.old_content if self.old_content else self.get_old_content(session_id)
        return old_content
    
    def get_chat_model(self, old_flag):
        old_content = self.check_old_content(old_flag)
        config_builder = ModelConfigBuilder()
        enable_return_reasoning_content = True if self.model_conf.has_reason else False
        config_builder.set_param({
            "app_key": self.app_key,
            "app_secret": self.app_secret,
            "model": self.model_conf.model_path,
            "model_url": self.model_conf.model_url,
            "prompt": self.prompt,
            "old_content": old_content,
            "enable_return_reasoning_content": enable_return_reasoning_content
        })
        self.chat_model = ModelFactory.get_chat_sdk_by_model_code(self.model_code, config_builder)
    
    def get_chat_glm_model(self, cfg='aigc_chatglm', old_flag=True):
        old_content = self.check_old_content(old_flag)
        config_builderv = ModelConfigBuilder()
        config_builderv.set_param({
            "cfg": cfg,
            "prompt": self.prompt,
            "old_content": old_content
        })
        self.chat_model = ModelFactory.get_chat_sdk_by_model_code(self.model_code, config_builderv)

    def get_chat_gpt_model(self, data_type='text', old_flag=True, dalle_flag=False, api_key=''):
        gpt_url = self.model_conf.model_url
        if gpt_url:
            gpt_url = json.loads(gpt_url).get('POST')
            if not gpt_url:
                raise f'ChatGPT未配置！'
        old_content = []
        if data_type == 'text':
            self.check_old_content(old_flag)
        config_builder = ModelConfigBuilder()
        config_builder.set_param({
            "gpt_url": gpt_url,
            "data_type": data_type,
            "prompt": self.prompt,
            "old_content": old_content,
            "model": self.model_conf.model_path,
            "corpid": self.corpid,
            "dalle_flag": dalle_flag,
            "api_key": api_key
        })
        self.chat_model = ModelFactory.get_chat_sdk_by_model_code(self.model_code, config_builder)

    def get_old_content(self, session_id):
        old_content = []
        chat_record = self.mongo.get_chat_record(session_id)
        for record in chat_record:
            question, answer = record.get("question"), record.get("answer")
            if question or answer:                
                old_content.append({"role": "user", "content": question})
                old_content.append({"role": "assistant", "content": answer})
        # 只取最新的5次提问及回答, 1. 避免提问超出接口接收长度限制(文心一言限制11200字符), 2. 减少提问tokens
        old_content = old_content[::-1][:11][::-1]
        if old_content and old_content[0].get('role') == 'assistant':
            old_content = old_content[1:]
        return old_content