import settings
from lib_func.const_map import StatusMap
from horticulture.cache_decoration import cache_ret
from horticulture.permission import SuperManager
from . import BaseController
from modeler.mysql.user_orm import *


class TPUserController(BaseController):
    """ 用户管理 """

    def __init__(self, *args):
        super(TPUserController, self).__init__(*args)
        self.model = TPUserM(self.corpid)

    # 用户信息详情
    @cache_ret('user-info-live', 0, 60)
    def user_info(self, user_id, is_up=False):
        return self.model.user_info(user_id, delete_flag=str(settings.DELETE_FLAG_FALSE))

    # 用户信息详情
    @cache_ret('user_info_by_code', 0, 60)
    def user_info_by_code(self, user_code, is_up=False):
        return self.model.user_info(user_code, delete_flag=str(settings.DELETE_FLAG_FALSE))

    def bulk_add_user_info(self, data_list):
        return self.model.bulk_add_user_info(data_list)

    def add_user_info(self, user_info):
        return self.model.add_user_info(user_info)

    def up_info(self, user_id, up_conf):
        return self.model.up_user_info(user_id, up_conf)

    def page_user(self, key_word, po, ps):
        return self.model.page_user_info(key_word, po, ps)


class DPTController(BaseController):
    def __init__(self, *args):
        super(DPTController, self).__init__(*args)
        self.model = DepartmentM(self.corpid)

    # 向上部门关联列表
    @cache_ret('dpt-up-rel-dpt', 0, 60)
    def get_up_rel_dps(self, dpt_id, is_up=False):
        return self.model.get_up_rel_dps(dpt_id)

    # 向下部门关联列表
    @cache_ret('dpt-down-rel-dpt', 0, 60)
    def get_down_rel_dps(self, dpt_id, is_up=False):
        return self.model.get_down_rel_dps(dpt_id)

    def bulk_add_dpt_info(self, data_list):
        return self.model.bulk_add_dpt_info(data_list)

    def add_dpt_info(self, dpt_info):
        return self.model.add_dpt_info(dpt_info)

    def up_dpf_info(self, dpt_id, up_conf):
        return self.model.up_dpf_info(dpt_id, up_conf)

    def page_dept(self, key_word, po, ps):
        return self.model.page_dept_info(key_word, po, ps)


class RoleController(BaseController):
    """ 角色管理 """

    def __init__(self):
        super(RoleController, self).__init__()
        self.model = RoleM(self.corpid)

    # 获取用户的角色编号
    @cache_ret('role-user-id-list', 0, 150)
    def get_all_user_role_ids(self, user_id, is_up=False):
        return self.model.get_all_user_role_ids(user_id)

    @cache_ret('get-user-auth-ids', 0, 5)
    def get_user_auth_ids(self, user_id, is_up=False):
        return self.model.get_user_auth_ids(user_id)


class SchemeController(BaseController):
    def __init__(self):
        super(SchemeController, self).__init__()
        self.model = SchemeM(self.corpid)

    # 获取用户的角色编号
    @cache_ret('get-user-all-auth-ids', 0, 5)
    def get_user_auths(self, user_id, is_up=False):
        user_id = TPUserController(self.corpid).user_info_by_code(user_id).get('tp_user_id')
        all_auth_ids = self.model.dpt_scheme_by_uid(user_id) + self.model.role_scheme_by_uid(user_id) + \
                       self.model.user_scheme_by_uid(user_id)
        return all_auth_ids

    @cache_ret('check-role-permission', 0, 5)
    def check_role_permit(self, user_id, role_id, is_up=False):
        if SuperManager not in role_id:
            return [x['scheme_id'] for x in self.get_user_auths(user_id, is_up)]
        return None