#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/4/1 14:43
# <AUTHOR> zhang<PERSON>
# @File    : chat_controller.py
# @Comment :
import json
import time
import uuid
from collections import defaultdict
from datetime import datetime, timedelta

from modeler.mongo.chat_mongo import Chat<PERSON>ong<PERSON>
from puppet.cache import redis_pool
from puppet.mq_sdk import AiGcRabbitMQProducer
from puppet.modal_call import ImgToText,TextToImg
from utils.tools import get_uuid
from . import BaseController
from modeler.mysql.app_orm import AppOrm
from horticulture.cache_decoration import CJsonEncoder
from .chat_view_controller import ChatViewController
from controller.app_controller import AppController
from lib_func.const_map import *
from lib_func.logger import logger
from apps.ai import task
from time import sleep



class ChatController(BaseController):

    def __init__(self, *args):
        super(Chat<PERSON>ontroller, self).__init__(*args)
        self.app_model = AppOrm(self.corpid)
        self.mongo = ChatMongo(self.corpid)

    # @cache_ret('get_menus', 0, 100)
    def get_menus(self, role_ids, model_id=''):
        res = []
        view_supplements = self.app_model.get_menu_supplement()
        view_sort_dict = {obj.view_id: obj.sort for obj in view_supplements}
        view_tag_hello_dict = {obj.view_id: (obj.tag, obj.hello) for obj in view_supplements}
        all_views = self.app_model.get_chat_menus()
        role_view_ids = [one.function_view_id for one in self.app_model.get_chat_menus(role_ids, join_rel_flag=True)]
        first_view_id = ''
        two_three_view_ids = defaultdict(list)
        three_father_id = {}
        view_id_info = {}
        for view in all_views:
            if int(view.view_level_id) == 3:
                two_three_view_ids[view.father_view_id].append(view.function_view_id)
                three_father_id[view.function_view_id] = view.father_view_id
            elif int(view.view_level_id) == 1:
                first_view_id = view.function_view_id
            placeholder = [view.search_content] if view.search_content else []
            view_tag, view_hello = view_tag_hello_dict.get(view.function_view_id) or ('', '')
            view_id_info[view.function_view_id] = {'view_code': view.view_code, 'placeholder': placeholder,
                                                   'view_id': view.function_view_id, 'view_name': view.view_name,
                                                   'child': [], 'view_icon': view.view_icon or '',
                                                   'view_tag': view_tag, 'view_hello': view_hello}
        if first_view_id in role_view_ids:
            # 绑定了一级菜单
            for two_id, three_ids in two_three_view_ids.items():
                two_info = view_id_info.get(two_id)
                for three_id in three_ids:
                    two_info['child'].append(view_id_info.get(three_id))
                if two_info['child']:
                    two_info['child'] = sorted(two_info['child'], key=lambda x: view_sort_dict.get(x['view_id'], 999))
                    res.append(two_info)
            res = sorted(res, key=lambda x: view_sort_dict.get(x['view_id'], 999))
            return res
        two_exist_ids = {}
        for role_id in role_view_ids:
            if role_id in two_three_view_ids:
                # 二级菜单, 创建并将二级菜单下的三级菜单加入
                two_info = view_id_info.get(role_id)
                for three_id in two_three_view_ids.get(role_id):
                    two_info['child'].append(view_id_info.get(three_id))
                if two_info['child']:
                    two_exist_ids[role_id] = two_info
            elif role_id in three_father_id:
                # 三级菜单, 找对应的二级菜单创建父结构, 再写入子结构
                two_id = three_father_id.get(role_id)
                if two_id in two_exist_ids:
                    continue
                two_info = view_id_info.get(two_id)
                if role_id not in two_exist_ids:
                    two_exist_ids[two_id] = two_info
                two_exist_ids[two_id]['child'].append(view_id_info.get(role_id))
        if not two_exist_ids:
            return res
        two_exist_ids[BASE_VIEW_ID]['child'] = sorted(two_exist_ids[BASE_VIEW_ID]['child'],
                                                      key=lambda x: view_sort_dict.get(x['view_id'], 999))
        res = [item for _, item in two_exist_ids.items()]
        res = sorted(res, key=lambda x: view_sort_dict.get(x['view_id'], 999))
        return res

    def __base_analyse_type_obj(self, obj):
        if not obj:
            return {}
        else:
            return {'aigc_type_id': obj.aigc_type_id, 'aigc_type_name': obj.aigc_type_name, 'api_key': obj.api_key,
                    'secret_key': obj.secret_key}

    def get_model_types(self):
        objs = self.app_model.get_model_types()
        ret = list()
        for obj in objs:
            ret.append(self.__base_analyse_type_obj(obj))
        return ret

    def add_model_type(self, params):
        if self.app_model.get_model_by_name(params['aigc_type_name']):
            raise ValueError(StatusMap['name_duplication'])
        params['aigc_type_id'] = str(uuid.uuid1())
        return self.app_model.add_model_type(params)

    def up_model_type(self, qp, up):
        obj = self.app_model.get_model_by_name(up['aigc_type_name'])
        if obj and obj.aigc_type_id != qp['aigc_type_id']:
            raise ValueError(StatusMap['name_duplication'])
        return self.app_model.up_model_type(qp, up)

    def __base_analyse_obj(self, obj):
        if not obj:
            return {}
        else:
            return {"aigc_model_id": obj.aigc_model_id, "model_name": obj.model_name, 'model_aol': obj.aol,
                    "model_icon": obj.model_icon, 'aigc_type_name': obj.aigc_type.aigc_type_name, 'status': obj.status,
                    'aigc_type_id': obj.aigc_type_id, 'model_id': obj.aigc_model_id, 'model_code': obj.model_code,
                    'use_range': obj.use_range, 'dims': obj.dims, 'aol': obj.aol, 'model_url': obj.model_url,
                    'model_path': obj.model_path, 'has_reason': obj.has_reason, 'is_default': obj.is_default,
                    'param_size_b': obj.param_size_b, 'param_config': obj.param_config, 'param_preference': obj.param_preference, 'param_human_rating': obj.param_human_rating
                    }

    def add_ai_model(self, params):
        if params['use_range'] == 3 and not params['dims']:
            return ValueError('dims参数不能为空')
        aigc_model_id = params.pop('aigc_model_id')
        if aigc_model_id:
            self.app_model.check_per_use_range_default(params['is_default'], aigc_model_id)
            return self.app_model.up_ai_model({'aigc_model_id': aigc_model_id}, params)
        else:
            params['aigc_model_id'] = str(uuid.uuid1())
            return self.app_model.add_ai_model(params)

    def up_ai_model(self, qp, up):
        return self.app_model.up_ai_model(qp, up)

    def get_model_list(self, use_range=None):
        """
        获取模型列表
        """
        data = self.app_model.get_model_list(use_range=use_range)
        res = []
        for item in data:
            res.append(self.__base_analyse_obj(item))
        return res

    def get_model_detail(self, aigc_model_id):
        return self.__base_analyse_obj(self.app_model.get_model(aigc_model_id))

    def get_models(self):
        res = []
        # model_type_dict = {obj.aigc_type_id: obj.aigc_type_name for obj in self.app_model.get_model_types()}
        for doc in self.app_model.get_model_list(use_range=[2]):
            res.append(self.__base_analyse_obj(doc))
        return res

    def get_new_session(self, tp_user_id, view_id, model_id, session_id=''):
        session_id = session_id or get_uuid()
        now = datetime.now()
        expire_time = datetime.now() + timedelta(hours=24)  # 过期时间, 同一用户24小时内不创建新session
        data = {'tp_user_id': tp_user_id, 'session_id': session_id, 'create_time': now,
                'expire_time': expire_time, 'view_id': view_id, 'model_id': model_id}
        self.mongo.insert_session(data)
        return session_id

    def get_chat_record(self, tp_user_id, view_id, model_id):
        session_id = self.mongo.get_session(tp_user_id, view_id, model_id)
        if not session_id:
            session_id = self.get_new_session(tp_user_id, view_id, model_id)
            return {'message': [], 'session_id': session_id}
        res = []
        for doc in self.mongo.get_chat_record(session_id, tp_user_id=tp_user_id, view_id=view_id, model_id=model_id):
            res.append(doc)
        return {'message': res, 'session_id': session_id}
    
    def get_async_task_result(self, task_id):
        logger.info('task_id: {}'.format(task_id))
        redis_client = redis_pool.use(redis_pool.aigc_pool)
        task_key = AI_TASK_GENERATE_ASYNC_PREFIX + task_id
        pubsub = redis_client.pubsub()
        pubsub.subscribe(task_key)  # 订阅任务专属频道
        try:
            # 等待消息
            for message in pubsub.listen():
                if message['type'] == 'message':
                    data = json.loads(message['data'])
                    return data
        finally:
            pubsub.unsubscribe(task_id)
        return None
    

    def call_generate_async(self,**kwargs):
        task_id = str(uuid.uuid4())
        task.chat_call_generate_async.delay(self.corpid, task_id, **kwargs)
        logger.info('---- corpid: {},task_id: {}, kwargs: {} ----'.format(self.corpid, task_id, kwargs))
        return json.dumps({'task_id': task_id})
    
    def call_generate_graphic(self, tp_user_id, view_id, model_id, session_id, content, context, graphic_script_type, message_id, img_url='', batch=0, batch_count=1):
        view_supplement = self.app_model.get_menu_supplement(view_id)
        model_id = view_supplement.model_ids if view_supplement.model_ids else model_id
        if not self.mongo.check_session(session_id):
            if self.mongo.check_session(session_id, expire=False):
                self.mongo.update_session_expire(session_id)
            else:
                session_id = self.get_new_session(tp_user_id, view_id, model_id, session_id=session_id)
        view_obj = ChatViewController(model_id, app_model=self.app_model, mongo=self.mongo,old_content=[])
        model_code = view_obj.model_conf.aigc_type_id
        chat_model = view_obj.chat_model
        if chat_model:
            record_question = content
            prompt = view_supplement.prompt
            user_demand = f"使用{graphic_script_type}语法生成{content}"
            content_info = prompt + context + user_demand
            row = ''
            data = ''
            for item in chat_model.receive(content_info, is_stream=True, session_id=session_id, tp_user_id=tp_user_id,
                                            img_url=img_url, message_id=message_id):
                json_item = json.loads(item.decode())
                result = json_item.get('result')
                if not result:
                    continue
                if '\n' in result:
                    result_list = result.split('\n')
                    for i, line in enumerate(result_list):
                        row += line
                        if i < len(result_list) - 1:
                            row += '\n'
                            data += row
                            doc_data = json.dumps({"session_id": session_id,"status": "generating","content": row,"contentType": "text"}, ensure_ascii=False)
                            row = ''
                            yield doc_data
                else:
                    row += result
            if len(data) == 0 and len(row) > 0:
                doc_data = json.dumps({"session_id": session_id,"status": "generating","content": row,"contentType": "text"}, ensure_ascii=False)
                yield doc_data
                data += row
            # 添加结束状态
            yield json.dumps({"session_id": session_id,"status": "finished","content": '',"contentType": "text"}, ensure_ascii=False)
            # 添加到消息记录里
            record_info = {'answer': [], 'prompt_tokens': 0, 'completion_tokens': 0, 'contentType': 'text'}
            record_info['prompt_tokens'] = len(prompt)
            record_info['completion_tokens'] = len(data)
            record_info['answer'] = data
            self.to_record(tp_user_id, view_id, model_id, model_code, session_id, record_question, record_info, message_id, img_url)

    def call_generate(self, tp_user_id, view_id, model_id, session_id, content, message_id, img_url='',temp_prompt='', batch=0, batch_count=1):
        if not self.mongo.check_session(session_id):
            if self.mongo.check_session(session_id, expire=False):
                self.mongo.update_session_expire(session_id)
            else:
                session_id = self.get_new_session(tp_user_id, view_id, model_id, session_id=session_id)
        view_obj = ChatViewController(model_id, view_id=view_id, app_model=self.app_model, mongo=self.mongo,
                                      session_id=session_id, batch=batch)
        model_code = view_obj.model_conf.aigc_type_id
        model_path = view_obj.model_conf.model_path
        aigc_model_id = view_obj.model_conf.aigc_model_id
        prompt = view_obj.prompt
        chat_model = view_obj.chat_model
        if chat_model:
            record_question = content
            record_info = {'answer': '', 'prompt_tokens': 0, 'completion_tokens': 0, 'contentType': 'text'}
            params = {
                    'aigc_type_id':model_code, 'model_path': model_path,'aigc_model_id': aigc_model_id,
                    'message_id':message_id, 'session_id':session_id, 'tp_user_id':tp_user_id,
                    'view_id':view_id, 'record_info': record_info, 'img_url':img_url, 'prompt':prompt, 
                    'temp_prompt':temp_prompt, 
                    'content':content, 'batch_count': batch_count, 'batch': batch,
                }
            # if view_id in [IMG_TO_IMG_ID_1, TEXT_TO_IMG_ID]:
            #     yield self.call_generate_async(**params)
            if view_id in (IMG_TO_IMG_ID_1):
                # to img
                for doc in self.img_generate(**params):
                    yield doc                
            
            elif view_id in (TEXT_TO_IMG_ID):
                for doc in self.img_generate_v1(**params):
                    yield doc
            elif view_id in (CASE_VIEW_ID, MATERIAL_VIEW_ID, AI_NUMBER_VIEW_ID):
                # to json
                for doc in self.json_generate(chat_model, content, session_id, tp_user_id, record_info):
                    yield doc
            elif view_id == IMG_TO_TEXT:
                for doc in self.img_to_text_gen(img_url, session_id, record_info):
                    yield doc
            else:
                # 兼容以前的逻辑
                content = f'{prompt}{content}' if view_id == DATA_SYNTHESIS_ID else content
                # to text
                for doc in self.text_generate(chat_model, content, session_id, tp_user_id, record_info,
                                              img_url=img_url, view_id=view_id, message_id=message_id):
                    yield doc
            # to record
            self.to_record(tp_user_id, view_id, model_id, model_code, session_id, record_question, record_info, message_id, img_url)
    
    def to_record(self, tp_user_id, view_id, model_id, model_code, session_id, record_question, record_info, message_id, img_url=''):
        record = {"tp_user_id": tp_user_id, "view_id": view_id, "model_id": model_id, "model_code": model_code,
                      "session_id": session_id, "question": record_question, "answer": record_info['answer'],
                      "message_id": message_id, "create_time": datetime.now(),
                      'prompt_tokens': record_info['prompt_tokens'],
                      'completion_tokens': record_info['completion_tokens'], "img_url": img_url, 
                      'contentType': record_info['contentType']}
        self.mongo.insert_record(record)
        self.mongo.update_session_expire(session_id)
    
    def text_generate(self, chat_model, content, session_id, tp_user_id, record_info, img_url='', view_id='', message_id=''):
        for item in chat_model.receive(content, is_stream=True, session_id=session_id, tp_user_id=tp_user_id,
                                       img_url=img_url, message_id=message_id):
            json_item = json.loads(item.decode())
            result = json_item.get('result')
            if not result:
                continue
            usage = json_item.get('usage')
            record_info['prompt_tokens'] = usage.get('prompt_tokens') or 0
            record_info['completion_tokens'] = usage.get('completion_tokens') or 0
            doc = json.dumps({"session_id": session_id, "status": "generating", "content": result,
                                "contentType": "text"}, ensure_ascii=False)
            record_info['answer'] += result
            yield doc
        
        if view_id == IMG_TO_RECORD_ID:
            # 聊天截图生成进展后固定加上 时间、用户名
            user_name = self.app_model.get_user_name(tp_user_id)
            content = f"\n\n时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  记录人：{user_name}"
            doc = json.dumps({"session_id": session_id, "status": "generating", "content": content,
                              "contentType": "text"}, ensure_ascii=False)
            record_info['answer'] += content
            yield doc
        yield json.dumps({"session_id": session_id, "status": "finished", "content": "",
                          "contentType": "text"}, ensure_ascii=False)
    
    def img_generate_v1(self, **kwargs):
        content = kwargs.get('content')
        record_info = kwargs.get('record_info')
        session_id = kwargs.get('session_id')
        aigc_type_id = kwargs.get('aigc_type_id')
        model_path = kwargs.get('model_path')
        aigc_model_id = kwargs.get('aigc_model_id')
        batch = kwargs.get('batch')
        batch_count = kwargs.get('batch_count')

        prompt = f'''
        请将```{content}```翻译为英文，只需翻译内容，不需要翻译标点符号。
        '''.strip()

        res_text = ""
        for item, _ in AppController(self.corpid).ai_app_chat(prompt, [], aigc_type_id, model_path, True, model_id=aigc_model_id):
            if item:
                res_text+= item
        text_to_img = TextToImg()
        record_info['contentType'] = "img"
        final_res = []
        try:
            for item in range(batch*batch_count):
                result = text_to_img.get_img(res_text)
                if result['code'] == 0:
                    result_content = result.get('data', '')
                    record_info['answer']+= (result_content +'\n')
                    final_res.append(result_content)
                else:
                    yield json.dumps({"session_id": session_id, "order": item,"status": "finished", "content": "解析失败",
                                "contentType": "text"}, ensure_ascii=False)
            yield json.dumps({"session_id": session_id, "status": 'generating', "content": json.loads(json.dumps(final_res)),
                            "contentType": "img"}, ensure_ascii=False)
            sleep(0.05)
            yield json.dumps({"session_id": session_id, "status": "finished", "content":  json.loads(json.dumps(final_res)),"contentType": "img"}, ensure_ascii=False)
        except Exception as e:
            yield json.dumps({"session_id": session_id, "status": "finished", "content": "解析失败：{}".format(str(e)),
                          "contentType": "text"}, ensure_ascii=False)
        
    def img_to_text_gen(self, img_url, session_id, record_info):
        # TODO: 生成图片描述 后面传model进来
        img_to_text = ImgToText()
        try:
            result = img_to_text.get_text(img_url)

            if result['code'] == 0:
                result_content = result.get('data', '解析失败')
                for str_item in result_content:
                    yield json.dumps({"session_id": session_id, "status": "generating", "content": str_item,
                                      "contentType": "text"}, ensure_ascii=False)
                    sleep(0.05)

                yield json.dumps({"session_id": session_id, "status": "finished", "content": ""}, ensure_ascii=False)
                record_info['answer'] = result_content
                record_info['contentType'] = "text"
            else:
                yield json.dumps({"session_id": session_id, "status": "finished", "content": "解析失败",
                          "contentType": "text"}, ensure_ascii=False)

        except Exception as e:
            yield json.dumps({"session_id": session_id, "status": "finished", "content": "解析失败：{}".format(str(e)),
                          "contentType": "text"}, ensure_ascii=False)

    def img_generate(self, **kwargs):
        session_id = kwargs.get('session_id')
        img_url = kwargs.get('img_url')
        res = []
        # if hasattr(chat_model, 'dalle_flag') and chat_model.dalle_flag:
        #     for item in chat_model.receive(content):
        #         json_item = json.loads(item.decode())
        #         record_info['answer'] = json.dumps(json_item['result'], ensure_ascii=False)
        #         contentType = 'img'
        #         record_info['contentType'] = contentType
        #         yield json.dumps({"session_id": session_id, "status": "finished", "content": json_item['result'],
        #                         "contentType": contentType}, ensure_ascii=False, cls=CJsonEncoder)
        #         return

        img_to_text_res = ImgToText().get_text(img_url)
        if img_to_text_res['code'] == 0:
            kwargs['content'] = img_to_text_res['data']
        
        if kwargs.get('temp_prompt'):
            kwargs['content'] = kwargs['content'].strip() + f'''\n同时，{kwargs['temp_prompt']}'''
         
        for doc in self.img_generate_v1(**kwargs):
            doc = json.loads(doc)
            if doc.get('status') == 'finished':
                res = doc.get('content')
        yield  json.dumps({"session_id": session_id, "status": "finished", "content": res,
                              "contentType": "img"}, ensure_ascii=False)


    def json_generate(self, chat_model, content, session_id, tp_user_id, record_info):
        # 20250213,对model_classes的receive方法进行统一返回格式，view_classes不做处理
        # 此函数是属于 view_classes， chat_model.receivce的返回格式保持原样
        history_list = self.mongo.get_chat_record_history(session_id)
        for item in chat_model.receive(content, is_stream=False, tp_user_id=tp_user_id, history_list=history_list):
            record_info['answer'] = json.dumps(item, ensure_ascii=False)
            contentType = 'text' if isinstance(item, str) else 'json'
            record_info['contentType'] = contentType
            yield json.dumps({"session_id": session_id, "status": "finished", "content": item,
                              "contentType": contentType}, ensure_ascii=False, cls=CJsonEncoder)

    def get_msg(self, message_id, view_id=''):
        redis_client = redis_pool.use(redis_pool.aigc_pool)
        res = []
        if view_id in (TEXT_TO_IMG_ID, IMG_TO_IMG_ID_1):
            img_list = redis_client.get(f'aigc_text2img{message_id}')
            status = "generating"
            if img_list:
                self.mongo.update_record(message_id, {"answer": img_list})
                status = "finished"
            return [{"session_id": "", "status": status, "content": json.loads(img_list or json.dumps([])),
                     "contentType": "img"}]
        for item in redis_client.lrange(message_id, 0, -1):
            res.append(json.loads(item))
        return res

    def get_radar_data(self):
        data = self.app_model.get_radar_data()
        res = []
        for item in data:
            tmp = {"client_radar_id": item.client_radar_id, "radar_title": item.radar_title,
                   "default_pic_url": item.default_pic_url, "qr_code": item.qr_code, 
                   "radar_name": item.radar_name}
            radar_stats = self.mongo.get_radar_stats_info(item.client_radar_id)
            tmp.update(radar_stats)
            res.append(tmp)
        return res

    def update_chatbi_record(self, message_id, record_info):
        self.mongo.update_record(message_id, {"answer": json.dumps(record_info, ensure_ascii=False)})
        return True

    def get_model_detail_by_id(self, aigc_model_id):
        """
        获取AI模型详细信息
        Args:
            aigc_model_id: 模型唯一标识
        Returns:
            dict: 包含模型和类型所有字段的字典
        """
        try:
            model_data = self.app_model.get_model_detail_by_id(aigc_model_id)
            if not model_data:
                logger.warning(f"Model not found: {aigc_model_id}")
                return {}

            return model_data
        except Exception as e:
            logger.error(f"Error getting model detail: {str(e)}", exc_info=True)
            return {"error": str(e)}
