import json
import importlib.util
from datetime import timed<PERSON><PERSON>

from controller.knowledge_controller import KnowledgeController
from . import BaseController
from modeler.mysql.knowledge_orm import KnowledgeOrm, LifeCycleRuleOrm, FileClearRuleORM, DocumentClearRuleOrm
from utils.tools import str_to_date, download_url_file
from controller.app_controller import AppController
from modeler.mongo.knowledge_mg import DocumentClearMg
from settings import KNOWLEDGE_OSS_BUCKET_NAME
from module import minio_util
from lib_func.logger import task_logger
import os
from docx import Document
from io import BytesIO
import pdfplumber
from pptx import Presentation
import pandas as pd
from utils.tools import download_and_verify_scripts, convert_otherfile_to_markdown,convert_docx_by_MarkItDown,convert_xlsx_to_markdown,convert_xls_to_markdown
import settings
class RuleController(BaseController):
    type_list = ['call_word', 'code_logic', 'script']
    source_list = ['built_in', 'custom']

    def __init__(self, *args):
        super(Rule<PERSON><PERSON>roller, self).__init__(*args)
        self.rule_model = LifeCycleRuleOrm(self.corpid)
        self.k_model = KnowledgeOrm(self.corpid)
        self.clear_model = FileClearRuleORM(self.corpid)
        self.clear_mg = DocumentClearMg(self.corpid)
        self.doc_clear_rule = DocumentClearRuleOrm(self.corpid)

    def up_data_life_rule_status(self, data_id, _type, open_life):
        if _type == 'doc':
            self.k_model.update_knowledge_doc(data_id, {'open_life': open_life})
        elif _type == 'kno':
            self.k_model.update_knowledge(data_id, {'open_life': open_life})
        return True

    def up_dic_life_rule(self, doc_id, life_type, expire_date, life_days):
        if life_type == 'date':
            self.k_model.update_knowledge_doc(doc_id, {'life_type': life_type, 'expire_date': str_to_date(expire_date)})
        elif life_type == 'days':
            doc_info = self.k_model.get_knowledge_document_detail(doc_id)
            if doc_info:
                expire_date = doc_info['add_time'] + timedelta(days=life_days)
                self.k_model.update_knowledge_doc(doc_id, {'life_type': life_type, 'expire_date': expire_date})
        return True

    def get_doc_life_rule(self, doc_id):
        doc_info = self.k_model.get_knowledge_document_detail(doc_id)
        if doc_info:
            if doc_info['life_type'] == 'date':
                doc_info['expire_date'] = str(doc_info['expire_date'])
                return doc_info
            elif doc_info['life_type'] == 'days':
                doc_info['life_days'] = (doc_info['expire_date'] - doc_info['add_time']).days
                return doc_info
        return None

    def up_knowledge_life_rule(self, kid, rule_list):
        # 清除历史规则
        self.rule_model.del_scope_rule(kid=kid)
        self.rule_model.del_life_rule(kid=kid)

        # 新增规则
        for i in rule_list:
            rule_params = {"life_type": i['life_type'], "file_scope": i['file_scope'], "life_days": i.get('life_days', 0),
                           "expire_date": i.get('expire_date') or None, 'knowledge_id': kid}
            rule_id = self.rule_model.add_life_rule([rule_params])[0]

            scope_list = [{'rule_key': j['rule_key'], 'rule_content': j['rule_content'], 'knowledge_id': kid,
                           'life_rule_id': rule_id} for j in i.get("scope_list", [])]
            self.rule_model.add_scope_rule(scope_list)
        return True

    def get_knowledge_life_rule(self, kid):
        ret = list()
        objs = self.rule_model.get_all_life_rule(kid)
        for obj in objs:
            scope_objs = self.rule_model.get_all_scope_rule(kid)
            scope_list = [{"rule_key": x.rule_key, "rule_content": x.rule_content} for x in scope_objs]
            m_dic = {
                    "life_type": obj.life_type,
                    "file_scope": obj.file_scope,
                    "scope_list": scope_list,
                    "life_days": obj.life_days or 0,
                    "expire_date": obj.expire_date
            }
            ret.append(m_dic)
        return ret

    def page_clear_rule_list(self, name, key_type, source, page_size, page_no):
        return self.clear_model.get_page_rule(name=name, source=source, _type=key_type, ps=page_size, po=page_no)

    def get_knowledge_clear_rule(self, kid):
        tag_map = {'prepare': {'keys': ['built_in&call_word', 'built_in&code_logic'], 'datas': []},
                   'script': {'keys': ['custom&script'], 'datas': []},
                   'self_call_word': {'keys': ['custom&call_word'], 'datas': []}}
        datas = self.clear_model.get_all_rel_clear_rule(kid)
        ret = {_key: x['datas'] for _key, x in tag_map.items()}
        for one in datas:
            check_key = f"{one['source']}&{one['key_type']}"
            for _key, value in tag_map.items():
                ret.setdefault(_key, [])
                if check_key in value['keys']:
                    ret[_key].append(one)
                    break
        list_lengths = {key: len(value) for key, value in ret.items()}
        ret['rule_num'] = list_lengths
        return ret

    def up_clear_rule_data(self, rule_id, conf):
        return self.clear_model.up_clear_rule(rule_id, conf)

    def add_clear_rule(self, params):
        rule_id = params.pop('id')
        key_type = params['type']
        if key_type == 'script' and 'http' not in params['content']:
            raise ValueError(f'关键内容 - {params["content"]} 异常')
        # 更新逻辑
        if rule_id:
            return self.up_clear_rule_data(rule_id, params)
        # 新增逻辑
        else:
            return self.clear_model.add_clear_rule([params])

    def up_knowledge_rel_clear_rule(self, kid_id, rule_json):
        self.clear_model.del_rel_rule(kid_id)
        for _key, values in rule_json.items():
            self.clear_model.add_rel_rule(kid_id, values)
        return True

    def do_clear_action(self, task_id, content, chunk_id, doc_id, aigc_type_id, model_path, kid, rules, model_id, call_words=None):
        old_content = content

        def inner_model_clear(content, call_word_list):
            if call_words:
                if type(call_words) is list:
                    call_word_list += call_words
                else:
                    call_word_list.append(call_words)
            if not call_word_list:
                return content
            desc = f"请帮我按以下规范处理文本句子 \n 文本句子: {content} \n"
            desc += """## 限制:\n - 直接返回内容，不要添加前言和后语 \n """
            for _index, msg in enumerate(set(call_word_list)):
                if not msg:
                    continue
                desc += f'- ' + msg + '\n'
            desc += '处理后的内容：'

            ret = ''
            for item,_ in AppController(self.corpid).ai_app_chat(desc, [], aigc_type_id, model_path, model_id=model_id):
                ret += item

            return ret or content

        def inner_script(content, file_urls):
            base_path = './upload/clear_script'
            if download_url_file(base_path, file_urls):
                for one in file_urls:
                    try:
                        spec = importlib.util.spec_from_file_location(one, f'{base_path}/{one}')
                        clear_script = importlib.util.module_from_spec(spec)
                        # 加载模块
                        spec.loader.exec_module(clear_script)
                        content = clear_script.run(content)
                    except Exception as err:
                        print(err)
                        continue
                return content
            else:
                return content
        group_ret = dict()
        for rule in rules:
            group_ret.setdefault(rule['key_type'], {})[f"{rule['id']}_{rule['name']}.py"] = rule['content']
        is_chat_model = False
        for key, values in group_ret.items():
            if key == 'call_word':
                content = inner_model_clear(content, list(values.values()))
                is_chat_model = True
            elif key == 'script':
                content = inner_script(content, values)

            # 目前仅处理docx
            elif key == 'code_logic':
                pass
        if call_words and not is_chat_model:
            content = inner_model_clear(content, [])
        if content != old_content:
            query = {'task_id': task_id, 'chunk_id': chunk_id, 'doc_id': doc_id, 'kid': kid}
            # 先判断doc的is_sharing是否为1，如果为1，则不更新
            doc_info = self.k_model.get_knowledge_document_detail(doc_id)
            if doc_info['is_sharing'] == 1:
                # 查询soruce_chunk_id = chunk_id的chunk_id
                chunk_id = self.k_model.get_document_chunk_id_by_chunk_source_id(chunk_id)
                if chunk_id:
                    # 更新chunk_id的content
                    self.k_model.update_knowledge_doc_chunk(chunk_id, {'content': content})
            self.clear_mg.update_doc_run_record(query, dict(content=content, **query))
        return content


    def do_clear_action_v1(self, task_id, aigc_type_id, model_path, doc_id, doc_url, rules, model_id, kid=None):
        """
        文档清洗处理逻辑，包括模型处理和脚本处理。
        :param task_id: 任务ID
        :param aigc_type_id: AIGC类型ID
        :param model_path: 模型路径
        :param doc_id: 文档ID
        :param content: 文档内容
        :param rules: 规则列表
        :param kid: 知识库ID
        :param aigc_model_id: aigc模型ID
        :param model_path: aigc模型路径
        :return: 处理后的文档内容
        """

        bucket_name = KNOWLEDGE_OSS_BUCKET_NAME
        doc_name = doc_url.split("/")[-1]
        object_name = f"knowledge/{doc_name}"   
        minio_end_point = settings.MINIO_CONF['endpoint']

        content = minio_util.download_file_v1(bucket_name, object_name)

        md_oss_name = os.path.splitext(doc_name)[0] + ".md"
        md_object_name = f"knowledge/{md_oss_name}"


        doc_name = doc_url.split("/")[-1]
        file_ext = doc_name.split('.')[-1].lower()

        call_word_list = []
        # 初始化 call_word_list
        def inner_model_clear(content, call_words):
            """
            使用大模型处理文本内容。
            :param content: 原始文本
            :param call_words: 需要的提示词
            :return: 处理后的文本
            """
            if call_words:
                if isinstance(call_words, list):
                    call_word_list.extend(call_words)
                else:
                    call_word_list.append(call_words)
            if not call_word_list:
                return content
              # 如果没有call_words，直接返回原始文本的字节流
            
            file_ext = doc_name.split('.')[-1].lower()

            if file_ext == 'docx':
                doc = Document(BytesIO(content))
                content = "\n".join([para.text for para in doc.paragraphs])

            elif file_ext == 'pptx':
                try:
                    pptx_file = ""
                    prs = Presentation(BytesIO(content))
                    for slide in prs.slides:
                        for shape in slide.shapes:
                            if hasattr(shape, "text") and shape.text:
                                pptx_file += shape.text + "\n"
                    content = pptx_file
                except Exception as e:
                    task_logger.error(f"PPTX 文件解析失败: {e}")
                    return content  
                
            elif file_ext == 'pdf':
                pdf_file = ""
                try:
                    with pdfplumber.open(BytesIO(content)) as pdf:
                        for page in pdf.pages:
                            text = page.extract_text()
                            if text:
                                pdf_file += text + "\n"
                    content = pdf_file
                except Exception as e:
                    task_logger.error(f"PDF 文件解析失败: {e}")
                    return content  # 如果 PDF 解析失败，返回原始字节流               
            
            desc = f"请帮我按以下规范处理文本句子 \n 文本句子: {content} \n"
            desc += """## 限制:\n - 直接返回内容，不要添加前言和后语 \n """
            desc += '\n'.join([f'- {msg}' for msg in set(call_word_list) if msg])  # 优化拼接
            desc += '\n处理后的内容：'
            ret = ''    
            for item,_ in AppController(self.corpid).ai_app_chat(desc, [], aigc_type_id, model_path, model_id=model_id):
                if item is None:  # 判断 item 是否为 NoneType
                    continue  # 如果是 None，跳过当前迭代，继续下一个
                ret += item
            return ret.encode('utf-8')
        
        def inner_script(content, file_url):
            """
            使用脚本处理文本内容。
            :param content: 原始文本（字符串类型）
            :param file_url: 脚本文件URL（字符串）
            :return: 处理后的文本（字节流类型）
            """
            base_path = './upload/doc_clear_script'
            
            # file_url = f"http://{minio_end_point}/{bucket_name}/pre_handle/{file_url}"
            
            if not download_and_verify_scripts(file_url, base_path):
                raise ValueError("脚本下载或验证失败，无法继续执行")

            try:
                script_path = os.path.join(base_path, file_url.split('/')[-1])
                with open(script_path, 'r', encoding='utf-8') as file:
                    script_content = file.read()

                    task_logger.info(f"脚本内容：{script_content}")  # 打印脚本内容进行调试

                    # 使用 exec 加载脚本
                    exec(script_content, globals())

                    # 确认 run 函数是否定义
                    if 'run' in globals():
                        content = globals()['run'](content,file_ext) 
                        task_logger.info(f"成功执行脚本：{file_url}")
                    else:
                        task_logger.warning(f"脚本 {file_url} 中没有定义 run 函数，跳过该脚本")
        
            except Exception as err:
                task_logger.error(f"执行脚本 {file_url} 时发生错误: {err}")
            return content
            
        def to_markdown(content):
            """
            将清洗后的内容转化为 Markdown 格式。
            :param content: 清洗后的文本内容
            :return: 转化后的Markdown链接
            """

            # 根据文件类型进行不同的转换
            try:
                if file_ext == 'docx':
                    content = convert_docx_by_MarkItDown(content,file_ext,doc_name)
                elif file_ext == 'pptx':
                    content = convert_otherfile_to_markdown(content,file_ext,doc_name)
                elif file_ext == 'pdf':
                    content = convert_otherfile_to_markdown(content,file_ext,doc_name)
                elif file_ext == 'txt':
                    content = convert_otherfile_to_markdown(content,file_ext,doc_name)
                elif file_ext == 'xlsx':
                    content = convert_xlsx_to_markdown(content,doc_name)
                elif file_ext == 'xls':
                    content = convert_xls_to_markdown(content,doc_name)
                else:
                    task_logger.warning(f"不支持的文件格式: {file_ext}")
                return minio_util.upload_to_minio(bucket_name, md_object_name, content)
            except Exception as e:
                task_logger.error(f"转换失败: {e}")
                raise e

        # 遍历所有规则，并根据规则类型执行相应的操作
        try:
            if rules:
                for rule in rules:
                    rule_id = rule['rule_id']
                    task_logger.info(f"rule_id:{rule_id}")
                    rule_type = self.doc_clear_rule.get_rule_type(rule_id)
                    rule_content = self.doc_clear_rule.get_rule_content(rule_id) 

                    if rule_type == 0:  # 大模型提示词处理
                        content = inner_model_clear(content, rule_content)                        
                        minio_url = minio_util.upload_to_minio(bucket_name, md_object_name, content)
                        task_logger.info(f"content：成功执行了模型处理")
                        KnowledgeController(self.corpid).update_knowledge_doc(doc_id, {'doc_clear_url': minio_url})
                        task_logger.info(f"minio_url:{minio_url}")
                        return minio_url
                    
                    elif rule_type == 1:  # 脚本处理
                        content = inner_script(content, rule_content)
                        task_logger.info(f"content：成功执行了脚本处理")
                        minio_url = minio_util.upload_to_minio(bucket_name, md_object_name, content)
                        KnowledgeController(self.corpid).update_knowledge_doc(doc_id, {'doc_clear_url': minio_url})
                        task_logger.info(f"minio_url:{minio_url}")
                        return minio_url
            else:
                task_logger.info(f"没执行脚本和大模型")
                minio_url = to_markdown(content)
                KnowledgeController(self.corpid).update_knowledge_doc(doc_id, {'doc_clear_url': minio_url})
                task_logger.info(f"minio_url:{minio_url}")
                return minio_url
        except Exception as e:
            raise e

    def get_mg_page_clear_data(self, task_id):
        group_data = dict()
        ret = list()
        datas = self.clear_mg.get_doc_run_record({'task_id': task_id})
        for one in datas:
            doc_id = one['doc_id']
            group_data.setdefault(doc_id, []).append({'data_id': one['_id'], 'task_id': one['task_id'], 'chunk_id': one['chunk_id'], 'kid': one['kid'], 'content': one['content']})
        doc_infos = self.k_model.get_doc_list_by_doc_ids(list(group_data.keys()))
        for one in doc_infos:
            doc_id = one['document_id']
            doc_name = one['doc_name']
            ret.append({'doc_id': doc_id, 'doc_name': doc_name, 'chunk_list': {'data_list': group_data.get(doc_id, [])}})
        return ret

    def up_mg_clear_data(self, query, up_conf):
        self.clear_mg.update_doc_run_record(query, up_conf)

    def mg_replace_sql_content(self, task_id):
        datas = self.clear_mg.get_doc_run_record({'task_id': task_id})
        for one in datas:
            self.k_model.update_knowledge_doc_chunk(one['chunk_id'], {'content': one['content']})
        return True

    def del_mg_clear_data(self, data_id):
        return self.clear_mg.del_doc_run_record({'_id': data_id})